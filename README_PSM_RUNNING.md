# PSM匹配脚本运行指南

## 概述
这个项目包含了一个需要长时间运行的PSM（Propensity Score Matching）匹配脚本。由于运行时间可能长达数小时，我们提供了多种方式来确保脚本在用户断开连接后仍能继续运行。

## 运行方式

### 方式1：使用nohup（推荐用于完全后台运行）
```bash
./run_psm_matching.sh
```
- 脚本会在后台运行，输出重定向到日志文件
- 即使断开SSH连接，脚本也会继续运行
- 日志文件保存在 `logs/` 目录下

### 方式2：使用screen会话（推荐用于可交互监控）
```bash
./run_psm_screen.sh
```
- 创建一个screen会话来运行脚本
- 可以随时重新连接查看进度
- 断开连接后脚本继续运行

## 监控脚本运行状态

### 检查运行状态
```bash
./check_psm_status.sh
```
这个脚本会显示：
- 当前运行的进程
- 最新的日志文件
- 最新的日志内容
- 缓存文件状态
- 输出文件状态

### 手动检查方法

#### 1. 检查进程是否运行
```bash
ps aux | grep 20250629_PSM_matching_multi_limits.py
```

#### 2. 查看最新日志
```bash
tail -f logs/psm_matching_*.log
```

#### 3. 查看screen会话
```bash
screen -ls
```

#### 4. 重新连接到screen会话
```bash
screen -r [session_name]
```

## 文件结构

```
disengagement/
├── src_new/
│   └── 20250629_PSM_matching_multi_limits.py  # 主脚本
├── run_psm_matching.sh                        # nohup启动脚本
├── run_psm_screen.sh                          # screen启动脚本
├── check_psm_status.sh                        # 状态检查脚本
├── logs/                                      # 日志目录
├── cache/                                     # 缓存目录
└── result/                                    # 输出目录
```

## 重要注意事项

### 1. 缓存机制
- 脚本使用缓存机制，如果中断可以从断点继续
- 缓存文件保存在 `cache/psm_20250629/` 目录
- 每个limit都有独立的缓存文件

### 2. 输出文件
- 最终结果保存在 `result/20250629_did_result/` 目录
- 文件名格式：`compiled_data_test_limit{limit}_optimized_v2.csv`

### 3. 内存管理
- 脚本包含内存监控和垃圾回收
- 如果内存不足，脚本会自动清理

### 4. 进度监控
- 脚本会定期输出进度信息
- 可以通过日志文件查看详细进度

## 故障排除

### 如果脚本意外停止
1. 检查日志文件中的错误信息
2. 重新运行启动脚本（会从缓存继续）
3. 检查磁盘空间是否充足

### 如果需要停止脚本
```bash
# 查找进程ID
ps aux | grep 20250629_PSM_matching_multi_limits.py

# 停止进程
kill [PID]

# 或者强制停止
kill -9 [PID]
```

### 如果需要清理缓存重新开始
```bash
rm -rf cache/psm_20250629/*
```

## 预期运行时间
- 每个limit大约需要1-2小时
- 总共3个limits，预计总运行时间3-6小时
- 实际时间取决于数据大小和服务器性能

## 完成后的检查
运行完成后，检查以下文件是否存在：
- `result/20250629_did_result/compiled_data_test_limit180_optimized_v2.csv`
- `result/20250629_did_result/compiled_data_test_limit270_optimized_v2.csv`
- `result/20250629_did_result/compiled_data_test_limit450_optimized_v2.csv` 