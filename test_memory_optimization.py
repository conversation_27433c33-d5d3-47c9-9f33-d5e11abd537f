#!/usr/bin/env python3
"""
测试内存优化效果的脚本
"""

import sys
import os
import psutil
import gc
import time
from datetime import datetime

# 添加源代码路径
sys.path.append('src_new_new')

def monitor_memory():
    """监控内存使用情况"""
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    return memory_mb

def test_memory_optimization():
    """测试内存优化效果"""
    print(f"开始内存优化测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"可用内存: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
    print(f"可用CPU核心: {psutil.cpu_count()}")
    print("-" * 60)
    
    # 记录初始内存
    initial_memory = monitor_memory()
    print(f"初始内存使用: {initial_memory:.1f}MB")
    
    try:
        # 导入优化后的模块
        from src_new_new.psm_matching_multi_limits_improved import process_single_limit
        
        print("成功导入优化后的模块")
        
        # 测试单个limit处理
        print("开始测试单个limit处理...")
        start_time = time.time()
        
        # 监控内存使用
        memory_before = monitor_memory()
        print(f"处理前内存: {memory_before:.1f}MB")
        
        # 运行处理
        result = process_single_limit(365)
        
        # 监控处理后内存
        memory_after = monitor_memory()
        processing_time = time.time() - start_time
        
        print(f"处理后内存: {memory_after:.1f}MB")
        print(f"内存增长: {memory_after - memory_before:.1f}MB")
        print(f"处理时间: {processing_time:.1f}秒")
        
        if result:
            limit, total_time, peak_memory = result
            print(f"处理结果: limit={limit}, 时间={total_time:.1f}s, 峰值内存={peak_memory:.1f}MB")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保源代码文件存在且路径正确")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 最终内存清理
        gc.collect()
        final_memory = monitor_memory()
        print(f"最终内存使用: {final_memory:.1f}MB")
        print(f"总内存变化: {final_memory - initial_memory:.1f}MB")
        print(f"测试完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_memory_optimization()
