# Automated DID Analysis Scripts

## Overview

This repository contains automated scripts to run Difference-in-Differences (DID) analysis for multiple limits (180, 270, 450) and generate results for 6 different models.

## Files

- `automated_did_analysis.R` - Main R script for running all DID analyses
- `run_did_analysis.sh` - <PERSON>sh wrapper script to execute the R analysis
- `README_DID_Analysis.md` - This documentation file

## Models Included

### Main Treatment Effect Models (3 models)
1. **PR Throughput** (`did_main_pr_throughput.txt`)
2. **PR Accept Rate** (`did_main_pr_accept_rate.txt`) 
3. **PR Time to Merge** (`did_main_pr_time_to_merge.txt`)

### Moderating Effect Models (3 models)
4. **Moderating PR Throughput** (`did_moderating_pr_throughput.txt`)
5. **Moderating PR Accept Rate** (`did_moderating_pr_accept_rate.txt`)
6. **Moderating PR Time to Merge** (`did_moderating_pr_time_to_merge.txt`)

## Data Requirements

The script expects the following data files to exist:
- `/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit180_processed_ultra_optimized.csv`
- `/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit270_processed_ultra_optimized.csv`
- `/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit450_processed_ultra_optimized.csv`

## Required R Packages

The script requires the following R packages:
- stats
- lme4
- readr
- ggplot2
- stargazer
- lmtest
- MuMIn
- lmerTest
- survival
- ggpubr
- survminer
- car
- coxme
- dplyr

## Usage

### Method 1: Using the Bash Script (Recommended)
```bash
./run_did_analysis.sh
```

### Method 2: Direct R Execution
```bash
Rscript automated_did_analysis.R
```

### Method 3: From R Console
```r
source("automated_did_analysis.R")
```

## Output Structure

Results will be saved in the following directory structure:
```
../result/20250629_did_result/
├── attrition_180/
│   ├── did_main_pr_throughput.txt
│   ├── did_main_pr_accept_rate.txt
│   ├── did_main_pr_time_to_merge.txt
│   ├── did_moderating_pr_throughput.txt
│   ├── did_moderating_pr_accept_rate.txt
│   └── did_moderating_pr_time_to_merge.txt
├── attrition_270/
│   └── [same 6 files]
└── attrition_450/
    └── [same 6 files]
```

## Output Format

Each output file contains:
- Model formula
- VIF (Variance Inflation Factor) values
- Complete model summary with coefficients, standard errors, and p-values
- R-squared values (marginal and conditional)
- Random effects information

## Error Handling

The script includes error handling for:
- Missing data files
- Model convergence issues
- Memory problems
- Package loading failures

## Performance Notes

- The script uses optimized lmer control parameters for better convergence
- Large datasets may take significant time to process
- Memory usage can be high for complex models with large datasets

## Troubleshooting

### Common Issues:

1. **Package not found**: Install missing packages using `install.packages()`
2. **Memory issues**: Consider running on a machine with more RAM
3. **Convergence warnings**: The script uses optimized parameters, but some models may still have convergence issues
4. **File not found**: Ensure data files exist in the specified paths

### Debug Mode:
To run with more verbose output, modify the script to remove `suppressPackageStartupMessages()`.

## Customization

To modify the analysis:
1. Edit the `limits` vector to change which limits are processed
2. Modify the `data_files` list to point to different data sources
3. Adjust the `ctrl` parameters for different optimization settings
4. Modify model formulas in the respective functions

## Contact

For issues or questions about the analysis, please refer to the original Jupyter notebook or contact the analysis team. 