{"cells": [{"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (*********.py, line 2)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[18], line 2\u001b[0;36m\u001b[0m\n\u001b[0;31m    ChromeOptions options = new ChromeOptions();\u001b[0m\n\u001b[0m                  ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": ["System.setProperty(\"webdriver.chrome.driver\", \"C:\\\\path\\\\to\\\\chromedriver.exe\");\n", "ChromeOptions options = new ChromeOptions();\n", "options.addArguments(\"start-maximized\"); // open Browser in maximized mode\n", "options.addArguments(\"disable-infobars\"); // disabling infobars\n", "options.addArguments(\"--disable-extensions\"); // disabling extensions\n", "options.addArguments(\"--disable-gpu\"); // applicable to windows os only\n", "options.addArguments(\"--disable-dev-shm-usage\"); // overcome limited resource problems\n", "options.addArguments(\"--no-sandbox\"); // Bypass OS security model\n", "WebDriver driver = new ChromeDriver(options);\n", "driver.get(\"https://google.com\");"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting undetected-chromedriver\n", "  Downloading undetected-chromedriver-3.5.5.tar.gz (65 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: selenium>=4.9.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from undetected-chromedriver) (4.31.0)\n", "Collecting requests (from undetected-chromedriver)\n", "  Downloading requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting websockets (from undetected-chromedriver)\n", "  Downloading websockets-15.0.1-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)\n", "Requirement already satisfied: urllib3<3,>=1.26 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from urllib3[socks]<3,>=1.26->selenium>=4.9.0->undetected-chromedriver) (2.3.0)\n", "Requirement already satisfied: trio~=0.17 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium>=4.9.0->undetected-chromedriver) (0.29.0)\n", "Requirement already satisfied: trio-websocket~=0.9 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium>=4.9.0->undetected-chromedriver) (0.12.2)\n", "Requirement already satisfied: certifi>=2021.10.8 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium>=4.9.0->undetected-chromedriver) (2025.1.31)\n", "Requirement already satisfied: typing_extensions~=4.9 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium>=4.9.0->undetected-chromedriver) (4.12.2)\n", "Requirement already satisfied: websocket-client~=1.8 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium>=4.9.0->undetected-chromedriver) (1.8.0)\n", "Collecting charset-normalizer<4,>=2 (from requests->undetected-chromedriver)\n", "  Downloading charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (35 kB)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from requests->undetected-chromedriver) (3.10)\n", "Requirement already satisfied: attrs>=23.2.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium>=4.9.0->undetected-chromedriver) (25.3.0)\n", "Requirement already satisfied: sortedcontainers in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium>=4.9.0->undetected-chromedriver) (2.4.0)\n", "Requirement already satisfied: outcome in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium>=4.9.0->undetected-chromedriver) (1.3.0.post0)\n", "Requirement already satisfied: sniffio>=1.3.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium>=4.9.0->undetected-chromedriver) (1.3.1)\n", "Requirement already satisfied: wsproto>=0.14 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio-websocket~=0.9->selenium>=4.9.0->undetected-chromedriver) (1.2.0)\n", "Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from urllib3[socks]<3,>=1.26->selenium>=4.9.0->undetected-chromedriver) (1.7.1)\n", "Requirement already satisfied: h11<1,>=0.9.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium>=4.9.0->undetected-chromedriver) (0.14.0)\n", "Downloading requests-2.32.3-py3-none-any.whl (64 kB)\n", "Downloading websockets-15.0.1-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (182 kB)\n", "Downloading charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (144 kB)\n", "Building wheels for collected packages: undetected-chromedriver\n", "  Building wheel for undetected-chromedriver (setup.py) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for undetected-chromedriver: filename=undetected_chromedriver-3.5.5-py3-none-any.whl size=47099 sha256=89f67864763b9cff1916202828d8be13246b3c096c2de36ede971afb6a7aa39c\n", "  Stored in directory: /home/<USER>/.cache/pip/wheels/7a/5f/c1/06f68421cc7172ef51504631252870bcb3a2fdf3b6a025f362\n", "Successfully built undetected-chromedriver\n", "Installing collected packages: websockets, charset-normalizer, requests, undetected-chromedriver\n", "Successfully installed charset-normalizer-3.4.1 requests-2.32.3 undetected-chromedriver-3.5.5 websockets-15.0.1\n"]}], "source": ["!pip install undetected-chromedriver"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "WebDriver.__init__() got an unexpected keyword argument 'service_args'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[9], line 17\u001b[0m\n\u001b[1;32m     14\u001b[0m     driver\u001b[38;5;241m.\u001b[39mquit()\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 17\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[9], line 11\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m      9\u001b[0m options\u001b[38;5;241m.\u001b[39madd_argument(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcrash-dumps-dir=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mos\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexpanduser(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m~/tmp/Crashpad\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     10\u001b[0m options\u001b[38;5;241m.\u001b[39madd_argument(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m--verbose\u001b[39m\u001b[38;5;124m\"\u001b[39m)  \u001b[38;5;66;03m# Enable verbose logging\u001b[39;00m\n\u001b[0;32m---> 11\u001b[0m driver \u001b[38;5;241m=\u001b[39m \u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_args\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m--verbose\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# Enable verbose logging for ChromeDriver\u001b[39;00m\n\u001b[1;32m     12\u001b[0m driver\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://google.com/\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpress enter\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mTypeError\u001b[0m: WebDriver.__init__() got an unexpected keyword argument 'service_args'"]}], "source": ["import os.path\n", "\n", "from selenium import webdriver\n", "\n", "def main():\n", "    options = webdriver.ChromeOptions()\n", "    options.add_argument(\"disable-gpu\")\n", "    options.add_argument(\"headless\")\n", "    options.add_argument(f\"crash-dumps-dir={os.path.expanduser('~/tmp/Crashpad')}\")\n", "    options.add_argument(\"--verbose\")  # Enable verbose logging\n", "    driver = webdriver.Chrome(options=options, service_args=[\"--verbose\"])  # Enable verbose logging for ChromeDriver\n", "    driver.get(\"https://google.com/\")\n", "    input(\"press enter\")\n", "    driver.quit()\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/.wdm/drivers/chromedriver/linux64/104.0.5112.79/chromedriver\n"]}], "source": ["from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "print(ChromeDriverManager().install())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "WebDriverException", "evalue": "Message: unknown error: DevToolsActivePort file doesn't exist\nStacktrace:\n#0 0x637d58020403 <unknown>\n#1 0x637d57e26778 <unknown>\n#2 0x637d57e4dcc2 <unknown>\n#3 0x637d57e4a08b <unknown>\n#4 0x637d57e4612b <unknown>\n#5 0x637d57e8183a <unknown>\n#6 0x637d57e7b8f3 <unknown>\n#7 0x637d57e510d8 <unknown>\n#8 0x637d57e52205 <unknown>\n#9 0x637d58067e3d <unknown>\n#10 0x637d5806adb6 <unknown>\n#11 0x637d5805113e <unknown>\n#12 0x637d5806b9b5 <unknown>\n#13 0x637d58045970 <unknown>\n#14 0x637d58088228 <unknown>\n#15 0x637d580883bf <unknown>\n#16 0x637d580a2abe <unknown>\n#17 0x7faeb3894ac3 <unknown>\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mWebDriverException\u001b[0m                        Traceback (most recent call last)", "Cell \u001b[0;32mIn[17], line 23\u001b[0m\n\u001b[1;32m     20\u001b[0m     service\u001b[38;5;241m.\u001b[39mstop()\n\u001b[1;32m     22\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 23\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[17], line 16\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     13\u001b[0m service\u001b[38;5;241m.\u001b[39mservice_args \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m--verbose\u001b[39m\u001b[38;5;124m'\u001b[39m]  \u001b[38;5;66;03m# Enable verbose logging for ChromeDriver\u001b[39;00m\n\u001b[1;32m     14\u001b[0m service\u001b[38;5;241m.\u001b[39mstart()\n\u001b[0;32m---> 16\u001b[0m driver \u001b[38;5;241m=\u001b[39m \u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     17\u001b[0m driver\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://google.com/\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpress enter\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chrome/webdriver.py:45\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     42\u001b[0m service \u001b[38;5;241m=\u001b[39m service \u001b[38;5;28;01mif\u001b[39;00m service \u001b[38;5;28;01melse\u001b[39;00m Service()\n\u001b[1;32m     43\u001b[0m options \u001b[38;5;241m=\u001b[39m options \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;28;01melse\u001b[39;00m Options()\n\u001b[0;32m---> 45\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbrowser_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDesiredCapabilities\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCHROME\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbrowserName\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     47\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvendor_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgoog\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     49\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     50\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     51\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chromium/webdriver.py:66\u001b[0m, in \u001b[0;36mChromiumDriver.__init__\u001b[0;34m(self, browser_name, vendor_prefix, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     57\u001b[0m executor \u001b[38;5;241m=\u001b[39m ChromiumRemoteConnection(\n\u001b[1;32m     58\u001b[0m     remote_server_addr\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mservice\u001b[38;5;241m.\u001b[39mservice_url,\n\u001b[1;32m     59\u001b[0m     browser_name\u001b[38;5;241m=\u001b[39mbrowser_name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     62\u001b[0m     ignore_proxy\u001b[38;5;241m=\u001b[39moptions\u001b[38;5;241m.\u001b[39m_ignore_local_proxy,\n\u001b[1;32m     63\u001b[0m )\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 66\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexecutor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m     68\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquit()\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:250\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, command_executor, keep_alive, file_detector, options, locator_converter, web_element_cls, client_config)\u001b[0m\n\u001b[1;32m    248\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_authenticator_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    249\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstart_client()\n\u001b[0;32m--> 250\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcapabilities\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    251\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fedcm \u001b[38;5;241m=\u001b[39m FedCM(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m    253\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_websocket_connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:342\u001b[0m, in \u001b[0;36mWebDriver.start_session\u001b[0;34m(self, capabilities)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Creates a new session with the desired capabilities.\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \n\u001b[1;32m    335\u001b[0m \u001b[38;5;124;03mParameters:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    338\u001b[0m \u001b[38;5;124;03m    - A capabilities dict to start the session with.\u001b[39;00m\n\u001b[1;32m    339\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    341\u001b[0m caps \u001b[38;5;241m=\u001b[39m _create_caps(capabilities)\n\u001b[0;32m--> 342\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mNEW_SESSION\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaps\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession_id \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    344\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcaps \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcapabilities\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:429\u001b[0m, in \u001b[0;36mWebDriver.execute\u001b[0;34m(self, driver_command, params)\u001b[0m\n\u001b[1;32m    427\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_executor\u001b[38;5;241m.\u001b[39mexecute(driver_command, params)\n\u001b[1;32m    428\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[0;32m--> 429\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43merror_handler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    430\u001b[0m     response[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_unwrap_value(response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    431\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/errorhandler.py:232\u001b[0m, in \u001b[0;36mErrorHandler.check_response\u001b[0;34m(self, response)\u001b[0m\n\u001b[1;32m    230\u001b[0m         alert_text \u001b[38;5;241m=\u001b[39m value[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124malert\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    231\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[0;32m--> 232\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[0;31mWebDriverException\u001b[0m: Message: unknown error: DevToolsActivePort file doesn't exist\nStacktrace:\n#0 0x637d58020403 <unknown>\n#1 0x637d57e26778 <unknown>\n#2 0x637d57e4dcc2 <unknown>\n#3 0x637d57e4a08b <unknown>\n#4 0x637d57e4612b <unknown>\n#5 0x637d57e8183a <unknown>\n#6 0x637d57e7b8f3 <unknown>\n#7 0x637d57e510d8 <unknown>\n#8 0x637d57e52205 <unknown>\n#9 0x637d58067e3d <unknown>\n#10 0x637d5806adb6 <unknown>\n#11 0x637d5805113e <unknown>\n#12 0x637d5806b9b5 <unknown>\n#13 0x637d58045970 <unknown>\n#14 0x637d58088228 <unknown>\n#15 0x637d580883bf <unknown>\n#16 0x637d580a2abe <unknown>\n#17 0x7faeb3894ac3 <unknown>\n"]}], "source": ["import os.path\n", "\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "\n", "def main():\n", "    options = webdriver.ChromeOptions()\n", "    options.add_argument(\"disable-gpu\")\n", "    options.add_argument(\"headless\")\n", "    options.add_argument(f\"crash-dumps-dir={os.path.expanduser('~/tmp/Crashpad')}\")\n", "    # no-sandbox is not needed in headless mode\n", "    options.add_argument(\"no-sandbox\")\n", "    service = Service('/home/<USER>/.wdm/drivers/chromedriver/linux64/104.0.5112.79/chromedriver')\n", "    service.service_args = ['--verbose']  # Enable verbose logging for ChromeDriver\n", "    service.start()\n", "\n", "    driver = webdriver.Chrome(options=options, service=service)\n", "    driver.get(\"https://google.com/\")\n", "    input(\"press enter\")\n", "    driver.quit()\n", "    service.stop()\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "ProtocolError", "evalue": "('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRemoteDisconnected\u001b[0m                        <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connectionpool.py:787\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    786\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 787\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    788\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    789\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    790\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    791\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    792\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    793\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    795\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    796\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    797\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    798\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    802\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connectionpool.py:534\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    533\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 534\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    535\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (BaseSSLError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connection.py:516\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    515\u001b[0m \u001b[38;5;66;03m# Get the response from http.client.HTTPConnection\u001b[39;00m\n\u001b[0;32m--> 516\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    518\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/http/client.py:1430\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1429\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1430\u001b[0m     \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1431\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/http/client.py:331\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 331\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    332\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/http/client.py:300\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    297\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m line:\n\u001b[1;32m    298\u001b[0m     \u001b[38;5;66;03m# Presumably, the server closed the connection before\u001b[39;00m\n\u001b[1;32m    299\u001b[0m     \u001b[38;5;66;03m# sending a valid response.\u001b[39;00m\n\u001b[0;32m--> 300\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RemoteDisconnected(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRemote end closed connection without\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    301\u001b[0m                              \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m response\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    302\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[0;31mRemoteDisconnected\u001b[0m: Remote end closed connection without response", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mProtocolError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 82\u001b[0m\n\u001b[1;32m     78\u001b[0m                 \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m无法点击 \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mChinese PDF\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m 图标\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;66;03m# ，错误: {e}\u001b[39;00m\n\u001b[1;32m     80\u001b[0m         driver\u001b[38;5;241m.\u001b[39mquit()\n\u001b[0;32m---> 82\u001b[0m \u001b[43mget_un_documents_zh\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mA/RES/47/52\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[3], line 20\u001b[0m, in \u001b[0;36mget_un_documents_zh\u001b[0;34m(symbol)\u001b[0m\n\u001b[1;32m     17\u001b[0m chrome_options\u001b[38;5;241m.\u001b[39madd_argument(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m--window-size=1920,1080\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     19\u001b[0m \u001b[38;5;66;03m# 打开联合国正式文件库的检索页面\u001b[39;00m\n\u001b[0;32m---> 20\u001b[0m driver \u001b[38;5;241m=\u001b[39m \u001b[43muc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43mheadless\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43muse_subprocess\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mF<PERSON>e\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m     21\u001b[0m \u001b[38;5;66;03m# driver = webdriver.Chrome(options=chrome_options) # 初始化Chrome驱动\u001b[39;00m\n\u001b[1;32m     22\u001b[0m driver\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttp://documents.un.org\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;66;03m# 访问目标网站\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/undetected_chromedriver/__init__.py:466\u001b[0m, in \u001b[0;36mChrome.__init__\u001b[0;34m(self, options, user_data_dir, driver_executable_path, browser_executable_path, port, enable_cdp_events, desired_capabilities, advanced_elements, keep_alive, log_level, headless, version_main, patcher_force_close, suppress_welcome, use_subprocess, debug, no_sandbox, user_multi_procs, **kw)\u001b[0m\n\u001b[1;32m    459\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbrowser_pid \u001b[38;5;241m=\u001b[39m browser\u001b[38;5;241m.\u001b[39mpid\n\u001b[1;32m    462\u001b[0m service \u001b[38;5;241m=\u001b[39m selenium\u001b[38;5;241m.\u001b[39mwebdriver\u001b[38;5;241m.\u001b[39mchromium\u001b[38;5;241m.\u001b[39mservice\u001b[38;5;241m.\u001b[39mChromiumService(\n\u001b[1;32m    463\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpatcher\u001b[38;5;241m.\u001b[39mexecutable_path\n\u001b[1;32m    464\u001b[0m )\n\u001b[0;32m--> 466\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mChrome\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    467\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    468\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    469\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    470\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    472\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreactor \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    474\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m enable_cdp_events:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chrome/webdriver.py:45\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     42\u001b[0m service \u001b[38;5;241m=\u001b[39m service \u001b[38;5;28;01mif\u001b[39;00m service \u001b[38;5;28;01melse\u001b[39;00m Service()\n\u001b[1;32m     43\u001b[0m options \u001b[38;5;241m=\u001b[39m options \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;28;01melse\u001b[39;00m Options()\n\u001b[0;32m---> 45\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbrowser_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDesiredCapabilities\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCHROME\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbrowserName\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     47\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvendor_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgoog\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     49\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     50\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     51\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chromium/webdriver.py:66\u001b[0m, in \u001b[0;36mChromiumDriver.__init__\u001b[0;34m(self, browser_name, vendor_prefix, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     57\u001b[0m executor \u001b[38;5;241m=\u001b[39m ChromiumRemoteConnection(\n\u001b[1;32m     58\u001b[0m     remote_server_addr\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mservice\u001b[38;5;241m.\u001b[39mservice_url,\n\u001b[1;32m     59\u001b[0m     browser_name\u001b[38;5;241m=\u001b[39mbrowser_name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     62\u001b[0m     ignore_proxy\u001b[38;5;241m=\u001b[39moptions\u001b[38;5;241m.\u001b[39m_ignore_local_proxy,\n\u001b[1;32m     63\u001b[0m )\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 66\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexecutor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m     68\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquit()\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:250\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, command_executor, keep_alive, file_detector, options, locator_converter, web_element_cls, client_config)\u001b[0m\n\u001b[1;32m    248\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_authenticator_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    249\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstart_client()\n\u001b[0;32m--> 250\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcapabilities\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    251\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fedcm \u001b[38;5;241m=\u001b[39m FedCM(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m    253\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_websocket_connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/undetected_chromedriver/__init__.py:724\u001b[0m, in \u001b[0;36mChrome.start_session\u001b[0;34m(self, capabilities, browser_profile)\u001b[0m\n\u001b[1;32m    722\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m capabilities:\n\u001b[1;32m    723\u001b[0m     capabilities \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mto_capabilities()\n\u001b[0;32m--> 724\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mselenium\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchrome\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mWebDriver\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    725\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcapabilities\u001b[49m\n\u001b[1;32m    726\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:342\u001b[0m, in \u001b[0;36mWebDriver.start_session\u001b[0;34m(self, capabilities)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Creates a new session with the desired capabilities.\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \n\u001b[1;32m    335\u001b[0m \u001b[38;5;124;03mParameters:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    338\u001b[0m \u001b[38;5;124;03m    - A capabilities dict to start the session with.\u001b[39;00m\n\u001b[1;32m    339\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    341\u001b[0m caps \u001b[38;5;241m=\u001b[39m _create_caps(capabilities)\n\u001b[0;32m--> 342\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mNEW_SESSION\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaps\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession_id \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    344\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcaps \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcapabilities\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:427\u001b[0m, in \u001b[0;36mWebDriver.execute\u001b[0;34m(self, driver_command, params)\u001b[0m\n\u001b[1;32m    424\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m params:\n\u001b[1;32m    425\u001b[0m         params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession_id\n\u001b[0;32m--> 427\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcommand_executor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver_command\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    428\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[1;32m    429\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39merror_handler\u001b[38;5;241m.\u001b[39mcheck_response(response)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/remote_connection.py:404\u001b[0m, in \u001b[0;36mRemoteConnection.execute\u001b[0;34m(self, command, params)\u001b[0m\n\u001b[1;32m    402\u001b[0m trimmed \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_trim_large_entries(params)\n\u001b[1;32m    403\u001b[0m LOGGER\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, command_info[\u001b[38;5;241m0\u001b[39m], url, \u001b[38;5;28mstr\u001b[39m(trimmed))\n\u001b[0;32m--> 404\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand_info\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/remote_connection.py:428\u001b[0m, in \u001b[0;36mRemoteConnection._request\u001b[0;34m(self, method, url, body)\u001b[0m\n\u001b[1;32m    425\u001b[0m     body \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    427\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client_config\u001b[38;5;241m.\u001b[39mkeep_alive:\n\u001b[0;32m--> 428\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_conn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client_config\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    429\u001b[0m     statuscode \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mstatus\n\u001b[1;32m    430\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/_request_methods.py:143\u001b[0m, in \u001b[0;36mRequestMethods.request\u001b[0;34m(self, method, url, body, fields, headers, json, **urlopen_kw)\u001b[0m\n\u001b[1;32m    135\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest_encode_url(\n\u001b[1;32m    136\u001b[0m         method,\n\u001b[1;32m    137\u001b[0m         url,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    140\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39murlopen_kw,\n\u001b[1;32m    141\u001b[0m     )\n\u001b[1;32m    142\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 143\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest_encode_body\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    144\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfields\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfields\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43murlopen_kw\u001b[49m\n\u001b[1;32m    145\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/_request_methods.py:278\u001b[0m, in \u001b[0;36mRequestMethods.request_encode_body\u001b[0;34m(self, method, url, fields, headers, encode_multipart, multipart_boundary, **urlopen_kw)\u001b[0m\n\u001b[1;32m    274\u001b[0m     extra_kw[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39msetdefault(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mContent-Type\u001b[39m\u001b[38;5;124m\"\u001b[39m, content_type)\n\u001b[1;32m    276\u001b[0m extra_kw\u001b[38;5;241m.\u001b[39mupdate(urlopen_kw)\n\u001b[0;32m--> 278\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mextra_kw\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/poolmanager.py:443\u001b[0m, in \u001b[0;36mPoolManager.urlopen\u001b[0;34m(self, method, url, redirect, **kw)\u001b[0m\n\u001b[1;32m    441\u001b[0m     response \u001b[38;5;241m=\u001b[39m conn\u001b[38;5;241m.\u001b[39murlopen(method, url, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw)\n\u001b[1;32m    442\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 443\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest_uri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    445\u001b[0m redirect_location \u001b[38;5;241m=\u001b[39m redirect \u001b[38;5;129;01mand\u001b[39;00m response\u001b[38;5;241m.\u001b[39mget_redirect_location()\n\u001b[1;32m    446\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m redirect_location:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connectionpool.py:841\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    838\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(new_e, (\u001b[38;5;167;01mOSError\u001b[39;00m, HTTPException)):\n\u001b[1;32m    839\u001b[0m     new_e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, new_e)\n\u001b[0;32m--> 841\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    842\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnew_e\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m    843\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    844\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n\u001b[1;32m    846\u001b[0m \u001b[38;5;66;03m# Keep track of the error for the retry warning.\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/util/retry.py:474\u001b[0m, in \u001b[0;36mRetry.increment\u001b[0;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[1;32m    471\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m error \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_read_error(error):\n\u001b[1;32m    472\u001b[0m     \u001b[38;5;66;03m# Read retry?\u001b[39;00m\n\u001b[1;32m    473\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m read \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m method \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01<PERSON>\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_method_retryable(method):\n\u001b[0;32m--> 474\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mreraise\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mtype\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    475\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m read \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    476\u001b[0m         read \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/util/util.py:38\u001b[0m, in \u001b[0;36mreraise\u001b[0;34m(tp, value, tb)\u001b[0m\n\u001b[1;32m     36\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m     37\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m value\u001b[38;5;241m.\u001b[39m__traceback__ \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m tb:\n\u001b[0;32m---> 38\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m value\u001b[38;5;241m.\u001b[39mwith_traceback(tb)\n\u001b[1;32m     39\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m value\n\u001b[1;32m     40\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connectionpool.py:787\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    784\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    786\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 787\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    788\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    789\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    790\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    791\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    792\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    793\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    795\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    796\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    797\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    798\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    802\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n\u001b[1;32m    803\u001b[0m clean_exit \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connectionpool.py:534\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    532\u001b[0m \u001b[38;5;66;03m# Receive the response from the server\u001b[39;00m\n\u001b[1;32m    533\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 534\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    535\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (BaseSSLError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    536\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_timeout(err\u001b[38;5;241m=\u001b[39me, url\u001b[38;5;241m=\u001b[39murl, timeout_value\u001b[38;5;241m=\u001b[39mread_timeout)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/urllib3/connection.py:516\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    513\u001b[0m _shutdown \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mshutdown\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    515\u001b[0m \u001b[38;5;66;03m# Get the response from http.client.HTTPConnection\u001b[39;00m\n\u001b[0;32m--> 516\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    518\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    519\u001b[0m     assert_header_parsing(httplib_response\u001b[38;5;241m.\u001b[39mmsg)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/http/client.py:1430\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1428\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1429\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1430\u001b[0m         \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1431\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n\u001b[1;32m   1432\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/http/client.py:331\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    329\u001b[0m \u001b[38;5;66;03m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 331\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    332\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n\u001b[1;32m    333\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/http/client.py:300\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    296\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreply:\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mrepr\u001b[39m(line))\n\u001b[1;32m    297\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m line:\n\u001b[1;32m    298\u001b[0m     \u001b[38;5;66;03m# Presumably, the server closed the connection before\u001b[39;00m\n\u001b[1;32m    299\u001b[0m     \u001b[38;5;66;03m# sending a valid response.\u001b[39;00m\n\u001b[0;32m--> 300\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RemoteDisconnected(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRemote end closed connection without\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    301\u001b[0m                              \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m response\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    302\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    303\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m line\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m2\u001b[39m)\n", "\u001b[0;31mProtocolError\u001b[0m: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.support.ui import Select\n", "import time\n", "import undetected_chromedriver as uc\n", "def get_un_documents_zh(symbol):\n", "    # 配置Chrome选项\n", "    chrome_options = Options()\n", "    chrome_options.add_argument('--no-sandbox')\n", "    chrome_options.add_argument('--disable-dev-shm-usage')\n", "    chrome_options.add_argument('--headless')  # 无界面模式\n", "    chrome_options.add_argument('--disable-gpu')\n", "    chrome_options.add_argument('--remote-debugging-port=9222')\n", "    chrome_options.add_argument('--window-size=1920,1080')\n", "\n", "    # 打开联合国正式文件库的检索页面\n", "    driver = uc.Chrome(headless=True,use_subprocess=False)\n", "    # driver = webdriver.Chrome(options=chrome_options) # 初始化Chrome驱动\n", "    driver.get('http://documents.un.org') # 访问目标网站\n", "    # driver.implicitly_wait(10)\n", "\n", "    # 键入检索条件\n", "    input_box = driver.find_element(By.ID, \"symbol\")\n", "    input_box.clear()\n", "    input_box.send_keys(symbol)\n", "\n", "    # 点击检索按钮\n", "    search_button = driver.find_element(By.ID, \"btnSearch\")  # 定位到搜索按钮\n", "    driver.execute_script(\"arguments[0].click();\", search_button) # 使用 JavaScript 强制点击按钮\n", "\n", "    # 等待搜索结果加载完成\n", "    time.sleep(2)\n", "\n", "    # 获取所有\"Details\"按钮\n", "    details_buttons = driver.find_elements(By.CLASS_NAME, \"btn-link-sm\")\n", "\n", "    if len(details_buttons) == 0:\n", "        print('没有找到任何文件储存位置')\n", "    else:\n", "        print(f\"找到 {len(details_buttons)} 个文件储存位置\")  # 打印找到的按钮数量\n", "        for i in range(0, len(details_buttons)):\n", "            time.sleep(1)\n", "\n", "            driver.execute_script(\"arguments[0].click();\", details_buttons[i])\n", "            time.sleep(2)  # 等待页面加载\n", "\n", "            chinese_tag = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, \"//h4[contains(text(), 'Chinese')]\"))\n", "            )\n", "\n", "            pdf_icon = chinese_tag.find_element(By.XPATH, \"./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-pdf')]\")\n", "\n", "            try:\n", "                print(\"点击 'Chinese PDF' 图标\")  # 打印信息确认点击了 PDF 图标\n", "                pdf_icon.click()\n", "\n", "                WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > 1)\n", "                window_handles = driver.window_handles\n", "                pdf_window = window_handles[-1]  # 新打开的 PDF 窗口\n", "                driver.switch_to.window(pdf_window)\n", "\n", "                time.sleep(30) # 确保 PDF 页面加载完成\n", "\n", "                pdf_url = driver.current_url\n", "                print(f\"PDF 链接: {pdf_url}\")\n", "\n", "                driver.close()\n", "                driver.switch_to.window(window_handles[0])  # 切换回主窗口\n", "                print(\"已返回主窗口\")\n", "\n", "                break  # 找到后停止遍历\n", "\n", "            except Exception as e:\n", "                pdf_url = 'None'\n", "                print(f\"无法点击 'Chinese PDF' 图标\") # ，错误: {e}\n", "\n", "        driver.quit()\n", "\n", "get_un_documents_zh(\"A/RES/47/52\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chrome_options = Options()\n", "chrome_options.add_argument('--no-sandbox')\n", "chrome_options.add_argument('--disable-dev-shm-usage')\n", "chrome_options.add_argument('--headless')  # 无界面模式\n", "chrome_options.add_argument('--disable-gpu')\n", "chrome_options.add_argument('--remote-debugging-port=9222')\n", "chrome_options.add_argument('--window-size=1920,1080')\n", "\n", "# 打开联合国正式文件库的检索页面\n", "driver = uc.Chrome(headless=True,use_subprocess=False)\n", "# driver = webdriver.Chrome(options=chrome_options) # 初始化Chrome驱动\n", "driver.get('http://documents.un.org') # 访问目标网站\n", "# driver.implicitly_wait(10)\n", "\n", "# 键入检索条件\n", "input_box = driver.find_element(By.ID, \"symbol\")\n", "input_box.clear()\n", "input_box.send_keys(symbol)\n", "\n", "# 点击检索按钮\n", "search_button = driver.find_element(By.ID, \"btnSearch\")  # 定位到搜索按钮\n", "driver.execute_script(\"arguments[0].click();\", search_button) # 使用 JavaScript 强制点击按钮\n", "\n", "# 等待搜索结果加载完成\n", "time.sleep(2)\n", "\n", "# 获取所有\"Details\"按钮\n", "details_buttons = driver.find_elements(By.CLASS_NAME, \"btn-link-sm\")\n", "\n", "if len(details_buttons) == 0:\n", "    print('没有找到任何文件储存位置')\n", "else:\n", "    print(f\"找到 {len(details_buttons)} 个文件储存位置\")  # 打印找到的按钮数量\n", "    for i in range(0, len(details_buttons)):\n", "        time.sleep(1)\n", "\n", "        driver.execute_script(\"arguments[0].click();\", details_buttons[i])\n", "        time.sleep(2)  # 等待页面加载\n", "\n", "        chinese_tag = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located((By.XPATH, \"//h4[contains(text(), 'Chinese')]\"))\n", "        )\n", "\n", "        pdf_icon = chinese_tag.find_element(By.XPATH, \"./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-pdf')]\")\n", "\n", "        try:\n", "            print(\"点击 'Chinese PDF' 图标\")  # 打印信息确认点击了 PDF 图标\n", "            pdf_icon.click()\n", "\n", "            WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > 1)\n", "            window_handles = driver.window_handles\n", "            pdf_window = window_handles[-1]  # 新打开的 PDF 窗口\n", "            driver.switch_to.window(pdf_window)\n", "\n", "            time.sleep(30) # 确保 PDF 页面加载完成\n", "\n", "            pdf_url = driver.current_url\n", "            print(f\"PDF 链接: {pdf_url}\")\n", "\n", "            driver.close()\n", "            driver.switch_to.window(window_handles[0])  # 切换回主窗口\n", "            print(\"已返回主窗口\")\n", "\n", "            break  # 找到后停止遍历\n", "\n", "        except Exception as e:\n", "            pdf_url = 'None'\n", "            print(f\"无法点击 'Chinese PDF' 图标\") # ，错误: {e}\n", "\n", "    driver.quit()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}