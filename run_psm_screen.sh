#!/bin/bash

# PSM匹配脚本Screen会话启动器
# 使用screen会话，可以重新连接查看进度

SESSION_NAME="psm_matching_$(date +%Y%m%d_%H%M%S)"

echo "=========================================="
echo "PSM Matching Script Screen Session Launcher"
echo "Started at: $(date)"
echo "Session name: $SESSION_NAME"
echo "=========================================="

# 检查screen是否安装
if ! command -v screen &> /dev/null; then
    echo "Error: screen is not installed. Installing..."
    sudo apt-get update && sudo apt-get install -y screen
fi

# 创建新的screen会话并运行脚本
screen -dmS $SESSION_NAME bash -c "
    cd /home/<USER>/repo/disengagement
    echo 'Starting PSM matching script in screen session...'
    echo 'Session started at: $(date)'
    echo 'You can reconnect to this session using: screen -r $SESSION_NAME'
    echo '=========================================='
    python src_new/20250629_PSM_matching_multi_limits.py
    echo '=========================================='
    echo 'PSM matching script completed at: $(date)'
    echo 'Press any key to close this session...'
    read -n 1
"

echo "Screen session '$SESSION_NAME' created successfully!"
echo ""
echo "To reconnect to the session and view progress:"
echo "  screen -r $SESSION_NAME"
echo ""
echo "To list all screen sessions:"
echo "  screen -ls"
echo ""
echo "To disconnect from session (without killing it):"
echo "  Press Ctrl+A, then D"
echo ""
echo "The script will continue running even if you disconnect."
echo "==========================================" 