import pandas as pd
import numpy as np
import pickle
import time
from datetime import datetime
import subprocess
import sys

def run_test_script(script_name):
    """运行测试脚本并捕获输出"""
    print(f"\n{'='*60}")
    print(f"RUNNING {script_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=300)
        end_time = time.time()
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode != 0:
            print(f"Script failed with return code: {result.returncode}")
            return None, end_time - start_time
        
        return result, end_time - start_time
    
    except subprocess.TimeoutExpired:
        print(f"Script {script_name} timed out after 300 seconds")
        return None, 300
    except Exception as e:
        print(f"Error running {script_name}: {e}")
        return None, time.time() - start_time

def load_results(filename):
    """加载测试结果"""
    try:
        with open(filename, 'rb') as f:
            return pickle.load(f)
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return None

def compare_matched_pairs(original_pairs, improved_pairs):
    """详细比较两个匹配结果"""
    print("\n" + "="*60)
    print("DETAILED COMPARISON OF MATCHED PAIRS")
    print("="*60)
    
    # 基本统计
    print(f"Original pairs count: {len(original_pairs)}")
    print(f"Improved pairs count: {len(improved_pairs)}")
    
    if len(original_pairs) != len(improved_pairs):
        print("❌ DIFFERENT NUMBER OF MATCHED PAIRS!")
        return False
    
    # 检查键是否相同
    original_keys = set(original_pairs.keys())
    improved_keys = set(improved_pairs.keys())
    
    if original_keys != improved_keys:
        print("❌ DIFFERENT KEYS IN MATCHED PAIRS!")
        print(f"Keys only in original: {original_keys - improved_keys}")
        print(f"Keys only in improved: {improved_keys - original_keys}")
        return False
    
    print("✅ Same number of pairs and same keys")
    
    # 详细比较每个匹配对
    all_match = True
    for key in original_keys:
        orig = original_pairs[key]
        impr = improved_pairs[key]
        
        # 比较基本信息
        if (orig['burst'] != impr['burst'] or 
            orig['repo_name'] != impr['repo_name'] or 
            orig['treatment_time'] != impr['treatment_time']):
            print(f"❌ Basic info mismatch for key {key}")
            all_match = False
            continue
        
        # 比较控制组数量
        if len(orig['controls']) != len(impr['controls']):
            print(f"❌ Different number of controls for key {key}: {len(orig['controls'])} vs {len(impr['controls'])}")
            all_match = False
            continue
        
        # 比较控制组内容
        orig_control_repos = set(c['repo_name'] for c in orig['controls'])
        impr_control_repos = set(c['repo_name'] for c in impr['controls'])
        
        if orig_control_repos != impr_control_repos:
            print(f"❌ Different control repos for key {key}")
            print(f"  Original: {orig_control_repos}")
            print(f"  Improved: {impr_control_repos}")
            all_match = False
            continue
        
        # 比较特征矩阵
        try:
            # 确保都是numpy数组并且数据类型一致
            orig_treatment = np.array(orig['treatment_features'], dtype=float)
            impr_treatment = np.array(impr['treatment_features'], dtype=float)

            if not np.allclose(orig_treatment, impr_treatment, rtol=1e-10):
                print(f"❌ Treatment features mismatch for key {key}")
                all_match = False
                continue
        except Exception as e:
            print(f"❌ Error comparing treatment features for key {key}: {e}")
            all_match = False
            continue

        try:
            # 确保都是numpy数组并且数据类型一致
            orig_control = np.array(orig['control_features'], dtype=float)
            impr_control = np.array(impr['control_features'], dtype=float)

            if not np.allclose(orig_control, impr_control, rtol=1e-10):
                print(f"❌ Control features mismatch for key {key}")
                all_match = False
                continue
        except Exception as e:
            print(f"❌ Error comparing control features for key {key}: {e}")
            all_match = False
            continue
    
    if all_match:
        print("✅ ALL MATCHED PAIRS ARE IDENTICAL!")
        return True
    else:
        print("❌ SOME DIFFERENCES FOUND IN MATCHED PAIRS")
        return False

def compare_dataframes(df1, df2, name1, name2):
    """比较两个DataFrame"""
    print(f"\nComparing {name1} vs {name2}:")
    
    if df1.shape != df2.shape:
        print(f"❌ Different shapes: {df1.shape} vs {df2.shape}")
        return False
    
    # 比较列名
    if list(df1.columns) != list(df2.columns):
        print(f"❌ Different columns")
        return False
    
    # 比较数值
    try:
        if df1.equals(df2):
            print("✅ DataFrames are identical")
            return True
        else:
            # 检查数值差异
            numeric_cols = df1.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if not np.allclose(df1[col], df2[col], rtol=1e-10, equal_nan=True):
                    print(f"❌ Numeric differences in column {col}")
                    return False
            
            # 检查非数值列
            for col in df1.columns:
                if col not in numeric_cols:
                    if not df1[col].equals(df2[col]):
                        print(f"❌ Differences in column {col}")
                        return False
            
            print("✅ DataFrames are functionally identical")
            return True
    except Exception as e:
        print(f"❌ Error comparing DataFrames: {e}")
        return False

def main():
    """主函数：运行测试并比较结果"""
    print(f"Comparison started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行原始版本测试
    original_result, original_time = run_test_script('test_original_psm.py')
    
    # 运行改进版本测试
    improved_result, improved_time = run_test_script('test_improved_psm.py')
    
    if original_result is None or improved_result is None:
        print("❌ One or both tests failed to run")
        return
    
    # 加载结果
    print("\n" + "="*60)
    print("LOADING AND COMPARING RESULTS")
    print("="*60)
    
    original_data = load_results('test_results_original.pkl')
    improved_data = load_results('test_results_improved.pkl')
    
    if original_data is None or improved_data is None:
        print("❌ Failed to load test results")
        return
    
    # 比较处理时间
    print(f"\nProcessing time comparison:")
    print(f"Original: {original_data['processing_time']:.2f}s")
    print(f"Improved: {improved_data['processing_time']:.2f}s")
    time_diff = improved_data['processing_time'] - original_data['processing_time']
    print(f"Difference: {time_diff:+.2f}s")
    
    # 比较匹配结果
    pairs_match = compare_matched_pairs(
        original_data['matched_pairs'], 
        improved_data['matched_pairs']
    )
    
    # 比较DataFrame
    treatment_match = compare_dataframes(
        original_data['treatment_features_df'],
        improved_data['treatment_features_df'],
        "Original treatment features",
        "Improved treatment features"
    )
    
    control_match = compare_dataframes(
        original_data['control_features_df'],
        improved_data['control_features_df'],
        "Original control features", 
        "Improved control features"
    )
    
    # 最终结果
    print("\n" + "="*60)
    print("FINAL COMPARISON RESULTS")
    print("="*60)
    
    if pairs_match and treatment_match and control_match:
        print("🎉 SUCCESS: All results are identical!")
        print("✅ The improved version produces exactly the same results as the original")
    else:
        print("❌ FAILURE: Results differ between versions")
        print("❌ The improved version does not produce identical results")
    
    print(f"\nComparison completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
