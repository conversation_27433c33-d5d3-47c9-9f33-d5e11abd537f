#!/bin/bash

# =============================================================================
# Run DID Analysis Script
# Purpose: Execute the automated DID analysis R script
# =============================================================================

echo "Starting DID Analysis..."
echo "=================================================="

# Check if R is available
if ! command -v Rscript &> /dev/null; then
    echo "Error: Rscript is not installed or not in PATH"
    exit 1
fi

# Check if the R script exists
if [ ! -f "automated_did_analysis.R" ]; then
    echo "Error: automated_did_analysis.R not found in current directory"
    exit 1
fi

# Run the R script
echo "Executing R script..."
Rscript automated_did_analysis.R

# Check exit status
if [ $? -eq 0 ]; then
    echo "=================================================="
    echo "✅ Analysis completed successfully!"
    echo "=================================================="
else
    echo "=================================================="
    echo "❌ Analysis failed with errors!"
    echo "=================================================="
    exit 1
fi 