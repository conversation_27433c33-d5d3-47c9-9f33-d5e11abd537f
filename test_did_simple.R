#!/usr/bin/env Rscript

# Simple test script for DID analysis with improved error handling
suppressPackageStartupMessages({
  library(stats)
  library(lme4)
  library(readr)
  library(ggplot2)
  library(stargazer)
  library(lmtest)
  library(MuMIn)
  library(lmerTest)
  library(survival)
  library(ggpubr)
  library(survminer)
  library(car)
  library(coxme)
  library(dplyr)
})

# Test configuration
limit <- 180
base_result_dir <- "result/20250629_did_result"
output_dir <- file.path(base_result_dir, paste0("test_attrition_", limit))

# Create output directory
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  cat("Created directory:", output_dir, "\n")
}

# Load test data
data_file <- "/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit180_processed.csv"
cat("Loading data from:", data_file, "\n")

if (!file.exists(data_file)) {
  cat("❌ Data file not found:", data_file, "\n")
  quit(status = 1)
}

compiled_data_test <- read.csv(data_file)
cat("Data loaded. Initial observations:", nrow(compiled_data_test), "\n")

# Simple preprocessing
compiled_data_test <- compiled_data_test %>%
  mutate(
    log_tenure_c = scale(log_tenure),
    log_commit_percent_c = scale(log_commit_percent),
    log_commits_c = scale(log_commits),
    log_project_commits = scale(log_project_commits),
    log_project_contributors = scale(log_project_contributors),
    log_project_age = scale(log_project_age)
  )

# Filter growth phases
compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '', ]
compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'), ]

cat("After preprocessing. Observations:", nrow(compiled_data_test), "\n")

# Set up control parameters
ctrl <- lmerControl(
  optimizer = "nloptwrap",
  optCtrl = list(maxeval = 1e5, xtol_abs = 1e-8, ftol_abs = 1e-8),
  calc.derivs = FALSE
)

# Test function for saving results
save_model_results <- function(model, vif_values, r2_values, model_name, limit, output_dir) {
  file_path <- file.path(output_dir, paste0(model_name, ".txt"))
  
  cat("📝 Saving results to:", file_path, "\n")
  
  tryCatch({
    sink(file_path)
    
    cat("=== DID Model Results:", model_name, "=== (Limit:", limit, ")\n\n")
    cat("Model Formula:\n")
    cat(as.character(formula(model)), "\n\n")
    
    # Check if model converged
    if (is.null(model@optinfo$conv$lme4$messages)) {
      cat("✓ Model converged successfully\n\n")
    } else {
      cat("⚠️  Model convergence warnings:\n")
      cat(model@optinfo$conv$lme4$messages, "\n\n")
    }
    
    # VIF Values with error handling
    cat("VIF Values:\n")
    tryCatch({
      if (!is.null(vif_values) && length(vif_values) > 0) {
        print(vif_values)
      } else {
        cat("VIF calculation failed or returned NULL\n")
      }
    }, error = function(e) {
      cat("Error calculating VIF:", e$message, "\n")
    })
    
    cat("\nModel Summary:\n")
    tryCatch({
      print(summary(model))
    }, error = function(e) {
      cat("Error printing model summary:", e$message, "\n")
    })
    
    # R-squared Values with error handling
    cat("\nR-squared Values:\n")
    tryCatch({
      if (!is.null(r2_values) && length(r2_values) > 0) {
        print(r2_values)
      } else {
        cat("R-squared calculation failed or returned NULL\n")
      }
    }, error = function(e) {
      cat("Error calculating R-squared:", e$message, "\n")
    })
    
    cat("\n=== End of Results ===\n")
    sink()
    
    # Verify file was written
    if (file.exists(file_path) && file.size(file_path) > 0) {
      cat("✓ Successfully saved:", file_path, "(Size:", file.size(file_path), "bytes)\n")
    } else {
      cat("❌ File was not created or is empty:", file_path, "\n")
    }
    
  }, error = function(e) {
    sink()  # Close sink in case of error
    cat("❌ Error saving results for", model_name, ":", e$message, "\n")
  })
}

# Test one simple model
cat("\n--- Testing Simple Model ---\n")
tryCatch({
  cat("Fitting simple PR Throughput model...\n")
  model <- lmer(
    log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
      log_project_commits + log_project_contributors + log_project_age + 
      (1 | time_cohort_effect) + (1 | repo_cohort_effect),
    REML = FALSE, data = compiled_data_test, control = ctrl
  )
  cat("✓ Model fitted successfully\n")
  
  # Calculate VIF and R-squared with error handling
  vif_result <- tryCatch(vif(model), error = function(e) {
    cat("⚠️  VIF calculation failed:", e$message, "\n")
    return(NULL)
  })
  
  r2_result <- tryCatch(r.squaredGLMM(model), error = function(e) {
    cat("⚠️  R-squared calculation failed:", e$message, "\n")
    return(NULL)
  })
  
  save_model_results(model, vif_result, r2_result, 
                    "test_did_main_pr_throughput", limit, output_dir)
  
}, error = function(e) {
  cat("❌ Error fitting model:", e$message, "\n")
})

cat("\nTest completed!\n") 