# Fix for "contrasts dropped from factor due to missing levels" Warnings

## Problem Explanation

The warning messages you're seeing:
```
Warning message: "contrasts dropped from factor project_main_language due to missing levels"
Warning message: "contrasts dropped from factor growth_phase due to missing levels"
```

This happens because:

1. You filter your data to exclude certain levels (like `'before_start'` from `growth_phase`)
2. You create factors and set contrasts using `contr.sum`
3. R tries to create contrast matrices for all possible levels, including the ones that were filtered out
4. R then drops the contrasts for missing levels and warns you

## Solution Options

### Option 1: Explicitly specify levels when creating factors (Recommended)

Replace your current factor creation code:

```r
# Current problematic code:
compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)
compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)
compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = "JavaScript")
compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = "steady")
contrasts(compiled_data_test$project_main_language) <- "contr.sum"
contrasts(compiled_data_test$growth_phase) <- "contr.sum"
```

With this fixed code:

```r
# Fixed code - explicitly specify levels:
compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language, 
                                                  levels = unique(compiled_data_test$project_main_language))
compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase, 
                                         levels = unique(compiled_data_test$growth_phase))

# Set reference levels
compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = "JavaScript")
compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = "steady")

# Set contrasts
contrasts(compiled_data_test$project_main_language) <- "contr.sum"
contrasts(compiled_data_test$growth_phase) <- "contr.sum"
```

### Option 2: Use droplevels() function

```r
# Alternative approach using droplevels():
compiled_data_test$project_main_language <- droplevels(factor(compiled_data_test$project_main_language))
compiled_data_test$growth_phase <- droplevels(factor(compiled_data_test$growth_phase))
compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = "JavaScript")
compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = "steady")
contrasts(compiled_data_test$project_main_language) <- "contr.sum"
contrasts(compiled_data_test$growth_phase) <- "contr.sum"
```

### Option 3: Create factors after all filtering is complete

Move your factor creation code to after all data filtering operations are complete.

## Why This Happens

When you use `factor()` without specifying levels, R automatically determines the levels from the data. However, when you set contrasts like `contr.sum`, R tries to create contrast matrices for all levels. If some levels were present in the original data but filtered out, R will warn you that it's dropping contrasts for those missing levels.

## Verification

After applying the fix, you can verify it worked by checking:

```r
# Check levels
levels(compiled_data_test$project_main_language)
levels(compiled_data_test$growth_phase)

# Check contrasts
contrasts(compiled_data_test$project_main_language)
contrasts(compiled_data_test$growth_phase)
```

You should see no warning messages and the contrasts should be properly set for only the levels that exist in your filtered data.

## Impact on Analysis

The warnings don't affect your analysis results - R automatically handles the missing levels correctly. However, it's good practice to fix them to:
1. Avoid confusion
2. Ensure your factor levels are exactly what you expect
3. Make your code more robust and explicit 