[1] "Model 1: Fixed Effects Only"
           is_post_treatment                   is_treated 
                    1.383628                     1.059279 
         log_project_commits     log_project_contributors 
                    1.966781                     2.232213 
             log_project_age is_post_treatment:is_treated 
                    1.441547                     1.361649 
Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s
  method [lmerModLmerTest]
Formula: 
log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  
    log_project_commits + log_project_contributors + log_project_age +  
    (1 | time_cohort_effect) + (1 | repo_cohort_effect)
   Data: compiled_data_test
Control: ctrl

     AIC      BIC   logLik deviance df.resid 
 6270722  6270853 -3135351  6270702  3482123 

Scaled residuals: 
    Min      1Q  Median      3Q     Max 
-7.8417 -0.4979 -0.1642  0.5282  8.7294 

Random effects:
 Groups             Name        Variance Std.Dev.
 repo_cohort_effect (Intercept) 0.32888  0.5735  
 time_cohort_effect (Intercept) 0.02541  0.1594  
 Residual                       0.30272  0.5502  
Number of obs: 3482133, groups:  
repo_cohort_effect, 139906; time_cohort_effect, 139896

Fixed effects:
                               Estimate Std. Error         df t value Pr(>|t|)
(Intercept)                   7.206e-01  2.348e-03  1.491e+05  306.96   <2e-16
is_post_treatment            -1.917e-02  1.221e-03  1.171e+05  -15.70   <2e-16
is_treated                   -6.718e-02  3.214e-03  1.266e+05  -20.90   <2e-16
log_project_commits           2.676e-01  2.080e-03  1.775e+05  128.67   <2e-16
log_project_contributors      3.261e-01  2.207e-03  1.898e+05  147.74   <2e-16
log_project_age              -2.131e-01  1.589e-03  2.693e+05 -134.08   <2e-16
is_post_treatment:is_treated -9.851e-02  1.186e-03  3.297e+06  -83.09   <2e-16
                                
(Intercept)                  ***
is_post_treatment            ***
is_treated                   ***
log_project_commits          ***
log_project_contributors     ***
log_project_age              ***
is_post_treatment:is_treated ***
---
Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1

Correlation of Fixed Effects:
            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g
is_pst_trtm -0.267                                                 
is_treated  -0.689  0.106                                          
lg_prjct_cm -0.057  0.003  0.084                                   
lg_prjct_cn  0.094 -0.020 -0.128 -0.612                            
log_prjct_g  0.062 -0.160 -0.041 -0.111      -0.332                
is_pst_tr:_  0.124 -0.494 -0.179  0.004       0.005       0.027    
           R2m       R2c
[1,] 0.2562577 0.6573158
[1] "Model 2: Fixed Effects Developer"
                                is_post_treatment 
                                         1.046420 
                                       is_treated 
                                         1.025481 
                              log_project_commits 
                                         1.984939 
                         log_project_contributors 
                                         2.238319 
                                  log_project_age 
                                         1.443082 
        is_post_treatment:is_treated:log_tenure_c 
                                         1.512382 
is_post_treatment:is_treated:log_commit_percent_c 
                                         1.348517 
       is_post_treatment:is_treated:log_commits_c 
                                         1.758813 
Linear mixed model fit by maximum likelihood . t-tests use Satterthwaite's
  method [lmerModLmerTest]
Formula: 
log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  
    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  
    log_project_commits + log_project_contributors + log_project_age +  
    (1 | time_cohort_effect) + (1 | repo_cohort_effect)
   Data: compiled_data_test
Control: ctrl

     AIC      BIC   logLik deviance df.resid 
 6275739  6275896 -3137858  6275715  3482121 

Scaled residuals: 
    Min      1Q  Median      3Q     Max 
-7.7708 -0.4967 -0.1644  0.5286  8.7739 

Random effects:
 Groups             Name        Variance Std.Dev.
 repo_cohort_effect (Intercept) 0.32895  0.5735  
 time_cohort_effect (Intercept) 0.02547  0.1596  
 Residual                       0.30317  0.5506  
Number of obs: 3482133, groups:  
repo_cohort_effect, 139906; time_cohort_effect, 139896

Fixed effects:
                                                    Estimate Std. Error
(Intercept)                                        7.448e-01  2.330e-03
is_post_treatment                                 -6.934e-02  1.063e-03
is_treated                                        -1.148e-01  3.163e-03
log_project_commits                                2.657e-01  2.090e-03
log_project_contributors                           3.247e-01  2.211e-03
log_project_age                                   -2.068e-01  1.591e-03
is_post_treatment:is_treated:log_tenure_c          3.068e-02  1.235e-03
is_post_treatment:is_treated:log_commit_percent_c -4.448e-02  1.173e-03
is_post_treatment:is_treated:log_commits_c         3.058e-03  1.333e-03
                                                          df  t value Pr(>|t|)
(Intercept)                                        1.437e+05  319.653   <2e-16
is_post_treatment                                  6.676e+04  -65.236   <2e-16
is_treated                                         1.178e+05  -36.297   <2e-16
log_project_commits                                1.806e+05  127.108   <2e-16
log_project_contributors                           1.896e+05  146.881   <2e-16
log_project_age                                    2.673e+05 -129.993   <2e-16
is_post_treatment:is_treated:log_tenure_c          9.478e+05   24.832   <2e-16
is_post_treatment:is_treated:log_commit_percent_c  9.217e+05  -37.921   <2e-16
is_post_treatment:is_treated:log_commits_c         9.246e+05    2.294   0.0218
                                                     
(Intercept)                                       ***
is_post_treatment                                 ***
is_treated                                        ***
log_project_commits                               ***
log_project_contributors                          ***
log_project_age                                   ***
is_post_treatment:is_treated:log_tenure_c         ***
is_post_treatment:is_treated:log_commit_percent_c ***
is_post_treatment:is_treated:log_commits_c        *  
---
Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1

Correlation of Fixed Effects:
                           (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn
is_pst_trtm                -0.238                                      
is_treated                 -0.683  0.021                               
lg_prjct_cm                -0.058  0.005  0.086                        
lg_prjct_cn                 0.094 -0.021 -0.130 -0.611                 
log_prjct_g                 0.059 -0.168 -0.036 -0.113      -0.331     
is_pst_trtmnt:s_trtd:lg_t_  0.001 -0.006  0.000  0.030      -0.010     
is_p_:_:___                 0.001 -0.001 -0.001  0.056       0.019     
is_pst_trtmnt:s_trtd:lg_c_  0.003  0.003 -0.005 -0.093       0.034     
                           lg_prjct_g is_pst_trtmnt:s_trtd:lg_t_ i__:_:___
is_pst_trtm                                                               
is_treated                                                                
lg_prjct_cm                                                               
lg_prjct_cn                                                               
log_prjct_g                                                               
is_pst_trtmnt:s_trtd:lg_t_  0.013                                         
is_p_:_:___                -0.041     -0.101                              
is_pst_trtmnt:s_trtd:lg_c_  0.020     -0.491                     -0.382   
           R2m      R2c
[1,] 0.2583623 0.658086
[1] "Model 3: Fixed Effects Repo Add Two"
                                                                           GVIF
is_post_treatment                                                      1.385476
is_treated                                                             1.060416
log_project_commits                                                    1.998691
log_project_contributors                                               2.285042
log_project_age                                                        1.453785
is_post_treatment:is_treated:growth_phase                              3.545847
is_post_treatment:is_treated:project_main_language                     1.489240
is_post_treatment:is_treated:log_newcomers                             2.980242
is_post_treatment:is_treated:log_project_commits_before_treatment      2.206018
is_post_treatment:is_treated:log_project_contributors_before_treatment 2.772346
is_post_treatment:is_treated:log_project_age_before_treatment          1.652765
                                                                       Df
is_post_treatment                                                       1
is_treated                                                              1
log_project_commits                                                     1
log_project_contributors                                                1
log_project_age                                                         1
is_post_treatment:is_treated:growth_phase                               5
is_post_treatment:is_treated:project_main_language                      9
is_post_treatment:is_treated:log_newcomers                              1
is_post_treatment:is_treated:log_project_commits_before_treatment       1
is_post_treatment:is_treated:log_project_contributors_before_treatment  1
is_post_treatment:is_treated:log_project_age_before_treatment           1
                                                                       GVIF^(1/(2*Df))
is_post_treatment                                                             1.177062
is_treated                                                                    1.029765
log_project_commits                                                           1.413751
log_project_contributors                                                      1.511636
log_project_age                                                               1.205730
is_post_treatment:is_treated:growth_phase                                     1.134938
is_post_treatment:is_treated:project_main_language                            1.022372
is_post_treatment:is_treated:log_newcomers                                    1.726338
is_post_treatment:is_treated:log_project_commits_before_treatment             1.485267
is_post_treatment:is_treated:log_project_contributors_before_treatment        1.665036
is_post_treatment:is_treated:log_project_age_before_treatment                 1.285599
Linear mixed model fit by maximum likelihood . t-tests use Satterthwaite's
  method [lmerModLmerTest]
Formula: 
log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:growth_phase +  
    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:log_newcomers +  
    is_post_treatment:is_treated:log_project_commits_before_treatment +  
    is_post_treatment:is_treated:log_project_contributors_before_treatment +  
    is_post_treatment:is_treated:log_project_age_before_treatment +  
    log_project_commits + log_project_contributors + log_project_age +  
    (1 | time_cohort_effect) + (1 | repo_cohort_effect)
   Data: compiled_data_test
Control: ctrl

     AIC      BIC   logLik deviance df.resid 
 6260599  6260952 -3130272  6260545  3482106 

Scaled residuals: 
    Min      1Q  Median      3Q     Max 
-7.8302 -0.5017 -0.1670  0.5267  8.7488 

Random effects:
 Groups             Name        Variance Std.Dev.
 repo_cohort_effect (Intercept) 0.32175  0.5672  
 time_cohort_effect (Intercept) 0.02513  0.1585  
 Residual                       0.30210  0.5496  
Number of obs: 3482133, groups:  
repo_cohort_effect, 139906; time_cohort_effect, 139896

Fixed effects:
                                                                         Estimate
(Intercept)                                                             7.188e-01
is_post_treatment                                                      -1.880e-02
is_treated                                                             -6.393e-02
log_project_commits                                                     2.688e-01
log_project_contributors                                                3.078e-01
log_project_age                                                        -2.049e-01
is_post_treatment:is_treated:growth_phasesteady                        -2.056e-01
is_post_treatment:is_treated:growth_phaseaccelerating                  -2.913e-01
is_post_treatment:is_treated:growth_phasedecelerating                  -1.900e-01
is_post_treatment:is_treated:growth_phasefirst 3 months                -8.472e-01
is_post_treatment:is_treated:growth_phasesaturation                    -2.106e-01
is_post_treatment:is_treated:project_main_language1                    -6.457e-03
is_post_treatment:is_treated:project_main_language2                     1.048e-02
is_post_treatment:is_treated:project_main_language3                     1.294e-02
is_post_treatment:is_treated:project_main_language4                     2.439e-03
is_post_treatment:is_treated:project_main_language5                     6.736e-03
is_post_treatment:is_treated:project_main_language6                     6.396e-03
is_post_treatment:is_treated:project_main_language7                    -7.035e-03
is_post_treatment:is_treated:project_main_language8                    -5.418e-03
is_post_treatment:is_treated:project_main_language9                    -2.552e-02
is_post_treatment:is_treated:log_newcomers                              9.316e-02
is_post_treatment:is_treated:log_project_commits_before_treatment       2.386e-03
is_post_treatment:is_treated:log_project_contributors_before_treatment -3.932e-02
is_post_treatment:is_treated:log_project_age_before_treatment           2.767e-02
                                                                       Std. Error
(Intercept)                                                             2.325e-03
is_post_treatment                                                       1.217e-03
is_treated                                                              3.182e-03
log_project_commits                                                     2.078e-03
log_project_contributors                                                2.213e-03
log_project_age                                                         1.584e-03
is_post_treatment:is_treated:growth_phasesteady                         2.190e-03
is_post_treatment:is_treated:growth_phaseaccelerating                   3.393e-03
is_post_treatment:is_treated:growth_phasedecelerating                   2.276e-03
is_post_treatment:is_treated:growth_phasefirst 3 months                 5.185e-02
is_post_treatment:is_treated:growth_phasesaturation                     3.297e-03
is_post_treatment:is_treated:project_main_language1                     2.383e-03
is_post_treatment:is_treated:project_main_language2                     3.364e-03
is_post_treatment:is_treated:project_main_language3                     3.871e-03
is_post_treatment:is_treated:project_main_language4                     4.284e-03
is_post_treatment:is_treated:project_main_language5                     3.138e-03
is_post_treatment:is_treated:project_main_language6                     3.128e-03
is_post_treatment:is_treated:project_main_language7                     3.949e-03
is_post_treatment:is_treated:project_main_language8                     2.221e-03
is_post_treatment:is_treated:project_main_language9                     4.726e-03
is_post_treatment:is_treated:log_newcomers                              1.019e-03
is_post_treatment:is_treated:log_project_commits_before_treatment       1.488e-03
is_post_treatment:is_treated:log_project_contributors_before_treatment  1.668e-03
is_post_treatment:is_treated:log_project_age_before_treatment           1.287e-03
                                                                               df
(Intercept)                                                             1.485e+05
is_post_treatment                                                       1.152e+05
is_treated                                                              1.254e+05
log_project_commits                                                     1.834e+05
log_project_contributors                                                1.950e+05
log_project_age                                                         2.561e+05
is_post_treatment:is_treated:growth_phasesteady                         1.526e+06
is_post_treatment:is_treated:growth_phaseaccelerating                   1.143e+06
is_post_treatment:is_treated:growth_phasedecelerating                   1.466e+06
is_post_treatment:is_treated:growth_phasefirst 3 months                 9.482e+05
is_post_treatment:is_treated:growth_phasesaturation                     1.124e+06
is_post_treatment:is_treated:project_main_language1                     9.247e+05
is_post_treatment:is_treated:project_main_language2                     9.291e+05
is_post_treatment:is_treated:project_main_language3                     9.242e+05
is_post_treatment:is_treated:project_main_language4                     9.285e+05
is_post_treatment:is_treated:project_main_language5                     9.306e+05
is_post_treatment:is_treated:project_main_language6                     9.298e+05
is_post_treatment:is_treated:project_main_language7                     9.217e+05
is_post_treatment:is_treated:project_main_language8                     9.262e+05
is_post_treatment:is_treated:project_main_language9                     9.259e+05
is_post_treatment:is_treated:log_newcomers                              9.427e+05
is_post_treatment:is_treated:log_project_commits_before_treatment       8.974e+05
is_post_treatment:is_treated:log_project_contributors_before_treatment  9.045e+05
is_post_treatment:is_treated:log_project_age_before_treatment           9.551e+05
                                                                        t value
(Intercept)                                                             309.230
is_post_treatment                                                       -15.451
is_treated                                                              -20.091
log_project_commits                                                     129.370
log_project_contributors                                                139.075
log_project_age                                                        -129.350
is_post_treatment:is_treated:growth_phasesteady                         -93.906
is_post_treatment:is_treated:growth_phaseaccelerating                   -85.856
is_post_treatment:is_treated:growth_phasedecelerating                   -83.485
is_post_treatment:is_treated:growth_phasefirst 3 months                 -16.339
is_post_treatment:is_treated:growth_phasesaturation                     -63.887
is_post_treatment:is_treated:project_main_language1                      -2.709
is_post_treatment:is_treated:project_main_language2                       3.116
is_post_treatment:is_treated:project_main_language3                       3.343
is_post_treatment:is_treated:project_main_language4                       0.569
is_post_treatment:is_treated:project_main_language5                       2.147
is_post_treatment:is_treated:project_main_language6                       2.045
is_post_treatment:is_treated:project_main_language7                      -1.781
is_post_treatment:is_treated:project_main_language8                      -2.439
is_post_treatment:is_treated:project_main_language9                      -5.401
is_post_treatment:is_treated:log_newcomers                               91.452
is_post_treatment:is_treated:log_project_commits_before_treatment         1.603
is_post_treatment:is_treated:log_project_contributors_before_treatment  -23.574
is_post_treatment:is_treated:log_project_age_before_treatment            21.509
                                                                       Pr(>|t|)
(Intercept)                                                             < 2e-16
is_post_treatment                                                       < 2e-16
is_treated                                                              < 2e-16
log_project_commits                                                     < 2e-16
log_project_contributors                                                < 2e-16
log_project_age                                                         < 2e-16
is_post_treatment:is_treated:growth_phasesteady                         < 2e-16
is_post_treatment:is_treated:growth_phaseaccelerating                   < 2e-16
is_post_treatment:is_treated:growth_phasedecelerating                   < 2e-16
is_post_treatment:is_treated:growth_phasefirst 3 months                 < 2e-16
is_post_treatment:is_treated:growth_phasesaturation                     < 2e-16
is_post_treatment:is_treated:project_main_language1                    0.006750
is_post_treatment:is_treated:project_main_language2                    0.001835
is_post_treatment:is_treated:project_main_language3                    0.000828
is_post_treatment:is_treated:project_main_language4                    0.569059
is_post_treatment:is_treated:project_main_language5                    0.031798
is_post_treatment:is_treated:project_main_language6                    0.040885
is_post_treatment:is_treated:project_main_language7                    0.074872
is_post_treatment:is_treated:project_main_language8                    0.014710
is_post_treatment:is_treated:project_main_language9                    6.63e-08
is_post_treatment:is_treated:log_newcomers                              < 2e-16
is_post_treatment:is_treated:log_project_commits_before_treatment      0.108950
is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16
is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16
                                                                          
(Intercept)                                                            ***
is_post_treatment                                                      ***
is_treated                                                             ***
log_project_commits                                                    ***
log_project_contributors                                               ***
log_project_age                                                        ***
is_post_treatment:is_treated:growth_phasesteady                        ***
is_post_treatment:is_treated:growth_phaseaccelerating                  ***
is_post_treatment:is_treated:growth_phasedecelerating                  ***
is_post_treatment:is_treated:growth_phasefirst 3 months                ***
is_post_treatment:is_treated:growth_phasesaturation                    ***
is_post_treatment:is_treated:project_main_language1                    ** 
is_post_treatment:is_treated:project_main_language2                    ** 
is_post_treatment:is_treated:project_main_language3                    ***
is_post_treatment:is_treated:project_main_language4                       
is_post_treatment:is_treated:project_main_language5                    *  
is_post_treatment:is_treated:project_main_language6                    *  
is_post_treatment:is_treated:project_main_language7                    .  
is_post_treatment:is_treated:project_main_language8                    *  
is_post_treatment:is_treated:project_main_language9                    ***
is_post_treatment:is_treated:log_newcomers                             ***
is_post_treatment:is_treated:log_project_commits_before_treatment         
is_post_treatment:is_treated:log_project_contributors_before_treatment ***
is_post_treatment:is_treated:log_project_age_before_treatment          ***
---
Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1
           R2m       R2c
[1,] 0.2563659 0.6538347
[1] "Model 4: Fixed Effects Developer Repo"
                                                                           GVIF
is_post_treatment                                                      1.382680
is_treated                                                             1.059941
log_project_commits                                                    1.999039
log_project_contributors                                               2.285493
log_project_age                                                        1.454216
is_post_treatment:is_treated:log_tenure_c                              2.024961
is_post_treatment:is_treated:log_commit_percent_c                      2.997890
is_post_treatment:is_treated:log_commits_c                             4.832475
is_post_treatment:is_treated:growth_phase                              3.666260
is_post_treatment:is_treated:project_main_language                     1.522147
is_post_treatment:is_treated:log_newcomers                             3.004936
is_post_treatment:is_treated:log_project_commits_before_treatment      5.633637
is_post_treatment:is_treated:log_project_contributors_before_treatment 3.312701
is_post_treatment:is_treated:log_project_age_before_treatment          1.960965
                                                                       Df
is_post_treatment                                                       1
is_treated                                                              1
log_project_commits                                                     1
log_project_contributors                                                1
log_project_age                                                         1
is_post_treatment:is_treated:log_tenure_c                               1
is_post_treatment:is_treated:log_commit_percent_c                       1
is_post_treatment:is_treated:log_commits_c                              1
is_post_treatment:is_treated:growth_phase                               4
is_post_treatment:is_treated:project_main_language                      9
is_post_treatment:is_treated:log_newcomers                              1
is_post_treatment:is_treated:log_project_commits_before_treatment       1
is_post_treatment:is_treated:log_project_contributors_before_treatment  1
is_post_treatment:is_treated:log_project_age_before_treatment           1
                                                                       GVIF^(1/(2*Df))
is_post_treatment                                                             1.175874
is_treated                                                                    1.029534
log_project_commits                                                           1.413874
log_project_contributors                                                      1.511785
log_project_age                                                               1.205909
is_post_treatment:is_treated:log_tenure_c                                     1.423011
is_post_treatment:is_treated:log_commit_percent_c                             1.731442
is_post_treatment:is_treated:log_commits_c                                    2.198289
is_post_treatment:is_treated:growth_phase                                     1.176327
is_post_treatment:is_treated:project_main_language                            1.023615
is_post_treatment:is_treated:log_newcomers                                    1.733475
is_post_treatment:is_treated:log_project_commits_before_treatment             2.373528
is_post_treatment:is_treated:log_project_contributors_before_treatment        1.820083
is_post_treatment:is_treated:log_project_age_before_treatment                 1.400344
Linear mixed model fit by maximum likelihood . t-tests use Satterthwaite's
  method [lmerModLmerTest]
Formula: 
log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  
    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  
    is_post_treatment:is_treated:growth_phase + is_post_treatment:is_treated:project_main_language +  
    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  
    is_post_treatment:is_treated:log_project_contributors_before_treatment +  
    is_post_treatment:is_treated:log_project_age_before_treatment +  
    log_project_commits + log_project_contributors + log_project_age +  
    (1 | time_cohort_effect) + (1 | repo_cohort_effect)
   Data: compiled_data_test
Control: ctrl

     AIC      BIC   logLik deviance df.resid 
 6260339  6260718 -3130141  6260281  3482104 

Scaled residuals: 
    Min      1Q  Median      3Q     Max 
-7.8187 -0.5016 -0.1663  0.5267  8.7507 

Random effects:
 Groups             Name        Variance Std.Dev.
 repo_cohort_effect (Intercept) 0.32202  0.5675  
 time_cohort_effect (Intercept) 0.02502  0.1582  
 Residual                       0.30209  0.5496  
Number of obs: 3482133, groups:  
repo_cohort_effect, 139906; time_cohort_effect, 139896

Fixed effects:
                                                                         Estimate
(Intercept)                                                             7.200e-01
is_post_treatment                                                      -2.095e-02
is_treated                                                             -6.620e-02
log_project_commits                                                     2.679e-01
log_project_contributors                                                3.093e-01
log_project_age                                                        -2.055e-01
is_post_treatment:is_treated:log_tenure_c                               5.032e-02
is_post_treatment:is_treated:log_commit_percent_c                      -1.344e-02
is_post_treatment:is_treated:log_commits_c                             -2.740e-02
is_post_treatment:is_treated:growth_phase1                             -2.002e-01
is_post_treatment:is_treated:growth_phase2                             -2.721e-01
is_post_treatment:is_treated:growth_phase3                             -1.853e-01
is_post_treatment:is_treated:growth_phase4                              8.603e-01
is_post_treatment:is_treated:project_main_language1                    -4.661e-03
is_post_treatment:is_treated:project_main_language2                     3.356e-03
is_post_treatment:is_treated:project_main_language3                     3.617e-03
is_post_treatment:is_treated:project_main_language4                     5.125e-03
is_post_treatment:is_treated:project_main_language5                     5.280e-03
is_post_treatment:is_treated:project_main_language6                     2.561e-03
is_post_treatment:is_treated:project_main_language7                    -4.986e-03
is_post_treatment:is_treated:project_main_language8                    -8.098e-03
is_post_treatment:is_treated:project_main_language9                    -1.166e-02
is_post_treatment:is_treated:log_newcomers                              9.085e-02
is_post_treatment:is_treated:log_project_commits_before_treatment       1.307e-02
is_post_treatment:is_treated:log_project_contributors_before_treatment -4.868e-02
is_post_treatment:is_treated:log_project_age_before_treatment           1.294e-02
                                                                       Std. Error
(Intercept)                                                             2.325e-03
is_post_treatment                                                       1.214e-03
is_treated                                                              3.183e-03
log_project_commits                                                     2.079e-03
log_project_contributors                                                2.214e-03
log_project_age                                                         1.584e-03
is_post_treatment:is_treated:log_tenure_c                               1.425e-03
is_post_treatment:is_treated:log_commit_percent_c                       1.743e-03
is_post_treatment:is_treated:log_commits_c                              2.203e-03
is_post_treatment:is_treated:growth_phase1                              2.178e-03
is_post_treatment:is_treated:growth_phase2                              3.369e-03
is_post_treatment:is_treated:growth_phase3                              2.265e-03
is_post_treatment:is_treated:growth_phase4                              7.515e-03
is_post_treatment:is_treated:project_main_language1                     2.393e-03
is_post_treatment:is_treated:project_main_language2                     3.368e-03
is_post_treatment:is_treated:project_main_language3                     3.881e-03
is_post_treatment:is_treated:project_main_language4                     4.281e-03
is_post_treatment:is_treated:project_main_language5                     3.137e-03
is_post_treatment:is_treated:project_main_language6                     3.129e-03
is_post_treatment:is_treated:project_main_language7                     3.949e-03
is_post_treatment:is_treated:project_main_language8                     2.218e-03
is_post_treatment:is_treated:project_main_language9                     4.729e-03
is_post_treatment:is_treated:log_newcomers                              1.023e-03
is_post_treatment:is_treated:log_project_commits_before_treatment       2.377e-03
is_post_treatment:is_treated:log_project_contributors_before_treatment  1.823e-03
is_post_treatment:is_treated:log_project_age_before_treatment           1.401e-03
                                                                               df
(Intercept)                                                             1.483e+05
is_post_treatment                                                       1.145e+05
is_treated                                                              1.253e+05
log_project_commits                                                     1.833e+05
log_project_contributors                                                1.948e+05
log_project_age                                                         2.560e+05
is_post_treatment:is_treated:log_tenure_c                               9.250e+05
is_post_treatment:is_treated:log_commit_percent_c                       9.196e+05
is_post_treatment:is_treated:log_commits_c                              9.305e+05
is_post_treatment:is_treated:growth_phase1                              1.488e+06
is_post_treatment:is_treated:growth_phase2                              1.123e+06
is_post_treatment:is_treated:growth_phase3                              1.432e+06
is_post_treatment:is_treated:growth_phase4                              1.751e+06
is_post_treatment:is_treated:project_main_language1                     9.212e+05
is_post_treatment:is_treated:project_main_language2                     9.256e+05
is_post_treatment:is_treated:project_main_language3                     9.205e+05
is_post_treatment:is_treated:project_main_language4                     9.249e+05
is_post_treatment:is_treated:project_main_language5                     9.270e+05
is_post_treatment:is_treated:project_main_language6                     9.261e+05
is_post_treatment:is_treated:project_main_language7                     9.181e+05
is_post_treatment:is_treated:project_main_language8                     9.227e+05
is_post_treatment:is_treated:project_main_language9                     9.223e+05
is_post_treatment:is_treated:log_newcomers                              9.383e+05
is_post_treatment:is_treated:log_project_commits_before_treatment       9.168e+05
is_post_treatment:is_treated:log_project_contributors_before_treatment  9.055e+05
is_post_treatment:is_treated:log_project_age_before_treatment           9.512e+05
                                                                        t value
(Intercept)                                                             309.675
is_post_treatment                                                       -17.260
is_treated                                                              -20.801
log_project_commits                                                     128.875
log_project_contributors                                                139.707
log_project_age                                                        -129.676
is_post_treatment:is_treated:log_tenure_c                                35.322
is_post_treatment:is_treated:log_commit_percent_c                        -7.710
is_post_treatment:is_treated:log_commits_c                              -12.441
is_post_treatment:is_treated:growth_phase1                              -91.940
is_post_treatment:is_treated:growth_phase2                              -80.761
is_post_treatment:is_treated:growth_phase3                              -81.822
is_post_treatment:is_treated:growth_phase4                              114.467
is_post_treatment:is_treated:project_main_language1                      -1.948
is_post_treatment:is_treated:project_main_language2                       0.996
is_post_treatment:is_treated:project_main_language3                       0.932
is_post_treatment:is_treated:project_main_language4                       1.197
is_post_treatment:is_treated:project_main_language5                       1.683
is_post_treatment:is_treated:project_main_language6                       0.819
is_post_treatment:is_treated:project_main_language7                      -1.263
is_post_treatment:is_treated:project_main_language8                      -3.650
is_post_treatment:is_treated:project_main_language9                      -2.466
is_post_treatment:is_treated:log_newcomers                               88.845
is_post_treatment:is_treated:log_project_commits_before_treatment         5.498
is_post_treatment:is_treated:log_project_contributors_before_treatment  -26.706
is_post_treatment:is_treated:log_project_age_before_treatment             9.237
                                                                       Pr(>|t|)
(Intercept)                                                             < 2e-16
is_post_treatment                                                       < 2e-16
is_treated                                                              < 2e-16
log_project_commits                                                     < 2e-16
log_project_contributors                                                < 2e-16
log_project_age                                                         < 2e-16
is_post_treatment:is_treated:log_tenure_c                               < 2e-16
is_post_treatment:is_treated:log_commit_percent_c                      1.26e-14
is_post_treatment:is_treated:log_commits_c                              < 2e-16
is_post_treatment:is_treated:growth_phase1                              < 2e-16
is_post_treatment:is_treated:growth_phase2                              < 2e-16
is_post_treatment:is_treated:growth_phase3                              < 2e-16
is_post_treatment:is_treated:growth_phase4                              < 2e-16
is_post_treatment:is_treated:project_main_language1                    0.051447
is_post_treatment:is_treated:project_main_language2                    0.319040
is_post_treatment:is_treated:project_main_language3                    0.351279
is_post_treatment:is_treated:project_main_language4                    0.231313
is_post_treatment:is_treated:project_main_language5                    0.092350
is_post_treatment:is_treated:project_main_language6                    0.413038
is_post_treatment:is_treated:project_main_language7                    0.206745
is_post_treatment:is_treated:project_main_language8                    0.000262
is_post_treatment:is_treated:project_main_language9                    0.013677
is_post_treatment:is_treated:log_newcomers                              < 2e-16
is_post_treatment:is_treated:log_project_commits_before_treatment      3.84e-08
is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16
is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16
                                                                          
(Intercept)                                                            ***
is_post_treatment                                                      ***
is_treated                                                             ***
log_project_commits                                                    ***
log_project_contributors                                               ***
log_project_age                                                        ***
is_post_treatment:is_treated:log_tenure_c                              ***
is_post_treatment:is_treated:log_commit_percent_c                      ***
is_post_treatment:is_treated:log_commits_c                             ***
is_post_treatment:is_treated:growth_phase1                             ***
is_post_treatment:is_treated:growth_phase2                             ***
is_post_treatment:is_treated:growth_phase3                             ***
is_post_treatment:is_treated:growth_phase4                             ***
is_post_treatment:is_treated:project_main_language1                    .  
is_post_treatment:is_treated:project_main_language2                       
is_post_treatment:is_treated:project_main_language3                       
is_post_treatment:is_treated:project_main_language4                       
is_post_treatment:is_treated:project_main_language5                    .  
is_post_treatment:is_treated:project_main_language6                       
is_post_treatment:is_treated:project_main_language7                       
is_post_treatment:is_treated:project_main_language8                    ***
is_post_treatment:is_treated:project_main_language9                    *  
is_post_treatment:is_treated:log_newcomers                             ***
is_post_treatment:is_treated:log_project_commits_before_treatment      ***
is_post_treatment:is_treated:log_project_contributors_before_treatment ***
is_post_treatment:is_treated:log_project_age_before_treatment          ***
---
Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1
           R2m       R2c
[1,] 0.2566928 0.6540825
