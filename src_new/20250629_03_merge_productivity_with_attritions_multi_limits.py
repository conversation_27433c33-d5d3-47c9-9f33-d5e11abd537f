import pandas as pd
import numpy as np
import os
import glob
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import time

# Configure logging to output to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # Output to console
    ]
)

# Define the limits to process
limits = [180, 270, 450, 365]  # Added 365 for 4 limits
# limits = [365]

def sigmoid(x):
    """Sigmoid function."""
    return 1 / (1 + np.exp(-x))

# Read the productivity data - using correct path
productivity = pd.read_csv('../data/2025_0227_productivity.csv')
productivity["datetime"] = pd.to_datetime(productivity["datetime"])
global_min_time = productivity["datetime"].min()
productivity["standardized_time_weeks"] = (
    (productivity["datetime"] - global_min_time).dt.days // 7
).astype(int)

## 1. rolling slope trend
def calculate_rolling_slope_window(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling slope for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the slope for.
        output_column (str): Name of the output column for slopes.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling slope column.
    """
    
    def rolling_slope(df, column, window):
        y = df[column].values
        n = window
        if len(y) < n:
            return pd.Series([np.nan] * len(y), index=df.index)  # If not enough data, return NaN
        
        x = np.arange(1, n + 1)
        sum_x = np.sum(x)
        sum_x2 = np.sum(x ** 2)
        denominator = n * sum_x2 - sum_x ** 2
        
        sum_y = np.convolve(y, np.ones(n), 'valid')
        sum_xy = np.convolve(y, x[::-1], 'valid')
        
        slopes = (n * sum_xy - sum_x * sum_y) / denominator
        return pd.Series([np.nan] * (n - 1) + list(slopes), index=df.index)
    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling slope calculation for each group
    data[output_column] = data.groupby(group_column).apply(
        lambda group: rolling_slope(group, target_column, window_size)
    ).reset_index(level=0, drop=True)
    
    return data

## 2. rolling mean
def calculating_rolling_mean(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling mean for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the mean for.
        output_column (str): Name of the output column for means.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling mean column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling mean calculation for each group
    data[output_column] = data.groupby(group_column)[target_column].transform(
        lambda x: x.rolling(window=window_size, min_periods=window_size).mean()
    )
    
    return data

## 3. rolling rate of change
def calculate_rolling_rate_of_change(data, group_column, target_column, window_size, output_column, sort_column):
    """
    Calculate the rolling rate of change for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the rate of change for.
        output_column (str): Name of the output column for rate of change.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling rate of change column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling rate of change calculation for each group
    data['O_i_t_minus_1'] = data.groupby(group_column)[target_column].shift(1)
    data['I_it'] = np.log(
        (data[target_column] + 1) / (data['O_i_t_minus_1'] + 1)
    )
    data['sum_I_it_last_windows'] = data.groupby(group_column)['I_it'].transform(
        lambda x: x.rolling(window=window_size, min_periods=window_size).sum()
    )
    data[output_column] = data['sum_I_it_last_windows']
    
    # remove intermediate columns
    data = data.drop(columns=['O_i_t_minus_1', 'I_it', 'sum_I_it_last_windows'])
    
    return data

# 20250227 new 
def unify_repo_productivity_data(productivity_data):
    '''fill blank values in uniformed timeweeks using vectorized operations'''
    logging.info("Unifying productivity data...")
    
    # 聚合数据，确保每个仓库和周的pr_throughput唯一
    productivity_data_agg = productivity_data.groupby(
        ['repo_name', 'standardized_time_weeks'], as_index=False
    )['pr_throughput'].sum()
    
    # 找出每个仓库有非零产出的周范围
    non_zero = productivity_data_agg['pr_throughput'] > 0
    if not non_zero.any():  # 处理全为0的情况
        return pd.DataFrame(columns=['repo_name', 'standardized_time_weeks', 'pr_throughput'])
    
    non_zero_data = productivity_data_agg[non_zero]
    repo_first_last = non_zero_data.groupby('repo_name')['standardized_time_weeks'].agg(
        first_week=('min'),
        last_week=('max')
    ).reset_index()
    
    # 生成每个仓库的有效周范围
    repo_first_last['weeks'] = repo_first_last.apply(
        lambda x: list(range(x['first_week'], x['last_week'] + 1)),
        axis=1
    )
    unified_frame = repo_first_last.explode('weeks')[['repo_name', 'weeks']]
    unified_frame.rename(columns={'weeks': 'standardized_time_weeks'}, inplace=True)
    
    # 合并数据并填充缺失值
    unified_productivity_data = unified_frame.merge(
        productivity_data_agg,
        on=['repo_name', 'standardized_time_weeks'],
        how='left'
    )
    unified_productivity_data['pr_throughput'] = unified_productivity_data['pr_throughput'].fillna(0)
    
    return unified_productivity_data.reset_index(drop=True)

def process_single_limit(limit, p_test, global_min_time, output_dir):
    """
    Process a single limit value in parallel
    
    Parameters:
        limit (int): The limit value to process
        p_test (pd.DataFrame): Processed productivity data
        global_min_time (pd.Timestamp): Global minimum time for standardization
        output_dir (str): Output directory path
    
    Returns:
        dict: Processing results including limit, success status, and metrics
    """
    try:
        logging.info(f"Starting processing for limit: {limit}")
        
        # Read the attrition file for this limit
        attrition_file = f'../data/attrition_csv/attritions_add_all_variable_{limit}.csv'
        
        if not os.path.exists(attrition_file):
            logging.warning(f"Attrition file {attrition_file} not found, skipping...")
            return {
                'limit': limit,
                'success': False,
                'message': f'Attrition file not found: {attrition_file}'
            }
        
        attritions = pd.read_csv(attrition_file)
        logging.info(f"Loaded attrition data for limit {limit}: {attritions.shape}")
        
        # Standardize time to weeks
        attritions['standardized_time_weeks'] = (
            (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
        ).astype(int)
        
        # Merge productivity data with attrition data
        productivity_merged = p_test.merge(
            attritions[['repo_name', 'standardized_time_weeks', 'someone_left', 'tenure', 'commit_percent', 'commits', 'burst', 'attrition_count', 'growth_phase', 'num_newcomers']],
            on=['repo_name', 'standardized_time_weeks'],
            how='left'
        )
        # change name num_newcomers into newcomers
        productivity_merged.rename(columns={'num_newcomers': 'newcomers'}, inplace=True)
        
        # Fill missing values for someone_left column
        productivity_merged['someone_left'] = productivity_merged['someone_left'].fillna(0).astype(int)
        
        # Save the merged data
        output_file = f'{output_dir}productivity_with_propensity_scores_with_attritions_{limit}.csv'
        productivity_merged.to_csv(output_file, index=False)
        
        attrition_events = productivity_merged['someone_left'].sum()
        logging.info(f"Completed limit {limit} - Shape: {productivity_merged.shape}, Attrition events: {attrition_events}")
        
        return {
            'limit': limit,
            'success': True,
            'output_file': output_file,
            'shape': productivity_merged.shape,
            'attrition_events': attrition_events
        }
        
    except Exception as e:
        logging.error(f"Error processing limit {limit}: {str(e)}")
        return {
            'limit': limit,
            'success': False,
            'message': f'Error: {str(e)}'
        }

# p_test is the same meaning as productivity
p_test = unify_repo_productivity_data(productivity)

# calculate three features
window_size = 12
p_test = calculate_rolling_slope_window(p_test, 'repo_name', 'pr_throughput', 'rolling_slope', 12, 'standardized_time_weeks')
p_test = calculating_rolling_mean(p_test, 'repo_name', 'pr_throughput', 'rolling_mean', 12, 'standardized_time_weeks')
p_test = calculate_rolling_rate_of_change(p_test, 'repo_name', 'pr_throughput', 12, 'rolling_rate_of_change', 'standardized_time_weeks')

p_test['feature_sigmod_add'] = 1 / (1 + np.exp(-p_test['rolling_rate_of_change'] - p_test['rolling_mean']))

# Add back additional columns from original productivity data
logging.info("Adding additional columns from original productivity data...")
productivity_week = productivity.copy()
# for each repo_name within same standardized_time_weeks, just save the one with latest datetime
productivity_week = productivity_week.sort_values(by=['repo_name', 'datetime'], ascending=False)
productivity_week = productivity_week.drop_duplicates(subset=['repo_name', 'standardized_time_weeks'])
productivity_week = productivity_week.drop(columns=['datetime'])

# Merge additional columns back to p_test
p_test = p_test.merge(
    productivity_week[['repo_name', 'standardized_time_weeks', 'project_commits', 'project_contributors', 'project_age']],
    on=['repo_name', 'standardized_time_weeks'],
    how='left'
)

logging.info("Productivity data processing completed!")
logging.info(f"Productivity data shape: {p_test.shape}")
logging.info(f"Added columns: project_commits, project_contributors, project_age")

# Create output directory if it doesn't exist
output_dir = '../result/20250730_did_result/'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

def main():
    """Main function to run parallel processing"""
    start_time = time.time()
    
    logging.info(f"Starting parallel processing for {len(limits)} limits: {limits}")
    
    # Use ProcessPoolExecutor for parallel processing
    with ProcessPoolExecutor(max_workers=4) as executor:
        # Submit all limit processing tasks
        future_to_limit = {
            executor.submit(process_single_limit, limit, p_test, global_min_time, output_dir): limit 
            for limit in limits
        }
        
        # Collect results as they complete
        results = []
        for future in as_completed(future_to_limit):
            limit = future_to_limit[future]
            try:
                result = future.result()
                results.append(result)
                
                if result['success']:
                    logging.info(f"✅ Successfully processed limit {result['limit']}")
                    logging.info(f"   📁 Output: {result['output_file']}")
                    logging.info(f"   📊 Shape: {result['shape']}")
                    logging.info(f"   🔄 Attrition events: {result['attrition_events']}")
                else:
                    logging.error(f"❌ Failed to process limit {result['limit']}: {result['message']}")
                    
            except Exception as exc:
                logging.error(f"❌ Limit {limit} generated an exception: {exc}")
                results.append({
                    'limit': limit,
                    'success': False,
                    'message': f'Exception: {str(exc)}'
                })
    
    # Summary
    end_time = time.time()
    total_time = end_time - start_time
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    logging.info("=" * 60)
    logging.info("🎉 PROCESSING COMPLETED!")
    logging.info(f"⏱️  Total time: {total_time:.2f} seconds")
    logging.info(f"✅ Successful: {len(successful)}/{len(limits)}")
    logging.info(f"❌ Failed: {len(failed)}/{len(limits)}")
    
    if successful:
        logging.info("\n📈 Successfully processed limits:")
        for result in successful:
            logging.info(f"   - Limit {result['limit']}: {result['attrition_events']} attrition events")
    
    if failed:
        logging.warning("\n⚠️  Failed limits:")
        for result in failed:
            logging.warning(f"   - Limit {result['limit']}: {result['message']}")
    
    logging.info("=" * 60)

if __name__ == "__main__":
    main()