import datetime
from math import log
import pandas as pd
from pymongo import MongoClient
import numpy as np
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import warnings
# 连接到 MongoDB 实例
client = MongoClient('mongodb://localhost:27017/')  # 替换为你的 MongoDB 实例的 URI
# add logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/identify_core_developers.log", mode="w", encoding="utf-8"),
    ],
)

# 选择目标数据库和集合
db = client['disengagement']  # 选择 disengagement 数据库
cache_collection = db["progress_cache"]
finished_projects = cache_collection.find({
    "commits_finished": 1,
    "pr_finished": 1,
    "pr_review_finished": 1
}, {"repo_name": 1})
repos = [project["repo_name"] for project in finished_projects]
project_names = repos


def get_commit_file_repo_name(repo_name):
    file_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    repo_commit = pd.read_csv(file_path)
    # Exclude merge commits with those who have two values in the columns named 'parent_shas'
    repo_commit = repo_commit[repo_commit['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)
    # Exclude bot accounts
    bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')
    bot_developers = bot_developers['bot_name'].tolist()
    repo_commit = repo_commit[~repo_commit['author_login'].isin(bot_developers)].reset_index(drop=True)
    repo_commit = repo_commit[~repo_commit['author_name'].isin(bot_developers)].reset_index(drop=True)
    return repo_commit

def merge_alias_commit_from_tuple(commits_df):
    """
    高性能加速版本：合并来自同一作者但使用不同身份信息的提交记录
    优先基于 login 分组，已归类记录不再重复处理
    """
    warnings.filterwarnings("ignore")
    
    # 初始化 alias 列
    commits_df['alias'] = -1

    # 第一轮：基于非空 author_login 进行分组
    alias_counter = 0
    mask = commits_df['author_login'] != ''
    for _, group in commits_df[mask].groupby('author_login'):
        commits_df.loc[group.index, 'alias'] = alias_counter
        alias_counter += 1

    # 第二轮：对 alias 仍为 -1 的记录，利用 author_name 和 author_email 进行匹配
    # 建立从已分组记录到 alias 的映射（取首次出现的值）
    processed = commits_df[commits_df['alias'] != -1]
    name_to_alias = processed.groupby('author_name')['alias'].first().to_dict()
    email_to_alias = processed.groupby('author_email')['alias'].first().to_dict()

    # 对未分组记录（alias == -1），尝试通过映射来分配 alias
    unprocessed_mask = commits_df['alias'] == -1
    temp_alias = commits_df.loc[unprocessed_mask].apply(
        lambda row: name_to_alias.get(row['author_name'],
                                      email_to_alias.get(row['author_email'], np.nan)),
        axis=1
    )
    assigned = temp_alias.notna()
    commits_df.loc[unprocessed_mask & assigned, 'alias'] = temp_alias[assigned]

    # 对仍未匹配到的记录，分配新 alias（连续递增即可）
    remaining = commits_df['alias'] == -1
    num_remaining = remaining.sum()
    commits_df.loc[remaining, 'alias'] = np.arange(alias_counter, alias_counter + num_remaining)

    # 填充缺失的 author_login：对每个 alias，用首个非空 login 填充组中的 NaN
    commits_df = commits_df.groupby('alias', group_keys=False).apply(
        lambda group: group.assign(author_login=group['author_login'].fillna(
            group['author_login'].dropna().iloc[0] if not group['author_login'].dropna().empty else np.nan))
    )

    return commits_df



def identify_core_developer_commit(commits):
    """
    Identify the core developers based on commit counts from individual commits,
    then filter out developers with fewer than 10 commits.

    Args:
        commits (pd.DataFrame): A DataFrame where each row represents a commit,
                                with at least one column: 'author_login'.

    Returns:
        list: A list of core developers who collectively made over 80% of the total commits
              and have at least 10 commits.
    """
    # fill commits with empty author_login with author_name
    commits['author_login'] = commits['author_login'].fillna(commits['author_name'])
    # Count the number of commits per author
    commit_counts = commits["author_login"].value_counts().reset_index()
    commit_counts.columns = ["developer", "commits"]
    # Sort by commits in descending order
    commit_counts = commit_counts.sort_values(by="commits", ascending=False).reset_index(drop=True)

    # Calculate total commits and 80% threshold
    total_commits = commit_counts["commits"].sum()
    threshold = total_commits * 0.8

    # Identify core developers
    core_developers = []
    cumulative_commits = 0

    for _, row in commit_counts.iterrows():
        core_developers.append(row["developer"])
        cumulative_commits += row["commits"]

        if cumulative_commits >= threshold:
            break

    # Filter out developers with fewer than 10 commits from the core developers
    core_developers_df = commit_counts[commit_counts["developer"].isin(core_developers)]
    core_developers_filtered = core_developers_df[core_developers_df["commits"] >= 10]
    logging.info(f"Finished identifying core developers of {commits['repo_name'].iloc[0]} at time {datetime.datetime.now()}")
    # print(f"Finished identifying core developers of {commits['repo_name'].iloc[0]} at time {datetime.datetime.now()}")
    return core_developers_filtered["developer"].tolist()



def process_repo(repo_name):
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    # if there is processed_commits, directly identify_core_developer_commit
    if os.path.exists(output_path):
        try:
            repo_commit = pd.read_csv(output_path)
            core_developers = identify_core_developer_commit(repo_commit)
            return {
                'repo_name': repo_name,
                'core_developers': core_developers,
                'num_core_developers': len(core_developers),
                'num_total_developers': len(repo_commit['author_login'].unique())
            }
        except Exception as e:
            # print(f"Error identifying core developers for {repo_name}: {e}")
            logging.error(f"Error identifying core developers for {repo_name}: {e}")
            return None
    file_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    if not os.path.exists(file_path):
        return None
    repo_commit = get_commit_file_repo_name(repo_name)
    repo_commit = merge_alias_commit_from_tuple(repo_commit)
    # save new commit to new place
    if not os.path.exists(output_path):
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
    repo_commit.to_csv(output_path, index=False)    
    try:
        core_developers = identify_core_developer_commit(repo_commit)
        return {
            'repo_name': repo_name,
            'core_developers': core_developers,
            'num_core_developers': len(core_developers),
            'num_total_developers': len(repo_commit['author_login'].unique())
        }
    except Exception as e:
        # print(f"Error processing repository {repo_name}: {e}")
        logging.error(f"Error processing repository {repo_name}: {e}")
        return None

def get_all_core_developers(project_names):
    # Create temp file directory if it doesn't exist
    os.makedirs('../data', exist_ok=True)
    
    # # Load already processed repos if available
    # if os.path.exists('../data/temp_core_developer_list.csv'):
    #     temp_df = pd.read_csv('../data/temp_core_developer_list.csv')
    #     temp_repo_name = temp_df['repo_name'].tolist()
    #     core_developers_list = temp_df.to_dict('records')
    #     logging.info(f"Already processed repos: {len(temp_repo_name)}")
    # else:
    #     temp_repo_name = []
    #     core_developers_list = []
    #     logging.info("No previously processed repos found")
    
    # # Filter out already processed repos
    # remaining_projects = list(set(project_names) - set(temp_repo_name))
    # logging.info(f"Total repos to process: {len(remaining_projects)}")
    
    # if not remaining_projects:
    #     logging.info("All repos already processed!")
    #     return core_developers_list
    core_developers_list = []
    # Use tqdm with better settings for thread-safe progress tracking
    with ThreadPoolExecutor(max_workers=60) as executor:
        futures = {executor.submit(process_repo, repo): repo for repo in project_names}
        
        # Configure tqdm to work better with threads
        with tqdm(total=len(futures), desc="Processing repos", position=0, leave=True, 
                 dynamic_ncols=True, unit="repo") as progress_bar:
            completed = 0
            for future in as_completed(futures):
                repo = futures[future]
                try:
                    result = future.result()
                    if result is not None:
                        core_developers_list.append(result)
                        # Update progress bar description with current repo name
                        progress_bar.set_postfix_str(f"Current repo: {repo}")
                        
                        # Save after every 10 completed repos or at the last one
                        completed += 1
                        if completed % 10 == 0 or completed == len(futures):
                            temp_df = pd.DataFrame(core_developers_list)
                            temp_df.to_csv('../data/temp_core_developer_list.csv', index=False)
                            logging.info(f"Progress saved: {len(core_developers_list)} repos processed")
                except Exception as e:
                    logging.error(f"Error processing {repo}: {e}")
                
                progress_bar.update(1)
    
    logging.info(f"All repositories processed. Total: {len(core_developers_list)} repos")
    return core_developers_list

core_developers_list = get_all_core_developers(project_names)
# make it into a DataFrame
core_developers_df = pd.DataFrame(core_developers_list)
core_developers_df

# save the result to a CSV file
core_developers_df.to_csv('../data/core_developer_list_total_repo.csv', index=False)