import pandas as pd
import numpy as np
import os
from scipy.optimize import curve_fit
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing

# 拟合模型定义

def logistic_model(t, K, r, t0):
    return K / (1 + np.exp(-r * (t - t0)))

def gompertz_model(t, a, b, c):
    return a * np.exp(-b * np.exp(-c * t))

MODELS = {
    'linear': (lambda t, a, b: a*t + b, 'a*t + b'),
    'exponential': (lambda t, a, b: a*np.exp(b*t), 'a*exp(b*t)'),
    'logistic': (logistic_model, 'K / (1 + exp(-r*(t-t0)))'),
    'gompertz': (gompertz_model, 'a * exp(-b * exp(-c * t))')
}

def read_commit_file(repo_name, processed=True):
    if processed:
        path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    else:
        path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    if not os.path.exists(path):
        return None
    df = pd.read_csv(path)
    if df.empty:
        return None
    return df

def prepare_cumulative_commits(df):
    df['date'] = pd.to_datetime(df['date']).dt.to_period('M').dt.to_timestamp()
    commits = df.groupby('date').size().resample('ME').sum().reset_index(name='counts')
    commits['cumulative'] = commits['counts'].cumsum()
    start_date = commits['date'].min()
    # 填补缺失月份
    full_dates = pd.date_range(start=start_date, end=commits['date'].max(), freq='ME')
    commits = commits.set_index('date').reindex(full_dates).fillna({'counts':0, 'cumulative':0}).reset_index()
    commits['cumulative'] = commits['counts'].cumsum()
    commits.rename(columns={'index':'date'}, inplace=True)
    commits['months_since_start'] = (commits['date'] - start_date).apply(lambda x: x.days//30 + x.seconds//86400)
    return commits

def fit_best_model(commits):
    x_data = commits['months_since_start'].values
    y_data = commits['cumulative'].values
    best_model = None
    best_score = float('inf')
    best_params = None
    for name, (func, formula) in MODELS.items():
        try:
            params, _ = curve_fit(func, x_data, y_data, maxfev=5000)
            pred = func(x_data, *params)
            mse = np.mean((y_data - pred)**2)
            if mse < best_score:
                best_model = (name, formula, params)
                best_score = mse
        except Exception as e:
            pass
    return best_model

def model_formula_with_params(formula, params, model_name):
    # 生成带参数的公式字符串
    if model_name == 'linear':
        return f"{params[0]:.4f}*t + {params[1]:.4f}"
    elif model_name == 'exponential':
        return f"{params[0]:.4f}*exp({params[1]:.4f}*t)"
    elif model_name == 'logistic':
        return f"{params[0]:.4f} / (1 + exp(-{params[1]:.4f}*(t-{params[2]:.4f})))"
    elif model_name == 'gompertz':
        return f"{params[0]:.4f} * exp(-{params[1]:.4f} * exp(-{params[2]:.4f} * t))"
    else:
        return formula

def analyze_repo(repo_name, processed=True):
    df = read_commit_file(repo_name, processed=processed)
    if df is None:
        return repo_name, None, None
    commits = prepare_cumulative_commits(df)
    best_model = fit_best_model(commits)
    if best_model is None:
        return repo_name, None, None
    name, formula, params = best_model
    formula_str = model_formula_with_params(formula, params, name)
    return repo_name, name, formula_str

def get_all_repo_names_from_newcomer(attrition_file):
    df = pd.read_csv(attrition_file)
    return df['repo_name'].unique().tolist()

# 替换main中的repo_names获取方式

def main():
    processed = True  # 如需用原始commit文件，设为False
    # 指定attrition文件路径
    newcomers = pd.read_csv('../data/newcomer_contributors_core_developer.csv')
    repo_names = newcomers['repo_name'].unique().tolist()
    results = []
    import multiprocessing
    from concurrent.futures import ThreadPoolExecutor, as_completed
    optimal_workers = min(80, multiprocessing.cpu_count() * 4, len(repo_names))
    with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
        future_to_repo = {executor.submit(analyze_repo, repo, processed): repo for repo in repo_names}
        for future in as_completed(future_to_repo):
            repo = future_to_repo[future]
            try:
                repo_name, model_name, formula_str = future.result()
                results.append((repo_name, model_name, formula_str))
            except Exception as exc:
                results.append((repo, None, None))
    # 生成DataFrame
    df_result = pd.DataFrame(results, columns=['repo_name', 'best_model_name', 'best_model_formula'])
    df_result.to_csv('repo_growth_model_summary.csv', index=False)
    print(df_result)

if __name__ == "__main__":
    main() 