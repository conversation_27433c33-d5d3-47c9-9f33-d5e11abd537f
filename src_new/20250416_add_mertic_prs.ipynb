{"cells": [{"cell_type": "code", "execution_count": null, "id": "af1f7ab1", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "4fc7c828-e2c3-41f5-bbc3-ecd79b7de7d9", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895", "5.141017148795798", null], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "18.3589", "2.9631522620523985", "0.6931471805599453"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "25.7632", "3.287027809575607", "0.6931471805599453"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "14.9636", "2.770311130495723", "0.6931471805599453"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "361.1883", "5.8921642423323215", "0.6931471805599453"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "351.6383", "5.865442885732476", "0.6931471805599453"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.9486", "0.6671111660091038", "0.6931471805599453"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "1.7981", "1.0289406154187903", "0.6931471805599453"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "21.5526", "3.1158503586370414", "0.6931471805599453"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.005", "0.0049875415110389", "0.6931471805599453"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.6445", "0.4974363866469745", "0.6931471805599453"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "49.2922", "3.9178499954986576", "0.6931471805599453"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "91.0883", "4.522747899360907", null], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null]], "shape": {"columns": 46, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "      <td>1.421275</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "      <td>3.026300</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "      <td>3.619184</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 46 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        ...  decelerating       15.0       2.772589    6.751101   \n", "1        ...  decelerating       15.0       2.772589    6.751101   \n", "2        ...  decelerating       15.0       2.772589    6.751101   \n", "3        ...  decelerating       15.0       2.772589    6.751101   \n", "4        ...  decelerating       15.0       2.772589    6.751101   \n", "...      ...           ...        ...            ...         ...   \n", "3503016  ...        steady        1.0       0.693147    6.710523   \n", "3503017  ...        steady        1.0       0.693147    6.710523   \n", "3503018  ...        steady        1.0       0.693147    6.710523   \n", "3503019  ...        steady        1.0       0.693147    6.710523   \n", "3503020  ...        steady        1.0       0.693147    6.710523   \n", "\n", "        log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0                 0.144831     5.433722                        1.0   \n", "1                 0.144831     5.433722                        NaN   \n", "2                 0.144831     5.433722                        NaN   \n", "3                 0.144831     5.433722                        NaN   \n", "4                 0.144831     5.433722                        NaN   \n", "...                    ...          ...                        ...   \n", "3503016           0.018605     2.197225                        1.0   \n", "3503017           0.018605     2.197225                        NaN   \n", "3503018           0.018605     2.197225                        1.0   \n", "3503019           0.018605     2.197225                        1.0   \n", "3503020           0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge  log_time_to_merge  log_pull_request_success_rate  \n", "0                  NaN                NaN                       0.693147  \n", "1             169.8895           5.141017                            NaN  \n", "2                  NaN                NaN                            NaN  \n", "3                  NaN                NaN                            NaN  \n", "4                  NaN                NaN                            NaN  \n", "...                ...                ...                            ...  \n", "3503016         3.1424           1.421275                       0.693147  \n", "3503017            NaN                NaN                            NaN  \n", "3503018        19.6208           3.026300                       0.693147  \n", "3503019        36.3071           3.619184                       0.693147  \n", "3503020            NaN                NaN                            NaN  \n", "\n", "[3503021 rows x 46 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "compiled_data_test = pd.read_csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity.csv\")\n", "compiled_data_test"]}, {"cell_type": "code", "execution_count": null, "id": "a96d15b6", "metadata": {}, "outputs": [], "source": ["compiled_data_test['mainLanguage'].value_counts()"]}, {"cell_type": "code", "execution_count": 2, "id": "9e58f9e4", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "b4e43dca-1bbd-41d7-8d9b-cc9b7197cb6f", "rows": [["0", "01mf02/jaq", "646"], ["1", "01mf02/jaq", "647"], ["2", "01mf02/jaq", "648"], ["3", "01mf02/jaq", "649"], ["4", "01mf02/jaq", "650"], ["5", "01mf02/jaq", "651"], ["6", "01mf02/jaq", "652"], ["7", "01mf02/jaq", "653"], ["8", "01mf02/jaq", "654"], ["9", "01mf02/jaq", "655"], ["10", "01mf02/jaq", "656"], ["11", "01mf02/jaq", "657"], ["12", "01mf02/jaq", "658"], ["13", "01mf02/jaq", "659"], ["14", "01mf02/jaq", "660"], ["15", "01mf02/jaq", "661"], ["16", "01mf02/jaq", "662"], ["17", "01mf02/jaq", "663"], ["18", "01mf02/jaq", "664"], ["19", "01mf02/jaq", "665"], ["20", "01mf02/jaq", "666"], ["21", "01mf02/jaq", "667"], ["22", "01mf02/jaq", "668"], ["23", "01mf02/jaq", "669"], ["24", "01mf02/jaq", "670"], ["25", "Project-Babble/ProjectBabble", "647"], ["26", "Project-Babble/ProjectBabble", "648"], ["27", "Project-Babble/ProjectBabble", "649"], ["28", "Project-Babble/ProjectBabble", "650"], ["29", "Project-Babble/ProjectBabble", "651"], ["30", "Project-Babble/ProjectBabble", "652"], ["31", "Project-Babble/ProjectBabble", "653"], ["32", "Project-Babble/ProjectBabble", "654"], ["33", "Project-Babble/ProjectBabble", "655"], ["34", "Project-Babble/ProjectBabble", "656"], ["35", "Project-Babble/ProjectBabble", "657"], ["36", "Project-Babble/ProjectBabble", "658"], ["37", "Project-Babble/ProjectBabble", "659"], ["38", "Project-Babble/ProjectBabble", "660"], ["39", "Project-Babble/ProjectBabble", "661"], ["40", "Project-Babble/ProjectBabble", "662"], ["41", "Project-Babble/ProjectBabble", "663"], ["42", "Project-Babble/ProjectBabble", "664"], ["43", "Project-Babble/ProjectBabble", "665"], ["44", "Project-Babble/ProjectBabble", "666"], ["45", "Project-Babble/ProjectBabble", "667"], ["46", "Project-Babble/ProjectBabble", "668"], ["47", "Project-Babble/ProjectBabble", "669"], ["48", "Project-Babble/ProjectBabble", "670"], ["49", "Project-Babble/ProjectBabble", "671"]], "shape": {"columns": 2, "rows": 2742861}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742856</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>482</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742857</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>483</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742858</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742859</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>485</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742860</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>486</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2742861 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                             repo_name  standardized_time_weeks\n", "0                           01mf02/jaq                      646\n", "1                           01mf02/jaq                      647\n", "2                           01mf02/jaq                      648\n", "3                           01mf02/jaq                      649\n", "4                           01mf02/jaq                      650\n", "...                                ...                      ...\n", "2742856  zzzprojects/html-agility-pack                      482\n", "2742857  zzzprojects/html-agility-pack                      483\n", "2742858  zzzprojects/html-agility-pack                      484\n", "2742859  zzzprojects/html-agility-pack                      485\n", "2742860  zzzprojects/html-agility-pack                      486\n", "\n", "[2742861 rows x 2 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extract unique combinations of repo_name and standardized_time_weeks\n", "unique_repo_time = compiled_data_test[['repo_name', 'standardized_time_weeks']].drop_duplicates()\n", "\n", "# Reset index for cleaner output\n", "unique_repo_time.reset_index(drop=True, inplace=True)\n", "\n", "# Display the result\n", "unique_repo_time"]}, {"cell_type": "code", "execution_count": 3, "id": "0ce645af", "metadata": {}, "outputs": [], "source": ["global_min_time = pd.Timestamp('2010-08-30 00:00:00')"]}, {"cell_type": "code", "execution_count": 6, "id": "a94d77eb", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "00cd0bd4efc34c36804e03fa763ae744", "version_major": 2, "version_minor": 0}, "text/plain": ["Processing repositories:   0%|          | 0/36886 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:16: DtypeWarning: Columns (8) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  pr_data = pd.read_csv(file_path)\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:16: DtypeWarning: Columns (8) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  pr_data = pd.read_csv(file_path)\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:16: DtypeWarning: Columns (8) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  pr_data = pd.read_csv(file_path)\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/1844709254.py:25: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_count", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "bcd9c36c-aaec-4211-97cb-b3e37250eabb", "rows": [["0", "01mf02/jaq", "646", "1"], ["1", "01mf02/jaq", "647", "0"], ["2", "01mf02/jaq", "648", "0"], ["3", "01mf02/jaq", "649", "0"], ["4", "01mf02/jaq", "650", "0"]], "shape": {"columns": 3, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    repo_name  standardized_time_weeks  pr_count\n", "0  01mf02/jaq                      646         1\n", "1  01mf02/jaq                      647         0\n", "2  01mf02/jaq                      648         0\n", "3  01mf02/jaq                      649         0\n", "4  01mf02/jaq                      650         0"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from pathlib import Path\n", "from tqdm.notebook import tqdm\n", "\n", "# Create a function to process each repository\n", "def process_repo_pr_metrics(repo_name):\n", "    # Create file path for pull requests data\n", "    file_path = f\"../data/pullrequests/{repo_name.replace('/','_')}_pull_requests.csv\"\n", "    \n", "    # Check if file exists\n", "    if not Path(file_path).exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    try:\n", "        # Read pull requests data\n", "        pr_data = pd.read_csv(file_path)\n", "        \n", "        # Check if dataframe is empty\n", "        if pr_data.empty:\n", "            print(f\"Empty dataframe for {repo_name}\")\n", "            return None\n", "        \n", "        # Convert date columns to datetime\n", "        if 'created_at' in pr_data.columns:\n", "            pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce')\n", "            # If dates have timezone info, remove it to make them timezone-naive\n", "            if pr_data['created_at'].dt.tz is not None:\n", "                pr_data['created_at'] = pr_data['created_at'].dt.tz_localize(None)\n", "            \n", "            # Calculate standardized_time_weeks based on global_min_time\n", "            pr_data['standardized_time_weeks'] = ((pr_data['created_at'] - global_min_time).dt.days // 7)\n", "            \n", "            # Group by standardized_time_weeks and count pull requests\n", "            pr_counts = pr_data.groupby('standardized_time_weeks').size().reset_index(name='pr_count')\n", "            \n", "            # Add repo_name column\n", "            pr_counts['repo_name'] = repo_name\n", "            \n", "            return pr_counts\n", "        else:\n", "            print(f\"No created_at column in {repo_name} pull requests data\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"Error processing {repo_name}: {e}\")\n", "        return None\n", "\n", "# Get list of unique repository names\n", "unique_repos = unique_repo_time['repo_name'].unique()\n", "\n", "# Process all repositories with progress bar\n", "all_pr_metrics = []\n", "for repo in tqdm(unique_repos, desc=\"Processing repositories\"):\n", "    repo_metrics = process_repo_pr_metrics(repo)\n", "    if repo_metrics is not None:\n", "        all_pr_metrics.append(repo_metrics)\n", "\n", "# Combine all metrics\n", "if all_pr_metrics:\n", "    pr_metrics_df = pd.concat(all_pr_metrics, ignore_index=True)\n", "    \n", "    # Merge with unique_repo_time to ensure all repo-week combinations are included\n", "    all_metrics = pd.merge(\n", "        unique_repo_time,\n", "        pr_metrics_df,\n", "        on=['repo_name', 'standardized_time_weeks'],\n", "        how='left'\n", "    )\n", "    \n", "    # Fill NaN values with 0 for pr_count\n", "    all_metrics['pr_count'] = all_metrics['pr_count'].fillna(0).astype(int)\n", "else:\n", "    print(\"No valid PR metrics found\")\n", "    all_metrics = unique_repo_time.copy()\n", "    all_metrics['pr_count'] = 0\n", "\n", "# Display the result\n", "all_metrics.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "15ee7af7", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_count", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "7b2a89af-6a4c-48c4-be6f-5b5513fc2015", "rows": [["0", "01mf02/jaq", "646", "1"], ["1", "01mf02/jaq", "647", "0"], ["2", "01mf02/jaq", "648", "0"], ["3", "01mf02/jaq", "649", "0"], ["4", "01mf02/jaq", "650", "0"], ["5", "01mf02/jaq", "651", "1"], ["6", "01mf02/jaq", "652", "0"], ["7", "01mf02/jaq", "653", "0"], ["8", "01mf02/jaq", "654", "1"], ["9", "01mf02/jaq", "655", "1"], ["10", "01mf02/jaq", "656", "0"], ["11", "01mf02/jaq", "657", "1"], ["12", "01mf02/jaq", "658", "0"], ["13", "01mf02/jaq", "659", "1"], ["14", "01mf02/jaq", "660", "0"], ["15", "01mf02/jaq", "661", "0"], ["16", "01mf02/jaq", "662", "1"], ["17", "01mf02/jaq", "663", "0"], ["18", "01mf02/jaq", "664", "0"], ["19", "01mf02/jaq", "665", "1"], ["20", "01mf02/jaq", "666", "0"], ["21", "01mf02/jaq", "667", "1"], ["22", "01mf02/jaq", "668", "5"], ["23", "01mf02/jaq", "669", "1"], ["24", "01mf02/jaq", "670", "4"], ["25", "Project-Babble/ProjectBabble", "647", "0"], ["26", "Project-Babble/ProjectBabble", "648", "0"], ["27", "Project-Babble/ProjectBabble", "649", "0"], ["28", "Project-Babble/ProjectBabble", "650", "0"], ["29", "Project-Babble/ProjectBabble", "651", "0"], ["30", "Project-Babble/ProjectBabble", "652", "0"], ["31", "Project-Babble/ProjectBabble", "653", "0"], ["32", "Project-Babble/ProjectBabble", "654", "0"], ["33", "Project-Babble/ProjectBabble", "655", "2"], ["34", "Project-Babble/ProjectBabble", "656", "3"], ["35", "Project-Babble/ProjectBabble", "657", "0"], ["36", "Project-Babble/ProjectBabble", "658", "0"], ["37", "Project-Babble/ProjectBabble", "659", "0"], ["38", "Project-Babble/ProjectBabble", "660", "0"], ["39", "Project-Babble/ProjectBabble", "661", "1"], ["40", "Project-Babble/ProjectBabble", "662", "1"], ["41", "Project-Babble/ProjectBabble", "663", "1"], ["42", "Project-Babble/ProjectBabble", "664", "0"], ["43", "Project-Babble/ProjectBabble", "665", "4"], ["44", "Project-Babble/ProjectBabble", "666", "0"], ["45", "Project-Babble/ProjectBabble", "667", "1"], ["46", "Project-Babble/ProjectBabble", "668", "1"], ["47", "Project-Babble/ProjectBabble", "669", "0"], ["48", "Project-Babble/ProjectBabble", "670", "0"], ["49", "Project-Babble/ProjectBabble", "671", "0"]], "shape": {"columns": 3, "rows": 2742861}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742856</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>482</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742857</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>483</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742858</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>484</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742859</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>485</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742860</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>486</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2742861 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                             repo_name  standardized_time_weeks  pr_count\n", "0                           01mf02/jaq                      646         1\n", "1                           01mf02/jaq                      647         0\n", "2                           01mf02/jaq                      648         0\n", "3                           01mf02/jaq                      649         0\n", "4                           01mf02/jaq                      650         0\n", "...                                ...                      ...       ...\n", "2742856  zzzprojects/html-agility-pack                      482         0\n", "2742857  zzzprojects/html-agility-pack                      483         0\n", "2742858  zzzprojects/html-agility-pack                      484         0\n", "2742859  zzzprojects/html-agility-pack                      485         0\n", "2742860  zzzprojects/html-agility-pack                      486         0\n", "\n", "[2742861 rows x 3 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["all_metrics"]}, {"cell_type": "code", "execution_count": 8, "id": "6f852030", "metadata": {}, "outputs": [], "source": ["all_metrics.to_csv(\"../result/did_result_20250408/add_metrics_pr_count.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "523957da", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e5a3bd92da02431bbc3e981a4c564de1", "version_major": 2, "version_minor": 0}, "text/plain": ["Processing PR reviews:   0%|          | 0/36886 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "/tmp/ipykernel_3163164/760249584.py:27: User<PERSON>arning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_count", "rawType": "int64", "type": "integer"}, {"name": "pr_review_count", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "258e2b32-2232-468f-b99c-dcf5897f893e", "rows": [["0", "01mf02/jaq", "646", "1", "0"], ["1", "01mf02/jaq", "647", "0", "0"], ["2", "01mf02/jaq", "648", "0", "0"], ["3", "01mf02/jaq", "649", "0", "0"], ["4", "01mf02/jaq", "650", "0", "0"]], "shape": {"columns": 4, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_count</th>\n", "      <th>pr_review_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    repo_name  standardized_time_weeks  pr_count  pr_review_count\n", "0  01mf02/jaq                      646         1                0\n", "1  01mf02/jaq                      647         0                0\n", "2  01mf02/jaq                      648         0                0\n", "3  01mf02/jaq                      649         0                0\n", "4  01mf02/jaq                      650         0                0"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from pymongo import MongoClient\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "\n", "# MongoDB connection settings\n", "WINDOWS_IP = \"localhost\"\n", "PORT = 27017\n", "client = MongoClient(f\"mongodb://{WINDOWS_IP}:{PORT}/\")\n", "\n", "db = client[\"disengagement\"]\n", "pr_comments_collection = db[\"pr_comments\"]\n", "\n", "#\n", "def process_repo_pr_reviews(repo_name):\n", "    try:\n", "        # Query MongoDB for PR comments for this repository\n", "        pr_comments = list(pr_comments_collection.find({\"repo_name\": repo_name}))\n", "        \n", "        # Check if there are any comments\n", "        if not pr_comments:\n", "            return None\n", "        \n", "        # Create DataFrame from comments\n", "        comments_df = pd.DataFrame(pr_comments)\n", "        \n", "        # Convert created_at to datetime\n", "        comments_df['created_at'] = pd.to_datetime(comments_df['created_at'], errors='coerce')\n", "        # If dates have timezone info, remove it to make them timezone-naive\n", "        if comments_df['created_at'].dt.tz is not None:\n", "            comments_df['created_at'] = comments_df['created_at'].dt.tz_localize(None)\n", "        # Calculate standardized_time_weeks based on global_min_time\n", "        comments_df['standardized_time_weeks'] = ((comments_df['created_at'] - global_min_time).dt.days // 7)\n", "        \n", "        \n", "        # Group by week\n", "        review_counts = comments_df.groupby('standardized_time_weeks').size().reset_index(name='pr_review_count')\n", "        \n", "        # Add repo_name column\n", "        review_counts['repo_name'] = repo_name\n", "        \n", "        return review_counts\n", "    except Exception as e:\n", "        print(f\"Error processing PR reviews for {repo_name}: {e}\")\n", "        return None\n", "\n", "# Process all repositories with progress bar\n", "all_review_metrics = []\n", "for repo in tqdm(unique_repos, desc=\"Processing PR reviews\"):\n", "    repo_review_metrics = process_repo_pr_reviews(repo)\n", "    if repo_review_metrics is not None:\n", "        all_review_metrics.append(repo_review_metrics)\n", "\n", "# Combine all metrics\n", "if all_review_metrics:\n", "    review_metrics_df = pd.concat(all_review_metrics, ignore_index=True)\n", "    \n", "    # Merge with all_metrics to add the PR review count metric\n", "    all_metrics = pd.merge(\n", "        all_metrics,\n", "        review_metrics_df,\n", "        on=['repo_name', 'standardized_time_weeks'],\n", "        how='left'\n", "    )\n", "    \n", "    # Fill NaN values with 0 for pr_review_count\n", "    all_metrics['pr_review_count'] = all_metrics['pr_review_count'].fillna(0).astype(int)\n", "else:\n", "    print(\"No valid PR review metrics found\")\n", "    all_metrics['pr_review_count'] = 0\n", "\n", "# Display the first few rows with the new metric\n", "all_metrics.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "48104672", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_count", "rawType": "int64", "type": "integer"}, {"name": "pr_review_count", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "2bae5801-b95d-4621-896f-96009c454255", "rows": [["0", "01mf02/jaq", "646", "1", "0"], ["1", "01mf02/jaq", "647", "0", "0"], ["2", "01mf02/jaq", "648", "0", "0"], ["3", "01mf02/jaq", "649", "0", "0"], ["4", "01mf02/jaq", "650", "0", "0"], ["5", "01mf02/jaq", "651", "1", "0"], ["6", "01mf02/jaq", "652", "0", "0"], ["7", "01mf02/jaq", "653", "0", "0"], ["8", "01mf02/jaq", "654", "1", "0"], ["9", "01mf02/jaq", "655", "1", "5"], ["10", "01mf02/jaq", "656", "0", "0"], ["11", "01mf02/jaq", "657", "1", "0"], ["12", "01mf02/jaq", "658", "0", "0"], ["13", "01mf02/jaq", "659", "1", "0"], ["14", "01mf02/jaq", "660", "0", "0"], ["15", "01mf02/jaq", "661", "0", "0"], ["16", "01mf02/jaq", "662", "1", "0"], ["17", "01mf02/jaq", "663", "0", "0"], ["18", "01mf02/jaq", "664", "0", "0"], ["19", "01mf02/jaq", "665", "1", "0"], ["20", "01mf02/jaq", "666", "0", "0"], ["21", "01mf02/jaq", "667", "1", "0"], ["22", "01mf02/jaq", "668", "5", "0"], ["23", "01mf02/jaq", "669", "1", "0"], ["24", "01mf02/jaq", "670", "4", "0"], ["25", "Project-Babble/ProjectBabble", "647", "0", "0"], ["26", "Project-Babble/ProjectBabble", "648", "0", "0"], ["27", "Project-Babble/ProjectBabble", "649", "0", "0"], ["28", "Project-Babble/ProjectBabble", "650", "0", "0"], ["29", "Project-Babble/ProjectBabble", "651", "0", "0"], ["30", "Project-Babble/ProjectBabble", "652", "0", "0"], ["31", "Project-Babble/ProjectBabble", "653", "0", "0"], ["32", "Project-Babble/ProjectBabble", "654", "0", "0"], ["33", "Project-Babble/ProjectBabble", "655", "2", "0"], ["34", "Project-Babble/ProjectBabble", "656", "3", "0"], ["35", "Project-Babble/ProjectBabble", "657", "0", "0"], ["36", "Project-Babble/ProjectBabble", "658", "0", "0"], ["37", "Project-Babble/ProjectBabble", "659", "0", "0"], ["38", "Project-Babble/ProjectBabble", "660", "0", "0"], ["39", "Project-Babble/ProjectBabble", "661", "1", "0"], ["40", "Project-Babble/ProjectBabble", "662", "1", "0"], ["41", "Project-Babble/ProjectBabble", "663", "1", "0"], ["42", "Project-Babble/ProjectBabble", "664", "0", "0"], ["43", "Project-Babble/ProjectBabble", "665", "4", "0"], ["44", "Project-Babble/ProjectBabble", "666", "0", "0"], ["45", "Project-Babble/ProjectBabble", "667", "1", "0"], ["46", "Project-Babble/ProjectBabble", "668", "1", "0"], ["47", "Project-Babble/ProjectBabble", "669", "0", "0"], ["48", "Project-Babble/ProjectBabble", "670", "0", "0"], ["49", "Project-Babble/ProjectBabble", "671", "0", "0"]], "shape": {"columns": 4, "rows": 2742861}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_count</th>\n", "      <th>pr_review_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742856</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>482</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742857</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>483</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742858</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>484</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742859</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>485</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2742860</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2742861 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                             repo_name  standardized_time_weeks  pr_count  \\\n", "0                           01mf02/jaq                      646         1   \n", "1                           01mf02/jaq                      647         0   \n", "2                           01mf02/jaq                      648         0   \n", "3                           01mf02/jaq                      649         0   \n", "4                           01mf02/jaq                      650         0   \n", "...                                ...                      ...       ...   \n", "2742856  zzzprojects/html-agility-pack                      482         0   \n", "2742857  zzzprojects/html-agility-pack                      483         0   \n", "2742858  zzzprojects/html-agility-pack                      484         0   \n", "2742859  zzzprojects/html-agility-pack                      485         0   \n", "2742860  zzzprojects/html-agility-pack                      486         0   \n", "\n", "         pr_review_count  \n", "0                      0  \n", "1                      0  \n", "2                      0  \n", "3                      0  \n", "4                      0  \n", "...                  ...  \n", "2742856                0  \n", "2742857                0  \n", "2742858                0  \n", "2742859                0  \n", "2742860                0  \n", "\n", "[2742861 rows x 4 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["all_metrics"]}, {"cell_type": "code", "execution_count": null, "id": "f35806ac", "metadata": {}, "outputs": [], "source": ["all_metrics.to_csv(\"../result/did_result_20250408/add_metrics_pr_count_and_review_count.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 17, "id": "2072969d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape before merge: (3503021, 46)\n", "Shape after merge: (3503021, 50)\n", "PR count range: 0 to 1069\n", "PR review count range: 0 to 2926\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "string"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "pr_count", "rawType": "int64", "type": "integer"}, {"name": "pr_review_count", "rawType": "int64", "type": "integer"}, {"name": "log_pr_count", "rawType": "float64", "type": "float"}, {"name": "log_pr_review_count", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "5f764d3e-e08e-43e8-a374-b7ca30b39c6e", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895", "5.141017148795798", null, "0", "0", "0.0", "0.0"], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"]], "shape": {"columns": 50, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "      <th>pr_count</th>\n", "      <th>pr_review_count</th>\n", "      <th>log_pr_count</th>\n", "      <th>log_pr_review_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.693147</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 50 columns</p>\n", "</div>"], "text/plain": ["    repo_name  standardized_time_weeks  pr_throughput  rolling_slope  \\\n", "0  01mf02/jaq                      646            0.0       0.000000   \n", "1  01mf02/jaq                      647            1.0       0.038462   \n", "2  01mf02/jaq                      648            0.0       0.031469   \n", "3  01mf02/jaq                      649            0.0       0.024476   \n", "4  01mf02/jaq                      650            0.0       0.017483   \n", "\n", "   rolling_mean  rolling_rate_of_change  feature_sigmod_add  \\\n", "0      0.000000                0.000000            0.500000   \n", "1      0.083333                0.693147            0.684921   \n", "2      0.083333                0.000000            0.520821   \n", "3      0.083333                0.000000            0.520821   \n", "4      0.083333                0.000000            0.520821   \n", "\n", "   feature_sigmod_multiply  someone_left  tenure  ...  log_commit_percent  \\\n", "0                 0.500000             0   854.0  ...            0.144831   \n", "1                 0.514437             0   854.0  ...            0.144831   \n", "2                 0.500000             0   854.0  ...            0.144831   \n", "3                 0.500000             0   854.0  ...            0.144831   \n", "4                 0.500000             0   854.0  ...            0.144831   \n", "\n", "   log_commits  pull_request_success_rate  time_to_merge log_time_to_merge  \\\n", "0     5.433722                        1.0            NaN               NaN   \n", "1     5.433722                        NaN       169.8895          5.141017   \n", "2     5.433722                        NaN            NaN               NaN   \n", "3     5.433722                        NaN            NaN               NaN   \n", "4     5.433722                        NaN            NaN               NaN   \n", "\n", "   log_pull_request_success_rate  pr_count  pr_review_count  log_pr_count  \\\n", "0                       0.693147         1                0      0.693147   \n", "1                            NaN         0                0      0.000000   \n", "2                            NaN         0                0      0.000000   \n", "3                            NaN         0                0      0.000000   \n", "4                            NaN         0                0      0.000000   \n", "\n", "   log_pr_review_count  \n", "0                  0.0  \n", "1                  0.0  \n", "2                  0.0  \n", "3                  0.0  \n", "4                  0.0  \n", "\n", "[5 rows x 50 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Step 1: 聚合metrics，确保key唯一性\n", "metrics_agg = (\n", "    all_metrics\n", "    .groupby(['repo_name', 'standardized_time_weeks'], as_index=False)[['pr_count', 'pr_review_count']]\n", "    .sum()\n", ")\n", "\n", "# Step 2: merge\n", "compiled_data_with_metrics = pd.merge(\n", "    compiled_data_test,\n", "    metrics_agg,\n", "    on=['repo_name', 'standardized_time_weeks'],\n", "    how='left'\n", ")\n", "\n", "# Step 3: 填补NaN\n", "compiled_data_with_metrics['pr_count'] = compiled_data_with_metrics['pr_count'].fillna(0).astype(int)\n", "compiled_data_with_metrics['pr_review_count'] = compiled_data_with_metrics['pr_review_count'].fillna(0).astype(int)\n", "\n", "# Step 4: log1p\n", "compiled_data_with_metrics['log_pr_count'] = np.log1p(compiled_data_with_metrics['pr_count'])\n", "compiled_data_with_metrics['log_pr_review_count'] = np.log1p(compiled_data_with_metrics['pr_review_count'])\n", "\n", "# Step 5: 检查\n", "print(f\"Shape before merge: {compiled_data_test.shape}\")\n", "print(f\"Shape after merge: {compiled_data_with_metrics.shape}\")\n", "print(f\"PR count range: {compiled_data_with_metrics['pr_count'].min()} to {compiled_data_with_metrics['pr_count'].max()}\")\n", "print(f\"PR review count range: {compiled_data_with_metrics['pr_review_count'].min()} to {compiled_data_with_metrics['pr_review_count'].max()}\")\n", "\n", "compiled_data_with_metrics.head()\n"]}, {"cell_type": "code", "execution_count": 18, "id": "e898a7ab", "metadata": {}, "outputs": [], "source": ["compiled_data_with_metrics.to_csv(\"../result/did_result_20250408/compiled_data_with_pr_metrics.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 19, "id": "ebd25860", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "pr_count", "rawType": "int64", "type": "integer"}, {"name": "pr_review_count", "rawType": "int64", "type": "integer"}, {"name": "log_pr_count", "rawType": "float64", "type": "float"}, {"name": "log_pr_review_count", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "07c544c7-e69c-455a-9006-6265f71d76ec", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895", "5.141017148795798", null, "0", "0", "0.0", "0.0"], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "18.3589", "2.9631522620523985", "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "25.7632", "3.287027809575607", "0.6931471805599453", "1", "5", "0.6931471805599453", "1.791759469228055"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "14.9636", "2.770311130495723", "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0", "1", "0", "0.6931471805599453", "0.0"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0", "1", "0", "0.6931471805599453", "0.0"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "5", "0", "1.791759469228055", "0.0"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "361.1883", "5.8921642423323215", "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "351.6383", "5.865442885732476", "0.6931471805599453", "4", "0", "1.6094379124341003", "0.0"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.9486", "0.6671111660091038", "0.6931471805599453", "2", "0", "1.0986122886681098", "0.0"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "1.7981", "1.0289406154187903", "0.6931471805599453", "3", "0", "1.3862943611198906", "0.0"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "21.5526", "3.1158503586370414", "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.005", "0.0049875415110389", "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.6445", "0.4974363866469745", "0.6931471805599453", "4", "0", "1.6094379124341003", "0.0"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "49.2922", "3.9178499954986576", "0.6931471805599453", "1", "0", "0.6931471805599453", "0.0"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "91.0883", "4.522747899360907", null, "0", "0", "0.0", "0.0"], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null, "0", "0", "0.0", "0.0"]], "shape": {"columns": 50, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "      <th>pr_count</th>\n", "      <th>pr_review_count</th>\n", "      <th>log_pr_count</th>\n", "      <th>log_pr_review_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.693147</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "      <td>1.421275</td>\n", "      <td>0.693147</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>1.386294</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "      <td>3.026300</td>\n", "      <td>0.693147</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1.609438</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "      <td>3.619184</td>\n", "      <td>0.693147</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>1.098612</td>\n", "      <td>1.791759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 50 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0        ...            0.144831     5.433722                        1.0   \n", "1        ...            0.144831     5.433722                        NaN   \n", "2        ...            0.144831     5.433722                        NaN   \n", "3        ...            0.144831     5.433722                        NaN   \n", "4        ...            0.144831     5.433722                        NaN   \n", "...      ...                 ...          ...                        ...   \n", "3503016  ...            0.018605     2.197225                        1.0   \n", "3503017  ...            0.018605     2.197225                        NaN   \n", "3503018  ...            0.018605     2.197225                        1.0   \n", "3503019  ...            0.018605     2.197225                        1.0   \n", "3503020  ...            0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge log_time_to_merge  log_pull_request_success_rate  \\\n", "0                  NaN               NaN                       0.693147   \n", "1             169.8895          5.141017                            NaN   \n", "2                  NaN               NaN                            NaN   \n", "3                  NaN               NaN                            NaN   \n", "4                  NaN               NaN                            NaN   \n", "...                ...               ...                            ...   \n", "3503016         3.1424          1.421275                       0.693147   \n", "3503017            NaN               NaN                            NaN   \n", "3503018        19.6208          3.026300                       0.693147   \n", "3503019        36.3071          3.619184                       0.693147   \n", "3503020            NaN               NaN                            NaN   \n", "\n", "         pr_count  pr_review_count  log_pr_count  log_pr_review_count  \n", "0               1                0      0.693147             0.000000  \n", "1               0                0      0.000000             0.000000  \n", "2               0                0      0.000000             0.000000  \n", "3               0                0      0.000000             0.000000  \n", "4               0                0      0.000000             0.000000  \n", "...           ...              ...           ...                  ...  \n", "3503016         3                0      1.386294             0.000000  \n", "3503017         0                0      0.000000             0.000000  \n", "3503018         4                0      1.609438             0.000000  \n", "3503019         2                5      1.098612             1.791759  \n", "3503020         0                0      0.000000             0.000000  \n", "\n", "[3503021 rows x 50 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data_with_metrics"]}, {"cell_type": "code", "execution_count": null, "id": "43c9a092", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}