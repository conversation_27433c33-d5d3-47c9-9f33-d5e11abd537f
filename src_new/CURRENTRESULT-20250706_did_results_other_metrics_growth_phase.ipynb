{"cells": [{"cell_type": "code", "execution_count": 19, "id": "961b1dec", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "# Read data\n", "compiled_data_test <- read.csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity_decelerating_to_steady.csv\")"]}, {"cell_type": "code", "execution_count": 20, "id": "24e42941", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  ) \n", "  \n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2da9677d", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "  accelerating first 3 months     saturation         steady \n", "        424666           1300         388078        2668089 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "# compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'first 3 months', 'saturation', 'steady'),]\n", "# table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 22, "id": "7e1a0927", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.383628                     1.059279 \n", "         log_project_commits     log_project_contributors \n", "                    1.966781                     2.232213 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.441547                     1.361649 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6270722  6270853 -3135351  6270702  3482123 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8417 -0.4979 -0.1642  0.5282  8.7294 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32888  0.5735  \n", " time_cohort_effect (Intercept) 0.02541  0.1594  \n", " Residual                       0.30272  0.5502  \n", "Number of obs: 3482133, groups:  \n", "repo_cohort_effect, 139906; time_cohort_effect, 139896\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   7.206e-01  2.348e-03  1.491e+05  306.96   <2e-16\n", "is_post_treatment            -1.917e-02  1.221e-03  1.171e+05  -15.70   <2e-16\n", "is_treated                   -6.718e-02  3.214e-03  1.266e+05  -20.90   <2e-16\n", "log_project_commits           2.676e-01  2.080e-03  1.775e+05  128.67   <2e-16\n", "log_project_contributors      3.261e-01  2.207e-03  1.898e+05  147.74   <2e-16\n", "log_project_age              -2.131e-01  1.589e-03  2.693e+05 -134.08   <2e-16\n", "is_post_treatment:is_treated -9.851e-02  1.186e-03  3.297e+06  -83.09   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.267                                                 \n", "is_treated  -0.689  0.106                                          \n", "lg_prjct_cm -0.057  0.003  0.084                                   \n", "lg_prjct_cn  0.094 -0.020 -0.128 -0.612                            \n", "log_prjct_g  0.062 -0.160 -0.041 -0.111      -0.332                \n", "is_pst_tr:_  0.124 -0.494 -0.179  0.004       0.005       0.027    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["           R2m       R2c\n", "[1,] 0.2562577 0.6573158\n"]}], "source": ["# Model 1: Fixed Effects Only\n", "model_pr_throughput_1 <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_pr_throughput_1 <- vif(model_pr_throughput_1)\n", "print(vif_model_pr_throughput_1)\n", "\n", "# Summary of the model\n", "summary(model_pr_throughput_1)\n", "\n", "# Calculate R-squared values\n", "r_squared_values <- r.squaredGLMM(model_pr_throughput_1)\n", "print(r_squared_values)\n"]}, {"cell_type": "code", "execution_count": null, "id": "47f13a54", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.384111\n", "is_treated                                                             1.060118\n", "log_project_commits                                                    1.999061\n", "log_project_contributors                                               2.285532\n", "log_project_age                                                        1.453796\n", "is_post_treatment:is_treated:log_tenure_c                              2.024715\n", "is_post_treatment:is_treated:log_commit_percent_c                      2.996651\n", "is_post_treatment:is_treated:log_commits_c                             4.831273\n", "is_post_treatment:is_treated:log_newcomers                             3.018950\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      5.629925\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.310857\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.959742\n", "is_post_treatment:is_treated:project_main_language                     1.521666\n", "is_post_treatment:is_treated:growth_phase                              3.637488\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "is_post_treatment:is_treated:growth_phase                               3\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.176483\n", "is_treated                                                                    1.029620\n", "log_project_commits                                                           1.413882\n", "log_project_contributors                                                      1.511798\n", "log_project_age                                                               1.205734\n", "is_post_treatment:is_treated:log_tenure_c                                     1.422925\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.731084\n", "is_post_treatment:is_treated:log_commits_c                                    2.198016\n", "is_post_treatment:is_treated:log_newcomers                                    1.737513\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.372746\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.819576\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.399908\n", "is_post_treatment:is_treated:project_main_language                            1.023597\n", "is_post_treatment:is_treated:growth_phase                                     1.240129\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 25 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:growth_phase +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6260152  6260518 -3130048  6260096  3482105 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8186 -0.5017 -0.1663  0.5267  8.7512 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32187  0.5673  \n", " time_cohort_effect (Intercept) 0.02502  0.1582  \n", " Residual                       0.30208  0.5496  \n", "Number of obs: 3482133, groups:  \n", "repo_cohort_effect, 139906; time_cohort_effect, 139896\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             7.197e-01\n", "is_post_treatment                                                      -2.034e-02\n", "is_treated                                                             -6.559e-02\n", "log_project_commits                                                     2.680e-01\n", "log_project_contributors                                                3.090e-01\n", "log_project_age                                                        -2.053e-01\n", "is_post_treatment:is_treated:log_tenure_c                               5.037e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.357e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.722e-02\n", "is_post_treatment:is_treated:log_newcomers                              9.184e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.332e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.897e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.303e-02\n", "is_post_treatment:is_treated:project_main_language1                    -3.908e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.977e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.402e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.704e-03\n", "is_post_treatment:is_treated:project_main_language5                     4.991e-03\n", "is_post_treatment:is_treated:project_main_language6                     3.010e-03\n", "is_post_treatment:is_treated:project_main_language7                    -5.058e-03\n", "is_post_treatment:is_treated:project_main_language8                    -7.272e-03\n", "is_post_treatment:is_treated:project_main_language9                    -1.320e-02\n", "is_post_treatment:is_treated:growth_phase1                             -1.963e-01\n", "is_post_treatment:is_treated:growth_phase2                             -2.746e-01\n", "is_post_treatment:is_treated:growth_phase3                              6.757e-01\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             2.324e-03\n", "is_post_treatment                                                       1.214e-03\n", "is_treated                                                              3.182e-03\n", "log_project_commits                                                     2.078e-03\n", "log_project_contributors                                                2.213e-03\n", "log_project_age                                                         1.584e-03\n", "is_post_treatment:is_treated:log_tenure_c                               1.425e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       1.743e-03\n", "is_post_treatment:is_treated:log_commits_c                              2.202e-03\n", "is_post_treatment:is_treated:log_newcomers                              1.025e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.377e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.822e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.400e-03\n", "is_post_treatment:is_treated:project_main_language1                     2.393e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.879e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.282e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.367e-03\n", "is_post_treatment:is_treated:project_main_language5                     3.136e-03\n", "is_post_treatment:is_treated:project_main_language6                     3.128e-03\n", "is_post_treatment:is_treated:project_main_language7                     3.949e-03\n", "is_post_treatment:is_treated:project_main_language8                     2.219e-03\n", "is_post_treatment:is_treated:project_main_language9                     4.729e-03\n", "is_post_treatment:is_treated:growth_phase1                              1.903e-03\n", "is_post_treatment:is_treated:growth_phase2                              3.374e-03\n", "is_post_treatment:is_treated:growth_phase3                              6.131e-03\n", "                                                                               df\n", "(Intercept)                                                             1.484e+05\n", "is_post_treatment                                                       1.148e+05\n", "is_treated                                                              1.254e+05\n", "log_project_commits                                                     1.833e+05\n", "log_project_contributors                                                1.948e+05\n", "log_project_age                                                         2.561e+05\n", "is_post_treatment:is_treated:log_tenure_c                               9.251e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       9.197e+05\n", "is_post_treatment:is_treated:log_commits_c                              9.306e+05\n", "is_post_treatment:is_treated:log_newcomers                              9.382e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.170e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  9.056e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           9.516e+05\n", "is_post_treatment:is_treated:project_main_language1                     9.212e+05\n", "is_post_treatment:is_treated:project_main_language2                     9.206e+05\n", "is_post_treatment:is_treated:project_main_language3                     9.249e+05\n", "is_post_treatment:is_treated:project_main_language4                     9.257e+05\n", "is_post_treatment:is_treated:project_main_language5                     9.271e+05\n", "is_post_treatment:is_treated:project_main_language6                     9.262e+05\n", "is_post_treatment:is_treated:project_main_language7                     9.182e+05\n", "is_post_treatment:is_treated:project_main_language8                     9.228e+05\n", "is_post_treatment:is_treated:project_main_language9                     9.224e+05\n", "is_post_treatment:is_treated:growth_phase1                              1.762e+06\n", "is_post_treatment:is_treated:growth_phase2                              1.128e+06\n", "is_post_treatment:is_treated:growth_phase3                              1.594e+06\n", "                                                                        t value\n", "(Intercept)                                                             309.602\n", "is_post_treatment                                                       -16.752\n", "is_treated                                                              -20.612\n", "log_project_commits                                                     128.981\n", "log_project_contributors                                                139.617\n", "log_project_age                                                        -129.604\n", "is_post_treatment:is_treated:log_tenure_c                                35.359\n", "is_post_treatment:is_treated:log_commit_percent_c                        -7.789\n", "is_post_treatment:is_treated:log_commits_c                              -12.358\n", "is_post_treatment:is_treated:log_newcomers                               89.610\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         5.606\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -26.877\n", "is_post_treatment:is_treated:log_project_age_before_treatment             9.304\n", "is_post_treatment:is_treated:project_main_language1                      -1.633\n", "is_post_treatment:is_treated:project_main_language2                       1.025\n", "is_post_treatment:is_treated:project_main_language3                       1.028\n", "is_post_treatment:is_treated:project_main_language4                       1.100\n", "is_post_treatment:is_treated:project_main_language5                       1.591\n", "is_post_treatment:is_treated:project_main_language6                       0.962\n", "is_post_treatment:is_treated:project_main_language7                      -1.281\n", "is_post_treatment:is_treated:project_main_language8                      -3.277\n", "is_post_treatment:is_treated:project_main_language9                      -2.792\n", "is_post_treatment:is_treated:growth_phase1                             -103.203\n", "is_post_treatment:is_treated:growth_phase2                              -81.399\n", "is_post_treatment:is_treated:growth_phase3                              110.219\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                               < 2e-16\n", "is_post_treatment:is_treated:log_commit_percent_c                      6.77e-15\n", "is_post_treatment:is_treated:log_commits_c                              < 2e-16\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      2.07e-08\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:project_main_language1                     0.10247\n", "is_post_treatment:is_treated:project_main_language2                     0.30534\n", "is_post_treatment:is_treated:project_main_language3                     0.30388\n", "is_post_treatment:is_treated:project_main_language4                     0.27136\n", "is_post_treatment:is_treated:project_main_language5                     0.11152\n", "is_post_treatment:is_treated:project_main_language6                     0.33599\n", "is_post_treatment:is_treated:project_main_language7                     0.20026\n", "is_post_treatment:is_treated:project_main_language8                     0.00105\n", "is_post_treatment:is_treated:project_main_language9                     0.00524\n", "is_post_treatment:is_treated:growth_phase1                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase2                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase3                              < 2e-16\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                       \n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                       \n", "is_post_treatment:is_treated:project_main_language4                       \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ** \n", "is_post_treatment:is_treated:project_main_language9                    ** \n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.2567324</td><td>0.6540246</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.2567324 & 0.6540246\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.2567324 | 0.6540246 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.2567324 0.6540246"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "\n", "model_pr_throughput_2 <- lmer(\n", "  log_pr_throughput ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    # is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_pr_throughput_2,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_time_to_merge_2) %>% plot()\n", "# performance::model_performance(model_time_to_merge_2) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_pr_throughput_2,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_pr_throughput_2, # 使用lmer模型\n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 24, "id": "9979b288", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'JavaScript'</li><li>'C'</li><li>'C#'</li><li>'C++'</li><li>'Go'</li><li>'Java'</li><li>'PHP'</li><li>'Python'</li><li>'Rust'</li><li>'TypeScript'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'JavaScript'\n", "\\item 'C'\n", "\\item 'C\\#'\n", "\\item 'C++'\n", "\\item 'Go'\n", "\\item 'Java'\n", "\\item 'PHP'\n", "\\item 'Python'\n", "\\item 'Rust'\n", "\\item 'TypeScript'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'JavaScript'\n", "2. 'C'\n", "3. 'C#'\n", "4. 'C++'\n", "5. 'Go'\n", "6. 'Java'\n", "7. 'PHP'\n", "8. '<PERSON>'\n", "9. 'Rust'\n", "10. 'TypeScript'\n", "\n", "\n"], "text/plain": [" [1] \"JavaScript\" \"C\"          \"C#\"         \"C++\"        \"Go\"        \n", " [6] \"Java\"       \"PHP\"        \"Python\"     \"Rust\"       \"TypeScript\""]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'steady'</li><li>'accelerating'</li><li>'first 3 months'</li><li>'saturation'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'steady'\n", "\\item 'accelerating'\n", "\\item 'first 3 months'\n", "\\item 'saturation'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'steady'\n", "2. 'accelerating'\n", "3. 'first 3 months'\n", "4. 'saturation'\n", "\n", "\n"], "text/plain": ["[1] \"steady\"         \"accelerating\"   \"first 3 months\" \"saturation\"    "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 查看 project_main_language 的水平和順序\n", "levels(compiled_data_test$project_main_language)\n", "\n", "# 查看 growth_phase 的水平和順序\n", "levels(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 30, "id": "303139ef", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "  accelerating first 3 months     saturation         steady \n", "        218108            803          90689        1062497 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test <- read.csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity_decelerating_to_steady.csv\")\n", "# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  ) %>%\n", "  # 移除含有缺失值的观测（确保数据清洁）\n", "  tidyr::drop_na()\n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")\n", "compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'first 3 months', 'saturation', 'steady'),]\n", "table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 31, "id": "aa0c2a3c", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.623885                     1.148717 \n", "         log_project_commits     log_project_contributors \n", "                    1.983940                     2.285541 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.452978                     1.698589 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5358412  5358533 -2679196  5358392  1372087 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2368 -0.6080  0.0024  0.5956  5.5650 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.09832  1.0480  \n", " time_cohort_effect (Intercept) 0.08205  0.2864  \n", " Residual                       2.47699  1.5738  \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.243e+00  5.260e-03  1.468e+05 616.622  < 2e-16\n", "is_post_treatment            -6.295e-02  4.334e-03  8.630e+04 -14.525  < 2e-16\n", "is_treated                    1.525e-01  7.277e-03  1.297e+05  20.960  < 2e-16\n", "log_project_commits           2.872e-03  4.736e-03  1.186e+05   0.606    0.544\n", "log_project_contributors      4.703e-01  4.984e-03  1.147e+05  94.354  < 2e-16\n", "log_project_age               3.677e-02  3.883e-03  1.395e+05   9.469  < 2e-16\n", "is_post_treatment:is_treated -4.337e-02  5.656e-03  1.291e+06  -7.667 1.76e-14\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits             \n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.385                                                 \n", "is_treated  -0.685  0.229                                          \n", "lg_prjct_cm -0.107 -0.012  0.084                                   \n", "lg_prjct_cn  0.057 -0.011 -0.134 -0.608                            \n", "log_prjct_g  0.115 -0.094 -0.029 -0.111      -0.351                \n", "is_pst_tr:_  0.227 -0.607 -0.320 -0.001      -0.012       0.020    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.07503747 0.3735589\n"]}], "source": ["# Model 1: Fixed Effects Only\n", "model_time_to_merge_1 <- lmer(\n", "  log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_time_to_merge_1 <- vif(model_time_to_merge_1)\n", "print(vif_model_time_to_merge_1)\n", "\n", "# Summary of the model\n", "summary(model_time_to_merge_1)\n", "\n", "# Calculate R-squared values\n", "r_squared_values <- r.squaredG<PERSON>M(model_time_to_merge_1)\n", "print(r_squared_values)\n"]}, {"cell_type": "code", "execution_count": null, "id": "0a7e8f5c", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.623167\n", "is_treated                                                             1.155880\n", "log_project_commits                                                    2.079134\n", "log_project_contributors                                               2.397632\n", "log_project_age                                                        1.481093\n", "is_post_treatment:is_treated:log_tenure_c                              2.078071\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.198156\n", "is_post_treatment:is_treated:log_commits_c                             4.595999\n", "is_post_treatment:is_treated:log_newcomers                             4.168907\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.562115\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.767058\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.064001\n", "is_post_treatment:is_treated:project_main_language                     1.482362\n", "is_post_treatment:is_treated:growth_phase                              4.702369\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "is_post_treatment:is_treated:growth_phase                               3\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.274036\n", "is_treated                                                                    1.075119\n", "log_project_commits                                                           1.441920\n", "log_project_contributors                                                      1.548429\n", "log_project_age                                                               1.217002\n", "is_post_treatment:is_treated:log_tenure_c                                     1.441552\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.788339\n", "is_post_treatment:is_treated:log_commits_c                                    2.143828\n", "is_post_treatment:is_treated:log_newcomers                                    2.041790\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.561663\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.940891\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.436663\n", "is_post_treatment:is_treated:project_main_language                            1.022110\n", "is_post_treatment:is_treated:growth_phase                                     1.294353\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 25 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:growth_phase +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5357686  5358026 -2678815  5357630  1372069 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2689 -0.6082  0.0018  0.5954  5.5927 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.09460  1.0462  \n", " time_cohort_effect (Intercept) 0.07993  0.2827  \n", " Residual                       2.47701  1.5739  \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             3.245e+00\n", "is_post_treatment                                                      -6.164e-02\n", "is_treated                                                              1.588e-01\n", "log_project_commits                                                    -1.334e-02\n", "log_project_contributors                                                4.746e-01\n", "log_project_age                                                         4.397e-02\n", "is_post_treatment:is_treated:log_tenure_c                               3.280e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -4.940e-02\n", "is_post_treatment:is_treated:log_commits_c                              5.123e-03\n", "is_post_treatment:is_treated:log_newcomers                              6.130e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       7.051e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -7.111e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment          -2.593e-02\n", "is_post_treatment:is_treated:project_main_language1                    -6.289e-02\n", "is_post_treatment:is_treated:project_main_language2                    -1.563e-02\n", "is_post_treatment:is_treated:project_main_language3                     9.530e-03\n", "is_post_treatment:is_treated:project_main_language4                     4.929e-02\n", "is_post_treatment:is_treated:project_main_language5                     5.936e-02\n", "is_post_treatment:is_treated:project_main_language6                     8.276e-03\n", "is_post_treatment:is_treated:project_main_language7                    -6.718e-02\n", "is_post_treatment:is_treated:project_main_language8                     1.155e-02\n", "is_post_treatment:is_treated:project_main_language9                     5.730e-03\n", "is_post_treatment:is_treated:growth_phase1                             -1.781e-01\n", "is_post_treatment:is_treated:growth_phase2                             -1.860e-01\n", "is_post_treatment:is_treated:growth_phase3                              5.749e-01\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             5.250e-03\n", "is_post_treatment                                                       4.316e-03\n", "is_treated                                                              7.290e-03\n", "log_project_commits                                                     4.840e-03\n", "log_project_contributors                                                5.096e-03\n", "log_project_age                                                         3.914e-03\n", "is_post_treatment:is_treated:log_tenure_c                               6.208e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       9.862e-03\n", "is_post_treatment:is_treated:log_commits_c                              8.632e-03\n", "is_post_treatment:is_treated:log_newcomers                              3.926e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.746e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  7.280e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           5.943e-03\n", "is_post_treatment:is_treated:project_main_language1                     1.090e-02\n", "is_post_treatment:is_treated:project_main_language2                     1.757e-02\n", "is_post_treatment:is_treated:project_main_language3                     1.790e-02\n", "is_post_treatment:is_treated:project_main_language4                     1.369e-02\n", "is_post_treatment:is_treated:project_main_language5                     1.185e-02\n", "is_post_treatment:is_treated:project_main_language6                     1.293e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.792e-02\n", "is_post_treatment:is_treated:project_main_language8                     9.608e-03\n", "is_post_treatment:is_treated:project_main_language9                     1.838e-02\n", "is_post_treatment:is_treated:growth_phase1                              9.232e-03\n", "is_post_treatment:is_treated:growth_phase2                              1.375e-02\n", "is_post_treatment:is_treated:growth_phase3                              3.075e-02\n", "                                                                               df\n", "(Intercept)                                                             1.465e+05\n", "is_post_treatment                                                       8.275e+04\n", "is_treated                                                              1.302e+05\n", "log_project_commits                                                     1.308e+05\n", "log_project_contributors                                                1.257e+05\n", "log_project_age                                                         1.503e+05\n", "is_post_treatment:is_treated:log_tenure_c                               2.540e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       3.133e+05\n", "is_post_treatment:is_treated:log_commits_c                              2.089e+05\n", "is_post_treatment:is_treated:log_newcomers                              2.088e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.101e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.968e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.172e+05\n", "is_post_treatment:is_treated:project_main_language1                     2.427e+05\n", "is_post_treatment:is_treated:project_main_language2                     2.360e+05\n", "is_post_treatment:is_treated:project_main_language3                     2.212e+05\n", "is_post_treatment:is_treated:project_main_language4                     2.150e+05\n", "is_post_treatment:is_treated:project_main_language5                     1.989e+05\n", "is_post_treatment:is_treated:project_main_language6                     2.112e+05\n", "is_post_treatment:is_treated:project_main_language7                     2.502e+05\n", "is_post_treatment:is_treated:project_main_language8                     2.338e+05\n", "is_post_treatment:is_treated:project_main_language9                     2.130e+05\n", "is_post_treatment:is_treated:growth_phase1                              4.453e+05\n", "is_post_treatment:is_treated:growth_phase2                              2.813e+05\n", "is_post_treatment:is_treated:growth_phase3                              4.223e+05\n", "                                                                       t value\n", "(Intercept)                                                            618.165\n", "is_post_treatment                                                      -14.282\n", "is_treated                                                              21.782\n", "log_project_commits                                                     -2.757\n", "log_project_contributors                                                93.127\n", "log_project_age                                                         11.234\n", "is_post_treatment:is_treated:log_tenure_c                                5.284\n", "is_post_treatment:is_treated:log_commit_percent_c                       -5.009\n", "is_post_treatment:is_treated:log_commits_c                               0.593\n", "is_post_treatment:is_treated:log_newcomers                              15.616\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        7.235\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -9.768\n", "is_post_treatment:is_treated:log_project_age_before_treatment           -4.362\n", "is_post_treatment:is_treated:project_main_language1                     -5.772\n", "is_post_treatment:is_treated:project_main_language2                     -0.889\n", "is_post_treatment:is_treated:project_main_language3                      0.532\n", "is_post_treatment:is_treated:project_main_language4                      3.601\n", "is_post_treatment:is_treated:project_main_language5                      5.008\n", "is_post_treatment:is_treated:project_main_language6                      0.640\n", "is_post_treatment:is_treated:project_main_language7                     -3.748\n", "is_post_treatment:is_treated:project_main_language8                      1.202\n", "is_post_treatment:is_treated:project_main_language9                      0.312\n", "is_post_treatment:is_treated:growth_phase1                             -19.288\n", "is_post_treatment:is_treated:growth_phase2                             -13.525\n", "is_post_treatment:is_treated:growth_phase3                              18.695\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                    0.005836\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                              1.27e-07\n", "is_post_treatment:is_treated:log_commit_percent_c                      5.48e-07\n", "is_post_treatment:is_treated:log_commits_c                             0.552859\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      4.68e-13\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.29e-05\n", "is_post_treatment:is_treated:project_main_language1                    7.85e-09\n", "is_post_treatment:is_treated:project_main_language2                    0.373765\n", "is_post_treatment:is_treated:project_main_language3                    0.594511\n", "is_post_treatment:is_treated:project_main_language4                    0.000317\n", "is_post_treatment:is_treated:project_main_language5                    5.49e-07\n", "is_post_treatment:is_treated:project_main_language6                    0.522118\n", "is_post_treatment:is_treated:project_main_language7                    0.000178\n", "is_post_treatment:is_treated:project_main_language8                    0.229256\n", "is_post_treatment:is_treated:project_main_language9                    0.755231\n", "is_post_treatment:is_treated:growth_phase1                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase2                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase3                              < 2e-16\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ** \n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                       \n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                       \n", "is_post_treatment:is_treated:project_main_language9                       \n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.0756137</td><td>0.3729458</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.0756137 & 0.3729458\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.0756137 | 0.3729458 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.0756137 0.3729458"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "model_time_to_merge_2 <- lmer(\n", "  log_time_to_merge ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_time_to_merge_2,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_time_to_merge_2) %>% plot()\n", "# performance::model_performance(model_time_to_merge_2) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_time_to_merge_2,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_time_to_merge_2, # 使用lmer模型\n", "  null = lmer(log_time_to_merge ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 33, "id": "72d5eeb2", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.647148                     1.184922 \n", "         log_project_commits     log_project_contributors \n", "                    1.983104                     2.286067 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.453067                     1.758309 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", "-1693866 -1693745   846943 -1693886  1372087 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3712 -0.2066  0.2417  0.4907  3.5790 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " repo_cohort_effect (Intercept) 0.0047070 0.06861 \n", " time_cohort_effect (Intercept) 0.0004253 0.02062 \n", " Residual                       0.0148595 0.12190 \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df  t value Pr(>|t|)\n", "(Intercept)                   6.153e-01  3.665e-04  1.489e+05 1678.964  < 2e-16\n", "is_post_treatment             1.887e-05  3.298e-04  9.302e+04    0.057   0.9544\n", "is_treated                   -1.648e-03  5.052e-04  1.313e+05   -3.263   0.0011\n", "log_project_commits           5.540e-03  3.252e-04  1.118e+05   17.037  < 2e-16\n", "log_project_contributors     -2.460e-02  3.414e-04  1.066e+05  -72.052  < 2e-16\n", "log_project_age               4.546e-03  2.689e-04  1.290e+05   16.905  < 2e-16\n", "is_post_treatment:is_treated -2.650e-03  4.362e-04  1.303e+06   -6.076 1.23e-09\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment               \n", "is_treated                   ** \n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.421                                                 \n", "is_treated  -0.682  0.256                                          \n", "lg_prjct_cm -0.113 -0.012  0.083                                   \n", "lg_prjct_cn  0.050 -0.009 -0.132 -0.607                            \n", "log_prjct_g  0.122 -0.085 -0.028 -0.112      -0.353                \n", "is_pst_tr:_  0.254 -0.616 -0.360 -0.002      -0.013       0.019    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.02209389 0.2731404\n"]}], "source": ["model_pr_accept_rate_1 <- lmer(\n", "  log_pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_pr_accept_rate_1 <- vif(model_pr_accept_rate_1)\n", "print(vif_model_pr_accept_rate_1)\n", "# Summary of the model\n", "summary(model_pr_accept_rate_1)\n", "# Calculate R-squared values\n", "r_squared_values_pr_accept_rate <- r.squaredGLMM(model_pr_accept_rate_1)\n", "print(r_squared_values_pr_accept_rate)"]}, {"cell_type": "code", "execution_count": 34, "id": "f0f2e44b", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.639704\n", "is_treated                                                             1.191945\n", "log_project_commits                                                    2.103927\n", "log_project_contributors                                               2.426304\n", "log_project_age                                                        1.495831\n", "is_post_treatment:is_treated:log_tenure_c                              2.076198\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.184444\n", "is_post_treatment:is_treated:log_commits_c                             4.594073\n", "is_post_treatment:is_treated:log_newcomers                             4.131154\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.568832\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.787709\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.076950\n", "is_post_treatment:is_treated:project_main_language                     1.477718\n", "is_post_treatment:is_treated:growth_phase                              4.727259\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "is_post_treatment:is_treated:growth_phase                               3\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.280509\n", "is_treated                                                                    1.091762\n", "log_project_commits                                                           1.450492\n", "log_project_contributors                                                      1.557660\n", "log_project_age                                                               1.223042\n", "is_post_treatment:is_treated:log_tenure_c                                     1.440902\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.784501\n", "is_post_treatment:is_treated:log_commits_c                                    2.143379\n", "is_post_treatment:is_treated:log_newcomers                                    2.032524\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.562973\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.946204\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.441163\n", "is_post_treatment:is_treated:project_main_language                            1.021931\n", "is_post_treatment:is_treated:growth_phase                                     1.295492\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 25 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_tenure_c + is_post_treatment:is_treated:log_commit_percent_c +  \n", "    is_post_treatment:is_treated:log_commits_c + is_post_treatment:is_treated:log_newcomers +  \n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:growth_phase +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "       AIC        BIC     logLik   deviance   df.resid \n", "-1693948.5 -1693608.8   847002.3 -1694004.5    1372069 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3864 -0.2068  0.2417  0.4908  3.5766 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.004700 0.06856 \n", " time_cohort_effect (Intercept) 0.000427 0.02066 \n", " Residual                       0.014859 0.12190 \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             6.154e-01\n", "is_post_treatment                                                       4.543e-06\n", "is_treated                                                             -1.566e-03\n", "log_project_commits                                                     5.281e-03\n", "log_project_contributors                                               -2.455e-02\n", "log_project_age                                                         4.577e-03\n", "is_post_treatment:is_treated:log_tenure_c                               1.476e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.546e-03\n", "is_post_treatment:is_treated:log_commits_c                              6.677e-05\n", "is_post_treatment:is_treated:log_newcomers                              4.901e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       3.620e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.038e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.833e-04\n", "is_post_treatment:is_treated:project_main_language1                    -3.792e-03\n", "is_post_treatment:is_treated:project_main_language2                     2.071e-07\n", "is_post_treatment:is_treated:project_main_language3                     4.874e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.096e-03\n", "is_post_treatment:is_treated:project_main_language5                    -1.988e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.253e-03\n", "is_post_treatment:is_treated:project_main_language7                     5.286e-04\n", "is_post_treatment:is_treated:project_main_language8                     1.177e-03\n", "is_post_treatment:is_treated:project_main_language9                     6.227e-04\n", "is_post_treatment:is_treated:growth_phase1                             -3.843e-03\n", "is_post_treatment:is_treated:growth_phase2                             -3.764e-03\n", "is_post_treatment:is_treated:growth_phase3                              1.419e-02\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             3.663e-04\n", "is_post_treatment                                                       3.292e-04\n", "is_treated                                                              5.064e-04\n", "log_project_commits                                                     3.348e-04\n", "log_project_contributors                                                3.516e-04\n", "log_project_age                                                         2.728e-04\n", "is_post_treatment:is_treated:log_tenure_c                               4.678e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                       7.430e-04\n", "is_post_treatment:is_treated:log_commits_c                              6.511e-04\n", "is_post_treatment:is_treated:log_newcomers                              2.959e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       7.366e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  5.513e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.499e-04\n", "is_post_treatment:is_treated:project_main_language1                     8.216e-04\n", "is_post_treatment:is_treated:project_main_language2                     1.324e-03\n", "is_post_treatment:is_treated:project_main_language3                     1.350e-03\n", "is_post_treatment:is_treated:project_main_language4                     1.033e-03\n", "is_post_treatment:is_treated:project_main_language5                     8.947e-04\n", "is_post_treatment:is_treated:project_main_language6                     9.755e-04\n", "is_post_treatment:is_treated:project_main_language7                     1.351e-03\n", "is_post_treatment:is_treated:project_main_language8                     7.245e-04\n", "is_post_treatment:is_treated:project_main_language9                     1.387e-03\n", "is_post_treatment:is_treated:growth_phase1                              7.024e-04\n", "is_post_treatment:is_treated:growth_phase2                              1.042e-03\n", "is_post_treatment:is_treated:growth_phase3                              2.335e-03\n", "                                                                               df\n", "(Intercept)                                                             1.489e+05\n", "is_post_treatment                                                       8.905e+04\n", "is_treated                                                              1.312e+05\n", "log_project_commits                                                     1.260e+05\n", "log_project_contributors                                                1.193e+05\n", "log_project_age                                                         1.428e+05\n", "is_post_treatment:is_treated:log_tenure_c                               2.682e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       3.292e+05\n", "is_post_treatment:is_treated:log_commits_c                              2.199e+05\n", "is_post_treatment:is_treated:log_newcomers                              2.195e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.209e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  2.060e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.268e+05\n", "is_post_treatment:is_treated:project_main_language1                     2.565e+05\n", "is_post_treatment:is_treated:project_main_language2                     2.497e+05\n", "is_post_treatment:is_treated:project_main_language3                     2.335e+05\n", "is_post_treatment:is_treated:project_main_language4                     2.264e+05\n", "is_post_treatment:is_treated:project_main_language5                     2.099e+05\n", "is_post_treatment:is_treated:project_main_language6                     2.229e+05\n", "is_post_treatment:is_treated:project_main_language7                     2.643e+05\n", "is_post_treatment:is_treated:project_main_language8                     2.471e+05\n", "is_post_treatment:is_treated:project_main_language9                     2.247e+05\n", "is_post_treatment:is_treated:growth_phase1                              4.775e+05\n", "is_post_treatment:is_treated:growth_phase2                              2.992e+05\n", "is_post_treatment:is_treated:growth_phase3                              4.515e+05\n", "                                                                        t value\n", "(Intercept)                                                            1679.748\n", "is_post_treatment                                                         0.014\n", "is_treated                                                               -3.092\n", "log_project_commits                                                      15.773\n", "log_project_contributors                                                -69.837\n", "log_project_age                                                          16.781\n", "is_post_treatment:is_treated:log_tenure_c                                 3.154\n", "is_post_treatment:is_treated:log_commit_percent_c                        -4.772\n", "is_post_treatment:is_treated:log_commits_c                                0.103\n", "is_post_treatment:is_treated:log_newcomers                                1.656\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         0.491\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.883\n", "is_post_treatment:is_treated:log_project_age_before_treatment             0.630\n", "is_post_treatment:is_treated:project_main_language1                      -4.616\n", "is_post_treatment:is_treated:project_main_language2                       0.000\n", "is_post_treatment:is_treated:project_main_language3                       3.609\n", "is_post_treatment:is_treated:project_main_language4                       2.030\n", "is_post_treatment:is_treated:project_main_language5                      -2.222\n", "is_post_treatment:is_treated:project_main_language6                      -1.284\n", "is_post_treatment:is_treated:project_main_language7                       0.391\n", "is_post_treatment:is_treated:project_main_language8                       1.624\n", "is_post_treatment:is_treated:project_main_language9                       0.449\n", "is_post_treatment:is_treated:growth_phase1                               -5.472\n", "is_post_treatment:is_treated:growth_phase2                               -3.611\n", "is_post_treatment:is_treated:growth_phase3                                6.076\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                      0.988991\n", "is_treated                                                             0.001990\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                              0.001609\n", "is_post_treatment:is_treated:log_commit_percent_c                      1.82e-06\n", "is_post_treatment:is_treated:log_commits_c                             0.918325\n", "is_post_treatment:is_treated:log_newcomers                             0.097672\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      0.623148\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 0.059727\n", "is_post_treatment:is_treated:log_project_age_before_treatment          0.528905\n", "is_post_treatment:is_treated:project_main_language1                    3.91e-06\n", "is_post_treatment:is_treated:project_main_language2                    0.999875\n", "is_post_treatment:is_treated:project_main_language3                    0.000307\n", "is_post_treatment:is_treated:project_main_language4                    0.042342\n", "is_post_treatment:is_treated:project_main_language5                    0.026297\n", "is_post_treatment:is_treated:project_main_language6                    0.199063\n", "is_post_treatment:is_treated:project_main_language7                    0.695618\n", "is_post_treatment:is_treated:project_main_language8                    0.104394\n", "is_post_treatment:is_treated:project_main_language9                    0.653532\n", "is_post_treatment:is_treated:growth_phase1                             4.46e-08\n", "is_post_treatment:is_treated:growth_phase2                             0.000305\n", "is_post_treatment:is_treated:growth_phase3                             1.23e-09\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                         \n", "is_treated                                                             ** \n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ** \n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             .  \n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment .  \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    *  \n", "is_post_treatment:is_treated:project_main_language5                    *  \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                       \n", "is_post_treatment:is_treated:project_main_language9                       \n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.02219034</td><td>0.2730451</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.02219034 & 0.2730451\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.02219034 | 0.2730451 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.02219034 0.2730451"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "model_pr_accept_rate_4 <- lmer(\n", "  log_pull_request_success_rate ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "# 计算VIF（使用car包改进方法）\n", "vif_model_pr_accept_rate_4 <- car::vif(\n", "  model_pr_accept_rate_4,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model_pr_accept_rate_4)\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_pr_accept_rate_4) %>% plot()\n", "# performance::model_performance(model_pr_accept_rate_4) %>% print()\n", "# 模型总结（优化显示）\n", "summary(model_pr_accept_rate_4,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_pr_accept_rate_4, # 使用lmer模型\n", "  null = lmer(log_pull_request_success_rate ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n", "# 计算模型的AIC值"]}, {"cell_type": "code", "execution_count": 35, "id": "3420c149", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 25 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n", "\n", "Correlation matrix not shown by default, as p = 25 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n", "\n", "Correlation matrix not shown by default, as p = 25 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n"]}], "source": ["# 保存和打印以上所有模型结果到文件中，命名格式为日期_模型名称.txt\n", "# 目录在“../result/did_result_20250408/”下\n", "# 需要确保该目录存在\n", "output_dir <- \"../result/did_result_20250706/\"\n", "if (!dir.exists(output_dir)) {\n", "  dir.create(output_dir, recursive = TRUE)\n", "}\n", "# 保存结果包括模型摘要、VIF和R-squared\n", "save_model_results <- function(model, model_name) {\n", "  # 创建文件名\n", "  file_name <- paste0(output_dir, Sys.Date(), \"_\", model_name, \".txt\")\n", "  \n", "  # 打开文件连接\n", "  sink(file_name)\n", "  \n", "  # 打印模型摘要\n", "  cat(\"Model Summary:\\n\")\n", "  print(summary(model))\n", "  \n", "  # 打印VIF\n", "  cat(\"\\nVIF:\\n\")\n", "  print(vif(model))\n", "  \n", "  # 打印R-squared\n", "  cat(\"\\nR-squared:\\n\")\n", "  print(r.squaredGLMM(model))\n", "  \n", "  # 关闭文件连接\n", "  sink()\n", "}\n", "# 保存模型结果\n", "save_model_results(model_pr_throughput_1, \"model_pr_throughput_1\")\n", "save_model_results(model_pr_throughput_2, \"model_pr_throughput_2\")\n", "save_model_results(model_time_to_merge_1, \"model_time_to_merge_1\")\n", "save_model_results(model_time_to_merge_2, \"model_time_to_merge_2\")\n", "save_model_results(model_pr_accept_rate_1, \"model_pr_accept_rate_1\")\n", "save_model_results(model_pr_accept_rate_4, \"model_pr_accept_rate_4\")"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 5}