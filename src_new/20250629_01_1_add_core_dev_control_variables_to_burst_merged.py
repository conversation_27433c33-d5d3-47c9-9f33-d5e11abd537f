import pandas as pd
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
import os
import logging
import inspect
import time
import multiprocessing

# Set up logging
log_file = '../logs/add_core_dev_control_variables_to_burst_merged.log'
logging.basicConfig(
    filename=log_file,
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - Line %(lineno)d - %(message)s'
)

# ATTRITION_LIMITS = [180, 270, 450]
ATTRITION_LIMITS = [365,180, 270, 450]

# Cache for processed commit files to avoid repeated I/O
_commit_cache = {}

def get_processed_commit_file_repo_name(repo_name):
    """Get the processed commit file for a repository with caching."""
    if repo_name in _commit_cache:
        return _commit_cache[repo_name]
    
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
    
    # Cache the result
    _commit_cache[repo_name] = repo_commit
    return repo_commit

def process_repo_for_limit(repo, attritions_df, limit):
    """
    Process a single repository for a specific attrition limit, read its commits, 
    and calculate tenure, commit percentage and the number of commits for each attrition record.
    
    Returns a list of tuples (global_index, tenure, commit_percent, commit_count).
    """
    results = []
    logging.info(f"Starting to process repository: {repo} for limit {limit}")
    
    # Filter attrition records for the given repository
    repo_attrition = attritions_df[attritions_df['repo_name'] == repo]
    if repo_attrition.empty:
        logging.warning(f"Repository {repo} has no attrition records for limit {limit}. Skipping.")
        return results

    # Get processed commits for this repository
    try:
        commits = get_processed_commit_file_repo_name(repo)
        if commits.empty:
            logging.warning(f"Repository {repo} has no valid commits after filtering. Skipping.")
            for idx, row in repo_attrition.iterrows():
                results.append((row['index'], pd.NaT, 0, 0))
            return results
    except Exception as e:
        logging.error(f"Error reading commit file for repository {repo}: {e}")
        for idx, row in repo_attrition.iterrows():
            results.append((row['index'], pd.NaT, 0, 0))
        return results

    # Convert commit dates to datetime format, and remove timezone if present
    commits['date'] = pd.to_datetime(commits['date'], errors='coerce')

    # Ensure the commit dates are timezone-aware
    if commits['date'].dt.tz is None:
        commits['date'] = commits['date'].dt.tz_localize('UTC')
    else:
        commits['date'] = commits['date'].dt.tz_convert('UTC')

    commits = commits[commits['date'].notna()]

    total_commits = len(commits)
    if total_commits == 0:
        logging.warning(f"Repository {repo} has no valid commit records after filtering. Skipping.")
        for idx, row in repo_attrition.iterrows():
            results.append((row['index'], pd.NaT, 0, 0))
        return results

    # Pre-convert attrition dates to avoid repeated conversion
    repo_attrition = repo_attrition.copy()
    repo_attrition['attrition_date_converted'] = pd.to_datetime(repo_attrition['attrition_time']).dt.tz_localize('UTC')
    
    # Pre-calculate developer commit statistics for better performance
    dev_stats = {}
    unique_developers = repo_attrition['dev_login'].unique()
    
    for developer in unique_developers:
        dev_commits = commits[commits['author_login'] == developer]
        if not dev_commits.empty:
            dev_stats[developer] = {
                'commits': dev_commits
            }
        else:
            dev_stats[developer] = {
                'commits': pd.DataFrame()
            }

    # Calculate tenure, commit percentage and commit count for each attrition record
    for idx, row in repo_attrition.iterrows():
        developer = row['dev_login']
        attrition_date = row['attrition_date_converted']
        
        # Get pre-calculated developer stats
        dev_data = dev_stats[developer]
        dev_commits = dev_data['commits']
        
        if not dev_commits.empty:
            dev_commits_before = dev_commits[dev_commits['date'] < attrition_date]
            commit_count = len(dev_commits_before)
            commit_percent = commit_count / total_commits if total_commits > 0 else 0
            
            # Calculate tenure - use only commits before attrition_date (same as original logic)
            if commit_count > 0:
                first_commit_date = dev_commits_before['date'].min()
                tenure = attrition_date - first_commit_date
                tenure = tenure.days
            else:
                tenure = pd.NaT
        else:
            commit_count = 0
            commit_percent = 0
            tenure = pd.NaT
        
        results.append((row['index'], tenure, commit_percent, commit_count))

    logging.info(f"Completed processing repository {repo} for limit {limit}")
    return results

def process_attrition_limit(limit):
    """
    Process attrition data for a specific limit, adding commit_percent and commits columns.
    """
    start_time = time.time()
    logging.info(f"Starting processing for attrition limit: {limit}")
    
    # Define file paths
    input_file = f"../data/attrition_csv/attrition_burst_{limit}.csv"
    # input_file = f"../data/attrition_csv/attrition_burst_{limit}.csv"
    output_file = f"../data/attrition_csv/attrition_burst_core_merged_dev_{limit}.csv"
    
    # Check if input file exists
    if not os.path.exists(input_file):
        logging.error(f"Input file not found: {input_file}")
        return
    
    # Read the attrition data
    logging.info(f"Reading input file: {input_file}")
    try:
        attritions = pd.read_csv(input_file)
        logging.info(f"Successfully loaded {len(attritions)} records from {input_file}")
    except Exception as e:
        logging.error(f"Error reading file {input_file}: {e}")
        return
    
    # Reset index to keep track of the original row indices
    attritions.reset_index(inplace=True)
    
    # Add someone_left column with value 1
    attritions['someone_left'] = 1
    
    # Get the list of unique repositories
    repo_list = attritions['repo_name'].unique()
    logging.info(f"Found {len(repo_list)} unique repositories to process")
    
    # Use multithreading to process each repository in parallel
    results_all = []
    
    # Calculate optimal number of workers based on system capabilities
    optimal_workers = min(80, multiprocessing.cpu_count() * 4, len(repo_list))
    
    with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
        future_to_repo = {executor.submit(process_repo_for_limit, repo, attritions, limit): repo for repo in repo_list}
        
        completed = 0
        for future in as_completed(future_to_repo):
            repo = future_to_repo[future]
            completed += 1
            
            if completed % 10 == 0 or completed == len(repo_list):
                logging.info(f"Progress: {completed}/{len(repo_list)} repositories processed ({completed/len(repo_list)*100:.1f}%)")
            
            try:
                repo_results = future.result()
                results_all.extend(repo_results)
            except Exception as exc:
                logging.error(f"Error processing repository {repo}: {exc}")
    
    # Update the attritions DataFrame with the calculated results
    logging.info("Starting to update the attritions DataFrame with tenure and commit data")
    
    # Create dictionaries for faster lookup and update
    tenure_dict = {}
    commit_percent_dict = {}
    commit_count_dict = {}
    
    for idx, tenure, commit_percent, commit_count in results_all:
        tenure_dict[idx] = tenure
        commit_percent_dict[idx] = commit_percent
        commit_count_dict[idx] = commit_count
    
    # Use vectorized operations for better performance
    attritions['tenure'] = attritions['index'].map(tenure_dict)
    attritions['commit_percent'] = attritions['index'].map(commit_percent_dict)
    attritions['commits'] = attritions['index'].map(commit_count_dict)
    
    # Drop the temporary 'index' column
    attritions.drop(columns=['index'], inplace=True)
    
    # Save the updated result
    logging.info(f"Saving the updated attritions data to: {output_file}")
    attritions.to_csv(output_file, index=False)
    
    processing_time = time.time() - start_time
    logging.info(f"Completed processing for limit {limit} in {processing_time:.2f} seconds")
    logging.info(f"Final dataset contains {len(attritions)} records with tenure and commit data")

def main():
    """
    Main function to process all attrition limits.
    """
    overall_start_time = time.time()
    logging.info("=" * 60)
    logging.info("STARTING CORE DEV CONTROL VARIABLES ADDITION (TENURE + COMMIT DATA + SOMEONE_LEFT)")
    logging.info("=" * 60)
    
    total_limits = len(ATTRITION_LIMITS)
    
    for limit_idx, limit in enumerate(ATTRITION_LIMITS, 1):
        logging.info("-" * 40)
        logging.info(f"PROCESSING LIMIT {limit_idx}/{total_limits}: {limit}")
        logging.info("-" * 40)
        
        process_attrition_limit(limit)
        
        # Calculate progress percentage
        progress = (limit_idx / total_limits) * 100
        logging.info(f"Overall progress: {progress:.1f}% ({limit_idx}/{total_limits} limits completed)")
    
    overall_processing_time = time.time() - overall_start_time
    logging.info("=" * 60)
    logging.info("CORE DEV CONTROL VARIABLES ADDITION COMPLETED (TENURE + COMMIT DATA + SOMEONE_LEFT)")
    logging.info(f"Total processing time: {overall_processing_time:.2f} seconds")
    logging.info("=" * 60)
    
    # Clear cache to free memory
    global _commit_cache
    _commit_cache.clear()

if __name__ == "__main__":
    logging.info(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    main()
    logging.info(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}") 