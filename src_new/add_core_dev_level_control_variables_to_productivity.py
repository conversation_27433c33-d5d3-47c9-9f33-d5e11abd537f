import pandas as pd
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import os
import logging
import inspect

# Set up logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - Line %(lineno)d - %(message)s', 
    handlers=[logging.StreamHandler()]
)

# Read the data
repo_info = pd.read_csv('../data/sample_projects_total.csv')
attritions = pd.read_csv('../data/attritions_20250227_add_burst.csv')
productivity = pd.read_csv('../data/2025_0227_productivity.csv')
bot_list = pd.read_csv('../data/bot_developer_list_original.csv')

# Ensure attrition_date is in datetime format and remove timezone if present
# Reset index to keep track of the original row indices
attritions.reset_index(inplace=True)  # Retain original row numbers in 'index' column

def get_processed_commit_file_repo_name(repo_name):
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
        return None
    return repo_commit

def process_repo(repo):
    """
    Process a single repository, read its commits, exclude bot commits,
    and calculate tenure, commit percentage, and the number of commits for each attrition record.
    
    Returns a list of tuples (global_index, tenure, commit_percent, commit_count).
    """
    results = []
    logging.info(f"Starting to process repository: {repo}")
    
    # Filter attrition records for the given repository (using global attritions DataFrame)
    repo_attrition = attritions[attritions['repo_name'] == repo]
    if repo_attrition.empty:
        logging.warning(f"Repository {repo} has no attrition records. Skipping.")
        return results

    # Get the list of bots for this repository and create a set for faster lookup
    # bot_names = set(bot_list['bot_name'].values)
    
    # # Construct the commit file path and check if the file exists
    # commit_filename = repo.replace('/', '_') + '_commits.csv'
    # commit_filepath = os.path.join('../data/commits/', commit_filename)
    # if not os.path.exists(commit_filepath):
    #     logging.warning(f"Repository {repo} commit file not found. Skipping.")
    #     for idx, row in repo_attrition.iterrows():
    #         results.append((row['index'], pd.NaT, 0, 0))
    #     return results

    # try:
    #     commits = pd.read_csv(commit_filepath)
    #     logging.info(f"Successfully read the commit file for repository {repo}")
    # except Exception as e:
    #     logging.error(f"Error reading commit file for repository {repo}: {e}")
    #     for idx, row in repo_attrition.iterrows():
    #         results.append((row['index'], pd.NaT, 0, 0))
    #     return results
    # commits = commits[commits['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)
    # # Exclude bot commits
    # commits = commits[~commits['author_login'].isin(bot_names)]
    commits = get_processed_commit_file_repo_name(repo)
    if commits.empty:
        logging.warning(f"Repository {repo} has no valid commits after filtering out bots. Skipping.")
        for idx, row in repo_attrition.iterrows():
            results.append((row['index'], pd.NaT, 0, 0))
        return results

    # Convert commit dates to datetime format, and remove timezone if present
    commits['date'] = pd.to_datetime(commits['date'], errors='coerce')

    # Ensure the commit dates are timezone-aware
    if commits['date'].dt.tz is None:
        commits['date'] = commits['date'].dt.tz_localize('UTC')  # Localize to UTC if no timezone is set
    else:
        commits['date'] = commits['date'].dt.tz_convert('UTC')  # Convert to UTC if already timezone-aware

    commits = commits[commits['date'].notna()]

    total_commits = len(commits)
    if total_commits == 0:
        logging.warning(f"Repository {repo} has no valid commit records after filtering. Skipping.")
        for idx, row in repo_attrition.iterrows():
            results.append((row['index'], pd.NaT, 0, 0))
        return results


    # Calculate tenure, commit percentage, and commit count for each attrition record
    for idx, row in repo_attrition.iterrows():
        developer = row['attrition_developer']
        attrition_date = row['attrition_date']
        
        # Ensure the attrition_date is timezone naive before comparison
        attrition_date = pd.to_datetime(attrition_date)  # Convert back to datetime if it's still a string        
        # check if the date is timezone-aware and convert to UTC if necessary
        attrition_date = attrition_date.tz_localize('UTC')
        

        # Perform the comparison
        dev_commits = commits[commits['author_login'] == developer]
        dev_commits_before = dev_commits[dev_commits['date'] < attrition_date]
        
        commit_count = len(dev_commits_before)
        commit_percent = commit_count / total_commits if total_commits > 0 else 0
        if commit_count > 0:
            first_commit_date = dev_commits_before['date'].min()
            tenure = attrition_date - first_commit_date
            # Convert tenure to days
            tenure = tenure.days
        else:
            tenure = pd.NaT
        
        results.append((row['index'], tenure, commit_percent, commit_count))

    logging.info(f"Completed processing repository {repo}")
    return results

# Get the list of unique repositories
repo_list = attritions['repo_name'].unique()

# Use multithreading to process each repository in parallel
results_all = []
with ThreadPoolExecutor(max_workers=80) as executor:
    future_to_repo = {executor.submit(process_repo, repo): repo for repo in repo_list}
    for future in as_completed(future_to_repo):
        repo = future_to_repo[future]
        try:
            repo_results = future.result()
            results_all.extend(repo_results)
        except Exception as exc:
            logging.error(f"Error processing repository {repo}: {exc}")

# Update the attritions DataFrame with the calculated results
logging.info("Starting to update the attritions DataFrame")
for idx, tenure, commit_percent, commit_count in results_all:
    attritions.loc[attritions['index'] == idx, 'tenure'] = tenure
    attritions.loc[attritions['index'] == idx, 'commit_percent'] = commit_percent
    attritions.loc[attritions['index'] == idx, 'commits'] = commit_count

# Drop the temporary 'index' column
attritions.drop(columns=['index'], inplace=True)

# Save the updated result
logging.info("Saving the updated attritions data to a file")
attritions.to_csv('../result/attritions_updated_add_burst.csv', index=False)
