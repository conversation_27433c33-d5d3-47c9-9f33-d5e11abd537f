{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}], "ref": "b2134d12-c952-4016-a9f0-ec79fb0acd48", "rows": [["0", "yui/yuicompressor", "2011-05-16", "0.0", "1.0", null, "37"], ["1", "yui/yuicompressor", "2011-05-23", "1.0", null, "47.855", "38"], ["2", "yui/yuicompressor", "2011-07-25", "0.0", "1.0", null, "47"], ["3", "yui/yuicompressor", "2011-08-29", "0.0", "1.0", null, "52"], ["4", "yui/yuicompressor", "2011-09-19", "0.0", "1.0", null, "55"], ["5", "yui/yuicompressor", "2011-10-17", "0.0", "1.0", null, "59"], ["6", "yui/yuicompressor", "2012-01-02", "0.0", "1.0", null, "70"], ["7", "yui/yuicompressor", "2012-03-12", "0.0", "1.0", null, "80"], ["8", "yui/yuicompressor", "2012-10-01", "0.0", "1.0", null, "109"], ["9", "yui/yuicompressor", "2012-10-15", "0.0", "1.0", null, "111"], ["10", "yui/yuicompressor", "2012-11-19", "0.0", "1.0", null, "116"], ["11", "yui/yuicompressor", "2013-01-21", "2.0", null, "1561.0693", "125"], ["12", "yui/yuicompressor", "2013-02-11", "1.0", null, "11516.5586", "128"], ["13", "yui/yuicompressor", "2013-03-11", "1.0", "0.75", "8725.9814", "132"], ["14", "yui/yuicompressor", "2013-03-18", "2.0", "0.5", "2123.0378", "133"], ["15", "yui/yuicompressor", "2013-04-08", "4.0", "1.0", "4775.0679", "136"], ["16", "yui/yuicompressor", "2013-04-15", "4.0", "1.0", "3663.2932", "137"], ["17", "yui/yuicompressor", "2013-04-22", "5.0", "1.0", "5970.9692", "138"], ["18", "yui/yuicompressor", "2013-04-29", "1.0", "1.0", "55.05", "139"], ["19", "yui/yuicompressor", "2013-05-06", "3.0", "1.0", "3933.2603", "140"], ["20", "yui/yuicompressor", "2013-05-20", "1.0", null, "405.8814", "142"], ["21", "yui/yuicompressor", "2013-05-27", "0.0", "0.5", null, "143"], ["22", "yui/yuicompressor", "2013-06-10", "1.0", null, "272.2389", "145"], ["23", "yui/yuicompressor", "2013-06-24", "0.0", "1.0", null, "147"], ["24", "yui/yuicompressor", "2013-07-01", "2.0", null, "151.0504", "148"], ["25", "yui/yuicompressor", "2013-09-02", "0.0", "1.0", null, "157"], ["26", "yui/yuicompressor", "2013-09-16", "1.0", "1.0", "1.8606", "159"], ["27", "yui/yuicompressor", "2013-09-30", "0.0", "1.0", null, "161"], ["28", "yui/yuicompressor", "2013-12-02", "0.0", "1.0", null, "170"], ["29", "yui/yuicompressor", "2013-12-16", "0.0", "1.0", null, "172"], ["30", "yui/yuicompressor", "2014-01-27", "1.0", null, "1357.3676", "178"], ["31", "yui/yuicompressor", "2014-02-24", "0.0", "1.0", null, "182"], ["32", "yui/yuicompressor", "2014-03-03", "2.0", null, "1091.5203", "183"], ["33", "yui/yuicompressor", "2014-04-21", "2.0", "1.0", "25.1857", "190"], ["34", "yui/yuicompressor", "2014-05-26", "0.0", "1.0", null, "195"], ["35", "yui/yuicompressor", "2014-06-16", "0.0", "1.0", null, "198"], ["36", "yui/yuicompressor", "2014-06-30", "2.0", null, "11908.5586", "200"], ["37", "yui/yuicompressor", "2014-09-15", "1.0", "1.0", "26.8714", "211"], ["38", "yui/yuicompressor", "2014-11-03", "1.0", "1.0", "4.0528", "218"], ["39", "yui/yuicompressor", "2014-11-10", "3.0", "1.0", "3255.7209", "219"], ["40", "yui/yuicompressor", "2014-12-08", "1.0", "1.0", "100.6225", "223"], ["41", "yui/yuicompressor", "2015-01-05", "1.0", "1.0", "11835.2764", "227"], ["42", "yui/yuicompressor", "2015-03-09", "0.0", "1.0", null, "236"], ["43", "yui/yuicompressor", "2015-03-16", "1.0", null, "118.4372", "237"], ["44", "yui/yuicompressor", "2015-03-23", "3.0", "0.75", "8.0221", "238"], ["45", "yui/yuicompressor", "2015-04-27", "1.0", null, "8088.2432", "243"], ["46", "yui/yuicompressor", "2015-06-15", "2.0", "1.0", "29.3558", "250"], ["47", "yui/yuicompressor", "2015-10-26", "4.0", "1.0", "1784.355", "269"], ["48", "yui/yuicompressor", "2015-11-30", "0.0", "1.0", null, "274"], ["49", "yui/yuicompressor", "2015-12-07", "3.0", "1.0", "31.8201", "275"]], "shape": {"columns": 6, "rows": 5831294}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>standardized_time_weeks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>yui/yuicompressor</td>\n", "      <td>2011-05-16</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>yui/yuicompressor</td>\n", "      <td>2011-05-23</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>47.8550</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>yui/yuicompressor</td>\n", "      <td>2011-07-25</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>yui/yuicompressor</td>\n", "      <td>2011-08-29</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>yui/yuicompressor</td>\n", "      <td>2011-09-19</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5831289</th>\n", "      <td>podman-desktop/podman-desktop</td>\n", "      <td>2025-01-20</td>\n", "      <td>59.0</td>\n", "      <td>0.8056</td>\n", "      <td>107.0320</td>\n", "      <td>751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5831290</th>\n", "      <td>podman-desktop/podman-desktop</td>\n", "      <td>2025-01-27</td>\n", "      <td>58.0</td>\n", "      <td>0.8806</td>\n", "      <td>62.0314</td>\n", "      <td>752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5831291</th>\n", "      <td>podman-desktop/podman-desktop</td>\n", "      <td>2025-02-03</td>\n", "      <td>65.0</td>\n", "      <td>0.8736</td>\n", "      <td>49.7145</td>\n", "      <td>753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5831292</th>\n", "      <td>podman-desktop/podman-desktop</td>\n", "      <td>2025-02-10</td>\n", "      <td>81.0</td>\n", "      <td>0.8333</td>\n", "      <td>104.3540</td>\n", "      <td>754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5831293</th>\n", "      <td>podman-desktop/podman-desktop</td>\n", "      <td>2025-02-17</td>\n", "      <td>62.0</td>\n", "      <td>0.7538</td>\n", "      <td>39.7435</td>\n", "      <td>755</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5831294 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                             repo_name    datetime  pr_throughput  \\\n", "0                    yui/yuicompressor  2011-05-16            0.0   \n", "1                    yui/yuicompressor  2011-05-23            1.0   \n", "2                    yui/yuicompressor  2011-07-25            0.0   \n", "3                    yui/yuicompressor  2011-08-29            0.0   \n", "4                    yui/yuicompressor  2011-09-19            0.0   \n", "...                                ...         ...            ...   \n", "5831289  podman-desktop/podman-desktop  2025-01-20           59.0   \n", "5831290  podman-desktop/podman-desktop  2025-01-27           58.0   \n", "5831291  podman-desktop/podman-desktop  2025-02-03           65.0   \n", "5831292  podman-desktop/podman-desktop  2025-02-10           81.0   \n", "5831293  podman-desktop/podman-desktop  2025-02-17           62.0   \n", "\n", "         pull_request_success_rate  time_to_merge  standardized_time_weeks  \n", "0                           1.0000            NaN                       37  \n", "1                              NaN        47.8550                       38  \n", "2                           1.0000            NaN                       47  \n", "3                           1.0000            NaN                       52  \n", "4                           1.0000            NaN                       55  \n", "...                            ...            ...                      ...  \n", "5831289                     0.8056       107.0320                      751  \n", "5831290                     0.8806        62.0314                      752  \n", "5831291                     0.8736        49.7145                      753  \n", "5831292                     0.8333       104.3540                      754  \n", "5831293                     0.7538        39.7435                      755  \n", "\n", "[5831294 rows x 6 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "productivity = pd.read_csv('../result/productivity_metrics_20250312.csv')\n", "productivity"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earliest datetime: 2010-08-30\n", "Latest datetime: 2025-02-24\n"]}], "source": ["# 展示 datetime最早和最晚的\n", "earliest = productivity['datetime'].min()\n", "latest = productivity['datetime'].max()\n", "print(\"Earliest datetime:\", earliest)\n", "print(\"Latest datetime:\", latest)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_id", "rawType": "object", "type": "string"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "attrition_developer", "rawType": "object", "type": "string"}], "ref": "682de783-74f1-4b36-a8a2-6034d140a2d9", "rows": [["0", "67c03791a55e5dd650be5caa", "01mf02/jaq", "attrition", "2023-04-11", "kammerchorinnsbruck"], ["1", "67c03791a55e5dd650be5cac", "05bit/peewee-async", "attrition", "2020-09-25", "rudyryk"], ["2", "67c03791a55e5dd650be5cac", "05bit/peewee-async", "attrition", "2021-11-02", "rudyryk"], ["3", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-04-14", "bothra90"], ["4", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-08-17", "sunshowers"], ["5", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-08-26", "<PERSON><PERSON><PERSON><PERSON>"], ["6", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-09-18", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["7", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-05", "sblackshear"], ["8", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-05", "vgao1996"], ["9", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-08", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["10", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-14", "wrwg"], ["11", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-14", "mimoo"], ["12", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-14", "ma2bd"], ["13", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-14", "junkil-park"], ["14", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-23", "sausagee"], ["15", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-23", "<PERSON><PERSON><PERSON><PERSON>"], ["16", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-27", "phlip9"], ["17", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-10-30", "da<PERSON><PERSON><PERSON>"], ["18", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-11-03", "bmwill"], ["19", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-11-03", "zekun000"], ["20", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-11-09", "gre<PERSON><PERSON><PERSON>"], ["21", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-11-13", "msmouse"], ["22", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-11-19", "<PERSON><PERSON><PERSON>"], ["23", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2021-12-21", "gre<PERSON><PERSON><PERSON>"], ["24", "67c03791a55e5dd650be5caf", "0b01/tectonicdb", "attrition", "2020-09-08", "0b01"], ["25", "67c03791a55e5dd650be5caf", "0b01/tectonicdb", "attrition", "2021-09-27", "0b01"], ["26", "67c03791a55e5dd650be5cb0", "0b01001001/spectree", "attrition", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["27", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2018-12-08", "sachin-0chain"], ["28", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2019-07-21", "jay-at-0<PERSON><PERSON>"], ["29", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2019-07-31", "siva0chain"], ["30", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2020-11-20", "IntegralTeam"], ["31", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2021-05-02", "Kenwes13"], ["32", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2021-06-11", "bbist"], ["33", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2022-08-09", "ssardana08"], ["34", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2022-10-19", "0xhrsh"], ["35", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2023-06-19", "cnlangzi"], ["36", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2023-07-31", "<PERSON><PERSON>"], ["37", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2023-08-04", "lpoli"], ["38", "67c03791a55e5dd650be5cb2", "0ldsk00l/nestopia", "attrition", "2007-02-20", "rdanbrook"], ["39", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-04-14", "bothra90"], ["40", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-08-17", "sunshowers"], ["41", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-08-26", "<PERSON><PERSON><PERSON><PERSON>"], ["42", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-09-18", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["43", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-05", "sblackshear"], ["44", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-05", "vgao1996"], ["45", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-07", "emmazzz"], ["46", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-08", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["47", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-14", "wrwg"], ["48", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-14", "mimoo"], ["49", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-10-14", "ma2bd"]], "shape": {"columns": 5, "rows": 174887}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_id</th>\n", "      <th>repo_name</th>\n", "      <th>attrition</th>\n", "      <th>attrition_date</th>\n", "      <th>attrition_developer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67c03791a55e5dd650be5caa</td>\n", "      <td>01mf02/jaq</td>\n", "      <td>attrition</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67c03791a55e5dd650be5cac</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>attrition</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67c03791a55e5dd650be5cac</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>attrition</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67c03791a55e5dd650be5cad</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>attrition</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67c03791a55e5dd650be5cad</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>attrition</td>\n", "      <td>2020-08-17</td>\n", "      <td>sunshowers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174882</th>\n", "      <td>67c0379ca55e5dd650bf13b4</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>attrition</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174883</th>\n", "      <td>67c0379ca55e5dd650bf13b4</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>attrition</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>174884</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174885</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174886</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>174887 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                         repo_id                                   repo_name  \\\n", "0       67c03791a55e5dd650be5caa                                  01mf02/jaq   \n", "1       67c03791a55e5dd650be5cac                          05bit/peewee-async   \n", "2       67c03791a55e5dd650be5cac                          05bit/peewee-async   \n", "3       67c03791a55e5dd650be5cad          0LNetworkCommunity/libra-legacy-v6   \n", "4       67c03791a55e5dd650be5cad          0LNetworkCommunity/libra-legacy-v6   \n", "...                          ...                                         ...   \n", "174882  67c0379ca55e5dd650bf13b4  zzzprojects/entityframework.dynamicfilters   \n", "174883  67c0379ca55e5dd650bf13b4  zzzprojects/entityframework.dynamicfilters   \n", "174884  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "174885  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "174886  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "\n", "        attrition attrition_date  attrition_developer  \n", "0       attrition     2023-04-11  kammerchorinnsbruck  \n", "1       attrition     2020-09-25              rudyryk  \n", "2       attrition     2021-11-02              rudyryk  \n", "3       attrition     2020-04-14             bothra90  \n", "4       attrition     2020-08-17           sunshowers  \n", "...           ...            ...                  ...  \n", "174882  attrition     2018-08-13         stgelaisalex  \n", "174883  attrition     2022-10-13       <PERSON><PERSON><PERSON><PERSON>  \n", "174884  attrition     2017-07-31             waqasm78  \n", "174885  attrition     2018-11-09         stgelaisalex  \n", "174886  attrition     2019-10-02             waqasm78  \n", "\n", "[174887 rows x 5 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["attrition = pd.read_csv('../data/attritions_20250227.csv')\n", "attrition"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Earliest attrition date: 1970-01-01\n", "Latest attrition date: 2025-01-31\n"]}], "source": ["# 确认 attrition 最早和最晚的事件\n", "earliest_attrition = attrition['attrition_date'].min()\n", "latest_attrition = attrition['attrition_date'].max()\n", "print(\"Earliest attrition date:\", earliest_attrition)\n", "print(\"Latest attrition date:\", latest_attrition)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["productivity_new = pd.read_csv('../result/did_result_20250312/productivity_20250312_with_propensity_scores.csv')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}], "ref": "6eacbbf4-e8f3-49f5-98e7-b8a0455e2161", "rows": [["0", "01mf02/jaq", "609", "2022-05-08", "0.0", "1.0", null, "418", "3", "6", "Rust", null, null, null, null], ["1", "01mf02/jaq", "610", "2022-05-15", "0.0", null, null, "418", "3", "13", "Rust", null, null, null, null], ["2", "01mf02/jaq", "611", "2022-05-22", "2.0", "1.0", "188.2165", "425", "4", "20", "Rust", null, null, null, null], ["3", "01mf02/jaq", "612", "2022-05-29", "0.0", null, null, "430", "4", "27", "Rust", null, null, null, null], ["4", "01mf02/jaq", "613", "2022-06-05", "0.0", null, null, "430", "4", "34", "Rust", null, null, null, null], ["5", "01mf02/jaq", "614", "2022-06-12", "0.0", null, null, "431", "4", "41", "Rust", null, null, null, null], ["6", "01mf02/jaq", "615", "2022-06-19", "0.0", null, null, "436", "4", "48", "Rust", null, null, null, null], ["7", "01mf02/jaq", "616", "2022-06-26", "1.0", "1.0", "22.9372", "445", "5", "55", "Rust", null, null, null, null], ["8", "01mf02/jaq", "617", "2022-07-03", "0.0", null, null, "452", "5", "62", "Rust", null, null, null, null], ["9", "01mf02/jaq", "618", "2022-07-10", "0.0", null, null, "460", "5", "69", "Rust", null, null, null, null], ["10", "01mf02/jaq", "619", "2022-07-17", "0.0", null, null, "462", "5", "76", "Rust", null, null, null, null], ["11", "01mf02/jaq", "620", "2022-07-24", "1.0", "0.5", "6.7678", "477", "6", "83", "Rust", null, null, null, null], ["12", "01mf02/jaq", "621", "2022-07-31", "0.0", null, null, "488", "6", "90", "Rust", "0.5", null, null, null], ["13", "01mf02/jaq", "622", "2022-08-07", "0.0", "1.0", null, "498", "7", "97", "Rust", "0.5", null, null, null], ["14", "01mf02/jaq", "623", "2022-08-14", "1.0", null, "152.0294", "512", "7", "104", "Rust", "0.4", null, null, null], ["15", "01mf02/jaq", "624", "2022-08-21", "0.0", null, null, "518", "7", "111", "Rust", "0.5", null, null, null], ["16", "01mf02/jaq", "625", "2022-08-28", "0.0", null, null, "518", "7", "118", "Rust", "0.5", null, null, null], ["17", "01mf02/jaq", "626", "2022-09-04", "0.0", null, null, "520", "7", "125", "Rust", "0.5", null, null, null], ["18", "01mf02/jaq", "627", "2022-09-11", "0.0", null, null, "520", "7", "132", "Rust", "0.5", null, null, null], ["19", "01mf02/jaq", "628", "2022-09-18", "0.0", null, null, "526", "7", "139", "Rust", "0.3333333333333333", null, null, null], ["20", "01mf02/jaq", "629", "2022-09-25", "0.0", null, null, "529", "7", "146", "Rust", "0.5", null, null, null], ["21", "01mf02/jaq", "630", "2022-10-02", "0.0", null, null, "538", "7", "153", "Rust", "0.5", null, null, null], ["22", "01mf02/jaq", "631", "2022-10-09", "0.0", null, null, "558", "7", "160", "Rust", "0.5", null, null, null], ["23", "01mf02/jaq", "632", "2022-10-16", "1.0", "1.0", "2.4025", "561", "7", "167", "Rust", "0.5", null, null, null], ["24", "01mf02/jaq", "633", "2022-10-23", "0.0", null, null, "562", "7", "174", "Rust", "0.5", null, null, null], ["25", "01mf02/jaq", "634", "2022-10-30", "0.0", null, null, "571", "7", "181", "Rust", "0.5", null, null, null], ["26", "01mf02/jaq", "635", "2022-11-06", "0.0", null, null, "589", "7", "188", "Rust", "0.3333333333333333", null, null, null], ["27", "01mf02/jaq", "636", "2022-11-13", "0.0", null, null, "608", "7", "195", "Rust", "0.5", null, null, null], ["28", "01mf02/jaq", "637", "2022-11-20", "0.0", null, null, "608", "7", "202", "Rust", "0.5", null, null, null], ["29", "01mf02/jaq", "638", "2022-11-27", "0.0", null, null, "608", "7", "209", "Rust", "0.5", null, null, null], ["30", "01mf02/jaq", "639", "2022-12-04", "0.0", null, null, "608", "7", "216", "Rust", "0.5", null, null, null], ["31", "01mf02/jaq", "640", "2022-12-11", "0.0", null, null, "608", "7", "223", "Rust", "0.5", null, null, null], ["32", "01mf02/jaq", "641", "2022-12-18", "0.0", null, null, "609", "7", "230", "Rust", "0.5", null, null, null], ["33", "01mf02/jaq", "642", "2022-12-25", "0.0", null, null, "621", "7", "237", "Rust", "0.5", null, null, null], ["34", "01mf02/jaq", "643", "2023-01-01", "0.0", null, null, "629", "7", "244", "Rust", "0.5", null, null, null], ["35", "01mf02/jaq", "644", "2023-01-08", "0.0", null, null, "630", "7", "251", "Rust", "0.3333333333333333", null, null, null], ["36", "01mf02/jaq", "645", "2023-01-15", "0.0", null, null, "630", "7", "258", "Rust", "0.5", null, null, null], ["37", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", null, "634", "8", "265", "Rust", "0.5", null, null, null], ["38", "01mf02/jaq", "647", "2023-01-29", "1.0", null, "169.8895", "634", "8", "272", "Rust", "0.6666666666666666", null, null, null], ["39", "01mf02/jaq", "648", "2023-02-05", "0.0", null, null, "634", "8", "279", "Rust", "0.5", null, null, null], ["40", "01mf02/jaq", "649", "2023-02-12", "0.0", null, null, "642", "8", "286", "Rust", "0.5", null, null, null], ["41", "01mf02/jaq", "650", "2023-02-19", "0.0", null, null, "642", "8", "293", "Rust", "0.5", null, null, null], ["42", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6666666666666666", null, null, null], ["43", "01mf02/jaq", "652", "2023-03-05", "0.0", null, null, "645", "9", "307", "Rust", "0.5", null, null, null], ["44", "01mf02/jaq", "653", "2023-03-12", "0.0", null, null, "656", "9", "314", "Rust", "0.5", null, null, null], ["45", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", null, "663", "10", "321", "Rust", "0.5", null, null, null], ["46", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", null, null], ["47", "01mf02/jaq", "656", "2023-04-02", "0.0", null, null, "681", "10", "335", "Rust", "0.5", "0.5", null, null], ["48", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6666666666666666", "0.5", null, null], ["49", "01mf02/jaq", "658", "2023-04-16", "0.0", null, null, "765", "11", "349", "Rust", "0.5", "0.5", null, null]], "shape": {"columns": 14, "rows": 16284290}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>feature_sigmod_12_pr_throughput</th>\n", "      <th>feature_sigmod_12_pull_request_success_rate</th>\n", "      <th>feature_sigmod_12_time_to_merge</th>\n", "      <th>feature_sigmod_12</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>609</td>\n", "      <td>2022-05-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>610</td>\n", "      <td>2022-05-15</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>611</td>\n", "      <td>2022-05-22</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>425</td>\n", "      <td>4</td>\n", "      <td>20</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>612</td>\n", "      <td>2022-05-29</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>613</td>\n", "      <td>2022-06-05</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>34</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284285</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>737</td>\n", "      <td>2024-10-20</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>415</td>\n", "      <td>39</td>\n", "      <td>2687</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284286</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>738</td>\n", "      <td>2024-10-27</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>419</td>\n", "      <td>39</td>\n", "      <td>2694</td>\n", "      <td>C#</td>\n", "      <td>0.333333</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284287</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>739</td>\n", "      <td>2024-11-03</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2701</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284288</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>740</td>\n", "      <td>2024-11-10</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2708</td>\n", "      <td>C#</td>\n", "      <td>0.333333</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284289</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>741</td>\n", "      <td>2024-11-11</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>284.1031</td>\n", "      <td>424</td>\n", "      <td>40</td>\n", "      <td>2709</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16284290 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                              repo_name  standardized_time_weeks    datetime  \\\n", "0                            01mf02/jaq                      609  2022-05-08   \n", "1                            01mf02/jaq                      610  2022-05-15   \n", "2                            01mf02/jaq                      611  2022-05-22   \n", "3                            01mf02/jaq                      612  2022-05-29   \n", "4                            01mf02/jaq                      613  2022-06-05   \n", "...                                 ...                      ...         ...   \n", "16284285  zzzprojects/html-agility-pack                      737  2024-10-20   \n", "16284286  zzzprojects/html-agility-pack                      738  2024-10-27   \n", "16284287  zzzprojects/html-agility-pack                      739  2024-11-03   \n", "16284288  zzzprojects/html-agility-pack                      740  2024-11-10   \n", "16284289  zzzprojects/html-agility-pack                      741  2024-11-11   \n", "\n", "          pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                   0.0                        1.0            NaN   \n", "1                   0.0                        NaN            NaN   \n", "2                   2.0                        1.0       188.2165   \n", "3                   0.0                        NaN            NaN   \n", "4                   0.0                        NaN            NaN   \n", "...                 ...                        ...            ...   \n", "16284285            0.0                        NaN            NaN   \n", "16284286            0.0                        NaN            NaN   \n", "16284287            0.0                        1.0            NaN   \n", "16284288            0.0                        NaN            NaN   \n", "16284289            1.0                        NaN       284.1031   \n", "\n", "          project_commits  project_contributors  project_age mainLanguage  \\\n", "0                     418                     3            6         Rust   \n", "1                     418                     3           13         Rust   \n", "2                     425                     4           20         Rust   \n", "3                     430                     4           27         Rust   \n", "4                     430                     4           34         Rust   \n", "...                   ...                   ...          ...          ...   \n", "16284285              415                    39         2687           C#   \n", "16284286              419                    39         2694           C#   \n", "16284287              421                    40         2701           C#   \n", "16284288              421                    40         2708           C#   \n", "16284289              424                    40         2709           C#   \n", "\n", "          feature_sigmod_12_pr_throughput  \\\n", "0                                     NaN   \n", "1                                     NaN   \n", "2                                     NaN   \n", "3                                     NaN   \n", "4                                     NaN   \n", "...                                   ...   \n", "16284285                         0.500000   \n", "16284286                         0.333333   \n", "16284287                         0.500000   \n", "16284288                         0.333333   \n", "16284289                         0.500000   \n", "\n", "          feature_sigmod_12_pull_request_success_rate  \\\n", "0                                                 NaN   \n", "1                                                 NaN   \n", "2                                                 NaN   \n", "3                                                 NaN   \n", "4                                                 NaN   \n", "...                                               ...   \n", "16284285                                          NaN   \n", "16284286                                          NaN   \n", "16284287                                          NaN   \n", "16284288                                          NaN   \n", "16284289                                          NaN   \n", "\n", "          feature_sigmod_12_time_to_merge  feature_sigmod_12  \n", "0                                     NaN                NaN  \n", "1                                     NaN                NaN  \n", "2                                     NaN                NaN  \n", "3                                     NaN                NaN  \n", "4                                     NaN                NaN  \n", "...                                   ...                ...  \n", "16284285                              1.0                NaN  \n", "16284286                              1.0                NaN  \n", "16284287                              1.0                NaN  \n", "16284288                              1.0                NaN  \n", "16284289                              NaN                NaN  \n", "\n", "[16284290 rows x 14 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["productivity_new"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}], "ref": "4781e583-18a4-4997-8fae-2ac6449e93d0", "rows": [["0", "01mf02/jaq", "609", "2022-05-08", "0.0", "1.0", "188.2165", "418", "3", "6", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["1", "01mf02/jaq", "610", "2022-05-15", "0.0", "1.0", "188.2165", "418", "3", "13", "Rust", null, "0.5", "0.5", null, "0", null, null, null, null, null, null, null], ["2", "01mf02/jaq", "611", "2022-05-22", "2.0", "1.0", "188.2165", "425", "4", "20", "Rust", null, "0.5", "0.5", null, "0", null, null, null, null, null, null, null], ["3", "01mf02/jaq", "612", "2022-05-29", "0.0", "1.0", "188.2165", "430", "4", "27", "Rust", null, "0.5", "0.5", null, "0", null, null, null, null, null, null, null], ["4", "01mf02/jaq", "613", "2022-06-05", "0.0", "1.0", "188.2165", "430", "4", "34", "Rust", null, "0.5", "0.5", null, "0", null, null, null, null, null, null, null], ["5", "01mf02/jaq", "614", "2022-06-12", "0.0", "1.0", "188.2165", "431", "4", "41", "Rust", null, "0.5", "0.5", null, "0", null, null, null, null, null, null, null], ["6", "01mf02/jaq", "615", "2022-06-19", "0.0", "1.0", "188.2165", "436", "4", "48", "Rust", null, "0.5", "0.5", null, "0", null, null, null, null, null, null, null], ["7", "01mf02/jaq", "616", "2022-06-26", "1.0", "1.0", "22.9372", "445", "5", "55", "Rust", null, "0.5", "0.2935646179560138", null, "0", null, null, null, null, null, null, null], ["8", "01mf02/jaq", "617", "2022-07-03", "0.0", "1.0", "22.9372", "452", "5", "62", "Rust", null, "0.5", "0.2935646179560138", null, "0", null, null, null, null, null, null, null], ["9", "01mf02/jaq", "618", "2022-07-10", "0.0", "1.0", "22.9372", "460", "5", "69", "Rust", null, "0.5", "0.2935646179560138", null, "0", null, null, null, null, null, null, null], ["10", "01mf02/jaq", "619", "2022-07-17", "0.0", "1.0", "22.9372", "462", "5", "76", "Rust", null, "0.5", "0.2935646179560138", null, "0", null, null, null, null, null, null, null], ["11", "01mf02/jaq", "620", "2022-07-24", "1.0", "0.5", "6.7678", "477", "6", "83", "Rust", null, "0.3775406687981454", "0.1703602656140834", null, "0", null, null, null, null, null, null, null], ["12", "01mf02/jaq", "621", "2022-07-31", "0.0", "0.5", "6.7678", "488", "6", "90", "Rust", "0.5", "0.3775406687981454", "0.1703602656140834", "0.1107526447766344", "0", null, null, null, null, null, null, null], ["13", "01mf02/jaq", "622", "2022-08-07", "0.0", "1.0", "6.7678", "498", "7", "97", "Rust", "0.5", "0.5", "0.1703602656140834", "0.1703602656140834", "0", null, null, null, null, null, null, null], ["14", "01mf02/jaq", "623", "2022-08-14", "1.0", "1.0", "152.0294", "512", "7", "104", "Rust", "0.4", "0.5", "0.999999997677358", "0.9999999965160368", "0", null, null, null, null, null, null, null], ["15", "01mf02/jaq", "624", "2022-08-21", "0.0", "1.0", "152.0294", "518", "7", "111", "Rust", "0.5", "0.5", "0.999999997677358", "0.999999997677358", "0", null, null, null, null, null, null, null], ["16", "01mf02/jaq", "625", "2022-08-28", "0.0", "1.0", "152.0294", "518", "7", "118", "Rust", "0.5", "0.5", "0.999999997677358", "0.999999997677358", "0", null, null, null, null, null, null, null], ["17", "01mf02/jaq", "626", "2022-09-04", "0.0", "1.0", "152.0294", "520", "7", "125", "Rust", "0.5", "0.5", "0.999999997677358", "0.999999997677358", "0", null, null, null, null, null, null, null], ["18", "01mf02/jaq", "627", "2022-09-11", "0.0", "1.0", "152.0294", "520", "7", "132", "Rust", "0.5", "0.5", "0.999999997677358", "0.999999997677358", "0", null, null, null, null, null, null, null], ["19", "01mf02/jaq", "628", "2022-09-18", "0.0", "1.0", "152.0294", "526", "7", "139", "Rust", "0.3333333333333333", "0.5", "0.9999999990348084", "0.9999999980696168", "0", null, null, null, null, null, null, null], ["20", "01mf02/jaq", "629", "2022-09-25", "0.0", "1.0", "152.0294", "529", "7", "146", "Rust", "0.5", "0.5", "0.9999999990348084", "0.9999999990348084", "0", null, null, null, null, null, null, null], ["21", "01mf02/jaq", "630", "2022-10-02", "0.0", "1.0", "152.0294", "538", "7", "153", "Rust", "0.5", "0.5", "0.9999999990348084", "0.9999999990348084", "0", null, null, null, null, null, null, null], ["22", "01mf02/jaq", "631", "2022-10-09", "0.0", "1.0", "152.0294", "558", "7", "160", "Rust", "0.5", "0.5", "0.9999999990348084", "0.9999999990348084", "0", null, null, null, null, null, null, null], ["23", "01mf02/jaq", "632", "2022-10-16", "1.0", "1.0", "2.4025", "561", "7", "167", "Rust", "0.5", "0.6224593312018546", "0.9999999987238772", "0.9999999992259924", "0", null, null, null, null, null, null, null], ["24", "01mf02/jaq", "633", "2022-10-23", "0.0", "1.0", "2.4025", "562", "7", "174", "Rust", "0.5", "0.6224593312018546", "0.9999999987238772", "0.9999999992259924", "0", null, null, null, null, null, null, null], ["25", "01mf02/jaq", "634", "2022-10-30", "0.0", "1.0", "2.4025", "571", "7", "181", "Rust", "0.5", "0.5", "0.9999999987238772", "0.9999999987238772", "0", null, null, null, null, null, null, null], ["26", "01mf02/jaq", "635", "2022-11-06", "0.0", "1.0", "2.4025", "589", "7", "188", "Rust", "0.3333333333333333", "0.5", "0.2720597745194635", "0.1574474455236461", "0", null, null, null, null, null, null, null], ["27", "01mf02/jaq", "636", "2022-11-13", "0.0", "1.0", "2.4025", "608", "7", "195", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["28", "01mf02/jaq", "637", "2022-11-20", "0.0", "1.0", "2.4025", "608", "7", "202", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["29", "01mf02/jaq", "638", "2022-11-27", "0.0", "1.0", "2.4025", "608", "7", "209", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["30", "01mf02/jaq", "639", "2022-12-04", "0.0", "1.0", "2.4025", "608", "7", "216", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["31", "01mf02/jaq", "640", "2022-12-11", "0.0", "1.0", "2.4025", "608", "7", "223", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["32", "01mf02/jaq", "641", "2022-12-18", "0.0", "1.0", "2.4025", "609", "7", "230", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["33", "01mf02/jaq", "642", "2022-12-25", "0.0", "1.0", "2.4025", "621", "7", "237", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["34", "01mf02/jaq", "643", "2023-01-01", "0.0", "1.0", "2.4025", "629", "7", "244", "Rust", "0.5", "0.5", "0.2720597745194635", "0.2720597745194635", "0", null, null, null, null, null, null, null], ["35", "01mf02/jaq", "644", "2023-01-08", "0.0", "1.0", "2.4025", "630", "7", "251", "Rust", "0.3333333333333333", "0.5", "0.5", "0.3333333333333333", "0", null, null, null, null, null, null, null], ["36", "01mf02/jaq", "645", "2023-01-15", "0.0", "1.0", "2.4025", "630", "7", "258", "Rust", "0.5", "0.5", "0.5", "0.5", "0", null, null, null, null, null, null, null], ["37", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", "2.4025", "634", "8", "265", "Rust", "0.5", "0.5", "0.5", "0.5", "0", null, null, null, null, null, null, null], ["38", "01mf02/jaq", "647", "2023-01-29", "1.0", "1.0", "169.8895", "634", "8", "272", "Rust", "0.6666666666666666", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["39", "01mf02/jaq", "648", "2023-02-05", "0.0", "1.0", "169.8895", "634", "8", "279", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["40", "01mf02/jaq", "649", "2023-02-12", "0.0", "1.0", "169.8895", "642", "8", "286", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["41", "01mf02/jaq", "650", "2023-02-19", "0.0", "1.0", "169.8895", "642", "8", "293", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["42", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6666666666666666", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["43", "01mf02/jaq", "652", "2023-03-05", "0.0", "1.0", "18.3589", "645", "9", "307", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["44", "01mf02/jaq", "653", "2023-03-12", "0.0", "1.0", "18.3589", "656", "9", "314", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["45", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", "18.3589", "663", "10", "321", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["46", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["47", "01mf02/jaq", "656", "2023-04-02", "0.0", "1.0", "25.7632", "681", "10", "335", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["48", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6666666666666666", "0.5", "1.0", "1.0", "0", null, null, null, null, null, null, null], ["49", "01mf02/jaq", "658", "2023-04-16", "0.0", "1.0", "14.9636", "765", "11", "349", "Rust", "0.5", "0.5", "1.0", "1.0", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "decelerating", "15.0"]], "shape": {"columns": 22, "rows": 16284290}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>feature_sigmod_12_time_to_merge</th>\n", "      <th>feature_sigmod_12</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>609</td>\n", "      <td>2022-05-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>610</td>\n", "      <td>2022-05-15</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>611</td>\n", "      <td>2022-05-22</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>425</td>\n", "      <td>4</td>\n", "      <td>20</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>612</td>\n", "      <td>2022-05-29</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>613</td>\n", "      <td>2022-06-05</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>34</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284285</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>737</td>\n", "      <td>2024-10-20</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>128.8108</td>\n", "      <td>415</td>\n", "      <td>39</td>\n", "      <td>2687</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284286</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>738</td>\n", "      <td>2024-10-27</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>128.8108</td>\n", "      <td>419</td>\n", "      <td>39</td>\n", "      <td>2694</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284287</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>739</td>\n", "      <td>2024-11-03</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>128.8108</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2701</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284288</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>740</td>\n", "      <td>2024-11-10</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>128.8108</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2708</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284289</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>741</td>\n", "      <td>2024-11-11</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>284.1031</td>\n", "      <td>424</td>\n", "      <td>40</td>\n", "      <td>2709</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>0.769517</td>\n", "      <td>0.769517</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16284290 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                              repo_name  standardized_time_weeks    datetime  \\\n", "0                            01mf02/jaq                      609  2022-05-08   \n", "1                            01mf02/jaq                      610  2022-05-15   \n", "2                            01mf02/jaq                      611  2022-05-22   \n", "3                            01mf02/jaq                      612  2022-05-29   \n", "4                            01mf02/jaq                      613  2022-06-05   \n", "...                                 ...                      ...         ...   \n", "16284285  zzzprojects/html-agility-pack                      737  2024-10-20   \n", "16284286  zzzprojects/html-agility-pack                      738  2024-10-27   \n", "16284287  zzzprojects/html-agility-pack                      739  2024-11-03   \n", "16284288  zzzprojects/html-agility-pack                      740  2024-11-10   \n", "16284289  zzzprojects/html-agility-pack                      741  2024-11-11   \n", "\n", "          pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                   0.0                        1.0       188.2165   \n", "1                   0.0                        1.0       188.2165   \n", "2                   2.0                        1.0       188.2165   \n", "3                   0.0                        1.0       188.2165   \n", "4                   0.0                        1.0       188.2165   \n", "...                 ...                        ...            ...   \n", "16284285            0.0                        1.0       128.8108   \n", "16284286            0.0                        1.0       128.8108   \n", "16284287            0.0                        1.0       128.8108   \n", "16284288            0.0                        1.0       128.8108   \n", "16284289            1.0                        1.0       284.1031   \n", "\n", "          project_commits  project_contributors  project_age mainLanguage  \\\n", "0                     418                     3            6         Rust   \n", "1                     418                     3           13         Rust   \n", "2                     425                     4           20         Rust   \n", "3                     430                     4           27         Rust   \n", "4                     430                     4           34         Rust   \n", "...                   ...                   ...          ...          ...   \n", "16284285              415                    39         2687           C#   \n", "16284286              419                    39         2694           C#   \n", "16284287              421                    40         2701           C#   \n", "16284288              421                    40         2708           C#   \n", "16284289              424                    40         2709           C#   \n", "\n", "          ...  feature_sigmod_12_time_to_merge  feature_sigmod_12  \\\n", "0         ...                              NaN                NaN   \n", "1         ...                         0.500000                NaN   \n", "2         ...                         0.500000                NaN   \n", "3         ...                         0.500000                NaN   \n", "4         ...                         0.500000                NaN   \n", "...       ...                              ...                ...   \n", "16284285  ...                         1.000000           1.000000   \n", "16284286  ...                         1.000000           1.000000   \n", "16284287  ...                         1.000000           1.000000   \n", "16284288  ...                         1.000000           1.000000   \n", "16284289  ...                         0.769517           0.769517   \n", "\n", "          someone_left  tenure  commit_percent  commits  burst  \\\n", "0                    0     NaN             NaN      NaN    NaN   \n", "1                    0     NaN             NaN      NaN    NaN   \n", "2                    0     NaN             NaN      NaN    NaN   \n", "3                    0     NaN             NaN      NaN    NaN   \n", "4                    0     NaN             NaN      NaN    NaN   \n", "...                ...     ...             ...      ...    ...   \n", "16284285             0     NaN             NaN      NaN    NaN   \n", "16284286             0     NaN             NaN      NaN    NaN   \n", "16284287             0     NaN             NaN      NaN    NaN   \n", "16284288             0     NaN             NaN      NaN    NaN   \n", "16284289             0     NaN             NaN      NaN    NaN   \n", "\n", "          attrition_count  growth_phase  newcomers  \n", "0                     NaN           NaN        NaN  \n", "1                     NaN           NaN        NaN  \n", "2                     NaN           NaN        NaN  \n", "3                     NaN           NaN        NaN  \n", "4                     NaN           NaN        NaN  \n", "...                   ...           ...        ...  \n", "16284285              NaN           NaN        NaN  \n", "16284286              NaN           NaN        NaN  \n", "16284287              NaN           NaN        NaN  \n", "16284288              NaN           NaN        NaN  \n", "16284289              NaN           NaN        NaN  \n", "\n", "[16284290 rows x 22 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["productivity_new_with_control = pd.read_csv('../result/20250629_did_result/productivity_with_propensity_scores_with_attritions_450.csv')\n", "productivity_new_with_control"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "growth_phase", "rawType": "object", "type": "string"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "float64", "type": "float"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}], "ref": "15c157f1-7105-496a-a040-5aa7670e3344", "rows": [["0", "030/n3dr", "700", "2024-02-04", "14.0", "0.9165", "230.8593", "644", "9", "1749", "Go", "0.9375", "0.532", "0.9995", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-12", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.709", "0.6504", "6.4692503167957724", "2.3025851", "7.467371", "5.4461308", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["1", "030/n3dr", "701", "2024-02-11", "4.0", "0.5713", "27.5549", "645", "9", "1756", "Go", "0.8335", "0.446", "0.9985", "0.9995", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-11", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.609", "0.452", "6.470799503782602", "2.3025851", "7.471363", "3.3518286", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["2", "030/n3dr", "702", "2024-02-18", "0.0", "0.25", null, "645", "9", "1763", "Go", "0.5", "0.3687", "0.9985", "0.9976", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-10", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", "0.2231", "6.470799503782602", "2.3025851", "7.4753394", null, "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["3", "030/n3dr", "703", "2024-02-25", "7.0", "0.5835", "35.5835", "649", "9", "1770", "Go", "0.727", "0.449", "0.9985", "0.9995", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-9", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.08", "0.4597", "6.476972362889683", "2.3025851", "7.4792995", "3.5995972", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["4", "030/n3dr", "704", "2024-03-03", "7.0", "0.857", "76.5479", "649", "9", "1777", "Go", "0.4211", "0.452", "1.0", "0.9995", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-8", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.08", "0.619", "6.476972362889683", "2.3025851", "7.4832444", "4.350896", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["5", "030/n3dr", "705", "2024-03-10", "8.0", "0.4443", "45.6349", "650", "9", "1784", "Go", "0.9", "0.5127", "0.9995", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-7", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.197", "0.3677", "6.478509642208569", "2.3025851", "7.4871736", "3.8423493", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["6", "030/n3dr", "706", "2024-03-17", "4.0", "0.6", "0.2668", "654", "9", "1791", "Go", "0.5", "0.41", "0.999", "0.9985", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-6", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.609", "0.47", "6.484635235635252", "2.3025851", "7.4910874", "0.23649403", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["7", "030/n3dr", "707", "2024-03-24", "4.0", "1.0", "155.28", "654", "9", "1798", "Go", "0.5", "0.4792", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-5", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.609", "0.6934", "6.484635235635252", "2.3025851", "7.494986", "5.051649", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["8", "030/n3dr", "708", "2024-03-31", "0.0", null, null, "654", "9", "1805", "Go", "0.25", "0.521", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-4", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.484635235635252", "2.3025851", "7.49887", null, "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["9", "030/n3dr", "709", "2024-04-07", "0.0", "0.5", null, "654", "9", "1812", "Go", "0.5", "0.521", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-3", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", "0.4055", "6.484635235635252", "2.3025851", "7.502738", null, "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["10", "030/n3dr", "710", "2024-04-14", "0.0", "1.0", null, "654", "9", "1819", "Go", "0.1428", "0.6416", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-2", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", "0.6934", "6.484635235635252", "2.3025851", "7.506592", null, "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["11", "030/n3dr", "711", "2024-04-21", "14.0", "1.0", "536.1216", "657", "9", "1826", "Go", "0.9375", "0.6416", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-1", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.709", "0.6934", "6.489204931325317", "2.3025851", "7.5104303", "6.2862244", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["12", "030/n3dr", "712", "2024-04-28", "5.0", "0.5557", "16.0346", "658", "9", "1833", "Go", "0.2856", "0.5347", "1.0", "1.0", "1", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "0", "1", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.792", "0.442", "6.490723534502507", "2.3025851", "7.5142546", "2.8352466", "0.0", "0_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["13", "030/n3dr", "713", "2024-05-05", "0.0", null, null, "658", "9", "1840", "Go", "0.1666", "0.6187", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "1", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.518064", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["14", "030/n3dr", "714", "2024-05-12", "0.0", null, null, "658", "9", "1847", "Go", "0.5", "0.6914", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "2", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.521859", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["15", "030/n3dr", "715", "2024-05-19", "0.0", null, null, "658", "9", "1854", "Go", "0.1111", "0.6157", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "3", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.52564", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["16", "030/n3dr", "716", "2024-05-26", "0.0", null, null, "658", "9", "1861", "Go", "0.1111", "0.5493", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "4", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.5294065", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["17", "030/n3dr", "717", "2024-06-02", "0.0", null, null, "658", "9", "1868", "Go", "0.1", "0.6484", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "5", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.533159", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["18", "030/n3dr", "718", "2024-06-09", "0.0", null, null, "658", "9", "1875", "Go", "0.1666", "0.612", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "6", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.536897", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["19", "030/n3dr", "719", "2024-06-16", "0.0", null, null, "658", "9", "1882", "Go", "0.1666", "0.5137", "0.275", "0.0742", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "7", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.5406218", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["20", "030/n3dr", "720", "2024-06-23", "0.0", null, null, "658", "9", "1889", "Go", "0.5", "0.5137", "0.275", "0.2861", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "8", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.544332", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["21", "030/n3dr", "721", "2024-06-30", "0.0", null, null, "658", "9", "1896", "Go", "0.5", "0.5137", "0.275", "0.2861", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "9", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.548029", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["22", "030/n3dr", "722", "2024-07-07", "0.0", null, null, "658", "9", "1903", "Go", "0.5", "0.3906", "0.275", "0.1956", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "10", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.551712", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["23", "030/n3dr", "723", "2024-07-14", "0.0", null, null, "658", "9", "1910", "Go", "0.0625", "0.3906", "0.275", "0.01595", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "11", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.555382", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["24", "030/n3dr", "724", "2024-07-21", "0.0", null, null, "658", "9", "1917", "Go", "0.1428", null, null, null, "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "12", "1", "1.0", "0", "1", "1", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "6.490723534502507", "2.3025851", "7.559038", null, "0.0", "1_0", "1_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["25", "0no-co/gql.tada", "697", "2024-01-14", "1.0", "1.0", "3.04", "143", "2", "62", "TypeScript", null, "0.6226", "0.335", null, "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-12", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.6934", "0.6934", "4.969813299576001", "1.0986123", "4.1431346", "1.3962446", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["26", "0no-co/gql.tada", "698", "2024-01-21", "14.0", "0.933", "2.316", "167", "4", "69", "TypeScript", null, "0.6064", "0.2842", null, "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-11", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.709", "0.659", "5.123963979403259", "1.609438", "4.248495", "1.1987592", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["27", "0no-co/gql.tada", "699", "2024-01-28", "10.0", "0.909", "1.0555", "179", "4", "76", "TypeScript", null, "0.601", "0.1873", null, "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-10", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.398", "0.6465", "5.19295685089021", "1.609438", "4.3438053", "0.7205191", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["28", "0no-co/gql.tada", "700", "2024-02-04", "3.0", "0.75", "0.5686", "181", "4", "83", "TypeScript", null, "0.562", "0.1268", null, "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-9", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.387", "0.5596", "5.204006687076795", "1.609438", "4.4308167", "0.4501835", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["29", "0no-co/gql.tada", "701", "2024-02-11", "0.0", null, null, "181", "4", "90", "TypeScript", "0.3333", "0.562", "0.1268", "0.08527", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-8", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "5.204006687076795", "1.609438", "4.5108595", null, "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["30", "0no-co/gql.tada", "702", "2024-02-18", "0.0", null, null, "181", "4", "97", "TypeScript", "0.1428", "0.4377", "0.2238", "0.03607", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-7", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "5.204006687076795", "1.609438", "4.5849676", null, "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["31", "0no-co/gql.tada", "703", "2024-02-25", "7.0", "1.0", "2.0214", "191", "4", "104", "TypeScript", "0.8887", "0.4377", "0.2238", "0.6426", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-6", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.08", "0.6934", "5.2574953720277815", "1.609438", "4.65396", "1.1057203", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["32", "0no-co/gql.tada", "704", "2024-03-03", "7.0", "1.0", "5.1794", "198", "6", "111", "TypeScript", "0.8887", "0.4377", "0.579", "0.8955", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-5", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.08", "0.6934", "5.293304824724492", "1.9459102", "4.7184987", "1.8212211", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["33", "0no-co/gql.tada", "705", "2024-03-10", "13.0", "0.857", "29.1971", "209", "8", "118", "TypeScript", "0.933", "0.403", "0.993", "0.999", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-4", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.639", "0.619", "5.3471075307174685", "2.1972246", "4.7791233", "3.4077458", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["34", "0no-co/gql.tada", "706", "2024-03-17", "10.0", "1.0", "6.8409", "218", "10", "125", "TypeScript", "0.9165", "0.4377", "0.985", "0.998", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-3", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.398", "0.6934", "5.389071729816501", "2.3978953", "4.836282", "2.0593536", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["35", "0no-co/gql.tada", "707", "2024-03-24", "14.0", "0.875", "4.1575", "230", "10", "132", "TypeScript", "0.9375", "0.4072", "0.978", "0.998", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-2", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.709", "0.6284", "5.442417710521793", "2.3978953", "4.890349", "1.6404519", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["36", "0no-co/gql.tada", "708", "2024-03-31", "3.0", "1.0", "0.7", "235", "10", "139", "TypeScript", "0.8", "0.4377", "0.951", "0.984", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "-1", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.387", "0.6934", "5.4638318050256105", "2.3978953", "4.9416423", "0.5306282", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["37", "0no-co/gql.tada", "709", "2024-04-07", "7.0", "1.0", "35.8853", "242", "10", "146", "TypeScript", "0.8", "0.4377", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "0", "0", "0.0", "0", "0", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.08", "0.6934", "5.493061443340548", "2.3978953", "4.9904327", "3.6078131", "0.0", "0_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["38", "0no-co/gql.tada", "710", "2024-04-14", "11.0", "0.9233", "15.7361", "254", "10", "153", "TypeScript", "0.4443", "0.4353", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "1", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.484", "0.6543", "5.541263545158426", "2.3978953", "5.0369525", "2.817568", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["39", "0no-co/gql.tada", "711", "2024-04-21", "28.0", "0.931", "19.4072", "280", "10", "160", "TypeScript", "0.725", "0.443", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "2", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "3.367", "0.658", "5.638354669333745", "2.3978953", "5.081404", "3.0158877", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["40", "0no-co/gql.tada", "712", "2024-04-28", "29.0", "1.0", "5.5767", "313", "10", "167", "TypeScript", "0.8823", "0.5", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "3", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "3.4", "0.6934", "5.749392985908253", "2.3978953", "5.123964", "1.8835331", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["41", "0no-co/gql.tada", "713", "2024-05-05", "6.0", "0.857", "8.2081", "318", "12", "174", "TypeScript", "0.875", "0.4644", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "4", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.946", "0.619", "5.765191102784844", "2.5649493", "5.164786", "2.2200835", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["42", "0no-co/gql.tada", "714", "2024-05-12", "1.0", "0.3333", "0.3808", "319", "13", "181", "TypeScript", "0.6665", "0.3394", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "5", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.6934", "0.2876", "5.768320995793772", "2.6390574", "5.2040067", "0.32266304", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["43", "0no-co/gql.tada", "715", "2024-05-19", "12.0", "1.0", "0.8528", "326", "13", "188", "TypeScript", "0.619", "0.5", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "6", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "2.564", "0.6934", "5.7899601708972535", "2.6390574", "5.241747", "0.616698", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["44", "0no-co/gql.tada", "716", "2024-05-26", "0.0", null, null, "326", "13", "195", "TypeScript", "0.1111", "0.5", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "7", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "5.7899601708972535", "2.6390574", "5.278115", null, "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["45", "0no-co/gql.tada", "717", "2024-06-02", "0.0", null, null, "326", "13", "202", "TypeScript", "0.06665", "0.5356", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "8", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "5.7899601708972535", "2.6390574", "5.313206", null, "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["46", "0no-co/gql.tada", "718", "2024-06-09", "0.0", null, null, "326", "13", "209", "TypeScript", "0.0833", "0.5", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "9", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", null, "5.7899601708972535", "2.6390574", "5.3471074", null, "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["47", "0no-co/gql.tada", "719", "2024-06-16", "3.0", "1.0", "6.9449", "329", "13", "216", "TypeScript", "0.2106", "0.5312", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "10", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.387", "0.6934", "5.799092654460526", "2.6390574", "5.379897", "2.0725303", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["48", "0no-co/gql.tada", "720", "2024-06-23", "0.0", "1.0", null, "329", "13", "223", "TypeScript", "0.2", "0.5", "1.0", "1.0", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "11", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "0.0", "0.6934", "5.799092654460526", "2.6390574", "5.411646", null, "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"], ["49", "0no-co/gql.tada", "721", "2024-06-30", "4.0", "1.0", "834.9471", "332", "13", "230", "TypeScript", "0.3845", "0.5", "0.4297", "0.32", "0", "1828.0", "0.94873046875", "628.0", "accelerating", "0.0", "12", "0", "1.0", "0", "1", "0", "7.511524648390866", "0.6671781188893355", "6.444131256700441", "1.609", "0.6934", "5.808142489980444", "2.6390574", "5.4424176", "6.728565", "0.0", "1_0", "0_0", "6.490723534502507", "2.3025851249694824", "7.514254570007324", "Go"]], "shape": {"columns": 42, "rows": 7583439}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>log_project_contributors</th>\n", "      <th>log_project_age</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_newcomers</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>030/n3dr</td>\n", "      <td>700</td>\n", "      <td>2024-02-04</td>\n", "      <td>14.0</td>\n", "      <td>0.9165</td>\n", "      <td>230.8593</td>\n", "      <td>644</td>\n", "      <td>9</td>\n", "      <td>1749</td>\n", "      <td>Go</td>\n", "      <td>...</td>\n", "      <td>2.302585</td>\n", "      <td>7.467371</td>\n", "      <td>5.446131</td>\n", "      <td>0.0</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.490724</td>\n", "      <td>2.302585</td>\n", "      <td>7.514255</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>030/n3dr</td>\n", "      <td>701</td>\n", "      <td>2024-02-11</td>\n", "      <td>4.0</td>\n", "      <td>0.5713</td>\n", "      <td>27.5549</td>\n", "      <td>645</td>\n", "      <td>9</td>\n", "      <td>1756</td>\n", "      <td>Go</td>\n", "      <td>...</td>\n", "      <td>2.302585</td>\n", "      <td>7.471363</td>\n", "      <td>3.351829</td>\n", "      <td>0.0</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.490724</td>\n", "      <td>2.302585</td>\n", "      <td>7.514255</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>030/n3dr</td>\n", "      <td>702</td>\n", "      <td>2024-02-18</td>\n", "      <td>0.0</td>\n", "      <td>0.2500</td>\n", "      <td>NaN</td>\n", "      <td>645</td>\n", "      <td>9</td>\n", "      <td>1763</td>\n", "      <td>Go</td>\n", "      <td>...</td>\n", "      <td>2.302585</td>\n", "      <td>7.475339</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.490724</td>\n", "      <td>2.302585</td>\n", "      <td>7.514255</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>030/n3dr</td>\n", "      <td>703</td>\n", "      <td>2024-02-25</td>\n", "      <td>7.0</td>\n", "      <td>0.5835</td>\n", "      <td>35.5835</td>\n", "      <td>649</td>\n", "      <td>9</td>\n", "      <td>1770</td>\n", "      <td>Go</td>\n", "      <td>...</td>\n", "      <td>2.302585</td>\n", "      <td>7.479299</td>\n", "      <td>3.599597</td>\n", "      <td>0.0</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.490724</td>\n", "      <td>2.302585</td>\n", "      <td>7.514255</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>030/n3dr</td>\n", "      <td>704</td>\n", "      <td>2024-03-03</td>\n", "      <td>7.0</td>\n", "      <td>0.8570</td>\n", "      <td>76.5479</td>\n", "      <td>649</td>\n", "      <td>9</td>\n", "      <td>1777</td>\n", "      <td>Go</td>\n", "      <td>...</td>\n", "      <td>2.302585</td>\n", "      <td>7.483244</td>\n", "      <td>4.350896</td>\n", "      <td>0.0</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.490724</td>\n", "      <td>2.302585</td>\n", "      <td>7.514255</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7583434</th>\n", "      <td>datadog/documentation</td>\n", "      <td>476</td>\n", "      <td>2019-10-20</td>\n", "      <td>54.0</td>\n", "      <td>0.9287</td>\n", "      <td>65.4059</td>\n", "      <td>12667</td>\n", "      <td>325</td>\n", "      <td>2603</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>5.786897</td>\n", "      <td>7.864804</td>\n", "      <td>4.195786</td>\n", "      <td>0.0</td>\n", "      <td>1_52846</td>\n", "      <td>0_52846</td>\n", "      <td>6.539586</td>\n", "      <td>2.890372</td>\n", "      <td>7.039660</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7583435</th>\n", "      <td>datadog/documentation</td>\n", "      <td>477</td>\n", "      <td>2019-10-27</td>\n", "      <td>46.0</td>\n", "      <td>0.9790</td>\n", "      <td>38.8253</td>\n", "      <td>12761</td>\n", "      <td>327</td>\n", "      <td>2610</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>5.793014</td>\n", "      <td>7.867488</td>\n", "      <td>3.684502</td>\n", "      <td>0.0</td>\n", "      <td>1_52846</td>\n", "      <td>0_52846</td>\n", "      <td>6.539586</td>\n", "      <td>2.890372</td>\n", "      <td>7.039660</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7583436</th>\n", "      <td>datadog/documentation</td>\n", "      <td>478</td>\n", "      <td>2019-11-03</td>\n", "      <td>40.0</td>\n", "      <td>0.9020</td>\n", "      <td>61.8150</td>\n", "      <td>12910</td>\n", "      <td>330</td>\n", "      <td>2617</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>5.802118</td>\n", "      <td>7.870166</td>\n", "      <td>4.140194</td>\n", "      <td>0.0</td>\n", "      <td>1_52846</td>\n", "      <td>0_52846</td>\n", "      <td>6.539586</td>\n", "      <td>2.890372</td>\n", "      <td>7.039660</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7583437</th>\n", "      <td>datadog/documentation</td>\n", "      <td>479</td>\n", "      <td>2019-11-10</td>\n", "      <td>70.0</td>\n", "      <td>0.8390</td>\n", "      <td>56.2586</td>\n", "      <td>13040</td>\n", "      <td>336</td>\n", "      <td>2624</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>5.820083</td>\n", "      <td>7.872836</td>\n", "      <td>4.047578</td>\n", "      <td>0.0</td>\n", "      <td>1_52846</td>\n", "      <td>0_52846</td>\n", "      <td>6.539586</td>\n", "      <td>2.890372</td>\n", "      <td>7.039660</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7583438</th>\n", "      <td>datadog/documentation</td>\n", "      <td>480</td>\n", "      <td>2019-11-17</td>\n", "      <td>57.0</td>\n", "      <td>0.8955</td>\n", "      <td>40.4634</td>\n", "      <td>13140</td>\n", "      <td>339</td>\n", "      <td>2631</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>5.828946</td>\n", "      <td>7.875499</td>\n", "      <td>3.724811</td>\n", "      <td>0.0</td>\n", "      <td>1_52846</td>\n", "      <td>0_52846</td>\n", "      <td>6.539586</td>\n", "      <td>2.890372</td>\n", "      <td>7.039660</td>\n", "      <td>C#</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7583439 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                     repo_name  standardized_time_weeks    datetime  \\\n", "0                     030/n3dr                      700  2024-02-04   \n", "1                     030/n3dr                      701  2024-02-11   \n", "2                     030/n3dr                      702  2024-02-18   \n", "3                     030/n3dr                      703  2024-02-25   \n", "4                     030/n3dr                      704  2024-03-03   \n", "...                        ...                      ...         ...   \n", "7583434  datadog/documentation                      476  2019-10-20   \n", "7583435  datadog/documentation                      477  2019-10-27   \n", "7583436  datadog/documentation                      478  2019-11-03   \n", "7583437  datadog/documentation                      479  2019-11-10   \n", "7583438  datadog/documentation                      480  2019-11-17   \n", "\n", "         pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                 14.0                     0.9165       230.8593   \n", "1                  4.0                     0.5713        27.5549   \n", "2                  0.0                     0.2500            NaN   \n", "3                  7.0                     0.5835        35.5835   \n", "4                  7.0                     0.8570        76.5479   \n", "...                ...                        ...            ...   \n", "7583434           54.0                     0.9287        65.4059   \n", "7583435           46.0                     0.9790        38.8253   \n", "7583436           40.0                     0.9020        61.8150   \n", "7583437           70.0                     0.8390        56.2586   \n", "7583438           57.0                     0.8955        40.4634   \n", "\n", "         project_commits  project_contributors  project_age mainLanguage  ...  \\\n", "0                    644                     9         1749           Go  ...   \n", "1                    645                     9         1756           Go  ...   \n", "2                    645                     9         1763           Go  ...   \n", "3                    649                     9         1770           Go  ...   \n", "4                    649                     9         1777           Go  ...   \n", "...                  ...                   ...          ...          ...  ...   \n", "7583434            12667                   325         2603   JavaScript  ...   \n", "7583435            12761                   327         2610   JavaScript  ...   \n", "7583436            12910                   330         2617   JavaScript  ...   \n", "7583437            13040                   336         2624   JavaScript  ...   \n", "7583438            13140                   339         2631   JavaScript  ...   \n", "\n", "         log_project_contributors  log_project_age  log_time_to_merge  \\\n", "0                        2.302585         7.467371           5.446131   \n", "1                        2.302585         7.471363           3.351829   \n", "2                        2.302585         7.475339                NaN   \n", "3                        2.302585         7.479299           3.599597   \n", "4                        2.302585         7.483244           4.350896   \n", "...                           ...              ...                ...   \n", "7583434                  5.786897         7.864804           4.195786   \n", "7583435                  5.793014         7.867488           3.684502   \n", "7583436                  5.802118         7.870166           4.140194   \n", "7583437                  5.820083         7.872836           4.047578   \n", "7583438                  5.828946         7.875499           3.724811   \n", "\n", "         log_newcomers  time_cohort_effect  repo_cohort_effect  \\\n", "0                  0.0                 0_0                 1_0   \n", "1                  0.0                 0_0                 1_0   \n", "2                  0.0                 0_0                 1_0   \n", "3                  0.0                 0_0                 1_0   \n", "4                  0.0                 0_0                 1_0   \n", "...                ...                 ...                 ...   \n", "7583434            0.0             1_52846             0_52846   \n", "7583435            0.0             1_52846             0_52846   \n", "7583436            0.0             1_52846             0_52846   \n", "7583437            0.0             1_52846             0_52846   \n", "7583438            0.0             1_52846             0_52846   \n", "\n", "         log_project_commits_before_treatment  \\\n", "0                                    6.490724   \n", "1                                    6.490724   \n", "2                                    6.490724   \n", "3                                    6.490724   \n", "4                                    6.490724   \n", "...                                       ...   \n", "7583434                              6.539586   \n", "7583435                              6.539586   \n", "7583436                              6.539586   \n", "7583437                              6.539586   \n", "7583438                              6.539586   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         2.302585   \n", "1                                         2.302585   \n", "2                                         2.302585   \n", "3                                         2.302585   \n", "4                                         2.302585   \n", "...                                            ...   \n", "7583434                                   2.890372   \n", "7583435                                   2.890372   \n", "7583436                                   2.890372   \n", "7583437                                   2.890372   \n", "7583438                                   2.890372   \n", "\n", "        log_project_age_before_treatment  project_main_language  \n", "0                               7.514255                     Go  \n", "1                               7.514255                     Go  \n", "2                               7.514255                     Go  \n", "3                               7.514255                     Go  \n", "4                               7.514255                     Go  \n", "...                                  ...                    ...  \n", "7583434                         7.039660                     C#  \n", "7583435                         7.039660                     C#  \n", "7583436                         7.039660                     C#  \n", "7583437                         7.039660                     C#  \n", "7583438                         7.039660                     C#  \n", "\n", "[7583439 rows x 42 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["did_source_data = pd.read_csv(\"../result/20250629_did_result/compiled_data_test_limit180_processed.csv\")\n", "did_source_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "growth_phase", "rawType": "object", "type": "string"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "float64", "type": "float"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_project_newcomers", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}], "ref": "f7d246fe-4e81-4e49-a546-2338ddfb5cc6", "rows": [["0", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", "2.4025", "634", "8", "265", "Rust", "0.5", "0.5", "0.5", "0.5", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-12", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.453624998892692", "2.1972246", "5.583496", "1.2245104", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["1", "01mf02/jaq", "647", "2023-01-29", "1.0", "1.0", "169.8895", "634", "8", "272", "Rust", "0.6665", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-11", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.6934", "6.453624998892692", "2.1972246", "5.609472", "5.141017", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["2", "01mf02/jaq", "648", "2023-02-05", "0.0", "1.0", "169.8895", "634", "8", "279", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-10", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.453624998892692", "2.1972246", "5.6347895", "5.141017", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["3", "01mf02/jaq", "649", "2023-02-12", "0.0", "1.0", "169.8895", "642", "8", "286", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-9", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.466144724237619", "2.1972246", "5.659482", "5.141017", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["4", "01mf02/jaq", "650", "2023-02-19", "0.0", "1.0", "169.8895", "642", "8", "293", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-8", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.466144724237619", "2.1972246", "5.68358", "5.141017", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["5", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6665", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-7", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.6934", "6.4692503167957724", "2.3025851", "5.7071104", "2.9631522", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["6", "01mf02/jaq", "652", "2023-03-05", "0.0", "1.0", "18.3589", "645", "9", "307", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-6", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.470799503782602", "2.3025851", "5.7300997", "2.9631522", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["7", "01mf02/jaq", "653", "2023-03-12", "0.0", "1.0", "18.3589", "656", "9", "314", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-5", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.48768401848461", "2.3025851", "5.7525725", "2.9631522", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["8", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", "18.3589", "663", "10", "321", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-4", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.498282149476434", "2.3978953", "5.7745514", "2.9631522", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["9", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-3", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "1.099", "0.6934", "6.519147287940395", "2.3978953", "5.7960577", "3.2870278", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["10", "01mf02/jaq", "656", "2023-04-02", "0.0", "1.0", "25.7632", "681", "10", "335", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-2", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.525029657843462", "2.3978953", "5.817111", "3.2870278", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["11", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6665", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-1", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.6934", "6.580639137284949", "2.4849067", "5.8377304", "2.770311", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["12", "01mf02/jaq", "658", "2023-04-16", "0.0", "1.0", "14.9636", "765", "11", "349", "Rust", "0.5", "0.5", "1.0", "1.0", "1", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "0", "1", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.641182169740591", "2.4849067", "5.857933", "2.770311", "2.772588722239781", "0_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["13", "01mf02/jaq", "659", "2023-04-23", "0.0", "1.0", "14.9636", "768", "12", "356", "Rust", "0.3333", "0.5", "0.2874", "0.1678", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "1", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.8777356", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["14", "01mf02/jaq", "660", "2023-04-30", "0.0", "1.0", "14.9636", "768", "12", "363", "Rust", "0.5", "0.5", "0.2874", "0.2874", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "2", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.897154", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["15", "01mf02/jaq", "661", "2023-05-07", "0.0", "1.0", "14.9636", "768", "12", "370", "Rust", "0.5", "0.5", "0.2874", "0.2874", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "3", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.916202", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["16", "01mf02/jaq", "662", "2023-05-14", "0.0", "1.0", "14.9636", "768", "12", "377", "Rust", "0.5", "0.5", "0.2874", "0.2874", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "4", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.934894", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["17", "01mf02/jaq", "663", "2023-05-21", "0.0", "1.0", "14.9636", "768", "12", "384", "Rust", "0.3333", "0.5", "0.496", "0.3298", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "5", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.9532433", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["18", "01mf02/jaq", "664", "2023-05-28", "0.0", "1.0", "14.9636", "768", "12", "391", "Rust", "0.5", "0.5", "0.496", "0.496", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "6", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.971262", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["19", "01mf02/jaq", "665", "2023-06-04", "0.0", "1.0", "14.9636", "768", "12", "398", "Rust", "0.5", "0.5", "0.496", "0.496", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "7", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "5.988961", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["20", "01mf02/jaq", "666", "2023-06-11", "0.0", "1.0", "14.9636", "768", "12", "405", "Rust", "0.5", "0.5", "0.496", "0.496", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "8", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.645090969505644", "2.5649493", "6.0063534", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["21", "01mf02/jaq", "667", "2023-06-18", "0.0", "1.0", "14.9636", "769", "13", "412", "Rust", "0.25", "0.5", "0.3967", "0.1798", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "9", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.646390514847729", "2.6390574", "6.0234475", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["22", "01mf02/jaq", "668", "2023-06-25", "0.0", "1.0", "14.9636", "777", "13", "419", "Rust", "0.5", "0.5", "0.3967", "0.3967", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "10", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "6.656726524178391", "2.6390574", "6.0402546", "2.770311", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["23", "01mf02/jaq", "669", "2023-07-02", "7.0", "1.0", "361.1883", "799", "14", "426", "Rust", "0.8", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "11", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "2.08", "0.6934", "6.684611727667927", "2.7080503", "6.056784", "5.892164", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["24", "01mf02/jaq", "670", "2023-07-09", "1.0", "1.0", "351.6383", "825", "14", "433", "Rust", "0.6665", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "12", "1", "1.0", "0", "1", "1", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.6934", "6.716594773520978", "2.7080503", "6.073045", "5.8654428", "2.772588722239781", "1_0", "1_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["25", "0LNetworkCommunity/libra-legacy-v6", "645", "2023-01-15", "0.0", "1.0", "204.1507", "7008", "158", "972", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-12", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "6.880384", "5.323745", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["26", "0LNetworkCommunity/libra-legacy-v6", "646", "2023-01-22", "0.0", "1.0", "204.1507", "7008", "158", "979", "Rust", "0.5", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-11", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "6.8875527", "5.323745", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["27", "0LNetworkCommunity/libra-legacy-v6", "647", "2023-01-29", "2.0", "0.3333", "634.6174", "7008", "158", "986", "Rust", "0.75", "0.3394", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-10", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "1.099", "0.2876", "8.854950316500325", "5.0689044", "6.89467", "6.454597", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["28", "0LNetworkCommunity/libra-legacy-v6", "648", "2023-02-05", "1.0", "0.3333", "6061.3735", "7008", "158", "993", "Rust", "0.4", "0.521", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-9", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.2876", "8.854950316500325", "5.0689044", "6.901737", "8.709857", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["29", "0LNetworkCommunity/libra-legacy-v6", "649", "2023-02-12", "3.0", "0.3333", "239.1875", "7008", "158", "1000", "Rust", "0.8", "0.521", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-8", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "1.387", "0.2876", "8.854950316500325", "5.0689044", "6.908755", "5.48142", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["30", "0LNetworkCommunity/libra-legacy-v6", "650", "2023-02-19", "1.0", "1.0", "0.0275", "7008", "158", "1007", "Rust", "0.6665", "0.679", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-7", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.6934", "8.854950316500325", "5.0689044", "6.9157233", "0.027128667", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["31", "0LNetworkCommunity/libra-legacy-v6", "651", "2023-02-26", "1.0", "0.3333", "258.8997", "7008", "158", "1014", "Rust", "0.6665", "0.3394", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-6", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.2876", "8.854950316500325", "5.0689044", "6.922644", "5.5602956", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["32", "0LNetworkCommunity/libra-legacy-v6", "652", "2023-03-05", "0.0", "0.3333", "258.8997", "7008", "158", "1021", "Rust", "0.5", "0.3394", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-5", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.2876", "8.854950316500325", "5.0689044", "6.929517", "5.5602956", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["33", "0LNetworkCommunity/libra-legacy-v6", "653", "2023-03-12", "0.0", "0.3333", "258.8997", "7008", "158", "1028", "Rust", "0.5", "0.3394", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-4", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.2876", "8.854950316500325", "5.0689044", "6.9363427", "5.5602956", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["34", "0LNetworkCommunity/libra-legacy-v6", "654", "2023-03-19", "0.0", "0.3333", "258.8997", "7008", "158", "1035", "Rust", "0.25", "0.4585", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-3", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.2876", "8.854950316500325", "5.0689044", "6.9431224", "5.5602956", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["35", "0LNetworkCommunity/libra-legacy-v6", "655", "2023-03-26", "1.0", "0.5", "564.5242", "7008", "158", "1042", "Rust", "0.6665", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-2", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.4055", "8.854950316500325", "5.0689044", "6.9498563", "6.337753", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["36", "0LNetworkCommunity/libra-legacy-v6", "656", "2023-04-02", "0.0", "0.5", "564.5242", "7008", "158", "1049", "Rust", "0.5", "0.3774", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "-1", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.4055", "8.854950316500325", "5.0689044", "6.9565454", "6.337753", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["37", "0LNetworkCommunity/libra-legacy-v6", "657", "2023-04-09", "2.0", "1.0", "170.8628", "7008", "158", "1056", "Rust", "0.75", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "0", "0", "0.0", "0", "0", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "1.099", "0.6934", "8.854950316500325", "5.0689044", "6.96319", "5.1466966", "2.772588722239781", "0_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["38", "0LNetworkCommunity/libra-legacy-v6", "658", "2023-04-16", "1.0", "1.0", "1378.7114", "7008", "158", "1063", "Rust", "0.6665", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "1", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.6934", "0.6934", "8.854950316500325", "5.0689044", "6.9697905", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["39", "0LNetworkCommunity/libra-legacy-v6", "659", "2023-04-23", "0.0", "1.0", "1378.7114", "7008", "158", "1070", "Rust", "0.25", "0.6606", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "2", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "6.976348", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["40", "0LNetworkCommunity/libra-legacy-v6", "660", "2023-04-30", "0.0", "1.0", "1378.7114", "7008", "158", "1077", "Rust", "0.3333", "0.6606", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "3", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "6.982863", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["41", "0LNetworkCommunity/libra-legacy-v6", "661", "2023-05-07", "0.0", "1.0", "1378.7114", "7008", "158", "1084", "Rust", "0.2", "0.6606", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "4", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "6.989335", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["42", "0LNetworkCommunity/libra-legacy-v6", "662", "2023-05-14", "0.0", "1.0", "1378.7114", "7008", "158", "1091", "Rust", "0.3333", "0.5", "1.0", "1.0", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "5", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "6.995766", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["43", "0LNetworkCommunity/libra-legacy-v6", "663", "2023-05-21", "0.0", "1.0", "1378.7114", "7008", "158", "1098", "Rust", "0.3333", "0.6606", "0.9995", "0.9995", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "6", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.002156", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["44", "0LNetworkCommunity/libra-legacy-v6", "664", "2023-05-28", "0.0", "1.0", "1378.7114", "7008", "158", "1105", "Rust", "0.5", "0.6606", "0.9995", "0.9995", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "7", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.0085053", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["45", "0LNetworkCommunity/libra-legacy-v6", "665", "2023-06-04", "0.0", "1.0", "1378.7114", "7008", "158", "1112", "Rust", "0.5", "0.6606", "0.9995", "0.9995", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "8", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.0148144", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["46", "0LNetworkCommunity/libra-legacy-v6", "666", "2023-06-11", "0.0", "1.0", "1378.7114", "7008", "158", "1119", "Rust", "0.5", "0.6606", "0.9995", "0.9995", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "9", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.021084", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["47", "0LNetworkCommunity/libra-legacy-v6", "667", "2023-06-18", "0.0", "1.0", "1378.7114", "7008", "158", "1126", "Rust", "0.3333", "0.6226", "0.9985", "0.998", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "10", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.0273147", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["48", "0LNetworkCommunity/libra-legacy-v6", "668", "2023-06-25", "0.0", "1.0", "1378.7114", "7008", "158", "1133", "Rust", "0.5", "0.6226", "0.9985", "0.999", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "11", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.0335064", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"], ["49", "0LNetworkCommunity/libra-legacy-v6", "669", "2023-07-02", "0.0", "1.0", "1378.7114", "7008", "158", "1140", "Rust", "0.25", "0.5", "0.999", "0.9976", "0", "854.0", "0.1558837890625", "228.0", "decelerating", "15.0", "12", "0", "1.0", "0", "1", "0", "6.75110146893676", "0.1448652367042111", "5.43372200355424", "0.0", "0.6934", "8.854950316500325", "5.0689044", "7.0396605", "7.2296295", "2.772588722239781", "1_0", "0_0", "6.641182169740591", "2.4849066734313965", "5.857933044433594", "Rust"]], "shape": {"columns": 42, "rows": 8126970}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>log_project_contributors</th>\n", "      <th>log_project_age</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_project_newcomers</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>2023-01-22</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>2.4025</td>\n", "      <td>634</td>\n", "      <td>8</td>\n", "      <td>265</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>2.197225</td>\n", "      <td>5.583496</td>\n", "      <td>1.224510</td>\n", "      <td>2.772589</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.857933</td>\n", "      <td>Rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>2023-01-29</td>\n", "      <td>1.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>634</td>\n", "      <td>8</td>\n", "      <td>272</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>2.197225</td>\n", "      <td>5.609472</td>\n", "      <td>5.141017</td>\n", "      <td>2.772589</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.857933</td>\n", "      <td>Rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>2023-02-05</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>634</td>\n", "      <td>8</td>\n", "      <td>279</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>2.197225</td>\n", "      <td>5.634790</td>\n", "      <td>5.141017</td>\n", "      <td>2.772589</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.857933</td>\n", "      <td>Rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>2023-02-12</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>642</td>\n", "      <td>8</td>\n", "      <td>286</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>2.197225</td>\n", "      <td>5.659482</td>\n", "      <td>5.141017</td>\n", "      <td>2.772589</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.857933</td>\n", "      <td>Rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>2023-02-19</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>642</td>\n", "      <td>8</td>\n", "      <td>293</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>2.197225</td>\n", "      <td>5.683580</td>\n", "      <td>5.141017</td>\n", "      <td>2.772589</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.857933</td>\n", "      <td>Rust</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8126965</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>484</td>\n", "      <td>2019-12-15</td>\n", "      <td>20.0</td>\n", "      <td>0.8184</td>\n", "      <td>41.3989</td>\n", "      <td>3723</td>\n", "      <td>177</td>\n", "      <td>2113</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>5.181784</td>\n", "      <td>7.656337</td>\n", "      <td>3.747122</td>\n", "      <td>0.693147</td>\n", "      <td>1_57776</td>\n", "      <td>0_57776</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8126966</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>485</td>\n", "      <td>2019-12-22</td>\n", "      <td>4.0</td>\n", "      <td>0.8000</td>\n", "      <td>34.0408</td>\n", "      <td>3733</td>\n", "      <td>178</td>\n", "      <td>2120</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>5.187386</td>\n", "      <td>7.659643</td>\n", "      <td>3.556513</td>\n", "      <td>0.693147</td>\n", "      <td>1_57776</td>\n", "      <td>0_57776</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8126967</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>486</td>\n", "      <td>2019-12-29</td>\n", "      <td>0.0</td>\n", "      <td>0.8000</td>\n", "      <td>34.0408</td>\n", "      <td>3733</td>\n", "      <td>178</td>\n", "      <td>2127</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>5.187386</td>\n", "      <td>7.662938</td>\n", "      <td>3.556513</td>\n", "      <td>0.693147</td>\n", "      <td>1_57776</td>\n", "      <td>0_57776</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8126968</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>487</td>\n", "      <td>2020-01-05</td>\n", "      <td>0.0</td>\n", "      <td>0.5000</td>\n", "      <td>34.0408</td>\n", "      <td>3740</td>\n", "      <td>180</td>\n", "      <td>2134</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>5.198497</td>\n", "      <td>7.666222</td>\n", "      <td>3.556513</td>\n", "      <td>0.693147</td>\n", "      <td>1_57776</td>\n", "      <td>0_57776</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8126969</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>488</td>\n", "      <td>2020-01-12</td>\n", "      <td>42.0</td>\n", "      <td>0.8887</td>\n", "      <td>244.1081</td>\n", "      <td>3765</td>\n", "      <td>180</td>\n", "      <td>2141</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>5.198497</td>\n", "      <td>7.669495</td>\n", "      <td>5.501699</td>\n", "      <td>0.693147</td>\n", "      <td>1_57776</td>\n", "      <td>0_57776</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8126970 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                 repo_name  standardized_time_weeks    datetime  \\\n", "0               01mf02/jaq                      646  2023-01-22   \n", "1               01mf02/jaq                      647  2023-01-29   \n", "2               01mf02/jaq                      648  2023-02-05   \n", "3               01mf02/jaq                      649  2023-02-12   \n", "4               01mf02/jaq                      650  2023-02-19   \n", "...                    ...                      ...         ...   \n", "8126965  ceph/ceph-ansible                      484  2019-12-15   \n", "8126966  ceph/ceph-ansible                      485  2019-12-22   \n", "8126967  ceph/ceph-ansible                      486  2019-12-29   \n", "8126968  ceph/ceph-ansible                      487  2020-01-05   \n", "8126969  ceph/ceph-ansible                      488  2020-01-12   \n", "\n", "         pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                  0.0                     1.0000         2.4025   \n", "1                  1.0                     1.0000       169.8895   \n", "2                  0.0                     1.0000       169.8895   \n", "3                  0.0                     1.0000       169.8895   \n", "4                  0.0                     1.0000       169.8895   \n", "...                ...                        ...            ...   \n", "8126965           20.0                     0.8184        41.3989   \n", "8126966            4.0                     0.8000        34.0408   \n", "8126967            0.0                     0.8000        34.0408   \n", "8126968            0.0                     0.5000        34.0408   \n", "8126969           42.0                     0.8887       244.1081   \n", "\n", "         project_commits  project_contributors  project_age mainLanguage  ...  \\\n", "0                    634                     8          265         Rust  ...   \n", "1                    634                     8          272         Rust  ...   \n", "2                    634                     8          279         Rust  ...   \n", "3                    642                     8          286         Rust  ...   \n", "4                    642                     8          293         Rust  ...   \n", "...                  ...                   ...          ...          ...  ...   \n", "8126965             3723                   177         2113       Python  ...   \n", "8126966             3733                   178         2120       Python  ...   \n", "8126967             3733                   178         2127       Python  ...   \n", "8126968             3740                   180         2134       Python  ...   \n", "8126969             3765                   180         2141       Python  ...   \n", "\n", "         log_project_contributors  log_project_age  log_time_to_merge  \\\n", "0                        2.197225         5.583496           1.224510   \n", "1                        2.197225         5.609472           5.141017   \n", "2                        2.197225         5.634790           5.141017   \n", "3                        2.197225         5.659482           5.141017   \n", "4                        2.197225         5.683580           5.141017   \n", "...                           ...              ...                ...   \n", "8126965                  5.181784         7.656337           3.747122   \n", "8126966                  5.187386         7.659643           3.556513   \n", "8126967                  5.187386         7.662938           3.556513   \n", "8126968                  5.198497         7.666222           3.556513   \n", "8126969                  5.198497         7.669495           5.501699   \n", "\n", "         log_project_newcomers  time_cohort_effect  repo_cohort_effect  \\\n", "0                     2.772589                 0_0                 1_0   \n", "1                     2.772589                 0_0                 1_0   \n", "2                     2.772589                 0_0                 1_0   \n", "3                     2.772589                 0_0                 1_0   \n", "4                     2.772589                 0_0                 1_0   \n", "...                        ...                 ...                 ...   \n", "8126965               0.693147             1_57776             0_57776   \n", "8126966               0.693147             1_57776             0_57776   \n", "8126967               0.693147             1_57776             0_57776   \n", "8126968               0.693147             1_57776             0_57776   \n", "8126969               0.693147             1_57776             0_57776   \n", "\n", "         log_project_commits_before_treatment  \\\n", "0                                    6.641182   \n", "1                                    6.641182   \n", "2                                    6.641182   \n", "3                                    6.641182   \n", "4                                    6.641182   \n", "...                                       ...   \n", "8126965                              5.521461   \n", "8126966                              5.521461   \n", "8126967                              5.521461   \n", "8126968                              5.521461   \n", "8126969                              5.521461   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         2.484907   \n", "1                                         2.484907   \n", "2                                         2.484907   \n", "3                                         2.484907   \n", "4                                         2.484907   \n", "...                                            ...   \n", "8126965                                   3.295837   \n", "8126966                                   3.295837   \n", "8126967                                   3.295837   \n", "8126968                                   3.295837   \n", "8126969                                   3.295837   \n", "\n", "        log_project_age_before_treatment  project_main_language  \n", "0                               5.857933                   Rust  \n", "1                               5.857933                   Rust  \n", "2                               5.857933                   Rust  \n", "3                               5.857933                   Rust  \n", "4                               5.857933                   Rust  \n", "...                                  ...                    ...  \n", "8126965                         6.741701                     C#  \n", "8126966                         6.741701                     C#  \n", "8126967                         6.741701                     C#  \n", "8126968                         6.741701                     C#  \n", "8126969                         6.741701                     C#  \n", "\n", "[8126970 rows x 42 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["did_data = pd.read_csv(\"../result/20250629_did_result/compiled_data_test_limit450_processed.csv\", engine='c')\n", "did_data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}], "ref": "8823c7b4-6b94-4fb3-ad77-eec72893468d", "rows": [["0", "01mf02/jaq", "609", "2022-05-08", "0.0", "1.0", null, "418", "3", "6", "Rust", null, null, null, null, "0", null, null, null, null, null], ["1", "01mf02/jaq", "610", "2022-05-15", "0.0", null, null, "418", "3", "13", "Rust", null, null, null, null, "0", null, null, null, null, null], ["2", "01mf02/jaq", "611", "2022-05-22", "2.0", "1.0", "188.2165", "425", "4", "20", "Rust", null, null, null, null, "0", null, null, null, null, null], ["3", "01mf02/jaq", "612", "2022-05-29", "0.0", null, null, "430", "4", "27", "Rust", null, null, null, null, "0", null, null, null, null, null], ["4", "01mf02/jaq", "613", "2022-06-05", "0.0", null, null, "430", "4", "34", "Rust", null, null, null, null, "0", null, null, null, null, null], ["5", "01mf02/jaq", "614", "2022-06-12", "0.0", null, null, "431", "4", "41", "Rust", null, null, null, null, "0", null, null, null, null, null], ["6", "01mf02/jaq", "615", "2022-06-19", "0.0", null, null, "436", "4", "48", "Rust", null, null, null, null, "0", null, null, null, null, null], ["7", "01mf02/jaq", "616", "2022-06-26", "1.0", "1.0", "22.9372", "445", "5", "55", "Rust", null, null, null, null, "0", null, null, null, null, null], ["8", "01mf02/jaq", "617", "2022-07-03", "0.0", null, null, "452", "5", "62", "Rust", null, null, null, null, "0", null, null, null, null, null], ["9", "01mf02/jaq", "618", "2022-07-10", "0.0", null, null, "460", "5", "69", "Rust", null, null, null, null, "0", null, null, null, null, null], ["10", "01mf02/jaq", "619", "2022-07-17", "0.0", null, null, "462", "5", "76", "Rust", null, null, null, null, "0", null, null, null, null, null], ["11", "01mf02/jaq", "620", "2022-07-24", "1.0", "0.5", "6.7678", "477", "6", "83", "Rust", null, null, null, null, "0", null, null, null, null, null], ["12", "01mf02/jaq", "621", "2022-07-31", "0.0", null, null, "488", "6", "90", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["13", "01mf02/jaq", "622", "2022-08-07", "0.0", "1.0", null, "498", "7", "97", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["14", "01mf02/jaq", "623", "2022-08-14", "1.0", null, "152.0294", "512", "7", "104", "Rust", "0.4", null, null, null, "0", null, null, null, null, null], ["15", "01mf02/jaq", "624", "2022-08-21", "0.0", null, null, "518", "7", "111", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["16", "01mf02/jaq", "625", "2022-08-28", "0.0", null, null, "518", "7", "118", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["17", "01mf02/jaq", "626", "2022-09-04", "0.0", null, null, "520", "7", "125", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["18", "01mf02/jaq", "627", "2022-09-11", "0.0", null, null, "520", "7", "132", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["19", "01mf02/jaq", "628", "2022-09-18", "0.0", null, null, "526", "7", "139", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null], ["20", "01mf02/jaq", "629", "2022-09-25", "0.0", null, null, "529", "7", "146", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["21", "01mf02/jaq", "630", "2022-10-02", "0.0", null, null, "538", "7", "153", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["22", "01mf02/jaq", "631", "2022-10-09", "0.0", null, null, "558", "7", "160", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["23", "01mf02/jaq", "632", "2022-10-16", "1.0", "1.0", "2.4025", "561", "7", "167", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["24", "01mf02/jaq", "633", "2022-10-23", "0.0", null, null, "562", "7", "174", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["25", "01mf02/jaq", "634", "2022-10-30", "0.0", null, null, "571", "7", "181", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["26", "01mf02/jaq", "635", "2022-11-06", "0.0", null, null, "589", "7", "188", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null], ["27", "01mf02/jaq", "636", "2022-11-13", "0.0", null, null, "608", "7", "195", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["28", "01mf02/jaq", "637", "2022-11-20", "0.0", null, null, "608", "7", "202", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["29", "01mf02/jaq", "638", "2022-11-27", "0.0", null, null, "608", "7", "209", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["30", "01mf02/jaq", "639", "2022-12-04", "0.0", null, null, "608", "7", "216", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["31", "01mf02/jaq", "640", "2022-12-11", "0.0", null, null, "608", "7", "223", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["32", "01mf02/jaq", "641", "2022-12-18", "0.0", null, null, "609", "7", "230", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["33", "01mf02/jaq", "642", "2022-12-25", "0.0", null, null, "621", "7", "237", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["34", "01mf02/jaq", "643", "2023-01-01", "0.0", null, null, "629", "7", "244", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["35", "01mf02/jaq", "644", "2023-01-08", "0.0", null, null, "630", "7", "251", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null], ["36", "01mf02/jaq", "645", "2023-01-15", "0.0", null, null, "630", "7", "258", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["37", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", null, "634", "8", "265", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["38", "01mf02/jaq", "647", "2023-01-29", "1.0", null, "169.8895", "634", "8", "272", "Rust", "0.6666666666666666", null, null, null, "0", null, null, null, null, null], ["39", "01mf02/jaq", "648", "2023-02-05", "0.0", null, null, "634", "8", "279", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["40", "01mf02/jaq", "649", "2023-02-12", "0.0", null, null, "642", "8", "286", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["41", "01mf02/jaq", "650", "2023-02-19", "0.0", null, null, "642", "8", "293", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["42", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6666666666666666", null, null, null, "0", null, null, null, null, null], ["43", "01mf02/jaq", "652", "2023-03-05", "0.0", null, null, "645", "9", "307", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["44", "01mf02/jaq", "653", "2023-03-12", "0.0", null, null, "656", "9", "314", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["45", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", null, "663", "10", "321", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["46", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", null, null, "0", null, null, null, null, null], ["47", "01mf02/jaq", "656", "2023-04-02", "0.0", null, null, "681", "10", "335", "Rust", "0.5", "0.5", null, null, "0", null, null, null, null, null], ["48", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6666666666666666", "0.5", null, null, "0", null, null, null, null, null], ["49", "01mf02/jaq", "658", "2023-04-16", "0.0", null, null, "765", "11", "349", "Rust", "0.5", "0.5", null, null, "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0"]], "shape": {"columns": 20, "rows": 16284290}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>feature_sigmod_12_pr_throughput</th>\n", "      <th>feature_sigmod_12_pull_request_success_rate</th>\n", "      <th>feature_sigmod_12_time_to_merge</th>\n", "      <th>feature_sigmod_12</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>609</td>\n", "      <td>2022-05-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>610</td>\n", "      <td>2022-05-15</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>611</td>\n", "      <td>2022-05-22</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>425</td>\n", "      <td>4</td>\n", "      <td>20</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>612</td>\n", "      <td>2022-05-29</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>613</td>\n", "      <td>2022-06-05</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>34</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284285</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>737</td>\n", "      <td>2024-10-20</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>415</td>\n", "      <td>39</td>\n", "      <td>2687</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284286</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>738</td>\n", "      <td>2024-10-27</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>419</td>\n", "      <td>39</td>\n", "      <td>2694</td>\n", "      <td>C#</td>\n", "      <td>0.333333</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284287</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>739</td>\n", "      <td>2024-11-03</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2701</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284288</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>740</td>\n", "      <td>2024-11-10</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2708</td>\n", "      <td>C#</td>\n", "      <td>0.333333</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284289</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>741</td>\n", "      <td>2024-11-11</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>284.1031</td>\n", "      <td>424</td>\n", "      <td>40</td>\n", "      <td>2709</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16284290 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                              repo_name  standardized_time_weeks    datetime  \\\n", "0                            01mf02/jaq                      609  2022-05-08   \n", "1                            01mf02/jaq                      610  2022-05-15   \n", "2                            01mf02/jaq                      611  2022-05-22   \n", "3                            01mf02/jaq                      612  2022-05-29   \n", "4                            01mf02/jaq                      613  2022-06-05   \n", "...                                 ...                      ...         ...   \n", "16284285  zzzprojects/html-agility-pack                      737  2024-10-20   \n", "16284286  zzzprojects/html-agility-pack                      738  2024-10-27   \n", "16284287  zzzprojects/html-agility-pack                      739  2024-11-03   \n", "16284288  zzzprojects/html-agility-pack                      740  2024-11-10   \n", "16284289  zzzprojects/html-agility-pack                      741  2024-11-11   \n", "\n", "          pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                   0.0                        1.0            NaN   \n", "1                   0.0                        NaN            NaN   \n", "2                   2.0                        1.0       188.2165   \n", "3                   0.0                        NaN            NaN   \n", "4                   0.0                        NaN            NaN   \n", "...                 ...                        ...            ...   \n", "16284285            0.0                        NaN            NaN   \n", "16284286            0.0                        NaN            NaN   \n", "16284287            0.0                        1.0            NaN   \n", "16284288            0.0                        NaN            NaN   \n", "16284289            1.0                        NaN       284.1031   \n", "\n", "          project_commits  project_contributors  project_age mainLanguage  \\\n", "0                     418                     3            6         Rust   \n", "1                     418                     3           13         Rust   \n", "2                     425                     4           20         Rust   \n", "3                     430                     4           27         Rust   \n", "4                     430                     4           34         Rust   \n", "...                   ...                   ...          ...          ...   \n", "16284285              415                    39         2687           C#   \n", "16284286              419                    39         2694           C#   \n", "16284287              421                    40         2701           C#   \n", "16284288              421                    40         2708           C#   \n", "16284289              424                    40         2709           C#   \n", "\n", "          feature_sigmod_12_pr_throughput  \\\n", "0                                     NaN   \n", "1                                     NaN   \n", "2                                     NaN   \n", "3                                     NaN   \n", "4                                     NaN   \n", "...                                   ...   \n", "16284285                         0.500000   \n", "16284286                         0.333333   \n", "16284287                         0.500000   \n", "16284288                         0.333333   \n", "16284289                         0.500000   \n", "\n", "          feature_sigmod_12_pull_request_success_rate  \\\n", "0                                                 NaN   \n", "1                                                 NaN   \n", "2                                                 NaN   \n", "3                                                 NaN   \n", "4                                                 NaN   \n", "...                                               ...   \n", "16284285                                          NaN   \n", "16284286                                          NaN   \n", "16284287                                          NaN   \n", "16284288                                          NaN   \n", "16284289                                          NaN   \n", "\n", "          feature_sigmod_12_time_to_merge  feature_sigmod_12  someone_left  \\\n", "0                                     NaN                NaN             0   \n", "1                                     NaN                NaN             0   \n", "2                                     NaN                NaN             0   \n", "3                                     NaN                NaN             0   \n", "4                                     NaN                NaN             0   \n", "...                                   ...                ...           ...   \n", "16284285                              1.0                NaN             0   \n", "16284286                              1.0                NaN             0   \n", "16284287                              1.0                NaN             0   \n", "16284288                              1.0                NaN             0   \n", "16284289                              NaN                NaN             0   \n", "\n", "          tenure  commit_percent  commits  burst  attrition_count  \n", "0            NaN             NaN      NaN    NaN              NaN  \n", "1            NaN             NaN      NaN    NaN              NaN  \n", "2            NaN             NaN      NaN    NaN              NaN  \n", "3            NaN             NaN      NaN    NaN              NaN  \n", "4            NaN             NaN      NaN    NaN              NaN  \n", "...          ...             ...      ...    ...              ...  \n", "16284285     NaN             NaN      NaN    NaN              NaN  \n", "16284286     NaN             NaN      NaN    NaN              NaN  \n", "16284287     NaN             NaN      NaN    NaN              NaN  \n", "16284288     NaN             NaN      NaN    NaN              NaN  \n", "16284289     NaN             NaN      NaN    NaN              NaN  \n", "\n", "[16284290 rows x 20 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["did_source_data = pd.read_csv(\"../result/did_result_20250312/productivity_20250312_with_propensity_scores_with_attritions.csv\")\n", "did_source_data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}], "ref": "2c556931-162d-48ec-823f-07896d4e64d0", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895", "5.141017148795798", null], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "18.3589", "2.9631522620523985", "0.6931471805599453"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "25.7632", "3.287027809575607", "0.6931471805599453"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "14.9636", "2.770311130495723", "0.6931471805599453"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "361.1883", "5.8921642423323215", "0.6931471805599453"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "351.6383", "5.865442885732476", "0.6931471805599453"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.9486", "0.6671111660091038", "0.6931471805599453"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "1.7981", "1.0289406154187903", "0.6931471805599453"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "21.5526", "3.1158503586370414", "0.6931471805599453"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.005", "0.0049875415110389", "0.6931471805599453"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.6445", "0.4974363866469745", "0.6931471805599453"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "49.2922", "3.9178499954986576", "0.6931471805599453"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "91.0883", "4.522747899360907", null], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "steady", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null]], "shape": {"columns": 46, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "      <td>1.421275</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "      <td>3.026300</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "      <td>3.619184</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 46 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        ...        steady       15.0       2.772589    6.751101   \n", "1        ...        steady       15.0       2.772589    6.751101   \n", "2        ...        steady       15.0       2.772589    6.751101   \n", "3        ...        steady       15.0       2.772589    6.751101   \n", "4        ...        steady       15.0       2.772589    6.751101   \n", "...      ...           ...        ...            ...         ...   \n", "3503016  ...        steady        1.0       0.693147    6.710523   \n", "3503017  ...        steady        1.0       0.693147    6.710523   \n", "3503018  ...        steady        1.0       0.693147    6.710523   \n", "3503019  ...        steady        1.0       0.693147    6.710523   \n", "3503020  ...        steady        1.0       0.693147    6.710523   \n", "\n", "        log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0                 0.144831     5.433722                        1.0   \n", "1                 0.144831     5.433722                        NaN   \n", "2                 0.144831     5.433722                        NaN   \n", "3                 0.144831     5.433722                        NaN   \n", "4                 0.144831     5.433722                        NaN   \n", "...                    ...          ...                        ...   \n", "3503016           0.018605     2.197225                        1.0   \n", "3503017           0.018605     2.197225                        NaN   \n", "3503018           0.018605     2.197225                        1.0   \n", "3503019           0.018605     2.197225                        1.0   \n", "3503020           0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge  log_time_to_merge  log_pull_request_success_rate  \n", "0                  NaN                NaN                       0.693147  \n", "1             169.8895           5.141017                            NaN  \n", "2                  NaN                NaN                            NaN  \n", "3                  NaN                NaN                            NaN  \n", "4                  NaN                NaN                            NaN  \n", "...                ...                ...                            ...  \n", "3503016         3.1424           1.421275                       0.693147  \n", "3503017            NaN                NaN                            NaN  \n", "3503018        19.6208           3.026300                       0.693147  \n", "3503019        36.3071           3.619184                       0.693147  \n", "3503020            NaN                NaN                            NaN  \n", "\n", "[3503021 rows x 46 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "did_source_data = pd.read_csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity_decelerating_to_steady.csv\")\n", "did_source_data"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理时间窗口: [-10, 10]\n", "原始数据行数: 5590129\n", "过滤后数据行数: 4707646\n", "保留比例: 84.21%\n", "数据已保存到: ../result/20250629_did_result/compiled_data_test_limit365_processed_timewindow_-10_10.csv\n", "处理组观测数: 812758\n", "对照组观测数: 3894888\n", "处理前观测数: 2446840\n", "处理后观测数: 2260806\n", "\n", "处理时间窗口: [-8, 8]\n", "原始数据行数: 5590129\n", "过滤后数据行数: 3820516\n", "保留比例: 68.34%\n", "数据已保存到: ../result/20250629_did_result/compiled_data_test_limit365_processed_timewindow_-8_8.csv\n", "处理组观测数: 657986\n", "对照组观测数: 3162530\n", "处理前观测数: 2011480\n", "处理后观测数: 1809036\n", "\n", "处理时间窗口: [-6, 6]\n", "原始数据行数: 5590129\n", "过滤后数据行数: 2928350\n", "保留比例: 52.38%\n", "数据已保存到: ../result/20250629_did_result/compiled_data_test_limit365_processed_timewindow_-6_6.csv\n", "处理组观测数: 503203\n", "对照组观测数: 2425147\n", "处理前观测数: 1571285\n", "处理后观测数: 1357065\n", "\n", "所有时间窗口数据生成完成!\n"]}], "source": ["# 生成不同时间窗口的数据版本\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# 读取原始数据\n", "did_source_data = pd.read_csv(\"../result/20250629_did_result/compiled_data_test_limit365_processed.csv\")\n", "\n", "# 定义不同的时间窗口\n", "time_windows = [\n", "    # (-11, 11),  # 23周窗口\n", "    (-10, 10),  # 21周窗口  \n", "    # (-9, 9),    # 19周窗口\n", "    (-8, 8),    # 17周窗口\n", "    # (-7, 7),    # 15周窗口\n", "    (-6, 6)     # 13周窗口\n", "]\n", "\n", "# 为每个时间窗口生成数据\n", "for start_week, end_week in time_windows:\n", "    print(f\"\\n处理时间窗口: [{start_week}, {end_week}]\")\n", "    \n", "    # 过滤数据\n", "    filtered_data = did_source_data[\n", "        (did_source_data['relativized_time'] >= start_week) & \n", "        (did_source_data['relativized_time'] <= end_week)\n", "    ].copy()\n", "    \n", "    print(f\"原始数据行数: {len(did_source_data)}\")\n", "    print(f\"过滤后数据行数: {len(filtered_data)}\")\n", "    print(f\"保留比例: {len(filtered_data)/len(did_source_data)*100:.2f}%\")\n", "    \n", "    # 保存数据\n", "    output_filename = f\"../result/20250629_did_result/compiled_data_test_limit365_processed_timewindow_{start_week}_{end_week}.csv\"\n", "    filtered_data.to_csv(output_filename, index=False)\n", "    print(f\"数据已保存到: {output_filename}\")\n", "    \n", "    # 显示一些统计信息\n", "    print(f\"处理组观测数: {len(filtered_data[filtered_data['is_treated'] == 1])}\")\n", "    print(f\"对照组观测数: {len(filtered_data[filtered_data['is_treated'] == 0])}\")\n", "    print(f\"处理前观测数: {len(filtered_data[filtered_data['post_treatment'] == 0])}\")\n", "    print(f\"处理后观测数: {len(filtered_data[filtered_data['post_treatment'] == 1])}\")\n", "\n", "print(\"\\n所有时间窗口数据生成完成!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}