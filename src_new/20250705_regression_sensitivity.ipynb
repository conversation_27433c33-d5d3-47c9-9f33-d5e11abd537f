{"cells": [{"cell_type": "code", "execution_count": null, "id": "07dce309", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘lme4’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘Matrix’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘stargazer’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘lmtest’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘MuMIn’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘lmerTest’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘ggpubr’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘survminer’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘coxme’ was built under R version 4.3.3”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting Automated DID Analysis\n"]}, {"ename": "ERROR", "evalue": "Error in \"=\" * 50: non-numeric argument to binary operator\n", "output_type": "error", "traceback": ["Error in \"=\" * 50: non-numeric argument to binary operator\n<PERSON><PERSON>:\n", "1. cat(\"=\" * 50, \"\\n\")"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4f4648f7", "metadata": {}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "Traceback:\n", "\n", "1. all.vars(formula(model_fixed_effects_repo_add_two))\n", "2. formula(model_fixed_effects_repo_add_two)"]}], "source": ["# 提取模型用到的所有变量名\n", "model_vars <- all.vars(formula(model_fixed_effects_repo_add_two))\n", "\n", "# 筛选出这些变量，并计算每列的缺失值数量\n", "na_counts <- colSums(is.na(compiled_data_test[model_vars]))\n", "\n", "# 打印出有缺失值的列及其缺失数量\n", "print(na_counts[na_counts > 0])"]}, {"cell_type": "code", "execution_count": null, "id": "2bb1a256", "metadata": {}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "Traceback:\n", "\n", "1. all.vars(formula(model_fixed_effects_repo_add_two))\n", "2. formula(model_fixed_effects_repo_add_two)"]}], "source": ["# 提取模型用到的所有变量名\n", "model_vars <- all.vars(formula(model_fixed_effects_repo_add_two))\n", "\n", "# 筛选出这些变量，并计算每列的缺失值数量\n", "na_counts <- colSums(is.na(compiled_data_test[model_vars]))\n", "\n", "# 打印出有缺失值的列及其缺失数量\n", "print(na_counts[na_counts > 0])"]}, {"cell_type": "code", "execution_count": null, "id": "ff191e57", "metadata": {}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "Traceback:\n", "\n", "1. all.vars(formula(model_fixed_effects_repo_add_two))\n", "2. formula(model_fixed_effects_repo_add_two)"]}], "source": ["# 提取模型用到的所有变量名\n", "model_vars <- all.vars(formula(model_fixed_effects_repo_add_two))\n", "\n", "# 筛选出这些变量，并计算每列的缺失值数量\n", "na_counts <- colSums(is.na(compiled_data_test[model_vars]))\n", "\n", "# 打印出有缺失值的列及其缺失数量\n", "print(na_counts[na_counts > 0])"]}, {"cell_type": "code", "execution_count": null, "id": "0d60fb04", "metadata": {}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "Traceback:\n", "\n", "1. all.vars(formula(model_fixed_effects_repo_add_two))\n", "2. formula(model_fixed_effects_repo_add_two)"]}], "source": ["# 提取模型用到的所有变量名\n", "model_vars <- all.vars(formula(model_fixed_effects_repo_add_two))\n", "\n", "# 筛选出这些变量，并计算每列的缺失值数量\n", "na_counts <- colSums(is.na(compiled_data_test[model_vars]))\n", "\n", "# 打印出有缺失值的列及其缺失数量\n", "print(na_counts[na_counts > 0])"]}, {"cell_type": "code", "execution_count": null, "id": "d3dc2be9", "metadata": {}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'model_fixed_effects_repo_add_two' not found\n", "Traceback:\n", "\n", "1. all.vars(formula(model_fixed_effects_repo_add_two))\n", "2. formula(model_fixed_effects_repo_add_two)"]}], "source": ["# 提取模型用到的所有变量名\n", "model_vars <- all.vars(formula(model_fixed_effects_repo_add_two))\n", "\n", "# 筛选出这些变量，并计算每列的缺失值数量\n", "na_counts <- colSums(is.na(compiled_data_test[model_vars]))\n", "\n", "# 打印出有缺失值的列及其缺失数量\n", "print(na_counts[na_counts > 0])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "python", "pygments_lexer": "r", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}