import pandas as pd
import os
import logging
import numpy as np
import time
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 设置日志
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "process_compiled_data_20250629.log")),
        logging.StreamHandler(),
    ],
)

# 输出目录
output_dir = '../result/20250629_did_result/'
os.makedirs(output_dir, exist_ok=True)

# 定义要处理的limits
ATTRITION_LIMITS = [180, 270]

def process_compiled_data_for_limit(limit):
    """处理单个limit的compiled_data，统一cohort内的treatment属性"""
    start_time = time.time()
    print(f"\n{'='*60}")
    print(f"PROCESSING COMPILED DATA FOR LIMIT: {limit}")
    print(f"{'='*60}")
    
    # 读取compiled_data文件
    input_file = f'{output_dir}compiled_data_test_limit{limit}.csv'
    
    if not os.path.exists(input_file):
        print(f"Error: Compiled data file not found: {input_file}")
        logging.error(f"Compiled data file not found: {input_file}")
        return
    
    print(f"Reading compiled data from: {input_file}")
    compiled_data = pd.read_csv(input_file)
    print(f"Loaded compiled data shape: {compiled_data.shape}")
    
    # 确保必要的列存在
    required_columns = ['cohort_id', 'is_treated', 'relativized_time', 'tenure', 'commit_percent', 'commits']
    missing_columns = [col for col in required_columns if col not in compiled_data.columns]
    if missing_columns:
        print(f"Error: Missing required columns: {missing_columns}")
        logging.error(f"Missing required columns: {missing_columns}")
        return
    
    # 第一步：统一cohort内的treatment属性（tenure, commit_percent, commits, growth_phase, newcomers）
    print("Step 1: Unifying treatment attributes within each cohort...")
    
    # 检查growth_phase和newcomers列是否存在，如果不存在则创建
    if 'growth_phase' not in compiled_data.columns:
        compiled_data['growth_phase'] = 'unknown'
    if 'newcomers' not in compiled_data.columns:
        compiled_data['newcomers'] = 0
    
    treatment_attributes_updated = 0
    
    for cohort_id, group in compiled_data.groupby('cohort_id'):
        # 找到treatment组中relativized_time=0的行（treatment时间点）
        treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]
        if treated_group.empty:
            continue
        
        # 获取treatment行的属性值（包括growth_phase和newcomers）
        treatment_columns = ['tenure', 'commit_percent', 'commits', 'growth_phase', 'newcomers']
        available_columns = [col for col in treatment_columns if col in treated_group.columns]
        
        if available_columns:
            treated_row = treated_group.iloc[0][available_columns]
            
            # 更新该cohort内所有行的这些属性
            mask = compiled_data['cohort_id'] == cohort_id
            compiled_data.loc[mask, available_columns] = treated_row.values
            treatment_attributes_updated += 1
            
            if treatment_attributes_updated % 100 == 0:
                print(f"Updated treatment attributes for {treatment_attributes_updated} cohorts...")
    
    print(f"Completed treatment attributes update for {treatment_attributes_updated} cohorts")
    
    # 第二步：统一cohort内的项目特征（treatment时间点的项目特征）
    print("Step 2: Unifying project characteristics within each cohort...")
    
    # 检查并创建必要的列
    project_columns = ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 
                      'log_project_age_before_treatment', 'project_main_language']
    
    for col in project_columns:
        if col not in compiled_data.columns:
            if col == 'project_main_language':
                compiled_data[col] = ''
            else:
                compiled_data[col] = 0
    
    project_attributes_updated = 0
    
    for cohort_id, group in compiled_data.groupby('cohort_id'):
        # 找到treatment组中relativized_time=0的行
        treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]
        if treated_group.empty:
            continue
        
        # 获取treatment行的项目特征
        source_columns = ['log_project_commits', 'log_project_contributors', 'log_project_age', 'mainLanguage']
        target_columns = ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 
                         'log_project_age_before_treatment', 'project_main_language']
        
        # 检查源列是否存在
        available_source_columns = [col for col in source_columns if col in treated_group.columns]
        available_target_columns = [target_columns[source_columns.index(col)] for col in available_source_columns]
        
        if available_source_columns:
            treated_row = treated_group.iloc[0][available_source_columns]
            
            # 更新该cohort内所有行的项目特征
            mask = compiled_data['cohort_id'] == cohort_id
            compiled_data.loc[mask, available_target_columns] = treated_row.values
            project_attributes_updated += 1
            
            if project_attributes_updated % 100 == 0:
                print(f"Updated project characteristics for {project_attributes_updated} cohorts...")
    
    print(f"Completed project characteristics update for {project_attributes_updated} cohorts")
    
    # 第三步：添加一些有用的派生变量
    print("Step 3: Adding derived variables...")
    
    # 添加log版本的变量
    if 'tenure' in compiled_data.columns:
        compiled_data['log_tenure'] = np.log(compiled_data['tenure'] + 1)
    if 'commit_percent' in compiled_data.columns:
        compiled_data['log_commit_percent'] = np.log(compiled_data['commit_percent'] + 1)
    if 'commits' in compiled_data.columns:
        compiled_data['log_commits'] = np.log(compiled_data['commits'] + 1)
    if 'newcomers' in compiled_data.columns:
        compiled_data['log_newcomers'] = np.log(compiled_data['newcomers'] + 1)
    
    # 添加newcomers的布尔变量
    if 'newcomers' in compiled_data.columns:
        compiled_data['newcomers_bool'] = (compiled_data['newcomers'] > 0).astype(int)
    
    # 添加时间-队列效应和仓库-队列效应
    if 'relativized_time' in compiled_data.columns and 'cohort_id' in compiled_data.columns:
        compiled_data['time_cohort_effect'] = compiled_data['relativized_time'].astype(str) + '_' + compiled_data['cohort_id'].astype(str)
        compiled_data['repo_cohort_effect'] = compiled_data['repo_name'] + '_' + compiled_data['cohort_id'].astype(str)
    
    # 添加outlier标识（可选）
    compiled_data['outlier'] = 0
    
    print("Completed adding derived variables")
    
    # 第四步：保存处理后的数据
    print("Step 4: Saving processed data...")
    
    output_file = f'{output_dir}compiled_data_test_limit{limit}_processed.csv'
    compiled_data.to_csv(output_file, index=False)
    
    total_time = time.time() - start_time
    print(f"\n{'='*60}")
    print(f"COMPLETED PROCESSING FOR LIMIT: {limit}")
    print(f"Final data shape: {compiled_data.shape}")
    print(f"Treatment attributes updated: {treatment_attributes_updated}")
    print(f"Project characteristics updated: {project_attributes_updated}")
    print(f"Total processing time: {total_time:.1f} seconds")
    print(f"Output saved to: {output_file}")
    print(f"{'='*60}\n")
    
    # 输出一些统计信息
    if 'growth_phase' in compiled_data.columns:
        print("Growth phase distribution:")
        print(compiled_data['growth_phase'].value_counts())
    
    if 'newcomers' in compiled_data.columns:
        print(f"\nNewcomers statistics:")
        print(f"Mean: {compiled_data['newcomers'].mean():.2f}")
        print(f"Median: {compiled_data['newcomers'].median():.2f}")
        print(f"Max: {compiled_data['newcomers'].max()}")
        print(f"Min: {compiled_data['newcomers'].min()}")
    
    return compiled_data

def main():
    """主函数，处理所有limits的compiled_data"""
    overall_start_time = time.time()
    print(f"\n{'='*80}")
    print("STARTING COMPILED DATA PROCESSING FOR MULTIPLE LIMITS")
    print(f"Processing limits: {ATTRITION_LIMITS}")
    print(f"{'='*80}")
    
    total_limits = len(ATTRITION_LIMITS)
    processed_count = 0
    
    for limit_idx, limit in enumerate(ATTRITION_LIMITS, 1):
        print(f"\nProcessing limit {limit_idx}/{total_limits}: {limit}")
        
        try:
            result = process_compiled_data_for_limit(limit)
            if result is not None:
                processed_count += 1
        except Exception as e:
            print(f"Error processing limit {limit}: {e}")
            logging.error(f"Error processing limit {limit}: {e}")
        
        # 计算进度百分比
        progress = (limit_idx / total_limits) * 100
        print(f"Overall progress: {progress:.1f}% ({limit_idx}/{total_limits} limits completed)")
    
    overall_processing_time = time.time() - overall_start_time
    print(f"\n{'='*80}")
    print("COMPILED DATA PROCESSING FOR ALL LIMITS COMPLETED")
    print(f"Successfully processed: {processed_count}/{total_limits} limits")
    print(f"Total processing time: {overall_processing_time:.1f} seconds")
    print(f"{'='*80}")

if __name__ == "__main__":
    print(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    main()
    print(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}") 