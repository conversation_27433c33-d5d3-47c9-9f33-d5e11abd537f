# Diagnostic script to identify data issues
library(dplyr)
library(lme4)

# Load the data
compiled_data_test <- read.csv("../result/20250629_did_result/compiled_data_test_limit180_processed_ultra_optimized.csv")

# Check initial data dimensions
cat("Initial data dimensions:", dim(compiled_data_test), "\n")

# Add log transformations
compiled_data_test$log_project_commits <- log(compiled_data_test$project_commits + 1)
compiled_data_test$log_project_contributors <- log(compiled_data_test$project_contributors + 1)
compiled_data_test$log_project_age <- log(compiled_data_test$project_age + 1)
compiled_data_test$log_pr_count <- log(compiled_data_test$pr_count + 1)
compiled_data_test$log_pr_throughput <- log(compiled_data_test$pr_throughput + 1)

cat("After log transformations:", dim(compiled_data_test), "\n")

# Check for missing values in key variables
key_vars <- c("log_pr_throughput", "is_post_treatment", "is_treated", 
              "log_project_commits", "log_project_contributors", "log_project_age",
              "time_cohort_effect", "repo_cohort_effect")

cat("\nMissing values in key variables:\n")
for(var in key_vars) {
  if(var %in% names(compiled_data_test)) {
    missing_count <- sum(is.na(compiled_data_test[[var]]))
    cat(var, ":", missing_count, "missing values\n")
  } else {
    cat(var, ": Variable not found\n")
  }
}

# Check for missing values in variables used in scaling
scaling_vars <- c("log_tenure", "log_commit_percent", "log_commits",
                  "log_project_commits_before_treatment", 
                  "log_project_contributors_before_treatment",
                  "log_project_age_before_treatment")

cat("\nMissing values in scaling variables:\n")
for(var in scaling_vars) {
  if(var %in% names(compiled_data_test)) {
    missing_count <- sum(is.na(compiled_data_test[[var]]))
    cat(var, ":", missing_count, "missing values\n")
  } else {
    cat(var, ": Variable not found\n")
  }
}

# Apply the problematic scaling step and see what happens
cat("\nApplying scaling step...\n")
compiled_data_test_scaled <- compiled_data_test %>%
  mutate(
    log_tenure_c = scale(log_tenure),
    log_commit_percent_c = scale(log_commit_percent),
    log_commits_c = scale(log_commits),
    log_project_commits = scale(log_project_commits),
    log_project_contributors = scale(log_project_contributors),
    log_project_age = scale(log_project_age),
    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),
    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),
    log_project_age_before_treatment = scale(log_project_age_before_treatment),
  )

cat("After scaling:", dim(compiled_data_test_scaled), "\n")

# Check missing values after scaling
cat("\nMissing values after scaling:\n")
for(var in c("log_tenure_c", "log_commit_percent_c", "log_commits_c",
             "log_project_commits", "log_project_contributors", "log_project_age",
             "log_project_commits_before_treatment", 
             "log_project_contributors_before_treatment",
             "log_project_age_before_treatment")) {
  if(var %in% names(compiled_data_test_scaled)) {
    missing_count <- sum(is.na(compiled_data_test_scaled[[var]]))
    cat(var, ":", missing_count, "missing values\n")
  }
}

# Apply drop_na() and see the result
cat("\nApplying drop_na()...\n")
compiled_data_test_cleaned <- tidyr::drop_na(compiled_data_test_scaled)
cat("After drop_na():", dim(compiled_data_test_cleaned), "\n")

# If we still have data, check the model variables
if(nrow(compiled_data_test_cleaned) > 0) {
  cat("\nChecking model variables after cleaning:\n")
  model_vars <- c("log_pr_throughput", "is_post_treatment", "is_treated", 
                  "log_project_commits", "log_project_contributors", "log_project_age",
                  "time_cohort_effect", "repo_cohort_effect")
  
  for(var in model_vars) {
    if(var %in% names(compiled_data_test_cleaned)) {
      missing_count <- sum(is.na(compiled_data_test_cleaned[[var]]))
      cat(var, ":", missing_count, "missing values\n")
    } else {
      cat(var, ": Variable not found\n")
    }
  }
  
  # Try to create a minimal model to test
  cat("\nTesting minimal model...\n")
  tryCatch({
    test_model <- lmer(
      log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
        log_project_commits + log_project_contributors + log_project_age + 
        (1 | time_cohort_effect) + (1 | repo_cohort_effect),
      REML = FALSE,
      data = compiled_data_test_cleaned
    )
    cat("Minimal model successful!\n")
  }, error = function(e) {
    cat("Minimal model failed:", e$message, "\n")
  })
} 