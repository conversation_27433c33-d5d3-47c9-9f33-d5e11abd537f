{"cells": [{"cell_type": "code", "execution_count": 6, "id": "e33a24f8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "productivity_v_0 = pd.read_csv('../result/did_result_20250312/productivity_20250312_with_propensity_scores_with_attritions.csv')\n", "\n", "\n", "productivity_v_1= pd.read_csv('../result/20250629_did_result/productivity_with_propensity_scores_with_attritions_365.csv')\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "0a82d0e0", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}], "ref": "b27e0727-0d64-4e6e-b5d3-a4f2d463ce93", "rows": [["0", "01mf02/jaq", "609", "2022-05-08", "0.0", "1.0", null, "418", "3", "6", "Rust", null, null, null, null, "0", null, null, null, null, null], ["1", "01mf02/jaq", "610", "2022-05-15", "0.0", null, null, "418", "3", "13", "Rust", null, null, null, null, "0", null, null, null, null, null], ["2", "01mf02/jaq", "611", "2022-05-22", "2.0", "1.0", "188.2165", "425", "4", "20", "Rust", null, null, null, null, "0", null, null, null, null, null], ["3", "01mf02/jaq", "612", "2022-05-29", "0.0", null, null, "430", "4", "27", "Rust", null, null, null, null, "0", null, null, null, null, null], ["4", "01mf02/jaq", "613", "2022-06-05", "0.0", null, null, "430", "4", "34", "Rust", null, null, null, null, "0", null, null, null, null, null], ["5", "01mf02/jaq", "614", "2022-06-12", "0.0", null, null, "431", "4", "41", "Rust", null, null, null, null, "0", null, null, null, null, null], ["6", "01mf02/jaq", "615", "2022-06-19", "0.0", null, null, "436", "4", "48", "Rust", null, null, null, null, "0", null, null, null, null, null], ["7", "01mf02/jaq", "616", "2022-06-26", "1.0", "1.0", "22.9372", "445", "5", "55", "Rust", null, null, null, null, "0", null, null, null, null, null], ["8", "01mf02/jaq", "617", "2022-07-03", "0.0", null, null, "452", "5", "62", "Rust", null, null, null, null, "0", null, null, null, null, null], ["9", "01mf02/jaq", "618", "2022-07-10", "0.0", null, null, "460", "5", "69", "Rust", null, null, null, null, "0", null, null, null, null, null], ["10", "01mf02/jaq", "619", "2022-07-17", "0.0", null, null, "462", "5", "76", "Rust", null, null, null, null, "0", null, null, null, null, null], ["11", "01mf02/jaq", "620", "2022-07-24", "1.0", "0.5", "6.7678", "477", "6", "83", "Rust", null, null, null, null, "0", null, null, null, null, null], ["12", "01mf02/jaq", "621", "2022-07-31", "0.0", null, null, "488", "6", "90", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["13", "01mf02/jaq", "622", "2022-08-07", "0.0", "1.0", null, "498", "7", "97", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["14", "01mf02/jaq", "623", "2022-08-14", "1.0", null, "152.0294", "512", "7", "104", "Rust", "0.4", null, null, null, "0", null, null, null, null, null], ["15", "01mf02/jaq", "624", "2022-08-21", "0.0", null, null, "518", "7", "111", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["16", "01mf02/jaq", "625", "2022-08-28", "0.0", null, null, "518", "7", "118", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["17", "01mf02/jaq", "626", "2022-09-04", "0.0", null, null, "520", "7", "125", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["18", "01mf02/jaq", "627", "2022-09-11", "0.0", null, null, "520", "7", "132", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["19", "01mf02/jaq", "628", "2022-09-18", "0.0", null, null, "526", "7", "139", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null], ["20", "01mf02/jaq", "629", "2022-09-25", "0.0", null, null, "529", "7", "146", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["21", "01mf02/jaq", "630", "2022-10-02", "0.0", null, null, "538", "7", "153", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["22", "01mf02/jaq", "631", "2022-10-09", "0.0", null, null, "558", "7", "160", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["23", "01mf02/jaq", "632", "2022-10-16", "1.0", "1.0", "2.4025", "561", "7", "167", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["24", "01mf02/jaq", "633", "2022-10-23", "0.0", null, null, "562", "7", "174", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["25", "01mf02/jaq", "634", "2022-10-30", "0.0", null, null, "571", "7", "181", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["26", "01mf02/jaq", "635", "2022-11-06", "0.0", null, null, "589", "7", "188", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null], ["27", "01mf02/jaq", "636", "2022-11-13", "0.0", null, null, "608", "7", "195", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["28", "01mf02/jaq", "637", "2022-11-20", "0.0", null, null, "608", "7", "202", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["29", "01mf02/jaq", "638", "2022-11-27", "0.0", null, null, "608", "7", "209", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["30", "01mf02/jaq", "639", "2022-12-04", "0.0", null, null, "608", "7", "216", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["31", "01mf02/jaq", "640", "2022-12-11", "0.0", null, null, "608", "7", "223", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["32", "01mf02/jaq", "641", "2022-12-18", "0.0", null, null, "609", "7", "230", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["33", "01mf02/jaq", "642", "2022-12-25", "0.0", null, null, "621", "7", "237", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["34", "01mf02/jaq", "643", "2023-01-01", "0.0", null, null, "629", "7", "244", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["35", "01mf02/jaq", "644", "2023-01-08", "0.0", null, null, "630", "7", "251", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null], ["36", "01mf02/jaq", "645", "2023-01-15", "0.0", null, null, "630", "7", "258", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["37", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", null, "634", "8", "265", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["38", "01mf02/jaq", "647", "2023-01-29", "1.0", null, "169.8895", "634", "8", "272", "Rust", "0.6666666666666666", null, null, null, "0", null, null, null, null, null], ["39", "01mf02/jaq", "648", "2023-02-05", "0.0", null, null, "634", "8", "279", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["40", "01mf02/jaq", "649", "2023-02-12", "0.0", null, null, "642", "8", "286", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["41", "01mf02/jaq", "650", "2023-02-19", "0.0", null, null, "642", "8", "293", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["42", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6666666666666666", null, null, null, "0", null, null, null, null, null], ["43", "01mf02/jaq", "652", "2023-03-05", "0.0", null, null, "645", "9", "307", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["44", "01mf02/jaq", "653", "2023-03-12", "0.0", null, null, "656", "9", "314", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["45", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", null, "663", "10", "321", "Rust", "0.5", null, null, null, "0", null, null, null, null, null], ["46", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", null, null, "0", null, null, null, null, null], ["47", "01mf02/jaq", "656", "2023-04-02", "0.0", null, null, "681", "10", "335", "Rust", "0.5", "0.5", null, null, "0", null, null, null, null, null], ["48", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6666666666666666", "0.5", null, null, "0", null, null, null, null, null], ["49", "01mf02/jaq", "658", "2023-04-16", "0.0", null, null, "765", "11", "349", "Rust", "0.5", "0.5", null, null, "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0"]], "shape": {"columns": 20, "rows": 16284290}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>feature_sigmod_12_pr_throughput</th>\n", "      <th>feature_sigmod_12_pull_request_success_rate</th>\n", "      <th>feature_sigmod_12_time_to_merge</th>\n", "      <th>feature_sigmod_12</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>609</td>\n", "      <td>2022-05-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>610</td>\n", "      <td>2022-05-15</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>611</td>\n", "      <td>2022-05-22</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>425</td>\n", "      <td>4</td>\n", "      <td>20</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>612</td>\n", "      <td>2022-05-29</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>613</td>\n", "      <td>2022-06-05</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>34</td>\n", "      <td>Rust</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284285</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>737</td>\n", "      <td>2024-10-20</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>415</td>\n", "      <td>39</td>\n", "      <td>2687</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284286</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>738</td>\n", "      <td>2024-10-27</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>419</td>\n", "      <td>39</td>\n", "      <td>2694</td>\n", "      <td>C#</td>\n", "      <td>0.333333</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284287</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>739</td>\n", "      <td>2024-11-03</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2701</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284288</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>740</td>\n", "      <td>2024-11-10</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2708</td>\n", "      <td>C#</td>\n", "      <td>0.333333</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284289</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>741</td>\n", "      <td>2024-11-11</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>284.1031</td>\n", "      <td>424</td>\n", "      <td>40</td>\n", "      <td>2709</td>\n", "      <td>C#</td>\n", "      <td>0.500000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16284290 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                              repo_name  standardized_time_weeks    datetime  \\\n", "0                            01mf02/jaq                      609  2022-05-08   \n", "1                            01mf02/jaq                      610  2022-05-15   \n", "2                            01mf02/jaq                      611  2022-05-22   \n", "3                            01mf02/jaq                      612  2022-05-29   \n", "4                            01mf02/jaq                      613  2022-06-05   \n", "...                                 ...                      ...         ...   \n", "16284285  zzzprojects/html-agility-pack                      737  2024-10-20   \n", "16284286  zzzprojects/html-agility-pack                      738  2024-10-27   \n", "16284287  zzzprojects/html-agility-pack                      739  2024-11-03   \n", "16284288  zzzprojects/html-agility-pack                      740  2024-11-10   \n", "16284289  zzzprojects/html-agility-pack                      741  2024-11-11   \n", "\n", "          pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                   0.0                        1.0            NaN   \n", "1                   0.0                        NaN            NaN   \n", "2                   2.0                        1.0       188.2165   \n", "3                   0.0                        NaN            NaN   \n", "4                   0.0                        NaN            NaN   \n", "...                 ...                        ...            ...   \n", "16284285            0.0                        NaN            NaN   \n", "16284286            0.0                        NaN            NaN   \n", "16284287            0.0                        1.0            NaN   \n", "16284288            0.0                        NaN            NaN   \n", "16284289            1.0                        NaN       284.1031   \n", "\n", "          project_commits  project_contributors  project_age mainLanguage  \\\n", "0                     418                     3            6         Rust   \n", "1                     418                     3           13         Rust   \n", "2                     425                     4           20         Rust   \n", "3                     430                     4           27         Rust   \n", "4                     430                     4           34         Rust   \n", "...                   ...                   ...          ...          ...   \n", "16284285              415                    39         2687           C#   \n", "16284286              419                    39         2694           C#   \n", "16284287              421                    40         2701           C#   \n", "16284288              421                    40         2708           C#   \n", "16284289              424                    40         2709           C#   \n", "\n", "          feature_sigmod_12_pr_throughput  \\\n", "0                                     NaN   \n", "1                                     NaN   \n", "2                                     NaN   \n", "3                                     NaN   \n", "4                                     NaN   \n", "...                                   ...   \n", "16284285                         0.500000   \n", "16284286                         0.333333   \n", "16284287                         0.500000   \n", "16284288                         0.333333   \n", "16284289                         0.500000   \n", "\n", "          feature_sigmod_12_pull_request_success_rate  \\\n", "0                                                 NaN   \n", "1                                                 NaN   \n", "2                                                 NaN   \n", "3                                                 NaN   \n", "4                                                 NaN   \n", "...                                               ...   \n", "16284285                                          NaN   \n", "16284286                                          NaN   \n", "16284287                                          NaN   \n", "16284288                                          NaN   \n", "16284289                                          NaN   \n", "\n", "          feature_sigmod_12_time_to_merge  feature_sigmod_12  someone_left  \\\n", "0                                     NaN                NaN             0   \n", "1                                     NaN                NaN             0   \n", "2                                     NaN                NaN             0   \n", "3                                     NaN                NaN             0   \n", "4                                     NaN                NaN             0   \n", "...                                   ...                ...           ...   \n", "16284285                              1.0                NaN             0   \n", "16284286                              1.0                NaN             0   \n", "16284287                              1.0                NaN             0   \n", "16284288                              1.0                NaN             0   \n", "16284289                              NaN                NaN             0   \n", "\n", "          tenure  commit_percent  commits  burst  attrition_count  \n", "0            NaN             NaN      NaN    NaN              NaN  \n", "1            NaN             NaN      NaN    NaN              NaN  \n", "2            NaN             NaN      NaN    NaN              NaN  \n", "3            NaN             NaN      NaN    NaN              NaN  \n", "4            NaN             NaN      NaN    NaN              NaN  \n", "...          ...             ...      ...    ...              ...  \n", "16284285     NaN             NaN      NaN    NaN              NaN  \n", "16284286     NaN             NaN      NaN    NaN              NaN  \n", "16284287     NaN             NaN      NaN    NaN              NaN  \n", "16284288     NaN             NaN      NaN    NaN              NaN  \n", "16284289     NaN             NaN      NaN    NaN              NaN  \n", "\n", "[16284290 rows x 20 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["productivity_v_0"]}, {"cell_type": "code", "execution_count": 8, "id": "84591176", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}], "ref": "3dc86fcd-6730-491d-a14d-8f93b1f0eca7", "rows": [["0", "01mf02/jaq", "609", "2022-05-08", "0.0", "1.0", null, "418", "3", "6", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["1", "01mf02/jaq", "610", "2022-05-15", "0.0", null, null, "418", "3", "13", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["2", "01mf02/jaq", "611", "2022-05-22", "2.0", "1.0", "188.2165", "425", "4", "20", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["3", "01mf02/jaq", "612", "2022-05-29", "0.0", null, null, "430", "4", "27", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["4", "01mf02/jaq", "613", "2022-06-05", "0.0", null, null, "430", "4", "34", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["5", "01mf02/jaq", "614", "2022-06-12", "0.0", null, null, "431", "4", "41", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["6", "01mf02/jaq", "615", "2022-06-19", "0.0", null, null, "436", "4", "48", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["7", "01mf02/jaq", "616", "2022-06-26", "1.0", "1.0", "22.9372", "445", "5", "55", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["8", "01mf02/jaq", "617", "2022-07-03", "0.0", null, null, "452", "5", "62", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["9", "01mf02/jaq", "618", "2022-07-10", "0.0", null, null, "460", "5", "69", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["10", "01mf02/jaq", "619", "2022-07-17", "0.0", null, null, "462", "5", "76", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["11", "01mf02/jaq", "620", "2022-07-24", "1.0", "0.5", "6.7678", "477", "6", "83", "Rust", null, null, null, null, "0", null, null, null, null, null, null, null], ["12", "01mf02/jaq", "621", "2022-07-31", "0.0", null, null, "488", "6", "90", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["13", "01mf02/jaq", "622", "2022-08-07", "0.0", "1.0", null, "498", "7", "97", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["14", "01mf02/jaq", "623", "2022-08-14", "1.0", null, "152.0294", "512", "7", "104", "Rust", "0.4", null, null, null, "0", null, null, null, null, null, null, null], ["15", "01mf02/jaq", "624", "2022-08-21", "0.0", null, null, "518", "7", "111", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["16", "01mf02/jaq", "625", "2022-08-28", "0.0", null, null, "518", "7", "118", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["17", "01mf02/jaq", "626", "2022-09-04", "0.0", null, null, "520", "7", "125", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["18", "01mf02/jaq", "627", "2022-09-11", "0.0", null, null, "520", "7", "132", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["19", "01mf02/jaq", "628", "2022-09-18", "0.0", null, null, "526", "7", "139", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null, null, null], ["20", "01mf02/jaq", "629", "2022-09-25", "0.0", null, null, "529", "7", "146", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["21", "01mf02/jaq", "630", "2022-10-02", "0.0", null, null, "538", "7", "153", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["22", "01mf02/jaq", "631", "2022-10-09", "0.0", null, null, "558", "7", "160", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["23", "01mf02/jaq", "632", "2022-10-16", "1.0", "1.0", "2.4025", "561", "7", "167", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["24", "01mf02/jaq", "633", "2022-10-23", "0.0", null, null, "562", "7", "174", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["25", "01mf02/jaq", "634", "2022-10-30", "0.0", null, null, "571", "7", "181", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["26", "01mf02/jaq", "635", "2022-11-06", "0.0", null, null, "589", "7", "188", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null, null, null], ["27", "01mf02/jaq", "636", "2022-11-13", "0.0", null, null, "608", "7", "195", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["28", "01mf02/jaq", "637", "2022-11-20", "0.0", null, null, "608", "7", "202", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["29", "01mf02/jaq", "638", "2022-11-27", "0.0", null, null, "608", "7", "209", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["30", "01mf02/jaq", "639", "2022-12-04", "0.0", null, null, "608", "7", "216", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["31", "01mf02/jaq", "640", "2022-12-11", "0.0", null, null, "608", "7", "223", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["32", "01mf02/jaq", "641", "2022-12-18", "0.0", null, null, "609", "7", "230", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["33", "01mf02/jaq", "642", "2022-12-25", "0.0", null, null, "621", "7", "237", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["34", "01mf02/jaq", "643", "2023-01-01", "0.0", null, null, "629", "7", "244", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["35", "01mf02/jaq", "644", "2023-01-08", "0.0", null, null, "630", "7", "251", "Rust", "0.3333333333333333", null, null, null, "0", null, null, null, null, null, null, null], ["36", "01mf02/jaq", "645", "2023-01-15", "0.0", null, null, "630", "7", "258", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["37", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", null, "634", "8", "265", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["38", "01mf02/jaq", "647", "2023-01-29", "1.0", null, "169.8895", "634", "8", "272", "Rust", "0.6666666666666666", null, null, null, "0", null, null, null, null, null, null, null], ["39", "01mf02/jaq", "648", "2023-02-05", "0.0", null, null, "634", "8", "279", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["40", "01mf02/jaq", "649", "2023-02-12", "0.0", null, null, "642", "8", "286", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["41", "01mf02/jaq", "650", "2023-02-19", "0.0", null, null, "642", "8", "293", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["42", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6666666666666666", null, null, null, "0", null, null, null, null, null, null, null], ["43", "01mf02/jaq", "652", "2023-03-05", "0.0", null, null, "645", "9", "307", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["44", "01mf02/jaq", "653", "2023-03-12", "0.0", null, null, "656", "9", "314", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["45", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", null, "663", "10", "321", "Rust", "0.5", null, null, null, "0", null, null, null, null, null, null, null], ["46", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", null, null, "0", null, null, null, null, null, null, null], ["47", "01mf02/jaq", "656", "2023-04-02", "0.0", null, null, "681", "10", "335", "Rust", "0.5", "0.5", null, null, "0", null, null, null, null, null, null, null], ["48", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6666666666666666", "0.5", null, null, "0", null, null, null, null, null, null, null], ["49", "01mf02/jaq", "658", "2023-04-16", "0.0", null, null, "765", "11", "349", "Rust", "0.5", "0.5", null, null, "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "decelerating", "15.0"]], "shape": {"columns": 22, "rows": 16284290}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>feature_sigmod_12_time_to_merge</th>\n", "      <th>feature_sigmod_12</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>609</td>\n", "      <td>2022-05-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>610</td>\n", "      <td>2022-05-15</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>611</td>\n", "      <td>2022-05-22</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>188.2165</td>\n", "      <td>425</td>\n", "      <td>4</td>\n", "      <td>20</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>612</td>\n", "      <td>2022-05-29</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>613</td>\n", "      <td>2022-06-05</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>430</td>\n", "      <td>4</td>\n", "      <td>34</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284285</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>737</td>\n", "      <td>2024-10-20</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>415</td>\n", "      <td>39</td>\n", "      <td>2687</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284286</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>738</td>\n", "      <td>2024-10-27</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>419</td>\n", "      <td>39</td>\n", "      <td>2694</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284287</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>739</td>\n", "      <td>2024-11-03</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2701</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284288</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>740</td>\n", "      <td>2024-11-10</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>421</td>\n", "      <td>40</td>\n", "      <td>2708</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16284289</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>741</td>\n", "      <td>2024-11-11</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>284.1031</td>\n", "      <td>424</td>\n", "      <td>40</td>\n", "      <td>2709</td>\n", "      <td>C#</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16284290 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                              repo_name  standardized_time_weeks    datetime  \\\n", "0                            01mf02/jaq                      609  2022-05-08   \n", "1                            01mf02/jaq                      610  2022-05-15   \n", "2                            01mf02/jaq                      611  2022-05-22   \n", "3                            01mf02/jaq                      612  2022-05-29   \n", "4                            01mf02/jaq                      613  2022-06-05   \n", "...                                 ...                      ...         ...   \n", "16284285  zzzprojects/html-agility-pack                      737  2024-10-20   \n", "16284286  zzzprojects/html-agility-pack                      738  2024-10-27   \n", "16284287  zzzprojects/html-agility-pack                      739  2024-11-03   \n", "16284288  zzzprojects/html-agility-pack                      740  2024-11-10   \n", "16284289  zzzprojects/html-agility-pack                      741  2024-11-11   \n", "\n", "          pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                   0.0                        1.0            NaN   \n", "1                   0.0                        NaN            NaN   \n", "2                   2.0                        1.0       188.2165   \n", "3                   0.0                        NaN            NaN   \n", "4                   0.0                        NaN            NaN   \n", "...                 ...                        ...            ...   \n", "16284285            0.0                        NaN            NaN   \n", "16284286            0.0                        NaN            NaN   \n", "16284287            0.0                        1.0            NaN   \n", "16284288            0.0                        NaN            NaN   \n", "16284289            1.0                        NaN       284.1031   \n", "\n", "          project_commits  project_contributors  project_age mainLanguage  \\\n", "0                     418                     3            6         Rust   \n", "1                     418                     3           13         Rust   \n", "2                     425                     4           20         Rust   \n", "3                     430                     4           27         Rust   \n", "4                     430                     4           34         Rust   \n", "...                   ...                   ...          ...          ...   \n", "16284285              415                    39         2687           C#   \n", "16284286              419                    39         2694           C#   \n", "16284287              421                    40         2701           C#   \n", "16284288              421                    40         2708           C#   \n", "16284289              424                    40         2709           C#   \n", "\n", "          ...  feature_sigmod_12_time_to_merge  feature_sigmod_12  \\\n", "0         ...                              NaN                NaN   \n", "1         ...                              NaN                NaN   \n", "2         ...                              NaN                NaN   \n", "3         ...                              NaN                NaN   \n", "4         ...                              NaN                NaN   \n", "...       ...                              ...                ...   \n", "16284285  ...                              1.0                NaN   \n", "16284286  ...                              1.0                NaN   \n", "16284287  ...                              1.0                NaN   \n", "16284288  ...                              1.0                NaN   \n", "16284289  ...                              NaN                NaN   \n", "\n", "          someone_left  tenure  commit_percent  commits  burst  \\\n", "0                    0     NaN             NaN      NaN    NaN   \n", "1                    0     NaN             NaN      NaN    NaN   \n", "2                    0     NaN             NaN      NaN    NaN   \n", "3                    0     NaN             NaN      NaN    NaN   \n", "4                    0     NaN             NaN      NaN    NaN   \n", "...                ...     ...             ...      ...    ...   \n", "16284285             0     NaN             NaN      NaN    NaN   \n", "16284286             0     NaN             NaN      NaN    NaN   \n", "16284287             0     NaN             NaN      NaN    NaN   \n", "16284288             0     NaN             NaN      NaN    NaN   \n", "16284289             0     NaN             NaN      NaN    NaN   \n", "\n", "          attrition_count  growth_phase  newcomers  \n", "0                     NaN           NaN        NaN  \n", "1                     NaN           NaN        NaN  \n", "2                     NaN           NaN        NaN  \n", "3                     NaN           NaN        NaN  \n", "4                     NaN           NaN        NaN  \n", "...                   ...           ...        ...  \n", "16284285              NaN           NaN        NaN  \n", "16284286              NaN           NaN        NaN  \n", "16284287              NaN           NaN        NaN  \n", "16284288              NaN           NaN        NaN  \n", "16284289              NaN           NaN        NaN  \n", "\n", "[16284290 rows x 22 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["productivity_v_1"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}