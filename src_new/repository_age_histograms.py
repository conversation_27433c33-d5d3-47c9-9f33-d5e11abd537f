#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Repository Age Histograms
This script creates two histograms based on repository age data:
1. Contributor counts by repository age (in years) for attrition and engagement events
2. Unique repository counts by repository age (in years) for disengagement and engagement events
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Create output directory
os.makedirs('../figures/final_figure', exist_ok=True)

# =============================================================================
# Part 1: DATA LOADING AND PREPROCESSING
# =============================================================================

print("Loading and preprocessing data...")

# Load data files
try:
    repo_info = pd.read_csv('../data/sample_projects_total.csv')
    attritions_raw = pd.read_csv('../result/attritions.csv')
    newcomers_raw = pd.read_csv('../data/newcomer_contributors.csv')
    core_developer_list = pd.read_csv('../data/core_developer_list_total_repo.csv')
except FileNotFoundError as e:
    print(f"Error loading data: {e}")
    exit()

# Filter newcomers to only include core developers
print("Filtering for core developers...")
core_developer_list['core_developers'] = core_developer_list['core_developers'].apply(
    lambda x: x[1:-1].replace("'", "").split(', ')
)
core_developer_list = core_developer_list.explode('core_developers')
core_developer_list['core_developers'] = core_developer_list['core_developers'].str.strip()
newcomers_core = newcomers_raw[newcomers_raw['login'].isin(core_developer_list['core_developers'])].copy()

# Convert date columns
attritions_raw['attrition_date'] = pd.to_datetime(attritions_raw['attrition_date']).dt.tz_localize(None)
newcomers_core['date'] = pd.to_datetime(newcomers_core['date']).dt.tz_localize(None)
repo_info['createdAt'] = pd.to_datetime(repo_info['createdAt']).dt.tz_localize(None)

# =============================================================================
# Part 2: PREPARE DATA FOR HISTOGRAMS
# =============================================================================

print("Preparing data for histograms...")

# Calculate repository age in years for attrition events
attritions_with_age = pd.merge(
    attritions_raw, 
    repo_info[['name', 'createdAt']], 
    left_on='repo_name', 
    right_on='name', 
    how='left'
)
attritions_with_age['repo_age_years'] = (
    attritions_with_age['attrition_date'] - attritions_with_age['createdAt']
).dt.days / 365.25

# Calculate repository age in years for engagement events
newcomers_with_age = pd.merge(
    newcomers_core, 
    repo_info[['name', 'createdAt']], 
    left_on='repo_name', 
    right_on='name', 
    how='left'
)
newcomers_with_age['repo_age_years'] = (
    newcomers_with_age['date'] - newcomers_with_age['createdAt']
).dt.days / 365.25

# Remove any invalid ages (negative values)
attritions_with_age = attritions_with_age[attritions_with_age['repo_age_years'] >= 0]
newcomers_with_age = newcomers_with_age[newcomers_with_age['repo_age_years'] >= 0]

# =============================================================================
# Part 3: HISTOGRAM 1 - CONTRIBUTOR COUNTS BY REPOSITORY AGE
# =============================================================================

print("Creating contributor counts histogram...")

# Prepare data for plotting
attrition_ages = attritions_with_age['repo_age_years'].values
engagement_ages = newcomers_with_age['repo_age_years'].values

# Create figure
fig, ax = plt.subplots(1, 1, figsize=(12, 8))

# Set up bins (0-20 years, 1-year bins)
bins = np.arange(0, 21, 1)

# Create histograms
ax.hist(attrition_ages, bins=bins, alpha=0.7, color='tab:red', label='Disengagement', edgecolor='black')
ax.hist(engagement_ages, bins=bins, alpha=0.7, color='tab:blue', label='Engagement', edgecolor='black')

# Customize plot
ax.set_xlabel('Repository Age (years)', fontsize=14, weight='bold')
ax.set_ylabel('Number of Contributors', fontsize=14, weight='bold')
ax.set_title('Distribution of Contributors by Repository Age at Event Time', fontsize=16, weight='bold')
ax.legend(fontsize=12)
ax.grid(True, alpha=0.3)
ax.set_xticks(bins[::2])

# Add statistics text
attrition_stats = f"Disengagement: n={len(attrition_ages)}, mean={attrition_ages.mean():.1f} years, median={np.median(attrition_ages):.1f} years"
engagement_stats = f"Engagement: n={len(engagement_ages)}, mean={engagement_ages.mean():.1f} years, median={np.median(engagement_ages):.1f} years"

stats_text = f"{attrition_stats}\n{engagement_stats}"
ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10, verticalalignment='top',
        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

plt.tight_layout()
output_path1 = '../figures/final_figure/contributor_age_histogram.png'
plt.savefig(output_path1, dpi=300, bbox_inches='tight')
print(f"Saved contributor age histogram to: {output_path1}")
plt.close()

# =============================================================================
# Part 4: HISTOGRAM 2 - UNIQUE REPOSITORY COUNTS BY REPOSITORY AGE
# =============================================================================

print("Creating unique repository counts histogram...")

# Get unique repositories for each event type
attrition_repos = attritions_with_age[['repo_name', 'repo_age_years']].drop_duplicates()
engagement_repos = newcomers_with_age[['repo_name', 'repo_age_years']].drop_duplicates()

# Prepare data for plotting
attrition_repo_ages = attrition_repos['repo_age_years'].values
engagement_repo_ages = engagement_repos['repo_age_years'].values

# Create figure
fig, ax = plt.subplots(1, 1, figsize=(12, 8))

# Create histograms
ax.hist(attrition_repo_ages, bins=bins, alpha=0.7, color='tab:red', label='Disengagement', edgecolor='black')
ax.hist(engagement_repo_ages, bins=bins, alpha=0.7, color='tab:blue', label='Engagement', edgecolor='black')

# Customize plot
ax.set_xlabel('Repository Age (years)', fontsize=14, weight='bold')
ax.set_ylabel('Number of Unique Repositories', fontsize=14, weight='bold')
ax.set_title('Distribution of Unique Repositories by Age at Event Time', fontsize=16, weight='bold')
ax.legend(fontsize=12)
ax.grid(True, alpha=0.3)
ax.set_xticks(bins[::2])

# Add statistics text
attrition_repo_stats = f"Disengagement: n={len(attrition_repo_ages)}, mean={attrition_repo_ages.mean():.1f} years, median={np.median(attrition_repo_ages):.1f} years"
engagement_repo_stats = f"Engagement: n={len(engagement_repo_ages)}, mean={engagement_repo_ages.mean():.1f} years, median={np.median(engagement_repo_ages):.1f} years"

repo_stats_text = f"{attrition_repo_stats}\n{engagement_repo_stats}"
ax.text(0.02, 0.98, repo_stats_text, transform=ax.transAxes, fontsize=10, verticalalignment='top',
        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

plt.tight_layout()
output_path2 = '../figures/final_figure/repository_age_histogram.png'
plt.savefig(output_path2, dpi=300, bbox_inches='tight')
print(f"Saved repository age histogram to: {output_path2}")
plt.close()

# =============================================================================
# Part 5: ADDITIONAL ANALYSIS - OVERLAPPING REPOSITORIES
# =============================================================================

print("\nAdditional Analysis:")
print("=" * 50)

# Check for overlapping repositories
attrition_unique_repos = set(attrition_repos['repo_name'])
engagement_unique_repos = set(engagement_repos['repo_name'])
overlap_repos = attrition_unique_repos.intersection(engagement_unique_repos)

print(f"Total unique repositories with disengagement events: {len(attrition_unique_repos)}")
print(f"Total unique repositories with engagement events: {len(engagement_unique_repos)}")
print(f"Repositories with both disengagement and engagement events: {len(overlap_repos)}")

# Age distribution summary
print(f"\nAge distribution summary:")
print(f"Disengagement - Min: {attrition_ages.min():.1f} years, Max: {attrition_ages.max():.1f} years")
print(f"Engagement - Min: {engagement_ages.min():.1f} years, Max: {engagement_ages.max():.1f} years")

print("\nAnalysis complete! Both histograms have been generated and saved.")