import pandas as pd
import numpy as np
from scipy.optimize import curve_fit
from statsmodels.nonparametric.smoothers_lowess import lowess
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import logging
import time
import multiprocessing
import warnings
import gc

# 忽略警告
warnings.filterwarnings('ignore')

# 设置日志 - 直接输出到控制台
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - Line %(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# ATTRITION_LIMITS = [180, 270, 450]
ATTRITION_LIMITS = [365, 180, 270, 450]

# 缓存机制
_commit_cache = {}

def get_processed_commit_file_repo_name(repo_name):
    """获取处理后的commit文件，带缓存"""
    if repo_name in _commit_cache:
        return _commit_cache[repo_name]
    
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
    
    _commit_cache[repo_name] = repo_commit
    return repo_commit

def clear_repo_cache(repo_name):
    """清除特定仓库的缓存"""
    global _commit_cache
    if repo_name in _commit_cache:
        del _commit_cache[repo_name]
        logging.debug(f"Cleared cache for repository: {repo_name}")

def clear_all_cache():
    """清除所有缓存"""
    global _commit_cache
    _commit_cache.clear()
    gc.collect()  # 强制垃圾回收
    logging.info("Cleared all commit cache and performed garbage collection")

def calculate_newcomers_for_burst(repo_name, burst_data, commits):
    """计算特定burst期间的新来者数量 - 与原始代码完全一致"""
    try:
        # 获取最后一个attrition日期 - 与原始代码一致
        event_date = pd.to_datetime(burst_data['attrition_time'].iloc[-1])
        
        # 选择84天内的提交 - 与原始代码一致
        commits_within_84_days = commits[
            (commits['date'] >= pd.Timestamp(event_date).tz_localize('UTC')) & 
            (commits['date'] <= pd.Timestamp(event_date).tz_localize('UTC') + pd.Timedelta(days=84))
        ]
        
        # 获取attrition之前的提交
        commits_before_attrition = commits[commits['date'] < pd.Timestamp(event_date).tz_localize('UTC')]
        
        # 找出新来者 - 与原始代码一致
        newcomers = commits_within_84_days[
            ~commits_within_84_days['author_login'].isin(commits_before_attrition['author_login'])
        ]
        
        return len(newcomers)
        
    except Exception as e:
        logging.error(f"Error calculating newcomers for burst in {repo_name}: {e}")
        return 0

def process_repo_for_limit(repo, attritions_df, limit):
    """处理单个仓库的newcomers计算，growth_phase设为unknown"""
    results = []
    logging.info(f"Processing repository: {repo} for limit {limit}")
    
    # 过滤该仓库的attrition记录
    repo_attrition = attritions_df[attritions_df['repo_name'] == repo]
    if repo_attrition.empty:
        logging.warning(f"Repository {repo} has no attrition records for limit {limit}. Skipping.")
        return results

    try:
        # 获取原始commit数据用于newcomer计算
        raw_commits = get_processed_commit_file_repo_name(repo)
        raw_commits['date'] = pd.to_datetime(raw_commits['date'])
        
        # 获取该仓库的所有burst
        repo_bursts = repo_attrition['burst'].unique()
        
        for burst in repo_bursts:
            burst_data = repo_attrition[repo_attrition['burst'] == burst]
            
            # 计算newcomers
            num_newcomers = calculate_newcomers_for_burst(repo, burst_data, raw_commits)
            
            # 为burst中的所有记录添加结果，growth_phase设为"unknown"
            for idx, row in burst_data.iterrows():
                results.append((row['index'], "unknown", num_newcomers))
        
        # 处理完成后清除该仓库的缓存
        clear_repo_cache(repo)
        
    except Exception as e:
        logging.error(f"Error processing repository {repo} for limit {limit}: {e}")
        # 为所有记录设置默认值
        for idx, row in repo_attrition.iterrows():
            results.append((row['index'], "unknown", 0))
        # 即使出错也要清除缓存
        clear_repo_cache(repo)
    
    logging.info(f"Completed processing repository {repo} for limit {limit}")
    return results

def process_attrition_limit(limit):
    """处理特定limit的attrition数据，添加growth_phase和num_newcomers列"""
    start_time = time.time()
    logging.info(f"Starting processing for attrition limit: {limit}")
    
    # 定义文件路径
    input_file = f"../data/attrition_csv/attrition_burst_core_dev_merged_{limit}.csv"
    output_file = f"../data/attrition_csv/attritions_add_all_variable_{limit}.csv"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        logging.error(f"Input file not found: {input_file}")
        return
    
    # 读取attrition数据
    logging.info(f"Reading input file: {input_file}")
    try:
        attritions = pd.read_csv(input_file)
        logging.info(f"Successfully loaded {len(attritions)} records from {input_file}")
    except Exception as e:
        logging.error(f"Error reading file {input_file}: {e}")
        return
    
    # 重置索引以跟踪原始行索引
    attritions.reset_index(inplace=True)
    
    # 获取唯一仓库列表
    repo_list = attritions['repo_name'].unique()
    logging.info(f"Found {len(repo_list)} unique repositories to process")
    
    # 使用多线程处理每个仓库
    results_all = []
    
    # 计算最优线程数 - 优化线程数
    optimal_workers = min(80, multiprocessing.cpu_count() * 4, len(repo_list))
    
    with ThreadPoolExecutor(max_workers=optimal_workers) as executor:
        future_to_repo = {executor.submit(process_repo_for_limit, repo, attritions, limit): repo for repo in repo_list}
        
        completed = 0
        for future in as_completed(future_to_repo):
            repo = future_to_repo[future]
            completed += 1
            
            if completed % 10 == 0 or completed == len(repo_list):
                logging.info(f"Progress: {completed}/{len(repo_list)} repositories processed ({completed/len(repo_list)*100:.1f}%)")
            
            try:
                repo_results = future.result()
                results_all.extend(repo_results)
            except Exception as exc:
                logging.error(f"Error processing repository {repo}: {exc}")
    
    # 更新attritions DataFrame
    logging.info("Starting to update the attritions DataFrame with growth_phase and num_newcomers")
    
    # 创建字典进行快速查找和更新
    growth_phase_dict = {}
    num_newcomers_dict = {}
    
    for idx, growth_phase, num_newcomers in results_all:
        growth_phase_dict[idx] = growth_phase
        num_newcomers_dict[idx] = num_newcomers
    
    # 使用向量化操作进行批量更新
    attritions['growth_phase'] = attritions['index'].map(growth_phase_dict)
    attritions['num_newcomers'] = attritions['index'].map(num_newcomers_dict)
    
    # 删除临时'index'列
    attritions.drop(columns=['index'], inplace=True)
    
    # 保存更新后的结果
    logging.info(f"Saving the updated attritions data to: {output_file}")
    attritions.to_csv(output_file, index=False)
    
    # 清理内存
    del results_all, growth_phase_dict, num_newcomers_dict
    gc.collect()
    
    processing_time = time.time() - start_time
    logging.info(f"Completed processing for limit {limit} in {processing_time:.2f} seconds")
    logging.info(f"Final dataset contains {len(attritions)} records with growth_phase and num_newcomers")
    
    # 处理完一个limit后清除所有缓存
    clear_all_cache()

def main():
    """主函数，处理所有attrition limits"""
    overall_start_time = time.time()
    logging.info("=" * 60)
    logging.info("STARTING NEWCOMERS ADDITION (GROWTH PHASE SET TO UNKNOWN)")
    logging.info("=" * 60)
    
    total_limits = len(ATTRITION_LIMITS)
    
    for limit_idx, limit in enumerate(ATTRITION_LIMITS, 1):
        logging.info("-" * 40)
        logging.info(f"PROCESSING LIMIT {limit_idx}/{total_limits}: {limit}")
        logging.info("-" * 40)
        
        process_attrition_limit(limit)
        
        # 计算进度百分比
        progress = (limit_idx / total_limits) * 100
        logging.info(f"Overall progress: {progress:.1f}% ({limit_idx}/{total_limits} limits completed)")
        
        # 每个limit处理完后强制垃圾回收
        gc.collect()
    
    overall_processing_time = time.time() - overall_start_time
    logging.info("=" * 60)
    logging.info("NEWCOMERS ADDITION COMPLETED")
    logging.info(f"Total processing time: {overall_processing_time:.2f} seconds")
    logging.info("=" * 60)
    
    # 最终清理缓存释放内存
    clear_all_cache()

if __name__ == "__main__":
    logging.info(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    main()
    logging.info(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}") 