import pandas as pd
import numpy as np



productivity = pd.read_csv('../data/2025_0312_productivity.csv')

global_min_time = pd.Timestamp('2010-08-30 00:00:00')

productivity['datetime'] = pd.to_datetime(productivity['datetime'])
# 优化2: 直接使用更小的整数类型
productivity['standardized_time_weeks'] = ((productivity["datetime"] - global_min_time).dt.days // 7).astype('int')

# 优化3: 避免使用低效的apply，采用向量化操作
group_keys = ['repo_name', 'standardized_time_weeks']
metrics = ['pr_throughput', 'pull_request_success_rate', 'time_to_merge']

# 找到每个组中日期最新的行索引
latest_indices = productivity.groupby(group_keys)['datetime'].idxmax()

# 获取这些行作为基础结果
result = productivity.loc[latest_indices].copy()

# 优化4: 为每个指标高效更新最大值
for metric in metrics:
    # 过滤出有效的非NA和正值行
    valid_data = productivity.dropna(subset=[metric])
    valid_data = valid_data[valid_data[metric] > 0]
    
    if not valid_data.empty:
        # 计算每个组的最大值
        max_values = valid_data.groupby(group_keys, observed=True)[metric].max()
        
        # 临时设置索引以便高效更新
        result_indexed = result.set_index(group_keys)
        
        # 找到需要更新的索引交集
        common_idx = max_values.index.intersection(result_indexed.index)
        
        if not common_idx.empty:
            # 更新值
            result_indexed.loc[common_idx, metric] = max_values.loc[common_idx]
        
        # 重置索引
        result = result_indexed.reset_index()

# 保存结果
result.to_csv('../data/2025_0312_productivity_weekly_with_control_variables.csv', index=False)