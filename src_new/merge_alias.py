import pandas as pd
from difflib import SequenceMatcher
from typing import List, <PERSON><PERSON>, Set
import numpy as np

def get_similarity(a: str, b: str) -> float:
    """计算两个字符串的相似度"""
    if pd.isna(a) or pd.isna(b):
        return 0.0
    return SequenceMatcher(None, str(a).lower(), str(b).lower()).ratio()

def create_identity_tuple(row: pd.Series) -> Tuple[str, str, str]:
    """创建身份标识元组"""
    return (
        str(row['author_name']) if pd.notna(row['author_name']) else '',
        str(row['author_login']) if pd.notna(row['author_login']) else '',
        str(row['author_email']) if pd.notna(row['author_email']) else ''
    )

def merge_groups(groups: List[Set[Tuple[str, str, str]]]) -> List[Set[Tuple[str, str, str]]]:
    """合并有重叠的组"""
    merged = True
    while merged:
        merged = False
        new_groups = []
        used = set()
        
        for i, group1 in enumerate(groups):
            if i in used:
                continue
                
            current_group = group1.copy()
            used.add(i)
            
            for j, group2 in enumerate(groups[i+1:], i+1):
                if j in used:
                    continue
                    
                if any(any(t1[k] == t2[k] and t1[k] != '' for k in range(3))
                       for t1 in current_group for t2 in group2):
                    current_group.update(group2)
                    used.add(j)
                    merged = True
                    
            new_groups.append(current_group)
            
        groups = new_groups
        
    return groups

def merge_alias_commit_from_tuple(repo_name: str) -> pd.DataFrame:
    """
    合并来自同一作者但使用不同标识的提交记录
    
    参数:
        repo_name: 仓库名称
        
    返回:
        处理后的DataFrame，包含合并后的作者别名
    """
    # 读取提交数据
    file_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    df = pd.read_csv(file_path)
    
    # 第一轮：基于元组匹配
    identity_tuples = df.apply(create_identity_tuple, axis=1)
    unique_tuples = set(identity_tuples)
    
    # 初始化组
    groups = [{t} for t in unique_tuples]
    merged_groups = merge_groups(groups)
    
    # 为每个组创建唯一标识符
    group_mapping = {}
    for i, group in enumerate(merged_groups):
        for identity in group:
            group_mapping[identity] = f"author_{i}"
    
    # 添加别名列
    df['alias'] = identity_tuples.map(lambda x: group_mapping.get(x, 'unknown'))
    
    # 填充缺失的登录信息
    for alias in df['alias'].unique():
        mask = df['alias'] == alias
        valid_logins = df.loc[mask, 'author_login'].dropna()
        if not valid_logins.empty:
            df.loc[mask & df['author_login'].isna(), 'author_login'] = valid_logins.iloc[0]
    
    # 第二轮：基于相似度匹配
    # 创建作者标识符（名字+邮箱）
    df['author_identifier'] = df['author_name'].fillna('') + ' ' + df['author_email'].fillna('')
    
    # 获取唯一的标识符
    unique_identifiers = df['author_identifier'].unique()
    
    # 构建相似度矩阵
    n = len(unique_identifiers)
    similarity_matrix = np.zeros((n, n))
    
    for i in range(n):
        for j in range(i+1, n):
            similarity = get_similarity(unique_identifiers[i], unique_identifiers[j])
            similarity_matrix[i, j] = similarity
            similarity_matrix[j, i] = similarity
    
    # 找到相似度高的对
    similar_pairs = []
    for i in range(n):
        for j in range(i+1, n):
            if similarity_matrix[i, j] > 0.9:
                similar_pairs.append((unique_identifiers[i], unique_identifiers[j]))
    
    # 基于相似度合并别名
    for id1, id2 in similar_pairs:
        alias1 = df.loc[df['author_identifier'] == id1, 'alias'].iloc[0]
        alias2 = df.loc[df['author_identifier'] == id2, 'alias'].iloc[0]
        if alias1 != alias2:
            df.loc[df['alias'] == alias2, 'alias'] = alias1
    
    # 再次填充缺失的登录信息
    for alias in df['alias'].unique():
        mask = df['alias'] == alias
        valid_logins = df.loc[mask, 'author_login'].dropna()
        if not valid_logins.empty:
            df.loc[mask & df['author_login'].isna(), 'author_login'] = valid_logins.iloc[0]
    
    # 删除临时列
    df = df.drop('author_identifier', axis=1)
    
    return df
