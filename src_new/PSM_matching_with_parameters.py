from random import sample
import pandas as pd
import os
import logging
import numpy as np
from sklearn.neighbors import NearestNeighbors
import gc
import psutil
import pickle
import argparse
from typing import Dict, List, Set, Tuple, Generator

def setup_logging(log_dir="../logs"):
    os.makedirs(log_dir, exist_ok=True)
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "psm_matching_2025_0408.log")),
            logging.StreamHandler(),
        ],
    )

def memory_monitor(func):
    """监控函数执行期间的内存使用情况的装饰器"""
    def wrapper(*args, **kwargs):
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        result = func(*args, **kwargs)
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        logging.info(f"Memory usage: {memory_after-memory_before:.2f}MB")
        return result
    return wrapper

def get_control_candidates(
    control_data: pd.DataFrame,
    treatment_time: float,
    time_window: float = 3.0
) -> pd.DataFrame:
    """获取时间窗口内的控制组候选"""
    mask = (
        (control_data["standardized_time_weeks"] >= treatment_time - time_window) &
        (control_data["standardized_time_weeks"] <= treatment_time + time_window)
    )
    return control_data[mask]

def batch_process(
    array: np.ndarray,
    batch_size: int = 1000
) -> Generator[np.ndarray, None, None]:
    """批量处理数组的生成器"""
    for i in range(0, len(array), batch_size):
        yield array[i:i + batch_size]

def save_cache(matched_pairs, processed_items, cache_file):
    """保存当前匹配状态到缓存文件"""
    cache_data = {
        'matched_pairs': matched_pairs,
        'processed_items': processed_items
    }
    with open(cache_file, 'wb') as f:
        pickle.dump(cache_data, f)
    logging.info(f"Saved cache to {cache_file}, matched pairs: {len(matched_pairs)}")

def load_cache(cache_file):
    """加载缓存文件中的匹配状态"""
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            logging.info(f"Loaded cache from {cache_file}, matched pairs: {len(cache_data['matched_pairs'])}")
            return cache_data
        except Exception as e:
            logging.error(f"Error loading cache: {str(e)}")
    return None

@memory_monitor
def compile_control_group_psm_knn(
    treatment_repos_with_left_date: pd.DataFrame,
    candidate_repos: Set[str],
    productivity_metric_data: pd.DataFrame,
    n_neighbors: int,
    timewindow_weeks: int,
    feature_columns: List[str],
    extra_candidates: int = 10,
    batch_size: int = 1000,
    cache_interval: int = 1000,
    window_size: int = 12,
    feature_column_name: str = "pull_request_success_rate"
) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
    """
    优化后的PSM-KNN匹配实现，支持缓存和断点继续
    - 简化处理逻辑，直接处理每个(burst, repo)组合
    - 使用生成器减少内存占用
    - 批量处理大型矩阵计算
    - 及时清理不需要的数据
    - 每匹配1000个treatment group缓存一次
    """
    logging.info(f"Starting optimized PSM with NearestNeighbors for window_size={window_size} and feature={feature_column_name}...")
    
    # 缓存文件路径
    cache_file = os.path.join(cache_dir, f"psm_matching_cache_window{window_size}_{feature_column_name}.pkl")
    
    # 尝试加载缓存
    cache_data = load_cache(cache_file)
    matched_pairs = {}
    processed_items = set()
    processed_count = 0
    
    if cache_data:
        matched_pairs = cache_data['matched_pairs']
        processed_items = cache_data.get('processed_items', set())
        processed_count = len(matched_pairs)
        logging.info(f"Resuming from cache with {len(matched_pairs)} matched pairs")
    
    # 预处理控制组数据
    available_controls = set(candidate_repos)
    control_data = productivity_metric_data[
        productivity_metric_data["repo_name"].isin(available_controls)
    ].copy()
    
    # 预计算控制组离职时间
    control_treatment_weeks = (
        productivity_metric_data[
            (productivity_metric_data["someone_left"] == 1) &
            (productivity_metric_data["repo_name"].isin(available_controls))
        ]
        .groupby("repo_name")["standardized_time_weeks"]
        .apply(sorted)
        .to_dict()
    )

    # 优化：创建(burst, repo_name)作为唯一标识
    treatment_repos_with_left_date['item_key'] = treatment_repos_with_left_date['burst'].astype(str) + '_' + treatment_repos_with_left_date['repo_name']
    
    # 跳过已处理的项目
    treatment_repos_to_process = treatment_repos_with_left_date[~treatment_repos_with_left_date['item_key'].isin(processed_items)]
    total_items = len(treatment_repos_to_process)
    logging.info(f"Total items to process: {total_items}")
    
    # Initialize treatment_features as an empty DataFrame with the right columns
    # This will be used if no processing is needed (all items in cache)
    treatment_features = pd.DataFrame(columns=productivity_metric_data.columns)

    # 直接处理每个(burst, repo)组合，简化嵌套循环
    for i, row in treatment_repos_to_process.iterrows():
        burst = row['burst']
        t_repo = row['repo_name']
        item_key = row['item_key']
        
        if item_key in processed_items:
            continue
            
        # 获取treatment数据
        treatment_mask = (
            (productivity_metric_data["repo_name"] == t_repo) &
            (productivity_metric_data["someone_left"] == 1) &
            (productivity_metric_data["burst"] == burst)
        )
        treatment_features = productivity_metric_data[treatment_mask]
        
        if treatment_features.empty:
            logging.warning(f"No treatment features found for burst:{burst} repo:{t_repo}")
            processed_items.add(item_key)
            continue

        # 只需要一行数据，通常是最早的离职时间点
        t_row = treatment_features.iloc[0]
        t_time = t_row["standardized_time_weeks"]
            
        # 获取时间窗口内的控制组候选
        control_candidates = get_control_candidates(control_data, t_time, timewindow_weeks)
        if len(control_candidates) < n_neighbors:
            logging.warning(f"Insufficient candidates for burst:{burst} repo:{t_repo} at time {t_time}")
            processed_items.add(item_key)
            continue

        # 移除NaN值并构建特征矩阵
        control_candidates = control_candidates.dropna(subset=feature_columns)
        if control_candidates.empty:
            logging.warning(f"No valid control candidates for burst:{burst} repo:{t_repo}")
            processed_items.add(item_key)
            continue
            
        logging.info(f"Processing burst:{burst} repo:{t_repo} in time:{t_time} with {len(control_candidates)} candidates ({processed_count + 1}/{total_items})")
        X_control = control_candidates[feature_columns].values
        X_treatment = t_row[feature_columns].values.reshape(1, -1)

        # KNN匹配
        nn_model = NearestNeighbors(
            n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
            algorithm='auto'
        )
        nn_model.fit(X_control)
        
        matched_controls = []
        used_repos = set()

        # 获取近邻
        distances, indices = nn_model.kneighbors(
            X_treatment,
            n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
            return_distance=True
        )

        # 处理匹配结果
        for idx in indices[0]:
            if len(matched_controls) >= n_neighbors:
                break

            control_row = control_candidates.iloc[idx]
            control_repo = control_row["repo_name"]
            control_time = control_row["standardized_time_weeks"]

            # 跳过已使用的仓库
            if control_repo in used_repos:
                continue

            # 验证时间窗口约束
            treatment_weeks = control_treatment_weeks.get(control_repo, [])
            if treatment_weeks:
                window_start = control_time - timewindow_weeks
                window_end = control_time + timewindow_weeks
                import bisect
                left = bisect.bisect_left(treatment_weeks, window_start)
                right = bisect.bisect_right(treatment_weeks, window_end)
                if left < right:
                    continue

            matched_controls.append({
                "repo_name": control_repo,
                "matched_time": control_time,
                "features": control_row[feature_columns].values
            })
            used_repos.add(control_repo)

        # 保存匹配结果
        if matched_controls:
            matched_pairs[item_key] = {
                "burst": burst,
                "repo_name": t_repo,
                "treatment_time": t_time,
                "controls": matched_controls,
                "treatment_features": t_row[feature_columns].values,
                "control_features": np.array([c["features"] for c in matched_controls])
            }
            processed_count += 1
            logging.info(f"Matched burst:{burst} repo:{t_repo} with {len(matched_controls)} controls. Total: {processed_count}")
        else:
            logging.warning(f"No valid controls for burst:{burst} repo:{t_repo}")
            
        processed_items.add(item_key)
        
        # 定期缓存匹配结果
        if processed_count > 0 and processed_count % cache_interval == 0:
            save_cache(matched_pairs, processed_items, cache_file)
        
        # 定期清理内存
        if i % 1000 == 0:
            gc.collect()

    # 保存最终的匹配结果
    save_cache(matched_pairs, processed_items, cache_file)
    logging.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")
    return matched_pairs, treatment_features, control_data

def compile_data_from_matched_pairs(
    matched_pairs: Dict,
    productivity: pd.DataFrame,
    window_size: int,
    batch_size: int = 1000,
    log_interval: int = 1000
) -> pd.DataFrame:
    """从匹配结果编译数据 - 优化版本"""
    logging.info(f"Starting compilation of {len(matched_pairs)} matched pairs")
    
    # 为提高性能, 先对productivity按repo_name进行分组
    repo_groups = dict(tuple(productivity.groupby('repo_name')))
    
    # 批处理键值以减少内存使用
    all_keys = list(matched_pairs.keys())
    total_batches = (len(all_keys) + batch_size - 1) // batch_size
    
    all_data = []
    processed = 0
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_keys))
        batch_keys = all_keys[start_idx:end_idx]
        
        batch_data = []
        
        for item_key in batch_keys:
            matched_data = matched_pairs[item_key]
            burst_id = matched_data["burst"]
            repo_name = matched_data["repo_name"]
            treatment_time = matched_data["treatment_time"]
            control_groups = matched_data["controls"]
            
            # 只在repo_groups里有数据时处理
            if repo_name in repo_groups:
                # 处理treatment数据 - 使用预先分组的数据
                repo_data = repo_groups[repo_name]
                treatment_mask = (
                    (repo_data['standardized_time_weeks'] >= treatment_time - window_size) & 
                    (repo_data['standardized_time_weeks'] <= treatment_time + window_size)
                )
                treatment_productivity = repo_data[treatment_mask].copy()
                
                if not treatment_productivity.empty:
                    treatment_productivity['relativized_time'] = treatment_productivity['standardized_time_weeks'] - treatment_time
                    treatment_productivity['is_treated'] = 1
                    treatment_productivity['post_treatment'] = treatment_productivity['relativized_time'] > 0
                    treatment_productivity['cohort_id'] = processed
                    batch_data.append(treatment_productivity)
                
                    # 处理control数据
                    for c in control_groups:
                        control_repo = c['repo_name']
                        control_time = c['matched_time']
                        
                        if control_repo in repo_groups:
                            control_repo_data = repo_groups[control_repo]
                            control_mask = (
                                (control_repo_data['standardized_time_weeks'] >= control_time - window_size) & 
                                (control_repo_data['standardized_time_weeks'] <= control_time + window_size)
                            )
                            control_data = control_repo_data[control_mask].copy()
                            
                            if not control_data.empty:
                                control_data['relativized_time'] = control_data['standardized_time_weeks'] - control_time
                                control_data['is_treated'] = 0
                                control_data['post_treatment'] = control_data['relativized_time'] > 0
                                control_data['cohort_id'] = processed
                                batch_data.append(control_data)
                    
                    processed += 1
            
            # 每处理一定数量记录日志
            if processed % log_interval == 0:
                logging.info(f"Processed {processed}/{len(matched_pairs)} matched pairs")
        
        # 合并当前批次并添加到结果
        if batch_data:
            batch_result = pd.concat(batch_data)
            all_data.append(batch_result)
            
            # 清理内存
            del batch_data
            gc.collect()
            
        logging.info(f"Completed batch {batch_idx+1}/{total_batches}, total processed: {processed}")
    
    # 合并所有批次数据
    if all_data:
        logging.info(f"Concatenating all {len(all_data)} batches")
        final_data = pd.concat(all_data)
        logging.info(f"Final compiled data shape: {final_data.shape}")
        return final_data
    else:
        logging.warning("No data to compile")
        return pd.DataFrame()

def main(window_size, feature_column_name):
    log_dir = "../logs"
    # output_dir = "../result/did_result_20250312/"
    output_dir = "../result/did_result_20250408/"
    global cache_dir
    cache_dir = "../cache/"
    
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(cache_dir, exist_ok=True)
    setup_logging(log_dir)
    
    # Read the data
    # productivity = pd.read_csv('../result/did_result_20250312/productivity_20250312_with_propensity_scores_with_attritions.csv')
    productivity = pd.read_csv('../result/did_result_20250408/productivity_20250408_with_propensity_scores_with_attritions.csv')
    productivity["standardized_time_weeks"] = productivity["standardized_time_weeks"].astype(int)

    repo_name = productivity['repo_name'].unique()
    productivity_week = productivity.copy()
    # Sort and filter the productivity data
    productivity_week = productivity_week.sort_values(by=['repo_name', 'datetime'], ascending=False)
    productivity_week = productivity_week.drop_duplicates(subset=['repo_name', 'standardized_time_weeks'])
    productivity_week = productivity_week.drop(columns=['datetime'])
    
    attritions = productivity[productivity['someone_left'] == 1].copy()
    attritions = attritions[attritions[f'feature_sigmod_{window_size}_{feature_column_name}'].notnull()]
    attritions = attritions[attritions[f'feature_sigmod_{window_size}_{feature_column_name}'] > 0.5]
    print(f"In window {window_size}, attritions shape is {attritions.shape}")
    
    # 转换类型
    if 'burst' in attritions.columns:
        attritions['burst'] = attritions['burst'].astype(int)

    # 调用支持缓存的 PSM 函数进行匹配
    matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
        attritions,
        set(productivity['repo_name']),
        productivity,
        n_neighbors=5,
        timewindow_weeks=window_size,
        feature_columns=[f'feature_sigmod_{window_size}_{feature_column_name}'],
        cache_interval=1000,
        window_size=window_size,
        feature_column_name=feature_column_name
    )

    logging.info(f"Finished PSM matching with {len(matched_pairs)} matched pairs, starting to compile data...")
    
    # 使用独立函数编译数据
    compiled_data = compile_data_from_matched_pairs(matched_pairs, productivity, window_size)
    
    if not compiled_data.empty:
        compiled_data_test = compiled_data.copy()
        compiled_data_test['is_post_treatment'] = compiled_data_test['post_treatment'].astype(int)
        compiled_data_test['is_treated'] = compiled_data_test['is_treated'].astype(int)
        compiled_data_test['is_treated_post_treatment'] = compiled_data_test['is_treated'] * compiled_data_test['post_treatment']

        output_file = os.path.join(output_dir, f"compiled_data_test_{window_size}_{feature_column_name}.csv")
        compiled_data_test.to_csv(output_file, index=False)
        logging.info(f"Compiled data saved to {output_file}")
    else:
        logging.warning(f"No compiled data for window {window_size}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run PSM matching analysis with specified parameters")
    parser.add_argument("--window_size", type=int, default=12, help="Window size for analysis (default: 12)")
    parser.add_argument("--feature", type=str, required=True, 
                        choices=["pull_request_success_rate", "time_to_merge"], 
                        help="Feature column name to use for matching")
    
    args = parser.parse_args()
    main(args.window_size, args.feature)