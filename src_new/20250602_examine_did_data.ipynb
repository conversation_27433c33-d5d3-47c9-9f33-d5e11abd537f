{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "      <td>1.421275</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "      <td>3.026300</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "      <td>3.619184</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 46 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        ...  decelerating       15.0       2.772589    6.751101   \n", "1        ...  decelerating       15.0       2.772589    6.751101   \n", "2        ...  decelerating       15.0       2.772589    6.751101   \n", "3        ...  decelerating       15.0       2.772589    6.751101   \n", "4        ...  decelerating       15.0       2.772589    6.751101   \n", "...      ...           ...        ...            ...         ...   \n", "3503016  ...        steady        1.0       0.693147    6.710523   \n", "3503017  ...        steady        1.0       0.693147    6.710523   \n", "3503018  ...        steady        1.0       0.693147    6.710523   \n", "3503019  ...        steady        1.0       0.693147    6.710523   \n", "3503020  ...        steady        1.0       0.693147    6.710523   \n", "\n", "        log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0                 0.144831     5.433722                        1.0   \n", "1                 0.144831     5.433722                        NaN   \n", "2                 0.144831     5.433722                        NaN   \n", "3                 0.144831     5.433722                        NaN   \n", "4                 0.144831     5.433722                        NaN   \n", "...                    ...          ...                        ...   \n", "3503016           0.018605     2.197225                        1.0   \n", "3503017           0.018605     2.197225                        NaN   \n", "3503018           0.018605     2.197225                        1.0   \n", "3503019           0.018605     2.197225                        1.0   \n", "3503020           0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge  log_time_to_merge  log_pull_request_success_rate  \n", "0                  NaN                NaN                       0.693147  \n", "1             169.8895           5.141017                            NaN  \n", "2                  NaN                NaN                            NaN  \n", "3                  NaN                NaN                            NaN  \n", "4                  NaN                NaN                            NaN  \n", "...                ...                ...                            ...  \n", "3503016         3.1424           1.421275                       0.693147  \n", "3503017            NaN                NaN                            NaN  \n", "3503018        19.6208           3.026300                       0.693147  \n", "3503019        36.3071           3.619184                       0.693147  \n", "3503020            NaN                NaN                            NaN  \n", "\n", "[3503021 rows x 46 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "did_data = pd.read_csv('../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity.csv')\n", "did_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Spearman correlation between commits and pr_throughput: 0.0896 (p-value: 0)\n"]}], "source": ["from scipy.stats import spearmanr\n", "\n", "# 计算commits和pr_throughput的spearman相关系数\n", "# 先去除缺失值\n", "valid = did_data[['commits', 'log_project_contributors_before_treatment']].dropna()\n", "corr, p_value = spearmanr(valid['commits'], valid['pr_throughput'])\n", "\n", "print(f\"Spearman correlation between commits and pr_throughput: {corr:.4f} (p-value: {p_value:.4g})\")\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}], "ref": "0ed950c6-9e97-4336-9683-5f0f1cf28e6a", "rows": [["0", "0", "01mf02/jaq", "2.4849066497880004"], ["1", "1", "05bit/peewee-async", "3.218875824868201"], ["2", "2", "05bit/peewee-async", "3.295836866004329"], ["3", "3", "0LNetworkCommunity/libra-legacy-v6", "4.976733742420574"], ["4", "4", "0b01/tectonicdb", "2.4849066497880004"], ["5", "5", "0b01001001/spectree", "3.367295829986474"], ["6", "6", "0chain/0chain", "2.833213344056216"], ["7", "7", "0chain/0chain", "3.218875824868201"], ["8", "8", "0lnetworkcommunity/libra", "4.997212273764115"], ["9", "9", "0rpc/zerorpc-python", "2.5649493574615367"], ["10", "10", "0rpc/zerorpc-python", "2.772588722239781"], ["11", "11", "0rpc/zerorpc-python", "3.258096538021482"], ["12", "12", "0x5bfa/FluentHub", "2.6390573296152584"], ["13", "13", "0x7c13/notepads", "3.610917912644224"], ["14", "14", "0xPolygonZero/plonky2", "2.995732273553991"], ["15", "15", "0xax/linux-insides", "5.135798437050262"], ["16", "16", "0xax/linux-insides", "5.356586274672012"], ["17", "17", "0xax/linux-insides", "5.438079308923196"], ["18", "18", "0xax/linux-insides", "5.480638923341991"], ["19", "19", "0xax/linux-insides", "5.497168225293202"], ["20", "20", "0xax/linux-insides", "5.62040086571715"], ["21", "21", "0xax/linux-insides", "5.6240175061873385"], ["22", "22", "0xfe/vextab", "2.079441541679836"], ["23", "23", "0xpolygon/polygon-edge", "2.302585092994046"], ["24", "24", "0xpolygon/polygon-edge", "3.1354942159291497"], ["25", "25", "0xpolygon/polygon-sdk", "2.302585092994046"], ["26", "26", "0xpolygon/polygon-sdk", "3.1354942159291497"], ["27", "27", "0xpolygonhermez/zkevm-node", "3.4011973816621555"], ["28", "28", "0xpolygonmiden/crypto", "2.079441541679836"], ["29", "29", "0xproject/0x-starter-project", "2.3978952727983707"], ["30", "30", "0xs34n/starknet.js", "3.332204510175204"], ["31", "31", "0xs34n/starknet.js", "3.871201010907891"], ["32", "32", "0xs34n/starknet.js", "4.219507705176107"], ["33", "33", "0xspaceshard/starknet-hardhat-plugin", "1.6094379124341005"], ["34", "34", "1024pix/pix", "4.07753744390572"], ["35", "35", "1024pix/pix", "4.442651256490317"], ["36", "36", "10bestdesign/jqvmap", "3.555348061489413"], ["37", "37", "10bestdesign/jqvmap", "3.737669618283368"], ["38", "38", "10cella/hashids-java", "2.4849066497880004"], ["39", "39", "10cella/hashids-java", "2.833213344056216"], ["40", "40", "10gic/vanitygen-plusplus", "3.871201010907891"], ["41", "41", "10up/10up-experience", "2.4849066497880004"], ["42", "42", "10up/10up-experience", "2.833213344056216"], ["43", "43", "10up/10up-experience", "3.1780538303479458"], ["44", "44", "10up/autoshare-for-twitter", "1.9459101490553128"], ["45", "45", "10up/classifai", "2.9444389791664403"], ["46", "46", "10up/classifai", "3.091042453358316"], ["47", "47", "10up/distributor", "3.4011973816621555"], ["48", "48", "10up/distributor", "3.828641396489095"], ["49", "49", "10up/distributor", "4.060443010546419"]], "shape": {"columns": 3, "rows": 70372}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cohort_id</th>\n", "      <th>repo_name</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>01mf02/jaq</td>\n", "      <td>2.484907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>3.218876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>3.295837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>4.976734</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>0b01/tectonicdb</td>\n", "      <td>2.484907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70367</th>\n", "      <td>70367</td>\n", "      <td>zzzprojects/entityframework-plus</td>\n", "      <td>2.890372</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70368</th>\n", "      <td>70368</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>1.386294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70369</th>\n", "      <td>70369</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>1.945910</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70370</th>\n", "      <td>70370</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>3.135494</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70371</th>\n", "      <td>70371</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>3.295837</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>70372 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       cohort_id                                   repo_name  \\\n", "0              0                                  01mf02/jaq   \n", "1              1                          05bit/peewee-async   \n", "2              2                          05bit/peewee-async   \n", "3              3          0LNetworkCommunity/libra-legacy-v6   \n", "4              4                             0b01/tectonicdb   \n", "...          ...                                         ...   \n", "70367      70367            zzzprojects/entityframework-plus   \n", "70368      70368  zzzprojects/entityframework.dynamicfilters   \n", "70369      70369  zzzprojects/entityframework.dynamicfilters   \n", "70370      70370               zzzprojects/html-agility-pack   \n", "70371      70371               zzzprojects/html-agility-pack   \n", "\n", "       log_project_contributors_before_treatment  \n", "0                                       2.484907  \n", "1                                       3.218876  \n", "2                                       3.295837  \n", "3                                       4.976734  \n", "4                                       2.484907  \n", "...                                          ...  \n", "70367                                   2.890372  \n", "70368                                   1.386294  \n", "70369                                   1.945910  \n", "70370                                   3.135494  \n", "70371                                   3.295837  \n", "\n", "[70372 rows x 3 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 先筛选some_left为1的行\n", "filtered = did_data[did_data['someone_left'] == 1]\n", "\n", "# 按cohort_id分组，取每组的repo_name和project_contributors数量\n", "result_df = filtered.groupby('cohort_id').agg({\n", "    'repo_name': 'first',  # 取每组的第一个repo_name\n", "    'log_project_contributors_before_treatment': 'first'  # 取每组的第一个project_contributors数量\n", "}).reset_index()\n", "\n", "result_df\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# 绘制项目贡献者数量的分布直方图\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(result_df['log_project_contributors_before_treatment'], bins=50, color='skyblue', edgecolor='black')\n", "plt.xlabel('Number of Project Contributors')\n", "plt.ylabel('Number of Projects')\n", "plt.title('Distribution of Project Contributors (someone_left == 1)')\n", "plt.grid(axis='y', alpha=0.75)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0th percentile: 0.69\n", "20th percentile: 2.56\n", "40th percentile: 3.04\n", "60th percentile: 3.56\n", "80th percentile: 4.26\n"]}], "source": ["percentiles = [i for i in range(0, 100, 20)]\n", "contributor_percentiles = result_df['log_project_contributors_before_treatment'].quantile([p/100 for p in percentiles])\n", "\n", "for p, value in zip(percentiles, contributor_percentiles):\n", "    print(f\"{p}th percentile: {value:.2f}\")\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2.56494936, 3.04452244, 3.55534806, 4.26267988])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["quantiles"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Group 1: 785076 rows exported.\n", "Group 2: 649871 rows exported.\n", "Group 3: 670514 rows exported.\n", "Group 4: 700876 rows exported.\n", "Group 5: 696684 rows exported.\n"]}], "source": ["# 计算分位点，将项目贡献者数量分为5组（每20%一组）\n", "quantiles = result_df['log_project_contributors_before_treatment'].quantile([0.2, 0.4, 0.6, 0.8]).values\n", "\n", "# 定义一个函数分组\n", "def assign_group(contributors):\n", "    if contributors <= quantiles[0]:\n", "        return 1\n", "    elif contributors <= quantiles[1]:\n", "        return 2\n", "    elif contributors <= quantiles[2]:\n", "        return 3\n", "    elif contributors <= quantiles[3]:\n", "        return 4\n", "    else:\n", "        return 5\n", "\n", "# 给每个cohort分组\n", "result_df['contributor_group'] = result_df['log_project_contributors_before_treatment'].apply(assign_group)\n", "\n", "# 遍历每组，提取对应cohort_id的所有did_data\n", "for group_num in range(1, 6):\n", "    group_cohorts = result_df[result_df['contributor_group'] == group_num]['cohort_id']\n", "    group_did_data = did_data[did_data['cohort_id'].isin(group_cohorts)]\n", "    group_did_data.to_csv(f'../result/did_result_20250408/did_data_group_{group_num}.csv', index=False)\n", "    print(f\"Group {group_num}: {len(group_did_data)} rows exported.\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["导出cohort_id对应的所有did_data原始数据，共3494971行。\n"]}], "source": ["# 提取project_contributors小于1000的repo_name对应的cohort_id，然后保存\n", "cohort_ids = result_df[result_df['project_contributors'] < 1000][['cohort_id', 'repo_name', 'project_contributors']]\n", "\n", "# 根据cohort_ids提取对应的did_data原始数据并保存\n", "filtered_did_data = did_data[did_data['cohort_id'].isin(cohort_ids['cohort_id'])]\n", "filtered_did_data.to_csv('../result/did_result_20250408/did_data_cohort_id_lt_1000.csv', index=False)\n", "print(f\"导出cohort_id对应的所有did_data原始数据，共{len(filtered_did_data)}行。\")\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["project_contributors < 1000 的cohort占比: 0.9977 (70211/70372)\n"]}], "source": ["# 计算project_contributors小于1000的cohort占总cohort的比例\n", "num_lt_1000 = (result_df['project_contributors'] < 1000).sum()\n", "total_cohorts = len(result_df)\n", "ratio = num_lt_1000 / total_cohorts\n", "print(f\"project_contributors < 1000 的cohort占比: {ratio:.4f} ({num_lt_1000}/{total_cohorts})\")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["导出project_contributors >= 1000的cohort_id对应的所有did_data原始数据，共8050行。\n"]}], "source": ["# 提取project_contributors大于等于1000的repo_name对应的cohort_id，然后保存\n", "cohort_ids_ge_1000 = result_df[result_df['project_contributors'] >= 1000][['cohort_id', 'repo_name', 'project_contributors']]\n", "\n", "# 根据cohort_ids_ge_1000提取对应的did_data原始数据并保存\n", "filtered_did_data_ge_1000 = did_data[did_data['cohort_id'].isin(cohort_ids_ge_1000['cohort_id'])]\n", "filtered_did_data_ge_1000.to_csv('../result/did_result_20250408/did_data_cohort_id_ge_1000.csv', index=False)\n", "print(f\"导出project_contributors >= 1000的cohort_id对应的所有did_data原始数据，共{len(filtered_did_data_ge_1000)}行。\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}