#!/usr/bin/env python3
"""
检查并优化生产力数据，确保 add_derived_variables 函数能正确处理包含 NaN 的数据
保持 NaN 值不变，不填充为 0
"""

import pandas as pd
import numpy as np
import sys
import os

def check_data_structure(file_path):
    """检查数据结构和缺失值情况"""
    print("=" * 60)
    print("数据结构检查")
    print("=" * 60)
    
    # 读取数据
    df = pd.read_csv(file_path)
    
    print(f"数据集形状: {df.shape}")
    print(f"列数: {len(df.columns)}")
    
    # 检查关键列是否存在
    required_columns = ['repo_name', 'standardized_time_weeks', 'pull_request_success_rate', 'time_to_merge']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺失关键列: {missing_columns}")
        return False
    else:
        print("✅ 所有关键列都存在")
    
    # 检查缺失值情况
    print("\n关键列的缺失值情况:")
    for col in required_columns:
        null_count = df[col].isnull().sum()
        null_percent = (null_count / len(df)) * 100
        print(f"  {col}: {null_count:,} ({null_percent:.2f}%)")
    
    # 检查数据类型
    print("\n数据类型:")
    for col in required_columns:
        print(f"  {col}: {df[col].dtype}")
    
    # 检查数据范围
    print("\n数据范围:")
    for col in ['pull_request_success_rate', 'time_to_merge']:
        if df[col].notna().any():
            print(f"  {col}: {df[col].min():.3f} 到 {df[col].max():.3f}")
        else:
            print(f"  {col}: 全部为 NaN")
    
    return True

def add_derived_variables_safe(df):
    """
    安全地添加派生变量，正确处理 NaN 值
    不填充 NaN 为 0，保持原始的缺失值
    """
    print("\n" + "=" * 60)
    print("添加派生变量（保持 NaN 值）")
    print("=" * 60)
    
    df_result = df.copy()
    
    # 添加 log 版本的变量，正确处理 NaN
    log_columns = ['tenure', 'commit_percent', 'commits', 'pull_request_success_rate', 'time_to_merge']
    existing_log_columns = [col for col in log_columns if col in df_result.columns]
    
    print(f"处理以下列的 log 变换: {existing_log_columns}")
    
    for col in existing_log_columns:
        log_col_name = f'log_{col}'
        
        # 只对非 NaN 且大于 0 的值进行 log 变换
        # 使用 np.where 来保持 NaN 值
        df_result[log_col_name] = np.where(
            df_result[col].notna() & (df_result[col] > 0),
            np.log(df_result[col] + 1),
            np.nan
        )
        
        # 统计结果
        original_nan = df_result[col].isnull().sum()
        new_nan = df_result[log_col_name].isnull().sum()
        print(f"  {col} -> {log_col_name}: 原始 NaN {original_nan:,}, 新 NaN {new_nan:,}")
    
    # 添加其他可能需要的派生变量
    if 'newcomers' in df_result.columns:
        df_result['log_newcomers'] = np.where(
            df_result['newcomers'].notna() & (df_result['newcomers'] > 0),
            np.log(df_result['newcomers'] + 1),
            np.nan
        )
        df_result['newcomers_bool'] = np.where(
            df_result['newcomers'].notna(),
            (df_result['newcomers'] > 0).astype(int),
            np.nan
        )
    
    print("✅ 派生变量添加完成")
    return df_result

def validate_derived_variables(df):
    """验证派生变量的正确性"""
    print("\n" + "=" * 60)
    print("验证派生变量")
    print("=" * 60)
    
    # 检查 log 变量
    log_pairs = [
        ('pull_request_success_rate', 'log_pull_request_success_rate'),
        ('time_to_merge', 'log_time_to_merge'),
        ('tenure', 'log_tenure'),
        ('commit_percent', 'log_commit_percent'),
        ('commits', 'log_commits')
    ]
    
    for original_col, log_col in log_pairs:
        if original_col in df.columns and log_col in df.columns:
            # 检查 NaN 值的一致性（原始为 NaN 或 0 的地方，log 版本应该也是 NaN）
            original_valid = df[original_col].notna() & (df[original_col] > 0)
            log_valid = df[log_col].notna()
            
            consistency_check = (original_valid == log_valid).all()
            
            print(f"  {original_col} <-> {log_col}: {'✅' if consistency_check else '❌'}")
            
            if not consistency_check:
                mismatch_count = (~(original_valid == log_valid)).sum()
                print(f"    不一致的记录数: {mismatch_count}")

def main():
    """主函数"""
    file_path = '/home/<USER>/repo/disengagement/result/did_result_20250310/productivity_20250310_with_propensity_scores_with_attritions.csv'
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    # 检查数据结构
    if not check_data_structure(file_path):
        print("❌ 数据结构检查失败")
        sys.exit(1)
    
    # 读取数据
    print("\n读取数据...")
    df = pd.read_csv(file_path)
    
    # 添加派生变量
    df_with_derived = add_derived_variables_safe(df)
    
    # 验证派生变量
    validate_derived_variables(df_with_derived)
    
    # 保存优化后的数据
    output_path = file_path.replace('.csv', '_optimized.csv')
    print(f"\n保存优化后的数据到: {output_path}")
    df_with_derived.to_csv(output_path, index=False)
    
    print("\n" + "=" * 60)
    print("优化完成！")
    print("=" * 60)
    print(f"原始数据: {df.shape}")
    print(f"优化后数据: {df_with_derived.shape}")
    print(f"新增列数: {df_with_derived.shape[1] - df.shape[1]}")
    
    # 显示新增的列
    new_columns = [col for col in df_with_derived.columns if col not in df.columns]
    if new_columns:
        print(f"新增列: {new_columns}")

if __name__ == "__main__":
    main()
