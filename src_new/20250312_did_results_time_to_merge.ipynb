{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "# Read data\n", "# compiled_data_test <- read.csv(\"../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers_new_added_outcomes.csv\")\n", "compiled_data_test <- read.csv(\"../result/did_result_20250312/compiled_data_test_12_time_to_merge_rate_processed.csv\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  )\n", "  # ) %>%\n", "  # 移除含有缺失值的观测（确保数据清洁）\n", "  # tidyr::drop_na()\n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "         C         C#        C++         Go       Java JavaScript        PHP \n", "    279525     263792     484599     671721     537960     875987     270454 \n", "    Python       Rust TypeScript \n", "   1186451     252222     683696 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["table(compiled_data_test$project_main_language)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "  accelerating   decelerating first 3 months     saturation         steady \n", "        892526        1887558          85739         357000        2241766 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# exclude empty growth phase or ''\n", "compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'),]\n", "table(compiled_data_test$growth_phase)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model 1: General Effects"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.149850                     1.183700 \n", "         log_project_commits     log_project_contributors \n", "                    2.095230                     2.270335 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.470302                     1.310750 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", "-2309187 -2309056  1154604 -2309207  3677444 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.4182 -0.3497  0.3588  0.6192  3.1578 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0002473 0.01573 \n", " repo_cohort_effect (Intercept) 0.0037207 0.06100 \n", " Residual                       0.0300464 0.17334 \n", "Number of obs: 3677454, groups:  \n", "time_cohort_effect, 75942; repo_cohort_effect, 75926\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df  t value Pr(>|t|)\n", "(Intercept)                   8.831e-01  3.527e-04  5.821e+04 2503.467  < 2e-16\n", "is_post_treatment             2.456e-03  2.322e-04  4.685e+04   10.579  < 2e-16\n", "is_treated                   -3.792e-03  5.592e-04  8.928e+04   -6.780 1.21e-11\n", "log_project_commits          -8.443e-03  1.567e-04  2.905e+06  -53.876  < 2e-16\n", "log_project_contributors     -3.452e-02  1.630e-04  3.055e+06 -211.739  < 2e-16\n", "log_project_age               1.105e-02  1.361e-04  2.675e+06   81.221  < 2e-16\n", "is_post_treatment:is_treated -3.140e-03  4.963e-04  3.554e+06   -6.327 2.50e-10\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.312                                                 \n", "is_treated  -0.600  0.150                                          \n", "lg_prjct_cm -0.033 -0.012  0.048                                   \n", "lg_prjct_cn -0.002  0.002 -0.038 -0.613                            \n", "log_prjct_g  0.074 -0.066 -0.068 -0.167      -0.317                \n", "is_pst_tr:_  0.107 -0.351 -0.383  0.001      -0.006       0.008    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.03763538</td><td>0.1499018</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.03763538 & 0.1499018\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.03763538 | 0.1499018 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.03763538 0.1499018"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 1: Fixed Effects Only\n", "model_fixed_effects_only <- lmer(\n", "  pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.132164                     1.162151 \n", "         log_project_commits     log_project_contributors \n", "                    2.108131                     2.283859 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.471858                     1.273562 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", "13634660 13634791 -6817320 13634640  3511663 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-3.8817 -0.6424  0.0358  0.6563  4.9535 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.39867  0.6314  \n", " time_cohort_effect (Intercept) 0.03442  0.1855  \n", " Residual                       2.71903  1.6489  \n", "Number of obs: 3511673, groups:  \n", "repo_cohort_effect, 75942; time_cohort_effect, 75941\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.447e+00  3.645e-03  5.855e+04  945.50   <2e-16\n", "is_post_treatment             1.049e-01  2.394e-03  4.200e+04   43.83   <2e-16\n", "is_treated                    1.831e-01  5.661e-03  8.263e+04   32.35   <2e-16\n", "log_project_commits           1.774e-01  1.533e-03  2.915e+06  115.68   <2e-16\n", "log_project_contributors      3.055e-01  1.594e-03  3.045e+06  191.62   <2e-16\n", "log_project_age               8.715e-02  1.328e-03  2.703e+06   65.62   <2e-16\n", "is_post_treatment:is_treated -1.391e-01  4.865e-03  3.396e+06  -28.59   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.309                                                 \n", "is_treated  -0.602  0.135                                          \n", "lg_prjct_cm -0.035 -0.014  0.049                                   \n", "lg_prjct_cn  0.000  0.000 -0.038 -0.615                            \n", "log_prjct_g  0.073 -0.059 -0.068 -0.167      -0.317                \n", "is_pst_tr:_  0.099 -0.332 -0.362  0.001      -0.004       0.007    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.03763538</td><td>0.1499018</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.03763538 & 0.1499018\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.03763538 | 0.1499018 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.03763538 0.1499018"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 1: Fixed Effects Only\n", "model_fixed_effects_only_time_to_merge <- lmer(\n", "  log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only_time_to_merge <- vif(model_fixed_effects_only_time_to_merge)\n", "print(vif_model_fixed_effects_only_time_to_merge)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only_time_to_merge)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model 2: Fixed Effects + Core Develooper Characteristics"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                is_post_treatment \n", "                                         1.012434 \n", "                                       is_treated \n", "                                         1.010632 \n", "                              log_project_commits \n", "                                         2.090221 \n", "                         log_project_contributors \n", "                                         2.223448 \n", "                                  log_project_age \n", "                                         1.502507 \n", "        is_post_treatment:is_treated:log_tenure_c \n", "                                         1.536927 \n", "is_post_treatment:is_treated:log_commit_percent_c \n", "                                         1.266812 \n", "       is_post_treatment:is_treated:log_commits_c \n", "                                         1.833001 \n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_tenure_c + is_post_treatment:is_treated:log_commit_percent_c +  \n", "    is_post_treatment:is_treated:log_commits_c + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-682222.6 -682075.3  341123.3 -682246.6   1584047 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.5492 -0.4994  0.3669  0.6765  2.8356 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0004122 0.02030 \n", " repo_cohort_effect (Intercept) 0.0049764 0.07054 \n", " Residual                       0.0365618 0.19121 \n", "Number of obs: 1584059, groups:  \n", "time_cohort_effect, 30502; repo_cohort_effect, 30502\n", "\n", "Fixed effects:\n", "                                                    Estimate Std. Error\n", "(Intercept)                                        8.377e-01  6.359e-04\n", "is_post_treatment                                  1.919e-02  3.897e-04\n", "is_treated                                         5.253e-04  9.282e-04\n", "log_project_commits                                1.443e-02  2.717e-04\n", "log_project_contributors                          -2.989e-02  2.799e-04\n", "log_project_age                                    1.982e-03  2.263e-04\n", "is_post_treatment:is_treated:log_tenure_c          1.527e-03  9.325e-04\n", "is_post_treatment:is_treated:log_commit_percent_c  5.723e-03  1.012e-03\n", "is_post_treatment:is_treated:log_commits_c        -6.605e-03  9.660e-04\n", "                                                          df  t value Pr(>|t|)\n", "(Intercept)                                        2.423e+04 1317.408  < 2e-16\n", "is_post_treatment                                  1.390e+04   49.239  < 2e-16\n", "is_treated                                         2.606e+04    0.566    0.571\n", "log_project_commits                                1.391e+06   53.106  < 2e-16\n", "log_project_contributors                           1.341e+06 -106.794  < 2e-16\n", "log_project_age                                    1.294e+06    8.759  < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c          4.572e+05    1.638    0.102\n", "is_post_treatment:is_treated:log_commit_percent_c  5.871e+05    5.657 1.55e-08\n", "is_post_treatment:is_treated:log_commits_c         4.918e+05   -6.837 8.08e-12\n", "                                                     \n", "(Intercept)                                       ***\n", "is_post_treatment                                 ***\n", "is_treated                                           \n", "log_project_commits                               ***\n", "log_project_contributors                          ***\n", "log_project_age                                   ***\n", "is_post_treatment:is_treated:log_tenure_c            \n", "is_post_treatment:is_treated:log_commit_percent_c ***\n", "is_post_treatment:is_treated:log_commits_c        ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "                           (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn\n", "is_pst_trtm                -0.293                                      \n", "is_treated                 -0.609  0.018                               \n", "lg_prjct_cm                -0.029 -0.005  0.046                        \n", "lg_prjct_cn                 0.007 -0.002 -0.054 -0.594                 \n", "log_prjct_g                 0.039 -0.056 -0.021 -0.200      -0.313     \n", "is_pst_trtmnt:s_trtd:lg_t_ -0.002  0.005  0.006  0.017      -0.013     \n", "is_p_:_:___                -0.024  0.080  0.076  0.034       0.013     \n", "is_pst_trtmnt:s_trtd:lg_c_  0.015 -0.043 -0.040 -0.072       0.033     \n", "                           lg_prjct_g is_pst_trtmnt:s_trtd:lg_t_ i__:_:___\n", "is_pst_trtm                                                               \n", "is_treated                                                                \n", "lg_prjct_cm                                                               \n", "lg_prjct_cn                                                               \n", "log_prjct_g                                                               \n", "is_pst_trtmnt:s_trtd:lg_t_ -0.034                                         \n", "is_p_:_:___                -0.023      0.059                              \n", "is_pst_trtmnt:s_trtd:lg_c_  0.026     -0.561                     -0.403   "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.01276111</td><td>0.1395721</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.01276111 & 0.1395721\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.01276111 | 0.1395721 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.01276111 0.1395721"]}, "metadata": {}, "output_type": "display_data"}], "source": ["  # Model 2: 调整后的模型公式\n", "model_fixed_effects_developer <- lmer(\n", "  pull_request_success_rate ~ \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    \n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_fixed_effects_developer, \n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_fixed_effects_developer) %>% plot()\n", "# performance::model_performance(model_fixed_effects_developer) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_developer, \n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_fixed_effects_developer, \n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model 3: Fixed Effects + Project Characteristics"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "                 accelerating   decelerating first 3 months     saturation \n", "          9969         343762         798451          26683         151232 \n", "        steady \n", "        934890 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.treatment\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.treatment\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["0"], "text/latex": ["0"], "text/markdown": ["0"], "text/plain": ["[1] 0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["0"], "text/latex": ["0"], "text/markdown": ["0"], "text/plain": ["[1] 0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["NULL"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["NULL"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "         C         C#        C++         Go       Java JavaScript        PHP \n", "    119965      99285     188484     268260     221208     386660     123000 \n", "    Python       Rust TypeScript \n", "    461180     103432     293513 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "                 accelerating   decelerating first 3 months     saturation \n", "          9969         343762         798451          26683         151232 \n", "        steady \n", "        934890 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 类别数量\n", "nlevels(compiled_data_test$project_main_language)\n", "nlevels(compiled_data_test$growth_phase)\n", "\n", "# 类别名称\n", "levels(compiled_data_test$project_main_language)\n", "levels(compiled_data_test$growth_phase)\n", "\n", "# 频数统计 (查看每个类别的观测值数量，排除空类别)\n", "table(compiled_data_test$project_main_language)\n", "table(compiled_data_test$growth_phase)\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 10 × 9 of type dbl</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>CPlusPlus</th><th scope=col>CProgramming</th><th scope=col>CSharp</th><th scope=col>Go</th><th scope=col>Java</th><th scope=col>PHP</th><th scope=col>Python</th><th scope=col>Rust</th><th scope=col>TypeScript</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>JavaScript</th><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>CPlusPlus</th><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>CProgramming</th><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>CSharp</th><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>Go</th><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>Java</th><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>PHP</th><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>Python</th><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>Rust</th><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>0</td></tr>\n", "\t<tr><th scope=row>TypeScript</th><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 10 × 9 of type dbl\n", "\\begin{tabular}{r|lllllllll}\n", "  & CPlusPlus & CProgramming & CSharp & Go & Java & PHP & Python & Rust & TypeScript\\\\\n", "\\hline\n", "\tJavaScript & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0\\\\\n", "\tCPlusPlus & 1 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0\\\\\n", "\tCProgramming & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0 & 0\\\\\n", "\tCSharp & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0 & 0\\\\\n", "\tGo & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0 & 0\\\\\n", "\tJava & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0 & 0\\\\\n", "\tPHP & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0 & 0\\\\\n", "\tPython & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0 & 0\\\\\n", "\tRust & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1 & 0\\\\\n", "\tTypeScript & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 0 & 1\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 10 × 9 of type dbl\n", "\n", "| <!--/--> | CPlusPlus | CProgramming | CSharp | Go | Java | PHP | Python | Rust | TypeScript |\n", "|---|---|---|---|---|---|---|---|---|---|\n", "| JavaScript | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 |\n", "| CPlusPlus | 1 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 |\n", "| CProgramming | 0 | 1 | 0 | 0 | 0 | 0 | 0 | 0 | 0 |\n", "| CSharp | 0 | 0 | 1 | 0 | 0 | 0 | 0 | 0 | 0 |\n", "| Go | 0 | 0 | 0 | 1 | 0 | 0 | 0 | 0 | 0 |\n", "| Java | 0 | 0 | 0 | 0 | 1 | 0 | 0 | 0 | 0 |\n", "| PHP | 0 | 0 | 0 | 0 | 0 | 1 | 0 | 0 | 0 |\n", "| Python | 0 | 0 | 0 | 0 | 0 | 0 | 1 | 0 | 0 |\n", "| Rust | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 1 | 0 |\n", "| TypeScript | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 1 |\n", "\n"], "text/plain": ["             CPlusPlus CProgramming CSharp Go Java PHP Python Rust TypeScript\n", "JavaScript   0         0            0      0  0    0   0      0    0         \n", "CPlusPlus    1         0            0      0  0    0   0      0    0         \n", "CProgramming 0         1            0      0  0    0   0      0    0         \n", "CSharp       0         0            1      0  0    0   0      0    0         \n", "Go           0         0            0      1  0    0   0      0    0         \n", "Java         0         0            0      0  1    0   0      0    0         \n", "PHP          0         0            0      0  0    1   0      0    0         \n", "Python       0         0            0      0  0    0   1      0    0         \n", "Rust         0         0            0      0  0    0   0      1    0         \n", "TypeScript   0         0            0      0  0    0   0      0    1         "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 5 × 4 of type dbl</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>accelerating</th><th scope=col>decelerating</th><th scope=col>first 3 months</th><th scope=col>saturation</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>steady</th><td>0</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>accelerating</th><td>1</td><td>0</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>decelerating</th><td>0</td><td>1</td><td>0</td><td>0</td></tr>\n", "\t<tr><th scope=row>first 3 months</th><td>0</td><td>0</td><td>1</td><td>0</td></tr>\n", "\t<tr><th scope=row>saturation</th><td>0</td><td>0</td><td>0</td><td>1</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 5 × 4 of type dbl\n", "\\begin{tabular}{r|llll}\n", "  & accelerating & decelerating & first 3 months & saturation\\\\\n", "\\hline\n", "\tsteady & 0 & 0 & 0 & 0\\\\\n", "\taccelerating & 1 & 0 & 0 & 0\\\\\n", "\tdecelerating & 0 & 1 & 0 & 0\\\\\n", "\tfirst 3 months & 0 & 0 & 1 & 0\\\\\n", "\tsaturation & 0 & 0 & 0 & 1\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 5 × 4 of type dbl\n", "\n", "| <!--/--> | accelerating | decelerating | first 3 months | saturation |\n", "|---|---|---|---|---|\n", "| steady | 0 | 0 | 0 | 0 |\n", "| accelerating | 1 | 0 | 0 | 0 |\n", "| decelerating | 0 | 1 | 0 | 0 |\n", "| first 3 months | 0 | 0 | 1 | 0 |\n", "| saturation | 0 | 0 | 0 | 1 |\n", "\n"], "text/plain": ["               accelerating decelerating first 3 months saturation\n", "steady         0            0            0              0         \n", "accelerating   1            0            0              0         \n", "decelerating   0            1            0              0         \n", "first 3 months 0            0            1              0         \n", "saturation     0            0            0              1         "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'JavaScript'</li><li>'CPlusPlus'</li><li>'CProgramming'</li><li>'CSharp'</li><li>'Go'</li><li>'Java'</li><li>'PHP'</li><li>'Python'</li><li>'Rust'</li><li>'TypeScript'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'JavaScript'\n", "\\item 'CPlusPlus'\n", "\\item 'CProgramming'\n", "\\item 'CSharp'\n", "\\item 'Go'\n", "\\item 'Java'\n", "\\item 'PHP'\n", "\\item 'Python'\n", "\\item 'Rust'\n", "\\item 'TypeScript'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'JavaScript'\n", "2. 'CPlusPlus'\n", "3. 'CProgramming'\n", "4. 'CSharp'\n", "5. 'Go'\n", "6. 'Java'\n", "7. 'PHP'\n", "8. '<PERSON>'\n", "9. 'Rust'\n", "10. 'TypeScript'\n", "\n", "\n"], "text/plain": [" [1] \"JavaScript\"   \"CPlusPlus\"    \"CProgramming\" \"CSharp\"       \"Go\"          \n", " [6] \"Java\"         \"PHP\"          \"Python\"       \"Rust\"         \"TypeScript\"  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'steady'</li><li>'accelerating'</li><li>'decelerating'</li><li>'first 3 months'</li><li>'saturation'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'steady'\n", "\\item 'accelerating'\n", "\\item 'decelerating'\n", "\\item 'first 3 months'\n", "\\item 'saturation'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'steady'\n", "2. 'accelerating'\n", "3. 'decelerating'\n", "4. 'first 3 months'\n", "5. 'saturation'\n", "\n", "\n"], "text/plain": ["[1] \"steady\"         \"accelerating\"   \"decelerating\"   \"first 3 months\"\n", "[5] \"saturation\"    "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 查看 contrasts 属性设置\n", "contrasts(compiled_data_test$project_main_language)\n", "contrasts(compiled_data_test$growth_phase)\n", "\n", "# 确认基准类别 (通过 contrasts 矩阵的设置来推断，对于 contr.sum，基准类别比较隐晦)\n", "# 对于 contr.sum，没有明显的 \"基准类别\" 的概念，所有水平都与均值比较。\n", "# 但 R 在处理 factor 时，通常会有一个 \"第一个 level\" 作为某种意义上的参考，虽然在 contr.sum 中它的系数不直接作为基准。\n", "# 我们可以看一下 factor 的 levels 顺序，以及 relevel() 是否生效了。\n", "levels(compiled_data_test$project_main_language) # 再次查看 levels，确认 relevel() 效果\n", "levels(compiled_data_test$growth_phase)        # 再次查看 levels，确认 relevel() 效果\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.115236\n", "is_treated                                                             1.156967\n", "log_project_commits                                                    2.100527\n", "log_project_contributors                                               2.242804\n", "log_project_age                                                        1.510332\n", "is_post_treatment:is_treated:log_newcomers                             4.128453\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      2.211410\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 2.698674\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.979970\n", "is_post_treatment:is_treated:project_main_language                     1.430823\n", "is_post_treatment:is_treated:growth_phase                              4.458559\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "is_post_treatment:is_treated:growth_phase                               5\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.056047\n", "is_treated                                                                    1.075624\n", "log_project_commits                                                           1.449320\n", "log_project_contributors                                                      1.497599\n", "log_project_age                                                               1.228956\n", "is_post_treatment:is_treated:log_newcomers                                    2.031859\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             1.487081\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.642764\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.407114\n", "is_post_treatment:is_treated:project_main_language                            1.020102\n", "is_post_treatment:is_treated:growth_phase                                     1.161233\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 24 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:growth_phase +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-680286.2 -679954.8  340170.1 -680340.2   1579517 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.5491 -0.4992  0.3664  0.6767  2.8339 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0004094 0.02023 \n", " repo_cohort_effect (Intercept) 0.0049400 0.07029 \n", " Residual                       0.0365658 0.19122 \n", "Number of obs: 1579544, groups:  \n", "time_cohort_effect, 30422; repo_cohort_effect, 30422\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             8.376e-01\n", "is_post_treatment                                                       1.948e-02\n", "is_treated                                                              1.237e-03\n", "log_project_commits                                                     1.442e-02\n", "log_project_contributors                                               -2.965e-02\n", "log_project_age                                                         1.849e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.639e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -8.570e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.256e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.214e-03\n", "is_post_treatment:is_treated:project_main_language1                    -8.513e-03\n", "is_post_treatment:is_treated:project_main_language2                     2.310e-03\n", "is_post_treatment:is_treated:project_main_language3                     8.450e-03\n", "is_post_treatment:is_treated:project_main_language4                     1.419e-02\n", "is_post_treatment:is_treated:project_main_language5                    -8.661e-03\n", "is_post_treatment:is_treated:project_main_language6                     8.579e-04\n", "is_post_treatment:is_treated:project_main_language7                    -4.231e-03\n", "is_post_treatment:is_treated:project_main_language8                     4.965e-03\n", "is_post_treatment:is_treated:project_main_language9                    -3.995e-03\n", "is_post_treatment:is_treated:growth_phase1                              3.770e-03\n", "is_post_treatment:is_treated:growth_phase2                             -1.956e-02\n", "is_post_treatment:is_treated:growth_phase3                              4.854e-03\n", "is_post_treatment:is_treated:growth_phase4                              6.322e-03\n", "is_post_treatment:is_treated:growth_phase5                             -7.581e-04\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             6.373e-04\n", "is_post_treatment                                                       4.091e-04\n", "is_treated                                                              9.916e-04\n", "log_project_commits                                                     2.727e-04\n", "log_project_contributors                                                2.814e-04\n", "log_project_age                                                         2.270e-04\n", "is_post_treatment:is_treated:log_newcomers                              6.371e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.071e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.169e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.051e-03\n", "is_post_treatment:is_treated:project_main_language1                     1.861e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.030e-03\n", "is_post_treatment:is_treated:project_main_language3                     3.176e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.394e-03\n", "is_post_treatment:is_treated:project_main_language5                     2.006e-03\n", "is_post_treatment:is_treated:project_main_language6                     2.214e-03\n", "is_post_treatment:is_treated:project_main_language7                     3.012e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.691e-03\n", "is_post_treatment:is_treated:project_main_language9                     3.043e-03\n", "is_post_treatment:is_treated:growth_phase1                              1.722e-03\n", "is_post_treatment:is_treated:growth_phase2                              9.311e-03\n", "is_post_treatment:is_treated:growth_phase3                              2.288e-03\n", "is_post_treatment:is_treated:growth_phase4                              1.754e-03\n", "is_post_treatment:is_treated:growth_phase5                              7.292e-03\n", "                                                                               df\n", "(Intercept)                                                             2.455e+04\n", "is_post_treatment                                                       1.675e+04\n", "is_treated                                                              3.409e+04\n", "log_project_commits                                                     1.437e+06\n", "log_project_contributors                                                1.428e+06\n", "log_project_age                                                         1.369e+06\n", "is_post_treatment:is_treated:log_newcomers                              4.141e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       4.950e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  4.743e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.492e+05\n", "is_post_treatment:is_treated:project_main_language1                     4.668e+05\n", "is_post_treatment:is_treated:project_main_language2                     4.612e+05\n", "is_post_treatment:is_treated:project_main_language3                     4.598e+05\n", "is_post_treatment:is_treated:project_main_language4                     4.542e+05\n", "is_post_treatment:is_treated:project_main_language5                     4.580e+05\n", "is_post_treatment:is_treated:project_main_language6                     4.613e+05\n", "is_post_treatment:is_treated:project_main_language7                     4.660e+05\n", "is_post_treatment:is_treated:project_main_language8                     4.634e+05\n", "is_post_treatment:is_treated:project_main_language9                     4.592e+05\n", "is_post_treatment:is_treated:growth_phase1                              5.756e+05\n", "is_post_treatment:is_treated:growth_phase2                              3.515e+05\n", "is_post_treatment:is_treated:growth_phase3                              4.988e+05\n", "is_post_treatment:is_treated:growth_phase4                              5.724e+05\n", "is_post_treatment:is_treated:growth_phase5                              3.036e+05\n", "                                                                        t value\n", "(Intercept)                                                            1314.267\n", "is_post_treatment                                                        47.613\n", "is_treated                                                                1.247\n", "log_project_commits                                                      52.888\n", "log_project_contributors                                               -105.369\n", "log_project_age                                                           8.145\n", "is_post_treatment:is_treated:log_newcomers                               -4.142\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        -7.999\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.075\n", "is_post_treatment:is_treated:log_project_age_before_treatment             2.107\n", "is_post_treatment:is_treated:project_main_language1                      -4.574\n", "is_post_treatment:is_treated:project_main_language2                       0.762\n", "is_post_treatment:is_treated:project_main_language3                       2.660\n", "is_post_treatment:is_treated:project_main_language4                       5.925\n", "is_post_treatment:is_treated:project_main_language5                      -4.318\n", "is_post_treatment:is_treated:project_main_language6                       0.387\n", "is_post_treatment:is_treated:project_main_language7                      -1.405\n", "is_post_treatment:is_treated:project_main_language8                       2.936\n", "is_post_treatment:is_treated:project_main_language9                      -1.313\n", "is_post_treatment:is_treated:growth_phase1                                2.189\n", "is_post_treatment:is_treated:growth_phase2                               -2.101\n", "is_post_treatment:is_treated:growth_phase3                                2.121\n", "is_post_treatment:is_treated:growth_phase4                                3.604\n", "is_post_treatment:is_treated:growth_phase5                               -0.104\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                             0.212290\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                        3.78e-16\n", "is_post_treatment:is_treated:log_newcomers                             3.44e-05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      1.26e-15\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 0.282557\n", "is_post_treatment:is_treated:log_project_age_before_treatment          0.035137\n", "is_post_treatment:is_treated:project_main_language1                    4.78e-06\n", "is_post_treatment:is_treated:project_main_language2                    0.445800\n", "is_post_treatment:is_treated:project_main_language3                    0.007807\n", "is_post_treatment:is_treated:project_main_language4                    3.13e-09\n", "is_post_treatment:is_treated:project_main_language5                    1.58e-05\n", "is_post_treatment:is_treated:project_main_language6                    0.698394\n", "is_post_treatment:is_treated:project_main_language7                    0.160163\n", "is_post_treatment:is_treated:project_main_language8                    0.003324\n", "is_post_treatment:is_treated:project_main_language9                    0.189211\n", "is_post_treatment:is_treated:growth_phase1                             0.028568\n", "is_post_treatment:is_treated:growth_phase2                             0.035622\n", "is_post_treatment:is_treated:growth_phase3                             0.033907\n", "is_post_treatment:is_treated:growth_phase4                             0.000313\n", "is_post_treatment:is_treated:growth_phase5                             0.917194\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                                \n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment          *  \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ** \n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ** \n", "is_post_treatment:is_treated:project_main_language9                       \n", "is_post_treatment:is_treated:growth_phase1                             *  \n", "is_post_treatment:is_treated:growth_phase2                             *  \n", "is_post_treatment:is_treated:growth_phase3                             *  \n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "is_post_treatment:is_treated:growth_phase5                                \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.01307345</td><td>0.1390289</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.01307345 & 0.1390289\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.01307345 | 0.1390289 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.01307345 0.1390289"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "\n", "model_fixed_effects_repo_add_two <- lmer(\n", "  pull_request_success_rate ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # 三重交互项（标准化后）\n", "\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_fixed_effects_repo_add_two, \n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_fixed_effects_repo_add_two) %>% plot()\n", "# performance::model_performance(model_fixed_effects_repo_add_two) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_repo_add_two,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_fixed_effects_repo_add_two, \n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.115236\n", "is_treated                                                             1.156967\n", "log_project_commits                                                    2.100527\n", "log_project_contributors                                               2.242804\n", "log_project_age                                                        1.510332\n", "is_post_treatment:is_treated:log_newcomers                             4.128453\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      2.211410\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 2.698674\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.979970\n", "is_post_treatment:is_treated:growth_phase                              4.458559\n", "is_post_treatment:is_treated:project_main_language                     1.430823\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.056047\n", "is_treated                                                                    1.075624\n", "log_project_commits                                                           1.449320\n", "log_project_contributors                                                      1.497599\n", "log_project_age                                                               1.228956\n", "is_post_treatment:is_treated:log_newcomers                                    2.031859\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             1.487081\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.642764\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.407114\n", "is_post_treatment:is_treated:growth_phase                                     1.161233\n", "is_post_treatment:is_treated:project_main_language                            1.020102\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 24 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:growth_phase + is_post_treatment:is_treated:project_main_language +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-680286.2 -679954.8  340170.1 -680340.2   1579517 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.5491 -0.4992  0.3664  0.6767  2.8339 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0004094 0.02023 \n", " repo_cohort_effect (Intercept) 0.0049400 0.07029 \n", " Residual                       0.0365658 0.19122 \n", "Number of obs: 1579544, groups:  \n", "time_cohort_effect, 30422; repo_cohort_effect, 30422\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             8.376e-01\n", "is_post_treatment                                                       1.948e-02\n", "is_treated                                                              1.237e-03\n", "log_project_commits                                                     1.442e-02\n", "log_project_contributors                                               -2.965e-02\n", "log_project_age                                                         1.849e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.639e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -8.570e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.256e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.214e-03\n", "is_post_treatment:is_treated:growth_phase1                              3.770e-03\n", "is_post_treatment:is_treated:growth_phase2                             -1.956e-02\n", "is_post_treatment:is_treated:growth_phase3                              4.854e-03\n", "is_post_treatment:is_treated:growth_phase4                              6.322e-03\n", "is_post_treatment:is_treated:growth_phase5                             -7.581e-04\n", "is_post_treatment:is_treated:project_main_language1                    -8.513e-03\n", "is_post_treatment:is_treated:project_main_language2                     2.310e-03\n", "is_post_treatment:is_treated:project_main_language3                     8.450e-03\n", "is_post_treatment:is_treated:project_main_language4                     1.419e-02\n", "is_post_treatment:is_treated:project_main_language5                    -8.661e-03\n", "is_post_treatment:is_treated:project_main_language6                     8.579e-04\n", "is_post_treatment:is_treated:project_main_language7                    -4.231e-03\n", "is_post_treatment:is_treated:project_main_language8                     4.965e-03\n", "is_post_treatment:is_treated:project_main_language9                    -3.995e-03\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             6.373e-04\n", "is_post_treatment                                                       4.091e-04\n", "is_treated                                                              9.916e-04\n", "log_project_commits                                                     2.727e-04\n", "log_project_contributors                                                2.814e-04\n", "log_project_age                                                         2.270e-04\n", "is_post_treatment:is_treated:log_newcomers                              6.371e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.071e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.169e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.051e-03\n", "is_post_treatment:is_treated:growth_phase1                              1.722e-03\n", "is_post_treatment:is_treated:growth_phase2                              9.311e-03\n", "is_post_treatment:is_treated:growth_phase3                              2.288e-03\n", "is_post_treatment:is_treated:growth_phase4                              1.754e-03\n", "is_post_treatment:is_treated:growth_phase5                              7.292e-03\n", "is_post_treatment:is_treated:project_main_language1                     1.861e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.030e-03\n", "is_post_treatment:is_treated:project_main_language3                     3.176e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.394e-03\n", "is_post_treatment:is_treated:project_main_language5                     2.006e-03\n", "is_post_treatment:is_treated:project_main_language6                     2.214e-03\n", "is_post_treatment:is_treated:project_main_language7                     3.012e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.691e-03\n", "is_post_treatment:is_treated:project_main_language9                     3.043e-03\n", "                                                                               df\n", "(Intercept)                                                             2.455e+04\n", "is_post_treatment                                                       1.675e+04\n", "is_treated                                                              3.409e+04\n", "log_project_commits                                                     1.437e+06\n", "log_project_contributors                                                1.428e+06\n", "log_project_age                                                         1.369e+06\n", "is_post_treatment:is_treated:log_newcomers                              4.141e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       4.950e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  4.743e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.492e+05\n", "is_post_treatment:is_treated:growth_phase1                              5.756e+05\n", "is_post_treatment:is_treated:growth_phase2                              3.515e+05\n", "is_post_treatment:is_treated:growth_phase3                              4.988e+05\n", "is_post_treatment:is_treated:growth_phase4                              5.724e+05\n", "is_post_treatment:is_treated:growth_phase5                              3.036e+05\n", "is_post_treatment:is_treated:project_main_language1                     4.668e+05\n", "is_post_treatment:is_treated:project_main_language2                     4.612e+05\n", "is_post_treatment:is_treated:project_main_language3                     4.598e+05\n", "is_post_treatment:is_treated:project_main_language4                     4.542e+05\n", "is_post_treatment:is_treated:project_main_language5                     4.580e+05\n", "is_post_treatment:is_treated:project_main_language6                     4.613e+05\n", "is_post_treatment:is_treated:project_main_language7                     4.660e+05\n", "is_post_treatment:is_treated:project_main_language8                     4.634e+05\n", "is_post_treatment:is_treated:project_main_language9                     4.592e+05\n", "                                                                        t value\n", "(Intercept)                                                            1314.267\n", "is_post_treatment                                                        47.613\n", "is_treated                                                                1.247\n", "log_project_commits                                                      52.888\n", "log_project_contributors                                               -105.369\n", "log_project_age                                                           8.145\n", "is_post_treatment:is_treated:log_newcomers                               -4.142\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        -7.999\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.075\n", "is_post_treatment:is_treated:log_project_age_before_treatment             2.107\n", "is_post_treatment:is_treated:growth_phase1                                2.189\n", "is_post_treatment:is_treated:growth_phase2                               -2.101\n", "is_post_treatment:is_treated:growth_phase3                                2.121\n", "is_post_treatment:is_treated:growth_phase4                                3.604\n", "is_post_treatment:is_treated:growth_phase5                               -0.104\n", "is_post_treatment:is_treated:project_main_language1                      -4.574\n", "is_post_treatment:is_treated:project_main_language2                       0.762\n", "is_post_treatment:is_treated:project_main_language3                       2.660\n", "is_post_treatment:is_treated:project_main_language4                       5.925\n", "is_post_treatment:is_treated:project_main_language5                      -4.318\n", "is_post_treatment:is_treated:project_main_language6                       0.387\n", "is_post_treatment:is_treated:project_main_language7                      -1.405\n", "is_post_treatment:is_treated:project_main_language8                       2.936\n", "is_post_treatment:is_treated:project_main_language9                      -1.313\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                             0.212290\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                        3.78e-16\n", "is_post_treatment:is_treated:log_newcomers                             3.44e-05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      1.26e-15\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 0.282557\n", "is_post_treatment:is_treated:log_project_age_before_treatment          0.035137\n", "is_post_treatment:is_treated:growth_phase1                             0.028568\n", "is_post_treatment:is_treated:growth_phase2                             0.035622\n", "is_post_treatment:is_treated:growth_phase3                             0.033907\n", "is_post_treatment:is_treated:growth_phase4                             0.000313\n", "is_post_treatment:is_treated:growth_phase5                             0.917194\n", "is_post_treatment:is_treated:project_main_language1                    4.78e-06\n", "is_post_treatment:is_treated:project_main_language2                    0.445800\n", "is_post_treatment:is_treated:project_main_language3                    0.007807\n", "is_post_treatment:is_treated:project_main_language4                    3.13e-09\n", "is_post_treatment:is_treated:project_main_language5                    1.58e-05\n", "is_post_treatment:is_treated:project_main_language6                    0.698394\n", "is_post_treatment:is_treated:project_main_language7                    0.160163\n", "is_post_treatment:is_treated:project_main_language8                    0.003324\n", "is_post_treatment:is_treated:project_main_language9                    0.189211\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                                \n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment          *  \n", "is_post_treatment:is_treated:growth_phase1                             *  \n", "is_post_treatment:is_treated:growth_phase2                             *  \n", "is_post_treatment:is_treated:growth_phase3                             *  \n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "is_post_treatment:is_treated:growth_phase5                                \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ** \n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ** \n", "is_post_treatment:is_treated:project_main_language9                       \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.01307345</td><td>0.1390289</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.01307345 & 0.1390289\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.01307345 | 0.1390289 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.01307345 0.1390289"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model_fixed_effects_repo_add_two <- lmer(\n", "  pull_request_success_rate ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # 三重交互项（标准化后）\n", "\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:project_main_language +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_fixed_effects_repo_add_two, \n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_fixed_effects_repo_add_two) %>% plot()\n", "# performance::model_performance(model_fixed_effects_repo_add_two) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_repo_add_two,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_fixed_effects_repo_add_two, \n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# save the model\n", "saveRDS(model_fixed_effects_repo_add_two, \"../result/did_result_20250227/model_fixed_effects_repo_add_two.rds\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in gzfile(file, \"rb\"):\n", "“cannot open compressed file '../result/did_result_20250227/model_fixed_effects_repo_add_two.rds', probable reason 'No such file or directory'”\n"]}, {"ename": "ERROR", "evalue": "Error in gzfile(file, \"rb\"): cannot open the connection\n", "output_type": "error", "traceback": ["Error in gzfile(file, \"rb\"): cannot open the connection\nTraceback:\n", "1. readRDS(\"../result/did_result_20250227/model_fixed_effects_repo_add_two.rds\")", "2. gzfile(file, \"rb\")"]}], "source": ["# read the model\n", "model_fixed_effects_repo_add_two <- readRDS(\"../result/did_result_20250227/model_fixed_effects_repo_add_two.rds\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'pbkrtest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(pbkrtest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'lmerTest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(lmerTest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "NOTE: A nesting structure was detected in the fitted model:\n", "    growth_phase %in% (is_post_treatment*is_treated), project_main_language %in% (is_post_treatment*is_treated)\n", "\n"]}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.727 0.00233 Inf     0.722     0.731\n", " CPlusPlus              0.727 0.00233 Inf     0.722     0.731\n", " CProgramming           0.727 0.00233 Inf     0.722     0.731\n", " CSharp                 0.727 0.00233 Inf     0.722     0.731\n", " Go                     0.727 0.00233 Inf     0.722     0.731\n", " Java                   0.727 0.00233 Inf     0.722     0.731\n", " PHP                    0.727 0.00233 Inf     0.722     0.731\n", " Python                 0.727 0.00233 Inf     0.722     0.731\n", " Rust                   0.727 0.00233 Inf     0.722     0.731\n", " TypeScript             0.727 0.00233 Inf     0.722     0.731\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.645 0.00232 Inf     0.641     0.650\n", " CPlusPlus              0.645 0.00232 Inf     0.641     0.650\n", " CProgramming           0.645 0.00232 Inf     0.641     0.650\n", " CSharp                 0.645 0.00232 Inf     0.641     0.650\n", " Go                     0.645 0.00232 Inf     0.641     0.650\n", " Java                   0.645 0.00232 Inf     0.641     0.650\n", " PHP                    0.645 0.00232 Inf     0.641     0.650\n", " Python                 0.645 0.00232 Inf     0.641     0.650\n", " Rust                   0.645 0.00232 Inf     0.641     0.650\n", " TypeScript             0.645 0.00232 Inf     0.641     0.650\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.690 0.00232 Inf     0.686     0.695\n", " CPlusPlus              0.690 0.00232 Inf     0.686     0.695\n", " CProgramming           0.690 0.00232 Inf     0.686     0.695\n", " CSharp                 0.690 0.00232 Inf     0.686     0.695\n", " Go                     0.690 0.00232 Inf     0.686     0.695\n", " Java                   0.690 0.00232 Inf     0.686     0.695\n", " PHP                    0.690 0.00232 Inf     0.686     0.695\n", " Python                 0.690 0.00232 Inf     0.686     0.695\n", " Rust                   0.690 0.00232 Inf     0.686     0.695\n", " TypeScript             0.690 0.00232 Inf     0.686     0.695\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.496 0.01070 Inf     0.475     0.517\n", " CPlusPlus              0.377 0.01120 Inf     0.355     0.399\n", " CProgramming           0.382 0.01140 Inf     0.359     0.404\n", " CSharp                 0.374 0.01160 Inf     0.351     0.396\n", " Go                     0.382 0.01110 Inf     0.361     0.404\n", " Java                   0.376 0.01110 Inf     0.355     0.398\n", " PHP                    0.365 0.01140 Inf     0.343     0.388\n", " Python                 0.368 0.01080 Inf     0.347     0.389\n", " Rust                   0.355 0.01180 Inf     0.332     0.378\n", " TypeScript             0.379 0.01100 Inf     0.357     0.400\n", "\n", "Results are averaged over the levels of: growth_phase \n", "Degrees-of-freedom method: asymptotic \n", "Confidence level used: 0.95 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - CProgramming  0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - CSharp        0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Go            0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Java          0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - PHP           0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Python        0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Rust          0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - TypeScript    0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CProgramming   0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CSharp         0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Go             0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Java           0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - PHP            0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Python         0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Rust           0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - TypeScript     0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - CSharp      0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Go          0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Java        0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - PHP         0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Python      0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Rust        0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - TypeScript  0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Go                0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Java              0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - PHP               0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Python            0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Rust              0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - TypeScript        0.000000 0.00000 Inf     NaN     NaN\n", " Go - Java                  0.000000 0.00000 Inf     NaN     NaN\n", " Go - PHP                   0.000000 0.00000 Inf     NaN     NaN\n", " Go - Python                0.000000 0.00000 Inf     NaN     NaN\n", " Go - Rust                  0.000000 0.00000 Inf     NaN     NaN\n", " Go - TypeScript            0.000000 0.00000 Inf     NaN     NaN\n", " Java - PHP                 0.000000 0.00000 Inf     NaN     NaN\n", " Java - Python              0.000000 0.00000 Inf     NaN     NaN\n", " Java - Rust                0.000000 0.00000 Inf     NaN     NaN\n", " Java - TypeScript          0.000000 0.00000 Inf     NaN     NaN\n", " PHP - Python               0.000000 0.00000 Inf     NaN     NaN\n", " PHP - Rust                 0.000000 0.00000 Inf     NaN     NaN\n", " PHP - TypeScript           0.000000 0.00000 Inf     NaN     NaN\n", " Python - Rust              0.000000 0.00000 Inf     NaN     NaN\n", " Python - TypeScript        0.000000 0.00000 Inf     NaN     NaN\n", " Rust - TypeScript          0.000000 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - CProgramming  0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - CSharp        0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Go            0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Java          0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - PHP           0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Python        0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Rust          0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - TypeScript    0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CProgramming   0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CSharp         0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Go             0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Java           0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - PHP            0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Python         0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Rust           0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - TypeScript     0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - CSharp      0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Go          0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Java        0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - PHP         0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Python      0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Rust        0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - TypeScript  0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Go                0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Java              0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - PHP               0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Python            0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Rust              0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - TypeScript        0.000000 0.00000 Inf     NaN     NaN\n", " Go - Java                  0.000000 0.00000 Inf     NaN     NaN\n", " Go - PHP                   0.000000 0.00000 Inf     NaN     NaN\n", " Go - Python                0.000000 0.00000 Inf     NaN     NaN\n", " Go - Rust                  0.000000 0.00000 Inf     NaN     NaN\n", " Go - TypeScript            0.000000 0.00000 Inf     NaN     NaN\n", " Java - PHP                 0.000000 0.00000 Inf     NaN     NaN\n", " Java - Python              0.000000 0.00000 Inf     NaN     NaN\n", " Java - Rust                0.000000 0.00000 Inf     NaN     NaN\n", " Java - TypeScript          0.000000 0.00000 Inf     NaN     NaN\n", " PHP - Python               0.000000 0.00000 Inf     NaN     NaN\n", " PHP - Rust                 0.000000 0.00000 Inf     NaN     NaN\n", " PHP - TypeScript           0.000000 0.00000 Inf     NaN     NaN\n", " Python - Rust              0.000000 0.00000 Inf     NaN     NaN\n", " Python - TypeScript        0.000000 0.00000 Inf     NaN     NaN\n", " Rust - TypeScript          0.000000 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - CProgramming  0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - CSharp        0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Go            0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Java          0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - PHP           0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Python        0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - Rust          0.000000 0.00000 Inf     NaN     NaN\n", " JavaScript - TypeScript    0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CProgramming   0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CSharp         0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Go             0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Java           0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - PHP            0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Python         0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Rust           0.000000 0.00000 Inf     NaN     NaN\n", " CPlusPlus - TypeScript     0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - CSharp      0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Go          0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Java        0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - PHP         0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Python      0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - Rust        0.000000 0.00000 Inf     NaN     NaN\n", " CProgramming - TypeScript  0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Go                0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Java              0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - PHP               0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Python            0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - Rust              0.000000 0.00000 Inf     NaN     NaN\n", " CSharp - TypeScript        0.000000 0.00000 Inf     NaN     NaN\n", " Go - Java                  0.000000 0.00000 Inf     NaN     NaN\n", " Go - PHP                   0.000000 0.00000 Inf     NaN     NaN\n", " Go - Python                0.000000 0.00000 Inf     NaN     NaN\n", " Go - Rust                  0.000000 0.00000 Inf     NaN     NaN\n", " Go - TypeScript            0.000000 0.00000 Inf     NaN     NaN\n", " Java - PHP                 0.000000 0.00000 Inf     NaN     NaN\n", " Java - Python              0.000000 0.00000 Inf     NaN     NaN\n", " Java - Rust                0.000000 0.00000 Inf     NaN     NaN\n", " Java - TypeScript          0.000000 0.00000 Inf     NaN     NaN\n", " PHP - Python               0.000000 0.00000 Inf     NaN     NaN\n", " PHP - Rust                 0.000000 0.00000 Inf     NaN     NaN\n", " PHP - TypeScript           0.000000 0.00000 Inf     NaN     NaN\n", " Python - Rust              0.000000 0.00000 Inf     NaN     NaN\n", " Python - TypeScript        0.000000 0.00000 Inf     NaN     NaN\n", " Rust - TypeScript          0.000000 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.118763 0.00383 Inf  30.994  <.0001\n", " JavaScript - CProgramming  0.114233 0.00439 Inf  26.040  <.0001\n", " JavaScript - CSharp        0.122110 0.00485 Inf  25.201  <.0001\n", " JavaScript - Go            0.113512 0.00357 Inf  31.834  <.0001\n", " JavaScript - Java          0.119451 0.00357 Inf  33.413  <.0001\n", " JavaScript - PHP           0.130548 0.00446 Inf  29.248  <.0001\n", " JavaScript - Python        0.127620 0.00258 Inf  49.431  <.0001\n", " JavaScript - Rust          0.140986 0.00535 Inf  26.333  <.0001\n", " JavaScript - TypeScript    0.117155 0.00343 Inf  34.140  <.0001\n", " CPlusPlus - CProgramming  -0.004530 0.00539 Inf  -0.840  1.0000\n", " CPlusPlus - CSharp         0.003347 0.00580 Inf   0.577  1.0000\n", " CPlusPlus - Go            -0.005251 0.00484 Inf  -1.084  1.0000\n", " CPlusPlus - Java           0.000688 0.00476 Inf   0.145  1.0000\n", " CPlusPlus - PHP            0.011785 0.00551 Inf   2.137  1.0000\n", " CPlusPlus - Python         0.008857 0.00412 Inf   2.150  1.0000\n", " CPlusPlus - Rust           0.022222 0.00625 Inf   3.555  0.0170\n", " CPlusPlus - TypeScript    -0.001609 0.00472 Inf  -0.341  1.0000\n", " CProgramming - CSharp      0.007877 0.00619 Inf   1.273  1.0000\n", " CProgramming - Go         -0.000721 0.00529 Inf  -0.136  1.0000\n", " CProgramming - Java        0.005218 0.00524 Inf   0.996  1.0000\n", " CProgramming - PHP         0.016315 0.00591 Inf   2.760  0.2598\n", " CProgramming - Python      0.013387 0.00464 Inf   2.883  0.1774\n", " CProgramming - Rust        0.026753 0.00661 Inf   4.048  0.0023\n", " CProgramming - TypeScript  0.002922 0.00519 Inf   0.563  1.0000\n", " CSharp - Go               -0.008598 0.00566 Inf  -1.518  1.0000\n", " CSharp - Java             -0.002658 0.00564 Inf  -0.471  1.0000\n", " CSharp - PHP               0.008438 0.00626 Inf   1.348  1.0000\n", " CSharp - Python            0.005510 0.00508 Inf   1.085  1.0000\n", " CSharp - Rust              0.018876 0.00691 Inf   2.732  0.2833\n", " CSharp - TypeScript       -0.004955 0.00557 Inf  -0.889  1.0000\n", " Go - Java                  0.005939 0.00463 Inf   1.283  1.0000\n", " Go - PHP                   0.017036 0.00534 Inf   3.191  0.0637\n", " Go - Python                0.014108 0.00389 Inf   3.623  0.0131\n", " Go - Rust                  0.027473 0.00604 Inf   4.547  0.0002\n", " Go - TypeScript            0.003642 0.00448 Inf   0.813  1.0000\n", " Java - PHP                 0.011097 0.00534 Inf   2.079  1.0000\n", " Java - Python              0.008169 0.00388 Inf   2.105  1.0000\n", " Java - Rust                0.021534 0.00609 Inf   3.537  0.0182\n", " Java - TypeScript         -0.002297 0.00451 Inf  -0.509  1.0000\n", " PHP - Python              -0.002928 0.00473 Inf  -0.619  1.0000\n", " PHP - Rust                 0.010438 0.00665 Inf   1.570  1.0000\n", " PHP - TypeScript          -0.013393 0.00525 Inf  -2.550  0.4848\n", " Python - Rust              0.013366 0.00555 Inf   2.407  0.7228\n", " Python - TypeScript       -0.010465 0.00377 Inf  -2.777  0.2472\n", " Rust - TypeScript         -0.023831 0.00598 Inf  -3.988  0.0030\n", "\n", "Results are averaged over some or all of the levels of: growth_phase \n", "Degrees-of-freedom method: asymptotic \n", "P value adjustment: bonferroni method for varying numbers of tests "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'pbkrtest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(pbkrtest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'lmerTest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(lmerTest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "NOTE: A nesting structure was detected in the fitted model:\n", "    growth_phase %in% (is_post_treatment*is_treated), project_main_language %in% (is_post_treatment*is_treated)\n", "\n"]}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.727 0.00233 Inf     0.722   0.73150\n", " accelerating    0.727 0.00233 Inf     0.722   0.73150\n", " decelerating    0.727 0.00233 Inf     0.722   0.73150\n", " first 3 months  0.727 0.00233 Inf     0.722   0.73150\n", " saturation      0.727 0.00233 Inf     0.722   0.73150\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.645 0.00232 Inf     0.641   0.64994\n", " accelerating    0.645 0.00232 Inf     0.641   0.64994\n", " decelerating    0.645 0.00232 Inf     0.641   0.64994\n", " first 3 months  0.645 0.00232 Inf     0.641   0.64994\n", " saturation      0.645 0.00232 Inf     0.641   0.64994\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.690 0.00232 Inf     0.686   0.69502\n", " accelerating    0.690 0.00232 Inf     0.686   0.69502\n", " decelerating    0.690 0.00232 Inf     0.686   0.69502\n", " first 3 months  0.690 0.00232 Inf     0.686   0.69502\n", " saturation      0.690 0.00232 Inf     0.686   0.69502\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.574 0.00265 Inf     0.569   0.57890\n", " accelerating    0.442 0.00365 Inf     0.435   0.44907\n", " decelerating    0.529 0.00276 Inf     0.524   0.53436\n", " first 3 months -0.107 0.05200 Inf    -0.209  -0.00493\n", " saturation      0.489 0.00376 Inf     0.481   0.49623\n", "\n", "Results are averaged over the levels of: project_main_language \n", "Degrees-of-freedom method: asymptotic \n", "Confidence level used: 0.95 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " contrast                      estimate      SE  df z.ratio p.value\n", " steady - accelerating           0.0000 0.00000 Inf     NaN     NaN\n", " steady - decelerating           0.0000 0.00000 Inf     NaN     NaN\n", " steady - first 3 months         0.0000 0.00000 Inf     NaN     NaN\n", " steady - saturation             0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - decelerating     0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - first 3 months   0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - saturation       0.0000 0.00000 Inf     NaN     NaN\n", " decelerating - first 3 months   0.0000 0.00000 Inf     NaN     NaN\n", " decelerating - saturation       0.0000 0.00000 Inf     NaN     NaN\n", " first 3 months - saturation     0.0000 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " contrast                      estimate      SE  df z.ratio p.value\n", " steady - accelerating           0.0000 0.00000 Inf     NaN     NaN\n", " steady - decelerating           0.0000 0.00000 Inf     NaN     NaN\n", " steady - first 3 months         0.0000 0.00000 Inf     NaN     NaN\n", " steady - saturation             0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - decelerating     0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - first 3 months   0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - saturation       0.0000 0.00000 Inf     NaN     NaN\n", " decelerating - first 3 months   0.0000 0.00000 Inf     NaN     NaN\n", " decelerating - saturation       0.0000 0.00000 Inf     NaN     NaN\n", " first 3 months - saturation     0.0000 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " contrast                      estimate      SE  df z.ratio p.value\n", " steady - accelerating           0.0000 0.00000 Inf     NaN     NaN\n", " steady - decelerating           0.0000 0.00000 Inf     NaN     NaN\n", " steady - first 3 months         0.0000 0.00000 Inf     NaN     NaN\n", " steady - saturation             0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - decelerating     0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - first 3 months   0.0000 0.00000 Inf     NaN     NaN\n", " accelerating - saturation       0.0000 0.00000 Inf     NaN     NaN\n", " decelerating - first 3 months   0.0000 0.00000 Inf     NaN     NaN\n", " decelerating - saturation       0.0000 0.00000 Inf     NaN     NaN\n", " first 3 months - saturation     0.0000 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " contrast                      estimate      SE  df z.ratio p.value\n", " steady - accelerating           0.1318 0.00324 Inf  40.714  <.0001\n", " steady - decelerating           0.0448 0.00217 Inf  20.668  <.0001\n", " steady - first 3 months         0.6805 0.05190 Inf  13.107  <.0001\n", " steady - saturation             0.0849 0.00331 Inf  25.606  <.0001\n", " accelerating - decelerating    -0.0870 0.00340 Inf -25.566  <.0001\n", " accelerating - first 3 months   0.5487 0.05200 Inf  10.561  <.0001\n", " accelerating - saturation      -0.0469 0.00435 Inf -10.778  <.0001\n", " decelerating - first 3 months   0.6357 0.05190 Inf  12.242  <.0001\n", " decelerating - saturation       0.0401 0.00355 Inf  11.300  <.0001\n", " first 3 months - saturation    -0.5956 0.05200 Inf -11.450  <.0001\n", "\n", "Results are averaged over some or all of the levels of: project_main_language \n", "Degrees-of-freedom method: asymptotic \n", "P value adjustment: tukey method for varying family sizes "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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*******************************/b8mk6Nu3ZVgeTHj9NqbhA+O372/ZUx1NTUALIyM9/dkH33bvin9dt05KYnCyGEvkv4iR1CX6/Dwq0zjg13m+Ksunnp0PaM3JALf228Az23bhilXK+l0fwdi4877/nJYUrSyjHW2rIU/xNbt98WT3b6mwYA7X/5a+aRYScmO7L+mOdsII3x2LUtUsf10ixzANDV1QUIPP3XBbq5oe3oXvqNpFGDsJ42tdPO9begy6YpnRvc27Zfvzbw79HfdnVdZa9W9Np7/55X9vMc7+164XPO12mi7VdecqL81ZUDB17UuYnT2fUnu6Y7NWoqJLX/zOnGZ/atG/oTdc34LpzM+7u2hrW3ZyaEVd2t5uhkRfH12Lp1LG9KeyI94OhvbsUdOFXfa39sY5uO3PRkIYTQd6mZf5WL0HemyXN2VCk65ARguDykzo2Stz5bJjuYanLoNKaqgdXQJW6hJdV31T6BCEmSshz/fbOdLXhKTDpLVbdjv+lbfdIk7+KIU25vnGDXVl2BwdYw7ub6+9XY8powV2dYabEVVPWt1gQ2nvYQ98qa/5N22hCE3e7kmv9rn+6ELA75d4qdCZfNVNQy6z/nUGgpmXtjQRcNthLP+UBcg9Od1N3SJs66EvGnRaNPMtq/PPpYpx/rSBDj/ouTqYYCQ0G9ncP0A8+LPScyQHG6T9W9lXEXlwwy11NhMZX0ugxfezP1wQIdUJ5x7+Mb+7HIH5kshBD67hAkSTb6XIwQkpP0XT3b/Fr8R0TM5k7NnUpLUXrEWWVe9KLAjH12/5nICCH0/4DH2CH0rVVWVgKwWKzmzuM/SxC4b7rrqG3B745sK7l3/znQu3b91JPZNUNkhBBqHnioCELfUHmc322/s5eige1q2tRRcagpLBPN0ieea165Vq6b3VOzIu7Wng1Xy4wWrBir8v1GRgih5oFfxSL0DaUdcDJdHqRkOmTLxfOzLegffwBqXEXE2XVr9lwNjM0sIZUNrQZMW7N9lYuBPN6YfrvICCHUDHBhhxBCCCHUQuAxdgghhBBCLQQu7BBCCCGEWghc2CGEEEIItRC4sEMIIYQQaiFwYYcQQggh1ELgwg4hhBBCqIXAhR1CCCGEUAvxP7yeRelDs8qlAAAAAElFTkSuQmCC", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["library(emmeans)\n", "library(ggplot2) # 可选，用于可视化\n", "\n", "# ----------------------------------------------------------------------\n", "# 1. 分析 project_main_language 与干预的交互作用 (is_post_treatment:is_treated:project_main_language)\n", "\n", "# 计算不同 project_main_language 组别在不同干预条件下的边际均值\n", "emm_language_interact <- emmeans(model_fixed_effects_repo_add_two,\n", "                                 ~ project_main_language | is_treated * is_post_treatment)\n", "# `|` 符号表示 \"在...条件下\"，这里是在不同的 `is_treated` 和 `is_post_treatment` 组合条件下，计算 `project_main_language` 各组别的边际均值\n", "\n", "emm_language_interact\n", "\n", "# 进行 pairwise comparisons，比较不同 project_main_language 组别在 *特定干预条件* 下的差异\n", "# 例如，比较在 \"treatment group\" (is_treated = TRUE, is_post_treatment = FALSE) 中，不同 language 的差异\n", "pairs(emm_language_interact, by = c(\"is_treated\", \"is_post_treatment\"), adjust = \"bonferroni\")\n", "# `by = c(\"is_treated\", \"is_post_treatment\")` 表示在相同的干预条件下进行组间比较\n", "\n", "# 可视化交互作用 (交互作用图)\n", "emmip(emm_language_interact, is_post_treatment ~ project_main_language | is_treated,\n", "      cov.reduce = range) + #  cov.reduce 控制协变量的取值范围\n", "  labs(title = \"Interaction: Language and Intervention Effect\",\n", "       y = \"Estimated Marginal Mean (log_pr_throughput)\",\n", "       x = \"Project Main Language\",\n", "       fill = \"Post-Treatment\") +\n", "  facet_grid(~ is_treated) # 分面显示 Treated 和 Control 组\n", "\n", "\n", "# ----------------------------------------------------------------------\n", "# 2. 分析 growth_phase 与干预的交互作用 (is_post_treatment:is_treated:growth_phase)\n", "\n", "# 计算不同 growth_phase 组别在不同干预条件下的边际均值\n", "emm_growth_phase_interact <- emmeans(model_fixed_effects_repo_add_two,\n", "                                     ~ growth_phase | is_treated * is_post_treatment)\n", "emm_growth_phase_interact\n", "\n", "# 进行 pairwise comparisons，比较不同 growth_phase 组别在 *特定干预条件* 下的差异\n", "pairs(emm_growth_phase_interact, by = c(\"is_treated\", \"is_post_treatment\"), adjust = \"tukey\")\n", "\n", "# 可视化交互作用\n", "emmip(emm_growth_phase_interact, is_post_treatment ~ growth_phase | is_treated,\n", "      cov.reduce = range) +\n", "  labs(title = \"Interaction: Growth Phase and Intervention Effect\",\n", "       y = \"Estimated Marginal Mean (log_pr_throughput)\",\n", "       x = \"Growth Phase\",\n", "       fill = \"Post-Treatment\") +\n", "  facet_grid(~ is_treated)\n", "\n", "\n", "# ----------------------------------------------------------------------\n", "# 3. (更复杂) 探索 project_main_language 和 growth_phase 的 *三重交互作用* (如果模型中包含)\n", "#   虽然你的模型公式中没有直接包含 project_main_language:growth_phase 的交互项，\n", "#   但你可以通过分析上述两个双重交互作用，来间接理解它们之间的关系。\n", "#   如果你想显式地模型化三重交互，可以在模型公式中添加  project_main_language:growth_phase:is_treated:is_post_treatment 或类似的项。\n", "\n", "#   以下代码仅供参考，假设你添加了三重交互项：\n", "#   emm_language_growth_phase_interact <- emmeans(model_fixed_effects_repo_add_two,\n", "#                                                ~ project_main_language * growth_phase | is_treated * is_post_treatment)\n", "#   emm_language_growth_phase_interact\n", "#   # ... 后续可以进行更复杂的对比和可视化 ...\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'JavaScript'</li><li>'CPlusPlus'</li><li>'CProgramming'</li><li>'CSharp'</li><li>'Go'</li><li>'Java'</li><li>'PHP'</li><li>'Python'</li><li>'Rust'</li><li>'TypeScript'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'JavaScript'\n", "\\item 'CPlusPlus'\n", "\\item 'CProgramming'\n", "\\item 'CSharp'\n", "\\item 'Go'\n", "\\item 'Java'\n", "\\item 'PHP'\n", "\\item 'Python'\n", "\\item 'Rust'\n", "\\item 'TypeScript'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'JavaScript'\n", "2. 'CPlusPlus'\n", "3. 'CProgramming'\n", "4. 'CSharp'\n", "5. 'Go'\n", "6. 'Java'\n", "7. 'PHP'\n", "8. '<PERSON>'\n", "9. 'Rust'\n", "10. 'TypeScript'\n", "\n", "\n"], "text/plain": [" [1] \"JavaScript\"   \"CPlusPlus\"    \"CProgramming\" \"CSharp\"       \"Go\"          \n", " [6] \"Java\"         \"PHP\"          \"Python\"       \"Rust\"         \"TypeScript\"  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 查看因子变量的 levels\n", "levels(compiled_data_test$project_main_language)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'JavaScript'</li><li>'CPlusPlus'</li><li>'CProgramming'</li><li>'CSharp'</li><li>'Go'</li><li>'Java'</li><li>'PHP'</li><li>'Python'</li><li>'Rust'</li><li>'TypeScript'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'JavaScript'\n", "\\item 'CPlusPlus'\n", "\\item 'CProgramming'\n", "\\item 'CSharp'\n", "\\item 'Go'\n", "\\item 'Java'\n", "\\item 'PHP'\n", "\\item 'Python'\n", "\\item 'Rust'\n", "\\item 'TypeScript'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'JavaScript'\n", "2. 'CPlusPlus'\n", "3. 'CProgramming'\n", "4. 'CSharp'\n", "5. 'Go'\n", "6. 'Java'\n", "7. 'PHP'\n", "8. '<PERSON>'\n", "9. 'Rust'\n", "10. 'TypeScript'\n", "\n", "\n"], "text/plain": [" [1] \"JavaScript\"   \"CPlusPlus\"    \"CProgramming\" \"CSharp\"       \"Go\"          \n", " [6] \"Java\"         \"PHP\"          \"Python\"       \"Rust\"         \"TypeScript\"  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'steady'</li><li>'accelerating'</li><li>'decelerating'</li><li>'first 3 months'</li><li>'saturation'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'steady'\n", "\\item 'accelerating'\n", "\\item 'decelerating'\n", "\\item 'first 3 months'\n", "\\item 'saturation'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'steady'\n", "2. 'accelerating'\n", "3. 'decelerating'\n", "4. 'first 3 months'\n", "5. 'saturation'\n", "\n", "\n"], "text/plain": ["[1] \"steady\"         \"accelerating\"   \"decelerating\"   \"first 3 months\"\n", "[5] \"saturation\"    "]}, "metadata": {}, "output_type": "display_data"}], "source": ["levels(compiled_data_test$project_main_language)\n", "levels(compiled_data_test$growth_phase)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model 4: Fixed Effects + Core Develooper Characteristics + Project Characteristics"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.096288\n", "is_treated                                                             1.035319\n", "log_project_commits                                                    2.057003\n", "log_project_contributors                                               2.207549\n", "log_project_age                                                        1.459817\n", "is_post_treatment:is_treated:log_tenure_c                              2.173357\n", "is_post_treatment:is_treated:log_commit_percent_c                      2.474932\n", "is_post_treatment:is_treated:log_commits_c                             4.526040\n", "is_post_treatment:is_treated:log_newcomers                             3.822900\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      5.545438\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.376988\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.173921\n", "is_post_treatment:is_treated:growth_phase                              4.486643\n", "is_post_treatment:is_treated:project_main_language                     1.480868\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.047038\n", "is_treated                                                                    1.017506\n", "log_project_commits                                                           1.434226\n", "log_project_contributors                                                      1.485782\n", "log_project_age                                                               1.208229\n", "is_post_treatment:is_treated:log_tenure_c                                     1.474231\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.573192\n", "is_post_treatment:is_treated:log_commits_c                                    2.127449\n", "is_post_treatment:is_treated:log_newcomers                                    1.955224\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.354875\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.837658\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.474422\n", "is_post_treatment:is_treated:growth_phase                                     1.161963\n", "is_post_treatment:is_treated:project_main_language                            1.022052\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 27 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:growth_phase + is_post_treatment:is_treated:project_main_language +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5601830  5602209 -2800885  5601770  2258982 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.1299 -0.7063 -0.0590  0.6865  5.8896 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " time_cohort_effect (Intercept) 0.009873 0.09936 \n", " repo_cohort_effect (Intercept) 0.400119 0.63255 \n", " Residual                       0.662940 0.81421 \n", "Number of obs: 2259012, groups:  \n", "time_cohort_effect, 30422; repo_cohort_effect, 30422\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             1.178e+00\n", "is_post_treatment                                                      -5.145e-02\n", "is_treated                                                             -5.171e-02\n", "log_project_commits                                                     2.355e-01\n", "log_project_contributors                                                8.349e-02\n", "log_project_age                                                        -1.683e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.276e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -4.055e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.203e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.024e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.046e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.478e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.396e-02\n", "is_post_treatment:is_treated:growth_phase1                             -2.492e-01\n", "is_post_treatment:is_treated:growth_phase2                              1.384e+00\n", "is_post_treatment:is_treated:growth_phase3                             -3.153e-01\n", "is_post_treatment:is_treated:growth_phase4                             -2.336e-01\n", "is_post_treatment:is_treated:growth_phase5                             -3.174e-01\n", "is_post_treatment:is_treated:project_main_language1                    -2.164e-02\n", "is_post_treatment:is_treated:project_main_language2                    -9.123e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.871e-04\n", "is_post_treatment:is_treated:project_main_language4                     2.507e-03\n", "is_post_treatment:is_treated:project_main_language5                     1.137e-02\n", "is_post_treatment:is_treated:project_main_language6                     1.200e-02\n", "is_post_treatment:is_treated:project_main_language7                    -9.164e-03\n", "is_post_treatment:is_treated:project_main_language8                    -2.351e-02\n", "is_post_treatment:is_treated:project_main_language9                    -8.598e-04\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             5.258e-03\n", "is_post_treatment                                                       1.647e-03\n", "is_treated                                                              7.527e-03\n", "log_project_commits                                                     9.875e-04\n", "log_project_contributors                                                1.050e-03\n", "log_project_age                                                         8.349e-04\n", "is_post_treatment:is_treated:log_tenure_c                               4.021e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       4.259e-03\n", "is_post_treatment:is_treated:log_commits_c                              5.741e-03\n", "is_post_treatment:is_treated:log_newcomers                              2.509e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       6.382e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  4.975e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.125e-03\n", "is_post_treatment:is_treated:growth_phase1                              6.269e-03\n", "is_post_treatment:is_treated:growth_phase2                              3.504e-02\n", "is_post_treatment:is_treated:growth_phase3                              8.549e-03\n", "is_post_treatment:is_treated:growth_phase4                              6.377e-03\n", "is_post_treatment:is_treated:growth_phase5                              2.764e-02\n", "is_post_treatment:is_treated:project_main_language1                     6.699e-03\n", "is_post_treatment:is_treated:project_main_language2                     1.101e-02\n", "is_post_treatment:is_treated:project_main_language3                     1.190e-02\n", "is_post_treatment:is_treated:project_main_language4                     9.080e-03\n", "is_post_treatment:is_treated:project_main_language5                     7.759e-03\n", "is_post_treatment:is_treated:project_main_language6                     8.349e-03\n", "is_post_treatment:is_treated:project_main_language7                     1.084e-02\n", "is_post_treatment:is_treated:project_main_language8                     6.178e-03\n", "is_post_treatment:is_treated:project_main_language9                     1.176e-02\n", "                                                                               df\n", "(Intercept)                                                             2.897e+04\n", "is_post_treatment                                                       1.720e+04\n", "is_treated                                                              3.025e+04\n", "log_project_commits                                                     2.246e+06\n", "log_project_contributors                                                2.246e+06\n", "log_project_age                                                         2.244e+06\n", "is_post_treatment:is_treated:log_tenure_c                               1.112e+06\n", "is_post_treatment:is_treated:log_commit_percent_c                       1.115e+06\n", "is_post_treatment:is_treated:log_commits_c                              1.112e+06\n", "is_post_treatment:is_treated:log_newcomers                              1.100e+06\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.114e+06\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.115e+06\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.096e+06\n", "is_post_treatment:is_treated:growth_phase1                              1.343e+06\n", "is_post_treatment:is_treated:growth_phase2                              9.541e+05\n", "is_post_treatment:is_treated:growth_phase3                              1.210e+06\n", "is_post_treatment:is_treated:growth_phase4                              1.338e+06\n", "is_post_treatment:is_treated:growth_phase5                              8.200e+05\n", "is_post_treatment:is_treated:project_main_language1                     1.112e+06\n", "is_post_treatment:is_treated:project_main_language2                     1.113e+06\n", "is_post_treatment:is_treated:project_main_language3                     1.110e+06\n", "is_post_treatment:is_treated:project_main_language4                     1.113e+06\n", "is_post_treatment:is_treated:project_main_language5                     1.115e+06\n", "is_post_treatment:is_treated:project_main_language6                     1.113e+06\n", "is_post_treatment:is_treated:project_main_language7                     1.110e+06\n", "is_post_treatment:is_treated:project_main_language8                     1.112e+06\n", "is_post_treatment:is_treated:project_main_language9                     1.114e+06\n", "                                                                        t value\n", "(Intercept)                                                             224.094\n", "is_post_treatment                                                       -31.234\n", "is_treated                                                               -6.870\n", "log_project_commits                                                     238.516\n", "log_project_contributors                                                 79.532\n", "log_project_age                                                        -201.569\n", "is_post_treatment:is_treated:log_tenure_c                                15.608\n", "is_post_treatment:is_treated:log_commit_percent_c                        -9.522\n", "is_post_treatment:is_treated:log_commits_c                               -3.837\n", "is_post_treatment:is_treated:log_newcomers                               40.811\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         3.207\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -6.991\n", "is_post_treatment:is_treated:log_project_age_before_treatment            10.657\n", "is_post_treatment:is_treated:growth_phase1                              -39.742\n", "is_post_treatment:is_treated:growth_phase2                               39.502\n", "is_post_treatment:is_treated:growth_phase3                              -36.884\n", "is_post_treatment:is_treated:growth_phase4                              -36.629\n", "is_post_treatment:is_treated:growth_phase5                              -11.484\n", "is_post_treatment:is_treated:project_main_language1                      -3.230\n", "is_post_treatment:is_treated:project_main_language2                      -0.829\n", "is_post_treatment:is_treated:project_main_language3                       0.041\n", "is_post_treatment:is_treated:project_main_language4                       0.276\n", "is_post_treatment:is_treated:project_main_language5                       1.466\n", "is_post_treatment:is_treated:project_main_language6                       1.437\n", "is_post_treatment:is_treated:project_main_language7                      -0.845\n", "is_post_treatment:is_treated:project_main_language8                      -3.806\n", "is_post_treatment:is_treated:project_main_language9                      -0.073\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                             6.55e-12\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                               < 2e-16\n", "is_post_treatment:is_treated:log_commit_percent_c                       < 2e-16\n", "is_post_treatment:is_treated:log_commits_c                             0.000124\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      0.001343\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 2.73e-12\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:growth_phase1                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase2                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase3                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase4                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase5                              < 2e-16\n", "is_post_treatment:is_treated:project_main_language1                    0.001238\n", "is_post_treatment:is_treated:project_main_language2                    0.407275\n", "is_post_treatment:is_treated:project_main_language3                    0.967357\n", "is_post_treatment:is_treated:project_main_language4                    0.782427\n", "is_post_treatment:is_treated:project_main_language5                    0.142766\n", "is_post_treatment:is_treated:project_main_language6                    0.150643\n", "is_post_treatment:is_treated:project_main_language7                    0.398010\n", "is_post_treatment:is_treated:project_main_language8                    0.000141\n", "is_post_treatment:is_treated:project_main_language9                    0.941691\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ** \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "is_post_treatment:is_treated:growth_phase5                             ***\n", "is_post_treatment:is_treated:project_main_language1                    ** \n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                       \n", "is_post_treatment:is_treated:project_main_language4                       \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                       \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.06336008</td><td>0.4212718</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.06336008 & 0.4212718\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.06336008 | 0.4212718 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.06336008 0.4212718"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 4\n", "\n", "model_fixed_effects_developer_repo <- lmer(\n", "  log_pr_throughput ~ \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    \n", "\n", "    # Repo\n", "\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:project_main_language +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_fixed_effects_developer_repo,\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_developer_repo,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "    model_fixed_effects_developer_repo,\n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# save the model\n", "saveRDS(model_fixed_effects_developer_repo, \"../result/did_result_20250227/model_fixed_effects_developer_repo.rds\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'pbkrtest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(pbkrtest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'lmerTest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(lmerTest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "NOTE: A nesting structure was detected in the fitted model:\n", "    growth_phase %in% (is_post_treatment*is_treated), project_main_language %in% (is_post_treatment*is_treated)\n", "\n"]}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.719 0.00232 Inf     0.714     0.723\n", " CPlusPlus              0.719 0.00232 Inf     0.714     0.723\n", " CProgramming           0.719 0.00232 Inf     0.714     0.723\n", " CSharp                 0.719 0.00232 Inf     0.714     0.723\n", " Go                     0.719 0.00232 Inf     0.714     0.723\n", " Java                   0.719 0.00232 Inf     0.714     0.723\n", " PHP                    0.719 0.00232 Inf     0.714     0.723\n", " Python                 0.719 0.00232 Inf     0.714     0.723\n", " Rust                   0.719 0.00232 Inf     0.714     0.723\n", " TypeScript             0.719 0.00232 Inf     0.714     0.723\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.653 0.00231 Inf     0.648     0.657\n", " CPlusPlus              0.653 0.00231 Inf     0.648     0.657\n", " CProgramming           0.653 0.00231 Inf     0.648     0.657\n", " CSharp                 0.653 0.00231 Inf     0.648     0.657\n", " Go                     0.653 0.00231 Inf     0.648     0.657\n", " Java                   0.653 0.00231 Inf     0.648     0.657\n", " PHP                    0.653 0.00231 Inf     0.648     0.657\n", " Python                 0.653 0.00231 Inf     0.648     0.657\n", " Rust                   0.653 0.00231 Inf     0.648     0.657\n", " TypeScript             0.653 0.00231 Inf     0.648     0.657\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.698 0.00232 Inf     0.693     0.703\n", " CPlusPlus              0.698 0.00232 Inf     0.693     0.703\n", " CProgramming           0.698 0.00232 Inf     0.693     0.703\n", " CSharp                 0.698 0.00232 Inf     0.693     0.703\n", " Go                     0.698 0.00232 Inf     0.693     0.703\n", " Java                   0.698 0.00232 Inf     0.693     0.703\n", " PHP                    0.698 0.00232 Inf     0.693     0.703\n", " Python                 0.698 0.00232 Inf     0.693     0.703\n", " Rust                   0.698 0.00232 Inf     0.693     0.703\n", " TypeScript             0.698 0.00232 Inf     0.693     0.703\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " project_main_language emmean      SE  df asymp.LCL asymp.UCL\n", " JavaScript             0.730 0.00366 Inf     0.723     0.737\n", " CPlusPlus              0.749 0.00438 Inf     0.741     0.758\n", " CProgramming           0.753 0.00479 Inf     0.744     0.763\n", " CSharp                 0.745 0.00511 Inf     0.735     0.755\n", " Go                     0.745 0.00415 Inf     0.737     0.753\n", " Java                   0.745 0.00418 Inf     0.736     0.753\n", " PHP                    0.733 0.00484 Inf     0.724     0.743\n", " Python                 0.731 0.00353 Inf     0.724     0.738\n", " Rust                   0.719 0.00543 Inf     0.709     0.730\n", " TypeScript             0.744 0.00407 Inf     0.736     0.752\n", "\n", "Results are averaged over the levels of: growth_phase \n", "Degrees-of-freedom method: asymptotic \n", "Confidence level used: 0.95 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - CProgramming  0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - CSharp        0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Go            0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Java          0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - PHP           0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Python        0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Rust          0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - TypeScript    0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CProgramming   0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CSharp         0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Go             0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Java           0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - PHP            0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Python         0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Rust           0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - TypeScript     0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - CSharp      0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Go          0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Java        0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - PHP         0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Python      0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Rust        0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - TypeScript  0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Go                0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Java              0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - PHP               0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Python            0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Rust              0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - TypeScript        0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Java                  0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - PHP                   0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Python                0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Rust                  0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - TypeScript            0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - PHP                 0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - Python              0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - Rust                0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - TypeScript          0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - Python               0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - Rust                 0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - TypeScript           0.00e+00 0.00000 Inf     NaN     NaN\n", " Python - Rust              0.00e+00 0.00000 Inf     NaN     NaN\n", " Python - TypeScript        0.00e+00 0.00000 Inf     NaN     NaN\n", " Rust - TypeScript          0.00e+00 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - CProgramming  0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - CSharp        0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Go            0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Java          0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - PHP           0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Python        0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Rust          0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - TypeScript    0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CProgramming   0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CSharp         0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Go             0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Java           0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - PHP            0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Python         0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Rust           0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - TypeScript     0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - CSharp      0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Go          0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Java        0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - PHP         0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Python      0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Rust        0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - TypeScript  0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Go                0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Java              0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - PHP               0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Python            0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Rust              0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - TypeScript        0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Java                  0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - PHP                   0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Python                0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Rust                  0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - TypeScript            0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - PHP                 0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - Python              0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - Rust                0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - TypeScript          0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - Python               0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - Rust                 0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - TypeScript           0.00e+00 0.00000 Inf     NaN     NaN\n", " Python - Rust              0.00e+00 0.00000 Inf     NaN     NaN\n", " Python - TypeScript        0.00e+00 0.00000 Inf     NaN     NaN\n", " Rust - TypeScript          0.00e+00 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus     0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - CProgramming  0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - CSharp        0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Go            0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Java          0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - PHP           0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Python        0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - Rust          0.00e+00 0.00000 Inf     NaN     NaN\n", " JavaScript - TypeScript    0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CProgramming   0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - CSharp         0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Go             0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Java           0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - PHP            0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Python         0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - Rust           0.00e+00 0.00000 Inf     NaN     NaN\n", " CPlusPlus - TypeScript     0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - CSharp      0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Go          0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Java        0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - PHP         0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Python      0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - Rust        0.00e+00 0.00000 Inf     NaN     NaN\n", " CProgramming - TypeScript  0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Go                0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Java              0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - PHP               0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Python            0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - Rust              0.00e+00 0.00000 Inf     NaN     NaN\n", " CSharp - TypeScript        0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Java                  0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - PHP                   0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Python                0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - Rust                  0.00e+00 0.00000 Inf     NaN     NaN\n", " Go - TypeScript            0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - PHP                 0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - Python              0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - Rust                0.00e+00 0.00000 Inf     NaN     NaN\n", " Java - TypeScript          0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - Python               0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - Rust                 0.00e+00 0.00000 Inf     NaN     NaN\n", " PHP - TypeScript           0.00e+00 0.00000 Inf     NaN     NaN\n", " Python - Rust              0.00e+00 0.00000 Inf     NaN     NaN\n", " Python - TypeScript        0.00e+00 0.00000 Inf     NaN     NaN\n", " Rust - TypeScript          0.00e+00 0.00000 Inf     NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " contrast                   estimate      SE  df z.ratio p.value\n", " JavaScript - CPlusPlus    -1.94e-02 0.00427 Inf  -4.541  0.0003\n", " JavaScript - CProgramming -2.34e-02 0.00476 Inf  -4.914  <.0001\n", " JavaScript - CSharp       -1.48e-02 0.00517 Inf  -2.871  0.1839\n", " JavaScript - Go           -1.54e-02 0.00397 Inf  -3.882  0.0047\n", " JavaScript - Java         -1.48e-02 0.00402 Inf  -3.678  0.0106\n", " JavaScript - PHP          -3.44e-03 0.00481 Inf  -0.715  1.0000\n", " JavaScript - Python       -1.25e-03 0.00314 Inf  -0.400  1.0000\n", " JavaScript - Rust          1.03e-02 0.00560 Inf   1.837  1.0000\n", " JavaScript - TypeScript   -1.40e-02 0.00387 Inf  -3.609  0.0138\n", " CPlusPlus - CProgramming  -4.00e-03 0.00539 Inf  -0.743  1.0000\n", " CPlusPlus - CSharp         4.54e-03 0.00579 Inf   0.783  1.0000\n", " CPlusPlus - Go             3.95e-03 0.00484 Inf   0.817  1.0000\n", " CPlusPlus - Java           4.60e-03 0.00476 Inf   0.968  1.0000\n", " CPlusPlus - PHP            1.59e-02 0.00551 Inf   2.894  0.1712\n", " CPlusPlus - Python         1.81e-02 0.00412 Inf   4.402  0.0005\n", " CPlusPlus - Rust           2.97e-02 0.00624 Inf   4.752  0.0001\n", " CPlusPlus - TypeScript     5.42e-03 0.00472 Inf   1.148  1.0000\n", " CProgramming - CSharp      8.54e-03 0.00618 Inf   1.381  1.0000\n", " CProgramming - Go          7.96e-03 0.00529 Inf   1.504  1.0000\n", " CProgramming - Java        8.61e-03 0.00523 Inf   1.645  1.0000\n", " CProgramming - PHP         1.99e-02 0.00590 Inf   3.378  0.0329\n", " CProgramming - Python      2.21e-02 0.00464 Inf   4.770  0.0001\n", " CProgramming - Rust        3.37e-02 0.00660 Inf   5.100  <.0001\n", " CProgramming - TypeScript  9.42e-03 0.00518 Inf   1.817  1.0000\n", " CSharp - Go               -5.82e-04 0.00566 Inf  -0.103  1.0000\n", " CSharp - Java              6.88e-05 0.00563 Inf   0.012  1.0000\n", " CSharp - PHP               1.14e-02 0.00625 Inf   1.823  1.0000\n", " CSharp - Python            1.36e-02 0.00507 Inf   2.680  0.3318\n", " CSharp - Rust              2.51e-02 0.00690 Inf   3.641  0.0122\n", " CSharp - TypeScript        8.79e-04 0.00557 Inf   0.158  1.0000\n", " Go - Java                  6.51e-04 0.00463 Inf   0.141  1.0000\n", " Go - PHP                   1.20e-02 0.00533 Inf   2.248  1.0000\n", " Go - Python                1.42e-02 0.00389 Inf   3.642  0.0122\n", " Go - Rust                  2.57e-02 0.00603 Inf   4.263  0.0009\n", " Go - TypeScript            1.46e-03 0.00447 Inf   0.327  1.0000\n", " Java - PHP                 1.13e-02 0.00533 Inf   2.126  1.0000\n", " Java - Python              1.35e-02 0.00388 Inf   3.487  0.0220\n", " Java - Rust                2.51e-02 0.00608 Inf   4.122  0.0017\n", " Java - TypeScript          8.11e-04 0.00451 Inf   0.180  1.0000\n", " PHP - Python               2.18e-03 0.00472 Inf   0.462  1.0000\n", " PHP - Rust                 1.37e-02 0.00664 Inf   2.068  1.0000\n", " PHP - TypeScript          -1.05e-02 0.00525 Inf  -2.006  1.0000\n", " Python - Rust              1.15e-02 0.00554 Inf   2.084  1.0000\n", " Python - TypeScript       -1.27e-02 0.00376 Inf  -3.375  0.0332\n", " Rust - TypeScript         -2.43e-02 0.00597 Inf  -4.065  0.0022\n", "\n", "Results are averaged over some or all of the levels of: growth_phase \n", "Degrees-of-freedom method: asymptotic \n", "P value adjustment: bonferroni method for varying numbers of tests "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'pbkrtest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(pbkrtest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "Note: D.f. calculations have been disabled because the number of observations exceeds 3000.\n", "To enable adjustments, add the argument 'lmerTest.limit = 3482133' (or larger)\n", "[or, globally, 'set emm_options(lmerTest.limit = 3482133)' or larger];\n", "but be warned that this may result in large computation time and memory use.\n", "\n", "NOTE: A nesting structure was detected in the fitted model:\n", "    growth_phase %in% (is_post_treatment*is_treated), project_main_language %in% (is_post_treatment*is_treated)\n", "\n"]}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.719 0.00232 Inf     0.714     0.723\n", " accelerating    0.719 0.00232 Inf     0.714     0.723\n", " decelerating    0.719 0.00232 Inf     0.714     0.723\n", " first 3 months  0.719 0.00232 Inf     0.714     0.723\n", " saturation      0.719 0.00232 Inf     0.714     0.723\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.653 0.00231 Inf     0.648     0.657\n", " accelerating    0.653 0.00231 Inf     0.648     0.657\n", " decelerating    0.653 0.00231 Inf     0.648     0.657\n", " first 3 months  0.653 0.00231 Inf     0.648     0.657\n", " saturation      0.653 0.00231 Inf     0.648     0.657\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.698 0.00232 Inf     0.693     0.703\n", " accelerating    0.698 0.00232 Inf     0.693     0.703\n", " decelerating    0.698 0.00232 Inf     0.693     0.703\n", " first 3 months  0.698 0.00232 Inf     0.693     0.703\n", " saturation      0.698 0.00232 Inf     0.693     0.703\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " growth_phase   emmean      SE  df asymp.LCL asymp.UCL\n", " steady          0.543 0.00267 Inf     0.538     0.549\n", " accelerating    0.463 0.00364 Inf     0.456     0.470\n", " decelerating    0.559 0.00278 Inf     0.553     0.564\n", " first 3 months  1.592 0.00912 Inf     1.574     1.610\n", " saturation      0.540 0.00381 Inf     0.532     0.547\n", "\n", "Results are averaged over the levels of: project_main_language \n", "Degrees-of-freedom method: asymptotic \n", "Confidence level used: 0.95 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["is_treated = 0, is_post_treatment = 0:\n", " contrast                      estimate      SE  df  z.ratio p.value\n", " steady - accelerating          0.00000 0.00000 Inf      NaN     NaN\n", " steady - decelerating          0.00000 0.00000 Inf      NaN     NaN\n", " steady - first 3 months        0.00000 0.00000 Inf      NaN     NaN\n", " steady - saturation            0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - decelerating    0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - first 3 months  0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - saturation      0.00000 0.00000 Inf      NaN     NaN\n", " decelerating - first 3 months  0.00000 0.00000 Inf      NaN     NaN\n", " decelerating - saturation      0.00000 0.00000 Inf      NaN     NaN\n", " first 3 months - saturation    0.00000 0.00000 Inf      NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 0:\n", " contrast                      estimate      SE  df  z.ratio p.value\n", " steady - accelerating          0.00000 0.00000 Inf      NaN     NaN\n", " steady - decelerating          0.00000 0.00000 Inf      NaN     NaN\n", " steady - first 3 months        0.00000 0.00000 Inf      NaN     NaN\n", " steady - saturation            0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - decelerating    0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - first 3 months  0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - saturation      0.00000 0.00000 Inf      NaN     NaN\n", " decelerating - first 3 months  0.00000 0.00000 Inf      NaN     NaN\n", " decelerating - saturation      0.00000 0.00000 Inf      NaN     NaN\n", " first 3 months - saturation    0.00000 0.00000 Inf      NaN     NaN\n", "\n", "is_treated = 0, is_post_treatment = 1:\n", " contrast                      estimate      SE  df  z.ratio p.value\n", " steady - accelerating          0.00000 0.00000 Inf      NaN     NaN\n", " steady - decelerating          0.00000 0.00000 Inf      NaN     NaN\n", " steady - first 3 months        0.00000 0.00000 Inf      NaN     NaN\n", " steady - saturation            0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - decelerating    0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - first 3 months  0.00000 0.00000 Inf      NaN     NaN\n", " accelerating - saturation      0.00000 0.00000 Inf      NaN     NaN\n", " decelerating - first 3 months  0.00000 0.00000 Inf      NaN     NaN\n", " decelerating - saturation      0.00000 0.00000 Inf      NaN     NaN\n", " first 3 months - saturation    0.00000 0.00000 Inf      NaN     NaN\n", "\n", "is_treated = 1, is_post_treatment = 1:\n", " contrast                      estimate      SE  df  z.ratio p.value\n", " steady - accelerating          0.08029 0.00329 Inf   24.390  <.0001\n", " steady - decelerating         -0.01545 0.00232 Inf   -6.660  <.0001\n", " steady - first 3 months       -1.04883 0.00911 Inf -115.124  <.0001\n", " steady - saturation            0.00374 0.00349 Inf    1.070  0.8218\n", " accelerating - decelerating   -0.09575 0.00340 Inf  -28.148  <.0001\n", " accelerating - first 3 months -1.12913 0.01010 Inf -111.305  <.0001\n", " accelerating - saturation     -0.07656 0.00437 Inf  -17.509  <.0001\n", " decelerating - first 3 months -1.03338 0.00915 Inf -112.907  <.0001\n", " decelerating - saturation      0.01919 0.00355 Inf    5.399  <.0001\n", " first 3 months - saturation    1.05257 0.00989 Inf  106.462  <.0001\n", "\n", "Results are averaged over some or all of the levels of: project_main_language \n", "Degrees-of-freedom method: asymptotic \n", "P value adjustment: tukey method for varying family sizes "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["library(emmeans)\n", "library(ggplot2) # 可选，用于可视化\n", "\n", "# ----------------------------------------------------------------------\n", "# 1. 分析 project_main_language 与干预的交互作用 (is_post_treatment:is_treated:project_main_language)\n", "\n", "# 计算不同 project_main_language 组别在不同干预条件下的边际均值\n", "emm_language_interact <- emmeans(model_fixed_effects_repo_add_two,\n", "                                 ~ project_main_language | is_treated * is_post_treatment)\n", "# `|` 符号表示 \"在...条件下\"，这里是在不同的 `is_treated` 和 `is_post_treatment` 组合条件下，计算 `project_main_language` 各组别的边际均值\n", "\n", "emm_language_interact\n", "\n", "# 进行 pairwise comparisons，比较不同 project_main_language 组别在 *特定干预条件* 下的差异\n", "# 例如，比较在 \"treatment group\" (is_treated = TRUE, is_post_treatment = FALSE) 中，不同 language 的差异\n", "pairs(emm_language_interact, by = c(\"is_treated\", \"is_post_treatment\"), adjust = \"bonferroni\")\n", "# `by = c(\"is_treated\", \"is_post_treatment\")` 表示在相同的干预条件下进行组间比较\n", "\n", "# 可视化交互作用 (交互作用图)\n", "emmip(emm_language_interact, is_post_treatment ~ project_main_language | is_treated,\n", "      cov.reduce = range) + #  cov.reduce 控制协变量的取值范围\n", "  labs(title = \"Interaction: Language and Intervention Effect\",\n", "       y = \"Estimated Marginal Mean (log_pr_throughput)\",\n", "       x = \"Project Main Language\",\n", "       fill = \"Post-Treatment\") +\n", "  facet_grid(~ is_treated) # 分面显示 Treated 和 Control 组\n", "\n", "\n", "# ----------------------------------------------------------------------\n", "# 2. 分析 growth_phase 与干预的交互作用 (is_post_treatment:is_treated:growth_phase)\n", "\n", "# 计算不同 growth_phase 组别在不同干预条件下的边际均值\n", "emm_growth_phase_interact <- emmeans(model_fixed_effects_repo_add_two,\n", "                                     ~ growth_phase | is_treated * is_post_treatment)\n", "emm_growth_phase_interact\n", "\n", "# 进行 pairwise comparisons，比较不同 growth_phase 组别在 *特定干预条件* 下的差异\n", "pairs(emm_growth_phase_interact, by = c(\"is_treated\", \"is_post_treatment\"), adjust = \"tukey\")\n", "\n", "# 可视化交互作用\n", "emmip(emm_growth_phase_interact, is_post_treatment ~ growth_phase | is_treated,\n", "      cov.reduce = range) +\n", "  labs(title = \"Interaction: Growth Phase and Intervention Effect\",\n", "       y = \"Estimated Marginal Mean (log_pr_throughput)\",\n", "       x = \"Growth Phase\",\n", "       fill = \"Post-Treatment\") +\n", "  facet_grid(~ is_treated)\n", "\n", "\n", "# ----------------------------------------------------------------------\n", "# 3. (更复杂) 探索 project_main_language 和 growth_phase 的 *三重交互作用* (如果模型中包含)\n", "#   虽然你的模型公式中没有直接包含 project_main_language:growth_phase 的交互项，\n", "#   但你可以通过分析上述两个双重交互作用，来间接理解它们之间的关系。\n", "#   如果你想显式地模型化三重交互，可以在模型公式中添加  project_main_language:growth_phase:is_treated:is_post_treatment 或类似的项。\n", "\n", "#   以下代码仅供参考，假设你添加了三重交互项：\n", "#   emm_language_growth_phase_interact <- emmeans(model_fixed_effects_repo_add_two,\n", "#                                                ~ project_main_language * growth_phase | is_treated * is_post_treatment)\n", "#   emm_language_growth_phase_interact\n", "#   # ... 后续可以进行更复杂的对比和可视化 ...\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["levels(compiled_data_test$growth_phase)\n", "levels(compiled_data_test$project_main_language)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["contrasts(compiled_data_test$growth_phase)\n", "contrasts(compiled_data_test$project_main_language)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1] \"Model 1: Fixed Effects Only\"\n", "           is_post_treatment                   is_treated \n", "                    1.120150                     1.164912 \n", "         log_project_commits     log_project_contributors \n", "                    2.077838                     2.218928 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.500061                     1.272249 \n", "Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-682176.1 -682053.3  341098.0 -682196.1   1584049 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.5513 -0.4995  0.3670  0.6764  2.8364 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0004153 0.02038 \n", " repo_cohort_effect (Intercept) 0.0049732 0.07052 \n", " Residual                       0.0365622 0.19121 \n", "Number of obs: 1584059, groups:  \n", "time_cohort_effect, 30502; repo_cohort_effect, 30502\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df  t value Pr(>|t|)\n", "(Intercept)                   8.376e-01  6.386e-04  2.466e+04 1311.672   <2e-16\n", "is_post_treatment             1.941e-02  4.105e-04  1.697e+04   47.277   <2e-16\n", "is_treated                    1.250e-03  9.963e-04  3.479e+04    1.254   0.2097\n", "log_project_commits           1.428e-02  2.709e-04  1.323e+06   52.691   <2e-16\n", "log_project_contributors     -2.984e-02  2.796e-04  1.318e+06 -106.724   <2e-16\n", "log_project_age               2.016e-03  2.261e-04  1.270e+06    8.918   <2e-16\n", "is_post_treatment:is_treated -2.714e-03  8.494e-04  1.573e+06   -3.195   0.0014\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                      \n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ** \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.306                                                 \n", "is_treated  -0.598  0.129                                          \n", "lg_prjct_cm -0.027 -0.009  0.039                                   \n", "lg_prjct_cn  0.007  0.000 -0.049 -0.593                            \n", "log_prjct_g  0.039 -0.052 -0.019 -0.200      -0.314                \n", "is_pst_tr:_  0.095 -0.320 -0.371  0.002      -0.006       0.004    \n", "            R2m       R2c\n", "[1,] 0.01273963 0.1395516\n", "[1] \"Model 2: Fixed Effects Developer\"\n", "                                is_post_treatment \n", "                                         1.012434 \n", "                                       is_treated \n", "                                         1.010632 \n", "                              log_project_commits \n", "                                         2.090221 \n", "                         log_project_contributors \n", "                                         2.223448 \n", "                                  log_project_age \n", "                                         1.502507 \n", "        is_post_treatment:is_treated:log_tenure_c \n", "                                         1.536927 \n", "is_post_treatment:is_treated:log_commit_percent_c \n", "                                         1.266812 \n", "       is_post_treatment:is_treated:log_commits_c \n", "                                         1.833001 \n", "Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_tenure_c + is_post_treatment:is_treated:log_commit_percent_c +  \n", "    is_post_treatment:is_treated:log_commits_c + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-682222.6 -682075.3  341123.3 -682246.6   1584047 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.5492 -0.4994  0.3669  0.6765  2.8356 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0004122 0.02030 \n", " repo_cohort_effect (Intercept) 0.0049764 0.07054 \n", " Residual                       0.0365618 0.19121 \n", "Number of obs: 1584059, groups:  \n", "time_cohort_effect, 30502; repo_cohort_effect, 30502\n", "\n", "Fixed effects:\n", "                                                    Estimate Std. Error\n", "(Intercept)                                        8.377e-01  6.359e-04\n", "is_post_treatment                                  1.919e-02  3.897e-04\n", "is_treated                                         5.253e-04  9.282e-04\n", "log_project_commits                                1.443e-02  2.717e-04\n", "log_project_contributors                          -2.989e-02  2.799e-04\n", "log_project_age                                    1.982e-03  2.263e-04\n", "is_post_treatment:is_treated:log_tenure_c          1.527e-03  9.325e-04\n", "is_post_treatment:is_treated:log_commit_percent_c  5.723e-03  1.012e-03\n", "is_post_treatment:is_treated:log_commits_c        -6.605e-03  9.660e-04\n", "                                                          df  t value Pr(>|t|)\n", "(Intercept)                                        2.423e+04 1317.408  < 2e-16\n", "is_post_treatment                                  1.390e+04   49.239  < 2e-16\n", "is_treated                                         2.606e+04    0.566    0.571\n", "log_project_commits                                1.391e+06   53.106  < 2e-16\n", "log_project_contributors                           1.341e+06 -106.794  < 2e-16\n", "log_project_age                                    1.294e+06    8.759  < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c          4.572e+05    1.638    0.102\n", "is_post_treatment:is_treated:log_commit_percent_c  5.871e+05    5.657 1.55e-08\n", "is_post_treatment:is_treated:log_commits_c         4.918e+05   -6.837 8.08e-12\n", "                                                     \n", "(Intercept)                                       ***\n", "is_post_treatment                                 ***\n", "is_treated                                           \n", "log_project_commits                               ***\n", "log_project_contributors                          ***\n", "log_project_age                                   ***\n", "is_post_treatment:is_treated:log_tenure_c            \n", "is_post_treatment:is_treated:log_commit_percent_c ***\n", "is_post_treatment:is_treated:log_commits_c        ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "                           (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn\n", "is_pst_trtm                -0.293                                      \n", "is_treated                 -0.609  0.018                               \n", "lg_prjct_cm                -0.029 -0.005  0.046                        \n", "lg_prjct_cn                 0.007 -0.002 -0.054 -0.594                 \n", "log_prjct_g                 0.039 -0.056 -0.021 -0.200      -0.313     \n", "is_pst_trtmnt:s_trtd:lg_t_ -0.002  0.005  0.006  0.017      -0.013     \n", "is_p_:_:___                -0.024  0.080  0.076  0.034       0.013     \n", "is_pst_trtmnt:s_trtd:lg_c_  0.015 -0.043 -0.040 -0.072       0.033     \n", "                           lg_prjct_g is_pst_trtmnt:s_trtd:lg_t_ i__:_:___\n", "is_pst_trtm                                                               \n", "is_treated                                                                \n", "lg_prjct_cm                                                               \n", "lg_prjct_cn                                                               \n", "log_prjct_g                                                               \n", "is_pst_trtmnt:s_trtd:lg_t_ -0.034                                         \n", "is_p_:_:___                -0.023      0.059                              \n", "is_pst_trtmnt:s_trtd:lg_c_  0.026     -0.561                     -0.403   \n", "            R2m       R2c\n", "[1,] 0.01276111 0.1395721\n", "[1] \"Model 3: Fixed Effects Repo Add Two\"\n", "                                                                           GVIF\n", "is_post_treatment                                                      1.115236\n", "is_treated                                                             1.156967\n", "log_project_commits                                                    2.100527\n", "log_project_contributors                                               2.242804\n", "log_project_age                                                        1.510332\n", "is_post_treatment:is_treated:log_newcomers                             4.128453\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      2.211410\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 2.698674\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.979970\n", "is_post_treatment:is_treated:growth_phase                              4.458559\n", "is_post_treatment:is_treated:project_main_language                     1.430823\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.056047\n", "is_treated                                                                    1.075624\n", "log_project_commits                                                           1.449320\n", "log_project_contributors                                                      1.497599\n", "log_project_age                                                               1.228956\n", "is_post_treatment:is_treated:log_newcomers                                    2.031859\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             1.487081\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.642764\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.407114\n", "is_post_treatment:is_treated:growth_phase                                     1.161233\n", "is_post_treatment:is_treated:project_main_language                            1.020102\n", "Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:growth_phase + is_post_treatment:is_treated:project_main_language +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-680286.2 -679954.8  340170.1 -680340.2   1579517 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.5491 -0.4992  0.3664  0.6767  2.8339 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0004094 0.02023 \n", " repo_cohort_effect (Intercept) 0.0049400 0.07029 \n", " Residual                       0.0365658 0.19122 \n", "Number of obs: 1579544, groups:  \n", "time_cohort_effect, 30422; repo_cohort_effect, 30422\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             8.376e-01\n", "is_post_treatment                                                       1.948e-02\n", "is_treated                                                              1.237e-03\n", "log_project_commits                                                     1.442e-02\n", "log_project_contributors                                               -2.965e-02\n", "log_project_age                                                         1.849e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.639e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -8.570e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.256e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.214e-03\n", "is_post_treatment:is_treated:growth_phase1                              3.770e-03\n", "is_post_treatment:is_treated:growth_phase2                             -1.956e-02\n", "is_post_treatment:is_treated:growth_phase3                              4.854e-03\n", "is_post_treatment:is_treated:growth_phase4                              6.322e-03\n", "is_post_treatment:is_treated:growth_phase5                             -7.581e-04\n", "is_post_treatment:is_treated:project_main_language1                    -8.513e-03\n", "is_post_treatment:is_treated:project_main_language2                     2.310e-03\n", "is_post_treatment:is_treated:project_main_language3                     8.450e-03\n", "is_post_treatment:is_treated:project_main_language4                     1.419e-02\n", "is_post_treatment:is_treated:project_main_language5                    -8.661e-03\n", "is_post_treatment:is_treated:project_main_language6                     8.579e-04\n", "is_post_treatment:is_treated:project_main_language7                    -4.231e-03\n", "is_post_treatment:is_treated:project_main_language8                     4.965e-03\n", "is_post_treatment:is_treated:project_main_language9                    -3.995e-03\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             6.373e-04\n", "is_post_treatment                                                       4.091e-04\n", "is_treated                                                              9.916e-04\n", "log_project_commits                                                     2.727e-04\n", "log_project_contributors                                                2.814e-04\n", "log_project_age                                                         2.270e-04\n", "is_post_treatment:is_treated:log_newcomers                              6.371e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.071e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.169e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.051e-03\n", "is_post_treatment:is_treated:growth_phase1                              1.722e-03\n", "is_post_treatment:is_treated:growth_phase2                              9.311e-03\n", "is_post_treatment:is_treated:growth_phase3                              2.288e-03\n", "is_post_treatment:is_treated:growth_phase4                              1.754e-03\n", "is_post_treatment:is_treated:growth_phase5                              7.292e-03\n", "is_post_treatment:is_treated:project_main_language1                     1.861e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.030e-03\n", "is_post_treatment:is_treated:project_main_language3                     3.176e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.394e-03\n", "is_post_treatment:is_treated:project_main_language5                     2.006e-03\n", "is_post_treatment:is_treated:project_main_language6                     2.214e-03\n", "is_post_treatment:is_treated:project_main_language7                     3.012e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.691e-03\n", "is_post_treatment:is_treated:project_main_language9                     3.043e-03\n", "                                                                               df\n", "(Intercept)                                                             2.455e+04\n", "is_post_treatment                                                       1.675e+04\n", "is_treated                                                              3.409e+04\n", "log_project_commits                                                     1.437e+06\n", "log_project_contributors                                                1.428e+06\n", "log_project_age                                                         1.369e+06\n", "is_post_treatment:is_treated:log_newcomers                              4.141e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       4.950e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  4.743e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.492e+05\n", "is_post_treatment:is_treated:growth_phase1                              5.756e+05\n", "is_post_treatment:is_treated:growth_phase2                              3.515e+05\n", "is_post_treatment:is_treated:growth_phase3                              4.988e+05\n", "is_post_treatment:is_treated:growth_phase4                              5.724e+05\n", "is_post_treatment:is_treated:growth_phase5                              3.036e+05\n", "is_post_treatment:is_treated:project_main_language1                     4.668e+05\n", "is_post_treatment:is_treated:project_main_language2                     4.612e+05\n", "is_post_treatment:is_treated:project_main_language3                     4.598e+05\n", "is_post_treatment:is_treated:project_main_language4                     4.542e+05\n", "is_post_treatment:is_treated:project_main_language5                     4.580e+05\n", "is_post_treatment:is_treated:project_main_language6                     4.613e+05\n", "is_post_treatment:is_treated:project_main_language7                     4.660e+05\n", "is_post_treatment:is_treated:project_main_language8                     4.634e+05\n", "is_post_treatment:is_treated:project_main_language9                     4.592e+05\n", "                                                                        t value\n", "(Intercept)                                                            1314.267\n", "is_post_treatment                                                        47.613\n", "is_treated                                                                1.247\n", "log_project_commits                                                      52.888\n", "log_project_contributors                                               -105.369\n", "log_project_age                                                           8.145\n", "is_post_treatment:is_treated:log_newcomers                               -4.142\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        -7.999\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.075\n", "is_post_treatment:is_treated:log_project_age_before_treatment             2.107\n", "is_post_treatment:is_treated:growth_phase1                                2.189\n", "is_post_treatment:is_treated:growth_phase2                               -2.101\n", "is_post_treatment:is_treated:growth_phase3                                2.121\n", "is_post_treatment:is_treated:growth_phase4                                3.604\n", "is_post_treatment:is_treated:growth_phase5                               -0.104\n", "is_post_treatment:is_treated:project_main_language1                      -4.574\n", "is_post_treatment:is_treated:project_main_language2                       0.762\n", "is_post_treatment:is_treated:project_main_language3                       2.660\n", "is_post_treatment:is_treated:project_main_language4                       5.925\n", "is_post_treatment:is_treated:project_main_language5                      -4.318\n", "is_post_treatment:is_treated:project_main_language6                       0.387\n", "is_post_treatment:is_treated:project_main_language7                      -1.405\n", "is_post_treatment:is_treated:project_main_language8                       2.936\n", "is_post_treatment:is_treated:project_main_language9                      -1.313\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                             0.212290\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                        3.78e-16\n", "is_post_treatment:is_treated:log_newcomers                             3.44e-05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      1.26e-15\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 0.282557\n", "is_post_treatment:is_treated:log_project_age_before_treatment          0.035137\n", "is_post_treatment:is_treated:growth_phase1                             0.028568\n", "is_post_treatment:is_treated:growth_phase2                             0.035622\n", "is_post_treatment:is_treated:growth_phase3                             0.033907\n", "is_post_treatment:is_treated:growth_phase4                             0.000313\n", "is_post_treatment:is_treated:growth_phase5                             0.917194\n", "is_post_treatment:is_treated:project_main_language1                    4.78e-06\n", "is_post_treatment:is_treated:project_main_language2                    0.445800\n", "is_post_treatment:is_treated:project_main_language3                    0.007807\n", "is_post_treatment:is_treated:project_main_language4                    3.13e-09\n", "is_post_treatment:is_treated:project_main_language5                    1.58e-05\n", "is_post_treatment:is_treated:project_main_language6                    0.698394\n", "is_post_treatment:is_treated:project_main_language7                    0.160163\n", "is_post_treatment:is_treated:project_main_language8                    0.003324\n", "is_post_treatment:is_treated:project_main_language9                    0.189211\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                                \n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment          *  \n", "is_post_treatment:is_treated:growth_phase1                             *  \n", "is_post_treatment:is_treated:growth_phase2                             *  \n", "is_post_treatment:is_treated:growth_phase3                             *  \n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "is_post_treatment:is_treated:growth_phase5                                \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ** \n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ** \n", "is_post_treatment:is_treated:project_main_language9                       \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 24 > 12.\n", "Use print(...., correlation=TRUE)  or\n", "    vcov(....)        if you need it\n", "\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.01307345 0.1390289\n", "[1] \"Model 4: Fixed Effects Developer Repo\"\n", "                                                                           GVIF\n", "is_post_treatment                                                      1.096288\n", "is_treated                                                             1.035319\n", "log_project_commits                                                    2.057003\n", "log_project_contributors                                               2.207549\n", "log_project_age                                                        1.459817\n", "is_post_treatment:is_treated:log_tenure_c                              2.173357\n", "is_post_treatment:is_treated:log_commit_percent_c                      2.474932\n", "is_post_treatment:is_treated:log_commits_c                             4.526040\n", "is_post_treatment:is_treated:log_newcomers                             3.822900\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      5.545438\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.376988\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.173921\n", "is_post_treatment:is_treated:growth_phase                              4.486643\n", "is_post_treatment:is_treated:project_main_language                     1.480868\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.047038\n", "is_treated                                                                    1.017506\n", "log_project_commits                                                           1.434226\n", "log_project_contributors                                                      1.485782\n", "log_project_age                                                               1.208229\n", "is_post_treatment:is_treated:log_tenure_c                                     1.474231\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.573192\n", "is_post_treatment:is_treated:log_commits_c                                    2.127449\n", "is_post_treatment:is_treated:log_newcomers                                    1.955224\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.354875\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.837658\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.474422\n", "is_post_treatment:is_treated:growth_phase                                     1.161963\n", "is_post_treatment:is_treated:project_main_language                            1.022052\n", "Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:growth_phase + is_post_treatment:is_treated:project_main_language +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5601830  5602209 -2800885  5601770  2258982 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.1299 -0.7063 -0.0590  0.6865  5.8896 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " time_cohort_effect (Intercept) 0.009873 0.09936 \n", " repo_cohort_effect (Intercept) 0.400119 0.63255 \n", " Residual                       0.662940 0.81421 \n", "Number of obs: 2259012, groups:  \n", "time_cohort_effect, 30422; repo_cohort_effect, 30422\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             1.178e+00\n", "is_post_treatment                                                      -5.145e-02\n", "is_treated                                                             -5.171e-02\n", "log_project_commits                                                     2.355e-01\n", "log_project_contributors                                                8.349e-02\n", "log_project_age                                                        -1.683e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.276e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -4.055e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.203e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.024e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.046e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.478e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.396e-02\n", "is_post_treatment:is_treated:growth_phase1                             -2.492e-01\n", "is_post_treatment:is_treated:growth_phase2                              1.384e+00\n", "is_post_treatment:is_treated:growth_phase3                             -3.153e-01\n", "is_post_treatment:is_treated:growth_phase4                             -2.336e-01\n", "is_post_treatment:is_treated:growth_phase5                             -3.174e-01\n", "is_post_treatment:is_treated:project_main_language1                    -2.164e-02\n", "is_post_treatment:is_treated:project_main_language2                    -9.123e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.871e-04\n", "is_post_treatment:is_treated:project_main_language4                     2.507e-03\n", "is_post_treatment:is_treated:project_main_language5                     1.137e-02\n", "is_post_treatment:is_treated:project_main_language6                     1.200e-02\n", "is_post_treatment:is_treated:project_main_language7                    -9.164e-03\n", "is_post_treatment:is_treated:project_main_language8                    -2.351e-02\n", "is_post_treatment:is_treated:project_main_language9                    -8.598e-04\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             5.258e-03\n", "is_post_treatment                                                       1.647e-03\n", "is_treated                                                              7.527e-03\n", "log_project_commits                                                     9.875e-04\n", "log_project_contributors                                                1.050e-03\n", "log_project_age                                                         8.349e-04\n", "is_post_treatment:is_treated:log_tenure_c                               4.021e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       4.259e-03\n", "is_post_treatment:is_treated:log_commits_c                              5.741e-03\n", "is_post_treatment:is_treated:log_newcomers                              2.509e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       6.382e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  4.975e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.125e-03\n", "is_post_treatment:is_treated:growth_phase1                              6.269e-03\n", "is_post_treatment:is_treated:growth_phase2                              3.504e-02\n", "is_post_treatment:is_treated:growth_phase3                              8.549e-03\n", "is_post_treatment:is_treated:growth_phase4                              6.377e-03\n", "is_post_treatment:is_treated:growth_phase5                              2.764e-02\n", "is_post_treatment:is_treated:project_main_language1                     6.699e-03\n", "is_post_treatment:is_treated:project_main_language2                     1.101e-02\n", "is_post_treatment:is_treated:project_main_language3                     1.190e-02\n", "is_post_treatment:is_treated:project_main_language4                     9.080e-03\n", "is_post_treatment:is_treated:project_main_language5                     7.759e-03\n", "is_post_treatment:is_treated:project_main_language6                     8.349e-03\n", "is_post_treatment:is_treated:project_main_language7                     1.084e-02\n", "is_post_treatment:is_treated:project_main_language8                     6.178e-03\n", "is_post_treatment:is_treated:project_main_language9                     1.176e-02\n", "                                                                               df\n", "(Intercept)                                                             2.897e+04\n", "is_post_treatment                                                       1.720e+04\n", "is_treated                                                              3.025e+04\n", "log_project_commits                                                     2.246e+06\n", "log_project_contributors                                                2.246e+06\n", "log_project_age                                                         2.244e+06\n", "is_post_treatment:is_treated:log_tenure_c                               1.112e+06\n", "is_post_treatment:is_treated:log_commit_percent_c                       1.115e+06\n", "is_post_treatment:is_treated:log_commits_c                              1.112e+06\n", "is_post_treatment:is_treated:log_newcomers                              1.100e+06\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.114e+06\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.115e+06\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.096e+06\n", "is_post_treatment:is_treated:growth_phase1                              1.343e+06\n", "is_post_treatment:is_treated:growth_phase2                              9.541e+05\n", "is_post_treatment:is_treated:growth_phase3                              1.210e+06\n", "is_post_treatment:is_treated:growth_phase4                              1.338e+06\n", "is_post_treatment:is_treated:growth_phase5                              8.200e+05\n", "is_post_treatment:is_treated:project_main_language1                     1.112e+06\n", "is_post_treatment:is_treated:project_main_language2                     1.113e+06\n", "is_post_treatment:is_treated:project_main_language3                     1.110e+06\n", "is_post_treatment:is_treated:project_main_language4                     1.113e+06\n", "is_post_treatment:is_treated:project_main_language5                     1.115e+06\n", "is_post_treatment:is_treated:project_main_language6                     1.113e+06\n", "is_post_treatment:is_treated:project_main_language7                     1.110e+06\n", "is_post_treatment:is_treated:project_main_language8                     1.112e+06\n", "is_post_treatment:is_treated:project_main_language9                     1.114e+06\n", "                                                                        t value\n", "(Intercept)                                                             224.094\n", "is_post_treatment                                                       -31.234\n", "is_treated                                                               -6.870\n", "log_project_commits                                                     238.516\n", "log_project_contributors                                                 79.532\n", "log_project_age                                                        -201.569\n", "is_post_treatment:is_treated:log_tenure_c                                15.608\n", "is_post_treatment:is_treated:log_commit_percent_c                        -9.522\n", "is_post_treatment:is_treated:log_commits_c                               -3.837\n", "is_post_treatment:is_treated:log_newcomers                               40.811\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         3.207\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -6.991\n", "is_post_treatment:is_treated:log_project_age_before_treatment            10.657\n", "is_post_treatment:is_treated:growth_phase1                              -39.742\n", "is_post_treatment:is_treated:growth_phase2                               39.502\n", "is_post_treatment:is_treated:growth_phase3                              -36.884\n", "is_post_treatment:is_treated:growth_phase4                              -36.629\n", "is_post_treatment:is_treated:growth_phase5                              -11.484\n", "is_post_treatment:is_treated:project_main_language1                      -3.230\n", "is_post_treatment:is_treated:project_main_language2                      -0.829\n", "is_post_treatment:is_treated:project_main_language3                       0.041\n", "is_post_treatment:is_treated:project_main_language4                       0.276\n", "is_post_treatment:is_treated:project_main_language5                       1.466\n", "is_post_treatment:is_treated:project_main_language6                       1.437\n", "is_post_treatment:is_treated:project_main_language7                      -0.845\n", "is_post_treatment:is_treated:project_main_language8                      -3.806\n", "is_post_treatment:is_treated:project_main_language9                      -0.073\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                             6.55e-12\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                               < 2e-16\n", "is_post_treatment:is_treated:log_commit_percent_c                       < 2e-16\n", "is_post_treatment:is_treated:log_commits_c                             0.000124\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      0.001343\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 2.73e-12\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:growth_phase1                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase2                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase3                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase4                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase5                              < 2e-16\n", "is_post_treatment:is_treated:project_main_language1                    0.001238\n", "is_post_treatment:is_treated:project_main_language2                    0.407275\n", "is_post_treatment:is_treated:project_main_language3                    0.967357\n", "is_post_treatment:is_treated:project_main_language4                    0.782427\n", "is_post_treatment:is_treated:project_main_language5                    0.142766\n", "is_post_treatment:is_treated:project_main_language6                    0.150643\n", "is_post_treatment:is_treated:project_main_language7                    0.398010\n", "is_post_treatment:is_treated:project_main_language8                    0.000141\n", "is_post_treatment:is_treated:project_main_language9                    0.941691\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ** \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "is_post_treatment:is_treated:growth_phase5                             ***\n", "is_post_treatment:is_treated:project_main_language1                    ** \n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                       \n", "is_post_treatment:is_treated:project_main_language4                       \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                       \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 27 > 12.\n", "Use print(...., correlation=TRUE)  or\n", "    vcov(....)        if you need it\n", "\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.06336008 0.4212718\n"]}], "source": ["# save all summary and vif, r-squared into local file\n", "sink(\"../result/did_result_20250312/result_pr_accept_rate.txt\")\n", "print(\"Model 1: Fixed Effects Only\")\n", "\n", "print(vif_model_fixed_effects_only)\n", "\n", "print(summary(model_fixed_effects_only))\n", "\n", "print(r.squaredGLMM(model_fixed_effects_only))\n", "\n", "print(\"Model 2: Fixed Effects Developer\")\n", "\n", "vif_model2 = car::vif(model_fixed_effects_developer, type = \"predictor\", singular.ok = TRUE)\n", "\n", "print(vif_model2)\n", "print(summary(model_fixed_effects_developer))\n", "\n", "print(r.squaredGLMM(model_fixed_effects_developer))\n", "\n", "print(\"Model 3: Fixed Effects Repo Add Two\")\n", "\n", "vif_model_3 = car::vif(model_fixed_effects_repo_add_two, type = \"predictor\", singular.ok = TRUE)\n", "\n", "print(vif_model_3)\n", "print(summary(model_fixed_effects_repo_add_two))\n", "\n", "print(r.squaredGLMM(model_fixed_effects_repo_add_two))\n", "\n", "print(\"Model 4: Fixed Effects Developer Repo\")\n", "\n", "vif_model_developer_repo = car::vif(model_fixed_effects_developer_repo, type = \"predictor\", singular.ok = TRUE)\n", "\n", "print(vif_model_developer_repo)\n", "\n", "print(summary(model_fixed_effects_developer_repo))\n", "\n", "print(r.squaredGLMM(model_fixed_effects_developer_repo))\n", "\n", "sink()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model 5 Variation in growth stage and project_main_language"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# ------------------------------\n", "# 所有模型共享的数据预处理步骤\n", "# ------------------------------\n", "\n", "# 确保分类变量已定义\n", "compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "\n", "# 设置参考水平（若需对比特定控制组）\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "\n", "# 设置对比方式（根据分析目标选择）\n", "# 方案1: 对比各水平与参考水平（默认 treatment 编码）\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.treatment\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.treatment\"\n", "\n", "# 方案2: 对比各水平与整体均值（sum 编码）\n", "# contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "# contrasts(compiled_data_test$growth_phase) <- \"contr.sum\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["str(compiled_data_test$growth_phase)  # 应显示为 Factor\n", "attributes(compiled_data_test$growth_phase)$contrasts  # 检查对比矩阵"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# ------------------------------\n", "# Model 3\n", "# ------------------------------\n", "model_fixed_effects_repo_add_two <- lmer(\n", "  log_pr_throughput ~  \n", "    is_post_treatment + is_treated + \n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["summary(model_fixed_effects_repo_add_two)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["library(multcomp)\n", "\n", "# 对 project_main_language 各水平进行两两比较（控制组为 \"JavaScript\"）\n", "comparison_language <- glht(\n", "  model_fixed_effects_repo_add_two,\n", "  linfct = mcp(project_main_language = \"Tukey\"),\n", "  test = adjusted(\"bonferroni\")  # 或 \"fdr\"/\"holm\"\n", ")\n", "summary(comparison_language)\n", "\n", "# 对 growth_phase 各水平进行两两比较（控制组为 \"steady\"）\n", "comparison_growth <- glht(\n", "  model_fixed_effects_repo_add_two,\n", "  linfct = mcp(growth_phase = \"Tukey\"),\n", "  test = adjusted(\"bonferroni\")\n", ")\n", "summary(comparison_growth)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# ------------------------------\n", "# Model 4\n", "# ------------------------------\n", "model_fixed_effects_developer_repo <- lmer(\n", "  log_pr_throughput ~ \n", "    is_post_treatment + is_treated + \n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["summary(model_fixed_effects_developer_repo)"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 2}