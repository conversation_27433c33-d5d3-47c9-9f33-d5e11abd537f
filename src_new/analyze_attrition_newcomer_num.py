import pandas as pd
attritions = pd.read_csv('../data/attritions_20250227_add_burst_merged_add_growth_phase.csv')

def get_processed_commit_file_repo_name(repo_name):
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
        return None
    return repo_commit

from tqdm import tqdm

# iterate over bursts in each repo_name, an get commits to see if there are newcomers within 84 days, then add a column to the dataframe as a result
# of the iteration
repo_name = attritions['repo_name'].unique()
cnt_no_newcomers = 0
cnt_newcomers = 0

# Initialize newcomers column if it doesn't exist
if 'newcomers' not in attritions.columns:
  attritions['newcomers'] = 0

# Use tqdm to show progress
for repo in tqdm(repo_name, desc="Processing repositories"):
  repo_bursts = attritions[attritions['repo_name'] == repo]['burst'].unique()
  try:
    commits = get_processed_commit_file_repo_name(repo)
    commits['date'] = pd.to_datetime(commits['date'])
    
    for burst in repo_bursts:
      burst_data = attritions[attritions['burst'] == burst]
      event_date = pd.to_datetime(burst_data['attrition_date'].iloc[-1])
      
      # just select commits that are within 84 days of the last attrition date and compare the author_logins in these commits with author_logins from the beginning of the commits to the attrition date
      commits_within_84_days = commits[(commits['date'] >= pd.Timestamp(event_date).tz_localize('UTC')) & 
             (commits['date'] <= pd.Timestamp(event_date).tz_localize('UTC') + pd.Timedelta(days=84))]
      commits_before_attrition = commits[commits['date'] < pd.Timestamp(event_date).tz_localize('UTC')]
      newcomers = commits_within_84_days[~commits_within_84_days['author_login'].isin(commits_before_attrition['author_login'])]
      
      attritions.loc[attritions['burst'] == burst, 'newcomers'] = len(newcomers)
      
      if len(newcomers) > 0:
        # print(f'Newcomers in {repo} burst {burst}: {len(newcomers)}, with event date: {event_date} and logins: {newcomers["author_login"].unique()}')
        cnt_newcomers += 1
      else:
        cnt_no_newcomers += 1
  except Exception as e:
    print(f"Error processing {repo}: {e}")
    continue
    
print(f'No newcomers: {cnt_no_newcomers}, newcomers: {cnt_newcomers}')

attritions.to_csv('../data/attritions_20250227_add_burst_merged_add_growth_phase_add_newcomer_cnt.csv', index=False)