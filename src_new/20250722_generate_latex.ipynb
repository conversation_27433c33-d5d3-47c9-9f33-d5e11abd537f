{"cells": [{"cell_type": "markdown", "id": "8911ac0a", "metadata": {}, "source": ["# PR throughput"]}, {"cell_type": "code", "execution_count": 1, "id": "01cde3d9", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "# 原始的LaTeX表格字符串\n", "original_latex_general = r\"\"\"\n", "    \\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                        S[table-format=-1.3]\n", "                        S[table-format=-1.3]\n", "                        S[table-format=-1.3] \n", "                        !{\\qquad}\n", "                        S[table-format=-1.3]                           \n", "                        S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      }\n", "    \\toprule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\ \n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9}\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks} \\\\\n", "    \\midrule\n", "    \\textbf{Main Treatment Effects}\\\\\n", "    Is post-treatment & -6.500e-02*** & -6.194e-02 *** & .721*** & -6.110e-02*** & -4.424e-02*** & -3.358e-02*** & -2.846e-02*** & .721*** \\\\\n", "    Is treated group & -9.108e-03** & 1.841e-02* & -.067***&  5.226e-02*** & -5.160e-02*** & -6.045e-02*** & -6.740e-02*** &  -.067***\\\\\n", "    Is treated group : Is post-treatment & -1.044e-01*** & -9.779e-02*** & -.099*** & -9.445e-02*** & -1.064e-01*** & -1.013e-01*** & -9.566e-02*** & -.099***\\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)}  \\\\\n", "    project commits & 3.487e-01*** & 3.486e-01*** & .268*** & 3.569e-01*** & 2.707e-01*** & 2.706e-01*** & 2.699e-01*** & .268***\\\\\n", "    project contributors & 1.681e-01*** & 1.559e-01*** & .326*** & 1.437e-01*** & 2.994e-01*** & 3.104e-01*** & 3.186e-01*** & .326***\\\\\n", "    project age & -3.204e-01*** & -3.116e-01*** & -.213*** & -3.081e-01*** & -2.326e-01*** & -2.299e-01*** & -2.232e-01*** & -.213***\\\\\n", "\"\"\"\n", "original_latex = r\"\"\"\n", "\\begin{threeparttable}\n", "    \\begin{tabular}{l *{6}{S[table-format=-1.3]}} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{3}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{3}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-4} \\cmidrule(lr){5-7} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Core Contributor Characteristics} & & & & & & \\\\\n", "    Tenure (log) & 6.532e-02*** & 6.325e-02*** & 5.801e-02*** & 5.474e-02*** & 5.275e-02*** & 5.097e-02*** \\\\\n", "    Commit Percentage (log) &  -3.136e-02*** & -3.722e-02*** & -3.852e-02*** & -1.293e-02*** & -1.260e-02*** & -1.270e-02*** \\\\\n", "    \\#Commits (log) & -4.290e-02*** & -2.989e-02*** & -1.533e-03*** & -3.314e-02*** & -3.036e-02*** & -2.831e-02*** \\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & & \\\\\n", "    \\#Commits before treatment (log) & 1.891e-02*** &  1.258e-02 & -9.644e-03 & 2.388e-02*** & 1.800e-02*** & 1.463e-02*** \\\\\n", "    \\#Contributors before treatment (log) & -4.605e-02*** & -4.531e-02*** & -3.644e-02*** & -6.116e-02*** & -5.651e-02*** & -5.324e-02*** \\\\\n", "    Project Age before treatment (log) & 3.199e-02*** & 3.185e-02*** & 3.229e-02*** & 1.701e-02*** & 1.563e-02*** & 1.426e-02*** \\\\\n", "    \\#Newcomers after treatment (log) &  1.529e-01*** & 1.575e-01*** & 1.590e-01*** & 1.116e-01*** & 1.135e-01*** & 1.151e-01*** \\\\\n", "    \\midrule\n", "    \\textbf{Main Language} & & & & & & \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {} \\\\\n", "    MainLanguage-C++ & -5.473e-02** & 1.297e-02** & -1.433e-02** &  -5.946e-03 & -5.140e-03 & -3.346e-03 \\\\\n", "    MainLanguage-C & 2.548e-03*** & -3.281e-02*** & -1.505e-02*** & -1.196e-02* &  -3.816e-03 & 1.38E-03 \\\\\n", "    MainLanguage-C\\# & 6.006e-02 & 9.23E-03 & 3.577e-03 & 4.70E-03 & 1.78E-03 & 3.82E-03 \\\\\n", "    MainLanguage-Go & -2.478e-02** & -7.165e-03 & 3.394e-03 & -1.925e-03 & 2.51E-03 & 2.64E-03 \\\\\n", "    MainLanguage-Java & 3.911e-03 & 3.612e-02*** & 3.513e-02*** & 1.870e-02*** & 1.308e-02*** & 6.793e-03* \\\\\n", "    MainLanguage-PHP &  -5.256e-03** & 1.718e-02*** & 1.272e-02*** & 3.01E-03 & 7.25E-04 & 3.72E-03 \\\\\n", "    MainLanguage-Python &  2.296e-02 & -3.851e-02*** & -3.303e-02*** & -8.501e-03 & -8.767e-03 & -7.176e-03 \\\\\n", "    MainLanguage-Rust &  -6.830e-02*** & -1.253e-02*** & -1.381e-02*** & -1.143e-02*** & -9.376e-03*** & -7.085e-03** \\\\\n", "    MainLanguage-TypeScript & 5.086e-02 & 2.77E-03 & 1.03E-03 & -9.784e-03 & -9.009e-03 & -1.315e-02** \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & & \\\\\n", "    project commits &  3.488e-01*** & 3.487e-01*** & 3.572e-01*** & 2.676e-01*** & 2.691e-01*** & 2.697e-01*** \\\\\n", "    project contributors & 1.670e-01*** & 1.549e-01*** & 1.423e-01*** & 2.906e-01*** & 2.983e-01*** & 3.035e-01*** \\\\\n", "    project age & -3.201e-01*** & -3.117e-01*** & -3.079e-01*** & -2.273e-01*** & -2.234e-01*** & -2.157e-01*** \\\\\n", "    \\end{tabular} \n", "    \\end{threeparttable} \n", "\"\"\"\n", "def parse_full_stats(estimates_text, stars_text):\n", "    # 这个函数没有问题，保持原样\n", "    estimates_dict = {}\n", "    pattern_est = re.compile(r'^\\s*([\\w:._#]+)\\s+([-\\d.eE+]+)\\s*$')\n", "    for line in estimates_text.strip().split('\\n'):\n", "        match = pattern_est.match(line)\n", "        if match:\n", "            var_name, estimate = match.groups()\n", "            estimates_dict[var_name] = estimate\n", "\n", "    stars_dict = {}\n", "    pattern_stars = re.compile(r'^\\s*([\\w:._#]+)\\s+([*]*)\\s*$')\n", "    for line in stars_text.strip().split('\\n'):\n", "        match = pattern_stars.match(line)\n", "        if match:\n", "            var_name, stars = match.groups()\n", "            stars_dict[var_name] = stars.strip()\n", "\n", "    combined_stats = {}\n", "    for var_name, estimate in estimates_dict.items():\n", "        stars = stars_dict.get(var_name, '')\n", "        combined_stats[var_name] = {'estimate': estimate, 'stars': stars}\n", "        \n", "    return combined_stats\n", "\n", "# def update_latex_table(latex_table_text, combined_stats, target_column_name, debug=False):\n", "#     \"\"\"\n", "#     用新的统计值和星号更新LaTeX表格的指定列 (最终修正版)。\n", "    \n", "#     新增: debug模式，可以打印出匹配详情，帮助定位问题。\n", "#     \"\"\"\n", "#     label_to_stat_var = {\n", "#         'Is post-treatment': 'is_post_treatment',\n", "#         'Is treated': 'is_treated',\n", "#         'Is treated group : Is post-treatment': 'is_post_treatment:is_treated',\n", "#         'Tenure (log)': 'is_post_treatment:is_treated:log_tenure_c',\n", "#         'Commit Percentage (log)': 'is_post_treatment:is_treated:log_commit_percent_c',\n", "#         '#Commits (log)': 'is_post_treatment:is_treated:log_commits_c',\n", "#         '#Commits before treatment (log)': 'is_post_treatment:is_treated:log_project_commits_before_treatment',\n", "#         '#Contributors before treatment (log)': 'is_post_treatment:is_treated:log_project_contributors_before_treatment',\n", "#         'Project Age before treatment (log)': 'is_post_treatment:is_treated:log_project_age_before_treatment',\n", "#         '#Newcomers after treatment (log)': 'is_post_treatment:is_treated:log_newcomers', # <-- 我们关注的这一行\n", "#         'MainLanguage-C++': 'is_post_treatment:is_treated:project_main_language1',\n", "#         'MainLanguage-C': 'is_post_treatment:is_treated:project_main_language2',\n", "#         'MainLanguage-C#': 'is_post_treatment:is_treated:project_main_language3',\n", "#         'MainLanguage-Go': 'is_post_treatment:is_treated:project_main_language4',\n", "#         'MainLanguage-Java': 'is_post_treatment:is_treated:project_main_language5',\n", "#         'MainLanguage-PHP': 'is_post_treatment:is_treated:project_main_language6',\n", "#         'MainLanguage-Python': 'is_post_treatment:is_treated:project_main_language7',\n", "#         'MainLanguage-Rust': 'is_post_treatment:is_treated:project_main_language8',\n", "#         'MainLanguage-TypeScript': 'is_post_treatment:is_treated:project_main_language9',\n", "#         'project commits': 'log_project_commits',\n", "#         'project contributors': 'log_project_contributors',\n", "#         'project age': 'log_project_age'\n", "#     }\n", "\n", "#     lines = latex_table_text.strip().split('\\n')\n", "#     new_lines = []\n", "#     target_col_index = -1\n", "\n", "#     # 1. 精准定位目标列索引\n", "#     header_pattern = f'{{{target_column_name}}}'\n", "#     for line in lines:\n", "#         if '&' in line and header_pattern in line:\n", "#             headers = [h.strip().replace('\\\\', '').strip() for h in line.split('&')]\n", "#             try:\n", "#                 target_col_index = headers.index(header_pattern)\n", "#                 if debug:\n", "#                     print(f\"DEBUG: Found column '{target_column_name}' at index {target_col_index}.\")\n", "#                 break\n", "#             except ValueError:\n", "#                 continue\n", "    \n", "#     if target_col_index == -1:\n", "#         if debug:\n", "#             print(f\"DEBUG: ERROR! Column '{target_column_name}' not found.\")\n", "#         return latex_table_text\n", "\n", "#     # 2. 逐行处理和更新\n", "#     for line in lines:\n", "#         # 如果不是数据行（没有'&'），或者是一些特殊的LaTeX命令，直接保留\n", "#         if '&' not in line or not line.strip():\n", "#             new_lines.append(line)\n", "#             continue\n", "        \n", "#         parts = line.split('&')\n", "#         # 保证即使行不被修改，parts列表的长度也是足够的\n", "#         if len(parts) <= target_col_index:\n", "#             new_lines.append(line)\n", "#             continue\n", "\n", "#         raw_label = parts[0]\n", "#         # 强大的标签清理逻辑\n", "#         clean_label = re.sub(r'\\\\textbf{|}', '', raw_label).strip().replace('\\\\#', '#')\n", "\n", "#         # 检查清理后的标签是否是我们需要更新的目标\n", "#         if clean_label in label_to_stat_var:\n", "#             stat_var_name = label_to_stat_var[clean_label]\n", "            \n", "#             # 检查新的统计数据里是否有这一项\n", "#             if stat_var_name in combined_stats:\n", "#                 new_data = combined_stats[stat_var_name]\n", "#                 try:\n", "#                     # 统一格式化输出\n", "#                     new_value = f\"{float(new_data['estimate']):.3e}\"\n", "#                 except (Value<PERSON><PERSON><PERSON>, TypeError):\n", "#                     new_value = new_data['estimate'] # 格式化失败则使用原始值\n", "                \n", "#                 new_stars = new_data.get('stars', '')\n", "                \n", "#                 # *** 关键改动 ***\n", "#                 # 直接修改parts列表中的值\n", "#                 parts[target_col_index] = f\" {new_value}{new_stars} \"\n", "#                 if debug:\n", "#                     print(f\"  -> SUCCESS: Updating '{clean_label}' with value '{parts[target_col_index].strip()}'\")\n", "#             else:\n", "#                 if debug:\n", "#                     print(f\"  -> INFO: Label '{clean_label}' found, but no new stats for '{stat_var_name}'.\")\n", "#         else:\n", "#             if debug and raw_label.strip() and not raw_label.strip().startswith('\\\\'):\n", "#                  print(f\"  -> SKIP: Label '{clean_label}' not in dictionary.\")\n", "\n", "#         # 将修改后（或未修改）的parts重新组合成行\n", "#         new_lines.append('&'.join(parts))\n", "\n", "#     return '\\n'.join(new_lines)\n", "\n", "def update_latex_table(latex_table_text, combined_stats, target_column_name, debug=False):\n", "    \"\"\"\n", "    用新的统计值和星号更新LaTeX表格的指定列 (最终修正版)。\n", "    \n", "    新增: debug模式，可以打印出匹配详情，帮助定位问题。\n", "    \"\"\"\n", "    # ... (label_to_stat_var 字典保持不变) ...\n", "    label_to_stat_var = {\n", "        'Is post-treatment': 'is_post_treatment',\n", "        'Is treated': 'is_treated',\n", "        'Is treated group : Is post-treatment': 'is_post_treatment:is_treated',\n", "        'Tenure (log)': 'is_post_treatment:is_treated:log_tenure_c',\n", "        'Commit Percentage (log)': 'is_post_treatment:is_treated:log_commit_percent_c',\n", "        '#Commits (log)': 'is_post_treatment:is_treated:log_commits_c',\n", "        '#Commits before treatment (log)': 'is_post_treatment:is_treated:log_project_commits_before_treatment',\n", "        '#Contributors before treatment (log)': 'is_post_treatment:is_treated:log_project_contributors_before_treatment',\n", "        'Project Age before treatment (log)': 'is_post_treatment:is_treated:log_project_age_before_treatment',\n", "        '#Newcomers after treatment (log)': 'is_post_treatment:is_treated:log_newcomers',\n", "        'MainLanguage-C++': 'is_post_treatment:is_treated:project_main_language1',\n", "        'MainLanguage-C': 'is_post_treatment:is_treated:project_main_language2',\n", "        'MainLanguage-C#': 'is_post_treatment:is_treated:project_main_language3',\n", "        'MainLanguage-Go': 'is_post_treatment:is_treated:project_main_language4',\n", "        'MainLanguage-Java': 'is_post_treatment:is_treated:project_main_language5',\n", "        'MainLanguage-PHP': 'is_post_treatment:is_treated:project_main_language6',\n", "        'MainLanguage-Python': 'is_post_treatment:is_treated:project_main_language7',\n", "        'MainLanguage-Rust': 'is_post_treatment:is_treated:project_main_language8',\n", "        'MainLanguage-TypeScript': 'is_post_treatment:is_treated:project_main_language9',\n", "        'project commits': 'log_project_commits',\n", "        'project contributors': 'log_project_contributors',\n", "        'project age': 'log_project_age'\n", "    }\n", "\n", "    lines = latex_table_text.strip().split('\\n')\n", "    new_lines = []\n", "    target_col_index = -1\n", "\n", "    # 1. 精准定位目标列索引\n", "    header_pattern = f'{{{target_column_name}}}'\n", "    for line in lines:\n", "        if '&' in line and header_pattern in line:\n", "            headers = [h.strip().replace('\\\\', '').strip() for h in line.split('&')]\n", "            try:\n", "                target_col_index = headers.index(header_pattern)\n", "                if debug:\n", "                    print(f\"DEBUG: Found column '{target_column_name}' at index {target_col_index}.\")\n", "                break\n", "            except ValueError:\n", "                continue\n", "    \n", "    if target_col_index == -1:\n", "        if debug:\n", "            print(f\"DEBUG: ERROR! Column '{target_column_name}' not found.\")\n", "        return latex_table_text\n", "\n", "    # 2. 逐行处理和更新\n", "    for line in lines:\n", "        if '&' not in line or not line.strip():\n", "            new_lines.append(line)\n", "            continue\n", "        \n", "        # *** 关键改动: 开始 ***\n", "        # 先检查并保存行尾的 \\\\\n", "        line_ending = ''\n", "        processed_line = line\n", "        if processed_line.rstrip().endswith('\\\\\\\\'):\n", "            line_ending = ' \\\\\\\\'\n", "            processed_line = processed_line.rsplit('\\\\\\\\', 1)[0]\n", "        # *** 关键改动: 结束 ***\n", "\n", "        parts = processed_line.split('&')\n", "        if len(parts) <= target_col_index:\n", "            new_lines.append(line) # 如果列数不够，保留原始行\n", "            continue\n", "\n", "        raw_label = parts[0]\n", "        clean_label = re.sub(r'\\\\textbf{|}', '', raw_label).strip().replace('\\\\#', '#')\n", "\n", "        if clean_label in label_to_stat_var:\n", "            stat_var_name = label_to_stat_var[clean_label]\n", "            \n", "            if stat_var_name in combined_stats:\n", "                new_data = combined_stats[stat_var_name]\n", "                try:\n", "                    new_value = f\"{float(new_data['estimate']):.3e}\"\n", "                except (ValueErro<PERSON>, TypeError):\n", "                    new_value = new_data['estimate']\n", "                \n", "                new_stars = new_data.get('stars', '')\n", "                \n", "                # 修改parts列表中的值\n", "                parts[target_col_index] = f\" {new_value}{new_stars} \"\n", "                if debug:\n", "                    print(f\"  -> SUCCESS: Updating '{clean_label}' with value '{parts[target_col_index].strip()}'\")\n", "            else:\n", "                if debug:\n", "                    print(f\"  -> INFO: Label '{clean_label}' found, but no new stats for '{stat_var_name}'.\")\n", "        else:\n", "            if debug and raw_label.strip() and not raw_label.strip().startswith('\\\\'):\n", "                print(f\"  -> SKIP: Label '{clean_label}' not in dictionary.\")\n", "\n", "        # *** 关键改动: 开始 ***\n", "        # 将修改后的parts重新组合，并附加之前保存的行尾符号\n", "        new_line_str = '&'.join(parts) + line_ending\n", "        new_lines.append(new_line_str)\n", "        # *** 关键改动: 结束 ***\n", "\n", "    return '\\n'.join(new_lines)\n"]}, {"cell_type": "markdown", "id": "de95a282", "metadata": {}, "source": ["## PR throughput general\n"]}, {"cell_type": "code", "execution_count": 24, "id": "81788d01", "metadata": {}, "outputs": [], "source": ["original_latex_code_general = r\"\"\"\n", "    \\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                        S[table-format=-1.3]\n", "                        S[table-format=-1.3]\n", "                        S[table-format=-1.3] \n", "                        !{\\qquad}\n", "                        S[table-format=-1.3]                           \n", "                        S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      }\n", "    \\toprule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\ \n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9}\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks} \\\\\n", "    \\midrule\n", "    \\textbf{Main Treatment Effects}\\\\\n", "    Is post-treatment & -6.500e-02*** & -6.194e-02 *** & .721*** & -6.110e-02*** & -4.424e-02*** & -3.358e-02*** & -2.846e-02*** & .721*** \\\\\n", "    Is treated group & -9.108e-03** & 1.841e-02* & -.067***&  5.226e-02*** & -5.160e-02*** & -6.045e-02*** & -6.740e-02*** &  -.067***\\\\\n", "    Is treated group : Is post-treatment & -1.044e-01*** & -9.779e-02*** & -.099*** & -9.445e-02*** & -1.064e-01*** & -1.013e-01*** & -9.566e-02*** & -.099***\\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)}  \\\\\n", "    project commits & 3.487e-01*** & 3.486e-01*** & .268*** & 3.569e-01*** & 2.707e-01*** & 2.706e-01*** & 2.699e-01*** & .268***\\\\\n", "    project contributors & 1.681e-01*** & 1.559e-01*** & .326*** & 1.437e-01*** & 2.994e-01*** & 3.104e-01*** & 3.186e-01*** & .326***\\\\\n", "    project age & -3.204e-01*** & -3.116e-01*** & -.213*** & -3.081e-01*** & -2.326e-01*** & -2.299e-01*** & -2.232e-01*** & -.213***\\\\\n", "\"\"\"\n", "\n", "stats_estimates_data_365_general = \"\"\"\n", "(Intercept)                   1.076e+00\n", "is_post_treatment            -6.125e-02\n", "is_treated                    4.002e-02\n", "log_project_commits           3.504e-01\n", "log_project_contributors      1.519e-01\n", "log_project_age              -3.083e-01\n", "is_post_treatment:is_treated -9.671e-02\n", "\"\"\"\n", "\n", "stats_stars_data_365_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_10w_general = \"\"\"\n", "(Intercept)                   1.075e+00\n", "is_post_treatment            -5.439e-02\n", "is_treated                    5.896e-02\n", "log_project_commits           3.496e-01\n", "log_project_contributors      1.540e-01\n", "log_project_age              -3.098e-01\n", "is_post_treatment:is_treated -1.176e-01\n", "\"\"\"\n", "\n", "stats_stars_data_10w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_8w_general = \"\"\"\n", "is_post_treatment            -5.121e-02\n", "is_treated                    7.495e-02\n", "log_project_commits           3.477e-01\n", "log_project_contributors      1.557e-01\n", "log_project_age              -3.113e-01\n", "is_post_treatment:is_treated -1.350e-01\n", "\"\"\"\n", "\n", "stats_stars_data_8w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_6w_general = \"\"\"\n", "(Intercept)                   1.087e+00\n", "is_post_treatment            -5.342e-02\n", "is_treated                    9.127e-02\n", "log_project_commits           3.472e-01\n", "log_project_contributors      1.575e-01\n", "log_project_age              -3.134e-01\n", "is_post_treatment:is_treated -1.500e-01\n", "\"\"\"\n", "\n", "stats_stars_data_6w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 25, "id": "2a019c47", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                        S[table-format=-1.3]\n", "                        S[table-format=-1.3]\n", "                        S[table-format=-1.3] \n", "                        !{\\qquad}\n", "                        S[table-format=-1.3]                           \n", "                        S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      }\n", "    \\toprule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\ \n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9}\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}      \\\\\n", "    \\midrule\n", "    \\textbf{Main Treatment Effects}\\\\\n", "    Is post-treatment & -6.500e-02*** & -6.194e-02 *** & -6.125e-02*** & -6.110e-02*** & -5.342e-02*** & -5.121e-02*** & -5.439e-02*** & -6.125e-02***  \\\\\n", "    Is treated group & -9.108e-03** & 1.841e-02* & -.067***&  5.226e-02*** & -5.160e-02*** & -6.045e-02*** & -6.740e-02*** &  -.067***     \\\\\n", "    Is treated group : Is post-treatment & -1.044e-01*** & -9.779e-02*** & -9.671e-02*** & -9.445e-02*** & -1.500e-01*** & -1.350e-01*** & -1.176e-01*** & -9.671e-02***  \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)}  \\\\\n", "    project commits & 3.487e-01*** & 3.486e-01*** & 3.504e-01*** & 3.569e-01*** & 3.472e-01*** & 3.477e-01*** & 3.496e-01*** & 3.504e-01***  \\\\\n", "    project contributors & 1.681e-01*** & 1.559e-01*** & 1.519e-01*** & 1.437e-01*** & 1.575e-01*** & 1.557e-01*** & 1.540e-01*** & 1.519e-01***  \\\\\n", "    project age & -3.204e-01*** & -3.116e-01*** & -3.083e-01*** & -3.081e-01*** & -3.134e-01*** & -3.113e-01*** & -3.098e-01*** & -3.083e-01***  \\\\\n"]}], "source": ["target_columns = ['365 days', '6 weeks', '8 weeks', '10 weeks', '12 weeks']\n", "\n", "for target_column in target_columns:\n", "    if target_column == '365 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365_general, stats_stars_data_365_general)\n", "        updated_latex_code = update_latex_table(original_latex_code_general, parsed_data, target_column)\n", "    elif target_column == '6 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_6w_general, stats_stars_data_6w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '8 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_8w_general, stats_stars_data_8w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '10 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_10w_general, stats_stars_data_10w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '12 weeks':   \n", "        parsed_data = parse_full_stats(stats_estimates_data_365_general, stats_stars_data_365_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    else:\n", "        raise ValueError(f\"Invalid target column: {target_column}\")\n", "\n", "print(updated_latex_code)"]}, {"cell_type": "markdown", "id": "e2cad39c", "metadata": {}, "source": ["## Moderate PR throughput estimation and significance"]}, {"cell_type": "code", "execution_count": 26, "id": "9818191b", "metadata": {}, "outputs": [], "source": ["# --- 您的输入数据 ---\n", "\n", "# 输入 1: “180 days”列的数据\n", "stats_estimates_data_180 = \"\"\"\n", "is_post_treatment:is_treated:log_tenure_c 6.532e-02\n", "is_post_treatment:is_treated:log_commit_percent_c -3.136e-02\n", "is_post_treatment:is_treated:log_commits_c -4.290e-02\n", "is_post_treatment:is_treated:log_newcomers 1.529e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment 1.891e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.605e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment 3.199e-02\n", "is_post_treatment:is_treated:project_main_language1 -5.473e-02\n", "is_post_treatment:is_treated:project_main_language2 2.548e-03\n", "is_post_treatment:is_treated:project_main_language3 6.006e-02\n", "is_post_treatment:is_treated:project_main_language4 -2.478e-02\n", "is_post_treatment:is_treated:project_main_language5 3.911e-03\n", "is_post_treatment:is_treated:project_main_language6 -5.256e-03\n", "is_post_treatment:is_treated:project_main_language7 2.296e-02\n", "is_post_treatment:is_treated:project_main_language8 -6.830e-02\n", "is_post_treatment:is_treated:project_main_language9 5.086e-02\n", "log_project_commits 3.488e-01\n", "log_project_contributors 1.670e-01\n", "log_project_age -3.201e-01\n", "\"\"\"\n", "stats_stars_data_180 = \"\"\"\n", "is_post_treatment:is_treated:log_tenure_c ***\n", "is_post_treatment:is_treated:log_commit_percent_c ***\n", "is_post_treatment:is_treated:log_commits_c ***\n", "is_post_treatment:is_treated:log_newcomers ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment ***\n", "is_post_treatment:is_treated:project_main_language1 **\n", "is_post_treatment:is_treated:project_main_language2 ***\n", "is_post_treatment:is_treated:project_main_language3 \n", "is_post_treatment:is_treated:project_main_language4 **\n", "is_post_treatment:is_treated:project_main_language5 \n", "is_post_treatment:is_treated:project_main_language6 **\n", "is_post_treatment:is_treated:project_main_language7 \n", "is_post_treatment:is_treated:project_main_language8 ***\n", "is_post_treatment:is_treated:project_main_language9 \n", "log_project_commits ***\n", "log_project_contributors ***\n", "log_project_age ***\n", "\"\"\"\n", "\n", "stats_estimates_data_270 = \"\"\"\n", "(Intercept)                                                             1.089e+00\n", "is_post_treatment                                                      -7.585e-02\n", "is_treated                                                             -2.061e-02\n", "log_project_commits                                                     3.487e-01\n", "log_project_contributors                                                1.547e-01\n", "log_project_age                                                        -3.115e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.325e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.136e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.989e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.575e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.258e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.531e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.185e-02\n", "is_post_treatment:is_treated:project_main_language1                    -5.538e-02\n", "is_post_treatment:is_treated:project_main_language2                     7.861e-03\n", "is_post_treatment:is_treated:project_main_language3                     5.630e-02\n", "is_post_treatment:is_treated:project_main_language4                    -1.708e-02\n", "is_post_treatment:is_treated:project_main_language5                     5.641e-03\n", "is_post_treatment:is_treated:project_main_language6                    -3.646e-04\n", "is_post_treatment:is_treated:project_main_language7                     1.006e-02\n", "is_post_treatment:is_treated:project_main_language8                    -6.569e-02\n", "is_post_treatment:is_treated:project_main_language9                     5.098e-02\n", "\"\"\"\n", "stats_stars_data_270 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ** \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    *** \n", "\"\"\"\n", "\n", "stats_estimates_data_365 = \"\"\"\n", "(Intercept)                                                             1.083e+00\n", "is_post_treatment                                                      -7.478e-02\n", "is_treated                                                              2.318e-03\n", "log_project_commits                                                     3.508e-01\n", "log_project_contributors                                                1.506e-01\n", "log_project_age                                                        -3.083e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.310e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.170e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.138e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.581e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.153e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.942e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.970e-02\n", "is_post_treatment:is_treated:project_main_language1                    -5.793e-02\n", "is_post_treatment:is_treated:project_main_language2                     2.685e-02\n", "is_post_treatment:is_treated:project_main_language3                     5.266e-02\n", "is_post_treatment:is_treated:project_main_language4                    -1.385e-02\n", "is_post_treatment:is_treated:project_main_language5                    -3.962e-04\n", "is_post_treatment:is_treated:project_main_language6                    -5.498e-03\n", "is_post_treatment:is_treated:project_main_language7                     1.363e-02\n", "is_post_treatment:is_treated:project_main_language8                    -6.451e-02\n", "is_post_treatment:is_treated:project_main_language9                     4.762e-02\n", "\"\"\"\n", "\n", "stats_stars_data_365 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                                \n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    *  \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    .  \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_450 = \"\"\"\n", "(Intercept)                                                             1.083e+00\n", "is_post_treatment                                                      -7.431e-02\n", "is_treated                                                              1.554e-02\n", "log_project_commits                                                     3.572e-01\n", "log_project_contributors                                                1.422e-01\n", "log_project_age                                                        -3.077e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.333e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.302e-02\n", "is_post_treatment:is_treated:log_commits_c                             -1.242e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.597e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -3.951e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.781e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.040e-02\n", "is_post_treatment:is_treated:project_main_language1                    -5.715e-02\n", "is_post_treatment:is_treated:project_main_language2                     2.733e-02\n", "is_post_treatment:is_treated:project_main_language3                     4.414e-02\n", "is_post_treatment:is_treated:project_main_language4                    -1.282e-02\n", "is_post_treatment:is_treated:project_main_language5                     4.757e-03\n", "is_post_treatment:is_treated:project_main_language6                    -4.402e-03\n", "is_post_treatment:is_treated:project_main_language7                     8.109e-03\n", "is_post_treatment:is_treated:project_main_language8                    -6.554e-02\n", "is_post_treatment:is_treated:project_main_language9                     5.198e-02\n", "\"\"\"\n", "stats_stars_data_450 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ** \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    *  \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "# 输入 2: “6 weeks”列的数据\n", "stats_estimates_data_6w = \"\"\"\n", "(Intercept)                                                             1.098e+00\n", "is_post_treatment                                                      -7.493e-02\n", "is_treated                                                              3.356e-02\n", "log_project_commits                                                     3.470e-01\n", "log_project_contributors                                                1.561e-01\n", "log_project_age                                                        -3.132e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.615e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.697e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.429e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.579e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.413e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.137e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.874e-02\n", "is_post_treatment:is_treated:project_main_language1                    -8.308e-02\n", "is_post_treatment:is_treated:project_main_language2                     1.070e-02\n", "is_post_treatment:is_treated:project_main_language3                     7.924e-02\n", "is_post_treatment:is_treated:project_main_language4                    -2.839e-02\n", "is_post_treatment:is_treated:project_main_language5                     1.494e-02\n", "is_post_treatment:is_treated:project_main_language6                    -1.353e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.909e-02\n", "is_post_treatment:is_treated:project_main_language8                    -9.839e-02\n", "is_post_treatment:is_treated:project_main_language9                     7.862e-02\n", "\"\"\"\n", "stats_stars_data_6w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    *  \n", "is_post_treatment:is_treated:project_main_language6                    .  \n", "is_post_treatment:is_treated:project_main_language7                    *  \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_8w = \"\"\"\n", "(Intercept)                                                             1.087e+00\n", "is_post_treatment                                                      -7.043e-02\n", "is_treated                                                              2.251e-02\n", "log_project_commits                                                     3.478e-01\n", "log_project_contributors                                                1.544e-01\n", "log_project_age                                                        -3.113e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.648e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.386e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.387e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.591e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.531e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.065e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.854e-02\n", "is_post_treatment:is_treated:project_main_language1                    -7.605e-02\n", "is_post_treatment:is_treated:project_main_language2                     2.210e-02\n", "is_post_treatment:is_treated:project_main_language3                     6.790e-02\n", "is_post_treatment:is_treated:project_main_language4                    -2.191e-02\n", "is_post_treatment:is_treated:project_main_language5                     6.181e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.442e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.862e-02\n", "is_post_treatment:is_treated:project_main_language8                    -8.709e-02\n", "is_post_treatment:is_treated:project_main_language9                     7.260e-02\n", "\"\"\"\n", "\n", "stats_stars_data_8w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ** \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ** \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ** \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                    *  \n", "is_post_treatment:is_treated:project_main_language7                    *  \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_10w = \"\"\"\n", "(Intercept)                                                             1.083e+00\n", "is_post_treatment                                                      -7.095e-02\n", "is_treated                                                              1.322e-02\n", "log_project_commits                                                     3.499e-01\n", "log_project_contributors                                                1.528e-01\n", "log_project_age                                                        -3.098e-01\n", "is_post_treatment:is_treated:log_tenure_c                               6.515e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.261e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.276e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.596e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       8.439e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.019e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.881e-02\n", "is_post_treatment:is_treated:project_main_language1                    -6.846e-02\n", "is_post_treatment:is_treated:project_main_language2                     2.777e-02\n", "is_post_treatment:is_treated:project_main_language3                     6.210e-02\n", "is_post_treatment:is_treated:project_main_language4                    -1.716e-02\n", "is_post_treatment:is_treated:project_main_language5                    -1.486e-03\n", "is_post_treatment:is_treated:project_main_language6                    -9.235e-03\n", "is_post_treatment:is_treated:project_main_language7                     1.660e-02\n", "is_post_treatment:is_treated:project_main_language8                    -7.592e-02\n", "is_post_treatment:is_treated:project_main_language9                     6.152e-02\n", "\"\"\"\n", "\n", "stats_stars_data_10w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      .  \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ** \n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    *  \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 27, "id": "1a4e6073", "metadata": {}, "outputs": [], "source": ["original_latex = r\"\"\"\n", "\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks} \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \n", "    \\textbf{Core Contributor Characteristics} \\\\\n", "    Tenure (log) & 6.532e-02*** & 6.325e-02*** & .041*** & 6.333e-02*** & 5.661e-02*** & 5.538e-02*** & 5.383e-02*** & .041***\\\\\n", "    Commit Percentage (log) & -3.136e-02*** & -3.136e-02*** & -.024*** & -3.302e-02*** & -1.067e-02*** & -9.174e-03*** & -8.860e-03*** & -.024***\\\\\n", "    \\#Commits (log) & -4.290e-02*** & -2.989e-02*** & -.020*** & -1.242e-02** & -3.391e-02*** & -3.204e-02*** & -3.031e-02*** & -.020*** \\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & & \\\\\n", "    \\#Commits before treatment (log) & 1.891e-02*** & 1.258e-02*** & .020*** & -3.951e-03 & 2.188e-02*** & 1.724e-02*** & 1.435e-02*** & .020*** \\\\\n", "    \\#Contributors before treatment (log) & -4.605e-02*** & -4.531e-02*** & -.003 & -3.781e-02*** & -5.939e-02*** & -5.461e-02*** & -5.131e-02*** & -.003 \\\\\n", "    Project Age before treatment (log) & 3.199e-02*** & 3.185e-02*** & -.015*** & 3.040e-02*** & 1.766e-02*** & 1.665e-02*** & 1.546e-02*** & -.015*** \\\\\n", "    \\#Newcomers after treatment (log) & 1.529e-01*** & 1.575e-01*** & .011*** & 1.597e-01*** & 1.107e-01*** & 1.118e-01*** & 1.131e-01*** & .011*** \\\\\n", "    \\midrule\n", "    \\textbf{Main Language}  \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {} \\\\\n", "    MainLanguage-C++ & -5.473e-02** & -5.538e-02*** & -.058*** & -5.715e-02*** & -5.036e-02*** & -4.919e-02*** & -4.640e-02*** & -.058*** \\\\\n", "    MainLanguage-C & 2.548e-03*** & 7.861e-03 & .019*** & 2.733e-02*** & 7.069e-03 & 1.508e-02*** & 1.988e-02*** & .019***\\\\\n", "    MainLanguage-C\\# & 6.006e-02 & 5.630e-02*** & .051***  & 4.414e-02*** & 4.670e-02*** & 4.368e-02*** & 4.487e-02*** & .051*** \\\\\n", "    MainLanguage-Go & -2.478e-02** & -1.708e-02** & -.010**& -1.282e-02* & -8.537e-03 & -4.099e-03 & -3.817e-03 & -.010**\\\\\n", "    MainLanguage-Java & 3.911e-03 & 5.641e-03 & -.006* & 4.757e-03 & 2.878e-03 & -2.661e-03 & -8.658e-03** & -.006* \\\\\n", "    MainLanguage-PHP & -5.256e-03** & -3.646e-04 & -.019*** & -4.402e-03 & -1.280e-02** & -1.516e-02*** & -1.188e-02*** & -.019*** \\\\\n", "    MainLanguage-Python & 2.296e-02 & 1.006e-02 & .017*** & 8.109e-03 & 1.516e-02** & 1.504e-02** & 1.626e-02*** & .017***\\\\\n", "    MainLanguage-Rust & -6.830e-02*** & -6.569e-02*** & -.062*** & -6.554e-02*** & -5.972e-02*** & -5.760e-02*** & -5.434e-02*** & -.062*** \\\\\n", "    MainLanguage-TypeScript & 5.086e-02 & 5.098e-02*** & .078*** & 5.198e-02*** & 5.715e-02*** & 5.772e-02*** & 5.220e-02*** & .078*** \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & & \\\\\n", "    project commits & 3.488e-01*** & 3.487e-01*** & .265*** & 3.572e-01*** & 2.673e-01*** & 2.687e-01*** & 2.693e-01*** & .265***\\\\\n", "    project contributors & 1.670e-01*** & 1.547e-01*** & .322*** & 1.422e-01*** & 2.910e-01*** & 2.989e-01*** & 3.041e-01*** & .322***\\\\\n", "    project age & -3.201e-01*** & -3.115e-01*** & -.207*** & -3.077e-01*** & -2.262e-01*** & -2.218e-01*** & -2.136e-01*** & -.207*** \\\\\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 28, "id": "6a152ea3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}}   \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}         \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \n", "    \\textbf{Core Contributor Characteristics} \\\\\n", "    Tenure (log) & 6.532e-02*** & 6.325e-02*** & 6.310e-02*** & 6.333e-02*** & 6.615e-02*** & 6.648e-02*** & 6.515e-02*** & 6.310e-02***  \\\\\n", "    Commit Percentage (log) & -3.136e-02*** & -3.136e-02*** & -3.170e-02*** & -3.302e-02*** & -3.697e-02*** & -3.386e-02*** & -3.261e-02*** & -3.170e-02***  \\\\\n", "    \\#Commits (log) & -4.290e-02*** & -2.989e-02*** & -2.138e-02*** & -1.242e-02** & -2.429e-02*** & -2.387e-02*** & -2.276e-02*** & -2.138e-02***  \\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & &       \\\\\n", "    \\#Commits before treatment (log) & 1.891e-02*** & 1.258e-02*** & 2.153e-03 & -3.951e-03 & 2.413e-02*** & 1.531e-02** & 8.439e-03 & 2.153e-03  \\\\\n", "    \\#Contributors before treatment (log) & -4.605e-02*** & -4.531e-02*** & -3.942e-02*** & -3.781e-02*** & -4.137e-02*** & -4.065e-02*** & -4.019e-02*** & -3.942e-02***  \\\\\n", "    Project Age before treatment (log) & 3.199e-02*** & 3.185e-02*** & 2.970e-02*** & 3.040e-02*** & 2.874e-02*** & 2.854e-02*** & 2.881e-02*** & 2.970e-02***  \\\\\n", "    \\#Newcomers after treatment (log) & 1.529e-01*** & 1.575e-01*** & 1.581e-01*** & 1.597e-01*** & 1.579e-01*** & 1.591e-01*** & 1.596e-01*** & 1.581e-01***  \\\\\n", "    \\midrule\n", "    \\textbf{Main Language}  \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {}       \\\\\n", "    MainLanguage-C++ & -5.473e-02** & -5.538e-02*** & -5.793e-02*** & -5.715e-02*** & -8.308e-02*** & -7.605e-02*** & -6.846e-02*** & -5.793e-02***  \\\\\n", "    MainLanguage-C & 2.548e-03*** & 7.861e-03 & 2.685e-02*** & 2.733e-02*** & 1.070e-02 & 2.210e-02** & 2.777e-02*** & 2.685e-02***  \\\\\n", "    MainLanguage-C\\# & 6.006e-02 & 5.630e-02*** & 5.266e-02*** & 4.414e-02*** & 7.924e-02*** & 6.790e-02*** & 6.210e-02*** & 5.266e-02***  \\\\\n", "    MainLanguage-Go & -2.478e-02** & -1.708e-02** & -1.385e-02* & -1.282e-02* & -2.839e-02*** & -2.191e-02** & -1.716e-02** & -1.385e-02*  \\\\\n", "    MainLanguage-Java & 3.911e-03 & 5.641e-03 & -3.962e-04 & 4.757e-03 & 1.494e-02* & 6.181e-03 & -1.486e-03 & -3.962e-04  \\\\\n", "    MainLanguage-PHP & -5.256e-03** & -3.646e-04 & -5.498e-03 & -4.402e-03 & -1.353e-02 & -1.442e-02* & -9.235e-03 & -5.498e-03  \\\\\n", "    MainLanguage-Python & 2.296e-02 & 1.006e-02 & 1.363e-02 & 8.109e-03 & 1.909e-02* & 1.862e-02* & 1.660e-02* & 1.363e-02  \\\\\n", "    MainLanguage-Rust & -6.830e-02*** & -6.569e-02*** & -6.451e-02*** & -6.554e-02*** & -9.839e-02*** & -8.709e-02*** & -7.592e-02*** & -6.451e-02***  \\\\\n", "    MainLanguage-TypeScript & 5.086e-02 & 5.098e-02*** & 4.762e-02*** & 5.198e-02*** & 7.862e-02*** & 7.260e-02*** & 6.152e-02*** & 4.762e-02***  \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & &       \\\\\n", "    project commits & 3.488e-01*** & 3.487e-01*** & 3.508e-01*** & 3.572e-01*** & 3.470e-01*** & 3.478e-01*** & 3.499e-01*** & 3.508e-01***  \\\\\n", "    project contributors & 1.670e-01*** & 1.547e-01*** & 1.506e-01*** & 1.422e-01*** & 1.561e-01*** & 1.544e-01*** & 1.528e-01*** & 1.506e-01***  \\\\\n", "    project age & -3.201e-01*** & -3.115e-01*** & -3.083e-01*** & -3.077e-01*** & -3.132e-01*** & -3.113e-01*** & -3.098e-01*** & -3.083e-01***  \\\\\n"]}], "source": ["\n", "# --- 运行脚本 ---\n", "\n", "target_columns = ['180 days', '270 days', '365 days', '450 days', '6 weeks', '8 weeks', '10 weeks', '12 weeks']\n", "\n", "for target_column in target_columns:\n", "    if target_column == '180 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_180, stats_stars_data_180)\n", "        updated_latex_code = update_latex_table(original_latex, parsed_data, target_column)\n", "    elif target_column == '270 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_270, stats_stars_data_270)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '365 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365, stats_stars_data_365)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '450 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_450, stats_stars_data_450)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '6 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_6w, stats_stars_data_6w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '8 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_8w, stats_stars_data_8w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '10 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_10w, stats_stars_data_10w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '12 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365, stats_stars_data_365)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    else:\n", "        raise ValueError(f\"Invalid target column: {target_column}\")\n", "\n", "# --- 打印最终结果 ---\n", "# print(f\"--- 最终更新 '{target_column_1}' 和 '{target_column_2}' 后的 LaTeX 表格 ---\")\n", "print(updated_latex_code)"]}, {"cell_type": "markdown", "id": "bfcfeb46", "metadata": {}, "source": ["# PR ACCEPT RATE"]}, {"cell_type": "markdown", "id": "f6c3d3f9", "metadata": {}, "source": ["## General Effect"]}, {"cell_type": "code", "execution_count": 29, "id": "fb7b5da8", "metadata": {}, "outputs": [], "source": ["original_latex_code_general = r\"\"\"\n", "    \\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks} \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Main Treatment Effects} & & & & & & \\\\\n", "    Is post-treatment & 6.283e-04*** & 2.215e-03*** & .000 & 1.489e-03*** &  -2.804e-05 & 2.27E-06 & -1.180e-04 & .000\\\\\n", "    Is treated group & -3.649e-03*** & -1.868e-03*** & -.002** & -1.894e-03*** & -1.162e-03* & -1.352e-03* & -1.396e-03** & -.002**\\\\\n", "    Is treated group : Is post-treatment & -1.692e-03*** & -2.793e-03*** & -.003*** & -2.168e-03*** & -3.716e-03*** & -3.147e-03*** & -2.793e-03*** & -.003***\\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & & \\\\\n", "    project commits & -3.401e-03*** & -3.536e-03*** &  .006*** & -3.667e-03*** & 5.548e-03*** & 5.583e-03*** & 5.456e-03*** &  .006***\\\\\n", "    project contributors & -1.909e-02*** & -2.035e-02*** & -.025*** & -2.019e-02*** & -2.409e-02*** & -2.426e-02*** & -2.446e-02*** & -.025*** \\\\\n", "    project age & 6.361e-03*** & 6.695e-03*** & .005*** & 6.136e-03*** & 4.962e-03*** & 4.747e-03*** & 4.799e-03*** & .005***\\\\\n", "    \\midrule\n", "    Number of observations & {4,978,679} & {3,400,714} & {1,372,097} & {2,672,985} & {752,071} & {960,990} & {1,168,169} & {1,372,097}\\\\ % Numbers with commas or parentheses must be enclosed in {}\n", "    $R^2_m$ ($R^2_c$) & {0.03 (0.15)} & {0.04 (0.16)} & {0.02 (0.27)} & {0.04 (0.16)} & {0.02 (0.29)} & {0.02 (0.28)} & {0.02 (0.28)} & {0.02 (0.27)}\\\\\n", "\"\"\"\n", "\n", "stats_estimates_data_365_general = \"\"\"\n", "(Intercept)                   6.281e-01\n", "is_post_treatment             1.913e-03\n", "is_treated                   -1.723e-03\n", "log_project_commits          -3.538e-03\n", "log_project_contributors     -2.008e-02\n", "log_project_age               6.479e-03\n", "is_post_treatment:is_treated -2.480e-03\n", "\"\"\"\n", "\n", "stats_stars_data_365_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_10w_general = \"\"\"\n", "(Intercept)                   6.285e-01 \n", "is_post_treatment             1.888e-03\n", "is_treated                   -2.230e-03\n", "log_project_commits          -3.684e-03\n", "log_project_contributors     -1.967e-02\n", "log_project_age               6.669e-03\n", "is_post_treatment:is_treated -2.586e-03\n", "\"\"\"\n", "\n", "stats_stars_data_10w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_8w_general = \"\"\"\n", "(Intercept)                   6.292e-01\n", "is_post_treatment             1.227e-03\n", "is_treated                   -2.922e-03\n", "log_project_commits          -3.629e-03\n", "log_project_contributors     -1.950e-02\n", "log_project_age               6.453e-03\n", "is_post_treatment:is_treated -2.093e-03\n", "\"\"\"\n", "\n", "stats_stars_data_8w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_6w_general = \"\"\"\n", "(Intercept)                   6.295e-01\n", "is_post_treatment             1.299e-03\n", "is_treated                   -3.185e-03\n", "log_project_commits          -3.822e-03\n", "log_project_contributors     -1.919e-02\n", "log_project_age               6.260e-03\n", "is_post_treatment:is_treated -2.219e-03\n", "\"\"\"\n", "\n", "stats_stars_data_6w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 30, "id": "ad11e1b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}      \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Main Treatment Effects} & & & & & &    \\\\\n", "    Is post-treatment & 6.283e-04*** & 2.215e-03*** & 1.913e-03*** & 1.489e-03*** & 1.299e-03*** & 1.227e-03*** & 1.888e-03*** & 1.913e-03***  \\\\\n", "    Is treated group & -3.649e-03*** & -1.868e-03*** & -.002** & -1.894e-03*** & -1.162e-03* & -1.352e-03* & -1.396e-03** & -.002**     \\\\\n", "    Is treated group : Is post-treatment & -1.692e-03*** & -2.793e-03*** & -2.480e-03*** & -2.168e-03*** & -2.219e-03*** & -2.093e-03*** & -2.586e-03*** & -2.480e-03***  \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & &    \\\\\n", "    project commits & -3.401e-03*** & -3.536e-03*** & -3.538e-03*** & -3.667e-03*** & -3.822e-03*** & -3.629e-03*** & -3.684e-03*** & -3.538e-03***  \\\\\n", "    project contributors & -1.909e-02*** & -2.035e-02*** & -2.008e-02*** & -2.019e-02*** & -1.919e-02*** & -1.950e-02*** & -1.967e-02*** & -2.008e-02***  \\\\\n", "    project age & 6.361e-03*** & 6.695e-03*** & 6.479e-03*** & 6.136e-03*** & 6.260e-03*** & 6.453e-03*** & 6.669e-03*** & 6.479e-03***  \\\\\n", "    \\midrule\n", "    Number of observations & {4,978,679} & {3,400,714} & {1,372,097} & {2,672,985} & {752,071} & {960,990} & {1,168,169} & {1,372,097}\\\\ % Numbers with commas or parentheses must be enclosed in {}\n", "    $R^2_m$ ($R^2_c$) & {0.03 (0.15)} & {0.04 (0.16)} & {0.02 (0.27)} & {0.04 (0.16)} & {0.02 (0.29)} & {0.02 (0.28)} & {0.02 (0.28)} & {0.02 (0.27)}     \\\\\n"]}], "source": ["target_columns = ['365 days', '6 weeks', '8 weeks', '10 weeks', '12 weeks']\n", "\n", "for target_column in target_columns:\n", "    if target_column == '365 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365_general, stats_stars_data_365_general)\n", "        updated_latex_code = update_latex_table(original_latex_code_general, parsed_data, target_column)\n", "    elif target_column == '6 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_6w_general, stats_stars_data_6w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '8 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_8w_general, stats_stars_data_8w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '10 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_10w_general, stats_stars_data_10w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '12 weeks':   \n", "        parsed_data = parse_full_stats(stats_estimates_data_365_general, stats_stars_data_365_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    else:\n", "        raise ValueError(f\"Invalid target column: {target_column}\")\n", "\n", "print(updated_latex_code)"]}, {"cell_type": "markdown", "id": "1ed37ede", "metadata": {}, "source": ["## PR ACCEPT ESTIMATES AND STARS"]}, {"cell_type": "code", "execution_count": 2, "id": "1fc64665", "metadata": {}, "outputs": [], "source": ["# pr accept rate\n", "original_latex=r\"\"\"\n", "\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}  \n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "    } % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks}  & {12 weeks}\\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Core Contributor Characteristics} & & & & & & \\\\\n", "    Tenure (log) & -2.794e-04 & -8.707e-05 & .001** & 1.298e-04 & 1.742e-03** & 1.660e-03** & 1.754e-03***  & .001** \\\\\n", "    Commit Percentage (log) & -2.758e-03*** & -2.520e-03*** & -.003*** & -2.300e-03*** & -2.022e-03* & -1.701e-03* & -2.711e-03*** & -.003***\\\\\n", "    \\#Commits (log) & 1.393e-03** & 1.490e-03** & .000 & 1.924e-03** & -2.554e-04 & -1.816e-04 & -4.760e-05 & .000\\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & & \\\\\n", "    \\#Commits before treatment (log) & -4.109e-04 & -4.427e-04 & .000 & -9.848e-04 & 1.966e-04 & 1.777e-04 & -2.586e-04 & .000\\\\\n", "    \\#Contributors before treatment (log) & -4.281e-05 & 1.380e-04 & -.001 & 5.106e-04 & -1.626e-03* & -1.185e-03 & -1.092e-03 & -.001\\\\\n", "    Project Age before treatment (log) & 2.536e-04 & 5.594e-04 & .000 & 5.644e-04 & 6.826e-04 & 1.756e-04 & 1.314e-04 & .000\\\\\n", "    \\#Newcomers after treatment (log) & -2.433e-03*** & -2.764e-03*** & -.001* & -2.591e-03*** & 9.491e-06 & 2.730e-05 & 2.819e-04 & -.001*\\\\\n", "    \\midrule\n", "    \\textbf{Main Language} & & & & & & \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {} \\\\\n", "    MainLanguage-C++ & -2.935e-03*** & -3.526e-03*** & -.004*** & -4.315e-03*** & -5.327e-03*** & -4.765e-03*** & -5.032e-03*** & -.004***\\\\\n", "    MainLanguage-C & 4.234e-03*** & 4.455e-03*** & .001 & 4.387e-03*** & 7.403e-04 & 8.553e-04 & 6.176e-04  & .001\\\\\n", "    MainLanguage-C\\# & 3.650e-03*** & 4.792e-03*** & .006*** & 4.229e-03*** & 6.189e-03*** & 6.034e-03*** & 5.855e-03*** & .006***\\\\\n", "    MainLanguage-Go & 3.288e-03*** & 3.825e-03*** & .002*&  3.360e-03*** & 2.186e-03 & 2.223e-03 & 2.630e-03* & .002*\\\\\n", "    MainLanguage-Java & -3.489e-03*** & -3.509e-03*** & -.002** & -3.003e-03*** & -5.677e-04 & -1.922e-03 & -3.011e-03** & -.002**\\\\\n", "    MainLanguage-PHP & -2.518e-03*** & -1.494e-03* & -.002 & -1.823e-03* & -2.342e-03 & -1.876e-03 & -1.827e-03 & -.002\\\\\n", "    MainLanguage-Python & -1.365e-03 & -4.061e-03*** & .001 & -4.559e-03*** & -1.387e-03 & 5.851e-05 & 1.067e-03 & .001\\\\\n", "    MainLanguage-Rust & 1.146e-03* & 1.016e-03 & .000& 1.411e-03* & -7.733e-05 & -1.870e-04 & 6.384e-05  & .000\\\\\n", "    MainLanguage-TypeScript & 1.619e-03 & 8.643e-04 &.001 & 3.123e-03** & 3.321e-03 & 2.816e-03 & 2.600e-03 &.001 \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & & \\\\\n", "    project commits & -2.977e-03*** & -3.585e-03*** & .005***& -3.710e-03*** & 5.345e-03*** & 5.369e-03*** & 5.272e-03*** & .005***\\\\\n", "    project contributors & -1.988e-02*** & -2.028e-02*** & -.024***& -2.013e-02*** & -2.384e-02*** & -2.406e-02*** & -2.433e-02*** & -.024***\\\\\n", "    project age & 7.106e-03*** & 6.620e-03*** & .005***& 6.063e-03*** & 4.814e-03*** & 4.663e-03*** & 4.765e-03*** & .005***\\\\\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 3, "id": "a1b3b1a9", "metadata": {}, "outputs": [], "source": ["# --- 您的输入数据 ---\n", "\n", "# 输入 1: “180 days”列的数据\n", "stats_estimates_data_180 = \"\"\"\n", "(Intercept)                                                             6.297e-01\n", "is_post_treatment                                                       1.123e-03\n", "is_treated                                                             -3.226e-03\n", "log_project_commits                                                    -2.977e-03\n", "log_project_contributors                                               -1.988e-02\n", "log_project_age                                                         7.106e-03\n", "is_post_treatment:is_treated:log_tenure_c                              -2.794e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                      -2.758e-03\n", "is_post_treatment:is_treated:log_commits_c                              1.393e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.433e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -4.109e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.281e-05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.536e-04\n", "is_post_treatment:is_treated:project_main_language1                    -2.935e-03\n", "is_post_treatment:is_treated:project_main_language2                     4.234e-03\n", "is_post_treatment:is_treated:project_main_language3                     3.650e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.288e-03\n", "is_post_treatment:is_treated:project_main_language5                    -3.489e-03\n", "is_post_treatment:is_treated:project_main_language6                    -2.518e-03\n", "is_post_treatment:is_treated:project_main_language7                    -1.365e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.146e-03\n", "is_post_treatment:is_treated:project_main_language9                     1.619e-03\n", "\"\"\"\n", "stats_stars_data_180 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ** \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                    ***\n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    *  \n", "is_post_treatment:is_treated:project_main_language9                      \n", "\"\"\"\n", "\n", "stats_estimates_data_270 = \"\"\"\n", "(Intercept)                                                             6.284e-01\n", "is_post_treatment                                                       1.877e-03\n", "is_treated                                                             -2.736e-03\n", "log_project_commits                                                    -3.585e-03\n", "log_project_contributors                                               -2.028e-02\n", "log_project_age                                                         6.620e-03\n", "is_post_treatment:is_treated:log_tenure_c                              -8.707e-05\n", "is_post_treatment:is_treated:log_commit_percent_c                      -2.520e-03\n", "is_post_treatment:is_treated:log_commits_c                              1.490e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.764e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -4.427e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.380e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           5.594e-04\n", "is_post_treatment:is_treated:project_main_language1                    -3.526e-03\n", "is_post_treatment:is_treated:project_main_language2                     4.455e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.792e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.825e-03\n", "is_post_treatment:is_treated:project_main_language5                    -3.509e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.494e-03\n", "is_post_treatment:is_treated:project_main_language7                    -4.061e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.016e-03\n", "is_post_treatment:is_treated:project_main_language9                     8.643e-04\n", "\"\"\"\n", "stats_stars_data_270 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ** \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                    *  \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                      \n", "is_post_treatment:is_treated:project_main_language9                       \n", "\"\"\"\n", "stats_estimates_data_450 = \"\"\"\n", "is_post_treatment                                                       1.262e-03\n", "is_treated                                                             -2.463e-03\n", "log_project_commits                                                    -3.710e-03\n", "log_project_contributors                                               -2.013e-02\n", "log_project_age                                                         6.063e-03\n", "is_post_treatment:is_treated:log_tenure_c                               1.298e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                      -2.300e-03\n", "is_post_treatment:is_treated:log_commits_c                              1.924e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.591e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -9.848e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  5.106e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           5.644e-04\n", "is_post_treatment:is_treated:project_main_language1                    -4.315e-03\n", "is_post_treatment:is_treated:project_main_language2                     4.387e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.229e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.360e-03\n", "is_post_treatment:is_treated:project_main_language5                    -3.003e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.823e-03\n", "is_post_treatment:is_treated:project_main_language7                    -4.559e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.411e-03\n", "is_post_treatment:is_treated:project_main_language9                     3.123e-03\n", "\"\"\"\n", "stats_stars_data_450 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ** \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                    *  \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                    *  \n", "is_post_treatment:is_treated:project_main_language9                    ** \n", "\"\"\"\n", "\n", "stats_estimates_data_365 = \"\"\"\n", "(Intercept)                                                             6.282e-01\n", "is_post_treatment                                                       1.670e-03\n", "is_treated                                                             -2.342e-03\n", "log_project_commits                                                    -3.583e-03\n", "log_project_contributors                                               -2.002e-02\n", "log_project_age                                                         6.404e-03\n", "is_post_treatment:is_treated:log_tenure_c                               2.603e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.523e-03\n", "is_post_treatment:is_treated:log_commits_c                              9.916e-04\n", "is_post_treatment:is_treated:log_newcomers                             -2.618e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -2.429e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  3.130e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.437e-04\n", "is_post_treatment:is_treated:project_main_language1                    -4.389e-03\n", "is_post_treatment:is_treated:project_main_language2                     4.861e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.775e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.050e-03\n", "is_post_treatment:is_treated:project_main_language5                    -3.956e-03\n", "is_post_treatment:is_treated:project_main_language6                    -9.171e-04\n", "is_post_treatment:is_treated:project_main_language7                    -3.923e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.268e-03\n", "is_post_treatment:is_treated:project_main_language9                     2.321e-03\n", "\"\"\"\n", "\n", "stats_stars_data_365 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      ** \n", "is_post_treatment:is_treated:log_commits_c                             .  \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                    *  \n", "is_post_treatment:is_treated:project_main_language9                    *  \n", "\"\"\"\n", "\n", "\n", "# 输入 2: “6 weeks”列的数据\n", "stats_estimates_data_6w = \"\"\"\n", "(Intercept)                                                             6.296e-01\n", "is_post_treatment                                                       1.101e-03\n", "is_treated                                                             -3.653e-03\n", "log_project_commits                                                    -3.921e-03\n", "log_project_contributors                                               -1.904e-02\n", "log_project_age                                                         6.099e-03\n", "is_post_treatment:is_treated:log_tenure_c                               6.434e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.525e-03\n", "is_post_treatment:is_treated:log_commits_c                              5.553e-04\n", "is_post_treatment:is_treated:log_newcomers                             -2.856e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       8.067e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.213e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.390e-03\n", "is_post_treatment:is_treated:project_main_language1                    -5.572e-03\n", "is_post_treatment:is_treated:project_main_language2                     4.042e-03\n", "is_post_treatment:is_treated:project_main_language3                     5.862e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.580e-03\n", "is_post_treatment:is_treated:project_main_language5                    -3.078e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.256e-03\n", "is_post_treatment:is_treated:project_main_language7                    -4.938e-03\n", "is_post_treatment:is_treated:project_main_language8                     2.136e-03\n", "is_post_treatment:is_treated:project_main_language9                     2.666e-03\n", "\"\"\"\n", "stats_stars_data_6w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      *  \n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment *  \n", "is_post_treatment:is_treated:log_project_age_before_treatment          ** \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ** \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    *  \n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                    ** \n", "is_post_treatment:is_treated:project_main_language9                    .  \n", "\"\"\"\n", "\n", "stats_estimates_data_8w = \"\"\"\n", "(Intercept)                                                             6.293e-01\n", "is_post_treatment                                                       1.055e-03\n", "is_treated                                                             -3.348e-03\n", "log_project_commits                                                    -3.703e-03\n", "log_project_contributors                                               -1.939e-02\n", "log_project_age                                                         6.340e-03\n", "is_post_treatment:is_treated:log_tenure_c                               3.406e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.577e-03\n", "is_post_treatment:is_treated:log_commits_c                              8.995e-04\n", "is_post_treatment:is_treated:log_newcomers                             -2.947e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.562e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.682e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           8.031e-04\n", "is_post_treatment:is_treated:project_main_language1                    -5.305e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.855e-03\n", "is_post_treatment:is_treated:project_main_language3                     5.461e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.094e-03\n", "is_post_treatment:is_treated:project_main_language5                    -3.655e-03\n", "is_post_treatment:is_treated:project_main_language6                    -8.835e-04\n", "is_post_treatment:is_treated:project_main_language7                    -4.773e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.941e-03\n", "is_post_treatment:is_treated:project_main_language9                     2.908e-03\n", "\"\"\"\n", "\n", "stats_stars_data_8w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      ** \n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment          .  \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ** \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ** \n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                    ** \n", "is_post_treatment:is_treated:project_main_language9                    *  \n", "\"\"\"\n", "\n", "stats_estimates_data_10w = \"\"\"\n", "(Intercept)                                                             6.286e-01\n", "is_post_treatment                                                       1.642e-03\n", "is_treated                                                             -2.854e-03\n", "log_project_commits                                                    -3.739e-03\n", "log_project_contributors                                               -1.958e-02\n", "log_project_age                                                         6.579e-03\n", "is_post_treatment:is_treated:log_tenure_c                               2.499e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.448e-03\n", "is_post_treatment:is_treated:log_commits_c                              1.011e-03\n", "is_post_treatment:is_treated:log_newcomers                             -2.809e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -1.622e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  9.094e-06\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.856e-04\n", "is_post_treatment:is_treated:project_main_language1                    -5.050e-03\n", "is_post_treatment:is_treated:project_main_language2                     4.147e-03\n", "is_post_treatment:is_treated:project_main_language3                     5.054e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.530e-03\n", "is_post_treatment:is_treated:project_main_language5                    -4.008e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.011e-03\n", "is_post_treatment:is_treated:project_main_language7                    -4.219e-03\n", "is_post_treatment:is_treated:project_main_language8                     1.485e-03\n", "is_post_treatment:is_treated:project_main_language9                     2.795e-03\n", "\"\"\"\n", "\n", "stats_stars_data_10w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                                 \n", "is_post_treatment:is_treated:log_commit_percent_c                      ** \n", "is_post_treatment:is_treated:log_commits_c                             .  \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                    *  \n", "is_post_treatment:is_treated:project_main_language9                    *  \n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "id": "d30582c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}  \n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "    } % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}}   \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks}  & {12 weeks}        \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Core Contributor Characteristics} & & & & & &       \\\\\n", "    Tenure (log) & -2.794e-04 & -8.707e-05 & 2.603e-04 & 1.298e-04 & 6.434e-04 & 3.406e-04 & 2.499e-04 & 2.603e-04  \\\\\n", "    Commit Percentage (log) & -2.758e-03*** & -2.520e-03*** & -1.523e-03** & -2.300e-03*** & -1.525e-03* & -1.577e-03** & -1.448e-03** & -1.523e-03**  \\\\\n", "    \\#Commits (log) & 1.393e-03** & 1.490e-03** & 9.916e-04 & 1.924e-03** & 5.553e-04 & 8.995e-04 & 1.011e-03 & 9.916e-04  \\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & &       \\\\\n", "    \\#Commits before treatment (log) & -4.109e-04 & -4.427e-04 & -2.429e-04 & -9.848e-04 & 8.067e-04 & 2.562e-04 & -1.622e-04 & -2.429e-04  \\\\\n", "    \\#Contributors before treatment (log) & -4.281e-05 & 1.380e-04 & 3.130e-04 & 5.106e-04 & -1.213e-03* & -4.682e-04 & 9.094e-06 & 3.130e-04  \\\\\n", "    Project Age before treatment (log) & 2.536e-04 & 5.594e-04 & 3.437e-04 & 5.644e-04 & 1.390e-03** & 8.031e-04 & 4.856e-04 & 3.437e-04  \\\\\n", "    \\#Newcomers after treatment (log) & -2.433e-03*** & -2.764e-03*** & -2.618e-03*** & -2.591e-03*** & -2.856e-03*** & -2.947e-03*** & -2.809e-03*** & -2.618e-03***  \\\\\n", "    \\midrule\n", "    \\textbf{Main Language} & & & & & &       \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {}       \\\\\n", "    MainLanguage-C++ & -2.935e-03*** & -3.526e-03*** & -4.389e-03*** & -4.315e-03*** & -5.572e-03*** & -5.305e-03*** & -5.050e-03*** & -4.389e-03***  \\\\\n", "    MainLanguage-C & 4.234e-03*** & 4.455e-03*** & 4.861e-03*** & 4.387e-03*** & 4.042e-03** & 3.855e-03** & 4.147e-03*** & 4.861e-03***  \\\\\n", "    MainLanguage-C\\# & 3.650e-03*** & 4.792e-03*** & 4.775e-03*** & 4.229e-03*** & 5.862e-03*** & 5.461e-03*** & 5.054e-03*** & 4.775e-03***  \\\\\n", "    MainLanguage-Go & 3.288e-03*** & 3.825e-03*** & 3.050e-03*** & 3.360e-03*** & 2.580e-03* & 3.094e-03** & 3.530e-03*** & 3.050e-03***  \\\\\n", "    MainLanguage-Java & -3.489e-03*** & -3.509e-03*** & -3.956e-03*** & -3.003e-03*** & -3.078e-03*** & -3.655e-03*** & -4.008e-03*** & -3.956e-03***  \\\\\n", "    MainLanguage-PHP & -2.518e-03*** & -1.494e-03* & -9.171e-04 & -1.823e-03* & -1.256e-03 & -8.835e-04 & -1.011e-03 & -9.171e-04  \\\\\n", "    MainLanguage-Python & -1.365e-03 & -4.061e-03*** & -3.923e-03*** & -4.559e-03*** & -4.938e-03*** & -4.773e-03*** & -4.219e-03*** & -3.923e-03***  \\\\\n", "    MainLanguage-Rust & 1.146e-03* & 1.016e-03 & 1.268e-03* & 1.411e-03* & 2.136e-03** & 1.941e-03** & 1.485e-03* & 1.268e-03*  \\\\\n", "    MainLanguage-TypeScript & 1.619e-03 & 8.643e-04 & 2.321e-03* & 3.123e-03** & 2.666e-03 & 2.908e-03* & 2.795e-03* & 2.321e-03*  \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & &       \\\\\n", "    project commits & -2.977e-03*** & -3.585e-03*** & -3.583e-03*** & -3.710e-03*** & -3.921e-03*** & -3.703e-03*** & -3.739e-03*** & -3.583e-03***  \\\\\n", "    project contributors & -1.988e-02*** & -2.028e-02*** & -2.002e-02*** & -2.013e-02*** & -1.904e-02*** & -1.939e-02*** & -1.958e-02*** & -2.002e-02***  \\\\\n", "    project age & 7.106e-03*** & 6.620e-03*** & 6.404e-03*** & 6.063e-03*** & 6.099e-03*** & 6.340e-03*** & 6.579e-03*** & 6.404e-03***  \\\\\n"]}], "source": ["\n", "# --- 运行脚本 ---\n", "\n", "target_columns = ['180 days', '270 days', '365 days', '450 days', '6 weeks', '8 weeks', '10 weeks', '12 weeks']\n", "\n", "for target_column in target_columns:\n", "    if target_column == '180 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_180, stats_stars_data_180)\n", "        updated_latex_code = update_latex_table(original_latex, parsed_data, target_column)\n", "    elif target_column == '270 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_270, stats_stars_data_270)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '365 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365, stats_stars_data_365)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '450 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_450, stats_stars_data_450)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '6 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_6w, stats_stars_data_6w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '8 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_8w, stats_stars_data_8w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '10 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_10w, stats_stars_data_10w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '12 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365, stats_stars_data_365)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    else:\n", "        raise ValueError(f\"Invalid target column: {target_column}\")\n", "\n", "# --- 打印最终结果 ---\n", "# print(f\"--- 最终更新 '{target_column_1}' 和 '{target_column_2}' 后的 LaTeX 表格 ---\")\n", "print(updated_latex_code)"]}, {"cell_type": "markdown", "id": "2f182f89", "metadata": {}, "source": ["# PR TIME TO MERGE"]}, {"cell_type": "markdown", "id": "d1ba80c6", "metadata": {}, "source": ["## General Effect"]}, {"cell_type": "code", "execution_count": 32, "id": "cbb98844", "metadata": {}, "outputs": [], "source": ["original_latex_code_general = r\"\"\"\n", "    \\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}\\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Main Treatment Effects} & & & & & & \\\\\n", "    Is post-treatment & 9.609e-02*** & 8.199e-02*** & -.063*** & 8.382e-02*** & -8.976e-02*** & -7.815e-02*** & -7.112e-02*** & -.063*** \\\\\n", "    Is treated group & 1.794e-01*** & 1.753e-01*** & .153***& 1.826e-01*** & 1.684e-01*** & 1.629e-01*** & 1.582e-01*** & .153***\\\\\n", "    Is treated group : Is post-treatment & -1.369e-01*** & -1.717e-01*** & -.043*** & -1.713e-01*** & -5.306e-02*** & -4.913e-02*** & -4.362e-02*** & -.043***\\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & & \\\\\n", "    project commits & 1.67E-01 & 2.331e-01*** & .003 & 2.22E-01 & 1.610e-02** & 1.047e-02* & 6.83E-03 & .003\\\\\n", "    project contributors & 3.131e-01*** & 3.505e-01*** & .470*** & 3.514e-01*** & 3.4567e-01*** & 4.613e-01*** & 4.681e-01*** & .470***\\\\\n", "    project age & 6.925e-02*** & -5.369e-02*** & .037*** & -4.306e-02*** & 6.925e-02*** & 2.231e-02*** & 2.518e-02*** & .037***\\\\\n", "\"\"\"\n", "\n", "stats_estimates_data_365_general = \"\"\"\n", "(Intercept)                   3.284e+00\n", "is_post_treatment             8.771e-02\n", "is_treated                    1.882e-01\n", "log_project_commits           2.276e-01\n", "log_project_contributors      3.480e-01\n", "log_project_age              -5.042e-02\n", "is_post_treatment:is_treated -1.746e-01\n", "\"\"\"\n", "\n", "stats_stars_data_365_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_10w_general = \"\"\"\n", "(Intercept)                   3.285e+00\n", "is_post_treatment             8.345e-02\n", "is_treated                    1.947e-01\n", "log_project_commits           2.284e-01\n", "log_project_contributors      3.545e-01\n", "log_project_age              -5.178e-02\n", "is_post_treatment:is_treated -1.805e-01\n", "\"\"\"\n", "\n", "stats_stars_data_10w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_8w_general = \"\"\"\n", "(Intercept)                   3.295e+00\n", "is_post_treatment             7.680e-02\n", "is_treated                    1.931e-01\n", "log_project_commits           2.273e-01\n", "log_project_contributors      3.619e-01\n", "log_project_age              -5.166e-02\n", "is_post_treatment:is_treated -1.895e-01\n", "\"\"\"\n", "\n", "stats_stars_data_8w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\"\n", "\n", "stats_estimates_data_6w_general = \"\"\"\n", "(Intercept)                   3.295e+00\n", "is_post_treatment             7.386e-02\n", "is_treated                    2.050e-01\n", "log_project_commits           2.310e-01\n", "log_project_contributors      3.683e-01\n", "log_project_age              -5.140e-02\n", "is_post_treatment:is_treated -2.035e-01\n", "\"\"\"\n", "\n", "stats_stars_data_6w_general = \"\"\"\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 33, "id": "e7c35157", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}     \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Main Treatment Effects} & & & & & &    \\\\\n", "    Is post-treatment & 9.609e-02*** & 8.199e-02*** & 8.771e-02*** & 8.382e-02*** & 7.386e-02*** & 7.680e-02*** & 8.345e-02*** & 8.771e-02***  \\\\\n", "    Is treated group & 1.794e-01*** & 1.753e-01*** & .153***& 1.826e-01*** & 1.684e-01*** & 1.629e-01*** & 1.582e-01*** & .153***     \\\\\n", "    Is treated group : Is post-treatment & -1.369e-01*** & -1.717e-01*** & -1.746e-01*** & -1.713e-01*** & -2.035e-01*** & -1.895e-01*** & -1.805e-01*** & -1.746e-01***  \\\\\n", "    \\midrule\n", "    \\textbf{Controls (log)} & & & & & &    \\\\\n", "    project commits & 1.67E-01 & 2.331e-01*** & 2.276e-01*** & 2.22E-01 & 2.310e-01*** & 2.273e-01*** & 2.284e-01*** & 2.276e-01***  \\\\\n", "    project contributors & 3.131e-01*** & 3.505e-01*** & 3.480e-01*** & 3.514e-01*** & 3.683e-01*** & 3.619e-01*** & 3.545e-01*** & 3.480e-01***  \\\\\n", "    project age & 6.925e-02*** & -5.369e-02*** & -5.042e-02*** & -4.306e-02*** & -5.140e-02*** & -5.166e-02*** & -5.178e-02*** & -5.042e-02***  \\\\\n"]}], "source": ["target_columns = ['365 days', '6 weeks', '8 weeks', '10 weeks', '12 weeks']\n", "\n", "for target_column in target_columns:\n", "    if target_column == '365 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365_general, stats_stars_data_365_general)\n", "        updated_latex_code = update_latex_table(original_latex_code_general, parsed_data, target_column)\n", "    elif target_column == '6 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_6w_general, stats_stars_data_6w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '8 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_8w_general, stats_stars_data_8w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '10 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_10w_general, stats_stars_data_10w_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '12 weeks':   \n", "        parsed_data = parse_full_stats(stats_estimates_data_365_general, stats_stars_data_365_general)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    else:\n", "        raise ValueError(f\"Invalid target column: {target_column}\")\n", "\n", "print(updated_latex_code)"]}, {"cell_type": "markdown", "id": "645388fd", "metadata": {}, "source": ["## PR TIME TO MERGE ESTIMATES AND STARS"]}, {"cell_type": "code", "execution_count": 5, "id": "4830aeef", "metadata": {}, "outputs": [], "source": ["original_latex = r\"\"\"\n", "\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}} \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}\\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Core Contributor Characteristics} & & & & & & \\\\\n", "    Tenure (log) & 4.315e-02*** & 4.818e-02*** & .025*** & 4.701e-02*** & 3.505e-02*** & 3.225e-02*** & 3.432e-02*** & .025***\\\\\n", "    Commit Percentage (log) & -1.363e-02 & -1.107e-02 & -.020*** & -7.627e-03 & -3.919e-02** & -3.329e-02** & -1.886e-02 & -.020***\\\\\n", "    \\#Commits (log) & -1.939e-02* & -2.475e-02** & -.001 & -1.229e-02 & -1.843e-03 & -2.831e-04 & -6.822e-03 & -.001\\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & & \\\\\n", "    \\#Commits before treatment (log) & 9.498e-03 & 4.798e-03 & .063*** & 1.628e-03 & 7.434e-02*** & 6.775e-02*** & 7.026e-02*** & .063*** \\\\\n", "    \\#Contributors before treatment (log) & -6.734e-02*** & -6.620e-02*** & -.050*** & -6.163e-02*** & -9.660e-02*** & -9.496e-02*** & -8.813e-02*** & -.050***\\\\\n", "    Project Age before treatment (log) & 5.001e-03 & 1.110e-02 & -.037***& 7.483e-03 & -1.569e-02* & -1.060e-02 & -1.357e-02* & -.037***\\\\\n", "    \\#Newcomers after treatment (log) & 7.905e-02*** & 8.161e-02*** & .013*** & 7.960e-02*** & 6.486e-02*** & 6.919e-02*** & 7.136e-02*** & .013***\\\\\n", "    \\midrule\n", "    \\textbf{Main Language} & & & & & & \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {} \\\\\n", "    MainLanguage-C++ & -1.716e-01*** & -1.728e-01*** & -.095*** & -1.648e-01*** & -1.246e-01*** & -1.145e-01*** & -1.050e-01*** & -.095***\\\\\n", "    MainLanguage-C & 6.185e-02*** & 8.297e-02*** & .015 & 8.080e-02*** & -2.113e-03 & -8.484e-03 & 8.142e-03 & .015\\\\\n", "    MainLanguage-C\\# & 8.713e-02*** & 8.482e-02*** & .039 & 7.180e-02*** & 4.412e-02 & 4.762e-02* & 4.519e-02* & .039\\\\\n", "    MainLanguage-Go & 7.794e-02*** & 6.881e-02*** & .046*** & 8.454e-02*** & 6.656e-02*** & 6.160e-02*** & 4.879e-02*** & .046***\\\\\n", "    MainLanguage-Java & 4.478e-03 & 2.870e-03 & .037***  & 2.626e-02* & 7.212e-02*** & 6.074e-02*** & 4.852e-02*** & .037***\\\\\n", "    MainLanguage-PHP & -2.808e-02* & -1.920e-02 & -.006 & -2.378e-02 & -1.114e-02 & -4.050e-03 & -1.234e-02 & -.006\\\\\n", "    MainLanguage-Python & 3.529e-02* & 2.774e-02 & -.036*** & -5.268e-04 & -4.898e-02* & -3.198e-02 & -3.322e-02 & -.036*** \\\\\n", "    MainLanguage-Rust & -6.677e-02*** & -7.044e-02*** & -.024 & -7.474e-02*** & -3.138e-02* & -2.550e-02* & -2.347e-02* & -.024\\\\\n", "    MainLanguage-TypeScript & 1.102e-01*** & 9.048e-02*** & .045 & 1.015e-01*** & 6.367e-02** & 4.651e-02* & 4.845e-02* & .045\\\\\n", "    \\textbf{Controls (log)} & & & & & & \\\\\n", "    project commits & 2.224e-01*** & 2.327e-01*** & -.011*** & 2.217e-01*** & -6.140e-04 & -5.333e-03 & -8.172e-03 & -.011***\\\\\n", "    project contributors & 3.499e-01*** & 3.509e-01*** & .478***& 3.520e-01*** & 4.662e-01*** & 4.703e-01*** & 4.757e-01*** & .478***\\\\\n", "    project age & -6.560e-02*** & -5.447e-02*** & .042***& -4.397e-02*** & 2.367e-02*** & 2.793e-02*** & 3.077e-02*** & .042***\\\\\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 6, "id": "5cc49b5f", "metadata": {}, "outputs": [], "source": ["# --- 您的输入数据 ---\n", "\n", "# 输入 1: “180 days”列的数据\n", "stats_estimates_data_180 = \"\"\"\n", "(Intercept)                                                             3.262e+00\n", "is_post_treatment                                                       6.345e-02\n", "is_treated                                                              1.142e-01\n", "log_project_commits                                                     2.224e-01\n", "log_project_contributors                                                3.499e-01\n", "log_project_age                                                        -6.560e-02\n", "is_post_treatment:is_treated:log_tenure_c                               4.315e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.363e-02\n", "is_post_treatment:is_treated:log_commits_c                             -1.939e-02\n", "is_post_treatment:is_treated:log_newcomers                              7.905e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.498e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -6.734e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           5.001e-03\n", "is_post_treatment:is_treated:project_main_language1                    -1.716e-01\n", "is_post_treatment:is_treated:project_main_language2                     6.185e-02\n", "is_post_treatment:is_treated:project_main_language3                     8.713e-02\n", "is_post_treatment:is_treated:project_main_language4                     7.794e-02\n", "is_post_treatment:is_treated:project_main_language5                     4.478e-03\n", "is_post_treatment:is_treated:project_main_language6                    -2.808e-02\n", "is_post_treatment:is_treated:project_main_language7                     3.529e-02\n", "is_post_treatment:is_treated:project_main_language8                    -6.677e-02\n", "is_post_treatment:is_treated:project_main_language9                     1.102e-01\n", "\"\"\"\n", "stats_stars_data_180 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                        \n", "is_post_treatment:is_treated:log_commits_c                             *  \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                    *  \n", "is_post_treatment:is_treated:project_main_language7                    *  \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_270 = \"\"\"\n", "(Intercept)                                                             3.294e+00\n", "is_post_treatment                                                       5.650e-02\n", "is_treated                                                              1.109e-01\n", "log_project_commits                                                     2.327e-01\n", "log_project_contributors                                                3.509e-01\n", "log_project_age                                                        -5.447e-02\n", "is_post_treatment:is_treated:log_tenure_c                               4.818e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.107e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.475e-02\n", "is_post_treatment:is_treated:log_newcomers                              8.161e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       4.798e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -6.620e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.110e-02\n", "is_post_treatment:is_treated:project_main_language1                    -1.728e-01\n", "is_post_treatment:is_treated:project_main_language2                     8.297e-02\n", "is_post_treatment:is_treated:project_main_language3                     8.482e-02\n", "is_post_treatment:is_treated:project_main_language4                     6.881e-02\n", "is_post_treatment:is_treated:project_main_language5                     2.870e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.920e-02\n", "is_post_treatment:is_treated:project_main_language7                     2.774e-02\n", "is_post_treatment:is_treated:project_main_language8                    -7.044e-02\n", "is_post_treatment:is_treated:project_main_language9                     9.048e-02\n", "\"\"\"\n", "stats_stars_data_270 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                         \n", "is_post_treatment:is_treated:log_commits_c                             ** \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          .  \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "stats_estimates_data_450 = \"\"\"\n", "(Intercept)                                                             3.305e+00\n", "is_post_treatment                                                       5.748e-02\n", "is_treated                                                              1.175e-01\n", "log_project_commits                                                     2.217e-01\n", "log_project_contributors                                                3.520e-01\n", "log_project_age                                                        -4.397e-02\n", "is_post_treatment:is_treated:log_tenure_c                               4.701e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -7.627e-03\n", "is_post_treatment:is_treated:log_commits_c                             -1.229e-02\n", "is_post_treatment:is_treated:log_newcomers                              7.960e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.628e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -6.163e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           7.483e-03\n", "is_post_treatment:is_treated:project_main_language1                    -1.648e-01\n", "is_post_treatment:is_treated:project_main_language2                     8.080e-02\n", "is_post_treatment:is_treated:project_main_language3                     7.180e-02\n", "is_post_treatment:is_treated:project_main_language4                     8.454e-02\n", "is_post_treatment:is_treated:project_main_language5                     2.626e-02\n", "is_post_treatment:is_treated:project_main_language6                    -2.378e-02\n", "is_post_treatment:is_treated:project_main_language7                    -5.268e-04\n", "is_post_treatment:is_treated:project_main_language8                    -7.474e-02\n", "is_post_treatment:is_treated:project_main_language9                     1.015e-01\n", "\"\"\"\n", "stats_stars_data_450 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                         \n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    *  \n", "is_post_treatment:is_treated:project_main_language6                    .  \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_365 = \"\"\"\n", "is_post_treatment                                                       6.124e-02\n", "is_treated                                                              1.226e-01\n", "log_project_commits                                                     2.271e-01\n", "log_project_contributors                                                3.485e-01\n", "log_project_age                                                        -5.145e-02\n", "is_post_treatment:is_treated:log_tenure_c                               5.094e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -7.547e-03\n", "is_post_treatment:is_treated:log_commits_c                             -1.782e-02\n", "is_post_treatment:is_treated:log_newcomers                              7.611e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.889e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -5.792e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.002e-02\n", "is_post_treatment:is_treated:project_main_language1                    -1.647e-01\n", "is_post_treatment:is_treated:project_main_language2                     8.153e-02\n", "is_post_treatment:is_treated:project_main_language3                     7.842e-02\n", "is_post_treatment:is_treated:project_main_language4                     7.441e-02\n", "is_post_treatment:is_treated:project_main_language5                     1.836e-02\n", "is_post_treatment:is_treated:project_main_language6                    -3.181e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.331e-02\n", "is_post_treatment:is_treated:project_main_language8                    -7.577e-02\n", "is_post_treatment:is_treated:project_main_language9                     1.024e-01\n", "\"\"\"\n", "\n", "stats_stars_data_365 = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                         \n", "is_post_treatment:is_treated:log_commits_c                             .  \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                       \n", "is_post_treatment:is_treated:project_main_language6                    *  \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "# 输入 2: “6 weeks”列的数据\n", "stats_estimates_data_6w = \"\"\"\n", "(Intercept)                                                             3.309e+00\n", "is_post_treatment                                                       4.261e-02\n", "is_treated                                                              1.316e-01\n", "log_project_commits                                                     2.302e-01\n", "log_project_contributors                                                3.694e-01\n", "log_project_age                                                        -5.201e-02\n", "is_post_treatment:is_treated:log_tenure_c                               5.806e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -5.010e-02\n", "is_post_treatment:is_treated:log_commits_c                             -2.373e-03\n", "is_post_treatment:is_treated:log_newcomers                              6.728e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -1.611e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -7.650e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.905e-02\n", "is_post_treatment:is_treated:project_main_language1                    -1.913e-01\n", "is_post_treatment:is_treated:project_main_language2                     6.099e-02\n", "is_post_treatment:is_treated:project_main_language3                     9.429e-02\n", "is_post_treatment:is_treated:project_main_language4                     1.008e-01\n", "is_post_treatment:is_treated:project_main_language5                     5.598e-02\n", "is_post_treatment:is_treated:project_main_language6                    -4.510e-02\n", "is_post_treatment:is_treated:project_main_language7                    -9.625e-04\n", "is_post_treatment:is_treated:project_main_language8                    -8.117e-02\n", "is_post_treatment:is_treated:project_main_language9                     1.247e-01\n", "\"\"\"\n", "stats_stars_data_6w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          *  \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ** \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                    ** \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_8w = \"\"\"\n", "(Intercept)                                                             3.309e+00\n", "is_post_treatment                                                       4.771e-02\n", "is_treated                                                              1.232e-01\n", "log_project_commits                                                     2.265e-01\n", "log_project_contributors                                                3.630e-01\n", "log_project_age                                                        -5.262e-02\n", "is_post_treatment:is_treated:log_tenure_c                               5.389e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.507e-02\n", "is_post_treatment:is_treated:log_commits_c                             -5.299e-03\n", "is_post_treatment:is_treated:log_newcomers                              7.191e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -7.886e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -7.431e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.083e-02\n", "is_post_treatment:is_treated:project_main_language1                    -1.745e-01\n", "is_post_treatment:is_treated:project_main_language2                     5.247e-02\n", "is_post_treatment:is_treated:project_main_language3                     9.664e-02\n", "is_post_treatment:is_treated:project_main_language4                     8.902e-02\n", "is_post_treatment:is_treated:project_main_language5                     3.695e-02\n", "is_post_treatment:is_treated:project_main_language6                    -3.574e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.742e-02\n", "is_post_treatment:is_treated:project_main_language8                    -7.510e-02\n", "is_post_treatment:is_treated:project_main_language9                     1.066e-01\n", "\"\"\"\n", "\n", "stats_stars_data_8w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ** \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    *  \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ** \n", "is_post_treatment:is_treated:project_main_language6                    *  \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\"\n", "\n", "stats_estimates_data_10w = \"\"\"\n", "(Intercept)                                                             3.299e+00\n", "is_post_treatment                                                       5.593e-02\n", "is_treated                                                              1.275e-01\n", "log_project_commits                                                     2.276e-01\n", "log_project_contributors                                                3.554e-01\n", "log_project_age                                                        -5.277e-02\n", "is_post_treatment:is_treated:log_tenure_c                               5.463e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -1.858e-02\n", "is_post_treatment:is_treated:log_commits_c                             -1.371e-02\n", "is_post_treatment:is_treated:log_newcomers                              7.809e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       3.724e-05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -6.860e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.561e-02\n", "is_post_treatment:is_treated:project_main_language1                    -1.706e-01\n", "is_post_treatment:is_treated:project_main_language2                     6.936e-02\n", "is_post_treatment:is_treated:project_main_language3                     9.106e-02\n", "is_post_treatment:is_treated:project_main_language4                     7.483e-02\n", "is_post_treatment:is_treated:project_main_language5                     2.149e-02\n", "is_post_treatment:is_treated:project_main_language6                    -3.641e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.670e-02\n", "is_post_treatment:is_treated:project_main_language8                    -7.408e-02\n", "is_post_treatment:is_treated:project_main_language9                     1.129e-01\n", "\"\"\"\n", "\n", "stats_stars_data_10w = \"\"\"\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      *  \n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          *  \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    .  \n", "is_post_treatment:is_treated:project_main_language6                    ** \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 7, "id": "6c03759a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\begin{threeparttable}\n", "    \\begin{tabular}{l S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      !{\\qquad}\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]\n", "                      S[table-format=-1.3]} % 'l' for the first column (text), 'S' for data columns\n", "    \\toprule % Top horizontal rule\n", "    & \\multicolumn{4}{c}{\\textbf{Different Disengagement Definition}} & \\multicolumn{4}{c}{\\textbf{Different Week Length Effect}}   \\\\\n", "    \\cmidrule(lr){2-5} \\cmidrule(lr){6-9} % Horizontal rules for column groups\n", "    & {180 days} & {270 days} & {365 days} & {450 days} & {6 weeks} & {8 weeks} & {10 weeks} & {12 weeks}        \\\\\n", "    \\midrule % Mid horizontal rule\n", "    \\textbf{Core Contributor Characteristics} & & & & & &       \\\\\n", "    Tenure (log) & 4.315e-02*** & 4.818e-02*** & 5.094e-02*** & 4.701e-02*** & 5.806e-02*** & 5.389e-02*** & 5.463e-02*** & 5.094e-02***  \\\\\n", "    Commit Percentage (log) & -1.363e-02 & -1.107e-02 & -7.547e-03 & -7.627e-03 & -5.010e-02*** & -3.507e-02*** & -1.858e-02* & -7.547e-03  \\\\\n", "    \\#Commits (log) & -1.939e-02* & -2.475e-02** & -1.782e-02 & -1.229e-02 & -2.373e-03 & -5.299e-03 & -1.371e-02 & -1.782e-02  \\\\\n", "    \\midrule\n", "    \\textbf{Repository Characteristics} & & & & & &       \\\\\n", "    \\#Commits before treatment (log) & 9.498e-03 & 4.798e-03 & 1.889e-03 & 1.628e-03 & -1.611e-02 & -7.886e-03 & 3.724e-05 & 1.889e-03  \\\\\n", "    \\#Contributors before treatment (log) & -6.734e-02*** & -6.620e-02*** & -5.792e-02*** & -6.163e-02*** & -7.650e-02*** & -7.431e-02*** & -6.860e-02*** & -5.792e-02***  \\\\\n", "    Project Age before treatment (log) & 5.001e-03 & 1.110e-02 & 1.002e-02 & 7.483e-03 & 1.905e-02* & 2.083e-02** & 1.561e-02* & 1.002e-02  \\\\\n", "    \\#Newcomers after treatment (log) & 7.905e-02*** & 8.161e-02*** & 7.611e-02*** & 7.960e-02*** & 6.728e-02*** & 7.191e-02*** & 7.809e-02*** & 7.611e-02***  \\\\\n", "    \\midrule\n", "    \\textbf{Main Language} & & & & & &       \\\\\n", "    MainLanguage-JavaScript (Reference) & {} & {} & {} & {} & {} & {}       \\\\\n", "    MainLanguage-C++ & -1.716e-01*** & -1.728e-01*** & -1.647e-01*** & -1.648e-01*** & -1.913e-01*** & -1.745e-01*** & -1.706e-01*** & -1.647e-01***  \\\\\n", "    MainLanguage-C & 6.185e-02*** & 8.297e-02*** & 8.153e-02*** & 8.080e-02*** & 6.099e-02** & 5.247e-02* & 6.936e-02*** & 8.153e-02***  \\\\\n", "    MainLanguage-C\\# & 8.713e-02*** & 8.482e-02*** & 7.842e-02*** & 7.180e-02*** & 9.429e-02*** & 9.664e-02*** & 9.106e-02*** & 7.842e-02***  \\\\\n", "    MainLanguage-Go & 7.794e-02*** & 6.881e-02*** & 7.441e-02*** & 8.454e-02*** & 1.008e-01*** & 8.902e-02*** & 7.483e-02*** & 7.441e-02***  \\\\\n", "    MainLanguage-Java & 4.478e-03 & 2.870e-03 & 1.836e-02 & 2.626e-02* & 5.598e-02*** & 3.695e-02** & 2.149e-02 & 1.836e-02  \\\\\n", "    MainLanguage-PHP & -2.808e-02* & -1.920e-02 & -3.181e-02* & -2.378e-02 & -4.510e-02** & -3.574e-02* & -3.641e-02** & -3.181e-02*  \\\\\n", "    MainLanguage-Python & 3.529e-02* & 2.774e-02 & 1.331e-02 & -5.268e-04 & -9.625e-04 & 1.742e-02 & 1.670e-02 & 1.331e-02  \\\\\n", "    MainLanguage-Rust & -6.677e-02*** & -7.044e-02*** & -7.577e-02*** & -7.474e-02*** & -8.117e-02*** & -7.510e-02*** & -7.408e-02*** & -7.577e-02***  \\\\\n", "    MainLanguage-TypeScript & 1.102e-01*** & 9.048e-02*** & 1.024e-01*** & 1.015e-01*** & 1.247e-01*** & 1.066e-01*** & 1.129e-01*** & 1.024e-01***  \\\\\n", "    \\textbf{Controls (log)} & & & & & &       \\\\\n", "    project commits & 2.224e-01*** & 2.327e-01*** & 2.271e-01*** & 2.217e-01*** & 2.302e-01*** & 2.265e-01*** & 2.276e-01*** & 2.271e-01***  \\\\\n", "    project contributors & 3.499e-01*** & 3.509e-01*** & 3.485e-01*** & 3.520e-01*** & 3.694e-01*** & 3.630e-01*** & 3.554e-01*** & 3.485e-01***  \\\\\n", "    project age & -6.560e-02*** & -5.447e-02*** & -5.145e-02*** & -4.397e-02*** & -5.201e-02*** & -5.262e-02*** & -5.277e-02*** & -5.145e-02***  \\\\\n"]}], "source": ["\n", "# --- 运行脚本 ---\n", "\n", "target_columns = ['180 days', '270 days', '365 days', '450 days', '6 weeks', '8 weeks', '10 weeks', '12 weeks']\n", "\n", "for target_column in target_columns:\n", "    if target_column == '180 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_180, stats_stars_data_180)\n", "        updated_latex_code = update_latex_table(original_latex, parsed_data, target_column)\n", "    elif target_column == '270 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_270, stats_stars_data_270)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '365 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365, stats_stars_data_365)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '450 days':\n", "        parsed_data = parse_full_stats(stats_estimates_data_450, stats_stars_data_450)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '6 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_6w, stats_stars_data_6w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '8 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_8w, stats_stars_data_8w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '10 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_10w, stats_stars_data_10w)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    elif target_column == '12 weeks':\n", "        parsed_data = parse_full_stats(stats_estimates_data_365, stats_stars_data_365)\n", "        updated_latex_code = update_latex_table(updated_latex_code, parsed_data, target_column)\n", "    else:\n", "        raise ValueError(f\"Invalid target column: {target_column}\")\n", "\n", "# --- 打印最终结果 ---\n", "# print(f\"--- 最终更新 '{target_column_1}' 和 '{target_column_2}' 后的 LaTeX 表格 ---\")\n", "print(updated_latex_code)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}