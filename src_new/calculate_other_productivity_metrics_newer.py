# from math import log
# import pandas as pd
# from pymongo import MongoClient
# from datetime import datetime
# import numpy as np
# import logging
# import concurrent.futures
# import gc
# from pymongo import IndexModel, ASCENDING
# import os
# import json
# import time

# logging.basicConfig(level=logging.INFO, 
#                     format='%(asctime)s - %(levelname)s - %(message)s')

# class PullRequestMetrics:
#     def __init__(self, repo_name, time_unit, db_name='disengagement', 
#                  collection_pr='pull_requests', collection_comments='pr_comments',
#                  batch_size=1000):
#         self.repo_name = repo_name if isinstance(repo_name, list) else [repo_name]
#         self.time_unit = time_unit
#         self.batch_size = batch_size
#         self.freq_mapping = {
#             'weekly': 'W',
#             'biweekly': '2W',
#             'monthly': 'M'
#         }
#         # Use connection pooling settings
#         self.client = MongoClient('localhost', 27017, 
#                                  maxPoolSize=50, 
#                                  waitQueueTimeoutMS=2000)
#         self.db = self.client[db_name]
#         self.collection_pr = self.db[collection_pr]
#         self.collection_comments = self.db[collection_comments]
#         # Create indexes if they don't exist
#         self._ensure_indexes()
#         # Define standard reference time
#         self.reference_time = pd.Timestamp('2010-08-30 00:00:00')
    
#     def _ensure_indexes(self):
#         """Create necessary indexes to speed up queries"""
#         try:
#             # Create indexes for pull requests collection
#             pr_indexes = [
#                 IndexModel([("repo_name", ASCENDING)]),
#                 IndexModel([("repo_name", ASCENDING), ("merged_at", ASCENDING)]),
#                 IndexModel([("repo_name", ASCENDING), ("created_at", ASCENDING)])
#             ]
#             self.collection_pr.create_indexes(pr_indexes)
            
#             # Create indexes for comments collection
#             comment_indexes = [
#                 IndexModel([("repo_name", ASCENDING)]),
#                 IndexModel([("repo_name", ASCENDING), ("pr_number", ASCENDING)])
#             ]
#             self.collection_comments.create_indexes(comment_indexes)
#             logging.info("MongoDB indexes created or already exist")
#         except Exception as e:
#             logging.warning(f"Failed to create indexes: {e}")

#     def fetch_pull_requests(self):
#         """Fetch pull requests in batches to reduce memory usage"""
#         query = {'repo_name': {'$in': self.repo_name}} if self.repo_name else {}
#         # Define fields we actually need to reduce memory usage
#         projection = {
#             'repo_name': 1, 
#             'created_at': 1, 
#             'merged_at': 1, 
#             'closed_at': 1, 
#             'user.login': 1, 
#             'number': 1
#         }
        
#         all_pr_data = []
#         # Get total count for logging
#         total = self.collection_pr.count_documents(query)
#         processed = 0
        
#         # Use cursor with batch processing
#         cursor = self.collection_pr.find(query, projection).batch_size(self.batch_size)
        
#         for doc in cursor:
#             all_pr_data.append(doc)
#             processed += 1
#             if processed % 10000 == 0:
#                 logging.info(f"Processed {processed}/{total} pull requests")
        
#         return all_pr_data

#     def process_pull_requests(self, pr_data):
#         """Process pull request data more efficiently"""
#         if not pr_data:
#             return pd.DataFrame(columns=['repo_name', 'created_at', 'merged_at', 
#                                          'closed_at', 'pr_status', 'user_login', 'number'])
        
#         # Create DataFrame from list of dictionaries
#         df_pr = pd.DataFrame(pr_data)
        
#         # Extract user login directly from the nested structure
#         df_pr['user_login'] = df_pr['user'].apply(lambda x: x.get('login') if isinstance(x, dict) else None)
        
#         # Drop the original user column to save memory
#         df_pr.drop('user', axis=1, inplace=True)
        
#         # Convert date columns efficiently
#         date_cols = ['created_at', 'merged_at', 'closed_at']
#         for col in date_cols:
#             if col in df_pr.columns:
#                 df_pr[col] = pd.to_datetime(df_pr[col], errors='coerce').dt.tz_localize(None)
        
#         # Determine PR status using vectorized operations
#         conditions = [
#             df_pr['merged_at'].notna(),
#             (df_pr['merged_at'].isna()) & (df_pr['closed_at'].notna()),
#         ]
#         choices = ['merged', 'closed_without_merge']
#         df_pr['pr_status'] = np.select(conditions, choices, default='open')
        
#         # Convert number to int
#         if 'number' in df_pr.columns:
#             df_pr['number'] = df_pr['number'].astype(int)
        
#         # Log the stats
#         merged_count = (df_pr['pr_status'] == 'merged').sum()
#         closed_count = (df_pr['pr_status'] == 'closed_without_merge').sum()
#         open_count = (df_pr['pr_status'] == 'open').sum()
        
#         logging.info(f"Repo: {self.repo_name} - PRs: {len(df_pr)} "
#                      f"(Merged: {merged_count}, Closed: {closed_count}, Open: {open_count})")
        
#         # Only keep columns we need
#         return df_pr[['repo_name', 'created_at', 'merged_at', 
#                     'closed_at', 'pr_status', 'user_login', 'number']]

#     def calculate_pr_throughput(self, df_pr):
#         """Calculate PR throughput with optimized groupby operations"""
#         # Filter for merged PRs
#         df_merged = df_pr[df_pr['pr_status'] == 'merged'].copy()
        
#         if df_merged.empty:
#             return pd.DataFrame(columns=['repo_name', 'datetime', 'pr_throughput'])
        
#         # Get frequency string
#         freq = self.freq_mapping.get(self.time_unit, 'W')
        
#         # Convert to period efficiently
#         df_merged['datetime'] = df_merged['merged_at'].dt.to_period(freq)
        
#         # Group and count
#         throughput = df_merged.groupby(['repo_name', 'datetime']).size().reset_index(name='pr_throughput')
#         return throughput
    
#     def calculate_pull_request_success_rate(self, df_pr):
#         """Calculate PR success rate more efficiently"""
#         if df_pr.empty:
#             return pd.DataFrame(columns=['repo_name', 'datetime', 'pull_request_success_rate'])
        
#         freq = self.freq_mapping.get(self.time_unit, 'W')
        
#         # Create a copy to avoid SettingWithCopyWarning
#         df_success = df_pr.copy()
        
#         # Convert to period
#         df_success['datetime'] = df_success['created_at'].dt.to_period(freq)
        
#         # Optimized approach using groupby with custom aggregation
#         grouped = df_success.groupby(['repo_name', 'datetime'])
        
#         agg_result = grouped.agg(
#             merged_prs=('pr_status', lambda x: (x == 'merged').sum()),
#             total_prs=('pr_status', 'count')
#         ).reset_index()
        
#         # Calculate success rate
#         agg_result['pull_request_success_rate'] = agg_result['merged_prs'] / agg_result['total_prs']
        
#         return agg_result[['repo_name', 'datetime', 'pull_request_success_rate']]

#     def calculate_time_to_merge(self, df_pr):
#         """Calculate time to merge more efficiently"""
#         # Filter merged PRs
#         df_merged = df_pr[df_pr['pr_status'] == 'merged'].copy()
        
#         if df_merged.empty:
#             return pd.DataFrame(columns=['repo_name', 'datetime', 'time_to_merge'])
        
#         # Vectorized calculation of time to merge
#         df_merged['time_to_merge'] = (df_merged['merged_at'] - df_merged['created_at']).dt.total_seconds() / 3600
        
#         # Get frequency string and convert to period
#         freq = self.freq_mapping.get(self.time_unit, 'W')
#         df_merged['datetime'] = df_merged['merged_at'].dt.to_period(freq)
        
#         # Calculate average time to merge
#         time_to_merge = df_merged.groupby(['repo_name', 'datetime'])['time_to_merge'].mean().reset_index()
        
#         return time_to_merge

#     def generate_timeseries_dataframe(self):
#         """Generate timeseries dataframe with optimized memory usage"""
#         # Fetch and process PR data
#         pr_data = self.fetch_pull_requests()
#         df_pr = self.process_pull_requests(pr_data)
        
#         # Free up memory
#         del pr_data
#         gc.collect()
        
#         # Calculate metrics
#         throughput = self.calculate_pr_throughput(df_pr)
#         success_rate = self.calculate_pull_request_success_rate(df_pr)
#         time_to_merge = self.calculate_time_to_merge(df_pr)
        
#         # Free up memory
#         del df_pr
#         gc.collect()
        
#         # Combine metrics - use efficient pd.merge with explicit parameters
#         combined_df = pd.merge(
#             throughput, success_rate, 
#             on=['repo_name', 'datetime'], 
#             how='outer'
#         )
        
#         combined_df = pd.merge(
#             combined_df, time_to_merge, 
#             on=['repo_name', 'datetime'], 
#             how='outer'
#         )
        
#         # Free up more memory
#         del throughput, success_rate, time_to_merge
#         gc.collect()
        
#         # Convert period to datetime
#         combined_df['datetime'] = combined_df['datetime'].dt.start_time
        
#         # Fill NaN with 0 for throughput (more memory efficient than fillna)
#         combined_df['pr_throughput'] = combined_df['pr_throughput'].fillna(0)
        
#         # Add standardized time weeks - use vectorized operation
#         combined_df['standardized_time_weeks'] = ((combined_df['datetime'] - self.reference_time).dt.days / 7).astype(int)
        
#         # Sort for efficient processing later
#         combined_df.sort_values(by=['repo_name', 'datetime'], inplace=True)
        
#         return combined_df
    
#     @staticmethod
#     def process_single_repo(repo_name, time_unit):
#         """Process a single repository with better error handling"""
#         try:
#             pr_metrics = PullRequestMetrics(repo_name, time_unit)
#             df = pr_metrics.generate_timeseries_dataframe()
            
#             # Close MongoDB connection to prevent connection leaks
#             pr_metrics.client.close()
            
#             if df is not None and not df.empty:
#                 logging.info(f"Successfully processed {repo_name}: {len(df)} records")
#                 return df
#             else:
#                 logging.warning(f"No data found for {repo_name}")
#                 return None
                
#         except Exception as e:
#             logging.error(f"Failed to process {repo_name}: {str(e)}", exc_info=True)
#             return None
    
#     @staticmethod
#     def load_cache(cache_file):
#         """Load cache of processed repositories"""
#         if os.path.exists(cache_file):
#             try:
#                 with open(cache_file, 'r') as f:
#                     cache_data = json.load(f)
#                 logging.info(f"Cache loaded from {cache_file}: {len(cache_data['processed_repos'])} repositories already processed")
#                 return cache_data
#             except Exception as e:
#                 logging.warning(f"Failed to load cache from {cache_file}: {e}")
#                 return {'processed_repos': [], 'last_updated': None}
#         else:
#             return {'processed_repos': [], 'last_updated': None}
    
#     @staticmethod
#     def update_cache(cache_file, processed_repo, cache_data=None):
#         """Update cache with newly processed repository"""
#         if cache_data is None:
#             cache_data = PullRequestMetrics.load_cache(cache_file)
        
#         # Add the repo to processed list if not already there
#         if processed_repo not in cache_data['processed_repos']:
#             cache_data['processed_repos'].append(processed_repo)
        
#         cache_data['last_updated'] = datetime.now().isoformat()
        
#         try:
#             # Create directory if it doesn't exist
#             os.makedirs(os.path.dirname(os.path.abspath(cache_file)), exist_ok=True)
            
#             with open(cache_file, 'w') as f:
#                 json.dump(cache_data, f)
#         except Exception as e:
#             logging.error(f"Failed to update cache at {cache_file}: {e}")
    
#     @staticmethod
#     def export_repo_productivity_to_csv(repo_list, time_unit, output_file, max_workers=None, 
#                                         cache_file='../cache/productivity_metrics_cache.json', 
#                                         resume=True):
#         """Export repository productivity metrics with progress caching"""
#         # Determine optimal worker count based on CPUs if not specified
#         if max_workers is None:
#             import multiprocessing
#             max_workers = min(multiprocessing.cpu_count() * 2, 32)
        
#         total_repos = len(repo_list)
#         logging.info(f"Processing {total_repos} repositories with {max_workers} workers")
        
#         # Load cache if resuming
#         cache_data = None
#         if resume:
#             cache_data = PullRequestMetrics.load_cache(cache_file)
#             processed_repos = set(cache_data['processed_repos'])
#             remaining_repos = [repo for repo in repo_list if repo not in processed_repos]
            
#             if processed_repos:
#                 logging.info(f"Resuming from cache: {len(processed_repos)} already processed, {len(remaining_repos)} remaining")
#             else:
#                 logging.info("No cache found or empty cache, processing all repositories")
#                 remaining_repos = repo_list
#         else:
#             # Not resuming, process all repos
#             remaining_repos = repo_list
#             cache_data = {'processed_repos': [], 'last_updated': None}
            
#             # Create a new cache file
#             if os.path.exists(cache_file):
#                 logging.info(f"Not resuming: Creating new cache file at {cache_file}")
            
#         # Process in batches to control memory usage
#         batch_size = 100
#         all_data = []
#         total_remaining = len(remaining_repos)
#         progress_counter = {'completed': 0}
#         start_time = time.time()
        
#         # Function to track progress
#         def update_progress(completed_repo):
#             progress_counter['completed'] += 1
#             current = progress_counter['completed']
#             percentage = (current / total_remaining) * 100
#             elapsed = time.time() - start_time
#             repos_per_min = current / (elapsed / 60) if elapsed > 0 else 0
#             eta_mins = (total_remaining - current) / repos_per_min if repos_per_min > 0 else 0
            
#             logging.info(f"Progress: {current}/{total_remaining} ({percentage:.1f}%) completed | "
#                          f"Speed: {repos_per_min:.1f} repos/min | ETA: {eta_mins:.1f} mins")
            
#             # Update cache with completed repo
#             PullRequestMetrics.update_cache(cache_file, completed_repo, cache_data)
        
#         for i in range(0, total_remaining, batch_size):
#             batch_repos = remaining_repos[i:i+batch_size]
#             batch_results = []
            
#             logging.info(f"Processing batch {i//batch_size + 1}/{(total_remaining+batch_size-1)//batch_size}: "
#                          f"{len(batch_repos)} repositories")
            
#             # Use thread pool for parallel processing
#             with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
#                 future_to_repo = {
#                     executor.submit(PullRequestMetrics.process_single_repo, repo, time_unit): repo 
#                     for repo in batch_repos
#                 }
                
#                 # Process completed futures
#                 for future in concurrent.futures.as_completed(future_to_repo):
#                     repo = future_to_repo[future]
#                     try:
#                         df = future.result()
#                         if df is not None and not df.empty:
#                             batch_results.append(df)
#                         # Update progress regardless of success
#                         update_progress(repo)
#                     except Exception as e:
#                         logging.error(f"Exception for {repo}: {str(e)}")
#                         # Still mark as processed to avoid retrying failed repos
#                         update_progress(repo)
            
#             # Combine batch results and free memory
#             if batch_results:
#                 batch_df = pd.concat(batch_results, ignore_index=True)
#                 all_data.append(batch_df)
#                 del batch_results
#                 gc.collect()
#                 logging.info(f"Batch {i//batch_size + 1} completed: {len(batch_df)} records")
        
#         # Check if we need to load previously processed data
#         if resume and cache_data and cache_data['processed_repos']:
#             prev_output_file = output_file.replace('.csv', '_prev.csv')
#             if os.path.exists(output_file):
#                 # Previous results exist, load them
#                 logging.info(f"Loading previously processed data from {output_file}")
#                 try:
#                     prev_data = pd.read_csv(output_file)
#                     all_data.append(prev_data)
#                     logging.info(f"Loaded {len(prev_data)} previous records")
#                 except Exception as e:
#                     logging.error(f"Failed to load previous results: {e}")
        
#         # Combine all batches and save to CSV
#         if all_data:
#             final_df = pd.concat(all_data, ignore_index=True)
            
#             # Deduplicate data in case of overlaps
#             final_df = final_df.drop_duplicates()
            
#             # Optimize memory before saving
#             for col in final_df.columns:
#                 if final_df[col].dtype == 'float64' and col != 'standardized_time_weeks':
#                     final_df[col] = final_df[col].astype('float32')
            
#             # Ensure standardized_time_weeks is integer
#             final_df['standardized_time_weeks'] = final_df['standardized_time_weeks'].astype(int)
            
#             # Save to CSV with efficient settings
#             final_df.to_csv(output_file, index=False, float_format='%.4f')
#             logging.info(f"Productivity metrics exported to {output_file}: {len(final_df)} total records")
            
#             return len(final_df)
#         else:
#             logging.warning("No data to export")
#             return 0

# def main():
#     import time
#     start_time = time.time()
    
#     project_names = pd.read_csv('../result/repo_name_list.csv')['repo_name'].tolist()
#     time_unit = 'weekly'
#     output_file = '../result/productivity_metrics_20250312.csv'
    
#     # Cache file for tracking progress
#     cache_file = '../cache/productivity_metrics_cache.json'
    
#     # Detect optimal number of workers based on system
#     import multiprocessing
#     recommended_workers = min(multiprocessing.cpu_count() * 2, 4)
    
#     logging.info(f"Starting processing with {recommended_workers} workers")
    
#     # Set resume=True to continue from previous run
#     total_records = PullRequestMetrics.export_repo_productivity_to_csv(
#         project_names, time_unit, output_file, 
#         max_workers=recommended_workers,
#         cache_file=cache_file,
#         resume=True
#     )
    
#     elapsed_time = time.time() - start_time
#     logging.info(f"Processing completed in {elapsed_time:.2f} seconds")
#     logging.info(f"Exported {total_records} records to {output_file}")

# if __name__ == '__main__':
#     # Suppress warnings
#     import warnings
#     warnings.filterwarnings("ignore")
#     main()