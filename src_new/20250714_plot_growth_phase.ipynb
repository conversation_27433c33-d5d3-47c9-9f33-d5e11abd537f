{"cells": [{"cell_type": "code", "execution_count": 2, "id": "69b07ed5", "metadata": {}, "outputs": [], "source": ["# ==== 依赖导入 ====\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "from scipy.optimize import curve_fit"]}, {"cell_type": "code", "execution_count": 97, "id": "f15625c2", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "best_model_name", "rawType": "object", "type": "unknown"}, {"name": "best_model_formula", "rawType": "object", "type": "unknown"}], "ref": "0aae80bc-7944-425c-aab8-9b0000c84e60", "rows": [["0", "openorienteering/mapper", "logistic", "6762.2789 / (1 + exp(-0.0382*(t-66.1253)))"], ["1", "superedge/superedge", "<PERSON><PERSON><PERSON>", "387.2986 * exp(-2.8930 * exp(-0.1310 * t))"], ["2", "nih-at/libzip", "<PERSON><PERSON><PERSON>", "6961.5432 * exp(-4.6308 * exp(-0.0061 * t))"], ["3", "quiteafancyemerald/holy-unblocker-old", "linear", "10.6510*t + 729.1103"], ["4", "fastnlp/fastnlp", "<PERSON><PERSON><PERSON>", "1600.4810 * exp(-3.5586 * exp(-0.1044 * t))"], ["5", "jupyterlab/jupyterlab", "<PERSON><PERSON><PERSON>", "21427.4161 * exp(-3.5285 * exp(-0.0456 * t))"], ["6", "core-ds/core-components", "linear", "50.2891*t + 45.4578"], ["7", "verdaccio/verdaccio", "<PERSON><PERSON><PERSON>", "4783.5467 * exp(-4.6004 * exp(-0.0250 * t))"], ["8", "teknogods/openparrot", "linear", "8.5644*t + 96.2872"], ["9", "wechatpy/wechatpy", "linear", "7.5951*t + 441.5138"], ["10", "casbin/casbin", "linear", "6.7151*t + 173.1811"], ["11", "pqrs-org/KE-complex_modifications", "<PERSON><PERSON><PERSON>", "4966.6238 * exp(-2.7977 * exp(-0.0164 * t))"], ["12", "adguardteam/adguardforios", "logistic", "5035.2877 / (1 + exp(-0.0870*(t-57.7638)))"], ["13", "jupyterhub/jupyterhub", "linear", "41.9287*t + -100.2347"], ["14", "jupyter/nbgrader", "<PERSON><PERSON><PERSON>", "2689.0326 * exp(-1.8966 * exp(-0.0352 * t))"], ["15", "mongoengine/mongoengine", "<PERSON><PERSON><PERSON>", "3206.6897 * exp(-2.6680 * exp(-0.0229 * t))"], ["16", "k<PERSON><PERSON><PERSON>-mobile/painter", "<PERSON><PERSON><PERSON>", "364.8168 * exp(-2.2853 * exp(-0.0975 * t))"], ["17", "helix-editor/helix", "<PERSON><PERSON><PERSON>", "5747.2346 * exp(-4.8630 * exp(-0.0771 * t))"], ["18", "janus-idp/backstage-showcase", "<PERSON><PERSON><PERSON>", "2716.7124 * exp(-3.0252 * exp(-0.0478 * t))"], ["19", "opennos/opennos", "<PERSON><PERSON><PERSON>", "5106.9928 * exp(-4.1554 * exp(-0.1656 * t))"], ["20", "elektrainitiative/libelektra", "<PERSON><PERSON><PERSON>", "32580.8070 * exp(-26.2823 * exp(-0.0195 * t))"], ["21", "kubean-io/kubean", "linear", "34.5700*t + 106.5355"], ["22", "sr-sunny-raj/hacktoberfest2021-dsa", "<PERSON><PERSON><PERSON>", "1555.2511 * exp(-412160169272650357632120065785857359098377404416.0000 * exp(-6.7339 * t))"], ["23", "olsf/libra", "<PERSON><PERSON><PERSON>", "7239.5782 * exp(-3.7609 * exp(-0.1932 * t))"], ["24", "seeed-studio/wiki-documents", "linear", "137.2762*t + 21.1624"], ["25", "librenms/librenms", "logistic", "13022.0884 / (1 + exp(-0.0334*(t-114.4974)))"], ["26", "pixiv/three-vrm", "linear", "23.2256*t + 131.7526"], ["27", "paddlepaddle/models", "<PERSON><PERSON><PERSON>", "2858.2154 * exp(-2.3284 * exp(-0.0651 * t))"], ["28", "openadaptai/openadapt", "linear", "26.6013*t + 303.3680"], ["29", "cataclysmbnteam/cataclysm-bn", "<PERSON><PERSON><PERSON>", "70054.3602 * exp(-6.5017 * exp(-0.0314 * t))"], ["30", "nancyfx/nancy", "<PERSON><PERSON><PERSON>", "4373.6028 * exp(-6.5623 * exp(-0.0469 * t))"], ["31", "azure/azure-sdk-tools", "<PERSON><PERSON><PERSON>", "6328.7526 * exp(-3.6565 * exp(-0.0305 * t))"], ["32", "reakit/reakit", "linear", "30.2636*t + 90.4721"], ["33", "xi-editor/xi-editor", "<PERSON><PERSON><PERSON>", "1802.1918 * exp(-7.0176 * exp(-0.0985 * t))"], ["34", "trustwallet/tokens", "<PERSON><PERSON><PERSON>", "5350.6312 * exp(-3.4906 * exp(-0.0856 * t))"], ["35", "microsoft/typescript-website-localizations", "<PERSON><PERSON><PERSON>", "443.0088 * exp(-1.8875 * exp(-0.0832 * t))"], ["36", "jellyfin/jellyfin", "linear", "132.8374*t + 80.1322"], ["37", "vuejs/vue-jest", "linear", "4.4216*t + 121.6804"], ["38", "teammates/teammates", "<PERSON><PERSON><PERSON>", "13642.0028 * exp(-9.8833 * exp(-0.0650 * t))"], ["39", "graphiteeditor/graphite", "<PERSON><PERSON><PERSON>", "2338.6933 * exp(-4.5183 * exp(-0.0459 * t))"], ["40", "codemirror/codemirror", "<PERSON><PERSON><PERSON>", "7128.5700 * exp(-2.9600 * exp(-0.0357 * t))"], ["41", "labring/laf", "<PERSON><PERSON><PERSON>", "2801.4738 * exp(-3.2201 * exp(-0.0783 * t))"], ["42", "TheOdinProject/curriculum", "<PERSON><PERSON><PERSON>", "42696.7538 * exp(-5.6792 * exp(-0.0259 * t))"], ["43", "cloudnetservice/cloudnet-v3", "logistic", "2521.5589 / (1 + exp(-0.1094*(t-25.5448)))"], ["44", "intelowlproject/intelowl", "<PERSON><PERSON><PERSON>", "9744.8399 * exp(-4.6396 * exp(-0.0182 * t))"], ["45", "sky-map-team/stardroid", "<PERSON><PERSON><PERSON>", "503.1238 * exp(-8.0038 * exp(-0.0359 * t))"], ["46", "google/guava", "<PERSON><PERSON><PERSON>", "6481.6668 * exp(-4.1918 * exp(-0.0233 * t))"], ["47", "sympy/sympy", "<PERSON><PERSON><PERSON>", "64991.0857 * exp(-11.3399 * exp(-0.0116 * t))"], ["48", "lxc/lxc", "logistic", "10347.0299 / (1 + exp(-0.0326*(t-108.0282)))"], ["49", "saghul/pythonz", "<PERSON><PERSON><PERSON>", "419.1731 * exp(-1.9139 * exp(-0.0403 * t))"]], "shape": {"columns": 3, "rows": 34930}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>best_model_name</th>\n", "      <th>best_model_formula</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>openorienteering/mapper</td>\n", "      <td>logistic</td>\n", "      <td>6762.2789 / (1 + exp(-0.0382*(t-66.1253)))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>superedge/superedge</td>\n", "      <td>gompertz</td>\n", "      <td>387.2986 * exp(-2.8930 * exp(-0.1310 * t))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>nih-at/libzip</td>\n", "      <td>gompertz</td>\n", "      <td>6961.5432 * exp(-4.6308 * exp(-0.0061 * t))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>quiteafancyemerald/holy-unblocker-old</td>\n", "      <td>linear</td>\n", "      <td>10.6510*t + 729.1103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>fastnlp/fastnlp</td>\n", "      <td>gompertz</td>\n", "      <td>1600.4810 * exp(-3.5586 * exp(-0.1044 * t))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34925</th>\n", "      <td>zzrough/gs-extensions-drop-down-terminal</td>\n", "      <td>gompertz</td>\n", "      <td>176.1608 * exp(-1.2249 * exp(-0.0531 * t))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34926</th>\n", "      <td>zycgit/hasor</td>\n", "      <td>gompertz</td>\n", "      <td>4770.9526 * exp(-4.8872 * exp(-0.0203 * t))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34927</th>\n", "      <td>zyachel/libremdb</td>\n", "      <td>gompertz</td>\n", "      <td>157.3623 * exp(-3.1271 * exp(-0.1306 * t))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34928</th>\n", "      <td>zxh0/vscode-proto3</td>\n", "      <td>linear</td>\n", "      <td>2.0390*t + 101.9695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34929</th>\n", "      <td>zspecza/common-tags</td>\n", "      <td>gompertz</td>\n", "      <td>253.1160 * exp(-2.7599 * exp(-0.0700 * t))</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>34930 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                      repo_name best_model_name  \\\n", "0                       openorienteering/mapper        logistic   \n", "1                           superedge/superedge        gompertz   \n", "2                                 nih-at/libzip        gompertz   \n", "3         quiteafancyemerald/holy-unblocker-old          linear   \n", "4                               fastnlp/fastnlp        gompertz   \n", "...                                         ...             ...   \n", "34925  zzrough/gs-extensions-drop-down-terminal        gompertz   \n", "34926                              zycgit/hasor        gompertz   \n", "34927                          zyachel/libremdb        gompertz   \n", "34928                        zxh0/vscode-proto3          linear   \n", "34929                       zspecza/common-tags        gompertz   \n", "\n", "                                best_model_formula  \n", "0       6762.2789 / (1 + exp(-0.0382*(t-66.1253)))  \n", "1       387.2986 * exp(-2.8930 * exp(-0.1310 * t))  \n", "2      6961.5432 * exp(-4.6308 * exp(-0.0061 * t))  \n", "3                             10.6510*t + 729.1103  \n", "4      1600.4810 * exp(-3.5586 * exp(-0.1044 * t))  \n", "...                                            ...  \n", "34925   176.1608 * exp(-1.2249 * exp(-0.0531 * t))  \n", "34926  4770.9526 * exp(-4.8872 * exp(-0.0203 * t))  \n", "34927   157.3623 * exp(-3.1271 * exp(-0.1306 * t))  \n", "34928                          2.0390*t + 101.9695  \n", "34929   253.1160 * exp(-2.7599 * exp(-0.0700 * t))  \n", "\n", "[34930 rows x 3 columns]"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["growth_phase_summary = pd.read_csv('../result/repo_growth_model_summary.csv')\n", "growth_phase_summary"]}, {"cell_type": "code", "execution_count": 99, "id": "3e6ef512", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["best_model_name\n", "<PERSON><PERSON>tz       22520\n", "linear         10623\n", "logistic        1639\n", "exponential      144\n", "Name: count, dtype: int64\n"]}], "source": ["print(growth_phase_summary['best_model_name'].value_counts())"]}, {"cell_type": "code", "execution_count": 120, "id": "df6b0820", "metadata": {}, "outputs": [], "source": ["# ==== 拟合模型定义 ====\n", "from tkinter import font\n", "\n", "\n", "def logistic_model(t, K, r, t0):\n", "    return K / (1 + np.exp(-r * (t - t0)))\n", "\n", "def gompertz_model(t, a, b, c):\n", "    return a * np.exp(-b * np.exp(-c * t))\n", "\n", "MODELS = {\n", "    'linear': lambda t, a, b: a*t + b,\n", "    'exponential': lambda t, a, b: a*np.exp(b*t),\n", "    'logistic': logistic_model,\n", "    'gompertz': gompertz_model\n", "}\n", "\n", "# ==== 读取commit文件 ====\n", "def read_commit_file(repo_name, processed=True):\n", "    if processed:\n", "        path = f\"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv\"\n", "    else:\n", "        path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "    if not os.path.exists(path):\n", "        raise FileNotFoundError(f\"Commit file not found: {path}\")\n", "    df = pd.read_csv(path)\n", "    if df.empty:\n", "        raise ValueError(\"The commit file is empty.\")\n", "    return df\n", "\n", "# ==== 处理累积提交 ====\n", "def prepare_cumulative_commits(df):\n", "    df['date'] = pd.to_datetime(df['date']).dt.to_period('M').dt.to_timestamp()\n", "    commits = df.groupby('date').size().resample('ME').sum().reset_index(name='counts')\n", "    commits['cumulative'] = commits['counts'].cumsum()\n", "    start_date = commits['date'].min()\n", "    # 填补缺失月份\n", "    full_dates = pd.date_range(start=start_date, end=commits['date'].max(), freq='ME')\n", "    commits = commits.set_index('date').reindex(full_dates).fillna({'counts':0, 'cumulative':0}).reset_index()\n", "    commits['cumulative'] = commits['counts'].cumsum()\n", "    commits.rename(columns={'index':'date'}, inplace=True)\n", "    commits['months_since_start'] = (commits['date'] - start_date).apply(lambda x: x.days//30 + x.seconds//86400)\n", "    return commits\n", "\n", "# ==== 拟合最佳模型 ====\n", "def fit_best_model(commits):\n", "    x_data = commits['months_since_start'].values\n", "    y_data = commits['cumulative'].values\n", "    best_model = None\n", "    best_score = float('inf')\n", "    for name, func in MODELS.items():\n", "        try:\n", "            params, _ = curve_fit(func, x_data, y_data, maxfev=5000)\n", "            pred = func(x_data, *params)\n", "            mse = np.mean((y_data - pred)**2)\n", "            if mse < best_score:\n", "                best_model = (name, func, params)\n", "                best_score = mse\n", "        except Exception as e:\n", "            pass\n", "    return best_model\n", "\n", "# ==== 绘图函数 ====\n", "def plot_cumulative(commits, repo_name, sample_n=None, seed=None, save_path=None):\n", "    plt.figure(figsize=(8,5))\n", "    if sample_n is not None and sample_n < len(commits):\n", "        np.random.seed(seed)\n", "        sampled = commits.sample(n=sample_n, random_state=seed).sort_values('date')\n", "        plt.plot(sampled['date'], sampled['cumulative'], marker='o', label='Sampled Cumulative Commits')\n", "    else:\n", "        plt.plot(commits['date'], commits['cumulative'], marker='o', label='Cumulative Commits')\n", "    plt.xlabel('Date (Month)')\n", "    plt.ylabel('Cumulative Commits')\n", "    plt.title(f'Cumulative Commits Over Time\\n{repo_name}')\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    if save_path:\n", "        plt.savefig(save_path)\n", "    plt.show()\n", "\n", "def plot_fit(commits, best_model, repo_name, sample_n=None, seed=None, save_path=None):\n", "    plt.figure(figsize=(8,5))\n", "    if sample_n is not None and sample_n < len(commits):\n", "        np.random.seed(seed)\n", "        sampled = commits.sample(n=sample_n, random_state=seed).sort_values('date')\n", "        x_data = sampled['months_since_start'].values\n", "        y_data = sampled['cumulative'].values\n", "        plt.plot(sampled['date'], y_data, 'o', label='Sampled Cumulative Commits')\n", "    else:\n", "        x_data = commits['months_since_start'].values\n", "        y_data = commits['cumulative'].values\n", "        plt.plot(commits['date'], y_data, 'o', label='Cumulative Commits')\n", "    # 拟合曲线\n", "    name, func, params = best_model\n", "    x_fit = np.linspace(x_data.min(), x_data.max(), 200)\n", "    y_fit = func(x_fit, *params)\n", "    # x_fit映射到日期\n", "    date_fit = pd.to_datetime(commits['date'].min()) + pd.to_timedelta(x_fit*30, unit='D')\n", "    plt.plot(date_fit, y_fit, '-', label=f'Best Fit: {name}')\n", "    plt.xlabel('Month',size=30)\n", "    plt.ylabel('# Commits',fontsize=30)\n", "    plt.title(f'Commits Trend & Best Fit\\n{repo_name} of model {name}', fontsize=30)\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    if save_path:\n", "        plt.savefig(save_path)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "29706453", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 自动获取所有 repo_names\n", "newcomers = pd.read_csv('../data/newcomer_contributors_core_developer.csv')\n", "repo_names = newcomers['repo_name'].unique().tolist()\n", "\n", "sample_n = 1           # 采样数量，如 20，若不采样则设为 None\n", "seed = None              # 随机种子，如 42，若不设则为 None\n", "use_raw = False        # 是否用原始commit文件，True/False\n", "save_dir = \"../figures/growth_trend\"  # 图片保存目录，如 \"./plots\"，不保存则设为 None\n", "\n", "# 随机采样 repo\n", "np.random.seed(seed)\n", "if sample_n is not None and sample_n < len(repo_names):\n", "    sampled_repo_names = np.random.choice(repo_names, size=sample_n, replace=False)\n", "else:\n", "    sampled_repo_names = repo_names\n", "\n", "print(\"Sampled repo_names:\", sampled_repo_names)"]}, {"cell_type": "code", "execution_count": 121, "id": "ab2831ed", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3200947/1863658552.py:33: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  df['date'] = pd.to_datetime(df['date']).dt.to_period('M').dt.to_timestamp()\n", "/tmp/ipykernel_3200947/1863658552.py:53: OptimizeWarning: Covariance of the parameters could not be estimated\n", "  params, _ = curve_fit(func, x_data, y_data, maxfev=5000)\n", "/tmp/ipykernel_3200947/1863658552.py:9: RuntimeWarning: overflow encountered in exp\n", "  return a * np.exp(-b * np.exp(-c * t))\n", "/tmp/ipykernel_3200947/1863658552.py:9: RuntimeWarning: overflow encountered in multiply\n", "  return a * np.exp(-b * np.exp(-c * t))\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "import time\n", "\n", "# 修正种子，确保为整数\n", "seed_int = int(time.time())\n", "np.random.seed(seed_int)\n", "if sample_n is not None and sample_n < len(repo_names):\n", "    sampled_repo_names = np.random.choice(repo_names, size=sample_n, replace=False)\n", "else:\n", "    sampled_repo_names = repo_names\n", "repo_name = sampled_repo_names[0]  # 或手动指定 repo_name\n", "repo_name = 'pynapple-org/pynapple'\n", "df = read_commit_file(repo_name, processed=not use_raw)\n", "commits = prepare_cumulative_commits(df)\n", "best_model = fit_best_model(commits)\n", "\n", "if save_dir:\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    save1 = os.path.join(save_dir, f'{repo_name.replace(\"/\", \"_\")}_cumulative.png')\n", "    save2 = os.path.join(save_dir, f'{repo_name.replace(\"/\", \"_\")}_fit.png')\n", "else:\n", "    save1 = save2 = None\n", "\n", "plot_cumulative(commits, repo_name, sample_n=None, seed=seed, save_path=save1)\n", "plot_fit(commits, best_model, repo_name, sample_n=None, seed=seed, save_path=save2)"]}, {"cell_type": "code", "execution_count": null, "id": "b79ce5c1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}