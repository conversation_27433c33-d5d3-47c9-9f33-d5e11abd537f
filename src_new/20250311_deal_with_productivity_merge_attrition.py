import pandas as pd
productivity = pd.read_csv('../result/did_result_20250312/productivity_20250312_with_propensity_scores.csv')
# productivity = pd.read_csv('../result/did_result_20250312/productivity_20250408_with_propensity_scores.csv')
attritions = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')
global_min_time = pd.Timestamp('2010-08-30 00:00:00')
attritions['standardized_time_weeks'] = (
    (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
).astype(int)

productivity_merged = productivity.merge(
    attritions[['repo_name', 'standardized_time_weeks', 'someone_left', 'tenure', 'commit_percent', 'commits', 'burst', 'attrition_count']],
    on=['repo_name', 'standardized_time_weeks'],
    how='left'
)

productivity_merged['someone_left'] = productivity_merged['someone_left'].fillna(0).astype(int)


# save the merged productivity and attrition data
# productivity_merged.to_csv('../result/did_result_20250312/productivity_20250312_with_propensity_scores_with_attritions.csv', index=False)
# create directory if not exists
import os
if not os.path.exists('../result/did_result_20250408'):
    os.makedirs('../result/did_result_20250408')
productivity_merged.to_csv('../result/did_result_20250408/productivity_20250408_with_propensity_scores_with_attritions.csv', index=False)