import pandas as pd
import numpy as np
import os
import logging
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.neighbors import NearestNeighbors
import statsmodels.api as sm
import statsmodels.formula.api as smf
import matplotlib.pyplot as plt
from itertools import product

# Set up logging
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "psm_2025_0408.log")),
        logging.StreamHandler(),
    ],
)

output_dir = "../result/did_result_20250408/"
# output_dir = "../result/did_result_20250312/"
os.makedirs(output_dir, exist_ok=True)
compiled_data_test = pd.read_csv(output_dir + "compiled_data_test_12_time_to_merge.csv")
compiled_data_test['log_pr_throughput'] = np.log(compiled_data_test['pr_throughput'] + 1)
compiled_data_test['log_pull_request_success_rate'] = np.log(compiled_data_test['pull_request_success_rate'] + 1)
compiled_data_test['log_project_commits'] = np.log(compiled_data_test['project_commits'] + 1)
compiled_data_test['log_project_contributors'] = np.log(compiled_data_test['project_contributors'] + 1)
compiled_data_test['log_project_age'] = np.log(compiled_data_test['project_age'] + 1)
# compiled_data_test['log_newcomers'] = np.log(compiled_data_test['newcomers'] + 1)
# Create time-cohort and repo-cohort effects
compiled_data_test['time_cohort_effect'] = compiled_data_test['is_post_treatment'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)
compiled_data_test['repo_cohort_effect'] = compiled_data_test['is_treated'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)


for cohort_id, group in compiled_data_test.groupby('cohort_id'):
    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]
    if treated_group.empty:
        continue
    treated_row = treated_group.iloc[0][['tenure', 'commit_percent', 'commits']]
    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['tenure', 'commit_percent', 'commits']] = treated_row.values    
    
# generate the model with each cohort group of the project characteristics just before the treatment
compiled_data_test['log_project_commits_before_treatment'] = 0
compiled_data_test['log_project_contributors_before_treatment'] = 0
compiled_data_test['log_project_age_before_treatment'] = 0
compiled_data_test['project_main_language'] = ''
for cohort_id, group in compiled_data_test.groupby('cohort_id'):
    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]
    if treated_group.empty:
        continue
    treated_row = treated_group.iloc[0][['log_project_commits', 'log_project_contributors', 'log_project_age', 'mainLanguage']]
    # rename ['log_project_commits', 'log_project_contributors', 'log_project_age'] into [log_project_commits_before_treatment, log_project_contributors_before_treatment, log_project_age_before_treatment]
    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values
compiled_data_test