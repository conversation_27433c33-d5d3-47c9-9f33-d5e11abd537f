import logging
from pymongo import MongoClient
import pandas as pd
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/generate_attrition_365_new.log", mode="w", encoding="utf-8"),
    ],
)

def get_processed_commit_file_repo_name(repo_name):
    """Get processed commit data for a repository"""
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
    return repo_commit

def generate_attrition_365_new():
    """
    Generate Attrition_365_new field for each core developer by replicating 
    the logic from identify_break_disengagement.py
    """
    # Connect to MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    
    # Get all unique repos from the collection
    repos = collection.distinct("repo_name")
    logging.info(f"Found {len(repos)} repositories to process")
    
    attrition_limit = 365  # 365 days
    
    for repo_name in repos:
        logging.info(f"Processing repository: {repo_name}")
        
        try:
            # Get processed commit data for this repo
            repo_commits = get_processed_commit_file_repo_name(repo_name)
            repo_commits['date'] = pd.to_datetime(repo_commits['date'], format='mixed')
            
            # Get all developers for this repo from MongoDB
            developers = collection.find({"repo_name": repo_name})
            
            for dev in developers:
                dev_login = dev.get("core_dev_login")
                if not dev_login:
                    continue
                
                logging.debug(f"Processing developer: {dev_login}")
                
                # Filter commits by the developer
                dev_commits = repo_commits[repo_commits['author_login'] == dev_login]
                if dev_commits.empty:
                    logging.debug(f"No commits found for developer {dev_login}")
                    continue
                
                dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')
                dev_commits = dev_commits.sort_values(by='date')
                
                # Get the last commit date for this developer
                last_commit_date = dev_commits['date'].max()
                project_last_commit_date = repo_commits['date'].max()
                
                # Ensure both dates are timezone-naive
                last_commit_date = last_commit_date.tz_localize(None)
                project_last_commit_date = project_last_commit_date.tz_localize(None)
                
                # Initialize attrition data
                attrition_dates = []
                
                # Check if developer has been inactive for more than attrition_limit days
                if (project_last_commit_date - last_commit_date).days > attrition_limit:
                    attrition_dates.append(str(last_commit_date.date()))
                    logging.info(f"Developer {dev_login} marked as disengaged since {last_commit_date.date()}")
                
                # Check breaks for attrition
                breaks = dev.get("Breaks", [])
                for break_event in breaks:
                    duration_units = break_event.get("duration_units", 0)
                    if duration_units >= attrition_limit:
                        attrition_dates.append(str(break_event["start_date"]))
                        logging.info(f"Developer {dev_login} marked as disengaged due to break starting {break_event['start_date']}")
                
                # Create Attrition_365_new field
                attrition_365_new = None
                if attrition_dates:
                    attrition_365_new = {
                        "attrition_date": attrition_dates
                    }
                
                # Update the document with Attrition_365_new field
                try:
                    collection.update_one(
                        {"_id": dev["_id"]},
                        {"$set": {"Attrition_365_new": attrition_365_new}}
                    )
                    logging.info(f"Updated Attrition_365_new for developer {dev_login}")
                except Exception as e:
                    logging.error(f"Error updating Attrition_365_new for developer {dev_login}: {e}")
                
        except Exception as e:
            logging.error(f"Error processing repository {repo_name}: {e}")
            continue
    
    logging.info("Attrition_365_new generation completed")

def main():
    generate_attrition_365_new()

if __name__ == "__main__":
    main()