import logging
import pandas as pd
import numpy as np
from scipy.optimize import curve_fit
from statsmodels.nonparametric.smoothers_lowess import lowess
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - Line %(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(), 
        logging.FileHandler('../logs/analyze_attritions_growth_stage.log')
    ]
)
import warnings
warnings.filterwarnings('ignore')

# Load attritions data
logging.info("Loading newcomer data from CSV...")
try:
    attritions = pd.read_csv('../result/attritions.csv')
    logging.info(f"Loaded attritions data with shape: {attritions.shape}")
except Exception as e:
    logging.error(f"Failed to load attritions CSV: {e}")
    raise

def analyze_repo_commits(repo_name):
    logging.info(f"Analyzing repo: {repo_name}")
    import pandas as pd
    import numpy as np
    from scipy.optimize import curve_fit
    from statsmodels.nonparametric.smoothers_lowess import lowess

    try:
        commit_file = f'../data/commits/{repo_name.replace("/","_")}_commits.csv'
        logging.info(f"Loading commits data from {commit_file}")
        commits = pd.read_csv(commit_file)
        logging.info(f"Commits loaded: {commits.shape[0]} rows")
    except Exception as e:
        logging.error(f"Failed to load commit file for {repo_name}: {e}")
        raise

    try:
        commits['date'] = pd.to_datetime(commits['date']).dt.to_period('M').dt.to_timestamp()
        logging.info("Converted commit dates to monthly period timestamps.")
        commits = commits.groupby('date').size().resample('ME').sum().reset_index(name='counts')
        logging.info(f"Grouped and resampled to monthly data: {commits.shape[0]} rows")
        commits['cumulative'] = commits['counts'].cumsum()
        start_date = commits['date'].min()
        logging.info(f"Start date for repo {repo_name}: {start_date}")
    except Exception as e:
        logging.error(f"Error processing commit dates for {repo_name}: {e}")
        raise

    # 补全时间序列，处理缺失月份
    full_dates = pd.date_range(start=start_date, end=commits['date'].max(), freq='ME')
    commits = commits.set_index('date').reindex(full_dates).fillna({'counts':0, 'cumulative':0}).reset_index()
    commits['cumulative'] = commits['counts'].cumsum()
    commits.rename(columns={'index':'date'}, inplace=True)
    logging.info(f"Reindexed and filled missing months. Data now has {commits.shape[0]} rows.")

    # 计算相对月份数
    commits['months_since_start'] = (commits['date'] - start_date).apply(
        lambda x: x.days//30 + x.seconds//86400  # 精确月份
    )
    logging.info("Computed months since start for each row.")

    # 模型定义
    def logistic_model(t, K, r, t0):
        return K / (1 + np.exp(-r * (t - t0)))

    def gompertz_model(t, a, b, c):
        return a * np.exp(-b * np.exp(-c * t))

    models = {
        'linear': lambda t, a, b: a*t + b,
        'exponential': lambda t, a, b: a*np.exp(b*t),
        'logistic': logistic_model,
        'gompertz': gompertz_model
    }

    # 模型拟合
    x_data = commits['months_since_start'].values
    y_data = commits['cumulative'].values
    best_model = None
    best_score = float('inf')
    logging.info(f"Fitting growth models for repo {repo_name}...")

    for name, func in models.items():
        try:
            params, _ = curve_fit(func, x_data, y_data, maxfev=5000)
            pred = func(x_data, *params)
            mse = np.mean((y_data - pred)**2)
            logging.info(f"Model {name}: fitted with params {params}, MSE={mse:.3f}")
            if mse < best_score:
                best_model = (name, func, params)
                best_score = mse
        except Exception as e:
            logging.warning(f"{name} model fit failed: {str(e)}")

    if best_model is not None:
        logging.info(f"Best model: {best_model[0]} with score {best_score:.3f}")
    else:
        logging.warning("No model could be fitted successfully.")

    # 分阶段函数
    def classify_growth_phase(months_data, counts_data, model_info=None):
        """统一月份数据分类逻辑"""
        if len(months_data) < 3:
            logging.info("Data length < 3 months - classified as 'first 3 months'")
            return "first 3 months"

        # 平滑处理
        smooth_frac = max(0.1, min(0.4, 6/len(months_data)))
        smoothed = lowess(counts_data, months_data, frac=smooth_frac, it=3)
        logging.debug(f"LOESS smoothing with frac={smooth_frac}")

        # 增长率
        gr = np.diff(smoothed[:, 1]) / np.diff(smoothed[:, 0] + 1e-6)
        acceleration = np.diff(gr) if len(gr)>1 else np.array([0])

        # 时间衰减权重
        weights = np.exp(np.linspace(0, 3, len(gr)))
        w_mean = np.average(gr, weights=weights)
        w_std = np.sqrt(np.average((gr - w_mean)**2, weights=weights))
        current_gr = gr[-1] if len(gr)>0 else 0
        current_accel = acceleration[-1] if len(acceleration)>0 else 0

        msg = f"Current gr={current_gr:.3f}, Accel={current_accel:.3f}, " \
              f"Weighted Mean={w_mean:.3f} Std={w_std:.3f}"
        logging.debug(msg)

        if current_gr > w_mean + 0.8*w_std:
            phase = "accelerating"
        elif current_gr > w_mean - 0.8*w_std:
            if current_accel > 0.15*w_mean:
                phase = "accelerating"
            elif current_accel < -0.15*w_mean:
                phase = "decelerating"
            else:
                phase = "steady"
        else:
            phase = "decelerating" if current_gr > 0 else "saturation"

        # 模型辅助
        if model_info:
            name, func, params = model_info
            try:
                current_val = func(months_data[-1], *params)
                if name == 'logistic' and current_val >= 0.93*params[0]:
                    phase = "saturation"
                elif name == 'gompertz' and current_val >= 0.95*params[0]:
                    phase = "saturation"
                logging.debug(f"Model {name} predicts value {current_val:.3f}, may override phase to {phase}")
            except Exception as e:
                logging.warning(f"Model phase check failed: {e}")

        logging.info(f"Classified phase: {phase}")
        return phase

    # 人员流失时点评估
    newcomer_data = attritions[attritions['repo_name'] == repo_name]
    burst_dev_stage = pd.DataFrame(columns=['date', 'login', 'growth_phase', 'repo_name'])
    logging.info(f"Processing newcomer data for repo {repo_name}, total rows: {len(newcomer_data)}")
    for newcomber in newcomer_data.itertuples():
        newcomer_id = newcomber.Index
        newcomer_date = newcomber.attrition_date
        newcomer_developer = newcomber.attrition_developer
        if pd.isna(newcomer_date):
            logging.debug(f"Skipping login {newcomer_developer} as attrition date is nan.")
            continue
        date = pd.to_datetime(newcomer_date).to_period('M').to_timestamp()
        if date >= start_date:
            phase = classify_growth_phase(
                commits[commits['date'] <= date]['months_since_start'].values,
                commits[commits['date'] <= date]['cumulative'].values,
                best_model
            )
            attritions.loc[newcomber.Index, 'growth_phase'] = phase
            new_row = pd.DataFrame({
                'date': [date],
                'login': [newcomer_developer],
                'growth_phase': [phase],
                'repo_name': [repo_name]
            })
            burst_dev_stage = pd.concat([burst_dev_stage, new_row], ignore_index=True)
            logging.info(f"Processed developer {newcomer_developer} at {date}: phase={phase}")
        else:
            logging.debug(f"Skipping {newcomer_developer} as date {date} < repo start date {start_date}")
    return burst_dev_stage

def process_all_repos():
    repo_names = attritions['repo_name'].unique().tolist()
    all_results = []

    logging.info(f"Processing all repos, count: {len(repo_names)}")
    for repo_name in repo_names:
        try:
            logging.info(f"---- Processing {repo_name} ----")
            results = analyze_repo_commits(repo_name)
            all_results.append(results)
            logging.info(f"Completed {repo_name}, {results.shape[0]} rows returned.")
        except Exception as e:
            logging.error(f"Error processing {repo_name}: {e}")

    try:
        attritions.to_csv('../data/attrition_contributors_core_developer_add_growth_phase_without_burst.csv', index=False)
        logging.info("Saved updated attritions CSV with growth phase.")
    except Exception as e:
        logging.error(f"Error saving attritions CSV: {e}")

    if all_results:
        try:
            combined_results = pd.concat(all_results, ignore_index=True)
            combined_results.to_csv('../data/attrition_growth_phase_without_merge.csv', index=False)
            logging.info(f"Saved combined attrition growth phase data. Total rows: {combined_results.shape[0]}")
        except Exception as e:
            logging.error(f"Error saving combined results: {e}")

if __name__ == "__main__":
    logging.info("Starting main analysis process...")
    process_all_repos()
