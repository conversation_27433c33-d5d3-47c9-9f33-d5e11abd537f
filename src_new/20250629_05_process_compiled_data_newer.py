import pandas as pd
import os
import logging
import numpy as np
import time
import warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from typing import Dict, List, Tuple, Optional

# 忽略警告
warnings.filterwarnings('ignore')

# 设置日志
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "process_compiled_data_20250731.log")),
        logging.StreamHandler(),
    ],
)

# 输出目录
output_dir = '../result/20250730_did_result_psm_matching_not_equal_0_5/'
os.makedirs(output_dir, exist_ok=True)

# 定义要处理的limits
ATTRITION_LIMITS = [180, 270, 450, 365]
# ATTRITION_LIMITS = [365]

def unify_treatment_attributes(compiled_data: pd.DataFrame) -> <PERSON>ple[pd.DataFrame, int]:
    """统一treatment属性：使用向量化操作最大化性能"""
    print("Step 1: Unifying treatment attributes...")
    
    # 检查并创建必要的列
    if 'growth_phase' not in compiled_data.columns:
        compiled_data['growth_phase'] = 'unknown'
    if 'newcomers' not in compiled_data.columns:
        compiled_data['newcomers'] = 0
    
    # 一次性获取所有treatment数据，避免重复groupby
    treatment_mask = (compiled_data['is_treated'] == 1) & (compiled_data['relativized_time'] == 0)
    treatment_data = compiled_data[treatment_mask][['cohort_id', 'tenure', 'commit_percent', 'commits', 'growth_phase', 'newcomers']]
    
    if treatment_data.empty:
        print("No treatment data found")
        return compiled_data, 0
    
    # 创建cohort_id到treatment值的映射
    treatment_dict = treatment_data.set_index('cohort_id').to_dict('index')
    
    # 使用向量化操作更新所有列
    treatment_columns = ['tenure', 'commit_percent', 'commits', 'growth_phase', 'newcomers']
    for col in treatment_columns:
        if col in treatment_data.columns:  # 确保列存在
            # 创建映射Series
            mapping_series = compiled_data['cohort_id'].map(
                {cohort_id: treatment_dict[cohort_id][col] for cohort_id in treatment_dict.keys()}
            )
            # 更新所有值，包括NaN值
            compiled_data[col] = mapping_series
    
    return compiled_data, len(treatment_data)

def add_derived_variables(compiled_data: pd.DataFrame) -> pd.DataFrame:
    """添加派生变量：使用向量化操作最大化性能"""
    print("Step 2: Adding derived variables...")
    
    # 批量添加log版本的变量 - 个人层面变量
    log_columns = ['tenure', 'commit_percent', 'commits']
    existing_log_columns = [col for col in log_columns if col in compiled_data.columns]
    
    # 批量创建log列，使用向量化操作
    if existing_log_columns:
        log_data = np.log1p(compiled_data[existing_log_columns])
        log_data.columns = [f'log_{col}' for col in existing_log_columns]
        compiled_data = pd.concat([compiled_data, log_data], axis=1)
    
    # 添加项目层面的log transform
    project_log_columns = ['pr_throughput', 'pull_request_success_rate', 'project_commits', 'project_contributors', 'project_age', 'time_to_merge', 'newcomers']
    existing_project_columns = [col for col in project_log_columns if col in compiled_data.columns]
    
    # 批量创建项目log列
    if existing_project_columns:
        project_log_data = np.log1p(compiled_data[existing_project_columns])
        # 为newcomers创建特殊的列名
        new_column_names = []
        for col in existing_project_columns:
            new_column_names.append(f'log_{col}')
        project_log_data.columns = new_column_names
        compiled_data = pd.concat([compiled_data, project_log_data], axis=1)
    
    # 创建time-cohort和repo-cohort effects - 使用更高效的字符串操作
    if 'is_post_treatment' in compiled_data.columns and 'cohort_id' in compiled_data.columns:
        # 使用str.cat进行更高效的字符串拼接
        compiled_data['time_cohort_effect'] = (
            compiled_data['is_post_treatment'].astype(str).str.cat(
                compiled_data['cohort_id'].astype(str), sep='_'
            )
        )
        
        if 'is_treated' in compiled_data.columns:
            compiled_data['repo_cohort_effect'] = (
                compiled_data['is_treated'].astype(str).str.cat(
                    compiled_data['cohort_id'].astype(str), sep='_'
                )
            )
    
    print("Completed adding derived variables")
    return compiled_data

def unify_project_characteristics(compiled_data: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
    """统一项目特征：使用向量化操作最大化性能"""
    print("Step 3: Unifying project characteristics...")
    
    # 检查并创建必要的列
    project_columns = ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 
                      'log_project_age_before_treatment', 'project_main_language']
    
    for col in project_columns:
        if col not in compiled_data.columns:
            if col == 'project_main_language':
                compiled_data[col] = ''
            else:
                compiled_data[col] = 0
    
    # 一次性获取所有treatment数据，避免重复groupby
    treatment_mask = (compiled_data['is_treated'] == 1) & (compiled_data['relativized_time'] == 0)
    treatment_data = compiled_data[treatment_mask][['cohort_id', 'log_project_commits', 'log_project_contributors', 'log_project_age', 'mainLanguage']]
    
    if treatment_data.empty:
        print("No treatment data found for project characteristics")
        return compiled_data, 0
    
    # 创建cohort_id到项目特征的映射
    project_dict = treatment_data.set_index('cohort_id').to_dict('index')
    
    # 定义列映射
    column_mapping = {
        'log_project_commits': 'log_project_commits_before_treatment',
        'log_project_contributors': 'log_project_contributors_before_treatment',
        'log_project_age': 'log_project_age_before_treatment',
        'mainLanguage': 'project_main_language'
    }
    
    # 使用向量化操作更新所有项目特征列
    for source_col, target_col in column_mapping.items():
        if source_col in treatment_data.columns:
            # 创建映射Series
            mapping_series = compiled_data['cohort_id'].map(
                {cohort_id: project_dict[cohort_id][source_col] for cohort_id in project_dict.keys()}
            )
            # 更新所有值，包括NaN值
            compiled_data[target_col] = mapping_series
    
    return compiled_data, len(treatment_data)

def modify_growth_phase(compiled_data: pd.DataFrame) -> pd.DataFrame:
    """修改growth_phase列：将before_start替换为first 3 months"""
    print("Step 4: Modifying growth_phase values...")
    
    if 'growth_phase' in compiled_data.columns:
        # 统计修改前的值
        before_count = (compiled_data['growth_phase'] == 'before_start').sum()
        
        # 替换before_start为first 3 months
        compiled_data['growth_phase'] = compiled_data['growth_phase'].replace('before_start', 'first 3 months')
        compiled_data['growth_phase'] = compiled_data['growth_phase'].replace('decelerating', 'steady')
        # 统计修改后的值
        after_count = (compiled_data['growth_phase'] == 'first 3 months').sum()
        
        print(f"Modified {before_count} 'before_start' values to 'first 3 months'")
        print(f"Total 'first 3 months' values after modification: {after_count}")
    else:
        print("growth_phase column not found in data")
    
    return compiled_data

def remove_unwanted_columns(compiled_data: pd.DataFrame) -> pd.DataFrame:
    """删除不需要的列：burst 和 attrition_count"""
    print("Step 5: Removing unwanted columns...")
    
    columns_to_remove = ['burst', 'attrition_count']
    existing_columns = [col for col in columns_to_remove if col in compiled_data.columns]
    
    if existing_columns:
        compiled_data = compiled_data.drop(columns=existing_columns)
        print(f"Successfully removed columns: {existing_columns}")
    else:
        print("No columns were removed")
    
    return compiled_data

def optimize_memory_usage(df: pd.DataFrame) -> pd.DataFrame:
    """优化DataFrame的内存使用"""
    print("Optimizing memory usage...")
    
    for col in df.columns:
        col_type = df[col].dtype
        
        if col_type != 'object':
            c_min = df[col].min()
            c_max = df[col].max()
            
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
    
    return df

def add_main_language(compiled_data: pd.DataFrame) -> pd.DataFrame:
    """从sample_projects_total.csv添加mainLanguage列"""
    print("Step 0: Adding mainLanguage from sample projects data...")

    # 读取sample projects文件
    sample_projects_file = '/home/<USER>/repo/disengagement/data/sample_projects_total.csv'

    if not os.path.exists(sample_projects_file):
        print(f"Warning: Sample projects file not found: {sample_projects_file}")
        if 'mainLanguage' not in compiled_data.columns:
            compiled_data['mainLanguage'] = 'unknown'
        return compiled_data

    try:
        sample_projects_data = pd.read_csv(sample_projects_file)
        print(f"Loaded sample projects data shape: {sample_projects_data.shape}")

        # 检查必要的列
        if 'name' not in sample_projects_data.columns or 'mainLanguage' not in sample_projects_data.columns:
            print("Warning: Required columns ('name', 'mainLanguage') not found in sample projects file")
            if 'mainLanguage' not in compiled_data.columns:
                compiled_data['mainLanguage'] = 'unknown'
            return compiled_data

        # 创建repo name到mainLanguage的映射
        # 注意：sample_projects_data中的'name'列包含完整的repo名称（如"owner/repo"）
        language_mapping = sample_projects_data.set_index('name')['mainLanguage'].to_dict()
        print(f"Created language mapping for {len(language_mapping)} repositories")

        # 添加mainLanguage列
        if 'repo_name' in compiled_data.columns:
            compiled_data['mainLanguage'] = compiled_data['repo_name'].map(language_mapping)
            # 填充缺失值
            compiled_data['mainLanguage'] = compiled_data['mainLanguage'].fillna('unknown')

            matched_count = compiled_data['mainLanguage'].ne('unknown').sum()
            total_count = len(compiled_data)
            print(f"Successfully added mainLanguage for {matched_count}/{total_count} records")
            print("Language distribution:")
            print(compiled_data['mainLanguage'].value_counts().head())
        else:
            print("Warning: repo_name column not found in compiled_data")
            compiled_data['mainLanguage'] = 'unknown'

    except Exception as e:
        print(f"Error reading sample projects file: {e}")
        if 'mainLanguage' not in compiled_data.columns:
            compiled_data['mainLanguage'] = 'unknown'

    return compiled_data

def process_compiled_data_for_limit(limit: int) -> Optional[pd.DataFrame]:
    """处理单个limit的compiled_data"""
    start_time = time.time()
    print(f"\n{'='*60}")
    print(f"PROCESSING COMPILED DATA FOR LIMIT: {limit}")
    print(f"{'='*60}")
    
    # 读取compiled_data文件
    input_file = f'{output_dir}compiled_data_test_limit{limit}.csv'
    
    if not os.path.exists(input_file):
        print(f"Error: Compiled data file not found: {input_file}")
        logging.error(f"Compiled data file not found: {input_file}")
        return None
    
    print(f"Reading compiled data from: {input_file}")
    
    # 使用更高效的数据读取方式
    compiled_data = pd.read_csv(input_file, low_memory=False, engine='c')
    print(f"Loaded compiled data shape: {compiled_data.shape}")
    
    # 优化内存使用
    compiled_data = optimize_memory_usage(compiled_data)
    
    # 确保必要的列存在
    required_columns = ['cohort_id', 'is_treated', 'relativized_time', 'tenure', 'commit_percent', 'commits']
    missing_columns = [col for col in required_columns if col not in compiled_data.columns]
    if missing_columns:
        print(f"Error: Missing required columns: {missing_columns}")
        logging.error(f"Missing required columns: {missing_columns}")
        return None
    
    # 第零步：添加mainLanguage
    compiled_data = add_main_language(compiled_data)
    
    # 第一步：统一treatment属性
    compiled_data, treatment_count = unify_treatment_attributes(compiled_data)
    
    # 第二步：添加派生变量（创建log_xxx列）
    compiled_data = add_derived_variables(compiled_data)
    
    # 第三步：统一项目特征（需要log_xxx列）
    compiled_data, project_count = unify_project_characteristics(compiled_data)
    
    # 第四步：修改growth_phase值
    compiled_data = modify_growth_phase(compiled_data)
    
    # 第五步：删除不需要的列
    compiled_data = remove_unwanted_columns(compiled_data)
    
    # 第六步：保存处理后的数据
    print("Step 6: Saving processed data...")
    output_file = f'{output_dir}compiled_data_test_limit{limit}_processed.csv'
    
    # 使用更高效的保存方式
    compiled_data.to_csv(output_file, index=False, compression=None)
    
    total_time = time.time() - start_time
    print(f"\n{'='*60}")
    print(f"COMPLETED PROCESSING FOR LIMIT: {limit}")
    print(f"Final data shape: {compiled_data.shape}")
    print(f"Memory usage: {compiled_data.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
    print(f"Treatment attributes updated: {treatment_count}")
    print(f"Project characteristics updated: {project_count}")
    print(f"Total processing time: {total_time:.1f} seconds")
    print(f"Output saved to: {output_file}")
    print(f"{'='*60}\n")
    
    # 输出统计信息
    if 'growth_phase' in compiled_data.columns:
        print("Growth phase distribution:")
        print(compiled_data['growth_phase'].value_counts())
    
    if 'newcomers' in compiled_data.columns:
        print(f"\nNewcomers statistics:")
        print(f"Mean: {compiled_data['newcomers'].mean():.2f}")
        print(f"Median: {compiled_data['newcomers'].median():.2f}")
        print(f"Max: {compiled_data['newcomers'].max()}")
        print(f"Min: {compiled_data['newcomers'].min()}")
    
    return compiled_data

def process_limit_wrapper(limit: int) -> Optional[pd.DataFrame]:
    """包装函数，用于并行处理"""
    try:
        return process_compiled_data_for_limit(limit)
    except Exception as e:
        print(f"Error processing limit {limit}: {e}")
        logging.error(f"Error processing limit {limit}: {e}")
        return None

def main_parallel():
    """并行处理主函数"""
    overall_start_time = time.time()
    print(f"\n{'='*80}")
    print("STARTING COMPILED DATA PROCESSING (PARALLEL)")
    print(f"Processing limits: {ATTRITION_LIMITS}")
    print(f"CPU cores available: {mp.cpu_count()}")
    print(f"{'='*80}")
    
    # 确定并行进程数
    max_workers = min(len(ATTRITION_LIMITS), mp.cpu_count())
    print(f"Using {max_workers} parallel workers")
    
    processed_count = 0
    results = []
    
    # 使用进程池并行处理
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_limit = {executor.submit(process_limit_wrapper, limit): limit 
                          for limit in ATTRITION_LIMITS}
        
        # 收集结果
        for future in as_completed(future_to_limit):
            limit = future_to_limit[future]
            try:
                result = future.result()
                if result is not None:
                    processed_count += 1
                    results.append(result)
                print(f"Completed processing limit: {limit}")
            except Exception as e:
                print(f"Limit {limit} generated an exception: {e}")
                logging.error(f"Limit {limit} generated an exception: {e}")
    
    overall_processing_time = time.time() - overall_start_time
    print(f"\n{'='*80}")
    print("COMPILED DATA PROCESSING COMPLETED")
    print(f"Successfully processed: {processed_count}/{len(ATTRITION_LIMITS)} limits")
    print(f"Total processing time: {overall_processing_time:.1f} seconds")
    print(f"Average time per limit: {overall_processing_time/len(ATTRITION_LIMITS):.1f} seconds")
    print(f"{'='*80}")
    
    return results

def main_sequential():
    """顺序处理主函数"""
    overall_start_time = time.time()
    print(f"\n{'='*80}")
    print("STARTING COMPILED DATA PROCESSING (SEQUENTIAL)")
    print(f"Processing limits: {ATTRITION_LIMITS}")
    print(f"{'='*80}")
    
    total_limits = len(ATTRITION_LIMITS)
    processed_count = 0
    
    for limit_idx, limit in enumerate(ATTRITION_LIMITS, 1):
        print(f"\nProcessing limit {limit_idx}/{total_limits}: {limit}")
        
        try:
            result = process_compiled_data_for_limit(limit)
            if result is not None:
                processed_count += 1
        except Exception as e:
            print(f"Error processing limit {limit}: {e}")
            logging.error(f"Error processing limit {limit}: {e}")
        
        # 计算进度百分比
        progress = (limit_idx / total_limits) * 100
        print(f"Overall progress: {progress:.1f}% ({limit_idx}/{total_limits} limits completed)")
    
    overall_processing_time = time.time() - overall_start_time
    print(f"\n{'='*80}")
    print("COMPILED DATA PROCESSING FOR ALL LIMITS COMPLETED")
    print(f"Successfully processed: {processed_count}/{total_limits} limits")
    print(f"Total processing time: {overall_processing_time:.1f} seconds")
    print(f"Average time per limit: {overall_processing_time/total_limits:.1f} seconds")
    print(f"{'='*80}")

def main():
    """主函数，选择并行或顺序处理"""
    print(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 根据limits数量决定是否使用并行处理
    if len(ATTRITION_LIMITS) > 1 and mp.cpu_count() > 1:
        print("Using parallel processing...")
        main_parallel()
    else:
        print("Using sequential processing...")
        main_sequential()
    
    print(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 
