import logging
import pandas as pd
from pymongo import MongoClient
import os

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/generate_csv_pr_comments.log", mode="w", encoding="utf-8"),
    ],
)

# MongoDB 连接配置
WINDOWS_IP = "localhost"
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")

db = client["disengagement"]
pr_comments_collection = db["pr_comments"]  # 修正集合名称


def fetch_pr_comments_and_generate_csv(repo_name, output_csv_path):
    """
    从 MongoDB 中获取指定仓库的拉取请求评论并生成 CSV 文件。

    参数:
        repo_name (str): 要过滤的仓库名称。
        output_csv_path (str): 输出 CSV 文件的路径。
    """
    logging.info(f"Fetching pull request comments for repository: {repo_name}")

    # 查询 MongoDB 获取拉取请求评论
    try:
        pr_comments = list(pr_comments_collection.find({"repo_name": repo_name}))
    except Exception as e:
        logging.error(f"Error querying pull request comments for {repo_name}: {e}")
        return

    if not pr_comments:
        logging.warning(f"No pull request comments found for repository: {repo_name}")
        return

    # 转换为 DataFrame
    logging.info(f"Found {len(pr_comments)} pull request comments for repository: {repo_name}")

    # 提取所需字段
    rows = []
    for pr_comment in pr_comments:
        user = pr_comment.get("user") or {}
        login = user.get("login") if user else ""
        pull_request_url = pr_comment.get("pull_request_url") or ""
        pr_id = pull_request_url.split("/")[-1] if pull_request_url else ""

        # 处理 reactions
        reactions = pr_comment.get("reactions") or {}
        total_reactions = reactions.get("total_count", 0)
        plus_one = reactions.get("+1", 0)
        minus_one = reactions.get("-1", 0)
        laugh = reactions.get("laugh", 0)
        hooray = reactions.get("hooray", 0)
        confused = reactions.get("confused", 0)
        heart = reactions.get("heart", 0)
        rocket = reactions.get("rocket", 0)
        eyes = reactions.get("eyes", 0)

        rows.append({
            "repo_name": repo_name,
            "pr_id": pr_id,
            "comment_id": pr_comment.get("id"),
            "pull_request_review_id": pr_comment.get("pull_request_review_id") or "",
            "user_login": login,
            "body": pr_comment.get("body") or "",
            "created_at": pr_comment.get("created_at") or "",
            "updated_at": pr_comment.get("updated_at") or "",
            "diff_hunk": pr_comment.get("diff_hunk") or "",
            "path": pr_comment.get("path") or "",
            "commit_id": pr_comment.get("commit_id") or "",
            "original_commit_id": pr_comment.get("original_commit_id") or "",
            "line": pr_comment.get("line") or "",
            "side": pr_comment.get("side") or "",
            "subject_type": pr_comment.get("subject_type") or "",
            "total_reactions": total_reactions,
            "plus_one_reactions": plus_one,
            "minus_one_reactions": minus_one,
            "laugh_reactions": laugh,
            "hooray_reactions": hooray,
            "confused_reactions": confused,
            "heart_reactions": heart,
            "rocket_reactions": rocket,
            "eyes_reactions": eyes,
        })

    # 创建 DataFrame
    df = pd.DataFrame(rows)

    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
        df.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"Pull request comments CSV generated at {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to save pull request comments CSV: {e}")


def main():
    # 获取所有仓库名称
    try:
        cache_collection = db["progress_cache"]
        finished_projects = cache_collection.find({
            "commits_finished": 1,
            "pr_finished": 1,
            "pr_review_finished": 1
        }, {"repo_name": 1})
        repos = [project["repo_name"] for project in finished_projects]
    except Exception as e:
        logging.error(f"Error fetching distinct repo names: {e}")
        return

    if not repos:
        logging.warning("No repositories found in the collection.")
        return

    for repo in repos:
        # 替换 "/" 为 "_" 以避免文件路径问题
        output_csv_path = os.path.join("../data/pr_comments", f"{repo.replace('/', '_')}_pr_comments.csv")
        fetch_pr_comments_and_generate_csv(repo, output_csv_path)


    for repo in repos:
        output_csv_path = os.path.join("../data/pr_comments", f"{repo.replace('/', '_')}_pr_comments.csv")
        df = pd.DataFrame()
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
            df.to_csv(output_csv_path, index=False, encoding="utf-8")
            logging.info(f"Empty pull request comments CSV generated at {output_csv_path}")
        except Exception as e:
            logging.error(f"Failed to save empty pull request comments CSV for {repo}: {e}")

    logging.info("All pull request comments CSVs generated.")


if __name__ == "__main__":
    main()
