import gc
import pandas as pd

def get_processed_commit_file_repo_name(repo_name):
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
        return None
    return repo_commit

import logging
from pymongo import MongoClient
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/identify_break_disengagement.log", mode="w", encoding="utf-8"),
    ],
)

def process_repo_data_and_store(project_names, core_devs_data, window_size=1, attrition_limit=365):
    """
    Process data for each repository based on its commits and core developers,
    then store the results in MongoDB incrementally.

    Args:
        project_names (pd.DataFrame): Names for all repositories.
        core_devs_data (pd.DataFrame): Core developers for each repository.
        window_size (int): The size of the window in days for calculating pauses.
        attrition_limit (int): The threshold in days for determining developer attrition.
    """
    def process_repo_data(project_names, core_devs_data, collection, window_size, attrition_limit):
        # Ensure 'core_developers' column is correctly processed
        logging.info("Processing core developers data.")
        core_devs_data['core_developers'] = core_devs_data['core_developers'].apply(
            lambda x: x if isinstance(x, list) else eval(x)
        )

        # Process each repository with a sequential project_id
        project_counter = 0
        # collect repo_name and core_dev_id from collection, so that we can skip the processed data
        processed_repo_names = collection.distinct("repo_name")
        processed_core_devs = collection.distinct("core_dev_login")
        
        # for repo_name in project_names:
        # only process the repo_name that is not in the collection
        for repo_name in project_names:
            logging.info(f"Processing repository: {repo_name} (Project ID: {project_counter})")

            # Fetch commit data for the current repository
            try:
                repo_commits = get_processed_commit_file_repo_name(repo_name)
                # unify timezone for repo_commits
                repo_commits['date'] = pd.to_datetime(repo_commits['date'], format='mixed')                #
            except Exception as e:
                logging.error(f"Error fetching commit data for repository '{repo_name}': {e}")
                continue

            # Get core developers for the repository
            try:
                core_developers = core_devs_data[core_devs_data['repo_name'] == repo_name]['core_developers'].iloc[0]
                logging.debug(f"Core developers for # {project_counter} '{repo_name}': {core_developers}")
            except Exception as e:
                logging.error(f"Error fetching core developers for repository '{repo_name}': {e}")
                continue

            # Process each core developer for the repository
            core_dev_counter = 0
            for core_dev in core_developers:
                if core_dev in processed_core_devs:
                    continue
                
                logging.debug(f"# {project_counter} '{repo_name}' -- Processing core developer: {core_dev}")

                # Filter commits by the core developer
                dev_commits = repo_commits[repo_commits['author_login'] == core_dev]
                dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')
                dev_commits = dev_commits.sort_values(by='date')

                # Calculate inactivity periods using sliding window
                def get_pauses_with_commit(dev_commits, window_size):
                    pauses = {}
                    pause_id = 0
                    for i in range(1, len(dev_commits)):
                        days_diff = (dev_commits.iloc[i]['date'] - dev_commits.iloc[i - 1]['date']).days
                        units_diff = days_diff / window_size
                        # if units_diff > 0, then it is a pause
                        if units_diff > 0:
                            pauses[pause_id] = {
                                'start_date': dev_commits.iloc[i - 1]['date'],
                                'end_date': dev_commits.iloc[i]['date'],
                                'duration_units': units_diff
                            }
                            pause_id += 1
                    logging.debug(f"# {project_counter} '{repo_name}' -- Pauses: {pauses}")
                    return pauses

                pauses = get_pauses_with_commit(dev_commits, window_size)

                # Identify long pauses
                dev_output = {
                    "repo_id": project_counter,
                    "repo_name": repo_name,
                    "core_dev_id": core_dev_counter,
                    "core_dev_login": core_dev,
                    "Attrition": None,
                    "Breaks": []
                }
                core_dev_counter += 1

                if len(pauses) > 0:
                    pauses = pd.DataFrame(pauses).T
                    pauses_units = pauses['duration_units']
                    logging.debug(f"# {project_counter} '{repo_name}' -- Pauses: {pauses}")
                    if not pauses_units.empty:
                        Q1 = pauses_units.quantile(0.25)
                        Q3 = pauses_units.quantile(0.75)
                        IQR = Q3 - Q1
                        Tfov = Q3 + 3 * IQR
                        logging.debug(f"# {project_counter} '{repo_name}' -- Q1: {Q1}, Q3: {Q3}, IQR: {IQR}, Tfov: {Tfov}")
                        long_pauses = pauses[pauses['duration_units'] > Tfov].reset_index(drop=True)
                        logging.debug(f"# {project_counter} '{repo_name}' -- Long pauses: {long_pauses}")

                        break_counter = 0
                        for pause_index in long_pauses.index:
                            try:
                                start_date = long_pauses.loc[pause_index, 'start_date']
                                end_date = long_pauses.loc[pause_index, 'end_date']
                                duration_units = long_pauses.loc[pause_index, 'duration_units']
                                dev_output["Breaks"].append({
                                    "break_id": break_counter,
                                    "start_date": str(start_date.date()),
                                    "end_date": str(end_date.date()),
                                    "duration_units": duration_units
                                })
                                break_counter += 1
                                logging.info(f"# {project_counter} '{repo_name}' -- Break recorded for {core_dev}: {start_date.date()} to {end_date.date()} ({duration_units} units)")
                            except IndexError as e:
                                logging.error(f"Error processing pause for {core_dev}: {e}")
                                continue
                        last_commit_date = dev_commits['date'].max()
                        project_last_commit_date = repo_commits['date'].max()

                        # Ensure both dates are timezone-naive
                        last_commit_date = last_commit_date.tz_localize(None)
                        project_last_commit_date = project_last_commit_date.tz_localize(None)
                        
                        if (project_last_commit_date - last_commit_date).days > attrition_limit:
                            dev_output["Attrition"] = {
                                "attrition_date": [str(last_commit_date.date())]
                            }
                            logging.info(f"# {project_counter} '{repo_name}' -- Developer {core_dev} marked as disengaged since {last_commit_date.date()}.")
                        # also add breaks with duration_units >= attrition_limits into attrition
                        for break_event in dev_output["Breaks"]:
                            if break_event["duration_units"] >= attrition_limit:
                                ## add the break into attrition
                                if dev_output["Attrition"] is None:
                                    dev_output["Attrition"] = {
                                        "attrition_date": [str(break_event["start_date"])]
                                    }
                                else:
                                    dev_output["Attrition"]["attrition_date"].append(str(break_event["start_date"]))
                                
                                logging.info(f"# {project_counter} '{repo_name}' -- Developer {core_dev} marked as disengaged since {break_event['start_date']}.")
                try:
                    collection.insert_one(dev_output)
                    logging.info(f"# {project_counter} '{repo_name}' -- Data for core developer {core_dev} stored in MongoDB.")
                except Exception as e:
                    logging.error(f"Error storing data for core developer {core_dev} in MongoDB: {e}")
                del dev_output, pauses
                gc.collect()
            project_counter += 1
            del repo_commits
            gc.collect()

    # MongoDB Connection
    logging.info("Connecting to MongoDB.")
    WINDOWS_IP = "localhost"
    PORT = 27017
    client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    # set unique index for collection of repo_name and core_dev_id
    # collection.create_index([("repo_name", 1), ("core_dev_id", 1)], unique=True)
    # Process the data and store incrementally
    logging.info("Starting data processing for all repositories.")
    process_repo_data(project_names, core_devs_data, collection, window_size, attrition_limit)
    logging.info("Data processing completed.")

    logging.info("Data successfully stored in MongoDB.")

client = MongoClient('mongodb://localhost:27017/')  # 替换为你的 MongoDB 实例的 URI

db = client['disengagement']  # 选择 disengagement 数据库
cache_collection = db["progress_cache"]
finished_projects = cache_collection.find({
    "commits_finished": 1,
    "pr_finished": 1,
    "pr_review_finished": 1
}, {"repo_name": 1})
repos = [project["repo_name"] for project in finished_projects]
project_names = repos

core_developers_df = pd.read_csv('../data/core_developer_list_total_repo.csv')
process_repo_data_and_store(project_names, core_developers_df, window_size=1, attrition_limit=365)