import pandas as pd

# attritions with growth phase
attrition_growth_phase = pd.read_csv('../data/combined_attrition_growth_phases.csv')
attrition_growth_phase

# repo_info with create_time, mainLanguage, and duration
repo_info = pd.read_csv('../data/sample_projects_total.csv')
repo_info

project_names = pd.read_csv('../result/repo_name_list.csv')['repo_name'].tolist()
project_names

attritions = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')

global_min_time = pd.Timestamp('2010-08-30 00:00:00')

attritions['standardized_time_weeks'] = (
    (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
).astype(int)

attritions = pd.read_csv('../result/attritions.csv')
attritions

# group by repo_name to generate attrition count
attrition_count = attritions.groupby('repo_name')['attrition_date'].count().reset_index()
attrition_count.columns = ['repo_name', 'attrition_count']
attrition_count

attritions['standardized_time_weeks'] = (
    (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
).astype(int)

import matplotlib.pyplot as plt
# Merge attritions with repo_info to get the mainLanguage for each repo
attritions_with_language = pd.merge(
    attritions, 
    repo_info[['name', 'mainLanguage']], 
    left_on='repo_name', 
    right_on='name', 
    how='left'
)

# Drop the redundant name column
attritions_with_language.drop('name', axis=1, inplace=True)


# Count attritions by programming language
language_attrition_counts = attritions_with_language.groupby('mainLanguage').size().sort_values(ascending=False)

# Show the top 15 languages with most attritions
top_languages = language_attrition_counts.head(15)
print(f"Top 15 programming languages by attrition count:\n{top_languages}")

# Calculate percentage of attritions by language
language_attrition_percentage = (language_attrition_counts / language_attrition_counts.sum() * 100).round(2)

# Visualize attritions by programming language
plt.figure(figsize=(12, 6))
top_languages.plot(kind='bar')
plt.title('Number of Core Developer Attritions by Programming Language', fontsize=14)
plt.xlabel('Programming Language', fontsize=12)
plt.ylabel('Number of Attritions', fontsize=12)
plt.xticks(rotation=45)
plt.tight_layout()

# Save the updated attritions DataFrame with mainLanguage for future use
attritions.to_csv('../result/attritions_with_language.csv', index=False)

attritions_with_language

import pandas as pd
# only maintain newcomer list that are core developers
core_developer_list =  pd.read_csv('../data/core_developer_list_total_repo.csv')
core_developer_list['core_developers'] = core_developer_list['core_developers'].apply(lambda x: x[1:-1].replace("'", "").split(', '))
core_developer_list = core_developer_list.explode('core_developers')
core_developer_list['core_developers'] = core_developer_list['core_developers'].str.strip()
core_developer_list
newcomer_contributors = pd.read_csv('../data/newcomer_contributors.csv')
newcomer_contributors['date'] = pd.to_datetime(newcomer_contributors['date'])
newcomer_contributors = newcomer_contributors[newcomer_contributors['login'].isin(core_developer_list['core_developers'])]


# Merge newcomer_contributors with repo_info to get the mainLanguage information
newcomer_contributors_with_language = pd.merge(
    newcomer_contributors,
    repo_info[['name', 'mainLanguage']],
    left_on='repo_name',
    right_on='name',
    how='left'
)

# Drop the redundant name column
newcomer_contributors_with_language.drop('name', axis=1, inplace=True)

# Check if there are any missing values in mainLanguage after merging
missing_languages = newcomer_contributors_with_language['mainLanguage'].isna().sum()
print(f"Number of newcomers with missing language information: {missing_languages}")


newcomer_contributors_with_language
attritions_with_language

newcomer_contributors_with_language['mainLanguage'].value_counts()

languages = newcomer_contributors_with_language['mainLanguage'].value_counts().index.tolist()
languages

import pandas as pd
import seaborn as sns
import numpy as np
import os
import matplotlib.pyplot as plt

os.makedirs('../figures/language_comparison', exist_ok=True)
os.makedirs('../figures/20250423_language_trend', exist_ok=True)
sns.set(style="whitegrid")
languages = newcomer_contributors_with_language['mainLanguage'].value_counts().index.tolist()

# 数据准备同前（假设attritions/newcomer_contributors/top_languages已准备好）

attritions_with_language['attrition_date'] = pd.to_datetime(attritions_with_language['attrition_date'])
attritions_with_language['quarter'] = pd.PeriodIndex(attritions_with_language['attrition_date'], freq='Q')
attritions_quarterly = attritions_with_language[(attritions_with_language['attrition_date'] >= '2011-01-01') &
                                 (attritions_with_language['attrition_date'] <= '2023-12-31')]

newcomer_contributors_with_language['date'] = pd.to_datetime(newcomer_contributors_with_language['date'])
newcomer_contributors_with_language['quarter'] = newcomer_contributors_with_language['date'].dt.to_period("Q")
newcomers_quarterly = newcomer_contributors_with_language[(newcomer_contributors_with_language['date'] >= '2011-01-01') &
                                           (newcomer_contributors_with_language['date'] <= '2023-12-31')]

attrition_by_lang_quarterly = (attritions_quarterly
    .groupby(['mainLanguage', 'quarter'])
    .size().reset_index(name='attrition_count'))
newcomer_by_lang_quarterly = (newcomers_quarterly
    .groupby(['mainLanguage', 'quarter'])
    .size().reset_index(name='newcomer_count'))

quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
all_quarters = pd.DataFrame(quarters, columns=['quarter'])
quarter_strs = [str(q) for q in quarters]
x_idx = np.arange(len(quarters))   # 横坐标序号

for language in languages:
    # 主趋势数据
    lang_attrition = attrition_by_lang_quarterly[attrition_by_lang_quarterly['mainLanguage'] == language]
    lang_newcomer = newcomer_by_lang_quarterly[newcomer_by_lang_quarterly['mainLanguage'] == language]
    lang_attrition_all_quarters = pd.merge(
        all_quarters, lang_attrition, on='quarter', how='left'
    ).fillna({'mainLanguage': language, 'attrition_count': 0})
    lang_newcomer_all_quarters = pd.merge(
        all_quarters, lang_newcomer, on='quarter', how='left'
    ).fillna({'mainLanguage': language, 'newcomer_count': 0})

    attrition_y = lang_attrition_all_quarters['attrition_count'].values
    newcomer_y = lang_newcomer_all_quarters['newcomer_count'].values

    fig, ax1 = plt.subplots(figsize=(8, 6))
    color1 = 'tab:red'
    color2 = 'tab:blue'

    # 画主趋势折线，两条都画在 ax1 上
    line1, = ax1.plot(
        x_idx, attrition_y, marker='o', color=color1, linewidth=2, label='Disengagement'
    )
    line2, = ax1.plot(
        x_idx, newcomer_y, marker='s', color=color2, linewidth=2, linestyle='--', label='New Contributors'
    )

    # violin分布数据
    lang_attritions = attritions_quarterly[attritions_quarterly['mainLanguage'] == language].copy()
    lang_attritions['quarter_str'] = lang_attritions['quarter'].astype(str)
    attr_df = (lang_attritions
               .groupby(['quarter_str', 'repo_name'])
               .size().reset_index(name='attrition_count'))
    lang_newcomers = newcomers_quarterly[newcomers_quarterly['mainLanguage'] == language].copy()
    lang_newcomers['quarter_str'] = lang_newcomers['quarter'].astype(str)
    new_df = (lang_newcomers
              .groupby(['quarter_str', 'repo_name'])
              .size().reset_index(name='newcomer_count'))
    # X轴格式化
    ax1.set_xticks(x_idx)
    ax1.set_xticklabels(quarter_strs, rotation=90, fontsize=10)
    ax1.set_xlim(-0.5, len(quarters) - 0.5)
    # ax1.set_xlabel('Quarter', fontsize=14)
    # ax1.set_ylabel('Count', fontsize=14)

    # 统一 y 轴范围
    y_max = max(attrition_y.max(), newcomer_y.max())
    ax1.set_ylim(0, y_max * 1.1)

    # plt.title(f'Core Newcomers v.s. Attritions of {language}', fontsize=20)
    ax1.legend([line1, line2], ['Disengagement', 'Engagement'], loc='upper left', fontsize=20)
    plt.tight_layout()    
    plt.savefig(f'../figures/20250423_language_trend/{language}_trend.pdf', bbox_inches='tight')
    plt.show()

print(f"Generated violin+trend charts for {len(top_languages)} programming languages")


# Convert repo creation dates to datetime
repo_info['createdAt'] = pd.to_datetime(repo_info['createdAt'])

# Ensure attrition dates are in datetime format
attritions['attrition_date'] = pd.to_datetime(attritions['attrition_date'])

# Merge attritions with repo creation dates
attritions_with_age = pd.merge(
    attritions,
    repo_info[['name', 'createdAt']],
    left_on='repo_name',
    right_on='name',
    how='left'
)

# Calculate repo age in days when attrition happened
attritions_with_age['repo_age_days'] = (
    attritions_with_age['attrition_date'] - attritions_with_age['createdAt']
).dt.days

# Calculate repo age in years for easier interpretation
attritions_with_age['repo_age_years'] = attritions_with_age['repo_age_days'] / 365.25

# Drop redundant column
attritions_with_age.drop('name', axis=1, inplace=True)

# Show the first few rows
attritions_with_age.head()

# get attritions with quarter between 2011-01-01 and 2023-12-31
attritions_with_age = attritions_with_age[
    (attritions_with_age['attrition_date'] >= '2011-01-01') &
    (attritions_with_age['attrition_date'] <= '2023-12-31')
]

attritions_with_age = attritions_with_age[attritions_with_age['repo_age_days'] > 0]
attritions_with_age

# get quartiles of repo age days
repo_age_quartiles = attritions_with_age['repo_age_days'].quantile([0.25, 0.5, 0.75])

# plot distribution of repo age days
plt.figure(figsize=(12, 6))
sns.histplot(attritions_with_age['repo_age_days'], bins=30, kde=True)
plt.axvline(repo_age_quartiles[0.25], color='r', linestyle='--', label='25th Percentile')
plt.axvline(repo_age_quartiles[0.5], color='g', linestyle='--', label='50th Percentile')
plt.axvline(repo_age_quartiles[0.75], color='b', linestyle='--', label='75th Percentile')
plt.title('Distribution of Repo Age (in Days) at Attrition', fontsize=14)
plt.xlabel('Repo Age (Days)', fontsize=12)
plt.ylabel('Frequency', fontsize=12)
plt.legend()

# Convert repo creation dates to datetime if not already
repo_info['createdAt'] = pd.to_datetime(repo_info['createdAt'], utc=True)

# Ensure newcomer dates are in datetime format
newcomer_contributors['date'] = pd.to_datetime(newcomer_contributors['date'], utc=True)

# Merge newcomers with repo creation dates
newcomers_with_age = pd.merge(
    newcomer_contributors,
    repo_info[['name', 'createdAt']],
    left_on='repo_name',
    right_on='name',
    how='left'
)

# Calculate repo age in days when newcomer joined
newcomers_with_age['repo_age_days'] = (
    newcomers_with_age['date'] - newcomers_with_age['createdAt']
).dt.days

# Calculate repo age in years for easier interpretation
newcomers_with_age['repo_age_years'] = newcomers_with_age['repo_age_days'] / 365.25

# Drop redundant column
newcomers_with_age.drop('name', axis=1, inplace=True)

# Filter to ensure we only have valid ages (age > 0)
newcomers_with_age = newcomers_with_age[newcomers_with_age['repo_age_days'] > 0]

# Get newcomers with quarter between 2011-01-01 and 2023-12-31
newcomers_with_age = newcomers_with_age[
    (newcomers_with_age['date'] >= '2011-01-01') &
    (newcomers_with_age['date'] <= '2023-12-31')
]

# Get quartiles of repo age days
newcomer_repo_age_quartiles = newcomers_with_age['repo_age_days'].quantile([0.25, 0.5, 0.75])

# Plot distribution of repo age days
plt.figure(figsize=(12, 6))
sns.histplot(newcomers_with_age['repo_age_days'], bins=30, kde=True)
plt.axvline(newcomer_repo_age_quartiles[0.25], color='r', linestyle='--', label='25th Percentile')
plt.axvline(newcomer_repo_age_quartiles[0.5], color='g', linestyle='--', label='50th Percentile')
plt.axvline(newcomer_repo_age_quartiles[0.75], color='b', linestyle='--', label='75th Percentile')
plt.title('Distribution of Repo Age (in Days) at Newcomer Joining', fontsize=14)
plt.xlabel('Repo Age (Days)', fontsize=12)
plt.ylabel('Frequency', fontsize=12)
plt.legend()
newcomer_repo_age_quartiles

repo_age_quartiles

newcomer_repo_age_quartiles

import seaborn as sns
import os

# set period based on repo age quartiles
periods = {
    '< 1 year': (0, 365),
    '1-2 year': (365, 730),
    '2-4 year': (730, 1460), 
    '> 4 year': (1460, float('inf'))
}
# Create a new column for repo age category
def categorize_repo_age(days):
    for period, (lower, upper) in periods.items():
        if lower <= days < upper:
            return period
    return 'Unknown'


# Apply the age category to both attritions and newcomers data
attritions_with_age['repo_age_category'] = attritions_with_age['repo_age_days'].apply(categorize_repo_age)
newcomers_with_age['repo_age_category'] = newcomers_with_age['repo_age_days'].apply(categorize_repo_age)

# Add quarter information to attritions and newcomers data sets
attritions_with_age['quarter'] = pd.PeriodIndex(attritions_with_age['attrition_date'], freq='Q')
newcomers_with_age['quarter'] = pd.PeriodIndex(newcomers_with_age['date'], freq='Q')

# Filter data for our time range
attritions_by_age_quarterly = attritions_with_age[
    (attritions_with_age['attrition_date'] >= '2011-01-01') & 
    (attritions_with_age['attrition_date'] <= '2023-12-31')
]

newcomers_by_age_quarterly = newcomers_with_age[
    (newcomers_with_age['date'] >= '2011-01-01') & 
    (newcomers_with_age['date'] <= '2023-12-31')
]

# Group by age category and quarter
attrition_by_age_quarter = attritions_by_age_quarterly.groupby(['repo_age_category', 'quarter']).size().reset_index(name='attrition_count')
newcomer_by_age_quarter = newcomers_by_age_quarterly.groupby(['repo_age_category', 'quarter']).size().reset_index(name='newcomer_count')

# Get all quarters for plotting
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
all_quarters = pd.DataFrame(quarters, columns=['quarter'])
quarter_strs = [str(q) for q in quarters]
x_idx = np.arange(len(quarters))

# Make plots for each age category
os.makedirs('../figures/age_comparison', exist_ok=True)
sns.set(style="whitegrid")

for age_category in periods.keys():
    # Filter data for current age category
    age_attrition = attrition_by_age_quarter[attrition_by_age_quarter['repo_age_category'] == age_category]
    age_newcomer = newcomer_by_age_quarter[newcomer_by_age_quarter['repo_age_category'] == age_category]
    
    # Ensure all quarters are present
    age_attrition_all_quarters = pd.merge(
        all_quarters, age_attrition, on='quarter', how='left'
    ).fillna({'repo_age_category': age_category, 'attrition_count': 0})
    
    age_newcomer_all_quarters = pd.merge(
        all_quarters, age_newcomer, on='quarter', how='left'
    ).fillna({'repo_age_category': age_category, 'newcomer_count': 0})

    # Get the y values for plotting
    attrition_y = age_attrition_all_quarters['attrition_count'].values
    newcomer_y = age_newcomer_all_quarters['newcomer_count'].values
    
    # Create the plot
    fig, ax1 = plt.subplots(figsize=(8, 6))
    color1 = 'tab:red'
    color2 = 'tab:blue'
    
    # Plot main trend lines
    line1, = ax1.plot(x_idx, attrition_y, marker='o', color=color1, linewidth=2, label='Disengagement')
    line2, = ax1.plot(x_idx, newcomer_y, marker='s', color=color2, linewidth=2, linestyle='--', label='New Contributors')
    
    # Set up x-axis
    ax1.set_xticks(x_idx)
    ax1.set_xticklabels(quarter_strs, rotation=90, fontsize=10)
    ax1.set_xlim(-0.5, len(quarters) - 0.5)
    
    # Set up y-axis
    y_max = max(attrition_y.max(), newcomer_y.max())
    ax1.set_ylim(0, y_max * 1.1)
    
    # Add title and legend
    # plt.title(f'Core Newcomers vs. Attritions in Repositories {age_category} Old', fontsize=20)
    ax1.legend([line1, line2], ['Disengagement', 'Engagement'], loc='upper left', fontsize=20)
    
    plt.tight_layout()
    plt.savefig(f'../figures/age_comparison/{age_category.replace(" ", "_")}_turnover.pdf', bbox_inches='tight')
    plt.show()

print(f"Generated comparison charts for {len(periods)} repository age categories")

attrition_growth_phase = pd.read_csv('../data/attrition_growth_phase_without_merge.csv')

attrition_growth_phase

newcomer_growth_phase = pd.read_csv('../data/newcomer_growth_phase.csv')
newcomer_growth_phase = newcomer_growth_phase[newcomer_growth_phase['login'].isin(core_developer_list['core_developers'])]
newcomer_growth_phase

import pandas as pd
import seaborn as sns
import numpy as np
import os
import matplotlib.pyplot as plt

# Ensure output directory exists
os.makedirs('../figures/growth_phase_comparison', exist_ok=True)

# Convert dates to datetime
attrition_growth_phase['date'] = pd.to_datetime(attrition_growth_phase['date'])
newcomer_growth_phase['date'] = pd.to_datetime(newcomer_growth_phase['date'])

# Add quarter information
attrition_growth_phase['quarter'] = pd.PeriodIndex(attrition_growth_phase['date'], freq='Q')
newcomer_growth_phase['quarter'] = pd.PeriodIndex(newcomer_growth_phase['date'], freq='Q')

# Filter data for our time range
attrition_by_phase = attrition_growth_phase[
    (attrition_growth_phase['date'] >= '2011-01-01') & 
    (attrition_growth_phase['date'] <= '2023-12-31')
]

newcomer_by_phase = newcomer_growth_phase[
    (newcomer_growth_phase['date'] >= '2011-01-01') & 
    (newcomer_growth_phase['date'] <= '2023-12-31')
]

# Group by growth phase and quarter
attrition_by_phase_quarter = attrition_by_phase.groupby(['growth_phase', 'quarter']).size().reset_index(name='attrition_count')
newcomer_by_phase_quarter = newcomer_by_phase.groupby(['growth_phase', 'quarter']).size().reset_index(name='newcomer_count')

# Get all quarters for plotting
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
all_quarters = pd.DataFrame(quarters, columns=['quarter'])
quarter_strs = [str(q) for q in quarters]
x_idx = np.arange(len(quarters))

# Set style
sns.set(style="whitegrid")
growth_phases = newcomer_growth_phase['growth_phase'].unique()
growth_phases
# Plot for each growth phase
for phase in growth_phases:
    # Filter data for current phase
    phase_attrition = attrition_by_phase_quarter[attrition_by_phase_quarter['growth_phase'] == phase]
    phase_newcomer = newcomer_by_phase_quarter[newcomer_by_phase_quarter['growth_phase'] == phase]
    
    # Ensure all quarters are present
    phase_attrition_all_quarters = pd.merge(
        all_quarters, phase_attrition, on='quarter', how='left'
    ).fillna({'growth_phase': phase, 'attrition_count': 0})
    
    phase_newcomer_all_quarters = pd.merge(
        all_quarters, phase_newcomer, on='quarter', how='left'
    ).fillna({'growth_phase': phase, 'newcomer_count': 0})

    # Get the y values for plotting
    attrition_y = phase_attrition_all_quarters['attrition_count'].values
    newcomer_y = phase_newcomer_all_quarters['newcomer_count'].values
    
    # Create the plot
    fig, ax1 = plt.subplots(figsize=(8, 6))
    color1 = 'tab:red'
    color2 = 'tab:blue'
    
    # Plot main trend lines
    line1, = ax1.plot(x_idx, attrition_y, marker='o', color=color1, linewidth=2, label='Disengagement')
    line2, = ax1.plot(x_idx, newcomer_y, marker='s', color=color2, linewidth=2, linestyle='--', label='New Contributors')
    
    # Set up x-axis
    ax1.set_xticks(x_idx)
    ax1.set_xticklabels(quarter_strs, rotation=90, fontsize=10)
    ax1.set_xlim(-0.5, len(quarters) - 0.5)
    
    # Calculate overall ratio
    total_attrition = attrition_y.sum()
    total_newcomers = newcomer_y.sum()
    ratio = total_newcomers / total_attrition if total_attrition > 0 else 0
    
    # Set up y-axis
    y_max = max(attrition_y.max(), newcomer_y.max())
    ax1.set_ylim(0, y_max * 1.1)
    
    # Add title and legend
    # plt.title(f'Core Newcomers vs. Attritions in {phase.capitalize()} Growth Phase\nRatio (Newcomers/Disengagements): {ratio:.2f}', fontsize=20)
    ax1.legend([line1, line2], ['Disengagement', 'Engagement'], loc='upper left', fontsize=20)
    
    plt.tight_layout()
    plt.savefig(f'../figures/growth_phase_comparison/{phase}_turnover.pdf', bbox_inches='tight')
    plt.show()

print(f"Generated comparison charts for the different growth phases")



# attritions with growth phase
attrition_growth_phase = pd.read_csv('../data/combined_attrition_growth_phases.csv')
attrition_growth_phase

# repo_info with create_time, mainLanguage, and duration
repo_info = pd.read_csv('../data/sample_projects_total.csv')
repo_info

project_names = pd.read_csv('../result/repo_name_list.csv')['repo_name'].tolist()
project_names

attritions = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')

global_min_time = pd.Timestamp('2010-08-30 00:00:00')

attritions['standardized_time_weeks'] = (
    (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
).astype(int)

attritions = pd.read_csv('../result/attritions.csv')
attritions

attritions['standardized_time_weeks'] = (
    (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
).astype(int)



# Merge attritions with repo_info to get the mainLanguage for each repo
attritions_with_language = pd.merge(
    attritions, 
    repo_info[['name', 'mainLanguage']], 
    left_on='repo_name', 
    right_on='name', 
    how='left'
)

# Drop the redundant name column
attritions_with_language.drop('name', axis=1, inplace=True)

# Update the original attritions DataFrame with the mainLanguage
attritions = attritions_with_language

# Count attritions by programming language
language_attrition_counts = attritions.groupby('mainLanguage').size().sort_values(ascending=False)

# Show the top 15 languages with most attritions
top_languages = language_attrition_counts.head(15)
print(f"Top 15 programming languages by attrition count:\n{top_languages}")

# Calculate percentage of attritions by language
language_attrition_percentage = (language_attrition_counts / language_attrition_counts.sum() * 100).round(2)

# Visualize attritions by programming language
plt.figure(figsize=(12, 6))
top_languages.plot(kind='bar')
plt.title('Number of Core Developer Attritions by Programming Language', fontsize=14)
plt.xlabel('Programming Language', fontsize=12)
plt.ylabel('Number of Attritions', fontsize=12)
plt.xticks(rotation=45)
plt.tight_layout()

# Save the updated attritions DataFrame with mainLanguage for future use
attritions.to_csv('../result/attritions_with_language.csv', index=False)

attritions_with_language

import pandas as pd
# only maintain newcomer list that are core developers
core_developer_list =  pd.read_csv('../data/core_developer_list_total_repo.csv')
core_developer_list['core_developers'] = core_developer_list['core_developers'].apply(lambda x: x[1:-1].replace("'", "").split(', '))
core_developer_list = core_developer_list.explode('core_developers')
core_developer_list['core_developers'] = core_developer_list['core_developers'].str.strip()
core_developer_list
newcomer_contributors = pd.read_csv('../data/newcomer_contributors.csv')
newcomer_contributors['date'] = pd.to_datetime(newcomer_contributors['date'])
newcomer_contributors = newcomer_contributors[newcomer_contributors['login'].isin(core_developer_list['core_developers'])]




# Merge newcomer_contributors with repo_info to get the mainLanguage information
newcomer_contributors_with_language = pd.merge(
    newcomer_contributors,
    repo_info[['name', 'mainLanguage']],
    left_on='repo_name',
    right_on='name',
    how='left'
)

# Drop the redundant name column
newcomer_contributors_with_language.drop('name', axis=1, inplace=True)

# Check if there are any missing values in mainLanguage after merging
missing_languages = newcomer_contributors_with_language['mainLanguage'].isna().sum()
print(f"Number of newcomers with missing language information: {missing_languages}")


newcomer_contributors_with_language
attritions_with_language

import pandas as pd
import seaborn as sns
import numpy as np
from matplotlib.dates import YearLocator, DateFormatter
import os

import matplotlib.pyplot as plt

# Create output directory if it doesn't exist
os.makedirs('../figures/language_comparison', exist_ok=True)

# Set seaborn style for better aesthetics
sns.set(style="whitegrid")

# Filter data to our time range
attritions['attrition_date'] = pd.to_datetime(attritions['attrition_date'])
attritions['quarter'] = pd.PeriodIndex(attritions['attrition_date'], freq='Q')
attritions_quarterly = attritions[(attritions['attrition_date'] >= '2011-01-01') & 
                                 (attritions['attrition_date'] <= '2023-12-31')]

# Prepare newcomer data
newcomer_contributors['date'] = pd.to_datetime(newcomer_contributors['date'])
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")
newcomers_quarterly = newcomer_contributors[(newcomer_contributors['date'] >= '2011-01-01') & 
                                           (newcomer_contributors['date'] <= '2023-12-31')]

# Group by language and quarter
attrition_by_lang_quarterly = attritions_quarterly.groupby(['mainLanguage', 'quarter']).size().reset_index(name='attrition_count')
newcomer_by_lang_quarterly = newcomers_quarterly.groupby(['mainLanguage', 'quarter']).size().reset_index(name='newcomer_count')

# Ensure all quarters are present for all languages
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
all_quarters = pd.DataFrame(quarters, columns=['quarter'])

# Generate plots for each top language
for language in top_languages:
    # Filter data for current language
    lang_attrition = attrition_by_lang_quarterly[attrition_by_lang_quarterly['mainLanguage'] == language]
    lang_newcomer = newcomer_by_lang_quarterly[newcomer_by_lang_quarterly['mainLanguage'] == language]
    
    # Ensure all quarters are included
    lang_attrition_all_quarters = pd.merge(all_quarters, lang_attrition, on='quarter', how='left').fillna(
        {'mainLanguage': language, 'attrition_count': 0})
    lang_newcomer_all_quarters = pd.merge(all_quarters, lang_newcomer, on='quarter', how='left').fillna(
        {'mainLanguage': language, 'newcomer_count': 0})
    
    # Convert period to datetime for plotting
    lang_attrition_all_quarters['quarter_datetime'] = lang_attrition_all_quarters['quarter'].dt.to_timestamp()
    lang_newcomer_all_quarters['quarter_datetime'] = lang_newcomer_all_quarters['quarter'].dt.to_timestamp()
    
    # Create figure
    fig, ax1 = plt.subplots(figsize=(12, 7))
    
    # Plot attrition trend
    color = 'tab:red'
    ax1.set_xlabel('Quarter', fontsize=14)
    ax1.set_ylabel('Core Developer Disengagement Count', color=color, fontsize=14)
    ax1.plot(lang_attrition_all_quarters['quarter_datetime'], 
             lang_attrition_all_quarters['attrition_count'], 
             marker='o', color=color, linewidth=2, label='Disengagement')
    ax1.tick_params(axis='y', labelcolor=color)
    
    # Create second y-axis
    ax2 = ax1.twinx()
    color = 'tab:blue'
    ax2.set_ylabel('New Core Developer Count', color=color, fontsize=14)
    ax2.bar(lang_newcomer_all_quarters['quarter_datetime'], 
            lang_newcomer_all_quarters['newcomer_count'], 
            alpha=0.5, color=color, width=60, label='New Contributors')
    ax2.tick_params(axis='y', labelcolor=color)
    
    # Format x-axis
    ax1.xaxis.set_major_locator(YearLocator())
    ax1.xaxis.set_major_formatter(DateFormatter('%Y'))
    plt.xticks(rotation=45)
    
    # Add title and legend
    plt.title(f'Core Developer Turnover for {language} (2011-2023)', fontsize=16)
    
    # Combine legends
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=12)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save figure
    plt.savefig(f'../figures/language_comparison/{language}_turnover.pdf', bbox_inches='tight')
    # plt.close()
    plt.show()

print(f"Generated comparison charts for {len(top_languages)} programming languages")

import pandas as pd
import seaborn as sns
import numpy as np
from matplotlib.dates import YearLocator, DateFormatter

import matplotlib.pyplot as plt

# Set seaborn style
sns.set(style="whitegrid")

# Prepare attrition data by language
attritions['attrition_date'] = pd.to_datetime(attritions['attrition_date'])
attritions['year'] = attritions['attrition_date'].dt.year
attrition_by_lang = attritions.groupby(['mainLanguage', 'year']).size().reset_index(name='attrition_count')

# Prepare newcomer data by language
newcomer_contributors['year'] = newcomer_contributors['date'].dt.year
newcomer_by_lang = newcomer_contributors.groupby(['mainLanguage', 'year']).size().reset_index(name='newcomer_count')

# Get top languages to analyze
top_languages = language_attrition_counts.head(8).index.tolist()

# Create a figure with subplots
fig, axes = plt.subplots(4, 2, figsize=(20, 24))
axes = axes.flatten()

# Plot for each top language
for i, language in enumerate(top_languages):
    ax = axes[i]
    
    # Filter data for current language
    lang_attrition = attrition_by_lang[attrition_by_lang['mainLanguage'] == language]
    lang_newcomer = newcomer_by_lang[newcomer_by_lang['mainLanguage'] == language]
    
    # Create DataFrame for plotting
    plot_data = pd.DataFrame({
        'Year': lang_attrition['year'].tolist() + lang_newcomer['year'].tolist(),
        'Count': lang_attrition['attrition_count'].tolist() + lang_newcomer['newcomer_count'].tolist(),
        'Type': ['Disengagement'] * len(lang_attrition) + ['New Contributors'] * len(lang_newcomer)
    })
    
    # Create violin plot
    sns.violinplot(x='Year', y='Count', hue='Type', data=plot_data, 
                  split=True, inner="quart", linewidth=1, palette={"Disengagement": "r", "New Contributors": "b"},
                  ax=ax)
    
    # Set title and labels
    ax.set_title(f"{language}", fontsize=18)
    ax.set_xlabel("Year", fontsize=14)
    ax.set_ylabel("Count", fontsize=14)
    
    # Format x-axis
    ax.tick_params(axis='x', rotation=45, labelsize=12)
    ax.tick_params(axis='y', labelsize=12)
    
    # Remove legend for all but first plot
    if i != 0:
        ax.get_legend().remove()

# Adjust layout
plt.tight_layout()
plt.subplots_adjust(top=0.95)

# Add a main title
fig.suptitle("Core Developer Turnover by Programming Language (2011-2023)", fontsize=24, y=0.98)

# Add a common legend
handles, labels = axes[0].get_legend_handles_labels()
fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, 0.95), 
           ncol=2, fontsize=16)

# Save figure
plt.savefig('../figures/language_attrition_newcomer_comparison.pdf', bbox_inches='tight')
plt.show()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# 创建quarter字段
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# 按repo_name和quarter统计每个仓库每个季度的新人数量
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# 筛选时间区间
quarters = pd.period_range(start='2010Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]

# 去除每个季度中新人数量为0的repo
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]

# 将quarter转换为字符串以便绘图
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# 计算每个季度的中位数和均值
median_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].median()
mean_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].mean()

# 设置Seaborn主题
sns.set(style="whitegrid")

# 使用seaborn的箱线图绘制不同季度的新人数量分布（以repo_name为单位）
plt.figure(figsize=(14, 6))
sns.boxplot(x='quarter', y='newcomer_count', data=repo_quarter_counts, palette="Set2")
plt.yscale("log")  # 使用对数尺度

# 添加中位数和均值曲线
plt.plot(median_values.index, median_values.values, color='orange', label='Median', linewidth=2, linestyle='--')
plt.plot(mean_values.index, mean_values.values, color='blue', label='Mean', linewidth=2, linestyle='-')

# 图表标题和标签
plt.title('Log-Scaled Distribution of Newcomers per Repo by Quarter (No Zero Repos)')
plt.xlabel('Quarter')
plt.ylabel('Newcomer Count per Repo (log scale)')
plt.xticks(rotation=45)
plt.legend()
plt.tight_layout()
plt.show()


# only maintain newcomer list that are core developers
core_developer_list =  pd.read_csv('../data/core_developer_list_total_repo.csv')
core_developer_list['core_developers'] = core_developer_list['core_developers'].apply(lambda x: x[1:-1].replace("'", "").split(', '))
core_developer_list = core_developer_list.explode('core_developers')
core_developer_list['core_developers'] = core_developer_list['core_developers'].str.strip()
core_developer_list
newcomer_contributors = pd.read_csv('../data/newcomer_contributors.csv')
newcomer_contributors['date'] = pd.to_datetime(newcomer_contributors['date'])
newcomer_contributors = newcomer_contributors[newcomer_contributors['login'].isin(core_developer_list['core_developers'])]


newcomer_contributors = newcomer_contributors.reset_index(drop=True)
newcomer_contributors

newcomer_contributors.to_csv('../data/newcomer_contributors_core_developer.csv', index=False)

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# 创建quarter字段
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# 按repo_name和quarter统计每个仓库每个季度的新人数量
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# 筛选时间区间
quarters = pd.period_range(start='2010Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]

# 去除每个季度中新人数量为0的repo
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]

# 将quarter转换为字符串以便绘图
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# 计算每个季度的中位数、均值和总和
mean_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].mean()
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()

# 设置Seaborn主题
sns.set(style="whitegrid")

# 创建图形和轴
fig, ax1 = plt.subplots(figsize=(14, 10))

# 绘制箱线图（新人数量）
sns.boxplot(x='quarter', y='newcomer_count', data=repo_quarter_counts, palette="Set2", ax=ax1)
ax1.set_ylabel('Newcomer Count per Repo', fontsize=20)

# 添加均值曲线
ax1.plot(mean_values.index, mean_values.values, color='blue', label='Mean', linewidth=2, linestyle='-')

# 创建第二个y轴用于绘制总和（sum）
ax2 = ax1.twinx()

# 绘制总和（每个季度所有仓库的总新人数量）
ax2.plot(total_values.index, total_values.values, color='red', label='Sum of Newcomer', linewidth=2, linestyle='--')

# 设置第二个y轴标签
ax2.set_ylabel('Sum of Newcomer', fontsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=20)
ax2.legend(loc='upper right', fontsize=20)

# 设置x轴标签和旋转
plt.title('Distribution of Newcomers per Repo by Quarter', fontsize=20)
plt.xticks(rotation=45, fontsize=20)
ax1.set_xlabel('Quarter', fontsize=20, rotation=45)

# 调整布局
plt.tight_layout()
plt.show()


import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# 创建quarter字段
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# 按repo_name和quarter统计每个仓库每个季度的新人数量
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# 筛选时间区间
quarters = pd.period_range(start='2010Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]

# 去除每个季度中新人数量为0的repo
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]

# 将quarter转换为字符串以便绘图
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# 计算每个季度的中位数、均值和总和
mean_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].mean()
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()

# 设置Seaborn主题
sns.set(style="whitegrid")

# 创建图形和轴
fig, ax1 = plt.subplots(figsize=(14, 10))

# 绘制箱线图（新人数量）
sns.boxplot(x='quarter', y='newcomer_count', data=repo_quarter_counts, palette="Set2", ax=ax1)
ax1.set_ylabel('Newcomer Count per Repo', fontsize=14)

# 添加均值曲线
ax1.plot(mean_values.index, mean_values.values, color='blue', label='Mean', linewidth=2, linestyle='-')

# 创建第二个y轴用于绘制总和（sum）
ax2 = ax1.twinx()

# 绘制总和（每个季度所有仓库的总新人数量）
ax2.plot(total_values.index, total_values.values, color='red', label='Sum', linewidth=2, linestyle='--')

# 设置第二个y轴标签
ax2.set_ylabel('Total Newcomer Count (Sum)', fontsize=20)

# 添加图例
ax1.legend(loc='upper left', fontsize=20)
ax2.legend(loc='upper right', fontsize=20)

# 设置x轴标签和旋转
ax1.set_xticklabels(ax1.get_xticklabels(), rotation=60,fontsize=16)

# 图表标题

# 调整布局
plt.tight_layout()
plt.show()


import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# 创建quarter字段
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# 按repo_name和quarter统计每个仓库每个季度的新人数量
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# 筛选时间区间
quarters = pd.period_range(start='2010Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]

# 去除每个季度中新人数量为0的repo
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]

# 将quarter转换为字符串以便绘图
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# 设置Seaborn主题
sns.set(style="whitegrid")

# 绘制小提琴图（Violin Plot）
plt.figure(figsize=(14, 10))
sns.violinplot(x='quarter', y='newcomer_count', data=repo_quarter_counts, palette="Set2")

# 图表标题和标签
plt.title('Violin Plot of Newcomers per Repo by Quarter', fontsize=16)
plt.xlabel('Quarter', fontsize=14)
plt.ylabel('Newcomer Count per Repo', fontsize=14)
plt.xticks(rotation=45, fontsize=12)

plt.tight_layout()
plt.show()


repo_quarter_counts

newcomer_contributors

core_developer_list =  pd.read_csv('../data/core_developer_list_total_repo.csv')

attritions

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# 创建quarter字段
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# 按repo_name和quarter统计每个仓库每个季度的新人数量
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# 筛选时间区间
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]

# 去除每个季度中新人数量为0的repo
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]

# 将quarter转换为字符串以便绘图
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# 计算每个季度的均值和总和
mean_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].mean()
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()

# 设置Seaborn主题
sns.set(style="whitegrid")

# 创建图形和轴
fig, ax1 = plt.subplots(figsize=(12, 5))

# 绘制小提琴图
sns.violinplot(
    x='quarter',
    y='newcomer_count',
    data=repo_quarter_counts,
    palette="Set2",
    ax=ax1,
    cut=0,
    scale='width',
    inner=None
)
ax1.set_ylabel('Newcomer Per Repo', fontsize=20)
ax1.set_yticklabels(ax1.get_yticklabels(), fontsize=16, rotation=90)

# 确保主轴 y 范围为 0-120
# ax1.set_ylim(0, 120)

# 创建第二个y轴用于绘制总和（sum）
ax2 = ax1.twinx()
ax1.grid(True, linestyle='--', alpha=0.7)
ax2.plot(
    total_values.index,
    total_values.values,
    color='red',
    label='Sum of Quarterly New Core Contributors',
    linewidth=2,
    linestyle='--'
)
ax2.set_ylabel('Total Newcomer Count', fontsize=20)
ax2.set_yticklabels(ax2.get_yticklabels(), fontsize=16, rotation=90)

# 确保第二个 y 轴范围为 0-4000
# ax2.set_ylim(0, 4000)

# 图例处理：合并两轴图例
lines_1, labels_1 = ax1.get_legend_handles_labels()
lines_2, labels_2 = ax2.get_legend_handles_labels()
ax1.legend(lines_1 + lines_2, labels_1 + labels_2, loc='upper left', fontsize=16)

# 设置x轴样式
ax1.set_xticklabels(ax1.get_xticklabels(), rotation=60, fontsize=14)

# 去除水平方向左右多出来的空白位置
ax1.set_xlim(-0.5, len(repo_quarter_counts['quarter'].unique()) - 0.5)

plt.tight_layout()
plt.savefig('../figures/newcomer_per_repo_by_quarter.pdf', bbox_inches='tight')
plt.show()


import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Set style parameters for better visualization
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = [15, 8]
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12

# Create quarter field
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# Calculate quarterly statistics
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# Filter time range
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# Calculate totals
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()
total_values = total_values[total_values > 0]

# Create figure with custom style
fig, ax1 = plt.subplots(figsize=(12, 5))
fig.patch.set_facecolor('white')
ax1.set_facecolor('#f8f9fa')

# Create violin plot with enhanced styling
violin = sns.violinplot(
    x='quarter',
    y='newcomer_count',
    data=repo_quarter_counts,
    palette="husl",
    ax=ax1,
    cut=0,
    scale='width',
    inner=None,
    alpha=0.7
)

# Apply log scale and customize y-axis
ax1.set_yscale('log')
ax1.set_ylabel('Newcomer Per Repo (log)', fontsize=14, fontweight='bold')
ax1.tick_params(axis='y', labelsize=12)

# Create and customize second y-axis
ax2 = ax1.twinx()
ax1.grid(True, linestyle='--', alpha=0.3, which="both", axis="y")
ax1.grid(True, linestyle='--', alpha=0.3, axis="x")

# Plot total values with enhanced styling
if not total_values.empty:
    line = ax2.plot(
        total_values.index,
        total_values.values,
        color='#e63946',
        label='Sum of Quarterly New Core Contributors',
        linewidth=2.5,
        linestyle='-',
        alpha=0.8
    )
    ax2.set_ylabel('Total Newcomer Count', fontsize=14, fontweight='bold')
    ax2.tick_params(axis='y', labelsize=12)

# Customize x-axis labels
unique_quarters = repo_quarter_counts['quarter'].unique()
tick_positions = np.arange(len(unique_quarters))
ax1.set_xticks(tick_positions)

# Dynamic x-axis label adjustment
num_quarters = len(unique_quarters)
if num_quarters > 20:
    step = num_quarters // 10
    ax1.set_xticklabels(
        [q if i % step == 0 else "" for i, q in enumerate(unique_quarters)],
        rotation=45,
        ha="right",
        fontsize=12
    )
else:
    ax1.set_xticklabels(unique_quarters, rotation=45, ha="right", fontsize=12)

# Remove x-axis label
ax1.set_xlabel('')

# Add legend with custom styling
if not total_values.empty:
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    ax1.legend(
        lines_1 + lines_2,
        labels_1 + labels_2,
        loc='upper left',
        fontsize=12,
        frameon=True,
        facecolor='white',
        edgecolor='gray',
        framealpha=0.8
    )

# Adjust layout and save
ax1.set_xlim(-0.5, len(unique_quarters) - 0.5)
plt.tight_layout()
plt.savefig('../figures/newcomer_per_repo_by_quarter_log_scale.pdf', 
            bbox_inches='tight',
            dpi=300,
            facecolor='white',
            edgecolor='none')
plt.show()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# 创建quarter字段
# newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")
# attritions = pd.read_csv('../data/attritions.csv')
attritions['year_month'] = pd.to_datetime(attritions['attrition_date'].str[:7])
filtered_attritions = attritions[(attritions['year_month'] >= '2011-01') & (attritions['year_month'] < '2024-01')]

# Convert monthly to quarterly data
filtered_attritions['quarter'] = pd.PeriodIndex(filtered_attritions['year_month'], freq='Q')
# 按repo_name和quarter统计每个仓库每个季度的新人数量
repo_quarter_counts = filtered_attritions.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# 筛选时间区间
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]

# 去除每个季度中新人数量为0的repo
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]

# 将quarter转换为字符串以便绘图
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# 计算每个季度的均值和总和
mean_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].mean()
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()

# 设置Seaborn主题
sns.set(style="whitegrid")

# 创建图形和轴
fig, ax1 = plt.subplots(figsize=(12, 5))

# 使用小提琴图代替箱线图
sns.violinplot(x='quarter', y='newcomer_count', data=repo_quarter_counts, palette="Set2", ax=ax1, cut=0, scale='width', inner=None)
ax1.set_ylabel('Left Count per Repo', fontsize=20)
ax1.set_yticklabels(ax1.get_yticklabels(), fontsize=16, rotation=90)

# 添加均值曲线

# 创建第二个y轴用于绘制总和（sum）
ax2 = ax1.twinx()
# set ax grid
ax1.grid(True, linestyle='--', alpha=0.7)
ax2.plot(total_values.index, total_values.values, color='red', label='Sum of Quarterly Left Core Contributors', linewidth=2, linestyle='--')
ax2.set_ylabel('Total Left Count (Sum)', fontsize=20)
ax2.set_yticklabels(ax2.get_yticklabels(), fontsize=12, rotation=90)
# 设定ax1.y轴范围

# 图例处理：合并两轴图例
lines_1, labels_1 = ax1.get_legend_handles_labels()
lines_2, labels_2 = ax2.get_legend_handles_labels()
ax1.legend(lines_1 + lines_2, labels_1 + labels_2, loc='upper left', fontsize=20)

# 设置x轴样式
ax1.set_xticklabels(ax1.get_xticklabels(), rotation=60, fontsize=14)

# 去除水平方向左右多出来的空白位置
ax1.set_xlim(-0.5, len(repo_quarter_counts['quarter'].unique()) - 0.5)
# ax1.set_ylim(0, 140)
# ax2.set_ylim(0, 4000)
plt.tight_layout()
plt.savefig('../figures/left_per_repo_by_quarter.pdf', bbox_inches='tight')
plt.show()


import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Set style parameters for better visualization
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = [15, 8]
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12

# Process attrition data
attritions['year_month'] = pd.to_datetime(attritions['attrition_date'].str[:7])
filtered_attritions = attritions[(attritions['year_month'] >= '2011-01') & (attritions['year_month'] < '2024-01')]

# Convert monthly to quarterly data
filtered_attritions['quarter'] = pd.PeriodIndex(filtered_attritions['year_month'], freq='Q')
repo_quarter_counts = filtered_attritions.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# Filter time range
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# Calculate totals
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()
total_values = total_values[total_values > 0]

# Create figure with custom style
fig, ax1 = plt.subplots(figsize=(12, 5))
fig.patch.set_facecolor('white')
ax1.set_facecolor('#f8f9fa')

# Create violin plot with enhanced styling
violin = sns.violinplot(
    x='quarter',
    y='newcomer_count',
    data=repo_quarter_counts,
    palette="husl",
    ax=ax1,
    cut=0,
    scale='width',
    inner=None
)

# Set log scale for y-axis
ax1.set_yscale('log')
ax1.set_ylabel('Left Count per Repo (Log Scale)')
ax1.tick_params(axis='y', labelsize=12)

# Create second y-axis for total values
ax2 = ax1.twinx()

# Add grid lines
ax1.grid(True, linestyle='--', alpha=0.7, which="both", axis="y")
ax1.grid(True, linestyle='--', alpha=0.4, axis="x")

# Plot total values if available
if not total_values.empty:
    ax2.plot(
        total_values.index,
        total_values.values,
        color='red',
        label='Sum of Quarterly Left Core Contributors',
        linewidth=2,
        linestyle='--'
    )
    ax2.set_ylabel('Total Left Count')
    ax2.tick_params(axis='y', labelsize=12)
else:
    ax2.set_yticks([])
    ax2.set_yticklabels([])
    ax2.set_ylabel('')

# Handle legend
if not total_values.empty:
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    ax1.legend(
        lines_1 + lines_2,
        labels_1 + labels_2,
        loc='upper left',
        fontsize=12,
        frameon=True,
        facecolor='white',
        edgecolor='gray',
        framealpha=0.8
    )

# Set x-axis style
unique_quarters = repo_quarter_counts['quarter'].unique()
tick_positions = np.arange(len(unique_quarters))
ax1.set_xticks(tick_positions)

# Dynamic x-axis label adjustment
num_quarters = len(unique_quarters)
if num_quarters > 20:
    step = num_quarters // 10
    ax1.set_xticklabels(
        [q if i % step == 0 else "" for i, q in enumerate(unique_quarters)],
        rotation=45,
        ha="right",
        fontsize=12
    )
else:
    ax1.set_xticklabels(unique_quarters, rotation=45, ha="right", fontsize=12)

# Remove x-axis label
ax1.set_xlabel('')

# Adjust layout and save
ax1.set_xlim(-0.5, len(unique_quarters) - 0.5)
plt.tight_layout()
plt.savefig('../figures/left_per_repo_by_quarter_log_scale.pdf', 
            bbox_inches='tight',
            dpi=300,
            facecolor='white',
            edgecolor='none')
plt.show()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Set style parameters for better visualization
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = [15, 8]
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12

# --- 假设 'attritions' DataFrame 已经加载 ---
# 示例数据创建 (如果需要独立运行，请替换为您的数据加载方式)
# attritions = pd.DataFrame({
# 'attrition_date': pd.to_datetime(['2011-01-15', '2011-01-20', '2011-04-10', '2023-12-05'] * 100),
# 'repo_name': [f'repo_{i%5}' for i in range(400)]
# })
# attritions['attrition_date'] = attritions['attrition_date'].astype(str)


# Process attrition data
attritions['year_month'] = pd.to_datetime(attritions['attrition_date'].str[:7])
filtered_attritions = attritions[(attritions['year_month'] >= '2011-01') & (attritions['year_month'] < '2024-01')]

# Convert monthly to quarterly data
filtered_attritions['quarter'] = pd.PeriodIndex(filtered_attritions['year_month'], freq='Q')
# 使用 'count' 而非 'newcomer_count' 以匹配标签，或者统一变量名
repo_quarter_counts = filtered_attritions.groupby(['quarter', 'repo_name']).size().reset_index(name='count_per_repo')


# Filter time range
quarters = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters)]
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['count_per_repo'] > 0] # 确保计数大于0
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# Calculate totals
total_values = repo_quarter_counts.groupby('quarter')['count_per_repo'].sum()
total_values = total_values[total_values > 0] # 确保总数大于0

# Create figure with custom style
fig, ax1 = plt.subplots(figsize=(12, 5))
fig.patch.set_facecolor('white')
ax1.set_facecolor('#f8f9fa')

# Create violin plot with enhanced styling
if not repo_quarter_counts.empty:
    violin = sns.violinplot(
        x='quarter',
        y='count_per_repo', # 使用更新后的列名
        data=repo_quarter_counts,
        palette="husl",
        ax=ax1,
        cut=0,
        scale='width',
        inner=None
    )

# Set log scale for y-axis
ax1.set_yscale('log')
ax1.set_ylabel('Left Count per Repo (Log Scale)')
ax1.tick_params(axis='y', labelsize=12)

# Create second y-axis for total values
ax2 = ax1.twinx()
# ax2.set_yscale('log') # <--- 修改: ax2 也设置为对数刻度

# Add grid lines
ax1.grid(True, linestyle='--', alpha=0.7, which="both", axis="y")
ax1.grid(True, linestyle='--', alpha=0.4, axis="x")

# Plot total values if available
if not total_values.empty:
    ax2.plot(
        total_values.index,
        total_values.values,
        color='red',
        label='Sum of Quarterly Left Core Contributors',
        linewidth=2,
        linestyle='--'
    )
    ax2.set_ylabel('Total Left Count (Log Scale)') # <--- 修改: 更新 ax2 标签
    ax2.tick_params(axis='y', labelsize=12)
else:
    ax2.set_yticks([])
    ax2.set_yticklabels([])
    ax2.set_ylabel('')

# --- 修改: 对齐 Y 轴范围 ---
if not repo_quarter_counts.empty and not total_values.empty:
    y_min_ax1_data = repo_quarter_counts['count_per_repo'].min()
    y_max_ax1_data = repo_quarter_counts['count_per_repo'].max()

    y_min_ax2_data = total_values.min()
    y_max_ax2_data = total_values.max()
    
    overall_min_y = min(y_min_ax1_data, y_min_ax2_data)
    overall_max_y = max(y_max_ax1_data, y_max_ax2_data)
    
    # 确保下限大于0 (因为是对数刻度，且数据已过滤)
    final_min_y = max(overall_min_y, 1e-1) # 设置一个非常小的正数作为绝对下限, 或根据数据调整
    if overall_min_y <=0 : # 以防万一有0或负值未被完全过滤
        print("Warning: Data for log scale Y-axis contains non-positive values. Min Y limit adjusted.")
        # 尝试找到数据的最小正值
        positive_min_ax1 = repo_quarter_counts[repo_quarter_counts['count_per_repo'] > 0]['count_per_repo'].min() if not repo_quarter_counts[repo_quarter_counts['count_per_repo'] > 0].empty else np.inf
        positive_min_ax2 = total_values[total_values > 0].min() if not total_values[total_values > 0].empty else np.inf
        actual_data_min = min(positive_min_ax1, positive_min_ax2)
        final_min_y = actual_data_min if actual_data_min != np.inf else 0.1


    final_max_y = overall_max_y

    ax1.set_ylim(bottom=final_min_y, top=final_max_y)
    ax2.set_ylim(bottom=final_min_y, top=final_max_y)
elif not repo_quarter_counts.empty: # 只有 ax1 有数据
    y_min_ax1_data = repo_quarter_counts['count_per_repo'].min()
    y_max_ax1_data = repo_quarter_counts['count_per_repo'].max()
    final_min_y = max(y_min_ax1_data, 1e-1)
    if y_min_ax1_data <= 0:
         positive_min_ax1 = repo_quarter_counts[repo_quarter_counts['count_per_repo'] > 0]['count_per_repo'].min()
         final_min_y = positive_min_ax1 if pd.notna(positive_min_ax1) else 0.1

    ax1.set_ylim(bottom=final_min_y, top=y_max_ax1_data)
# --- 结束修改 ---

# Handle legend
if not total_values.empty or not repo_quarter_counts.empty: # 确保至少有一个图例可以生成
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    if lines_1 or lines_2: # 只有当存在有效的图例项时才创建图例
        ax1.legend(
            lines_1 + lines_2,
            labels_1 + labels_2,
            loc='upper left',
            fontsize=12,
            frameon=True,
            facecolor='white',
            edgecolor='gray',
            framealpha=0.8
        )

# Set x-axis style
if not repo_quarter_counts.empty:
    unique_quarters = repo_quarter_counts['quarter'].unique()
    tick_positions = np.arange(len(unique_quarters))
    ax1.set_xticks(tick_positions)

    num_quarters = len(unique_quarters)
    if num_quarters > 20:
        step = num_quarters // 10
        ax1.set_xticklabels(
            [q if i % step == 0 else "" for i, q in enumerate(unique_quarters)],
            rotation=45,
            ha="right",
            fontsize=12
        )
    else:
        ax1.set_xticklabels(unique_quarters, rotation=45, ha="right", fontsize=12)
    ax1.set_xlim(-0.5, len(unique_quarters) - 0.5)
else: # 如果没有数据，清空x轴
    ax1.set_xticks([])
    ax1.set_xticklabels([])


# Remove x-axis label
ax1.set_xlabel('')

# Adjust layout and save
plt.tight_layout()
# plt.savefig('../figures/left_per_repo_by_quarter_log_scale_aligned.pdf', # 新文件名
#             bbox_inches='tight',
#             dpi=300,
#             facecolor='white',
#             edgecolor='none')
plt.show()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Set style parameters for better visualization
sns.set_palette("husl")
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12

# --- Assume 'attritions' DataFrame is already loaded ---
# Example data for testing (uncomment to run standalone):
# attritions_data = {
#     'attrition_date': pd.to_datetime(
#         ['2011-01-15', '2011-01-20', '2011-04-10', '2012-07-22', '2023-11-01'] * 100 +
#         ['2011-02-10', '2011-02-15', '2011-02-20'] * 70 + # More data for 2011Q1
#         ['2011-07-01'] * 5 + # Data for 2011Q3, which might have sum 0 if all counts are filtered out
#         ['2012-08-01'] * 10 # Modest count for 2012Q3
#     ).strftime('%Y-%m-%d').tolist(),
#     'repo_name': [f'repo_{(i%10)}' for i in range(500 + 210 + 5 + 10)]
# }
# attritions = pd.DataFrame(attritions_data)
# --- End of example data ---

# Process attrition data
attritions['year_month'] = pd.to_datetime(attritions['attrition_date'].str[:7])
filtered_attritions = attritions[(attritions['year_month'] >= '2011-01') & (attritions['year_month'] < '2024-01')]

# Convert monthly to quarterly data
filtered_attritions['quarter'] = pd.PeriodIndex(filtered_attritions['year_month'], freq='Q')
repo_quarter_counts = filtered_attritions.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# Calculate totals
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()
total_values = total_values[total_values > 0] # This means total_values might not have all quarters present in repo_quarter_counts

# Create figure with custom style
fig, ax1 = plt.subplots(figsize=(12, 5))
fig.patch.set_facecolor('white')
ax1.set_facecolor('#f8f9fa')

unique_quarters = [] # Initialize for broader scope
if not repo_quarter_counts.empty:
    unique_quarters = sorted(repo_quarter_counts['quarter'].unique()) # Defines the categories for ax1

# Create box plot with variable width on ax1
if not repo_quarter_counts.empty and unique_quarters:
    data_for_plot = [
        repo_quarter_counts[repo_quarter_counts['quarter'] == q]['newcomer_count'].values
        for q in unique_quarters
    ]

    widths_for_plot = []
    max_total_overall = total_values.max() if not total_values.empty else 0
    desired_max_plot_width = 0.85
    min_plot_width_threshold = desired_max_plot_width * 0.1

    for q_str in unique_quarters:
        current_quarter_total_for_width = total_values.get(q_str, 0)
        if current_quarter_total_for_width <= 0: # Fallback if not in total_values (e.g. sum was 0)
            q_data_list = repo_quarter_counts[repo_quarter_counts['quarter'] == q_str]['newcomer_count']
            current_quarter_total_for_width = q_data_list.sum() if not q_data_list.empty else 0
        
        if max_total_overall > 0 and current_quarter_total_for_width > 0:
            box_w = (current_quarter_total_for_width / max_total_overall) * desired_max_plot_width
            box_w = max(box_w, min_plot_width_threshold)
            box_w = min(box_w, desired_max_plot_width)
        elif current_quarter_total_for_width > 0:
            box_w = desired_max_plot_width * 0.5
        else:
            box_w = 0.01
        widths_for_plot.append(box_w)

    bp = ax1.boxplot(
        data_for_plot, widths=widths_for_plot, patch_artist=True,
        positions=np.arange(len(unique_quarters)), manage_ticks=False,
        showfliers=True, medianprops=dict(color='black', linewidth=1.5)
    )

    box_colors = sns.color_palette("husl", n_colors=len(unique_quarters))
    for i, box in enumerate(bp['boxes']):
        box.set_facecolor(box_colors[i % len(box_colors)])
        box.set_alpha(0.7); box.set_edgecolor('gray')
    for whisker in bp['whiskers']: whisker.set(color='dimgray', linewidth=1, linestyle='-')
    for cap in bp['caps']: cap.set(color='dimgray', linewidth=1)

    ax1.set_yscale('linear')
    ax1.set_ylabel('Disengaged Count per Repo', fontsize=16)
    if any(len(d) > 0 for d in data_for_plot):
        max_y_val = max(d.max() for d in data_for_plot if len(d) > 0)
        ax1.set_ylim(0, max_y_val * 1.1 if max_y_val > 0 else 10)
    else:
        ax1.set_ylim(0, 10)
    ax1.tick_params(axis='y', labelsize=12)
else:
    ax1.set_yscale('linear'); ax1.set_ylabel('Disengaged Count per Repo', fontsize=16)
    ax1.set_ylim(0, 10)

# Create second y-axis for total values
ax2 = ax1.twinx()
ax1.grid(True, linestyle='--', alpha=0.7, which="major", axis="y")
ax1.grid(True, linestyle='--', alpha=0.4, axis="x")

# Plot total values (sum line) on ax2, ensuring correct alignment
if not total_values.empty and unique_quarters: # Ensure unique_quarters is available
    sum_line_x_coords = []
    sum_line_y_coords = []
    
    # Create a mapping from quarter string to its numerical position on ax1
    quarter_to_xpos = {q_str: i for i, q_str in enumerate(unique_quarters)}
    
    # Iterate through unique_quarters to define the line's points.
    # This ensures the line connects points in the order they appear on the x-axis
    # and correctly handles missing data in total_values (line will have gaps).
    # To draw a continuous line connecting available points, we collect points and plot them sorted.
    
    temp_sum_points = {} # Using dict to store {x_pos: y_val}
    for q_str_from_total_values, sum_val in total_values.items():
        if q_str_from_total_values in quarter_to_xpos:
            x_position = quarter_to_xpos[q_str_from_total_values]
            temp_sum_points[x_position] = sum_val
            
    if temp_sum_points:
        # Sort the collected points by x-coordinate before plotting
        sorted_x_positions = sorted(temp_sum_points.keys())
        sum_line_x_coords = sorted_x_positions
        sum_line_y_coords = [temp_sum_points[x] for x in sorted_x_positions]

        ax2.plot(
            sum_line_x_coords,
            sum_line_y_coords,
            color='red',
            label='Sum of Quarterly Disengaged Core Contributors', # Single label for the sum line
            linewidth=2.5,
            linestyle='--'
        )
        
    ax2.set_ylabel('Total Disengagement Count', fontsize=16)
    ax2.tick_params(axis='y', labelsize=12)
    ax2.set_ylim(bottom=0, top=max(total_values.max() * 1.1 if not total_values.empty else 0, 100))
else:
    ax2.set_yticks([]); ax2.set_yticklabels([]); ax2.set_ylabel('')
    ax2.set_ylim(bottom=0, top=4000) # As per original script's else

# Handle legend (should only pick up the label from ax2.plot)
lines_1, labels_1 = ax1.get_legend_handles_labels() # Should be empty
lines_2, labels_2 = ax2.get_legend_handles_labels() # Should have one entry
if lines_1 or lines_2:
    ax1.legend(lines_1 + lines_2, labels_1 + labels_2, loc='upper left', fontsize=12,
               frameon=True, facecolor='white', edgecolor='gray', framealpha=0.8)

# Set x-axis style
if unique_quarters: # Check if unique_quarters was populated
    tick_positions = np.arange(len(unique_quarters))
    ax1.set_xticks(tick_positions)
    num_quarters_len = len(unique_quarters)
    if num_quarters_len > 20:
        step = num_quarters_len // 10 if num_quarters_len // 10 > 0 else 1
        ax1.set_xticklabels(
            [q if i % step == 0 else "" for i, q in enumerate(unique_quarters)],
            rotation=45, ha="right", fontsize=12)
    else:
        ax1.set_xticklabels(unique_quarters, rotation=45, ha="right", fontsize=12)
    ax1.set_xlim(-0.5, len(unique_quarters) - 0.5)
else:
    ax1.set_xticks([]); ax1.set_xticklabels([])

ax1.set_xlabel('')
plt.tight_layout()
plt.savefig('../figures/left_per_repo_by_quarter_boxplot.pdf',
            bbox_inches='tight', dpi=300, facecolor='white', edgecolor='none')
plt.show()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Set style parameters for better visualization
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12

# --- Assume 'attritions' DataFrame is already loaded ---

# Process attrition data
attritions['year_month'] = pd.to_datetime(attritions['attrition_date'].str[:7])
filtered_attritions = attritions[(attritions['year_month'] >= '2011-01') & (attritions['year_month'] < '2024-01')]

# Convert monthly to quarterly data
filtered_attritions['quarter'] = pd.PeriodIndex(filtered_attritions['year_month'], freq='Q')
repo_quarter_counts = filtered_attritions.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# Calculate totals for the sum line on ax2
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()
total_values = total_values[total_values > 0]

# Create figure with custom style
fig, ax1 = plt.subplots(figsize=(12, 5))
fig.patch.set_facecolor('white')
ax1.set_facecolor('#f8f9fa')

unique_quarters = []
if not repo_quarter_counts.empty:
    unique_quarters = sorted(repo_quarter_counts['quarter'].unique())

# Create scatter plot on ax1
if not repo_quarter_counts.empty and unique_quarters:
    quarter_to_xpos = {q_str: i for i, q_str in enumerate(unique_quarters)}
    
    scatter_plot_df = repo_quarter_counts.copy()
    scatter_plot_df['x_pos'] = scatter_plot_df['quarter'].map(quarter_to_xpos)
    scatter_plot_df.dropna(subset=['x_pos'], inplace=True)
    if not scatter_plot_df.empty:
      scatter_plot_df['x_pos'] = scatter_plot_df['x_pos'].astype(int)

    if not scatter_plot_df.empty:
        point_color = sns.color_palette("muted")[0] 

        sns.scatterplot(
            x='x_pos',
            y='newcomer_count',
            data=scatter_plot_df,
            color=point_color,
            ax=ax1,
            alpha=0.3,
            s=30,
            edgecolor='none'
        )

    ax1.set_yscale('linear')
    ax1.set_ylabel('Disengaged Count per Repo', fontsize=16)
    if not scatter_plot_df.empty:
        max_y_val = scatter_plot_df['newcomer_count'].max()
        ax1.set_ylim(bottom=0, top=150)
    else:
        ax1.set_ylim(0, 10)
    ax1.tick_params(axis='y', labelsize=12)
else:
    ax1.set_yscale('linear'); ax1.set_ylabel('Disengaged Count per Repo', fontsize=16)
    ax1.set_ylim(0, 10)

# Create second y-axis for total values
ax2 = ax1.twinx()
ax1.grid(True, linestyle='--', alpha=0.7, which="major", axis="y")
ax1.grid(True, linestyle='--', alpha=0.4, axis="x")

# Plot total values (sum line) on ax2
if not total_values.empty and unique_quarters:
    sum_line_x_coords = []
    sum_line_y_coords = []
    quarter_to_xpos_map_for_sum = {q_str: i for i, q_str in enumerate(unique_quarters)}
    
    temp_sum_points = {}
    for q_str_from_total_values, sum_val in total_values.items():
        if q_str_from_total_values in quarter_to_xpos_map_for_sum:
            x_position = quarter_to_xpos_map_for_sum[q_str_from_total_values]
            temp_sum_points[x_position] = sum_val
            
    if temp_sum_points:
        sorted_x_positions = sorted(temp_sum_points.keys())
        sum_line_x_coords = sorted_x_positions
        sum_line_y_coords = [temp_sum_points[x] for x in sorted_x_positions]

        ax2.plot(
            sum_line_x_coords, sum_line_y_coords,
            color='red', label='Sum of Quarterly Disengaged Core Contributors',
            linewidth=2.5, linestyle='--'
        )
        
    ax2.set_ylabel('Total Disengagement Count', fontsize=16)
    ax2.tick_params(axis='y', labelsize=12)
    ax2.set_ylim(bottom=0, top=4000)  # 固定上限为4000
else:
    ax2.set_yticks([]); ax2.set_yticklabels([]); ax2.set_ylabel('')
    ax2.set_ylim(bottom=0, top=4000)

# Handle legend
lines_1, labels_1 = ax1.get_legend_handles_labels()
lines_2, labels_2 = ax2.get_legend_handles_labels()
if lines_1 or lines_2:
    ax1.legend(lines_1 + lines_2, labels_1 + labels_2, loc='upper left', fontsize=12,
               frameon=True, facecolor='white', edgecolor='gray', framealpha=0.8)

# Set x-axis style
if unique_quarters:
    tick_positions = np.arange(len(unique_quarters))
    ax1.set_xticks(tick_positions)
    num_quarters_len = len(unique_quarters)
    if num_quarters_len > 20:
        step = num_quarters_len // 10 if num_quarters_len // 10 > 0 else 1
        ax1.set_xticklabels(
            [q if i % step == 0 else "" for i, q in enumerate(unique_quarters)],
            rotation=45, ha="right", fontsize=12)
    else:
        ax1.set_xticklabels(unique_quarters, rotation=45, ha="right", fontsize=12)
    ax1.set_xlim(-0.5, len(unique_quarters) - 0.5)
else:
    ax1.set_xticks([]); ax1.set_xticklabels([])

ax1.set_xlabel('')
plt.tight_layout()
plt.savefig('../figures/left_per_repo_by_quarter_scatterplot_density.pdf', 
            bbox_inches='tight', dpi=300, facecolor='white', edgecolor='none')
plt.show()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# Set style parameters for better visualization
# sns.set_palette("husl") # Global palette, will be overridden for the specific scatter plot color
plt.rcParams['figure.figsize'] = [15, 8] # Kept from original new script
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 14
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['xtick.labelsize'] = 12
plt.rcParams['ytick.labelsize'] = 12


# Create quarter field
newcomer_contributors['quarter'] = newcomer_contributors['date'].dt.to_period("Q")

# Calculate quarterly statistics
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')

# Filter time range
quarters_range = pd.period_range(start='2011Q1', end='2023Q4', freq='Q')
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['quarter'].isin(quarters_range)]
repo_quarter_counts = repo_quarter_counts[repo_quarter_counts['newcomer_count'] > 0]
repo_quarter_counts['quarter'] = repo_quarter_counts['quarter'].astype(str)

# Calculate totals for the sum line on ax2
total_values = repo_quarter_counts.groupby('quarter')['newcomer_count'].sum()
total_values = total_values[total_values > 0]

# Create figure with custom style
fig, ax1 = plt.subplots(figsize=(12, 5)) # Using (12,5) as in previous successful example for consistency, original was [15,8]
fig.patch.set_facecolor('white')
ax1.set_facecolor('#f8f9fa')

unique_quarters = []
if not repo_quarter_counts.empty:
    unique_quarters = sorted(repo_quarter_counts['quarter'].unique())

# Create scatter plot on ax1
if not repo_quarter_counts.empty and unique_quarters:
    quarter_to_xpos = {q_str: i for i, q_str in enumerate(unique_quarters)}
    
    scatter_plot_df = repo_quarter_counts.copy()
    scatter_plot_df['x_pos'] = scatter_plot_df['quarter'].map(quarter_to_xpos)
    scatter_plot_df.dropna(subset=['x_pos'], inplace=True) # Defensive
    if not scatter_plot_df.empty:
      scatter_plot_df['x_pos'] = scatter_plot_df['x_pos'].astype(int)

    if not scatter_plot_df.empty:
        # Define a single color for all points
        point_color = sns.color_palette("muted")[0] # Using a soft blue from seaborn's "muted" palette

        sns.scatterplot(
            x='x_pos',
            y='newcomer_count',
            data=scatter_plot_df,
            color=point_color, # Apply the single defined color
            ax=ax1,
            alpha=0.3,        # Transparency for density effect
            s=30,             # Marker size
            edgecolor='none'  # Remove marker edges for smoother density
        )

    # Apply linear scale and customize y-axis (ax1)
    ax1.set_yscale('linear') # Explicitly linear
    ax1.set_ylabel('Engagement Per Repo', fontsize=16) # Removed "(log)"
    ax1.tick_params(axis='y', labelsize=12)
    if not scatter_plot_df.empty:
        max_y_val = scatter_plot_df['newcomer_count'].max()
        ax1.set_ylim(bottom=0, top= 150 ) # Dynamic Y limit for linear scale
    else:
        ax1.set_ylim(0, 10)
else: # repo_quarter_counts was empty
    ax1.set_yscale('linear')
    ax1.set_ylabel('Engagement Per Repo', fontsize=16)
    ax1.set_ylim(0, 10)


# Create and customize second y-axis (ax2 will be linear by default)
ax2 = ax1.twinx()

# Grid lines (using alpha from the new script)
ax1.grid(True, linestyle='--', alpha=0.3, which="major", axis="y") # 'both' might be too dense for linear
ax1.grid(True, linestyle='--', alpha=0.3, axis="x")

# Plot total values (sum line) on ax2, ensuring correct alignment
if not total_values.empty and unique_quarters:
    sum_line_x_coords = []
    sum_line_y_coords = []
    # Ensure quarter_to_xpos_map_for_sum uses the same unique_quarters as ax1
    quarter_to_xpos_map_for_sum = {q_str: i for i, q_str in enumerate(unique_quarters)}
    
    temp_sum_points = {}
    for q_str_from_total_values, sum_val in total_values.items():
        if q_str_from_total_values in quarter_to_xpos_map_for_sum:
            x_position = quarter_to_xpos_map_for_sum[q_str_from_total_values]
            temp_sum_points[x_position] = sum_val
            
    if temp_sum_points:
        sorted_x_positions = sorted(temp_sum_points.keys())
        sum_line_x_coords = sorted_x_positions
        sum_line_y_coords = [temp_sum_points[x] for x in sorted_x_positions]

        ax2.plot(
            sum_line_x_coords, sum_line_y_coords,
            color='red',
            label='Sum of Quarterly Engagement Core Contributors', # Label from this script
            linewidth=2.5,
            linestyle='--'
        )
        
    ax2.set_ylabel('Total Engagement Count', fontsize=16) # Label from this script
    ax2.tick_params(axis='y', labelsize=12)
    # Dynamic top for ax2 based on its data, keeping bottom at 0
    ax2.set_ylim(bottom=0, top=4000) # Adjusted top from fixed 4000
else:
    ax2.set_yticks([])
    ax2.set_yticklabels([])
    ax2.set_ylabel('')
    ax2.set_ylim(bottom=0, top=4000) # Keep original fixed top if total_values is empty


# Customize x-axis labels (logic from the provided new script)
if unique_quarters: # Check if unique_quarters was populated
    tick_positions = np.arange(len(unique_quarters))
    ax1.set_xticks(tick_positions)

    num_quarters = len(unique_quarters)
    if num_quarters > 20:
        step = num_quarters // 10 if num_quarters // 10 > 0 else 1
        ax1.set_xticklabels(
            [q if i % step == 0 else "" for i, q in enumerate(unique_quarters)],
            rotation=45,
            ha="right",
            fontsize=12
        )
    else:
        ax1.set_xticklabels(unique_quarters, rotation=45, ha="right", fontsize=12)
    ax1.set_xlim(-0.5, len(unique_quarters) - 0.5)
else:
    ax1.set_xticks([])
    ax1.set_xticklabels([])


# Remove x-axis label
ax1.set_xlabel('')

# Add legend with custom styling (logic from the provided new script)
# This will pick up the label from ax2.plot()
if not repo_quarter_counts.empty or not total_values.empty: # Check if there's anything to warrant a legend
    lines_1, labels_1 = ax1.get_legend_handles_labels() # ax1 scatter plot has no label for legend
    lines_2, labels_2 = ax2.get_legend_handles_labels() # ax2 line plot has a label
    if lines_1 or lines_2: # If either ax1 or ax2 has legend items
        ax1.legend(
            lines_1 + lines_2,
            labels_1 + labels_2,
            loc='upper left',
            fontsize=12,
            frameon=True,
            facecolor='white',
            edgecolor='gray',
            framealpha=0.8
        )

# Adjust layout and save
plt.tight_layout()
# Suggesting a filename matching the new style and data context
plt.savefig('../figures/newcomer_engagement_per_repo_scatterplot_density.pdf', 
            bbox_inches='tight',
            dpi=300,
            facecolor='white',
            edgecolor='none')
plt.show()

# merge the two dataframes
merged_df = pd.merge(attrition_trend_quarterly, contributor_trend, on=['quarter', 'quarter_datetime'], how='outer')
merged_df.to_csv('../result/attrition_engagement_contributor_trend.csv', index=False)

# get the age of the repo when the attrition happened, first get repo 'createdAt' transformed to datetime, then get the age of the repo when the attrition happened by minus the attrition date with the repo 'createdAt'

# only maintain newcomer list that are core developers
core_developer_list =  pd.read_csv('../data/core_developer_list_total_repo.csv')
core_developer_list['core_developers'] = core_developer_list['core_developers'].apply(lambda x: x[1:-1].replace("'", "").split(', '))
core_developer_list = core_developer_list.explode('core_developers')
core_developer_list['core_developers'] = core_developer_list['core_developers'].str.strip()
core_developer_list
newcomer_contributors = pd.read_csv('../data/newcomer_contributors.csv')
newcomer_contributors['date'] = pd.to_datetime(newcomer_contributors['date'])
newcomer_contributors = newcomer_contributors[newcomer_contributors['login'].isin(core_developer_list['core_developers'])]


newcomer_contributors = newcomer_contributors.reset_index(drop=True)
newcomer_contributors

newcomer_contributors.to_csv('../data/newcomer_contributors_core_developer.csv', index=False)

newcomer_contributors

core_developer_list =  pd.read_csv('../data/core_developer_list_total_repo.csv')

attritions

# merge the two dataframes
merged_df = pd.merge(attrition_trend_quarterly, contributor_trend, on=['quarter', 'quarter_datetime'], how='outer')
merged_df.to_csv('../result/attrition_engagement_contributor_trend.csv', index=False)

# 按照quarter与repo进行分组, 然后根据数量从高到低排序，以方便我进行查看,不存在 Disengagement per repo这个属性！
filtered_attritions

repo_quarter_counts = filtered_attritions.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')
repo_quarter_counts.to_csv('../result/disengagement_per_repo_by_quarter.csv', index=False)
repo_quarter_counts = newcomer_contributors.groupby(['quarter', 'repo_name']).size().reset_index(name='newcomer_count')
repo_quarter_counts.to_csv('../result/engagement_per_repo_by_quarter.csv', index=False)






