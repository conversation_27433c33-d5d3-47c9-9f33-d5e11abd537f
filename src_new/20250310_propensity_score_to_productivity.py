from itertools import product
import pandas as pd
import os
import logging
import numpy as np
from sklearn.neighbors import NearestNeighbors
import statsmodels.api as sm
import matplotlib.pyplot as plt
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)
# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "psm_2025_0310.log")),
        logging.StreamHandler(),
    ],
)

output_dir = "../result/did_result_20250312/"
os.makedirs(output_dir, exist_ok=True)

near_days = 7
burst_gap = 84
window_size = [12]
# window_size = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]
ratio_control_num = 5

def save_data(data, filename):
    """Save data to a CSV file."""
    filepath = os.path.join(output_dir, filename)
    data.to_csv(filepath, index=False)
    logging.info(f"Saved {filepath}")


def sigmoid(x):
    """Sigmoid function."""
    return 1 / (1 + np.exp(-x))

# Read the data
repo_name_list = pd.read_csv('../result/repo_name_list.csv')
repo_info = pd.read_csv('../data/sample_projects_total.csv')
# attritions = pd.read_csv('../result/attritions_updated_add_burst.csv')
productivity = pd.read_csv('../data/2025_0310_productivity.csv')
# productivity = pd.read_csv('../data/2025_0312_productivity_weekly_with_control_variables.csv')
productivity = pd.read_csv('../data/2025_0312_productivity_weekly_with_control_variables_fillna.csv')

productivity["standardized_time_weeks"] = productivity['standardized_time_weeks'].astype(int)


def calculate_rolling_slope_window(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling slope for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the slope for.
        output_column (str): Name of the output column for slopes.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling slope column.
    """
    
    def rolling_slope(df, column, window):
        y = df[column].values
        n = window
        if len(y) < n:
            return pd.Series([np.nan] * len(y), index=df.index)  # If not enough data, return NaN
        
        x = np.arange(1, n + 1)
        sum_x = np.sum(x)
        sum_x2 = np.sum(x ** 2)
        denominator = n * sum_x2 - sum_x ** 2
        
        sum_y = np.convolve(y, np.ones(n), 'valid')
        sum_xy = np.convolve(y, x[::-1], 'valid')
        
        slopes = (n * sum_xy - sum_x * sum_y) / denominator
        return pd.Series([np.nan] * (n - 1) + list(slopes), index=df.index)
    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling slope calculation for each group
    data[output_column] = data.groupby(group_column).apply(
        lambda group: rolling_slope(group, target_column, window_size)
    ).reset_index(level=0, drop=True)
    
    return data

## 2. rolling mean
def calculating_rolling_mean(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling mean for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the mean for.
        output_column (str): Name of the output column for means.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling mean column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling mean calculation for each group
    data[output_column] = data.groupby(group_column)[target_column].transform(
        lambda x: x.rolling(window=window_size, min_periods=window_size).mean()
    )
    
    return data

## 3. rolling rate of change
def calculate_rolling_rate_of_change(data, group_column, target_column, window_size, output_column, sort_column):
    """
    Calculate the rolling rate of change for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the rate of change for.
        output_column (str): Name of the output column for rate of change.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling rate of change column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling rate of change calculation for each group
    data['O_i_t_minus_1'] = data.groupby(group_column)[target_column].shift(1)
    data['I_it'] = np.log(
      (data[target_column] + 1) / (data['O_i_t_minus_1'] + 1)
    )
    data['sum_I_it_last_windows'] = data.groupby(group_column)['I_it'].transform(
      lambda x: x.rolling(window=window_size, min_periods=window_size).sum()
    )
    data[output_column] =data['sum_I_it_last_windows']
    
    # remove intermediate columns
    data = data.drop(columns=['O_i_t_minus_1', 'I_it', 'sum_I_it_last_windows'])
    
    return data

def calculating_rolling_mean_with_na(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling mean for a grouped column in a DataFrame, properly handling NaN values.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the mean for.
        output_column (str): Name of the output column for means.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling mean column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling mean calculation for each group, ignoring NaN values
    data[output_column] = data.groupby(group_column)[target_column].transform(
        lambda x: x.rolling(window=window_size, min_periods=1).mean()
    )
    
    return data

def calculate_rolling_slope_with_na(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling slope for a grouped column in a DataFrame, handling NaN values.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the slope for.
        output_column (str): Name of the output column for slopes.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling slope column.
    """
    
    def rolling_slope_with_na(df, column, window):
        # Drop NaN values before calculation
        valid_data = df[[column]].dropna()
        if len(valid_data) < 2:  # Need at least 2 points for a slope
            return pd.Series([np.nan] * len(df), index=df.index)
        
        # Create a helper column with row numbers for non-NaN values
        valid_data['x'] = range(1, len(valid_data) + 1)
        
        # Apply rolling slope calculation on valid data
        slopes = []
        for i in range(len(valid_data) - window + 1):
            window_data = valid_data.iloc[i:i+window]
            if len(window_data) < window * 0.5:  # Require at least half the window to be valid
                slopes.append(np.nan)
                continue
                
            x = window_data['x'].values
            y = window_data[column].values
            
            if len(np.unique(x)) < 2:  # Need at least 2 distinct x values
                slopes.append(np.nan)
                continue
                
            # Calculate slope
            n = len(window_data)
            sum_x = np.sum(x)
            sum_y = np.sum(y)
            sum_xy = np.sum(x * y)
            sum_x2 = np.sum(x ** 2)
            
            denominator = n * sum_x2 - sum_x ** 2
            if denominator == 0:
                slopes.append(np.nan)
            else:
                slope = (n * sum_xy - sum_x * sum_y) / denominator
                slopes.append(slope)
        
        # Create result series with same shape as input
        result = pd.Series([np.nan] * len(df), index=df.index)
        
        # Find original indices of non-NaN values
        original_indices = df.index[~df[column].isna()]
        
        # Map slopes to correct positions, accounting for window size
        for i, slope in enumerate(slopes):
            if i + window - 1 < len(original_indices):
                idx = original_indices[i + window - 1]
                result.loc[idx] = slope
        
        return result
    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling slope calculation for each group
    data[output_column] = data.groupby(group_column).apply(
        lambda group: rolling_slope_with_na(group, target_column, window_size)
    ).reset_index(level=0, drop=True)
    
    return data

def calculate_rolling_rate_of_change_with_na(data, group_column, target_column, window_size, output_column, sort_column):
    """
    Calculate the rolling rate of change for a grouped column in a DataFrame, properly handling NaN values.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the rate of change for.
        output_column (str): Name of the output column for rate of change.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling rate of change column.
    """    
    # Make a copy to avoid modifying the original dataframe
    result = data.copy()
    
    # Sort data by group and specified sort column
    result = result.sort_values(by=[group_column, sort_column])
    
    # Apply rolling rate of change calculation for each group
    result['O_i_t_minus_1'] = result.groupby(group_column)[target_column].shift(1)
    
    # For percentage values (like success rate), we use simple difference instead of log transformation
    if 'rate' in target_column or 'success' in target_column:
        result['I_it'] = result[target_column] - result['O_i_t_minus_1']
    # For time values (like merge time), calculate percentage change
    elif 'time' in target_column:
        # Only calculate where both current and previous values are not NaN
        valid_mask = (~result[target_column].isna()) & (~result['O_i_t_minus_1'].isna())
        result['I_it'] = np.nan
        result.loc[valid_mask, 'I_it'] = (
            (result.loc[valid_mask, target_column] - result.loc[valid_mask, 'O_i_t_minus_1']) / 
            result.loc[valid_mask, 'O_i_t_minus_1']
        )
    # For count data, use log transformation as before
    else:
        result['I_it'] = np.log((result[target_column] + 1) / (result['O_i_t_minus_1'] + 1))
    
    # Calculate rolling sum, ignoring NaNs when possible
    result['sum_I_it_last_windows'] = result.groupby(group_column)['I_it'].transform(
        lambda x: x.rolling(window=window_size, min_periods=1).sum()
    )
    
    result[output_column] = result['sum_I_it_last_windows']
    
    # Remove intermediate columns
    result = result.drop(columns=['O_i_t_minus_1', 'I_it', 'sum_I_it_last_windows'])
    
    return result

unified_productivity = productivity.copy()

for win in window_size:
    # Calculate rolling slope for PR throughput
    logging.info(f"Calculating rolling slope for PR throughput with window size {win}...")
    unified_productivity = calculating_rolling_mean(unified_productivity, 'repo_name', 'pr_throughput', f'pr_throughput_slope_{win}', win, 'standardized_time_weeks'
    )
    
    unified_productivity = calculate_rolling_rate_of_change(
        unified_productivity, 'repo_name', 'pr_throughput', win, f'pr_throughput_rate_of_change_{win}', 'standardized_time_weeks'
    )
    
    unified_productivity = calculating_rolling_mean_with_na(
        unified_productivity, 'repo_name', 'pull_request_success_rate', f'pull_request_success_rate_mean_{win}', win, 'standardized_time_weeks'
    )
    
    unified_productivity = calculate_rolling_rate_of_change_with_na(
        unified_productivity, 'repo_name', 'pull_request_success_rate', win, f'pull_request_success_rate_rate_of_change_{win}', 'standardized_time_weeks'
    )
    
    unified_productivity = calculating_rolling_mean_with_na(
        unified_productivity, 'repo_name', 'time_to_merge', 
        f'time_to_merge_mean_{win}', win, 'standardized_time_weeks'
    )
    
    unified_productivity = calculate_rolling_rate_of_change_with_na(
        unified_productivity, 'repo_name', 'time_to_merge', win, f'time_to_merge_rate_of_change_{win}', 'standardized_time_weeks'
    )
    
    
    
    unified_productivity[f'feature_sigmod_{win}_pr_throughput'] = sigmoid(unified_productivity[f'pr_throughput_rate_of_change_{win}'])
    unified_productivity[f'feature_sigmod_{win}_pull_request_success_rate'] = sigmoid(unified_productivity[f'pull_request_success_rate_rate_of_change_{win}'])
    unified_productivity[f'feature_sigmod_{win}_time_to_merge'] = sigmoid(unified_productivity[f'time_to_merge_rate_of_change_{win}'])
    
    unified_productivity[f'feature_sigmod_{win}'] = sigmoid(unified_productivity[f'pr_throughput_rate_of_change_{win}'] + unified_productivity[f'pull_request_success_rate_rate_of_change_{win}'] + unified_productivity[f'time_to_merge_rate_of_change_{win}'])
    # only main feature_sigmod_{win} used in the following analysis
    unified_productivity = unified_productivity.drop(columns=[f'pr_throughput_rate_of_change_{win}', f'pull_request_success_rate_rate_of_change_{win}', f'time_to_merge_rate_of_change_{win}'])
    unified_productivity = unified_productivity.drop(columns=[f'pr_throughput_slope_{win}', f'pull_request_success_rate_mean_{win}', f'time_to_merge_mean_{win}'])

# save the productivity data
save_data(unified_productivity, 'productivity_20250312_with_propensity_scores.csv')
# save_data(unified_productivity, 'productivity_20250408_with_propensity_scores.csv')