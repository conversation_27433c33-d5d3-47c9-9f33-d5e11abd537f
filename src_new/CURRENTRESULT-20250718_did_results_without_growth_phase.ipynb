{"cells": [{"cell_type": "code", "execution_count": 1, "id": "961b1dec", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘lme4’ was built under R version 4.3.3”\n", "Loading required package: Matrix\n", "\n", "Warning message:\n", "“package ‘Matrix’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘stargazer’ was built under R version 4.3.3”\n", "\n", "Please cite as: \n", "\n", "\n", " <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2022). stargazer: Well-Formatted Regression and Summary Statistics Tables.\n", "\n", " R package version 5.2.3. https://CRAN.R-project.org/package=stargazer \n", "\n", "\n", "Warning message:\n", "“package ‘lmtest’ was built under R version 4.3.3”\n", "Loading required package: zoo\n", "\n", "\n", "Attaching package: ‘zoo’\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    as.Date, as.Date.numeric\n", "\n", "\n", "Warning message:\n", "“package ‘MuMIn’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘lmerTest’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘lmerTest’\n", "\n", "\n", "The following object is masked from ‘package:lme4’:\n", "\n", "    lmer\n", "\n", "\n", "The following object is masked from ‘package:stats’:\n", "\n", "    step\n", "\n", "\n", "Warning message:\n", "“package ‘ggpubr’ was built under R version 4.3.3”\n", "Registered S3 method overwritten by 'broom':\n", "  method        from \n", "  nobs.multinom MuMIn\n", "\n", "Warning message:\n", "“package ‘survminer’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘survminer’\n", "\n", "\n", "The following object is masked from ‘package:survival’:\n", "\n", "    myeloma\n", "\n", "\n", "Loading required package: carData\n", "\n", "Warning message:\n", "“package ‘coxme’ was built under R version 4.3.3”\n", "Loading required package: bdsmatrix\n", "\n", "\n", "Attaching package: ‘bdsmatrix’\n", "\n", "\n", "The following object is masked from ‘package:base’:\n", "\n", "    backsolve\n", "\n", "\n", "Registered S3 methods overwritten by 'coxme':\n", "  method        from \n", "  formula.coxme MuMIn\n", "  logLik.lmekin Mu<PERSON>n\n", "\n"]}], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "# Read data\n", "compiled_data_test <- read.csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity_decelerating_to_steady.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "24e42941", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: ‘dplyr’\n", "\n", "\n", "The following object is masked from ‘package:car’:\n", "\n", "    recode\n", "\n", "\n", "The following objects are masked from ‘package:stats’:\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n"]}], "source": ["# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  ) \n", "  \n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "2da9677d", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "  accelerating first 3 months     saturation         steady \n", "        424666           1300         388078        2668089 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "# compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'first 3 months', 'saturation', 'steady'),]\n", "# table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 3, "id": "7e1a0927", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.383617                     1.059517 \n", "         log_project_commits     log_project_contributors \n", "                    1.968269                     2.234025 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.441450                     1.361595 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6311523  6311653 -3155751  6311503  3503011 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8380 -0.4982 -0.1642  0.5291  8.7252 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32925  0.5738  \n", " time_cohort_effect (Intercept) 0.02544  0.1595  \n", " Residual                       0.30299  0.5504  \n", "Number of obs: 3503021, groups:  \n", "repo_cohort_effect, 140744; time_cohort_effect, 140734\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   7.213e-01  2.342e-03  1.501e+05  307.98   <2e-16\n", "is_post_treatment            -1.957e-02  1.218e-03  1.178e+05  -16.07   <2e-16\n", "is_treated                   -6.753e-02  3.207e-03  1.274e+05  -21.06   <2e-16\n", "log_project_commits           2.679e-01  2.075e-03  1.786e+05  129.10   <2e-16\n", "log_project_contributors      3.263e-01  2.202e-03  1.908e+05  148.21   <2e-16\n", "log_project_age              -2.136e-01  1.586e-03  2.709e+05 -134.65   <2e-16\n", "is_post_treatment:is_treated -9.823e-02  1.183e-03  3.317e+06  -83.06   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.267                                                 \n", "is_treated  -0.689  0.106                                          \n", "lg_prjct_cm -0.058  0.003  0.084                                   \n", "lg_prjct_cn  0.093 -0.020 -0.129 -0.613                            \n", "log_prjct_g  0.063 -0.160 -0.041 -0.111      -0.331                \n", "is_pst_tr:_  0.124 -0.494 -0.179  0.004       0.004       0.028    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["           R2m       R2c\n", "[1,] 0.2565421 0.6574947\n"]}], "source": ["# Model 1: Fixed Effects Only\n", "model_pr_throughput_1 <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_pr_throughput_1 <- vif(model_pr_throughput_1)\n", "print(vif_model_pr_throughput_1)\n", "\n", "# Summary of the model\n", "summary(model_pr_throughput_1)\n", "\n", "# Calculate R-squared values\n", "r_squared_values <- r.squaredGLMM(model_pr_throughput_1)\n", "print(r_squared_values)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "47f13a54", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.238252\n", "is_treated                                                             1.044051\n", "log_project_commits                                                    1.997906\n", "log_project_contributors                                               2.279153\n", "log_project_age                                                        1.453149\n", "is_post_treatment:is_treated:log_tenure_c                              1.987556\n", "is_post_treatment:is_treated:log_commit_percent_c                      2.912901\n", "is_post_treatment:is_treated:log_commits_c                             4.789662\n", "is_post_treatment:is_treated:log_newcomers                             1.585919\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      5.565022\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.154141\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.890061\n", "is_post_treatment:is_treated:project_main_language                     1.360902\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.112768\n", "is_treated                                                                    1.021788\n", "log_project_commits                                                           1.413473\n", "log_project_contributors                                                      1.509687\n", "log_project_age                                                               1.205466\n", "is_post_treatment:is_treated:log_tenure_c                                     1.409807\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.706722\n", "is_post_treatment:is_treated:log_commits_c                                    2.188530\n", "is_post_treatment:is_treated:log_newcomers                                    1.259333\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.359030\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.775990\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.374795\n", "is_post_treatment:is_treated:project_main_language                            1.017267\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 22 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6273988  6274315 -3136969  6273938  3482208 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.7511 -0.4973 -0.1644  0.5284  8.7991 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32739  0.5722  \n", " time_cohort_effect (Intercept) 0.02566  0.1602  \n", " Residual                       0.30301  0.5505  \n", "Number of obs: 3482233, groups:  \n", "repo_cohort_effect, 139910; time_cohort_effect, 139900\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             7.423e-01\n", "is_post_treatment                                                      -6.489e-02\n", "is_treated                                                             -1.101e-01\n", "log_project_commits                                                     2.650e-01\n", "log_project_contributors                                                3.223e-01\n", "log_project_age                                                        -2.067e-01\n", "is_post_treatment:is_treated:log_tenure_c                               4.106e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -2.425e-02\n", "is_post_treatment:is_treated:log_commits_c                             -1.990e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.109e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.008e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.105e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment          -1.505e-02\n", "is_post_treatment:is_treated:project_main_language1                    -5.779e-02\n", "is_post_treatment:is_treated:project_main_language2                     1.907e-02\n", "is_post_treatment:is_treated:project_main_language3                     5.107e-02\n", "is_post_treatment:is_treated:project_main_language4                    -1.000e-02\n", "is_post_treatment:is_treated:project_main_language5                    -6.441e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.898e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.693e-02\n", "is_post_treatment:is_treated:project_main_language8                    -6.181e-02\n", "is_post_treatment:is_treated:project_main_language9                     7.803e-02\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             2.336e-03\n", "is_post_treatment                                                       1.159e-03\n", "is_treated                                                              3.184e-03\n", "log_project_commits                                                     2.093e-03\n", "log_project_contributors                                                2.227e-03\n", "log_project_age                                                         1.594e-03\n", "is_post_treatment:is_treated:log_tenure_c                               1.416e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       1.724e-03\n", "is_post_treatment:is_treated:log_commits_c                              2.201e-03\n", "is_post_treatment:is_treated:log_newcomers                              7.455e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.371e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.785e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.380e-03\n", "is_post_treatment:is_treated:project_main_language1                     2.352e-03\n", "is_post_treatment:is_treated:project_main_language2                     3.891e-03\n", "is_post_treatment:is_treated:project_main_language3                     4.277e-03\n", "is_post_treatment:is_treated:project_main_language4                     3.377e-03\n", "is_post_treatment:is_treated:project_main_language5                     3.146e-03\n", "is_post_treatment:is_treated:project_main_language6                     3.134e-03\n", "is_post_treatment:is_treated:project_main_language7                     3.959e-03\n", "is_post_treatment:is_treated:project_main_language8                     2.175e-03\n", "is_post_treatment:is_treated:project_main_language9                     4.678e-03\n", "                                                                               df\n", "(Intercept)                                                             1.459e+05\n", "is_post_treatment                                                       8.925e+04\n", "is_treated                                                              1.218e+05\n", "log_project_commits                                                     1.836e+05\n", "log_project_contributors                                                1.959e+05\n", "log_project_age                                                         2.569e+05\n", "is_post_treatment:is_treated:log_tenure_c                               9.478e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       9.416e+05\n", "is_post_treatment:is_treated:log_commits_c                              9.506e+05\n", "is_post_treatment:is_treated:log_newcomers                              1.800e+06\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.364e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  9.531e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           9.923e+05\n", "is_post_treatment:is_treated:project_main_language1                     9.679e+05\n", "is_post_treatment:is_treated:project_main_language2                     9.400e+05\n", "is_post_treatment:is_treated:project_main_language3                     9.497e+05\n", "is_post_treatment:is_treated:project_main_language4                     9.452e+05\n", "is_post_treatment:is_treated:project_main_language5                     9.466e+05\n", "is_post_treatment:is_treated:project_main_language6                     9.474e+05\n", "is_post_treatment:is_treated:project_main_language7                     9.382e+05\n", "is_post_treatment:is_treated:project_main_language8                     9.741e+05\n", "is_post_treatment:is_treated:project_main_language9                     9.611e+05\n", "                                                                        t value\n", "(Intercept)                                                             317.836\n", "is_post_treatment                                                       -55.985\n", "is_treated                                                              -34.586\n", "log_project_commits                                                     126.608\n", "log_project_contributors                                                144.746\n", "log_project_age                                                        -129.665\n", "is_post_treatment:is_treated:log_tenure_c                                28.991\n", "is_post_treatment:is_treated:log_commit_percent_c                       -14.066\n", "is_post_treatment:is_treated:log_commits_c                               -9.045\n", "is_post_treatment:is_treated:log_newcomers                               14.882\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         8.467\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.740\n", "is_post_treatment:is_treated:log_project_age_before_treatment           -10.903\n", "is_post_treatment:is_treated:project_main_language1                     -24.567\n", "is_post_treatment:is_treated:project_main_language2                       4.900\n", "is_post_treatment:is_treated:project_main_language3                      11.940\n", "is_post_treatment:is_treated:project_main_language4                      -2.962\n", "is_post_treatment:is_treated:project_main_language5                      -2.048\n", "is_post_treatment:is_treated:project_main_language6                      -6.057\n", "is_post_treatment:is_treated:project_main_language7                       4.278\n", "is_post_treatment:is_treated:project_main_language8                     -28.424\n", "is_post_treatment:is_treated:project_main_language9                      16.682\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                               < 2e-16\n", "is_post_treatment:is_treated:log_commit_percent_c                       < 2e-16\n", "is_post_treatment:is_treated:log_commits_c                              < 2e-16\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       < 2e-16\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  0.08190\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:project_main_language1                     < 2e-16\n", "is_post_treatment:is_treated:project_main_language2                    9.58e-07\n", "is_post_treatment:is_treated:project_main_language3                     < 2e-16\n", "is_post_treatment:is_treated:project_main_language4                     0.00305\n", "is_post_treatment:is_treated:project_main_language5                     0.04060\n", "is_post_treatment:is_treated:project_main_language6                    1.39e-09\n", "is_post_treatment:is_treated:project_main_language7                    1.89e-05\n", "is_post_treatment:is_treated:project_main_language8                     < 2e-16\n", "is_post_treatment:is_treated:project_main_language9                     < 2e-16\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                             ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment .  \n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                    ***\n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    ** \n", "is_post_treatment:is_treated:project_main_language5                    *  \n", "is_post_treatment:is_treated:project_main_language6                    ***\n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                    ***\n", "is_post_treatment:is_treated:project_main_language9                    ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.2587826</td><td>0.6576583</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.2587826 & 0.6576583\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.2587826 | 0.6576583 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.2587826 0.6576583"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "\n", "model_pr_throughput_2 <- lmer(\n", "  log_pr_throughput ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    # is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_pr_throughput_2,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_time_to_merge_2) %>% plot()\n", "# performance::model_performance(model_time_to_merge_2) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_pr_throughput_2,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_pr_throughput_2, # 使用lmer模型\n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 24, "id": "9979b288", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'JavaScript'</li><li>'C'</li><li>'C#'</li><li>'C++'</li><li>'Go'</li><li>'Java'</li><li>'PHP'</li><li>'Python'</li><li>'Rust'</li><li>'TypeScript'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'JavaScript'\n", "\\item 'C'\n", "\\item 'C\\#'\n", "\\item 'C++'\n", "\\item 'Go'\n", "\\item 'Java'\n", "\\item 'PHP'\n", "\\item 'Python'\n", "\\item 'Rust'\n", "\\item 'TypeScript'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'JavaScript'\n", "2. 'C'\n", "3. 'C#'\n", "4. 'C++'\n", "5. 'Go'\n", "6. 'Java'\n", "7. 'PHP'\n", "8. '<PERSON>'\n", "9. 'Rust'\n", "10. 'TypeScript'\n", "\n", "\n"], "text/plain": [" [1] \"JavaScript\" \"C\"          \"C#\"         \"C++\"        \"Go\"        \n", " [6] \"Java\"       \"PHP\"        \"Python\"     \"Rust\"       \"TypeScript\""]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'steady'</li><li>'accelerating'</li><li>'first 3 months'</li><li>'saturation'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'steady'\n", "\\item 'accelerating'\n", "\\item 'first 3 months'\n", "\\item 'saturation'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. 'steady'\n", "2. 'accelerating'\n", "3. 'first 3 months'\n", "4. 'saturation'\n", "\n", "\n"], "text/plain": ["[1] \"steady\"         \"accelerating\"   \"first 3 months\" \"saturation\"    "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 查看 project_main_language 的水平和順序\n", "levels(compiled_data_test$project_main_language)\n", "\n", "# 查看 growth_phase 的水平和順序\n", "levels(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 5, "id": "303139ef", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "  accelerating first 3 months     saturation         steady \n", "        218108            803          90689        1062497 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test <- read.csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity_decelerating_to_steady.csv\")\n", "# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  ) %>%\n", "  # 移除含有缺失值的观测（确保数据清洁）\n", "  tidyr::drop_na()\n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")\n", "compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'first 3 months', 'saturation', 'steady'),]\n", "table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 6, "id": "aa0c2a3c", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.623885                     1.148717 \n", "         log_project_commits     log_project_contributors \n", "                    1.983940                     2.285541 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.452978                     1.698589 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5358412  5358533 -2679196  5358392  1372087 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2368 -0.6080  0.0024  0.5956  5.5650 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.09832  1.0480  \n", " time_cohort_effect (Intercept) 0.08205  0.2864  \n", " Residual                       2.47699  1.5738  \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.243e+00  5.260e-03  1.468e+05 616.622  < 2e-16\n", "is_post_treatment            -6.295e-02  4.334e-03  8.630e+04 -14.525  < 2e-16\n", "is_treated                    1.525e-01  7.277e-03  1.297e+05  20.960  < 2e-16\n", "log_project_commits           2.872e-03  4.736e-03  1.186e+05   0.606    0.544\n", "log_project_contributors      4.703e-01  4.984e-03  1.147e+05  94.354  < 2e-16\n", "log_project_age               3.677e-02  3.883e-03  1.395e+05   9.469  < 2e-16\n", "is_post_treatment:is_treated -4.337e-02  5.656e-03  1.291e+06  -7.667 1.76e-14\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits             \n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.385                                                 \n", "is_treated  -0.685  0.229                                          \n", "lg_prjct_cm -0.107 -0.012  0.084                                   \n", "lg_prjct_cn  0.057 -0.011 -0.134 -0.608                            \n", "log_prjct_g  0.115 -0.094 -0.029 -0.111      -0.351                \n", "is_pst_tr:_  0.227 -0.607 -0.320 -0.001      -0.012       0.020    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.07503747 0.3735589\n"]}], "source": ["# Model 1: Fixed Effects Only\n", "model_time_to_merge_1 <- lmer(\n", "  log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_time_to_merge_1 <- vif(model_time_to_merge_1)\n", "print(vif_model_time_to_merge_1)\n", "\n", "# Summary of the model\n", "summary(model_time_to_merge_1)\n", "\n", "# Calculate R-squared values\n", "r_squared_values <- r.squaredG<PERSON>M(model_time_to_merge_1)\n", "print(r_squared_values)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "0a7e8f5c", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.374466\n", "is_treated                                                             1.094738\n", "log_project_commits                                                    2.077370\n", "log_project_contributors                                               2.394820\n", "log_project_age                                                        1.479553\n", "is_post_treatment:is_treated:log_tenure_c                              2.048247\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.075190\n", "is_post_treatment:is_treated:log_commits_c                             4.558736\n", "is_post_treatment:is_treated:log_newcomers                             2.575493\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.484293\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.683163\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.047452\n", "is_post_treatment:is_treated:project_main_language                     1.363540\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.172376\n", "is_treated                                                                    1.046298\n", "log_project_commits                                                           1.441309\n", "log_project_contributors                                                      1.547521\n", "log_project_age                                                               1.216369\n", "is_post_treatment:is_treated:log_tenure_c                                     1.431170\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.753622\n", "is_post_treatment:is_treated:log_commits_c                                    2.135120\n", "is_post_treatment:is_treated:log_newcomers                                    1.604834\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.546428\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.919157\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.430892\n", "is_post_treatment:is_treated:project_main_language                            1.017376\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 22 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5358105  5358408 -2679028  5358055  1372072 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2341 -0.6080  0.0023  0.5956  5.5766 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.09591  1.0469  \n", " time_cohort_effect (Intercept) 0.08058  0.2839  \n", " Residual                       2.47735  1.5740  \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             3.260e+00\n", "is_post_treatment                                                      -9.621e-02\n", "is_treated                                                              1.245e-01\n", "log_project_commits                                                    -1.090e-02\n", "log_project_contributors                                                4.781e-01\n", "log_project_age                                                         4.150e-02\n", "is_post_treatment:is_treated:log_tenure_c                               2.477e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -2.003e-02\n", "is_post_treatment:is_treated:log_commits_c                             -1.291e-03\n", "is_post_treatment:is_treated:log_newcomers                              1.313e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       6.271e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.963e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment          -3.677e-02\n", "is_post_treatment:is_treated:project_main_language1                    -9.482e-02\n", "is_post_treatment:is_treated:project_main_language2                     1.459e-02\n", "is_post_treatment:is_treated:project_main_language3                     3.867e-02\n", "is_post_treatment:is_treated:project_main_language4                     4.647e-02\n", "is_post_treatment:is_treated:project_main_language5                     3.698e-02\n", "is_post_treatment:is_treated:project_main_language6                    -5.745e-03\n", "is_post_treatment:is_treated:project_main_language7                    -3.583e-02\n", "is_post_treatment:is_treated:project_main_language8                    -2.353e-02\n", "is_post_treatment:is_treated:project_main_language9                     4.528e-02\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             5.201e-03\n", "is_post_treatment                                                       3.976e-03\n", "is_treated                                                              7.098e-03\n", "log_project_commits                                                     4.841e-03\n", "log_project_contributors                                                5.096e-03\n", "log_project_age                                                         3.914e-03\n", "is_post_treatment:is_treated:log_tenure_c                               6.167e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       9.676e-03\n", "is_post_treatment:is_treated:log_commits_c                              8.602e-03\n", "is_post_treatment:is_treated:log_newcomers                              3.087e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.694e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  7.203e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           5.923e-03\n", "is_post_treatment:is_treated:project_main_language1                     1.079e-02\n", "is_post_treatment:is_treated:project_main_language2                     1.752e-02\n", "is_post_treatment:is_treated:project_main_language3                     1.786e-02\n", "is_post_treatment:is_treated:project_main_language4                     1.369e-02\n", "is_post_treatment:is_treated:project_main_language5                     1.181e-02\n", "is_post_treatment:is_treated:project_main_language6                     1.292e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.787e-02\n", "is_post_treatment:is_treated:project_main_language8                     9.461e-03\n", "is_post_treatment:is_treated:project_main_language9                     1.829e-02\n", "                                                                               df\n", "(Intercept)                                                             1.404e+05\n", "is_post_treatment                                                       6.048e+04\n", "is_treated                                                              1.176e+05\n", "log_project_commits                                                     1.306e+05\n", "log_project_contributors                                                1.257e+05\n", "log_project_age                                                         1.501e+05\n", "is_post_treatment:is_treated:log_tenure_c                               2.550e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       3.132e+05\n", "is_post_treatment:is_treated:log_commits_c                              2.096e+05\n", "is_post_treatment:is_treated:log_newcomers                              3.389e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.111e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.994e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.191e+05\n", "is_post_treatment:is_treated:project_main_language1                     2.475e+05\n", "is_post_treatment:is_treated:project_main_language2                     2.374e+05\n", "is_post_treatment:is_treated:project_main_language3                     2.229e+05\n", "is_post_treatment:is_treated:project_main_language4                     2.157e+05\n", "is_post_treatment:is_treated:project_main_language5                     2.000e+05\n", "is_post_treatment:is_treated:project_main_language6                     2.120e+05\n", "is_post_treatment:is_treated:project_main_language7                     2.512e+05\n", "is_post_treatment:is_treated:project_main_language8                     2.396e+05\n", "is_post_treatment:is_treated:project_main_language9                     2.158e+05\n", "                                                                       t value\n", "(Intercept)                                                            626.897\n", "is_post_treatment                                                      -24.196\n", "is_treated                                                              17.535\n", "log_project_commits                                                     -2.251\n", "log_project_contributors                                                93.817\n", "log_project_age                                                         10.601\n", "is_post_treatment:is_treated:log_tenure_c                                4.017\n", "is_post_treatment:is_treated:log_commit_percent_c                       -2.070\n", "is_post_treatment:is_treated:log_commits_c                              -0.150\n", "is_post_treatment:is_treated:log_newcomers                               4.251\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        6.469\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -6.890\n", "is_post_treatment:is_treated:log_project_age_before_treatment           -6.208\n", "is_post_treatment:is_treated:project_main_language1                     -8.790\n", "is_post_treatment:is_treated:project_main_language2                      0.833\n", "is_post_treatment:is_treated:project_main_language3                      2.166\n", "is_post_treatment:is_treated:project_main_language4                      3.393\n", "is_post_treatment:is_treated:project_main_language5                      3.132\n", "is_post_treatment:is_treated:project_main_language6                     -0.445\n", "is_post_treatment:is_treated:project_main_language7                     -2.005\n", "is_post_treatment:is_treated:project_main_language8                     -2.487\n", "is_post_treatment:is_treated:project_main_language9                      2.476\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                     0.02437\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                              5.91e-05\n", "is_post_treatment:is_treated:log_commit_percent_c                       0.03844\n", "is_post_treatment:is_treated:log_commits_c                              0.88075\n", "is_post_treatment:is_treated:log_newcomers                             2.13e-05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      9.92e-11\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 5.61e-12\n", "is_post_treatment:is_treated:log_project_age_before_treatment          5.38e-10\n", "is_post_treatment:is_treated:project_main_language1                     < 2e-16\n", "is_post_treatment:is_treated:project_main_language2                     0.40482\n", "is_post_treatment:is_treated:project_main_language3                     0.03035\n", "is_post_treatment:is_treated:project_main_language4                     0.00069\n", "is_post_treatment:is_treated:project_main_language5                     0.00174\n", "is_post_treatment:is_treated:project_main_language6                     0.65653\n", "is_post_treatment:is_treated:project_main_language7                     0.04491\n", "is_post_treatment:is_treated:project_main_language8                     0.01288\n", "is_post_treatment:is_treated:project_main_language9                     0.01329\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    *  \n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      *  \n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    *  \n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ** \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    *  \n", "is_post_treatment:is_treated:project_main_language8                    *  \n", "is_post_treatment:is_treated:project_main_language9                    *  \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.07541896</td><td>0.3731228</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.07541896 & 0.3731228\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.07541896 | 0.3731228 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.07541896 0.3731228"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "model_time_to_merge_2 <- lmer(\n", "  log_time_to_merge ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    # is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_time_to_merge_2,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_time_to_merge_2) %>% plot()\n", "# performance::model_performance(model_time_to_merge_2) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_time_to_merge_2,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_time_to_merge_2, # 使用lmer模型\n", "  null = lmer(log_time_to_merge ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "72d5eeb2", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.647148                     1.184922 \n", "         log_project_commits     log_project_contributors \n", "                    1.983104                     2.286067 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.453067                     1.758309 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", "-1693866 -1693745   846943 -1693886  1372087 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3712 -0.2066  0.2417  0.4907  3.5790 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " repo_cohort_effect (Intercept) 0.0047070 0.06861 \n", " time_cohort_effect (Intercept) 0.0004253 0.02062 \n", " Residual                       0.0148595 0.12190 \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df  t value Pr(>|t|)\n", "(Intercept)                   6.153e-01  3.665e-04  1.489e+05 1678.964  < 2e-16\n", "is_post_treatment             1.887e-05  3.298e-04  9.302e+04    0.057   0.9544\n", "is_treated                   -1.648e-03  5.052e-04  1.313e+05   -3.263   0.0011\n", "log_project_commits           5.540e-03  3.252e-04  1.118e+05   17.037  < 2e-16\n", "log_project_contributors     -2.460e-02  3.414e-04  1.066e+05  -72.052  < 2e-16\n", "log_project_age               4.546e-03  2.689e-04  1.290e+05   16.905  < 2e-16\n", "is_post_treatment:is_treated -2.650e-03  4.362e-04  1.303e+06   -6.076 1.23e-09\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment               \n", "is_treated                   ** \n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.421                                                 \n", "is_treated  -0.682  0.256                                          \n", "lg_prjct_cm -0.113 -0.012  0.083                                   \n", "lg_prjct_cn  0.050 -0.009 -0.132 -0.607                            \n", "log_prjct_g  0.122 -0.085 -0.028 -0.112      -0.353                \n", "is_pst_tr:_  0.254 -0.616 -0.360 -0.002      -0.013       0.019    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.02209389 0.2731404\n"]}], "source": ["model_pr_accept_rate_1 <- lmer(\n", "  log_pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_pr_accept_rate_1 <- vif(model_pr_accept_rate_1)\n", "print(vif_model_pr_accept_rate_1)\n", "# Summary of the model\n", "summary(model_pr_accept_rate_1)\n", "# Calculate R-squared values\n", "r_squared_values_pr_accept_rate <- r.squaredGLMM(model_pr_accept_rate_1)\n", "print(r_squared_values_pr_accept_rate)"]}, {"cell_type": "code", "execution_count": 9, "id": "f0f2e44b", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.379676\n", "is_treated                                                             1.113653\n", "log_project_commits                                                    2.101956\n", "log_project_contributors                                               2.423687\n", "log_project_age                                                        1.494083\n", "is_post_treatment:is_treated:log_tenure_c                              2.046199\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.064014\n", "is_post_treatment:is_treated:log_commits_c                             4.557035\n", "is_post_treatment:is_treated:log_newcomers                             2.575650\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.491935\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.706553\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.060979\n", "is_post_treatment:is_treated:project_main_language                     1.361805\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.174596\n", "is_treated                                                                    1.055298\n", "log_project_commits                                                           1.449813\n", "log_project_contributors                                                      1.556820\n", "log_project_age                                                               1.222327\n", "is_post_treatment:is_treated:log_tenure_c                                     1.430454\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.750432\n", "is_post_treatment:is_treated:log_commits_c                                    2.134721\n", "is_post_treatment:is_treated:log_newcomers                                    1.604883\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.547928\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.925241\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.435611\n", "is_post_treatment:is_treated:project_main_language                            1.017304\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 22 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_tenure_c + is_post_treatment:is_treated:log_commit_percent_c +  \n", "    is_post_treatment:is_treated:log_commits_c + is_post_treatment:is_treated:log_newcomers +  \n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "       AIC        BIC     logLik   deviance   df.resid \n", "-1693913.8 -1693610.5   846981.9 -1693963.8    1372072 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3775 -0.2065  0.2417  0.4906  3.5738 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " repo_cohort_effect (Intercept) 0.0046998 0.06855 \n", " time_cohort_effect (Intercept) 0.0004267 0.02066 \n", " Residual                       0.0148594 0.12190 \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             6.157e-01\n", "is_post_treatment                                                      -7.934e-04\n", "is_treated                                                             -2.365e-03\n", "log_project_commits                                                     5.339e-03\n", "log_project_contributors                                               -2.448e-02\n", "log_project_age                                                         4.518e-03\n", "is_post_treatment:is_treated:log_tenure_c                               1.255e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.027e-03\n", "is_post_treatment:is_treated:log_commits_c                             -1.028e-05\n", "is_post_treatment:is_treated:log_newcomers                             -5.250e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.143e-05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -5.879e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.252e-05\n", "is_post_treatment:is_treated:project_main_language1                    -4.497e-03\n", "is_post_treatment:is_treated:project_main_language2                     6.629e-04\n", "is_post_treatment:is_treated:project_main_language3                     5.505e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.026e-03\n", "is_post_treatment:is_treated:project_main_language5                    -2.470e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.565e-03\n", "is_post_treatment:is_treated:project_main_language7                     1.215e-03\n", "is_post_treatment:is_treated:project_main_language8                     3.969e-04\n", "is_post_treatment:is_treated:project_main_language9                     1.494e-03\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             3.618e-04\n", "is_post_treatment                                                       3.020e-04\n", "is_treated                                                              4.895e-04\n", "log_project_commits                                                     3.346e-04\n", "log_project_contributors                                                3.514e-04\n", "log_project_age                                                         2.726e-04\n", "is_post_treatment:is_treated:log_tenure_c                               4.644e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                       7.288e-04\n", "is_post_treatment:is_treated:log_commits_c                              6.484e-04\n", "is_post_treatment:is_treated:log_newcomers                              2.336e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       7.322e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  5.454e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.481e-04\n", "is_post_treatment:is_treated:project_main_language1                     8.131e-04\n", "is_post_treatment:is_treated:project_main_language2                     1.320e-03\n", "is_post_treatment:is_treated:project_main_language3                     1.346e-03\n", "is_post_treatment:is_treated:project_main_language4                     1.032e-03\n", "is_post_treatment:is_treated:project_main_language5                     8.908e-04\n", "is_post_treatment:is_treated:project_main_language6                     9.742e-04\n", "is_post_treatment:is_treated:project_main_language7                     1.346e-03\n", "is_post_treatment:is_treated:project_main_language8                     7.132e-04\n", "is_post_treatment:is_treated:project_main_language9                     1.380e-03\n", "                                                                               df\n", "(Intercept)                                                             1.411e+05\n", "is_post_treatment                                                       6.421e+04\n", "is_treated                                                              1.156e+05\n", "log_project_commits                                                     1.258e+05\n", "log_project_contributors                                                1.194e+05\n", "log_project_age                                                         1.425e+05\n", "is_post_treatment:is_treated:log_tenure_c                               2.686e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       3.286e+05\n", "is_post_treatment:is_treated:log_commits_c                              2.199e+05\n", "is_post_treatment:is_treated:log_newcomers                              3.542e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.214e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  2.079e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.280e+05\n", "is_post_treatment:is_treated:project_main_language1                     2.608e+05\n", "is_post_treatment:is_treated:project_main_language2                     2.505e+05\n", "is_post_treatment:is_treated:project_main_language3                     2.346e+05\n", "is_post_treatment:is_treated:project_main_language4                     2.264e+05\n", "is_post_treatment:is_treated:project_main_language5                     2.103e+05\n", "is_post_treatment:is_treated:project_main_language6                     2.231e+05\n", "is_post_treatment:is_treated:project_main_language7                     2.647e+05\n", "is_post_treatment:is_treated:project_main_language8                     2.525e+05\n", "is_post_treatment:is_treated:project_main_language9                     2.269e+05\n", "                                                                        t value\n", "(Intercept)                                                            1701.862\n", "is_post_treatment                                                        -2.627\n", "is_treated                                                               -4.832\n", "log_project_commits                                                      15.954\n", "log_project_contributors                                                -69.679\n", "log_project_age                                                          16.575\n", "is_post_treatment:is_treated:log_tenure_c                                 2.701\n", "is_post_treatment:is_treated:log_commit_percent_c                        -4.153\n", "is_post_treatment:is_treated:log_commits_c                               -0.016\n", "is_post_treatment:is_treated:log_newcomers                               -2.247\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         0.125\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.078\n", "is_post_treatment:is_treated:log_project_age_before_treatment             0.073\n", "is_post_treatment:is_treated:project_main_language1                      -5.531\n", "is_post_treatment:is_treated:project_main_language2                       0.502\n", "is_post_treatment:is_treated:project_main_language3                       4.090\n", "is_post_treatment:is_treated:project_main_language4                       1.962\n", "is_post_treatment:is_treated:project_main_language5                      -2.773\n", "is_post_treatment:is_treated:project_main_language6                      -1.606\n", "is_post_treatment:is_treated:project_main_language7                       0.902\n", "is_post_treatment:is_treated:project_main_language8                       0.556\n", "is_post_treatment:is_treated:project_main_language9                       1.083\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       0.00861\n", "is_treated                                                             1.35e-06\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                               0.00691\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.28e-05\n", "is_post_treatment:is_treated:log_commits_c                              0.98736\n", "is_post_treatment:is_treated:log_newcomers                              0.02462\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       0.90064\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  0.28108\n", "is_post_treatment:is_treated:log_project_age_before_treatment           0.94216\n", "is_post_treatment:is_treated:project_main_language1                    3.19e-08\n", "is_post_treatment:is_treated:project_main_language2                     0.61544\n", "is_post_treatment:is_treated:project_main_language3                    4.32e-05\n", "is_post_treatment:is_treated:project_main_language4                     0.04975\n", "is_post_treatment:is_treated:project_main_language5                     0.00556\n", "is_post_treatment:is_treated:project_main_language6                     0.10821\n", "is_post_treatment:is_treated:project_main_language7                     0.36690\n", "is_post_treatment:is_treated:project_main_language8                     0.57788\n", "is_post_treatment:is_treated:project_main_language9                     0.27897\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ** \n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ** \n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             *  \n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment    \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    *  \n", "is_post_treatment:is_treated:project_main_language5                    ** \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                       \n", "is_post_treatment:is_treated:project_main_language9                       \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.02215342</td><td>0.2729752</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.02215342 & 0.2729752\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.02215342 | 0.2729752 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.02215342 0.2729752"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "model_pr_accept_rate_4 <- lmer(\n", "  log_pull_request_success_rate ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    # is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "# 计算VIF（使用car包改进方法）\n", "vif_model_pr_accept_rate_4 <- car::vif(\n", "  model_pr_accept_rate_4,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model_pr_accept_rate_4)\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_pr_accept_rate_4) %>% plot()\n", "# performance::model_performance(model_pr_accept_rate_4) %>% print()\n", "# 模型总结（优化显示）\n", "summary(model_pr_accept_rate_4,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_pr_accept_rate_4, # 使用lmer模型\n", "  null = lmer(log_pull_request_success_rate ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n", "# 计算模型的AIC值"]}, {"cell_type": "code", "execution_count": 10, "id": "3420c149", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 22 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n", "\n", "Correlation matrix not shown by default, as p = 22 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n", "\n", "Correlation matrix not shown by default, as p = 22 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n"]}], "source": ["# 保存和打印以上所有模型结果到文件中，命名格式为日期_模型名称.txt\n", "# 目录在“../result/did_result_20250408/”下\n", "# 需要确保该目录存在\n", "output_dir <- \"../result/did_result_20250718/\"\n", "if (!dir.exists(output_dir)) {\n", "  dir.create(output_dir, recursive = TRUE)\n", "}\n", "# 保存结果包括模型摘要、VIF和R-squared\n", "save_model_results <- function(model, model_name) {\n", "  # 创建文件名\n", "  file_name <- paste0(output_dir, Sys.Date(), \"_\", model_name, \".txt\")\n", "  \n", "  # 打开文件连接\n", "  sink(file_name)\n", "  \n", "  # 打印模型摘要\n", "  cat(\"Model Summary:\\n\")\n", "  print(summary(model))\n", "  \n", "  # 打印VIF\n", "  cat(\"\\nVIF:\\n\")\n", "  print(vif(model))\n", "  \n", "  # 打印R-squared\n", "  cat(\"\\nR-squared:\\n\")\n", "  print(r.squaredGLMM(model))\n", "  \n", "  # 关闭文件连接\n", "  sink()\n", "}\n", "# 保存模型结果\n", "save_model_results(model_pr_throughput_1, \"model_pr_throughput_1\")\n", "save_model_results(model_pr_throughput_2, \"model_pr_throughput_2\")\n", "save_model_results(model_time_to_merge_1, \"model_time_to_merge_1\")\n", "save_model_results(model_time_to_merge_2, \"model_time_to_merge_2\")\n", "save_model_results(model_pr_accept_rate_1, \"model_pr_accept_rate_1\")\n", "save_model_results(model_pr_accept_rate_4, \"model_pr_accept_rate_2\")"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 5}