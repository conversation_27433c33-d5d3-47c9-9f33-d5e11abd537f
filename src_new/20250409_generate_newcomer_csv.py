import pandas as pd
import os
import datetime
from pymongo import MongoClient
import concurrent.futures
import os
import logging  # 导入 logging 模块

# 配置 logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

newcomer_contributors = pd.DataFrame(columns=['login', 'date', 'repo_name'])

def get_processed_commit_file_repo_name(repo_name):
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError(f"The processed commit file for {repo_name} is empty.")
    return repo_commit

def process_repo(repo):
    try:
        repo_commit = get_processed_commit_file_repo_name(repo)
    except ValueError as e:
        logging.warning(f"ValueError for repo {repo}: {e}") # 使用 logging.warning 记录警告信息
        return None

    # 矢量化日期转换
    try:
        repo_commit['date'] = pd.to_datetime(repo_commit['date'], errors='raise')
    except:
        repo_commit['date'] = pd.to_datetime(repo_commit['date'], errors='raise').dt.tz_localize('UTC')

    earliest_date_repo = repo_commit.groupby('author_login')['date'].min().reset_index()
    earliest_date_repo.rename(columns={'author_login': 'login'}, inplace=True)
    # 添加 repo_name 列
    earliest_date_repo['repo_name'] = repo
    return earliest_date_repo

client = MongoClient('mongodb://localhost:27017/')
db = client['disengagement']
cache_collection = db["progress_cache"]
finished_projects = cache_collection.find({
        "commits_finished": 1,
        "pr_finished": 1,
        "pr_review_finished": 1
    }, {"repo_name": 1})
repos = [project["repo_name"] for project in finished_projects]

core_developer_list = pd.read_csv('../data/core_developer_list_total_repo.csv')


batch_size = 200
num_repos = len(repos)
num_threads = os.cpu_count() * 4

logging.info(f"Total repos to process: {num_repos}, batch size: {batch_size}, threads: {num_threads}") # 记录脚本启动信息

for i in range(0, num_repos, batch_size):
    repo_batch = repos[i:min(i + batch_size, num_repos)]
    batch_earliest_dates = []

    logging.info(f"Processing batch {i // batch_size + 1}: {len(repo_batch)} repos - {repo_batch[0]} to {repo_batch[-1]}") # 记录批次开始信息

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        future_to_repo = {executor.submit(process_repo, repo): repo for repo in repo_batch}

        for future in concurrent.futures.as_completed(future_to_repo):
            repo = future_to_repo[future]
            try:
                earliest_date_repo = future.result()
                if earliest_date_repo is not None:
                    batch_earliest_dates.append(earliest_date_repo)
            except Exception as exc:
                logging.error(f'{repo} generated an exception: {exc}', exc_info=True) # 使用 logging.error 记录错误信息，并包含 exc_info

    if batch_earliest_dates:
        batch_combined_earliest_dates = pd.concat(batch_earliest_dates, ignore_index=True)
        # 找到每个login的最早日期对应的行索引
        batch_earliest_indices = batch_combined_earliest_dates.groupby('login')['date'].idxmin().tolist()
        # 根据索引提取对应的行，保留login、date和repo_name
        batch_aggregated_earliest_dates = batch_combined_earliest_dates.loc[batch_earliest_indices, ['login', 'date', 'repo_name']]
        
        if newcomer_contributors.empty:
            newcomer_contributors = batch_aggregated_earliest_dates
        else:
            # 合并当前批次与之前的结果
            combined = pd.concat([newcomer_contributors, batch_aggregated_earliest_dates], ignore_index=True)
            # 再次找到每个login的最早日期对应的行索引
            earliest_indices = combined.groupby('login')['date'].idxmin().tolist()
            # 提取对应的行，保留login、date和repo_name
            newcomer_contributors = combined.loc[earliest_indices, ['login', 'date', 'repo_name']]

    logging.info(f"Batch {i // batch_size + 1} processed.") # 记录批次结束信息

logging.info("All batches processed. Saving newcomer contributors data.") # 记录最终保存数据信息
newcomer_contributors.to_csv('../data/newcomer_contributors.csv', index=False)
logging.info("Newcomer contributors data saved to CSV.") # 记录数据保存完成信息
logging.info("Script finished.") # 记录脚本结束信息
