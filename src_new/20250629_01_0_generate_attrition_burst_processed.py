import logging
import pandas as pd
import os
from datetime import datetime
import time

# Configure logging
log_file = '../logs/generate_attrition_burst_processed.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# ATTRITION_LIMITS = [180, 270, 450]
ATTRITION_LIMITS = [365, 180, 270, 450]

def compile_attrition_aggregated(attritions, near_days, burst_gap):
    """
    Identify and flag attrition bursts based on temporal proximity.
    """
    start_time = time.time()
    logging.info(f"Starting burst processing with near_days={near_days}, burst_gap={burst_gap}")
    
    df = attritions.copy()
    df['attrition_date'] = pd.to_datetime(df['attrition_time'])
    repo_names = df['repo_name'].unique()
    
    logging.info(f"Processing {len(repo_names)} repositories for burst identification")

    global_burst_id = 0
    aggregated_data = []
    
    # Process each repository
    for i, repo in enumerate(repo_names, 1):
        if i % 10 == 0 or i == len(repo_names):  # Log every 10 repos or the last one
            logging.info(f"Processing repository {i}/{len(repo_names)}: {repo}")
        
        repo_df = df[df['repo_name'] == repo].copy().sort_values(by='attrition_date').reset_index(drop=True)
        burst_start = None
        burst_list = []
        
        for _, row in repo_df.iterrows():
            current_date = row['attrition_date']
            if burst_start is None or (current_date - burst_start).days > near_days:
                global_burst_id += 1
                burst_start = current_date
            burst_list.append(global_burst_id)
        
        repo_df['burst'] = burst_list
        aggregated_data.append(repo_df)

    logging.info(f"Initial burst assignment completed. Total bursts identified: {global_burst_id}")

    attrition_aggregated = pd.concat(aggregated_data, ignore_index=True)
    attrition_aggregated['attrition_count'] = attrition_aggregated.groupby('burst')['burst'].transform('count')

    logging.info("Calculating burst details and inter-burst gaps...")
    
    burst_details = attrition_aggregated.groupby(['repo_name', 'burst']).agg(
        start_date=('attrition_date', 'min'),
        end_date=('attrition_date', 'max')
    ).reset_index()
    burst_details.sort_values(['repo_name', 'start_date'], inplace=True)
    burst_details['previous_end_date'] = burst_details.groupby('repo_name')['end_date'].shift(1)
    burst_details['inter_burst_gap'] = (burst_details['start_date'] - burst_details['previous_end_date']).dt.days

    def flag_bursts(x):
        gap_mask = x['inter_burst_gap'].fillna(9999) < burst_gap
        next_gap = gap_mask.shift(-1, fill_value=False)
        x['gap_less_than_84'] = gap_mask | next_gap
        return x

    burst_details = burst_details.groupby('repo_name', group_keys=False).apply(flag_bursts)
    attrition_aggregated = attrition_aggregated.merge(
        burst_details[['repo_name', 'burst', 'gap_less_than_84', 'inter_burst_gap']],
        on=['repo_name', 'burst'],
        how='left'
    )

    processing_time = time.time() - start_time
    logging.info(f"Burst processing completed in {processing_time:.2f} seconds")
    logging.info(f"Final dataset contains {len(attrition_aggregated)} records with burst information")

    return attrition_aggregated

def generate_attrition_burst_processed():
    """
    For each attrition limit, read attrition data from CSV files, process bursts,
    and generate corresponding CSV files with burst information.
    """
    overall_start_time = time.time()
    logging.info("=" * 60)
    logging.info("STARTING ATTRITION BURST PROCESSING")
    logging.info("=" * 60)
    
    # Define input and output directories
    input_dir = '../data/attrition_csv'
    output_dir = '../data/attrition_csv'
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    logging.info(f"Output directory ensured: {output_dir}")

    total_limits = len(ATTRITION_LIMITS)
    
    for limit_idx, limit in enumerate(ATTRITION_LIMITS, 1):
        limit_start_time = time.time()
        input_file = f"{input_dir}/attrition_{limit}.csv"
        
        logging.info("-" * 40)
        logging.info(f"PROCESSING LIMIT {limit_idx}/{total_limits}: {limit}")
        logging.info("-" * 40)
        
        # Check if input file exists
        if not os.path.exists(input_file):
            logging.warning(f"Input file not found: {input_file}")
            logging.info(f"Skipping limit {limit} due to missing input file")
            continue
        
        # Read CSV file
        logging.info(f"Reading input file: {input_file}")
        try:
            df = pd.read_csv(input_file)
            logging.info(f"✓ Successfully loaded {len(df)} records from {input_file}")
        except Exception as e:
            logging.error(f"✗ Error reading file {input_file}: {e}")
            logging.info(f"Skipping limit {limit} due to file reading error")
            continue
        
        # Verify required columns exist
        required_columns = ['repo_name', 'attrition_id', 'attrition_time', 'dev_login']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"✗ Missing required columns in {input_file}: {missing_columns}")
            logging.info(f"Skipping limit {limit} due to missing columns")
            continue
        
        logging.info(f"✓ All required columns present: {required_columns}")
        
        # Sort by repo_name and attrition_time for better organization
        logging.info("Sorting data by repository name and attrition time...")
        df = df.sort_values(['repo_name', 'attrition_time'])
        
        # Process bursts (near_days=7, burst_gap=84 as typical values)
        logging.info("Processing bursts...")
        near_days = 7
        burst_gap = 84
        df_with_bursts = compile_attrition_aggregated(df, near_days, burst_gap)
        
        # Save burst-processed data
        burst_output_file = f"{output_dir}/attrition_burst_{limit}.csv"
        logging.info(f"Saving burst-processed data to: {burst_output_file}")
        df_with_bursts.to_csv(burst_output_file, index=False)
        logging.info(f"✓ Successfully saved burst-processed file: {burst_output_file} with {len(df_with_bursts)} records")
        
        # Log summary statistics
        original_count = len(df)
        burst_count = len(df_with_bursts)
        
        limit_processing_time = time.time() - limit_start_time
        logging.info(f"Summary for limit {limit}:")
        logging.info(f"  - Original records: {original_count}")
        logging.info(f"  - Records with bursts: {burst_count}")
        logging.info(f"  - Processing time: {limit_processing_time:.2f} seconds")
        
        # Calculate progress percentage
        progress = (limit_idx / total_limits) * 100
        logging.info(f"Overall progress: {progress:.1f}% ({limit_idx}/{total_limits} limits completed)")

    overall_processing_time = time.time() - overall_start_time
    logging.info("=" * 60)
    logging.info("ATTRITION BURST PROCESSING COMPLETED")
    logging.info(f"Total processing time: {overall_processing_time:.2f} seconds")
    logging.info("=" * 60)

def main():
    logging.info(f"Script started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    generate_attrition_burst_processed()
    logging.info(f"Script completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 