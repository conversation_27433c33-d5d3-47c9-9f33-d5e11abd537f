{"cells": [{"cell_type": "code", "execution_count": 1, "id": "961b1dec", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘lme4’ was built under R version 4.3.3”\n", "Loading required package: Matrix\n", "\n", "Warning message:\n", "“package ‘Matrix’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘stargazer’ was built under R version 4.3.3”\n", "\n", "Please cite as: \n", "\n", "\n", " <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2022). stargazer: Well-Formatted Regression and Summary Statistics Tables.\n", "\n", " R package version 5.2.3. https://CRAN.R-project.org/package=stargazer \n", "\n", "\n", "Warning message:\n", "“package ‘lmtest’ was built under R version 4.3.3”\n", "Loading required package: zoo\n", "\n", "\n", "Attaching package: ‘zoo’\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    as.Date, as.Date.numeric\n", "\n", "\n", "Warning message:\n", "“package ‘MuMIn’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘lmerTest’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘lmerTest’\n", "\n", "\n", "The following object is masked from ‘package:lme4’:\n", "\n", "    lmer\n", "\n", "\n", "The following object is masked from ‘package:stats’:\n", "\n", "    step\n", "\n", "\n", "Warning message:\n", "“package ‘ggpubr’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘survminer’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘survminer’\n", "\n", "\n", "The following object is masked from ‘package:survival’:\n", "\n", "    myeloma\n", "\n", "\n", "Loading required package: carData\n", "\n", "Warning message:\n", "“package ‘coxme’ was built under R version 4.3.3”\n", "Loading required package: bdsmatrix\n", "\n", "\n", "Attaching package: ‘bdsmatrix’\n", "\n", "\n", "The following object is masked from ‘package:base’:\n", "\n", "    backsolve\n", "\n", "\n", "Registered S3 methods overwritten by 'coxme':\n", "  method        from \n", "  formula.coxme MuMIn\n", "  logLik.lmekin Mu<PERSON>n\n", "\n"]}], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "# Read data\n", "compiled_data_test <- read.csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "24e42941", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: ‘dplyr’\n", "\n", "\n", "The following object is masked from ‘package:car’:\n", "\n", "    recode\n", "\n", "\n", "The following objects are masked from ‘package:stats’:\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n"]}], "source": ["# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  ) %>%\n", "  # 移除含有缺失值的观测（确保数据清洁）\n", "  tidyr::drop_na()\n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "2da9677d", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'compiled_data_test' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'compiled_data_test' not found\nTraceback:\n"]}], "source": ["compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'),]\n", "table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 4, "id": "aa0c2a3c", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.623885                     1.148717 \n", "         log_project_commits     log_project_contributors \n", "                    1.983940                     2.285541 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.452978                     1.698589 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5358412  5358533 -2679196  5358392  1372087 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2368 -0.6080  0.0024  0.5956  5.5650 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.09832  1.0480  \n", " time_cohort_effect (Intercept) 0.08205  0.2864  \n", " Residual                       2.47699  1.5738  \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.243e+00  5.260e-03  1.468e+05 616.622  < 2e-16\n", "is_post_treatment            -6.295e-02  4.334e-03  8.630e+04 -14.525  < 2e-16\n", "is_treated                    1.525e-01  7.277e-03  1.297e+05  20.960  < 2e-16\n", "log_project_commits           2.872e-03  4.736e-03  1.186e+05   0.606    0.544\n", "log_project_contributors      4.703e-01  4.984e-03  1.147e+05  94.354  < 2e-16\n", "log_project_age               3.677e-02  3.883e-03  1.395e+05   9.469  < 2e-16\n", "is_post_treatment:is_treated -4.337e-02  5.656e-03  1.291e+06  -7.667 1.76e-14\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits             \n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.385                                                 \n", "is_treated  -0.685  0.229                                          \n", "lg_prjct_cm -0.107 -0.012  0.084                                   \n", "lg_prjct_cn  0.057 -0.011 -0.134 -0.608                            \n", "log_prjct_g  0.115 -0.094 -0.029 -0.111      -0.351                \n", "is_pst_tr:_  0.227 -0.607 -0.320 -0.001      -0.012       0.020    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.07503747 0.3735589\n"]}], "source": ["# Model 1: Fixed Effects Only\n", "model_time_to_merge_1 <- lmer(\n", "  log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_time_to_merge_1 <- vif(model_time_to_merge_1)\n", "print(vif_model_time_to_merge_1)\n", "\n", "# Summary of the model\n", "summary(model_time_to_merge_1)\n", "\n", "# Calculate R-squared values\n", "r_squared_values <- r.squaredG<PERSON>M(model_time_to_merge_1)\n", "print(r_squared_values)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "0a7e8f5c", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.617976\n", "is_treated                                                             1.154669\n", "log_project_commits                                                    2.079154\n", "log_project_contributors                                               2.397577\n", "log_project_age                                                        1.481150\n", "is_post_treatment:is_treated:log_tenure_c                              2.078382\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.196446\n", "is_post_treatment:is_treated:log_commits_c                             4.595842\n", "is_post_treatment:is_treated:log_newcomers                             4.139873\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.571090\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.766989\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.065197\n", "is_post_treatment:is_treated:project_main_language                     1.482738\n", "is_post_treatment:is_treated:growth_phase                              4.692275\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "is_post_treatment:is_treated:growth_phase                               4\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.271997\n", "is_treated                                                                    1.074555\n", "log_project_commits                                                           1.441927\n", "log_project_contributors                                                      1.548411\n", "log_project_age                                                               1.217025\n", "is_post_treatment:is_treated:log_tenure_c                                     1.441659\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.787861\n", "is_post_treatment:is_treated:log_commits_c                                    2.143791\n", "is_post_treatment:is_treated:log_newcomers                                    2.034668\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.563414\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.940873\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.437079\n", "is_post_treatment:is_treated:project_main_language                            1.022124\n", "is_post_treatment:is_treated:growth_phase                                     1.213174\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 26 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_post_treatment:is_treated:log_tenure_c +  \n", "    is_post_treatment:is_treated:log_commit_percent_c + is_post_treatment:is_treated:log_commits_c +  \n", "    is_post_treatment:is_treated:log_newcomers + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:growth_phase +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 5357698  5358050 -2678820  5357640  1372068 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2683 -0.6082  0.0018  0.5955  5.5936 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.09469  1.0463  \n", " time_cohort_effect (Intercept) 0.07994  0.2827  \n", " Residual                       2.47701  1.5739  \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             3.245e+00\n", "is_post_treatment                                                      -6.243e-02\n", "is_treated                                                              1.580e-01\n", "log_project_commits                                                    -1.333e-02\n", "log_project_contributors                                                4.746e-01\n", "log_project_age                                                         4.389e-02\n", "is_post_treatment:is_treated:log_tenure_c                               3.281e-02\n", "is_post_treatment:is_treated:log_commit_percent_c                      -4.864e-02\n", "is_post_treatment:is_treated:log_commits_c                              4.813e-03\n", "is_post_treatment:is_treated:log_newcomers                              6.028e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       7.009e-02\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -7.070e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment          -2.598e-02\n", "is_post_treatment:is_treated:project_main_language1                    -6.363e-02\n", "is_post_treatment:is_treated:project_main_language2                    -1.532e-02\n", "is_post_treatment:is_treated:project_main_language3                     1.031e-02\n", "is_post_treatment:is_treated:project_main_language4                     4.911e-02\n", "is_post_treatment:is_treated:project_main_language5                     5.912e-02\n", "is_post_treatment:is_treated:project_main_language6                     7.927e-03\n", "is_post_treatment:is_treated:project_main_language7                    -6.676e-02\n", "is_post_treatment:is_treated:project_main_language8                     1.074e-02\n", "is_post_treatment:is_treated:project_main_language9                     6.858e-03\n", "is_post_treatment:is_treated:growth_phase1                             -1.778e-01\n", "is_post_treatment:is_treated:growth_phase2                             -1.820e-01\n", "is_post_treatment:is_treated:growth_phase3                             -1.695e-01\n", "is_post_treatment:is_treated:growth_phase4                              7.358e-01\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             5.249e-03\n", "is_post_treatment                                                       4.309e-03\n", "is_treated                                                              7.286e-03\n", "log_project_commits                                                     4.840e-03\n", "log_project_contributors                                                5.096e-03\n", "log_project_age                                                         3.914e-03\n", "is_post_treatment:is_treated:log_tenure_c                               6.208e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                       9.859e-03\n", "is_post_treatment:is_treated:log_commits_c                              8.632e-03\n", "is_post_treatment:is_treated:log_newcomers                              3.912e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.753e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  7.280e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           5.945e-03\n", "is_post_treatment:is_treated:project_main_language1                     1.089e-02\n", "is_post_treatment:is_treated:project_main_language2                     1.757e-02\n", "is_post_treatment:is_treated:project_main_language3                     1.790e-02\n", "is_post_treatment:is_treated:project_main_language4                     1.369e-02\n", "is_post_treatment:is_treated:project_main_language5                     1.186e-02\n", "is_post_treatment:is_treated:project_main_language6                     1.293e-02\n", "is_post_treatment:is_treated:project_main_language7                     1.792e-02\n", "is_post_treatment:is_treated:project_main_language8                     9.605e-03\n", "is_post_treatment:is_treated:project_main_language9                     1.838e-02\n", "is_post_treatment:is_treated:growth_phase1                              1.017e-02\n", "is_post_treatment:is_treated:growth_phase2                              1.369e-02\n", "is_post_treatment:is_treated:growth_phase3                              1.050e-02\n", "is_post_treatment:is_treated:growth_phase4                              3.745e-02\n", "                                                                               df\n", "(Intercept)                                                             1.464e+05\n", "is_post_treatment                                                       8.210e+04\n", "is_treated                                                              1.299e+05\n", "log_project_commits                                                     1.308e+05\n", "log_project_contributors                                                1.257e+05\n", "log_project_age                                                         1.503e+05\n", "is_post_treatment:is_treated:log_tenure_c                               2.540e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       3.131e+05\n", "is_post_treatment:is_treated:log_commits_c                              2.088e+05\n", "is_post_treatment:is_treated:log_newcomers                              2.090e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.101e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.968e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.171e+05\n", "is_post_treatment:is_treated:project_main_language1                     2.427e+05\n", "is_post_treatment:is_treated:project_main_language2                     2.360e+05\n", "is_post_treatment:is_treated:project_main_language3                     2.212e+05\n", "is_post_treatment:is_treated:project_main_language4                     2.150e+05\n", "is_post_treatment:is_treated:project_main_language5                     1.989e+05\n", "is_post_treatment:is_treated:project_main_language6                     2.112e+05\n", "is_post_treatment:is_treated:project_main_language7                     2.502e+05\n", "is_post_treatment:is_treated:project_main_language8                     2.339e+05\n", "is_post_treatment:is_treated:project_main_language9                     2.130e+05\n", "is_post_treatment:is_treated:growth_phase1                              3.807e+05\n", "is_post_treatment:is_treated:growth_phase2                              2.783e+05\n", "is_post_treatment:is_treated:growth_phase3                              3.630e+05\n", "is_post_treatment:is_treated:growth_phase4                              4.457e+05\n", "                                                                       t value\n", "(Intercept)                                                            618.344\n", "is_post_treatment                                                      -14.488\n", "is_treated                                                              21.682\n", "log_project_commits                                                     -2.754\n", "log_project_contributors                                                93.139\n", "log_project_age                                                         11.212\n", "is_post_treatment:is_treated:log_tenure_c                                5.284\n", "is_post_treatment:is_treated:log_commit_percent_c                       -4.933\n", "is_post_treatment:is_treated:log_commits_c                               0.558\n", "is_post_treatment:is_treated:log_newcomers                              15.410\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        7.187\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -9.712\n", "is_post_treatment:is_treated:log_project_age_before_treatment           -4.369\n", "is_post_treatment:is_treated:project_main_language1                     -5.842\n", "is_post_treatment:is_treated:project_main_language2                     -0.871\n", "is_post_treatment:is_treated:project_main_language3                      0.576\n", "is_post_treatment:is_treated:project_main_language4                      3.588\n", "is_post_treatment:is_treated:project_main_language5                      4.986\n", "is_post_treatment:is_treated:project_main_language6                      0.613\n", "is_post_treatment:is_treated:project_main_language7                     -3.725\n", "is_post_treatment:is_treated:project_main_language8                      1.118\n", "is_post_treatment:is_treated:project_main_language9                      0.373\n", "is_post_treatment:is_treated:growth_phase1                             -17.491\n", "is_post_treatment:is_treated:growth_phase2                             -13.293\n", "is_post_treatment:is_treated:growth_phase3                             -16.143\n", "is_post_treatment:is_treated:growth_phase4                              19.650\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                    0.005890\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                              1.26e-07\n", "is_post_treatment:is_treated:log_commit_percent_c                      8.09e-07\n", "is_post_treatment:is_treated:log_commits_c                             0.577137\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.64e-13\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment          1.25e-05\n", "is_post_treatment:is_treated:project_main_language1                    5.17e-09\n", "is_post_treatment:is_treated:project_main_language2                    0.383486\n", "is_post_treatment:is_treated:project_main_language3                    0.564519\n", "is_post_treatment:is_treated:project_main_language4                    0.000333\n", "is_post_treatment:is_treated:project_main_language5                    6.16e-07\n", "is_post_treatment:is_treated:project_main_language6                    0.539796\n", "is_post_treatment:is_treated:project_main_language7                    0.000196\n", "is_post_treatment:is_treated:project_main_language8                    0.263715\n", "is_post_treatment:is_treated:project_main_language9                    0.709002\n", "is_post_treatment:is_treated:growth_phase1                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase2                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase3                              < 2e-16\n", "is_post_treatment:is_treated:growth_phase4                              < 2e-16\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ** \n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ***\n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                       \n", "is_post_treatment:is_treated:project_main_language4                    ***\n", "is_post_treatment:is_treated:project_main_language5                    ***\n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                    ***\n", "is_post_treatment:is_treated:project_main_language8                       \n", "is_post_treatment:is_treated:project_main_language9                       \n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.07559833</td><td>0.372953</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.07559833 & 0.372953\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.07559833 | 0.372953 |\n", "\n"], "text/plain": ["     R2m        R2c     \n", "[1,] 0.07559833 0.372953"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\"\n", "\n", "model_time_to_merge_2 <- lmer(\n", "  log_time_to_merge ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_time_to_merge_2,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_time_to_merge_2) %>% plot()\n", "# performance::model_performance(model_time_to_merge_2) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_time_to_merge_2,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_time_to_merge_2, # 使用lmer模型\n", "  null = lmer(log_time_to_merge ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "72d5eeb2", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.647148                     1.184922 \n", "         log_project_commits     log_project_contributors \n", "                    1.983104                     2.286067 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.453067                     1.758309 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", "-1693866 -1693745   846943 -1693886  1372087 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3712 -0.2066  0.2417  0.4907  3.5790 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " repo_cohort_effect (Intercept) 0.0047070 0.06861 \n", " time_cohort_effect (Intercept) 0.0004253 0.02062 \n", " Residual                       0.0148595 0.12190 \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df  t value Pr(>|t|)\n", "(Intercept)                   6.153e-01  3.665e-04  1.489e+05 1678.964  < 2e-16\n", "is_post_treatment             1.887e-05  3.298e-04  9.302e+04    0.057   0.9544\n", "is_treated                   -1.648e-03  5.052e-04  1.313e+05   -3.263   0.0011\n", "log_project_commits           5.540e-03  3.252e-04  1.118e+05   17.037  < 2e-16\n", "log_project_contributors     -2.460e-02  3.414e-04  1.066e+05  -72.052  < 2e-16\n", "log_project_age               4.546e-03  2.689e-04  1.290e+05   16.905  < 2e-16\n", "is_post_treatment:is_treated -2.650e-03  4.362e-04  1.303e+06   -6.076 1.23e-09\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment               \n", "is_treated                   ** \n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.421                                                 \n", "is_treated  -0.682  0.256                                          \n", "lg_prjct_cm -0.113 -0.012  0.083                                   \n", "lg_prjct_cn  0.050 -0.009 -0.132 -0.607                            \n", "log_prjct_g  0.122 -0.085 -0.028 -0.112      -0.353                \n", "is_pst_tr:_  0.254 -0.616 -0.360 -0.002      -0.013       0.019    "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["            R2m       R2c\n", "[1,] 0.02209389 0.2731404\n"]}], "source": ["model_pr_accept_rate_1 <- lmer(\n", "  log_pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_pr_accept_rate_1 <- vif(model_pr_accept_rate_1)\n", "print(vif_model_pr_accept_rate_1)\n", "# Summary of the model\n", "summary(model_pr_accept_rate_1)\n", "# Calculate R-squared values\n", "r_squared_values_pr_accept_rate <- r.squaredGLMM(model_pr_accept_rate_1)\n", "print(r_squared_values_pr_accept_rate)"]}, {"cell_type": "code", "execution_count": 7, "id": "f0f2e44b", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                                           GVIF\n", "is_post_treatment                                                      1.634175\n", "is_treated                                                             1.190358\n", "log_project_commits                                                    2.103941\n", "log_project_contributors                                               2.426257\n", "log_project_age                                                        1.495861\n", "is_post_treatment:is_treated:log_tenure_c                              2.076516\n", "is_post_treatment:is_treated:log_commit_percent_c                      3.182761\n", "is_post_treatment:is_treated:log_commits_c                             4.593924\n", "is_post_treatment:is_treated:log_newcomers                             4.102335\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      6.577650\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 3.787670\n", "is_post_treatment:is_treated:log_project_age_before_treatment          2.078143\n", "is_post_treatment:is_treated:project_main_language                     1.478124\n", "is_post_treatment:is_treated:growth_phase                              4.715671\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure_c                               1\n", "is_post_treatment:is_treated:log_commit_percent_c                       1\n", "is_post_treatment:is_treated:log_commits_c                              1\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "is_post_treatment:is_treated:growth_phase                               4\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.278349\n", "is_treated                                                                    1.091035\n", "log_project_commits                                                           1.450497\n", "log_project_contributors                                                      1.557645\n", "log_project_age                                                               1.223054\n", "is_post_treatment:is_treated:log_tenure_c                                     1.441012\n", "is_post_treatment:is_treated:log_commit_percent_c                             1.784029\n", "is_post_treatment:is_treated:log_commits_c                                    2.143344\n", "is_post_treatment:is_treated:log_newcomers                                    2.025422\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             2.564693\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        1.946194\n", "is_post_treatment:is_treated:log_project_age_before_treatment                 1.441576\n", "is_post_treatment:is_treated:project_main_language                            1.021947\n", "is_post_treatment:is_treated:growth_phase                                     1.213928\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in summary.merMod(as(object, \"lmerMod\"), ...):\n", "“additional arguments ignored”\n", "\n", "Correlation matrix not shown by default, as p = 26 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_post_treatment:is_treated:log_tenure_c + is_post_treatment:is_treated:log_commit_percent_c +  \n", "    is_post_treatment:is_treated:log_commits_c + is_post_treatment:is_treated:log_newcomers +  \n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:project_main_language + is_post_treatment:is_treated:growth_phase +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "       AIC        BIC     logLik   deviance   df.resid \n", "-1693949.5 -1693597.6   847003.7 -1694007.5    1372068 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3875 -0.2068  0.2417  0.4908  3.5764 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.004700 0.06856 \n", " time_cohort_effect (Intercept) 0.000427 0.02066 \n", " Residual                       0.014859 0.12190 \n", "Number of obs: 1372097, groups:  \n", "repo_cohort_effect, 134248; time_cohort_effect, 132911\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             6.154e-01\n", "is_post_treatment                                                      -2.373e-05\n", "is_treated                                                             -1.598e-03\n", "log_project_commits                                                     5.283e-03\n", "log_project_contributors                                               -2.455e-02\n", "log_project_age                                                         4.571e-03\n", "is_post_treatment:is_treated:log_tenure_c                               1.471e-03\n", "is_post_treatment:is_treated:log_commit_percent_c                      -3.518e-03\n", "is_post_treatment:is_treated:log_commits_c                              5.036e-05\n", "is_post_treatment:is_treated:log_newcomers                              4.767e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       3.004e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.048e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.013e-04\n", "is_post_treatment:is_treated:project_main_language1                    -3.804e-03\n", "is_post_treatment:is_treated:project_main_language2                    -4.557e-05\n", "is_post_treatment:is_treated:project_main_language3                     4.908e-03\n", "is_post_treatment:is_treated:project_main_language4                     2.077e-03\n", "is_post_treatment:is_treated:project_main_language5                    -1.952e-03\n", "is_post_treatment:is_treated:project_main_language6                    -1.262e-03\n", "is_post_treatment:is_treated:project_main_language7                     5.036e-04\n", "is_post_treatment:is_treated:project_main_language8                     1.145e-03\n", "is_post_treatment:is_treated:project_main_language9                     6.656e-04\n", "is_post_treatment:is_treated:growth_phase1                             -4.416e-03\n", "is_post_treatment:is_treated:growth_phase2                             -3.669e-03\n", "is_post_treatment:is_treated:growth_phase3                             -2.877e-03\n", "is_post_treatment:is_treated:growth_phase4                              1.739e-02\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             3.662e-04\n", "is_post_treatment                                                       3.287e-04\n", "is_treated                                                              5.061e-04\n", "log_project_commits                                                     3.348e-04\n", "log_project_contributors                                                3.516e-04\n", "log_project_age                                                         2.728e-04\n", "is_post_treatment:is_treated:log_tenure_c                               4.679e-04\n", "is_post_treatment:is_treated:log_commit_percent_c                       7.428e-04\n", "is_post_treatment:is_treated:log_commits_c                              6.511e-04\n", "is_post_treatment:is_treated:log_newcomers                              2.948e-04\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       7.371e-04\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  5.513e-04\n", "is_post_treatment:is_treated:log_project_age_before_treatment           4.500e-04\n", "is_post_treatment:is_treated:project_main_language1                     8.214e-04\n", "is_post_treatment:is_treated:project_main_language2                     1.325e-03\n", "is_post_treatment:is_treated:project_main_language3                     1.350e-03\n", "is_post_treatment:is_treated:project_main_language4                     1.033e-03\n", "is_post_treatment:is_treated:project_main_language5                     8.950e-04\n", "is_post_treatment:is_treated:project_main_language6                     9.755e-04\n", "is_post_treatment:is_treated:project_main_language7                     1.351e-03\n", "is_post_treatment:is_treated:project_main_language8                     7.243e-04\n", "is_post_treatment:is_treated:project_main_language9                     1.387e-03\n", "is_post_treatment:is_treated:growth_phase1                              7.721e-04\n", "is_post_treatment:is_treated:growth_phase2                              1.038e-03\n", "is_post_treatment:is_treated:growth_phase3                              7.968e-04\n", "is_post_treatment:is_treated:growth_phase4                              2.847e-03\n", "                                                                               df\n", "(Intercept)                                                             1.487e+05\n", "is_post_treatment                                                       8.831e+04\n", "is_treated                                                              1.309e+05\n", "log_project_commits                                                     1.260e+05\n", "log_project_contributors                                                1.193e+05\n", "log_project_age                                                         1.427e+05\n", "is_post_treatment:is_treated:log_tenure_c                               2.682e+05\n", "is_post_treatment:is_treated:log_commit_percent_c                       3.290e+05\n", "is_post_treatment:is_treated:log_commits_c                              2.198e+05\n", "is_post_treatment:is_treated:log_newcomers                              2.197e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       2.210e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  2.060e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.267e+05\n", "is_post_treatment:is_treated:project_main_language1                     2.565e+05\n", "is_post_treatment:is_treated:project_main_language2                     2.497e+05\n", "is_post_treatment:is_treated:project_main_language3                     2.335e+05\n", "is_post_treatment:is_treated:project_main_language4                     2.264e+05\n", "is_post_treatment:is_treated:project_main_language5                     2.099e+05\n", "is_post_treatment:is_treated:project_main_language6                     2.229e+05\n", "is_post_treatment:is_treated:project_main_language7                     2.643e+05\n", "is_post_treatment:is_treated:project_main_language8                     2.471e+05\n", "is_post_treatment:is_treated:project_main_language9                     2.247e+05\n", "is_post_treatment:is_treated:growth_phase1                              4.076e+05\n", "is_post_treatment:is_treated:growth_phase2                              2.957e+05\n", "is_post_treatment:is_treated:growth_phase3                              3.886e+05\n", "is_post_treatment:is_treated:growth_phase4                              4.769e+05\n", "                                                                        t value\n", "(Intercept)                                                            1680.231\n", "is_post_treatment                                                        -0.072\n", "is_treated                                                               -3.158\n", "log_project_commits                                                      15.778\n", "log_project_contributors                                                -69.832\n", "log_project_age                                                          16.760\n", "is_post_treatment:is_treated:log_tenure_c                                 3.145\n", "is_post_treatment:is_treated:log_commit_percent_c                        -4.736\n", "is_post_treatment:is_treated:log_commits_c                                0.077\n", "is_post_treatment:is_treated:log_newcomers                                1.617\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         0.408\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment   -1.902\n", "is_post_treatment:is_treated:log_project_age_before_treatment             0.670\n", "is_post_treatment:is_treated:project_main_language1                      -4.631\n", "is_post_treatment:is_treated:project_main_language2                      -0.034\n", "is_post_treatment:is_treated:project_main_language3                       3.635\n", "is_post_treatment:is_treated:project_main_language4                       2.012\n", "is_post_treatment:is_treated:project_main_language5                      -2.181\n", "is_post_treatment:is_treated:project_main_language6                      -1.294\n", "is_post_treatment:is_treated:project_main_language7                       0.373\n", "is_post_treatment:is_treated:project_main_language8                       1.580\n", "is_post_treatment:is_treated:project_main_language9                       0.480\n", "is_post_treatment:is_treated:growth_phase1                               -5.720\n", "is_post_treatment:is_treated:growth_phase2                               -3.536\n", "is_post_treatment:is_treated:growth_phase3                               -3.610\n", "is_post_treatment:is_treated:growth_phase4                                6.107\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                      0.942440\n", "is_treated                                                             0.001587\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure_c                              0.001661\n", "is_post_treatment:is_treated:log_commit_percent_c                      2.18e-06\n", "is_post_treatment:is_treated:log_commits_c                             0.938343\n", "is_post_treatment:is_treated:log_newcomers                             0.105934\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      0.683611\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment 0.057234\n", "is_post_treatment:is_treated:log_project_age_before_treatment          0.503160\n", "is_post_treatment:is_treated:project_main_language1                    3.64e-06\n", "is_post_treatment:is_treated:project_main_language2                    0.972557\n", "is_post_treatment:is_treated:project_main_language3                    0.000278\n", "is_post_treatment:is_treated:project_main_language4                    0.044272\n", "is_post_treatment:is_treated:project_main_language5                    0.029177\n", "is_post_treatment:is_treated:project_main_language6                    0.195663\n", "is_post_treatment:is_treated:project_main_language7                    0.709369\n", "is_post_treatment:is_treated:project_main_language8                    0.114007\n", "is_post_treatment:is_treated:project_main_language9                    0.631324\n", "is_post_treatment:is_treated:growth_phase1                             1.07e-08\n", "is_post_treatment:is_treated:growth_phase2                             0.000407\n", "is_post_treatment:is_treated:growth_phase3                             0.000306\n", "is_post_treatment:is_treated:growth_phase4                             1.01e-09\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                         \n", "is_treated                                                             ** \n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure_c                              ** \n", "is_post_treatment:is_treated:log_commit_percent_c                      ***\n", "is_post_treatment:is_treated:log_commits_c                                \n", "is_post_treatment:is_treated:log_newcomers                                \n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment .  \n", "is_post_treatment:is_treated:log_project_age_before_treatment             \n", "is_post_treatment:is_treated:project_main_language1                    ***\n", "is_post_treatment:is_treated:project_main_language2                       \n", "is_post_treatment:is_treated:project_main_language3                    ***\n", "is_post_treatment:is_treated:project_main_language4                    *  \n", "is_post_treatment:is_treated:project_main_language5                    *  \n", "is_post_treatment:is_treated:project_main_language6                       \n", "is_post_treatment:is_treated:project_main_language7                       \n", "is_post_treatment:is_treated:project_main_language8                       \n", "is_post_treatment:is_treated:project_main_language9                       \n", "is_post_treatment:is_treated:growth_phase1                             ***\n", "is_post_treatment:is_treated:growth_phase2                             ***\n", "is_post_treatment:is_treated:growth_phase3                             ***\n", "is_post_treatment:is_treated:growth_phase4                             ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.0221981</td><td>0.273052</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.0221981 & 0.273052\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.0221981 | 0.273052 |\n", "\n"], "text/plain": ["     R2m       R2c     \n", "[1,] 0.0221981 0.273052"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "model_pr_accept_rate_4 <- lmer(\n", "  log_pull_request_success_rate ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "\n", "    # 三重交互项（标准化后）\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "# 计算VIF（使用car包改进方法）\n", "vif_model_pr_accept_rate_4 <- car::vif(\n", "  model_pr_accept_rate_4,  # 使用lmer模型\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model_pr_accept_rate_4)\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_pr_accept_rate_4) %>% plot()\n", "# performance::model_performance(model_pr_accept_rate_4) %>% print()\n", "# 模型总结（优化显示）\n", "summary(model_pr_accept_rate_4,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_pr_accept_rate_4, # 使用lmer模型\n", "  null = lmer(log_pull_request_success_rate ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n", "# 计算模型的AIC值"]}, {"cell_type": "code", "execution_count": 8, "id": "3420c149", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 26 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n", "\n", "Correlation matrix not shown by default, as p = 26 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n"]}], "source": ["# 保存和打印以上所有模型结果到文件中，命名格式为日期_模型名称.txt\n", "# 目录在“../result/did_result_20250408/”下\n", "# 需要确保该目录存在\n", "output_dir <- \"../result/did_result_20250408/\"\n", "if (!dir.exists(output_dir)) {\n", "  dir.create(output_dir, recursive = TRUE)\n", "}\n", "# 保存结果包括模型摘要、VIF和R-squared\n", "save_model_results <- function(model, model_name) {\n", "  # 创建文件名\n", "  file_name <- paste0(output_dir, Sys.Date(), \"_\", model_name, \".txt\")\n", "  \n", "  # 打开文件连接\n", "  sink(file_name)\n", "  \n", "  # 打印模型摘要\n", "  cat(\"Model Summary:\\n\")\n", "  print(summary(model))\n", "  \n", "  # 打印VIF\n", "  cat(\"\\nVIF:\\n\")\n", "  print(vif(model))\n", "  \n", "  # 打印R-squared\n", "  cat(\"\\nR-squared:\\n\")\n", "  print(r.squaredGLMM(model))\n", "  \n", "  # 关闭文件连接\n", "  sink()\n", "}\n", "# 保存模型结果\n", "save_model_results(model_time_to_merge_1, \"model_time_to_merge_1\")\n", "save_model_results(model_time_to_merge_2, \"model_time_to_merge_2\")\n", "save_model_results(model_pr_accept_rate_1, \"model_pr_accept_rate_1\")\n", "save_model_results(model_pr_accept_rate_4, \"model_pr_accept_rate_4\")"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 5}