import pandas as pd

# Load the data
productivity_data_20250730 = pd.read_csv('../result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365.csv')
productivity_data_20250227 = pd.read_csv('../result/p_test.csv')

print("Original data shapes:")
print(f"productivity_data_20250227: {productivity_data_20250227.shape}")
print(f"productivity_data_20250730: {productivity_data_20250730.shape}")

# Merge productivity_data_20250227 and productivity_data_20250730 according to specifications:
# 1. Use repo_name and standardized_time_weeks as index, keep only productivity_20250227
# 2. Add columns from productivity_data_20250730 that don't exist in productivity_20250227
# 3. For common columns, fill missing values in productivity_20250227 with values from productivity_data_20250730

# Set the index for both dataframes
productivity_20250227_indexed = productivity_data_20250227.set_index(['repo_name', 'standardized_time_weeks'])
productivity_20250730_indexed = productivity_data_20250730.set_index(['repo_name', 'standardized_time_weeks'])

# Get columns that exist in 20250730 but not in 20250227
new_columns = [col for col in productivity_data_20250730.columns 
               if col not in productivity_data_20250227.columns]

print(f"\nNew columns to add: {new_columns}")

# Get common columns (excluding the index columns)
common_columns = [col for col in productivity_data_20250227.columns 
                  if col in productivity_data_20250730.columns and 
                  col not in ['repo_name', 'standardized_time_weeks']]

print(f"Common columns to merge: {len(common_columns)} columns")

# Start with productivity_20250227 as the base
merged_data = productivity_20250227_indexed.copy()

# Add new columns from productivity_data_20250730
for col in new_columns:
    merged_data[col] = productivity_20250730_indexed[col]

# For common columns, fill missing values in productivity_20250227 with values from productivity_data_20250730
for col in common_columns:
    # Get the corresponding column from 20250730
    col_20250730 = productivity_20250730_indexed[col]
    
    # Fill missing values in merged_data with values from 20250730
    merged_data[col] = merged_data[col].fillna(col_20250730)

# Reset index to get back to normal dataframe format
final_merged_data = merged_data.reset_index()

print(f"\nFinal merged data shape: {final_merged_data.shape}")
print(f"Original productivity_data_20250227 shape: {productivity_data_20250227.shape}")
print(f"Original productivity_data_20250730 shape: {productivity_data_20250730.shape}")

# Display column information
print(f"\nColumns in final merged data: {len(final_merged_data.columns)}")
print(f"Columns in productivity_data_20250227: {len(productivity_data_20250227.columns)}")
print(f"Columns in productivity_data_20250730: {len(productivity_data_20250730.columns)}")

# Show the first few rows to verify the merge
print("\nFirst few rows of merged data:")
print(final_merged_data.head())

# Save the merged data
output_path = '../result/20250730_merged_productivity_data.csv'
final_merged_data.to_csv(output_path, index=False)
print(f"\nMerged data saved to: {output_path}")

# Summary statistics
print(f"\nSummary of merge operation:")
print(f"- Base dataset: productivity_data_20250227 ({productivity_data_20250227.shape[0]} rows, {productivity_data_20250227.shape[1]} columns)")
print(f"- Additional columns added: {len(new_columns)}")
print(f"- Common columns processed: {len(common_columns)}")
print(f"- Final dataset: {final_merged_data.shape[0]} rows, {final_merged_data.shape[1]} columns") 