import logging
import pandas as pd
import os
from datetime import datetime
import time

# Configure logging
log_file = '../logs/merge_attrition_burst.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# ATTRITION_LIMITS = [180, 270, 450]
ATTRITION_LIMITS = [365, 180, 270, 450]

def merge_attrition_bursts(df):
    """
    Merge records within the same burst if there are multiple attrition events.
    For each group (same repo_name and burst):
        - If there are multiple records:
            - Set attrition_date as the latest (maximum) time.
            - Compute tenure as the average.
            - Sum commit_percent and commits.
        - Otherwise, keep the record unchanged.
    Returns a new DataFrame with the merged results.
    """
    start_time = time.time()
    logging.info("Starting burst merging process...")
    
    original_count = len(df)
    merged_records = []
    grouped = df.groupby(['repo_name', 'burst'])
    total_groups = len(grouped)
    
    logging.info(f"Processing {total_groups} burst groups for merging")
    
    processed_groups = 0
    merged_groups = 0
    
    for (repo, burst), group in grouped:
        processed_groups += 1
        
        if processed_groups % 100 == 0 or processed_groups == total_groups:  # Log every 100 groups
            logging.info(f"Processing burst group {processed_groups}/{total_groups}: {repo} - burst {burst}")
        
        if len(group) > 1:
            merged = group.iloc[0].copy()  # copy common info
            merged['attrition_date'] = group['attrition_date'].max()
            merged['tenure'] = group['tenure'].mean()
            merged['commit_percent'] = group['commit_percent'].sum()
            merged['commits'] = group['commits'].sum()
            merged_records.append(merged)
            merged_groups += 1
        else:
            merged_records.append(group.iloc[0])
    
    result_df = pd.DataFrame(merged_records)
    
    processing_time = time.time() - start_time
    logging.info(f"Burst merging completed in {processing_time:.2f} seconds")
    logging.info(f"Merged {merged_groups} out of {total_groups} burst groups")
    logging.info(f"Final merged dataset contains {len(result_df)} records (from {original_count} original records)")
    
    return result_df

def generate_merged_attrition_burst():
    """
    For each attrition limit, read attrition burst data from CSV files, merge records within bursts,
    and generate corresponding CSV files with merged results.
    """
    overall_start_time = time.time()
    logging.info("=" * 60)
    logging.info("STARTING ATTRITION BURST MERGING")
    logging.info("=" * 60)
    
    # Define input and output directories
    input_dir = '../data/attrition_csv'
    output_dir = '../data/attrition_csv'
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    logging.info(f"Output directory ensured: {output_dir}")

    total_limits = len(ATTRITION_LIMITS)
    
    for limit_idx, limit in enumerate(ATTRITION_LIMITS, 1):
        limit_start_time = time.time()
        input_file = f"{input_dir}/attrition_burst_core_dev_{limit}.csv"
        
        logging.info("-" * 40)
        logging.info(f"PROCESSING LIMIT {limit_idx}/{total_limits}: {limit}")
        logging.info("-" * 40)
        
        # Check if input file exists
        if not os.path.exists(input_file):
            logging.warning(f"Input file not found: {input_file}")
            logging.info(f"Skipping limit {limit} due to missing input file")
            continue
        
        # Read CSV file
        logging.info(f"Reading input file: {input_file}")
        try:
            df = pd.read_csv(input_file)
            logging.info(f"✓ Successfully loaded {len(df)} records from {input_file}")
        except Exception as e:
            logging.error(f"✗ Error reading file {input_file}: {e}")
            logging.info(f"Skipping limit {limit} due to file reading error")
            continue
        
        # Verify required columns exist
        required_columns = ['repo_name', 'burst', 'attrition_date', 'tenure', 'commit_percent', 'commits']
        # rename core_dev into attrition_developer
        df = df.rename(columns={'core_dev': 'attrition_developer'})
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"✗ Missing required columns in {input_file}: {missing_columns}")
            logging.info(f"Skipping limit {limit} due to missing columns")
            continue
        
        logging.info(f"✓ All required columns present: {required_columns}")
        
        # Convert attrition_date to datetime if it's not already
        if df['attrition_date'].dtype == 'object':
            df['attrition_date'] = pd.to_datetime(df['attrition_date'])
        df = df[df['gap_less_than_84'] == False] 
        # Filter out records with null tenure and convert to int
        original_count = len(df)
        df = df[df['tenure'].notnull()]
        df['tenure'] = df['tenure'].astype(int)
        filtered_count = len(df)
        
        logging.info(f"Filtered records with valid tenure: {original_count} -> {filtered_count}")
        
        if filtered_count == 0:
            logging.warning(f"No records with valid tenure found in {input_file}")
            continue
        
        # Sort by repo_name and attrition_date for better organization
        logging.info("Sorting data by repository name and attrition date...")
        df = df.sort_values(['repo_name', 'attrition_date'])
        
        # Merge records within bursts
        logging.info("Merging records within bursts...")
        df_merged = merge_attrition_bursts(df)
        
        # Save merged data
        merged_output_file = f"{output_dir}/attrition_burst_core_dev_merged_{limit}.csv"
        logging.info(f"Saving merged data to: {merged_output_file}")
        df_merged.to_csv(merged_output_file, index=False)
        logging.info(f"✓ Successfully saved merged file: {merged_output_file} with {len(df_merged)} records")
        
        # Log summary statistics
        burst_count = len(df)
        merged_count = len(df_merged)
        
        limit_processing_time = time.time() - limit_start_time
        logging.info(f"Summary for limit {limit}:")
        logging.info(f"  - Original records: {original_count}")
        logging.info(f"  - Records with valid tenure: {filtered_count}")
        logging.info(f"  - Records with bursts: {burst_count}")
        logging.info(f"  - Records after merging: {merged_count}")
        logging.info(f"  - Processing time: {limit_processing_time:.2f} seconds")
        
        # Calculate progress percentage
        progress = (limit_idx / total_limits) * 100
        logging.info(f"Overall progress: {progress:.1f}% ({limit_idx}/{total_limits} limits completed)")

    overall_processing_time = time.time() - overall_start_time
    logging.info("=" * 60)
    logging.info("ATTRITION BURST MERGING COMPLETED")
    logging.info(f"Total processing time: {overall_processing_time:.2f} seconds")
    logging.info("=" * 60)

def main():
    logging.info(f"Script started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    generate_merged_attrition_burst()
    logging.info(f"Script completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
