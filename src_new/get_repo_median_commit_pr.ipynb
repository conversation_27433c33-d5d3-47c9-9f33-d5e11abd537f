{"cells": [{"cell_type": "code", "execution_count": 1, "id": "98952bcc", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "40f56631-308f-433d-8bb6-2c2ddb1652a8", "rows": [["0", "sparklemotion/nokogiri"], ["1", "davidb/scala-maven-plugin"], ["2", "tcurdt/jdeb"], ["3", "junit-team/junit4"], ["4", "yui/yuicompressor"], ["5", "unclebob/fitnesse"], ["6", "connectbot/connectbot"], ["7", "bpellin/keepassdroid"], ["8", "rnewson/couchdb-lucene"], ["9", "nodebox/nodebox"], ["10", "cwensel/cascading"], ["11", "cucumber-attic/cuke4duke"], ["12", "bndtools/bndtools"], ["13", "twitter4j/twitter4j"], ["14", "magro/memcached-session-manager"], ["15", "caelum/vraptor"], ["16", "maxcom/lorsource"], ["17", "rzwitserloot/lombok"], ["18", "voldemort/voldemort"], ["19", "jdbi/jdbi"], ["20", "simpligility/android-maven-plugin"], ["21", "jblas-project/jblas"], ["22", "pocmo/yaaic"], ["23", "ccw-ide/ccw"], ["24", "novoda/android-demos"], ["25", "qos-ch/logback"], ["26", "qos-ch/slf4j"], ["27", "apache/shiro"], ["28", "haraldk/twelvemonkeys"], ["29", "fusesource/jansi"], ["30", "yaxim-org/yaxim"], ["31", "cucumber-attic/gherkin2"], ["32", "talklittle/reddit-is-fun"], ["33", "twilio/twilio-java"], ["34", "webmetrics/browsermob-proxy"], ["35", "sitemesh/sitemesh2"], ["36", "sirthias/parboiled"], ["37", "resty-gwt/resty-gwt"], ["38", "maven-nar/nar-maven-plugin"], ["39", "martint/jmxutils"], ["40", "jruby/joni"], ["41", "sanger-pathogens/artemis"], ["42", "notnoop/java-apns"], ["43", "trifork/erjang"], ["44", "torquebox/jruby-maven-plugins"], ["45", "jhy/jsoup"], ["46", "jberkel/sms-backup-plus"], ["47", "ervandew/eclim"], ["48", "sbt/junit-interface"], ["49", "j<PERSON><PERSON><PERSON>/ognl"]], "shape": {"columns": 1, "rows": 50541}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>davidb/scala-maven-plugin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>tcurdt/jdeb</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>junit-team/junit4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>yui/yuicompressor</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50536</th>\n", "      <td>wso2/product-micro-integrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50537</th>\n", "      <td>mir-evaluation/mir_eval</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50538</th>\n", "      <td>graphql-hive/console</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50539</th>\n", "      <td>pixi-viewport/pixi-viewport</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50540</th>\n", "      <td>motiondivision/motion</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>50541 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                           repo_name\n", "0             sparklemotion/nokogiri\n", "1          davidb/scala-maven-plugin\n", "2                        tcurdt/jdeb\n", "3                  junit-team/junit4\n", "4                  yui/yuicompressor\n", "...                              ...\n", "50536  wso2/product-micro-integrator\n", "50537        mir-evaluation/mir_eval\n", "50538           graphql-hive/console\n", "50539    pixi-viewport/pixi-viewport\n", "50540          motiondivision/motion\n", "\n", "[50541 rows x 1 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "repo_names = pd.read_csv('../result/repo_name_list.csv')\n", "repo_names"]}, {"cell_type": "code", "execution_count": null, "id": "39b903a7", "metadata": {}, "outputs": [], "source": ["# generate a dataframe with repo_name, number of commits, number of prs\n", "df_repo =  "]}, {"cell_type": "code", "execution_count": null, "id": "dfe52a70", "metadata": {}, "outputs": [], "source": ["# get number of commits for repos\n", "for repo_name in repo_names:\n", "    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}