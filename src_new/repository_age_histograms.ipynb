{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Repository Age Histograms\n", "\n", "This notebook creates two histograms based on repository age data:\n", "1. Contributor counts by repository age (in years) for attrition and engagement events\n", "2. Unique repository counts by repository age (in years) for disengagement and engagement events"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==============================================================================\n", "# Part 1: IMPORTS AND SETUP\n", "# ==============================================================================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "\n", "# Set up plotting style\n", "sns.set(style=\"whitegrid\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 12\n", "\n", "# Create output directory\n", "os.makedirs('../figures/final_figure', exist_ok=True)\n", "print(\"Setup complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==============================================================================\n", "# Part 2: DATA LOADING\n", "# ==============================================================================\n", "\n", "print(\"Loading data files...\")\n", "\n", "# Load data files\n", "try:\n", "    repo_info = pd.read_csv('../data/sample_projects_total.csv')\n", "    attritions_raw = pd.read_csv('../result/attritions.csv')\n", "    newcomers_raw = pd.read_csv('../data/newcomer_contributors.csv')\n", "    core_developer_list = pd.read_csv('../data/core_developer_list_total_repo.csv')\n", "    \n", "    print(\"✓ All files loaded successfully\")\n", "    print(f\"Repository info: {len(repo_info)} repos\")\n", "    print(f\"Attritions: {len(attritions_raw)} events\")\n", "    print(f\"Newcomers: {len(newcomers_raw)} events\")\n", "    \n", "except FileNotFoundError as e:\n", "    print(f\"Error loading data: {e}\")\n", "    print(\"Please ensure all source CSV files are in the correct paths.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==============================================================================\n", "# Part 3: DATA PREPROCESSING\n", "# ==============================================================================\n", "\n", "print(\"Preprocessing data...\")\n", "\n", "# Filter newcomers to only include core developers\n", "core_developer_list['core_developers'] = core_developer_list['core_developers'].apply(\n", "    lambda x: x[1:-1].replace(\"'\", \"\").split(', ')\n", ")\n", "core_developer_list = core_developer_list.explode('core_developers')\n", "core_developer_list['core_developers'] = core_developer_list['core_developers'].str.strip()\n", "newcomers_core = newcomers_raw[newcomers_raw['login'].isin(core_developer_list['core_developers'])].copy()\n", "\n", "print(f\"Filtered to {len(newcomers_core)} core developer newcomers\")\n", "\n", "# Convert date columns\n", "attritions_raw['attrition_date'] = pd.to_datetime(attritions_raw['attrition_date']).dt.tz_localize(None)\n", "newcomers_core['date'] = pd.to_datetime(newcomers_core['date']).dt.tz_localize(None)\n", "repo_info['createdAt'] = pd.to_datetime(repo_info['createdAt']).dt.tz_localize(None)\n", "\n", "# Calculate repository age in years for attrition events\n", "attritions_with_age = pd.merge(\n", "    attritions_raw, \n", "    repo_info[['name', 'createdAt']], \n", "    left_on='repo_name', \n", "    right_on='name', \n", "    how='left'\n", ")\n", "attritions_with_age['repo_age_years'] = (\n", "    attritions_with_age['attrition_date'] - attritions_with_age['createdAt']\n", ").dt.days / 365.25\n", "\n", "# Calculate repository age in years for engagement events\n", "newcomers_with_age = pd.merge(\n", "    newcomers_core, \n", "    repo_info[['name', 'createdAt']], \n", "    left_on='repo_name', \n", "    right_on='name', \n", "    how='left'\n", ")\n", "newcomers_with_age['repo_age_years'] = (\n", "    newcomers_with_age['date'] - newcomers_with_age['createdAt']\n", ").dt.days / 365.25\n", "\n", "# Remove any invalid ages (negative values)\n", "attritions_with_age = attritions_with_age[attritions_with_age['repo_age_years'] >= 0]\n", "newcomers_with_age = newcomers_with_age[newcomers_with_age['repo_age_years'] >= 0]\n", "\n", "print(\"✓ Data preprocessing complete\")\n", "print(f\"Attrition events: {len(attritions_with_age)}\")\n", "print(f\"Engagement events: {len(newcomers_with_age)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Overview\n", "Let's examine the age distributions before creating the histograms."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary statistics\n", "print(\"AGE DISTRIBUTION SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "attrition_ages = attritions_with_age['repo_age_years'].values\n", "engagement_ages = newcomers_with_age['repo_age_years'].values\n", "\n", "print(\"DISENGAGEMENT EVENTS:\")\n", "print(f\"  Count: {len(attrition_ages):,}\")\n", "print(f\"  Mean: {attrition_ages.mean():.2f} years\")\n", "print(f\"  Median: {np.median(attrition_ages):.2f} years\")\n", "print(f\"  Min: {attrition_ages.min():.2f} years\")\n", "print(f\"  Max: {attrition_ages.max():.2f} years\")\n", "\n", "print(\"ENGAGEMENT EVENTS:\")\n", "print(f\"  Count: {len(engagement_ages):,}\")\n", "print(f\"  Mean: {engagement_ages.mean():.2f} years\")\n", "print(f\"  Median: {np.median(engagement_ages):.2f} years\")\n", "print(f\"  Min: {engagement_ages.min():.2f} years\")\n", "print(f\"  Max: {engagement_ages.max():.2f} years\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Histogram 1: Contributor Counts by Repository Age"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==============================================================================\n", "# Part 4: HISTOGRAM 1 - CONT<PERSON><PERSON><PERSON>OR COUNTS BY REPOSITORY AGE\n", "# ==============================================================================\n", "\n", "print(\"Creating contributor counts histogram...\")\n", "\n", "# Create figure\n", "fig, ax = plt.subplots(1, 1, figsize=(14, 8))\n", "\n", "# Set up bins (0-20 years, 1-year bins)\n", "bins = np.arange(0, 21, 1)\n", "\n", "# Create histograms\n", "ax.hist(attrition_ages, bins=bins, alpha=0.7, color='tab:red', \n", "        label='Disengagement', edgecolor='black', linewidth=0.5)\n", "ax.hist(engagement_ages, bins=bins, alpha=0.7, color='tab:blue', \n", "        label='Engagement', edgecolor='black', linewidth=0.5)\n", "\n", "# Customize plot\n", "ax.set_xlabel('Repository Age (years)', fontsize=14, weight='bold')\n", "ax.set_ylabel('Number of Contributors', fontsize=14, weight='bold')\n", "ax.set_title('Distribution of Contributors by Repository Age at Event Time', fontsize=16, weight='bold')\n", "ax.legend(fontsize=12)\n", "ax.grid(True, alpha=0.3)\n", "ax.set_xticks(bins[::2])\n", "\n", "# Add vertical line at mean values\n", "ax.axvline(attrition_ages.mean(), color='darkred', linestyle='--', linewidth=2, alpha=0.7)\n", "ax.axvline(engagement_ages.mean(), color='darkblue', linestyle='--', linewidth=2, alpha=0.7)\n", "\n", "# Add statistics text\n", "stats_text = f\"\"\"\n", "DISENGAGEMENT:\n", "  Count: {len(attrition_ages):,}\n", "  Mean: {attrition_ages.mean():.1f} years\n", "  Median: {np.median(attrition_ages):.1f} years\n", "\n", "ENGAGEMENT:\n", "  Count: {len(engagement_ages):,}\n", "  Mean: {engagement_ages.mean():.1f} years\n", "  Median: {np.median(engagement_ages):.1f} years\n", "\"\"\"\n", "\n", "ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10, \n", "        verticalalignment='top', \n", "        bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightgray\", alpha=0.5))\n", "\n", "plt.tight_layout()\n", "output_path1 = '../figures/final_figure/contributor_age_histogram.png'\n", "plt.savefig(output_path1, dpi=300, bbox_inches='tight')\n", "print(f\"✓ Saved contributor age histogram to: {output_path1}\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Histogram 2: Unique Repository Counts by Repository Age"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==============================================================================\n", "# Part 5: HISTOGRAM 2 - UNIQUE REPOSITORY COUNTS BY REPOSITORY AGE\n", "# ==============================================================================\n", "\n", "print(\"Creating unique repository counts histogram...\")\n", "\n", "# Get unique repositories for each event type\n", "attrition_repos = attritions_with_age[['repo_name', 'repo_age_years']].drop_duplicates()\n", "engagement_repos = newcomers_with_age[['repo_name', 'repo_age_years']].drop_duplicates()\n", "\n", "# Prepare data for plotting\n", "attrition_repo_ages = attrition_repos['repo_age_years'].values\n", "engagement_repo_ages = engagement_repos['repo_age_years'].values\n", "\n", "print(f\"Unique repos with disengagement: {len(attrition_repo_ages)}\")\n", "print(f\"Unique repos with engagement: {len(engagement_repo_ages)}\")\n", "\n", "# Create figure\n", "fig, ax = plt.subplots(1, 1, figsize=(14, 8))\n", "\n", "# Create histograms\n", "ax.hist(attrition_repo_ages, bins=bins, alpha=0.7, color='tab:red', \n", "        label='Disengagement', edgecolor='black', linewidth=0.5)\n", "ax.hist(engagement_repo_ages, bins=bins, alpha=0.7, color='tab:blue', \n", "        label='Engagement', edgecolor='black', linewidth=0.5)\n", "\n", "# Customize plot\n", "ax.set_xlabel('Repository Age (years)', fontsize=14, weight='bold')\n", "ax.set_ylabel('Number of Unique Repositories', fontsize=14, weight='bold')\n", "ax.set_title('Distribution of Unique Repositories by Age at Event Time', fontsize=16, weight='bold')\n", "ax.legend(fontsize=12)\n", "ax.grid(True, alpha=0.3)\n", "ax.set_xticks(bins[::2])\n", "\n", "# Add vertical line at mean values\n", "ax.axvline(attrition_repo_ages.mean(), color='darkred', linestyle='--', linewidth=2, alpha=0.7)\n", "ax.axvline(engagement_repo_ages.mean(), color='darkblue', linestyle='--', linewidth=2, alpha=0.7)\n", "\n", "# Add statistics text\n", "repo_stats_text = f\"\"\"\n", "DISENGAGEMENT:\n", "  Count: {len(attrition_repo_ages):,}\n", "  Mean: {attrition_repo_ages.mean():.1f} years\n", "  Median: {np.median(attrition_repo_ages):.1f} years\n", "\n", "ENGAGEMENT:\n", "  Count: {len(engagement_repo_ages):,}\n", "  Mean: {engagement_repo_ages.mean():.1f} years\n", "  Median: {np.median(engagement_repo_ages):.1f} years\n", "\"\"\"\n", "\n", "ax.text(0.02, 0.98, repo_stats_text, transform=ax.transAxes, fontsize=10, \n", "        verticalalignment='top', \n", "        bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightgray\", alpha=0.5))\n", "\n", "plt.tight_layout()\n", "output_path2 = '../figures/final_figure/repository_age_histogram.png'\n", "plt.savefig(output_path2, dpi=300, bbox_inches='tight')\n", "print(f\"✓ Saved repository age histogram to: {output_path2}\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Additional Analysis: Repository Overlaps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ==============================================================================\n", "# Part 6: ADDITIONAL ANALYSIS\n", "# ==============================================================================\n", "\n", "print(\"ADDITIONAL ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Check for overlapping repositories\n", "attrition_unique_repos = set(attrition_repos['repo_name'])\n", "engagement_unique_repos = set(engagement_repos['repo_name'])\n", "overlap_repos = attrition_unique_repos.intersection(engagement_unique_repos)\n", "\n", "print(\"REPOSITORY OVERLAP ANALYSIS:\")\n", "print(f\"  Total unique repositories with disengagement events: {len(attrition_unique_repos):,}\")\n", "print(f\"  Total unique repositories with engagement events: {len(engagement_unique_repos):,}\")\n", "print(f\"  Repositories with both disengagement and engagement events: {len(overlap_repos):,}\")\n", "print(f\"  Overlap percentage: {len(overlap_repos) / max(len(attrition_unique_repos), len(engagement_unique_repos)) * 100:.1f}%\")\n", "\n", "# Create a Venn diagram\n", "from matplotlib_venn import venn2\n", "\n", "plt.figure(figsize=(8, 8))\n", "venn2([attrition_unique_repos, engagement_unique_repos], \n", "      ('Disengagement Events', 'Engagement Events'))\n", "plt.title('Repository Overlap Between Disengagement and Engagement Events')\n", "venn_path = '../figures/final_figure/repository_overlap_venn.png'\n", "plt.savefig(venn_path, dpi=300, bbox_inches='tight')\n", "print(f\"✓ Saved Venn diagram to: {venn_path}\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optional: Interactive Histogram with <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optional: Create interactive plots with plotly\n", "try:\n", "    import plotly.graph_objects as go\n", "    from plotly.subplots import make_subplots\n", "    \n", "    # Create interactive histogram\n", "    fig = go.Figure()\n", "    \n", "    fig.add_trace(go.Histogram(\n", "        x=attrition_ages,\n", "        name='Disengagement',\n", "        opacity=0.7,\n", "        marker_color='red',\n", "        xbins=dict(start=0, end=20, size=1)\n", "    ))\n", "    \n", "    fig.add_trace(go.Histogram(\n", "        x=engagement_ages,\n", "        name='Engagement',\n", "        opacity=0.7,\n", "        marker_color='blue',\n", "        xbins=dict(start=0, end=20, size=1)\n", "    ))\n", "    \n", "    fig.update_layout(\n", "        title='Interactive: Contributors by Repository Age',\n", "        xaxis_title='Repository Age (years)',\n", "        yaxis_title='Number of Contributors',\n", "        barmode='overlay',\n", "        height=600\n", "    )\n", "    \n", "    fig.show()\n", "    \n", "except ImportError:\n", "    print(\"Plotly not available. Install with: pip install plotly\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook has created:\n", "1. **Contributor Age Histogram**: Shows the distribution of contributor counts by repository age at the time of engagement/disengagement events\n", "2. **Repository Age Histogram**: Shows the distribution of unique repository counts by age at the time of events\n", "3. **Repository Overlap Analysis**: Venn diagram showing overlap between repositories with disengagement vs engagement events\n", "\n", "All figures are saved in `../figures/final_figure/` directory. You can modify the bin sizes, colors, and other parameters above to customize the visualizations."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}