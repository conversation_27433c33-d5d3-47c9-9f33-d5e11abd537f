import logging
from pymongo import MongoClient, UpdateOne
import pandas as pd
from tqdm import tqdm
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import warnings
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/add_multi_attrition_limits_to_db.log", mode="w", encoding="utf-8"),
    ],
)

ATTRITION_LIMITS = [180, 270, 365, 450]
PROCESSED_COMMITS_DIR = "../data/processed_commits"

# 连接 MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client['disengagement']
collection = db['project_analysis']

def get_processed_commit_file_repo_name(repo_name):
    output_path = os.path.join(PROCESSED_COMMITS_DIR, f"{repo_name.replace('/', '_')}_processed_commits.csv")
    if not os.path.exists(output_path):
        raise FileNotFoundError(f"Processed commit file not found: {output_path}")
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
    return repo_commit

# 读取所有文档
docs = list(collection.find({}))
logging.info(f"Total documents to process: {len(docs)}")

# 按 repo_name 分组
d_repo = defaultdict(list)
for doc in docs:
    repo_name = doc.get("repo_name")
    if repo_name:
        d_repo[repo_name].append(doc)

def process_repo(repo_name, docs):
    try:
        repo_commits = get_processed_commit_file_repo_name(repo_name)
        repo_commits['date'] = pd.to_datetime(repo_commits['date'], format='mixed')
        project_last_commit_date = pd.to_datetime(repo_commits['date']).max()
        if project_last_commit_date is not None:
            project_last_commit_date = project_last_commit_date.tz_localize(None)
    except Exception as e:
        logging.error(f"Error reading commits for {repo_name}: {e}")
        return 0
    updates = []
    for doc in docs:
        update_fields = {}
        breaks = doc.get("Breaks", [])
        core_dev_login = doc.get("core_dev_login")
        if not core_dev_login:
            continue
        dev_commits = repo_commits[repo_commits['author_login'] == core_dev_login]
        dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')
        dev_commits = dev_commits.sort_values(by='date')
        if dev_commits.empty:
            last_commit_date = None
        else:
            last_commit_date = dev_commits['date'].max()
        if last_commit_date is not None:
            last_commit_date = last_commit_date.tz_localize(None)
        
        # 计算pauses，只有当pauses数量大于零时才进行attrition检查
        def get_pauses_with_commit(dev_commits, window_size=1):
            pauses = {}
            pause_id = 0
            for i in range(1, len(dev_commits)):
                days_diff = (dev_commits.iloc[i]['date'] - dev_commits.iloc[i - 1]['date']).days
                units_diff = days_diff / window_size
                # if units_diff > 0, then it is a pause
                if units_diff > 0:
                    pauses[pause_id] = {
                        'start_date': dev_commits.iloc[i - 1]['date'],
                        'end_date': dev_commits.iloc[i]['date'],
                        'duration_units': units_diff
                    }
                    pause_id += 1
            return pauses
        
        pauses = get_pauses_with_commit(dev_commits)
        
        # 只有当存在pauses时才处理attrition，与原始版本保持一致
        if len(pauses) > 0:
            for limit in ATTRITION_LIMITS:
                attrition_dates = []
                
                # 检查breaks中超过阈值的
                for b in breaks:
                    if b.get("duration_units", 0) >= limit:
                        attrition_dates.append(str(b.get("start_date")))
                
                # 检查项目级别的attrition：开发者最后提交与项目最后提交的时间差
                # 这个检查只在存在pauses时进行，与原始版本保持一致
                if last_commit_date is not None and project_last_commit_date is not None:
                    if (project_last_commit_date - last_commit_date).days > limit:
                        attrition_dates.append(str(last_commit_date.date()))
                
                # 去重
                attrition_dates = list(sorted(set(attrition_dates)))
                if attrition_dates:
                    update_fields[f"Attrition_{limit}"] = {"attrition_date": attrition_dates}
                else:
                    update_fields[f"Attrition_{limit}"] = None
        else:
            # 如果pauses为空，所有attrition都设为None
            for limit in ATTRITION_LIMITS:
                update_fields[f"Attrition_{limit}"] = None
                
        if update_fields:
            updates.append(UpdateOne({"_id": doc["_id"]}, {"$set": update_fields}))
    if updates:
        collection.bulk_write(updates, ordered=False)
    return len(updates)

# 并发处理所有 repo
with ThreadPoolExecutor(max_workers=32) as executor:
    futures = {executor.submit(process_repo, repo, docs): repo for repo, docs in d_repo.items()}
    for f in tqdm(as_completed(futures), total=len(futures), desc="All repos (parallel)"):
        repo = futures[f]
        try:
            updated = f.result()
            if updated:
                logging.info(f"Repo {repo}: updated {updated} docs.")
        except Exception as e:
            logging.error(f"Error processing repo {repo}: {e}")

logging.info("All repositories processed.") 