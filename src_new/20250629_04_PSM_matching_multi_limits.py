from random import sample
import pandas as pd
import os
import logging
import numpy as np
from sklearn.neighbors import NearestNeighbors
import gc
import psutil
import pickle
from typing import Dict, List, Set, Tuple, Generator
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import time

# Define limits to process
# ATTRITION_LIMITS = [365, 180, 270, 450]
ATTRITION_LIMITS = [180, 270, 365, 450]
# ATTRITION_LIMITS = [365]

# Get number of CPU cores
N_CPUS = multiprocessing.cpu_count()
print(f"Available CPU cores: {N_CPUS}")

def setup_logging_for_limit(limit):
    """Set up independent logging configuration for each limit"""
    log_dir = f"../logs/limit_{limit}"
    os.makedirs(log_dir, exist_ok=True)
    
    # Create independent log file
    log_file = os.path.join(log_dir, f"psm_matching_limit{limit}.log")
    
    # Configure logger
    logger = logging.getLogger(f'limit_{limit}')
    logger.setLevel(logging.INFO)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Add file handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s")
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

def memory_monitor(func):
    """监控函数执行期间的内存使用情况的装饰器"""
    def wrapper(*args, **kwargs):
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        result = func(*args, **kwargs)
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        return result
    return wrapper

def get_control_candidates(
    control_data: pd.DataFrame,
    treatment_time: float,
    time_window: float = 3.0
) -> pd.DataFrame:
    """获取时间窗口内的控制组候选"""
    mask = (
        (control_data["standardized_time_weeks"] >= treatment_time - time_window) &
        (control_data["standardized_time_weeks"] <= treatment_time + time_window)
    )
    return control_data[mask]

def batch_process(
    array: np.ndarray,
    batch_size: int = 1000
) -> Generator[np.ndarray, None, None]:
    """批量处理数组的生成器"""
    for i in range(0, len(array), batch_size):
        yield array[i:i + batch_size]

@memory_monitor
def compile_control_group_psm_knn(
    treatment_repos_with_left_date: pd.DataFrame,
    candidate_repos: Set[str],
    productivity_metric_data: pd.DataFrame,
    n_neighbors: int,
    timewindow_weeks: int,
    feature_columns: List[str],
    extra_candidates: int = 10,
    batch_size: int = 1000,
    window_size: int = 12,
    limit: int = None,
    logger=None
) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
    """
    完全按照原始PSM_matching_20250311.py的逻辑实现PSM-KNN匹配
    """
    if logger is None:
        logger = setup_logging_for_limit(limit)
    
    logger.info("Starting PSM with NearestNeighbors...")
    
    # 初始化匹配结果
    matched_pairs = {}
    processed_count = 0
    
    # 预处理控制组数据
    available_controls = set(candidate_repos)
    control_data = productivity_metric_data[
        productivity_metric_data["repo_name"].isin(available_controls)
    ].copy()
    
    # 预计算控制组离职时间
    control_treatment_weeks = (
        productivity_metric_data[
            (productivity_metric_data["someone_left"] == 1) &
            (productivity_metric_data["repo_name"].isin(available_controls))
        ]
        .groupby("repo_name")["standardized_time_weeks"]
        .apply(sorted)
        .to_dict()
    )

    # 创建(burst, repo_name)作为唯一标识
    treatment_repos_with_left_date['item_key'] = treatment_repos_with_left_date['burst'].astype(str) + '_' + treatment_repos_with_left_date['repo_name']
    
    # 处理所有treatment项目
    treatment_repos_to_process = treatment_repos_with_left_date
    total_items = len(treatment_repos_to_process)
    logger.info(f"Total items to process: {total_items}")

    # 直接处理每个(burst, repo)组合
    for i, row in treatment_repos_to_process.iterrows():
        burst = row['burst']
        t_repo = row['repo_name']
        item_key = row['item_key']
            
        # 获取treatment数据
        treatment_mask = (
            (productivity_metric_data["repo_name"] == t_repo) &
            (productivity_metric_data["someone_left"] == 1) &
            (productivity_metric_data["burst"] == burst)
        )
        treatment_features = productivity_metric_data[treatment_mask]
        
        if treatment_features.empty:
            logger.warning(f"No treatment features found for burst:{burst} repo:{t_repo}")
            continue

        # 只需要一行数据，通常是最早的离职时间点
        t_row = treatment_features.iloc[0]
        t_time = t_row["standardized_time_weeks"]
            
        # 获取时间窗口内的控制组候选
        control_candidates = get_control_candidates(control_data, t_time, timewindow_weeks)
        if len(control_candidates) < n_neighbors:
            logger.warning(f"Insufficient candidates for burst:{burst} repo:{t_repo} at time {t_time}")
            continue

        # 移除NaN值并构建特征矩阵
        control_candidates = control_candidates.dropna(subset=feature_columns)
        if control_candidates.empty:
            logger.warning(f"No valid control candidates for burst:{burst} repo:{t_repo}")
            continue
            
        logger.info(f"Processing burst:{burst} repo:{t_repo} in time:{t_time} with {len(control_candidates)} candidates ({processed_count + 1}/{total_items})")
        X_control = control_candidates[feature_columns].values
        X_treatment = t_row[feature_columns].values.reshape(1, -1)

        # KNN匹配
        nn_model = NearestNeighbors(
            n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
            algorithm='auto'
        )
        nn_model.fit(X_control)
        
        matched_controls = []
        used_repos = set()

        # 获取近邻
        distances, indices = nn_model.kneighbors(
            X_treatment,
            n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
            return_distance=True
        )

        # 处理匹配结果
        for idx in indices[0]:
            if len(matched_controls) >= n_neighbors:
                break

            control_row = control_candidates.iloc[idx]
            control_repo = control_row["repo_name"]
            control_time = control_row["standardized_time_weeks"]

            # 跳过已使用的仓库
            if control_repo in used_repos:
                continue

            # 验证时间窗口约束
            treatment_weeks = control_treatment_weeks.get(control_repo, [])
            if treatment_weeks:
                window_start = control_time - timewindow_weeks
                window_end = control_time + timewindow_weeks
                import bisect
                left = bisect.bisect_left(treatment_weeks, window_start)
                right = bisect.bisect_right(treatment_weeks, window_end)
                if left < right:
                    continue

            matched_controls.append({
                "repo_name": control_repo,
                "matched_time": control_time,
                "features": control_row[feature_columns].values
            })
            used_repos.add(control_repo)

        # 保存匹配结果
        if matched_controls:
            matched_pairs[item_key] = {
                "burst": burst,
                "repo_name": t_repo,
                "treatment_time": t_time,
                "controls": matched_controls,
                "treatment_features": t_row[feature_columns].values,
                "control_features": np.array([c["features"] for c in matched_controls])
            }
            processed_count += 1
            logger.info(f"Matched burst:{burst} repo:{t_repo} with {len(matched_controls)} controls. Total: {processed_count}")
        else:
            logger.warning(f"No valid controls for burst:{burst} repo:{t_repo}")
        
        # 定期清理内存
        if i % 1000 == 0:
            gc.collect()

    logger.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")
    return matched_pairs, treatment_features, control_data

def compile_data_from_matched_pairs(
    matched_pairs: Dict,
    productivity: pd.DataFrame,
    window_size: int,
    batch_size: int = 1000,
    log_interval: int = 1000
) -> pd.DataFrame:
    """从匹配结果编译数据 - 优化版本"""
    # 获取logger
    logger = logging.getLogger()
    logger.info(f"Starting compilation of {len(matched_pairs)} matched pairs")
    
    # 为提高性能, 先对productivity按repo_name进行分组
    repo_groups = dict(tuple(productivity.groupby('repo_name')))
    
    # 批处理键值以减少内存使用
    all_keys = list(matched_pairs.keys())
    total_batches = (len(all_keys) + batch_size - 1) // batch_size
    
    all_data = []
    processed = 0
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_keys))
        batch_keys = all_keys[start_idx:end_idx]
        
        batch_data = []
        
        for item_key in batch_keys:
            matched_data = matched_pairs[item_key]
            burst_id = matched_data["burst"]
            repo_name = matched_data["repo_name"]
            treatment_time = matched_data["treatment_time"]
            control_groups = matched_data["controls"]
            
            # 只在repo_groups里有数据时处理
            if repo_name in repo_groups:
                # 处理treatment数据 - 使用预先分组的数据
                repo_data = repo_groups[repo_name]
                treatment_mask = (
                    (repo_data['standardized_time_weeks'] >= treatment_time - window_size) & 
                    (repo_data['standardized_time_weeks'] <= treatment_time + window_size)
                )
                treatment_productivity = repo_data[treatment_mask].copy()
                
                if not treatment_productivity.empty:
                    treatment_productivity['relativized_time'] = treatment_productivity['standardized_time_weeks'] - treatment_time
                    treatment_productivity['is_treated'] = 1
                    treatment_productivity['post_treatment'] = treatment_productivity['relativized_time'] > 0
                    treatment_productivity['cohort_id'] = processed
                    batch_data.append(treatment_productivity)
                
                    # 处理control数据
                    for c in control_groups:
                        control_repo = c['repo_name']
                        control_time = c['matched_time']
                        
                        if control_repo in repo_groups:
                            control_repo_data = repo_groups[control_repo]
                            control_mask = (
                                (control_repo_data['standardized_time_weeks'] >= control_time - window_size) & 
                                (control_repo_data['standardized_time_weeks'] <= control_time + window_size)
                            )
                            control_data = control_repo_data[control_mask].copy()
                            
                            if not control_data.empty:
                                control_data['relativized_time'] = control_data['standardized_time_weeks'] - control_time
                                control_data['is_treated'] = 0
                                control_data['post_treatment'] = control_data['relativized_time'] > 0
                                control_data['cohort_id'] = processed
                                batch_data.append(control_data)
                    
                    processed += 1
            
            # 每处理一定数量记录日志
            if processed % log_interval == 0:
                logger.info(f"Processed {processed}/{len(matched_pairs)} matched pairs")
        
        # 合并当前批次并添加到结果
        if batch_data:
            batch_result = pd.concat(batch_data)
            all_data.append(batch_result)
            
            # 清理内存
            del batch_data
            gc.collect()
            
        logger.info(f"Completed batch {batch_idx+1}/{total_batches}, total processed: {processed}")
    
    # 合并所有批次数据
    if all_data:
        logger.info(f"Concatenating all {len(all_data)} batches")
        final_data = pd.concat(all_data)
        logger.info(f"Final compiled data shape: {final_data.shape}")
        return final_data
    else:
        logger.warning("No data to compile")
        return pd.DataFrame()

def process_single_limit(limit):
    """处理单个limit的PSM匹配 - 完全按照原始逻辑"""
    # 设置独立的logger
    logger = setup_logging_for_limit(limit)
    
    start_time = time.time()
    print(f"\n{'='*60}")
    print(f"STARTING PSM MATCHING FOR LIMIT: {limit}")
    print(f"{'='*60}")
    logger.info(f"Starting PSM matching for limit: {limit}")
    
    # 设置输出目录
    input_dir = f'../result/20250730_did_result/'
    output_dir = f'../result/20250730_did_result_psm_matching_not_equal_0_5/'
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取对应limit的生产力数据
    productivity_file = f'{input_dir}productivity_with_propensity_scores_with_attritions_{limit}.csv'
    
    if not os.path.exists(productivity_file):
        print(f"Error: Productivity file not found: {productivity_file}")
        logger.error(f"Productivity file not found: {productivity_file}")
        return
    
    print(f"Reading productivity data from: {productivity_file}")
    logger.info(f"Reading productivity data from: {productivity_file}")
    productivity = pd.read_csv(productivity_file)
    print(f"Loaded productivity data shape: {productivity.shape}")
    logger.info(f"Loaded productivity data shape: {productivity.shape}")
    
    # 转换类型
    productivity["standardized_time_weeks"] = productivity["standardized_time_weeks"].astype(int)
    
    # 获取attrition数据 - 完全按照原始逻辑
    window_sizes = [12]
    for window in window_sizes:
        attritions = productivity[productivity['someone_left'] == 1].copy()
        attritions = attritions[attritions[f'feature_sigmod_add'].notnull()]
        attritions = attritions[attritions[f'feature_sigmod_add'] != 0.5]
        print(f"In window {window}, attritions shape is {attritions.shape}")
        logger.info(f"In window {window}, attritions shape is {attritions.shape}")
        
        # 转换类型
        if 'burst' in attritions.columns:
            attritions['burst'] = attritions['burst'].astype(int)

        # 调用PSM函数进行匹配 - 完全按照原始逻辑
        matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
            attritions,
            set(productivity['repo_name']),
            productivity,
            n_neighbors=5,
            timewindow_weeks=window,
            feature_columns=[f'feature_sigmod_add'],
            window_size=window,
            limit=limit,
            logger=logger
        )

        logger.info(f"Finished PSM matching with {len(matched_pairs)} matched pairs, starting to compile data...")
        # INSERT_YOUR_CODE
        # --- 保存matched_pairs的中间结果 ---
        matched_pairs_file = output_dir + f"matched_pairs_limit{limit}_window{window}.pkl"
        try:
            import pickle
            with open(matched_pairs_file, "wb") as f:
                pickle.dump(matched_pairs, f)
            logger.info(f"Matched pairs saved to {matched_pairs_file}")
        except Exception as e:
            logger.error(f"Failed to save matched_pairs to {matched_pairs_file}: {e}")
        # 使用独立函数编译数据
        compiled_data = compile_data_from_matched_pairs(
            matched_pairs, 
            productivity, 
            window,
            batch_size=1000,
            log_interval=1000
        )
        
        if not compiled_data.empty:
            compiled_data_test = compiled_data.copy()
            compiled_data_test['is_post_treatment'] = compiled_data_test['post_treatment'].astype(int)
            compiled_data_test['is_treated'] = compiled_data_test['is_treated'].astype(int)
            compiled_data_test['is_treated_post_treatment'] = compiled_data_test['is_treated'] * compiled_data_test['post_treatment']

            output_file = output_dir + f"compiled_data_test_limit{limit}.csv"
            compiled_data_test.to_csv(output_file, index=False)
            print(f"Limit {limit}: Compiled data saved to {output_file}")
            logger.info(f"Compiled data saved to {output_file}")
        else:
            print(f"Limit {limit}: No compiled data")
            logger.warning(f"No compiled data for limit {limit}")
    
    total_time = time.time() - start_time
    print(f"\n{'='*60}")
    print(f"COMPLETED PSM MATCHING FOR LIMIT: {limit}")
    print(f"Total processing time: {total_time:.1f} seconds")
    print(f"{'='*60}\n")
    logger.info(f"Completed PSM matching for limit: {limit}, Total processing time: {total_time:.1f} seconds")
    
    # 清理内存
    del productivity, attritions, matched_pairs, compiled_data
    gc.collect()
    
    return limit, total_time

def main():
    """主函数，并行处理所有attrition limits"""
    overall_start_time = time.time()
    print(f"\n{'='*80}")
    print("STARTING PARALLEL PSM MATCHING FOR MULTIPLE LIMITS")
    print(f"Processing limits: {ATTRITION_LIMITS}")
    print(f"Using {min(len(ATTRITION_LIMITS), N_CPUS)} processes")
    print(f"{'='*80}")
    
    # 使用ProcessPoolExecutor进行并行处理
    max_workers = min(len(ATTRITION_LIMITS), N_CPUS)
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_limit = {executor.submit(process_single_limit, limit): limit for limit in ATTRITION_LIMITS}
        
        # 收集结果
        completed_limits = []
        for future in as_completed(future_to_limit):
            limit = future_to_limit[future]
            try:
                limit_result, processing_time = future.result()
                completed_limits.append((limit_result, processing_time))
                print(f"Limit {limit_result} completed in {processing_time:.1f}s")
            except Exception as exc:
                print(f"Limit {limit} generated an exception: {exc}")
    
    # 按原始顺序排序结果
    completed_limits.sort(key=lambda x: ATTRITION_LIMITS.index(x[0]))
    
    overall_processing_time = time.time() - overall_start_time
    print(f"\n{'='*80}")
    print("PARALLEL PSM MATCHING FOR ALL LIMITS COMPLETED")
    print(f"Total processing time: {overall_processing_time:.1f} seconds")
    print("Individual processing times:")
    for limit, processing_time in completed_limits:
        print(f"  Limit {limit}: {processing_time:.1f}s")
    print(f"{'='*80}")

if __name__ == "__main__":
    print(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    main()
    print(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}") 