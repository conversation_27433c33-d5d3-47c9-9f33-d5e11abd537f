import logging
import pandas as pd
from pymongo import MongoClient
import os

# Configure logging
log_file = '../logs/generate_attrition_csv.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# ATTRITION_LIMITS = [180, 270, 450]
ATTRITION_LIMITS = [365, 180, 270, 450]

def generate_attrition_csv_from_mongo():
    """
    For each attrition limit, extract attrition data from the MongoDB collection 
    repo_breaks_attritions_{limit} and generate corresponding attrition_{limit}.csv files.
    """
    # Connect to MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]

    # Create output directory if it doesn't exist
    output_dir = '../data/attrition_csv'
    os.makedirs(output_dir, exist_ok=True)

    for limit in ATTRITION_LIMITS:
        collection_name = f"repo_breaks_attritions_{limit}"
        output_file = f"{output_dir}/attrition_{limit}.csv"
        
        logging.info(f"Processing attrition data for limit: {limit}")
        
        # Get the collection
        collection = db[collection_name]
        
        # Initialize list to store all attrition records
        all_attritions = []
        
        # Iterate through all repos in the collection
        for repo_doc in collection.find():
            repo_name = repo_doc.get("repo_name", "unknown")
            attritions = repo_doc.get("attritions", [])
            
            logging.info(f"Processing repo: {repo_name} with {len(attritions)} attrition records")
            
            # Process each attrition record
            for attrition in attritions:
                attrition_record = {
                    "repo_name": repo_name,
                    "attrition_id": attrition.get("id"),
                    "attrition_time": attrition.get("attrition_time"),
                    "dev_login": attrition.get("dev_login")
                }
                all_attritions.append(attrition_record)
        
        # Create DataFrame and save to CSV
        if all_attritions:
            df = pd.DataFrame(all_attritions)
            
            # Sort by repo_name and attrition_time for better organization
            df = df.sort_values(['repo_name', 'attrition_time'])
            
            # Save to CSV
            df.to_csv(output_file, index=False)
            logging.info(f"Successfully generated {output_file} with {len(df)} attrition records")
        else:
            logging.warning(f"No attrition data found for limit {limit}")
            # Create empty CSV with headers
            empty_df = pd.DataFrame(columns=["repo_name", "attrition_id", "attrition_time", "dev_login"])
            empty_df.to_csv(output_file, index=False)
            logging.info(f"Created empty CSV file: {output_file}")

def main():
    generate_attrition_csv_from_mongo()

if __name__ == "__main__":
    main() 