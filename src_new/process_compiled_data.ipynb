{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import logging\n", "from sklearn.preprocessing import MinMaxScaler, StandardScaler\n", "from sklearn.neighbors import NearestNeighbors\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import matplotlib.pyplot as plt\n", "from itertools import product\n", "\n", "# Set up logging\n", "log_dir = \"../logs\"\n", "os.makedirs(log_dir, exist_ok=True)\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s\",\n", "    handlers=[\n", "        logging.FileHandler(os.path.join(log_dir, \"psm_2025_0408.log\")),\n", "        logging.StreamHandler(),\n", "    ],\n", ")\n", "\n", "output_dir = \"../result/did_result_20250408/\"\n", "# output_dir = \"../result/did_result_20250312/\"\n", "os.makedirs(output_dir, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import logging\n", "from sklearn.preprocessing import MinMaxScaler, StandardScaler\n", "from sklearn.neighbors import NearestNeighbors\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import matplotlib.pyplot as plt\n", "from itertools import product\n", "\n", "# Set up logging\n", "log_dir = \"../logs\"\n", "os.makedirs(log_dir, exist_ok=True)\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s\",\n", "    handlers=[\n", "        logging.FileHandler(os.path.join(log_dir, \"psm_2025_0408.log\")),\n", "        logging.StreamHandler(),\n", "    ],\n", ")\n", "\n", "output_dir = \"../result/did_result_20250408/\"\n", "# output_dir = \"../result/did_result_20250312/\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "compiled_data_test = pd.read_csv(output_dir + \"compiled_data_test_12_time_to_merge.csv\")\n", "compiled_data_test['log_pr_throughput'] = np.log(compiled_data_test['pr_throughput'] + 1)\n", "compiled_data_test['log_pull_request_success_rate'] = np.log(compiled_data_test['pull_request_success_rate'] + 1)\n", "compiled_data_test['log_time_to_merge'] = np.log(compiled_data_test['time_to_merge'] + 1)\n", "compiled_data_test['log_project_commits'] = np.log(compiled_data_test['project_commits'] + 1)\n", "compiled_data_test['log_project_contributors'] = np.log(compiled_data_test['project_contributors'] + 1)\n", "compiled_data_test['log_project_age'] = np.log(compiled_data_test['project_age'] + 1)\n", "# compiled_data_test['log_newcomers'] = np.log(compiled_data_test['newcomers'] + 1)\n", "# Create time-cohort and repo-cohort effects\n", "compiled_data_test['time_cohort_effect'] = compiled_data_test['is_post_treatment'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)\n", "compiled_data_test['repo_cohort_effect'] = compiled_data_test['is_treated'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "2ddc39de-c068-4491-9d71-b9d4d37074f2", "rows": [["0", "01mf02/jaq", "646", "2023-01-22", "0.0", "1.0", "2.4025", "634", "8", "265", "Rust", "0.5", "0.5", "0.5", "0.5", "0", null, null, null, null, null, "-12", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "1.224510455543484", "6.453624998892692", "2.1972245773362196", "5.583496308781699", "0_0", "1_0"], ["1", "01mf02/jaq", "647", "2023-01-29", "1.0", "1.0", "169.8895", "634", "8", "272", "Rust", "0.6666666666666666", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-11", "1", "False", "0", "0", "0", "0.6931471805599453", "0.6931471805599453", "5.141017148795798", "6.453624998892692", "2.1972245773362196", "5.60947179518496", "0_0", "1_0"], ["2", "01mf02/jaq", "648", "2023-02-05", "0.0", "1.0", "169.8895", "634", "8", "279", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-10", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.141017148795798", "6.453624998892692", "2.1972245773362196", "5.634789603169249", "0_0", "1_0"], ["3", "01mf02/jaq", "649", "2023-02-12", "0.0", "1.0", "169.8895", "642", "8", "286", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-9", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.141017148795798", "6.466144724237619", "2.1972245773362196", "5.659482215759621", "0_0", "1_0"], ["4", "01mf02/jaq", "650", "2023-02-19", "0.0", "1.0", "169.8895", "642", "8", "293", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-8", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.141017148795798", "6.466144724237619", "2.1972245773362196", "5.683579767338681", "0_0", "1_0"], ["5", "01mf02/jaq", "651", "2023-02-26", "1.0", "1.0", "18.3589", "644", "9", "300", "Rust", "0.6666666666666666", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-7", "1", "False", "0", "0", "0", "0.6931471805599453", "0.6931471805599453", "2.9631522620523985", "6.4692503167957724", "2.302585092994046", "5.707110264748875", "0_0", "1_0"], ["6", "01mf02/jaq", "652", "2023-03-05", "0.0", "1.0", "18.3589", "645", "9", "307", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-6", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "2.9631522620523985", "6.470799503782602", "2.302585092994046", "5.730099782973574", "0_0", "1_0"], ["7", "01mf02/jaq", "653", "2023-03-12", "0.0", "1.0", "18.3589", "656", "9", "314", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-5", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "2.9631522620523985", "6.48768401848461", "2.302585092994046", "5.752572638825633", "0_0", "1_0"], ["8", "01mf02/jaq", "654", "2023-03-19", "0.0", "1.0", "18.3589", "663", "10", "321", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-4", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "2.9631522620523985", "6.498282149476434", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0"], ["9", "01mf02/jaq", "655", "2023-03-26", "2.0", "1.0", "25.7632", "677", "10", "328", "Rust", "0.75", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-3", "1", "False", "0", "0", "0", "1.0986122886681098", "0.6931471805599453", "3.287027809575607", "6.519147287940395", "2.3978952727983707", "5.796057750765372", "0_0", "1_0"], ["10", "01mf02/jaq", "656", "2023-04-02", "0.0", "1.0", "25.7632", "681", "10", "335", "Rust", "0.5", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-2", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "3.287027809575607", "6.525029657843462", "2.3978952727983707", "5.817111159963204", "0_0", "1_0"], ["11", "01mf02/jaq", "657", "2023-04-09", "1.0", "1.0", "14.9636", "720", "11", "342", "Rust", "0.6666666666666666", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-1", "1", "False", "0", "0", "0", "0.6931471805599453", "0.6931471805599453", "2.770311130495723", "6.580639137284949", "2.4849066497880004", "5.8377304471659395", "0_0", "1_0"], ["12", "01mf02/jaq", "658", "2023-04-16", "0.0", "1.0", "14.9636", "765", "11", "349", "Rust", "0.5", "0.5", "1.0", "1.0", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "0", "1", "False", "0", "0", "0", "0.0", "0.6931471805599453", "2.770311130495723", "6.641182169740591", "2.4849066497880004", "5.857933154483459", "0_0", "1_0"], ["13", "01mf02/jaq", "659", "2023-04-23", "0.0", "1.0", "14.9636", "768", "12", "356", "Rust", "0.3333333333333333", "0.5", "0.2874472028479966", "0.1678472064195771", "0", null, null, null, null, null, "1", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0"], ["14", "01mf02/jaq", "660", "2023-04-30", "0.0", "1.0", "14.9636", "768", "12", "363", "Rust", "0.5", "0.5", "0.2874472028479966", "0.2874472028479966", "0", null, null, null, null, null, "2", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0"], ["15", "01mf02/jaq", "661", "2023-05-07", "0.0", "1.0", "14.9636", "768", "12", "370", "Rust", "0.5", "0.5", "0.2874472028479966", "0.2874472028479966", "0", null, null, null, null, null, "3", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0"], ["16", "01mf02/jaq", "662", "2023-05-14", "0.0", "1.0", "14.9636", "768", "12", "377", "Rust", "0.5", "0.5", "0.2874472028479966", "0.2874472028479966", "0", null, null, null, null, null, "4", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0"], ["17", "01mf02/jaq", "663", "2023-05-21", "0.0", "1.0", "14.9636", "768", "12", "384", "Rust", "0.3333333333333333", "0.5", "0.4960304382979785", "0.3298141471271717", "0", null, null, null, null, null, "5", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0"], ["18", "01mf02/jaq", "664", "2023-05-28", "0.0", "1.0", "14.9636", "768", "12", "391", "Rust", "0.5", "0.5", "0.4960304382979785", "0.4960304382979785", "0", null, null, null, null, null, "6", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0"], ["19", "01mf02/jaq", "665", "2023-06-04", "0.0", "1.0", "14.9636", "768", "12", "398", "Rust", "0.5", "0.5", "0.4960304382979785", "0.4960304382979785", "0", null, null, null, null, null, "7", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "5.988961416889864", "1_0", "1_0"], ["20", "01mf02/jaq", "666", "2023-06-11", "0.0", "1.0", "14.9636", "768", "12", "405", "Rust", "0.5", "0.5", "0.4960304382979785", "0.4960304382979785", "0", null, null, null, null, null, "8", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.645090969505644", "2.5649493574615367", "6.0063531596017325", "1_0", "1_0"], ["21", "01mf02/jaq", "667", "2023-06-18", "0.0", "1.0", "14.9636", "769", "13", "412", "Rust", "0.25", "0.5", "0.3967112964891203", "0.1797858054862284", "0", null, null, null, null, null, "9", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.646390514847729", "2.6390573296152584", "6.023447592961033", "1_0", "1_0"], ["22", "01mf02/jaq", "668", "2023-06-25", "0.0", "1.0", "14.9636", "777", "13", "419", "Rust", "0.5", "0.5", "0.3967112964891203", "0.3967112964891203", "0", null, null, null, null, null, "10", "1", "True", "0", "1", "1", "0.0", "0.6931471805599453", "2.770311130495723", "6.656726524178391", "2.6390573296152584", "6.040254711277414", "1_0", "1_0"], ["23", "01mf02/jaq", "669", "2023-07-02", "7.0", "1.0", "361.1883", "799", "14", "426", "Rust", "0.8", "0.5", "0.9999999999105904", "0.9999999999776475", "0", null, null, null, null, null, "11", "1", "True", "0", "1", "1", "2.0794415416798357", "0.6931471805599453", "5.8921642423323215", "6.684611727667927", "2.70805020110221", "6.056784013228625", "1_0", "1_0"], ["24", "01mf02/jaq", "670", "2023-07-09", "1.0", "1.0", "351.6383", "825", "14", "433", "Rust", "0.6666666666666666", "0.5", "0.999999999908195", "0.9999999999540974", "0", null, null, null, null, null, "12", "1", "True", "0", "1", "1", "0.6931471805599453", "0.6931471805599453", "5.865442885732476", "6.716594773520978", "2.70805020110221", "6.073044534100405", "1_0", "1_0"], ["25", "0LNetworkCommunity/libra-legacy-v6", "645", "2023-01-15", "0.0", "1.0", "204.1507", "7008", "158", "972", "Rust", "0.5000000000000001", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-12", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.323744831019834", "8.854950316500325", "5.0689042022202315", "6.880384082186005", "0_0", "0_0"], ["26", "0LNetworkCommunity/libra-legacy-v6", "646", "2023-01-22", "0.0", "1.0", "204.1507", "7008", "158", "979", "Rust", "0.5000000000000001", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-11", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.323744831019834", "8.854950316500325", "5.0689042022202315", "6.887552571664617", "0_0", "0_0"], ["27", "0LNetworkCommunity/libra-legacy-v6", "647", "2023-01-29", "2.0", "0.3333", "634.6174", "7008", "158", "986", "Rust", "0.7500000000000002", "0.3392361593612251", "1.0", "1.0", "0", null, null, null, null, null, "-10", "0", "False", "0", "0", "0", "1.0986122886681098", "0.28765707213927566", "6.454596809995845", "8.854950316500325", "5.0689042022202315", "6.894670039433482", "0_0", "0_0"], ["28", "0LNetworkCommunity/libra-legacy-v6", "648", "2023-02-05", "1.0", "0.3333", "6061.3735", "7008", "158", "993", "Rust", "0.4000000000000001", "0.5208129664845008", "0.9999667653897512", "0.9999541330742432", "0", null, null, null, null, null, "-9", "0", "False", "0", "0", "0", "0.6931471805599453", "0.28765707213927566", "8.70985666904866", "8.854950316500325", "5.0689042022202315", "6.901737206656574", "0_0", "0_0"], ["29", "0LNetworkCommunity/libra-legacy-v6", "649", "2023-02-12", "3.0", "0.3333", "239.1875", "7008", "158", "1000", "Rust", "0.8000000000000002", "0.5208129664845008", "0.999913159138964", "0.9999800236361512", "0", null, null, null, null, null, "-8", "0", "False", "0", "0", "0", "1.3862943611198906", "0.28765707213927566", "5.481419868325062", "8.854950316500325", "5.0689042022202315", "6.90875477931522", "0_0", "0_0"], ["30", "0LNetworkCommunity/libra-legacy-v6", "650", "2023-02-19", "1.0", "1.0", "0.0275", "7008", "158", "1007", "Rust", "0.6666666666666667", "0.679178699175393", "0.9997640044122702", "0.999944251739846", "0", null, null, null, null, null, "-7", "0", "False", "0", "0", "0", "0.6931471805599453", "0.6931471805599453", "0.027128667388252696", "8.854950316500325", "5.0689042022202315", "6.915723448631314", "0_0", "0_0"], ["31", "0LNetworkCommunity/libra-legacy-v6", "651", "2023-02-26", "1.0", "0.3333", "258.8997", "7008", "158", "1014", "Rust", "0.6666666666666667", "0.3392361593612251", "1.0", "1.0", "0", null, null, null, null, null, "-6", "0", "False", "0", "0", "0", "0.6931471805599453", "0.28765707213927566", "5.560295787356667", "8.854950316500325", "5.0689042022202315", "6.922643891475888", "0_0", "0_0"], ["32", "0LNetworkCommunity/libra-legacy-v6", "652", "2023-03-05", "0.0", "0.3333", "258.8997", "7008", "158", "1021", "Rust", "0.5000000000000001", "0.3392361593612251", "1.0", "1.0", "0", null, null, null, null, null, "-5", "0", "False", "0", "0", "0", "0.0", "0.28765707213927566", "5.560295787356667", "8.854950316500325", "5.0689042022202315", "6.92951677076365", "0_0", "0_0"], ["33", "0LNetworkCommunity/libra-legacy-v6", "653", "2023-03-12", "0.0", "0.3333", "258.8997", "7008", "158", "1028", "Rust", "0.5000000000000001", "0.3392361593612251", "1.0", "1.0", "0", null, null, null, null, null, "-4", "0", "False", "0", "0", "0", "0.0", "0.28765707213927566", "5.560295787356667", "8.854950316500325", "5.0689042022202315", "6.9363427358340495", "0_0", "0_0"], ["34", "0LNetworkCommunity/libra-legacy-v6", "654", "2023-03-19", "0.0", "0.3333", "258.8997", "7008", "158", "1035", "Rust", "0.2500000000000001", "0.4584212410648376", "1.0", "1.0", "0", null, null, null, null, null, "-3", "0", "False", "0", "0", "0", "0.0", "0.28765707213927566", "5.560295787356667", "8.854950316500325", "5.0689042022202315", "6.943122422819428", "0_0", "0_0"], ["35", "0LNetworkCommunity/libra-legacy-v6", "655", "2023-03-26", "1.0", "0.5", "564.5242", "7008", "158", "1042", "Rust", "0.6666666666666667", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "-2", "0", "False", "0", "0", "0", "0.6931471805599453", "0.4054651081081644", "6.33775308862816", "8.854950316500325", "5.0689042022202315", "6.949856455000773", "0_0", "0_0"], ["36", "0LNetworkCommunity/libra-legacy-v6", "656", "2023-04-02", "0.0", "0.5", "564.5242", "7008", "158", "1049", "Rust", "0.5000000000000001", "0.3775406687981454", "1.0", "1.0", "0", null, null, null, null, null, "-1", "0", "False", "0", "0", "0", "0.0", "0.4054651081081644", "6.33775308862816", "8.854950316500325", "5.0689042022202315", "6.956545443151569", "0_0", "0_0"], ["37", "0LNetworkCommunity/libra-legacy-v6", "657", "2023-04-09", "2.0", "1.0", "170.8628", "7008", "158", "1056", "Rust", "0.7500000000000002", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "0", "0", "False", "0", "0", "0", "1.0986122886681098", "0.6931471805599453", "5.146696484083326", "8.854950316500325", "5.0689042022202315", "6.963189985870238", "0_0", "0_0"], ["38", "0LNetworkCommunity/libra-legacy-v6", "658", "2023-04-16", "1.0", "1.0", "1378.7114", "7008", "158", "1063", "Rust", "0.6666666666666667", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "1", "0", "True", "0", "1", "0", "0.6931471805599453", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "6.96979066990159", "1_0", "0_0"], ["39", "0LNetworkCommunity/libra-legacy-v6", "659", "2023-04-23", "0.0", "1.0", "1378.7114", "7008", "158", "1070", "Rust", "0.25", "0.6607638406387748", "1.0", "1.0", "0", null, null, null, null, null, "2", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "6.976348070447749", "1_0", "0_0"], ["40", "0LNetworkCommunity/libra-legacy-v6", "660", "2023-04-30", "0.0", "1.0", "1378.7114", "7008", "158", "1077", "Rust", "0.3333333333333334", "0.6607638406387748", "1.0", "1.0", "0", null, null, null, null, null, "3", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "6.982862751468942", "1_0", "0_0"], ["41", "0LNetworkCommunity/libra-legacy-v6", "661", "2023-05-07", "0.0", "1.0", "1378.7114", "7008", "158", "1084", "Rust", "0.2", "0.6607638406387748", "1.0", "1.0", "0", null, null, null, null, null, "4", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "6.98933526597456", "1_0", "0_0"], ["42", "0LNetworkCommunity/libra-legacy-v6", "662", "2023-05-14", "0.0", "1.0", "1378.7114", "7008", "158", "1091", "Rust", "0.3333333333333334", "0.5", "1.0", "1.0", "0", null, null, null, null, null, "5", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "6.9957661563048505", "1_0", "0_0"], ["43", "0LNetworkCommunity/libra-legacy-v6", "663", "2023-05-21", "0.0", "1.0", "1378.7114", "7008", "158", "1098", "Rust", "0.3333333333333334", "0.6607638406387748", "0.9994753505126488", "0.999461297475194", "0", null, null, null, null, null, "6", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.002155954403621", "1_0", "0_0"], ["44", "0LNetworkCommunity/libra-legacy-v6", "664", "2023-05-28", "0.0", "1.0", "1378.7114", "7008", "158", "1105", "Rust", "0.5000000000000001", "0.6607638406387748", "0.9994753505126488", "0.9997305761679476", "0", null, null, null, null, null, "7", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.00850518208228", "1_0", "0_0"], ["45", "0LNetworkCommunity/libra-legacy-v6", "665", "2023-06-04", "0.0", "1.0", "1378.7114", "7008", "158", "1112", "Rust", "0.5000000000000001", "0.6607638406387748", "0.9994753505126488", "0.9997305761679476", "0", null, null, null, null, null, "8", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.014814351275545", "1_0", "0_0"], ["46", "0LNetworkCommunity/libra-legacy-v6", "666", "2023-06-11", "0.0", "1.0", "1378.7114", "7008", "158", "1119", "Rust", "0.5000000000000001", "0.6607638406387748", "0.9994753505126488", "0.9997305761679476", "0", null, null, null, null, null, "9", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.02108396428914", "1_0", "0_0"], ["47", "0LNetworkCommunity/libra-legacy-v6", "667", "2023-06-18", "0.0", "1.0", "1378.7114", "7008", "158", "1126", "Rust", "0.3333333333333334", "0.6224593312018546", "0.9982938032439403", "0.9979310308329434", "0", null, null, null, null, null, "10", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.027314514039777", "1_0", "0_0"], ["48", "0LNetworkCommunity/libra-legacy-v6", "668", "2023-06-25", "0.0", "1.0", "1378.7114", "7008", "158", "1133", "Rust", "0.5000000000000001", "0.6224593312018546", "0.9982938032439403", "0.9989644441499094", "0", null, null, null, null, null, "11", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.033506484287697", "1_0", "0_0"], ["49", "0LNetworkCommunity/libra-legacy-v6", "669", "2023-07-02", "0.0", "1.0", "1378.7114", "7008", "158", "1140", "Rust", "0.25", "0.5", "0.9991497367557178", "0.997453540588834", "0", null, null, null, null, null, "12", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "7.229629625845649", "8.854950316500325", "5.0689042022202315", "7.039660349862076", "1_0", "0_0"]], "shape": {"columns": 34, "rows": 8937112}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>is_post_treatment</th>\n", "      <th>is_treated_post_treatment</th>\n", "      <th>log_pr_throughput</th>\n", "      <th>log_pull_request_success_rate</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_project_commits</th>\n", "      <th>log_project_contributors</th>\n", "      <th>log_project_age</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>2023-01-22</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>2.4025</td>\n", "      <td>634</td>\n", "      <td>8</td>\n", "      <td>265</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.693147</td>\n", "      <td>1.224510</td>\n", "      <td>6.453625</td>\n", "      <td>2.197225</td>\n", "      <td>5.583496</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>2023-01-29</td>\n", "      <td>1.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>634</td>\n", "      <td>8</td>\n", "      <td>272</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.693147</td>\n", "      <td>0.693147</td>\n", "      <td>5.141017</td>\n", "      <td>6.453625</td>\n", "      <td>2.197225</td>\n", "      <td>5.609472</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>2023-02-05</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>634</td>\n", "      <td>8</td>\n", "      <td>279</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.693147</td>\n", "      <td>5.141017</td>\n", "      <td>6.453625</td>\n", "      <td>2.197225</td>\n", "      <td>5.634790</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>2023-02-12</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>642</td>\n", "      <td>8</td>\n", "      <td>286</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.693147</td>\n", "      <td>5.141017</td>\n", "      <td>6.466145</td>\n", "      <td>2.197225</td>\n", "      <td>5.659482</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>2023-02-19</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>169.8895</td>\n", "      <td>642</td>\n", "      <td>8</td>\n", "      <td>293</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.693147</td>\n", "      <td>5.141017</td>\n", "      <td>6.466145</td>\n", "      <td>2.197225</td>\n", "      <td>5.683580</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8937107</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>484</td>\n", "      <td>2019-12-15</td>\n", "      <td>20.0</td>\n", "      <td>0.8182</td>\n", "      <td>41.3989</td>\n", "      <td>3723</td>\n", "      <td>177</td>\n", "      <td>2113</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3.044522</td>\n", "      <td>0.597847</td>\n", "      <td>3.747122</td>\n", "      <td>8.222554</td>\n", "      <td>5.181784</td>\n", "      <td>7.656337</td>\n", "      <td>1_63884</td>\n", "      <td>0_63884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8937108</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>485</td>\n", "      <td>2019-12-22</td>\n", "      <td>4.0</td>\n", "      <td>0.8000</td>\n", "      <td>34.0408</td>\n", "      <td>3733</td>\n", "      <td>178</td>\n", "      <td>2120</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1.609438</td>\n", "      <td>0.587787</td>\n", "      <td>3.556513</td>\n", "      <td>8.225235</td>\n", "      <td>5.187386</td>\n", "      <td>7.659643</td>\n", "      <td>1_63884</td>\n", "      <td>0_63884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8937109</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>486</td>\n", "      <td>2019-12-29</td>\n", "      <td>0.0</td>\n", "      <td>0.8000</td>\n", "      <td>34.0408</td>\n", "      <td>3733</td>\n", "      <td>178</td>\n", "      <td>2127</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.587787</td>\n", "      <td>3.556513</td>\n", "      <td>8.225235</td>\n", "      <td>5.187386</td>\n", "      <td>7.662938</td>\n", "      <td>1_63884</td>\n", "      <td>0_63884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8937110</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>487</td>\n", "      <td>2020-01-05</td>\n", "      <td>0.0</td>\n", "      <td>0.5000</td>\n", "      <td>34.0408</td>\n", "      <td>3740</td>\n", "      <td>180</td>\n", "      <td>2134</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.405465</td>\n", "      <td>3.556513</td>\n", "      <td>8.227108</td>\n", "      <td>5.198497</td>\n", "      <td>7.666222</td>\n", "      <td>1_63884</td>\n", "      <td>0_63884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8937111</th>\n", "      <td>ceph/ceph-ansible</td>\n", "      <td>488</td>\n", "      <td>2020-01-12</td>\n", "      <td>42.0</td>\n", "      <td>0.8889</td>\n", "      <td>244.1081</td>\n", "      <td>3765</td>\n", "      <td>180</td>\n", "      <td>2141</td>\n", "      <td>Python</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3.761200</td>\n", "      <td>0.635995</td>\n", "      <td>5.501699</td>\n", "      <td>8.233769</td>\n", "      <td>5.198497</td>\n", "      <td>7.669495</td>\n", "      <td>1_63884</td>\n", "      <td>0_63884</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8937112 rows × 34 columns</p>\n", "</div>"], "text/plain": ["                 repo_name  standardized_time_weeks    datetime  \\\n", "0               01mf02/jaq                      646  2023-01-22   \n", "1               01mf02/jaq                      647  2023-01-29   \n", "2               01mf02/jaq                      648  2023-02-05   \n", "3               01mf02/jaq                      649  2023-02-12   \n", "4               01mf02/jaq                      650  2023-02-19   \n", "...                    ...                      ...         ...   \n", "8937107  ceph/ceph-ansible                      484  2019-12-15   \n", "8937108  ceph/ceph-ansible                      485  2019-12-22   \n", "8937109  ceph/ceph-ansible                      486  2019-12-29   \n", "8937110  ceph/ceph-ansible                      487  2020-01-05   \n", "8937111  ceph/ceph-ansible                      488  2020-01-12   \n", "\n", "         pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0                  0.0                     1.0000         2.4025   \n", "1                  1.0                     1.0000       169.8895   \n", "2                  0.0                     1.0000       169.8895   \n", "3                  0.0                     1.0000       169.8895   \n", "4                  0.0                     1.0000       169.8895   \n", "...                ...                        ...            ...   \n", "8937107           20.0                     0.8182        41.3989   \n", "8937108            4.0                     0.8000        34.0408   \n", "8937109            0.0                     0.8000        34.0408   \n", "8937110            0.0                     0.5000        34.0408   \n", "8937111           42.0                     0.8889       244.1081   \n", "\n", "         project_commits  project_contributors  project_age mainLanguage  ...  \\\n", "0                    634                     8          265         Rust  ...   \n", "1                    634                     8          272         Rust  ...   \n", "2                    634                     8          279         Rust  ...   \n", "3                    642                     8          286         Rust  ...   \n", "4                    642                     8          293         Rust  ...   \n", "...                  ...                   ...          ...          ...  ...   \n", "8937107             3723                   177         2113       Python  ...   \n", "8937108             3733                   178         2120       Python  ...   \n", "8937109             3733                   178         2127       Python  ...   \n", "8937110             3740                   180         2134       Python  ...   \n", "8937111             3765                   180         2141       Python  ...   \n", "\n", "         is_post_treatment  is_treated_post_treatment  log_pr_throughput  \\\n", "0                        0                          0           0.000000   \n", "1                        0                          0           0.693147   \n", "2                        0                          0           0.000000   \n", "3                        0                          0           0.000000   \n", "4                        0                          0           0.000000   \n", "...                    ...                        ...                ...   \n", "8937107                  1                          0           3.044522   \n", "8937108                  1                          0           1.609438   \n", "8937109                  1                          0           0.000000   \n", "8937110                  1                          0           0.000000   \n", "8937111                  1                          0           3.761200   \n", "\n", "         log_pull_request_success_rate  log_time_to_merge  \\\n", "0                             0.693147           1.224510   \n", "1                             0.693147           5.141017   \n", "2                             0.693147           5.141017   \n", "3                             0.693147           5.141017   \n", "4                             0.693147           5.141017   \n", "...                                ...                ...   \n", "8937107                       0.597847           3.747122   \n", "8937108                       0.587787           3.556513   \n", "8937109                       0.587787           3.556513   \n", "8937110                       0.405465           3.556513   \n", "8937111                       0.635995           5.501699   \n", "\n", "         log_project_commits  log_project_contributors  log_project_age  \\\n", "0                   6.453625                  2.197225         5.583496   \n", "1                   6.453625                  2.197225         5.609472   \n", "2                   6.453625                  2.197225         5.634790   \n", "3                   6.466145                  2.197225         5.659482   \n", "4                   6.466145                  2.197225         5.683580   \n", "...                      ...                       ...              ...   \n", "8937107             8.222554                  5.181784         7.656337   \n", "8937108             8.225235                  5.187386         7.659643   \n", "8937109             8.225235                  5.187386         7.662938   \n", "8937110             8.227108                  5.198497         7.666222   \n", "8937111             8.233769                  5.198497         7.669495   \n", "\n", "         time_cohort_effect  repo_cohort_effect  \n", "0                       0_0                 1_0  \n", "1                       0_0                 1_0  \n", "2                       0_0                 1_0  \n", "3                       0_0                 1_0  \n", "4                       0_0                 1_0  \n", "...                     ...                 ...  \n", "8937107             1_63884             0_63884  \n", "8937108             1_63884             0_63884  \n", "8937109             1_63884             0_63884  \n", "8937110             1_63884             0_63884  \n", "8937111             1_63884             0_63884  \n", "\n", "[8937112 rows x 34 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data_test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "for cohort_id, group in compiled_data_test.groupby('cohort_id'):\n", "    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]\n", "    if treated_group.empty:\n", "        continue\n", "    treated_row = treated_group.iloc[0][['tenure', 'commit_percent', 'commits']]\n", "    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['tenure', 'commit_percent', 'commits']] = treated_row.values    \n", "    \n", "# generate the model with each cohort group of the project characteristics just before the treatment\n", "compiled_data_test['log_project_commits_before_treatment'] = 0\n", "compiled_data_test['log_project_contributors_before_treatment'] = 0\n", "compiled_data_test['log_project_age_before_treatment'] = 0\n", "compiled_data_test['project_main_language'] = ''\n", "for cohort_id, group in compiled_data_test.groupby('cohort_id'):\n", "    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]\n", "    if treated_group.empty:\n", "        continue\n", "    treated_row = treated_group.iloc[0][['log_project_commits', 'log_project_contributors', 'log_project_age', 'mainLanguage']]\n", "    # rename ['log_project_commits', 'log_project_contributors', 'log_project_age'] into [log_project_commits_before_treatment, log_project_contributors_before_treatment, log_project_age_before_treatment]\n", "    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "compiled_data_test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["compiled_data_test.to_csv(output_dir + \"compiled_data_test_12_time_to_merge_rate_processed.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add growth stage and newcomers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attritions = pd.read_csv(\"../data/attritions_20250227_add_burst_merged_add_growth_phase_add_newcomer_cnt.csv\")\n", "compiled_data = pd.read_csv(\"../result/did_result_20250312/compiled_data_test_12_time_to_merge_rate_processed.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a mapping of burst to growth_phase and newcomers from attritions\n", "burst_to_data = {}\n", "for burst in attritions.burst.unique():\n", "  filtered_attritions = attritions[attritions.burst == burst]\n", "  burst_to_data[burst] = {\n", "    'growth_phase': filtered_attritions.growth_phase.values[0],\n", "    'newcomers': filtered_attritions.newcomers.values[0]\n", "  }\n", "\n", "# Create a mapping of cohort_id to burst\n", "cohort_to_burst = {}\n", "for burst in burst_to_data:\n", "  # Find one row with this burst to get its cohort_id\n", "  matching_rows = compiled_data[compiled_data.burst == burst]\n", "  if not matching_rows.empty:\n", "    cohort_id = matching_rows.iloc[0]['cohort_id']\n", "    cohort_to_burst[cohort_id] = burst\n", "\n", "# Now update all rows with the same cohort_id\n", "for cohort_id, burst in cohort_to_burst.items():\n", "  compiled_data.loc[compiled_data.cohort_id == cohort_id, 'growth_phase'] = burst_to_data[burst]['growth_phase']\n", "  compiled_data.loc[compiled_data.cohort_id == cohort_id, 'newcomers'] = burst_to_data[burst]['newcomers']\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["growth_phase\n", "steady            2241766\n", "decelerating      1887558\n", "accelerating       892526\n", "saturation         357000\n", "first 3 months      85739\n", "Name: count, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data['growth_phase'].value_counts()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "8c52be50-de3c-4db5-a29e-69ff072d88b5", "rows": [["0", "0LNetworkCommunity/libra-legacy-v6", "578", "2021-10-03", "8.0", "0.4", "39.6172", "6815", "137", "503", "Rust", "0.9000000000000001", "0.4833311793542836", "0.99999975761806", "0.999999971211086", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-12", "1", "False", "0", "0", "0", "2.19722457733622", "0.3364722366212129", "8.827028068509152", "4.927253685157205", "6.222576268071369", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "3.704191622215542"], ["1", "0LNetworkCommunity/libra-legacy-v6", "579", "2021-10-10", "13.0", "0.8462", "54.4169", "6831", "137", "510", "Rust", "0.6666666666666669", "0.6636270934502485", "0.9999998331746794", "0.9999999577205892", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-11", "1", "False", "0", "0", "0", "2.6390573296152584", "0.6131294725739141", "8.829372735468406", "4.927253685157205", "6.236369590203704", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.0148846013853055"], ["2", "0LNetworkCommunity/libra-legacy-v6", "580", "2021-10-17", "9.0", "0.4762", "12.0828", "6842", "137", "517", "Rust", "0.7692307692307694", "0.5148706131181635", "0.9999998588203912", "0.9999999600926628", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-10", "1", "False", "0", "0", "0", "2.302585092994046", "0.3894712183538149", "8.830981510952498", "4.927253685157205", "6.249975242259483", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "2.57129839039821"], ["3", "0LNetworkCommunity/libra-legacy-v6", "581", "2021-10-24", "8.0", "0.7778", "41.4331", "6850", "137", "524", "Rust", "0.642857142857143", "0.563160791105475", "0.9999997180569852", "0.9999998784994888", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-9", "1", "False", "0", "0", "0", "2.19722457733622", "0.5753766448254375", "8.832149906002899", "4.927253685157205", "6.263398262591624", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "3.7479287180584517"], ["4", "0LNetworkCommunity/libra-legacy-v6", "582", "2021-10-31", "5.0", "0.6667", "54.8299", "6857", "137", "531", "Rust", "0.7500000000000002", "0.5", "0.9999988998829626", "0.9999996332940518", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-8", "1", "False", "0", "0", "0", "1.791759469228055", "0.5108456235659934", "8.833171133022866", "4.927253685157205", "6.2766434893416445", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.0223095681689545"], ["5", "0LNetworkCommunity/libra-legacy-v6", "583", "2021-11-07", "2.0", "1.0", "100.9819", "6865", "137", "538", "Rust", "0.7500000000000002", "0.5825621003679713", "0.999999525888768", "0.9999998867576704", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-7", "1", "False", "0", "0", "0", "1.0986122886681098", "0.6931471805599453", "8.834336974017637", "4.927253685157205", "6.289715570908998", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.624795346557591"], ["6", "0LNetworkCommunity/libra-legacy-v6", "584", "2021-11-14", "0.0", null, null, "6878", "137", "545", "Rust", "0.1666666666666667", "0.5825621003679713", "0.999999525888768", "0.9999983013677476", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-6", "1", "False", "0", "0", "0", "0.0", null, "8.836228571526014", "4.927253685157205", "6.302618975744905", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["7", "0LNetworkCommunity/libra-legacy-v6", "585", "2021-11-21", "0.0", null, null, "6889", "139", "552", "Rust", "0.0909090909090909", "0.5312093733737563", "0.9198852655100952", "0.5654237593048187", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-5", "1", "False", "0", "0", "0", "0.0", null, "8.837826364007704", "4.941642422609304", "6.315358001522335", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["8", "0LNetworkCommunity/libra-legacy-v6", "586", "2021-11-28", "0.0", null, null, "6897", "141", "559", "Rust", "0.2", "0.5", "0.9627562394654116", "0.8659972561683749", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-4", "1", "False", "0", "0", "0", "0.0", null, "8.838986793496787", "4.955827057601261", "6.327936783729195", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["9", "0LNetworkCommunity/libra-legacy-v6", "587", "2021-12-05", "0.0", null, null, "6898", "142", "566", "Rust", "0.3333333333333334", "0.5621765008857981", "0.9654175849822932", "0.947153427694228", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-3", "1", "False", "0", "0", "0", "0.0", null, "8.839131752546109", "4.962844630259907", "6.340359303727752", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["10", "0LNetworkCommunity/libra-legacy-v6", "588", "2021-12-12", "0.0", null, null, "6910", "144", "573", "Rust", "0.125", "0.6877239608057475", "0.9612940932692432", "0.8865402143022296", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-2", "1", "False", "0", "0", "0", "0.0", null, "8.840869624091395", "4.976733742420574", "6.352629396319567", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["11", "0LNetworkCommunity/libra-legacy-v6", "589", "2021-12-19", "0.0", null, null, "6914", "144", "580", "Rust", "0.0833333333333333", "0.5585550851276514", "0.9324041557127414", "0.6133995020844122", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-1", "1", "False", "0", "0", "0", "0.0", null, "8.841448244098858", "4.976733742420574", "6.364750756851911", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["12", "0LNetworkCommunity/libra-legacy-v6", "590", "2021-12-26", "6.0", "0.375", "25.6031", "6921", "144", "587", "Rust", "0.4375", "0.6456563062257954", "0.9604475837167408", "0.971762435311964", "1", "581.0", "0.0204052511415525", "143.0", "13.0", "1.0", "0", "1", "False", "0", "0", "0", "1.9459101490553128", "0.3184537311185346", "8.84246002419529", "4.976733742420574", "6.376726947898627", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "3.281027750350621"], ["13", "0LNetworkCommunity/libra-legacy-v6", "591", "2022-01-02", "0.0", null, null, "6921", "144", "594", "Rust", "0.0666666666666666", "0.5383743860872742", "0.9435452601032374", "0.5819899614041409", "0", "581.0", "0.0204052511415525", "143.0", null, null, "1", "1", "True", "0", "1", "1", "0.0", null, "8.84246002419529", "4.976733742420574", "6.38856140554563", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["14", "0LNetworkCommunity/libra-legacy-v6", "592", "2022-01-09", "1.0", "0.4", "11.3772", "6922", "144", "601", "Rust", "0.1666666666666666", "0.6280359032238805", "0.973251490514884", "0.9247373568552196", "0", "581.0", "0.0204052511415525", "143.0", null, null, "2", "1", "True", "0", "1", "1", "0.6931471805599453", "0.3364722366212129", "8.842604480678025", "4.976733742420574", "6.400257445308821", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "2.5158560704319353"], ["15", "0LNetworkCommunity/libra-legacy-v6", "593", "2022-01-16", "4.0", "0.625", "32.2622", "6926", "144", "608", "Rust", "0.3571428571428571", "0.6099733029978927", "0.952608229322397", "0.9458419296795428", "0", "581.0", "0.0204052511415525", "143.0", null, null, "3", "1", "True", "0", "1", "1", "1.6094379124341005", "0.4855078157817008", "8.843182098022606", "4.976733742420574", "6.411818267709897", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "3.5044216170974076"], ["16", "0LNetworkCommunity/libra-legacy-v6", "594", "2022-01-23", "3.0", "0.6667", "42.0878", "6929", "144", "615", "Rust", "0.4", "0.6456563062257954", "0.951753096237358", "0.959940631932508", "0", "581.0", "0.0204052511415525", "143.0", null, null, "4", "1", "True", "0", "1", "1", "1.3862943611198906", "0.5108456235659934", "8.84361509218395", "4.976733742420574", "6.423246963533519", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "3.763239894394898"], ["17", "0LNetworkCommunity/libra-legacy-v6", "595", "2022-01-30", "0.0", "0.0909", null, "6929", "144", "622", "Rust", "0.25", "0.423334433846258", "0.8947535408993973", "0.6753604786604795", "0", "581.0", "0.0204052511415525", "143.0", null, null, "5", "1", "True", "0", "1", "1", "0.0", "0.087003043621574", "8.84361509218395", "4.976733742420574", "6.434546518787453", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["18", "0LNetworkCommunity/libra-legacy-v6", "596", "2022-02-06", "3.0", "0.4", "88.2078", "6932", "145", "629", "Rust", "0.8", "0.5", "0.8947535408993973", "0.971433492394052", "0", "581.0", "0.0204052511415525", "143.0", null, null, "6", "1", "True", "0", "1", "1", "1.3862943611198906", "0.3364722366212129", "8.84404789894249", "4.983606621708336", "6.4457198193855785", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.490968479709153"], ["19", "0LNetworkCommunity/libra-legacy-v6", "597", "2022-02-13", "5.0", "0.5385", "10.4928", "6936", "146", "636", "Rust", "0.8571428571428572", "0.5345697571818813", "0.7788881796858763", "0.960435645828343", "0", "581.0", "0.0204052511415525", "143.0", null, null, "7", "1", "True", "0", "1", "1", "1.791759469228055", "0.4308079157799594", "8.844624683385302", "4.990432586778736", "6.456769655572163", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "2.4417207523384"], ["20", "0LNetworkCommunity/libra-legacy-v6", "598", "2022-02-20", "1.0", "0.7143", "55.0633", "6936", "146", "643", "Rust", "0.6666666666666666", "0.5779344938555356", "0.995957821751555", "0.9985202013786708", "0", "581.0", "0.0204052511415525", "143.0", null, null, "8", "1", "True", "0", "1", "1", "0.6931471805599453", "0.5390048340312984", "8.844624683385302", "4.990432586778736", "6.467698726104354", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.026481409505386"], ["21", "0LNetworkCommunity/libra-legacy-v6", "599", "2022-02-27", "0.0", "0.6", null, "6936", "146", "650", "Rust", "0.5", "0.5498339973124778", "0.995957821751555", "0.9966881176675184", "0", "581.0", "0.0204052511415525", "143.0", null, null, "9", "1", "True", "0", "1", "1", "0.0", "0.4700036292457356", "8.844624683385302", "4.990432586778736", "6.478509642208569", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["22", "0LNetworkCommunity/libra-legacy-v6", "600", "2022-03-06", "1.0", "0.0833", "273.6717", "6937", "147", "657", "Rust", "0.6666666666666666", "0.4214801929888155", "0.995957821751555", "0.9972223495979404", "0", "581.0", "0.0204052511415525", "143.0", null, null, "10", "1", "True", "0", "1", "1", "0.6931471805599453", "0.0800119379693846", "8.844768827529695", "4.997212273764115", "6.489204931325317", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "5.615576566316175"], ["23", "0LNetworkCommunity/libra-legacy-v6", "601", "2022-03-13", "0.0", "0.5", null, "6937", "147", "664", "Rust", "0.5", "0.52497918747894", "0.995957821751555", "0.996341078414588", "0", "581.0", "0.0204052511415525", "143.0", null, null, "11", "1", "True", "0", "1", "1", "0.0", "0.4054651081081644", "8.844768827529695", "4.997212273764115", "6.499787040655854", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["24", "0LNetworkCommunity/libra-legacy-v6", "602", "2022-03-20", "0.0", null, null, "6937", "147", "671", "Rust", "0.125", "0.52497918747894", "0.995957821751555", "0.9749377536666451", "0", "581.0", "0.0204052511415525", "143.0", null, null, "12", "1", "True", "0", "1", "1", "0.0", null, "8.844768827529695", "4.997212273764115", "6.51025834052315", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["25", "pythonistaguild/twitchio", "571", "2021-08-15", "1.0", "1.0", "157.38", "274", "15", "1378", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-12", "0", "False", "0", "0", "0", "0.6931471805599453", "0.6931471805599453", "5.616771097666572", "2.772588722239781", "7.229113877793302", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "5.064997208782266"], ["26", "pythonistaguild/twitchio", "572", "2021-08-22", "3.0", "1.0", "85.7594", "278", "17", "1385", "Python", "0.5714285714285714", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-11", "0", "False", "0", "0", "0", "1.3862943611198906", "0.6931471805599453", "5.631211781821365", "2.8903717578961645", "7.234177179749849", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.463138770305438"], ["27", "pythonistaguild/twitchio", "573", "2021-08-29", "2.0", null, "364.9728", "282", "18", "1392", "Python", "0.7499999999999999", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-10", "0", "False", "0", "0", "0", "1.0986122886681098", null, "5.645446897643238", "2.9444389791664403", "7.239214973779806", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "5.902559013699835"], ["28", "pythonistaguild/twitchio", "574", "2021-09-05", "0.0", "1.0", null, "285", "18", "1399", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-9", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.655991810819852", "2.9444389791664403", "7.24422751560335", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["29", "pythonistaguild/twitchio", "575", "2021-09-12", "2.0", "0.5", "93.5675", "291", "19", "1406", "Python", "0.75", "0.3775406687981454", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-8", "0", "False", "0", "0", "0", "1.0986122886681098", "0.4054651081081644", "5.676753802268282", "2.995732273553991", "7.249215057114389", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.549313865233354"], ["30", "pythonistaguild/twitchio", "576", "2021-09-19", "0.0", "1.0", null, "291", "19", "1413", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-7", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.676753802268282", "2.995732273553991", "7.254177846456518", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["31", "pythonistaguild/twitchio", "577", "2021-09-26", "3.0", "1.0", "149.1795", "308", "21", "1420", "Python", "0.6666666666666666", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-6", "0", "False", "0", "0", "0", "1.3862943611198906", "0.6931471805599453", "5.733341276897746", "3.091042453358316", "7.259116128097101", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "5.011831245328068"], ["32", "pythonistaguild/twitchio", "578", "2021-10-03", "0.0", "1.0", null, "308", "21", "1427", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-5", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.733341276897746", "3.091042453358316", "7.2640301428995295", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["33", "pythonistaguild/twitchio", "579", "2021-10-10", "0.0", "0.5", null, "308", "21", "1434", "Python", "0.25", "0.3775406687981454", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-4", "0", "False", "0", "0", "0", "0.0", "0.4054651081081644", "5.733341276897746", "3.091042453358316", "7.268920128193722", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["34", "pythonistaguild/twitchio", "580", "2021-10-17", "4.0", "1.0", "92.2178", "318", "22", "1441", "Python", "0.8333333333333334", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-3", "0", "False", "0", "0", "0", "1.6094379124341005", "0.6931471805599453", "5.765191102784844", "3.1354942159291497", "7.273786317844895", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.53493869058029"], ["35", "pythonistaguild/twitchio", "581", "2021-10-24", "0.0", "0.5", null, "321", "24", "1448", "Python", "0.2", "0.3775406687981454", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-2", "0", "False", "0", "0", "0", "0.0", "0.4054651081081644", "5.7745515455444085", "3.218875824868201", "7.278628942320682", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["36", "pythonistaguild/twitchio", "582", "2021-10-31", "2.0", "0.6667", "145.7067", "336", "24", "1455", "Python", "0.5000000000000001", "0.4667242581638324", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-1", "0", "False", "0", "0", "0", "1.0986122886681098", "0.5108456235659934", "5.820082930352362", "3.218875824868201", "7.283448228756631", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "4.988435355544337"], ["37", "pythonistaguild/twitchio", "583", "2021-11-07", "5.0", "0.6", "202.3827", "349", "24", "1462", "Python", "0.75", "0.401312339887548", "0.9604436295576684", "0.9799305083592806", "0", "581.0", "0.0204052511415525", "143.0", null, null, "0", "0", "False", "0", "0", "0", "1.791759469228055", "0.4700036292457356", "5.857933154483459", "3.218875824868201", "7.288244401020124", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "5.3150894259165264"], ["38", "pythonistaguild/twitchio", "584", "2021-11-14", "0.0", null, null, "351", "24", "1469", "Python", "0.2", "0.401312339887548", "0.9745373266959764", "0.8651167492165106", "0", "581.0", "0.0204052511415525", "143.0", null, null, "1", "0", "True", "0", "1", "0", "0.0", null, "5.863631175598097", "3.218875824868201", "7.293017679772782", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["39", "pythonistaguild/twitchio", "585", "2021-11-21", "0.0", "1.0", null, "351", "24", "1476", "Python", "0.25", "0.401312339887548", "0.5960354914346854", "0.2479381779704038", "0", "581.0", "0.0204052511415525", "143.0", null, null, "2", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "5.863631175598097", "3.218875824868201", "7.29776828253138", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["40", "pythonistaguild/twitchio", "586", "2021-11-28", "3.0", "0.6667", "292.0997", "361", "25", "1483", "Python", "0.8", "0.324470983286606", "0.5960354914346854", "0.7392294680775993", "0", "581.0", "0.0204052511415525", "143.0", null, null, "3", "0", "True", "0", "1", "0", "1.3862943611198906", "0.5108456235659934", "5.891644211825772", "3.258096538021482", "7.302496423727326", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "5.680512824174869"], ["41", "pythonistaguild/twitchio", "587", "2021-12-05", "2.0", "0.6667", "3.8865", "370", "26", "1490", "Python", "0.5000000000000001", "0.441938115070719", "0.3548651696325048", "0.3034291394451756", "0", "581.0", "0.0204052511415525", "143.0", null, null, "4", "0", "True", "0", "1", "0", "1.0986122886681098", "0.5108456235659934", "5.916202062607435", "3.295836866004329", "7.307202314764738", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "1.5864763007967533"], ["42", "pythonistaguild/twitchio", "588", "2021-12-12", "0.0", null, null, "370", "26", "1497", "Python", "0.5000000000000001", "0.324470983286606", "0.3548651696325048", "0.2089904604277709", "0", "581.0", "0.0204052511415525", "143.0", null, null, "5", "0", "True", "0", "1", "0", "0.0", null, "5.916202062607435", "3.295836866004329", "7.311886164077165", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["43", "pythonistaguild/twitchio", "589", "2021-12-19", "1.0", "0.3333", "2.3814", "375", "27", "1504", "Python", "0.3333333333333334", "0.324470983286606", "0.3548651696325048", "0.1166886361072576", "0", "581.0", "0.0204052511415525", "143.0", null, null, "6", "0", "True", "0", "1", "0", "0.6931471805599453", "0.2876570721392756", "5.929589143389895", "3.332204510175204", "7.316548177182976", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", "1.218289824920729"], ["44", "pythonistaguild/twitchio", "590", "2021-12-26", "0.0", "0.6667", null, "377", "28", "1511", "Python", "0.5000000000000001", "0.4013363661992121", "0.3548651696325048", "0.269409335365653", "0", "581.0", "0.0204052511415525", "143.0", null, null, "7", "0", "True", "0", "1", "0", "0.0", "0.5108456235659934", "5.934894195619588", "3.367295829986474", "7.321188556739478", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["45", "pythonistaguild/twitchio", "591", "2022-01-02", "0.0", null, null, "377", "28", "1518", "Python", "0.5000000000000001", "0.5250041250206465", "0.3548651696325048", "0.3780998063414457", "0", "581.0", "0.0204052511415525", "143.0", null, null, "8", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.325807502595773", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["46", "pythonistaguild/twitchio", "592", "2022-01-09", "0.0", null, null, "377", "28", "1525", "Python", "0.1666666666666667", "0.4013363661992121", "0.3548651696325048", "0.0686854732260053", "0", "581.0", "0.0204052511415525", "143.0", null, null, "9", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.330405211844402", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["47", "pythonistaguild/twitchio", "593", "2022-01-16", "0.0", "1.0", null, "377", "28", "1532", "Python", "0.5000000000000001", "0.5250041250206465", "0.3548651696325048", "0.3780998063414457", "0", "581.0", "0.0204052511415525", "143.0", null, null, "10", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "5.934894195619588", "3.367295829986474", "7.334981878871814", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["48", "pythonistaguild/twitchio", "594", "2022-01-23", "0.0", null, null, "377", "28", "1539", "Python", "0.25", "0.4833561516109304", "0.3548651696325048", "0.1464232940158239", "0", "581.0", "0.0204052511415525", "143.0", null, null, "11", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.339537695407674", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null], ["49", "pythonistaguild/twitchio", "595", "2022-01-30", "0.0", null, null, "377", "28", "1546", "Python", "0.1428571428571429", "0.5000249999999792", "0.2715654479294401", "0.0585051507075485", "0", "581.0", "0.0204052511415525", "143.0", null, null, "12", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.344072850573066", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001", null]], "shape": {"columns": 44, "rows": 5506407}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>log_time_to_merge</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>578</td>\n", "      <td>2021-10-03</td>\n", "      <td>8.0</td>\n", "      <td>0.4000</td>\n", "      <td>39.6172</td>\n", "      <td>6815</td>\n", "      <td>137</td>\n", "      <td>503</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "      <td>3.704192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>579</td>\n", "      <td>2021-10-10</td>\n", "      <td>13.0</td>\n", "      <td>0.8462</td>\n", "      <td>54.4169</td>\n", "      <td>6831</td>\n", "      <td>137</td>\n", "      <td>510</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "      <td>4.014885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>580</td>\n", "      <td>2021-10-17</td>\n", "      <td>9.0</td>\n", "      <td>0.4762</td>\n", "      <td>12.0828</td>\n", "      <td>6842</td>\n", "      <td>137</td>\n", "      <td>517</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "      <td>2.571298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>581</td>\n", "      <td>2021-10-24</td>\n", "      <td>8.0</td>\n", "      <td>0.7778</td>\n", "      <td>41.4331</td>\n", "      <td>6850</td>\n", "      <td>137</td>\n", "      <td>524</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "      <td>3.747929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>582</td>\n", "      <td>2021-10-31</td>\n", "      <td>5.0</td>\n", "      <td>0.6667</td>\n", "      <td>54.8299</td>\n", "      <td>6857</td>\n", "      <td>137</td>\n", "      <td>531</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "      <td>4.022310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506402</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>357</td>\n", "      <td>2017-07-09</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>353</td>\n", "      <td>16</td>\n", "      <td>818</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506403</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>358</td>\n", "      <td>2017-07-16</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>353</td>\n", "      <td>16</td>\n", "      <td>825</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506404</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>359</td>\n", "      <td>2017-07-23</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>353</td>\n", "      <td>16</td>\n", "      <td>832</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506405</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>360</td>\n", "      <td>2017-07-30</td>\n", "      <td>1.0</td>\n", "      <td>1.0000</td>\n", "      <td>101.6147</td>\n", "      <td>354</td>\n", "      <td>17</td>\n", "      <td>839</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "      <td>4.630981</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506406</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>361</td>\n", "      <td>2017-08-06</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>354</td>\n", "      <td>17</td>\n", "      <td>846</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5506407 rows × 44 columns</p>\n", "</div>"], "text/plain": ["                                  repo_name  standardized_time_weeks  \\\n", "0        0LNetworkCommunity/libra-legacy-v6                      578   \n", "1        0LNetworkCommunity/libra-legacy-v6                      579   \n", "2        0LNetworkCommunity/libra-legacy-v6                      580   \n", "3        0LNetworkCommunity/libra-legacy-v6                      581   \n", "4        0LNetworkCommunity/libra-legacy-v6                      582   \n", "...                                     ...                      ...   \n", "5506402                   1-liners/1-liners                      357   \n", "5506403                   1-liners/1-liners                      358   \n", "5506404                   1-liners/1-liners                      359   \n", "5506405                   1-liners/1-liners                      360   \n", "5506406                   1-liners/1-liners                      361   \n", "\n", "           datetime  pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0        2021-10-03            8.0                     0.4000        39.6172   \n", "1        2021-10-10           13.0                     0.8462        54.4169   \n", "2        2021-10-17            9.0                     0.4762        12.0828   \n", "3        2021-10-24            8.0                     0.7778        41.4331   \n", "4        2021-10-31            5.0                     0.6667        54.8299   \n", "...             ...            ...                        ...            ...   \n", "5506402  2017-07-09            0.0                        NaN            NaN   \n", "5506403  2017-07-16            0.0                        NaN            NaN   \n", "5506404  2017-07-23            0.0                     1.0000            NaN   \n", "5506405  2017-07-30            1.0                     1.0000       101.6147   \n", "5506406  2017-08-06            0.0                     1.0000            NaN   \n", "\n", "         project_commits  project_contributors  project_age mainLanguage  ...  \\\n", "0                   6815                   137          503         Rust  ...   \n", "1                   6831                   137          510         Rust  ...   \n", "2                   6842                   137          517         Rust  ...   \n", "3                   6850                   137          524         Rust  ...   \n", "4                   6857                   137          531         Rust  ...   \n", "...                  ...                   ...          ...          ...  ...   \n", "5506402              353                    16          818   JavaScript  ...   \n", "5506403              353                    16          825   JavaScript  ...   \n", "5506404              353                    16          832   JavaScript  ...   \n", "5506405              354                    17          839   JavaScript  ...   \n", "5506406              354                    17          846   JavaScript  ...   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         4.976734   \n", "1                                         4.976734   \n", "2                                         4.976734   \n", "3                                         4.976734   \n", "4                                         4.976734   \n", "...                                            ...   \n", "5506402                                   2.079442   \n", "5506403                                   2.079442   \n", "5506404                                   2.079442   \n", "5506405                                   2.079442   \n", "5506406                                   2.079442   \n", "\n", "         log_project_age_before_treatment  project_main_language  \\\n", "0                                6.376727                   Rust   \n", "1                                6.376727                   Rust   \n", "2                                6.376727                   Rust   \n", "3                                6.376727                   Rust   \n", "4                                6.376727                   Rust   \n", "...                                   ...                    ...   \n", "5506402                          4.025352                     C#   \n", "5506403                          4.025352                     C#   \n", "5506404                          4.025352                     C#   \n", "5506405                          4.025352                     C#   \n", "5506406                          4.025352                     C#   \n", "\n", "         growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0          saturation        3.0       1.386294    6.366470   \n", "1          saturation        3.0       1.386294    6.366470   \n", "2          saturation        3.0       1.386294    6.366470   \n", "3          saturation        3.0       1.386294    6.366470   \n", "4          saturation        3.0       1.386294    6.366470   \n", "...               ...        ...            ...         ...   \n", "5506402  decelerating        3.0       1.386294    3.332205   \n", "5506403  decelerating        3.0       1.386294    3.332205   \n", "5506404  decelerating        3.0       1.386294    3.332205   \n", "5506405  decelerating        3.0       1.386294    3.332205   \n", "5506406  decelerating        3.0       1.386294    3.332205   \n", "\n", "         log_commit_percent  log_commits  log_time_to_merge  \n", "0                  0.020200     4.969813           3.704192  \n", "1                  0.020200     4.969813           4.014885  \n", "2                  0.020200     4.969813           2.571298  \n", "3                  0.020200     4.969813           3.747929  \n", "4                  0.020200     4.969813           4.022310  \n", "...                     ...          ...                ...  \n", "5506402            0.016298     2.079442                NaN  \n", "5506403            0.016298     2.079442                NaN  \n", "5506404            0.016298     2.079442                NaN  \n", "5506405            0.016298     2.079442           4.630981  \n", "5506406            0.016298     2.079442                NaN  \n", "\n", "[5506407 rows x 44 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["compiled_data['log_time_to_merge'] = np.log(compiled_data['time_to_merge'] + 1)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["compiled_data.to_csv(output_dir + \"compiled_data_test_12_time_to_merge_rate_processed.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["newcomers\n", "0.0       1183724\n", "1.0        511688\n", "2.0        311513\n", "3.0        217339\n", "4.0        166146\n", "           ...   \n", "284.0          50\n", "299.0          50\n", "417.0          50\n", "271.0          50\n", "1704.0         38\n", "Name: count, Length: 294, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data['newcomers'].value_counts()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "feature_sigmod_12_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_12", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "3c940a86-93b0-4430-9503-78189fb58dd1", "rows": [["0", "0LNetworkCommunity/libra-legacy-v6", "578", "2021-10-03", "8.0", "0.4", "39.6172", "6815", "137", "503", "Rust", "0.9000000000000001", "0.4833311793542836", "0.99999975761806", "0.999999971211086", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-12", "1", "False", "0", "0", "0", "2.19722457733622", "0.3364722366212129", "8.827028068509152", "4.927253685157205", "6.222576268071369", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["1", "0LNetworkCommunity/libra-legacy-v6", "579", "2021-10-10", "13.0", "0.8462", "54.4169", "6831", "137", "510", "Rust", "0.6666666666666669", "0.6636270934502485", "0.9999998331746794", "0.9999999577205892", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-11", "1", "False", "0", "0", "0", "2.6390573296152584", "0.6131294725739141", "8.829372735468406", "4.927253685157205", "6.236369590203704", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["2", "0LNetworkCommunity/libra-legacy-v6", "580", "2021-10-17", "9.0", "0.4762", "12.0828", "6842", "137", "517", "Rust", "0.7692307692307694", "0.5148706131181635", "0.9999998588203912", "0.9999999600926628", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-10", "1", "False", "0", "0", "0", "2.302585092994046", "0.3894712183538149", "8.830981510952498", "4.927253685157205", "6.249975242259483", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["3", "0LNetworkCommunity/libra-legacy-v6", "581", "2021-10-24", "8.0", "0.7778", "41.4331", "6850", "137", "524", "Rust", "0.642857142857143", "0.563160791105475", "0.9999997180569852", "0.9999998784994888", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-9", "1", "False", "0", "0", "0", "2.19722457733622", "0.5753766448254375", "8.832149906002899", "4.927253685157205", "6.263398262591624", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["4", "0LNetworkCommunity/libra-legacy-v6", "582", "2021-10-31", "5.0", "0.6667", "54.8299", "6857", "137", "531", "Rust", "0.7500000000000002", "0.5", "0.9999988998829626", "0.9999996332940518", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-8", "1", "False", "0", "0", "0", "1.791759469228055", "0.5108456235659934", "8.833171133022866", "4.927253685157205", "6.2766434893416445", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["5", "0LNetworkCommunity/libra-legacy-v6", "583", "2021-11-07", "2.0", "1.0", "100.9819", "6865", "137", "538", "Rust", "0.7500000000000002", "0.5825621003679713", "0.999999525888768", "0.9999998867576704", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-7", "1", "False", "0", "0", "0", "1.0986122886681098", "0.6931471805599453", "8.834336974017637", "4.927253685157205", "6.289715570908998", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["6", "0LNetworkCommunity/libra-legacy-v6", "584", "2021-11-14", "0.0", null, null, "6878", "137", "545", "Rust", "0.1666666666666667", "0.5825621003679713", "0.999999525888768", "0.9999983013677476", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-6", "1", "False", "0", "0", "0", "0.0", null, "8.836228571526014", "4.927253685157205", "6.302618975744905", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["7", "0LNetworkCommunity/libra-legacy-v6", "585", "2021-11-21", "0.0", null, null, "6889", "139", "552", "Rust", "0.0909090909090909", "0.5312093733737563", "0.9198852655100952", "0.5654237593048187", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-5", "1", "False", "0", "0", "0", "0.0", null, "8.837826364007704", "4.941642422609304", "6.315358001522335", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["8", "0LNetworkCommunity/libra-legacy-v6", "586", "2021-11-28", "0.0", null, null, "6897", "141", "559", "Rust", "0.2", "0.5", "0.9627562394654116", "0.8659972561683749", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-4", "1", "False", "0", "0", "0", "0.0", null, "8.838986793496787", "4.955827057601261", "6.327936783729195", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["9", "0LNetworkCommunity/libra-legacy-v6", "587", "2021-12-05", "0.0", null, null, "6898", "142", "566", "Rust", "0.3333333333333334", "0.5621765008857981", "0.9654175849822932", "0.947153427694228", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-3", "1", "False", "0", "0", "0", "0.0", null, "8.839131752546109", "4.962844630259907", "6.340359303727752", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["10", "0LNetworkCommunity/libra-legacy-v6", "588", "2021-12-12", "0.0", null, null, "6910", "144", "573", "Rust", "0.125", "0.6877239608057475", "0.9612940932692432", "0.8865402143022296", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-2", "1", "False", "0", "0", "0", "0.0", null, "8.840869624091395", "4.976733742420574", "6.352629396319567", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["11", "0LNetworkCommunity/libra-legacy-v6", "589", "2021-12-19", "0.0", null, null, "6914", "144", "580", "Rust", "0.0833333333333333", "0.5585550851276514", "0.9324041557127414", "0.6133995020844122", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-1", "1", "False", "0", "0", "0", "0.0", null, "8.841448244098858", "4.976733742420574", "6.364750756851911", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["12", "0LNetworkCommunity/libra-legacy-v6", "590", "2021-12-26", "6.0", "0.375", "25.6031", "6921", "144", "587", "Rust", "0.4375", "0.6456563062257954", "0.9604475837167408", "0.971762435311964", "1", "581.0", "0.0204052511415525", "143.0", "13.0", "1.0", "0", "1", "False", "0", "0", "0", "1.9459101490553128", "0.3184537311185346", "8.84246002419529", "4.976733742420574", "6.376726947898627", "0_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["13", "0LNetworkCommunity/libra-legacy-v6", "591", "2022-01-02", "0.0", null, null, "6921", "144", "594", "Rust", "0.0666666666666666", "0.5383743860872742", "0.9435452601032374", "0.5819899614041409", "0", "581.0", "0.0204052511415525", "143.0", null, null, "1", "1", "True", "0", "1", "1", "0.0", null, "8.84246002419529", "4.976733742420574", "6.38856140554563", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["14", "0LNetworkCommunity/libra-legacy-v6", "592", "2022-01-09", "1.0", "0.4", "11.3772", "6922", "144", "601", "Rust", "0.1666666666666666", "0.6280359032238805", "0.973251490514884", "0.9247373568552196", "0", "581.0", "0.0204052511415525", "143.0", null, null, "2", "1", "True", "0", "1", "1", "0.6931471805599453", "0.3364722366212129", "8.842604480678025", "4.976733742420574", "6.400257445308821", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["15", "0LNetworkCommunity/libra-legacy-v6", "593", "2022-01-16", "4.0", "0.625", "32.2622", "6926", "144", "608", "Rust", "0.3571428571428571", "0.6099733029978927", "0.952608229322397", "0.9458419296795428", "0", "581.0", "0.0204052511415525", "143.0", null, null, "3", "1", "True", "0", "1", "1", "1.6094379124341005", "0.4855078157817008", "8.843182098022606", "4.976733742420574", "6.411818267709897", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["16", "0LNetworkCommunity/libra-legacy-v6", "594", "2022-01-23", "3.0", "0.6667", "42.0878", "6929", "144", "615", "Rust", "0.4", "0.6456563062257954", "0.951753096237358", "0.959940631932508", "0", "581.0", "0.0204052511415525", "143.0", null, null, "4", "1", "True", "0", "1", "1", "1.3862943611198906", "0.5108456235659934", "8.84361509218395", "4.976733742420574", "6.423246963533519", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["17", "0LNetworkCommunity/libra-legacy-v6", "595", "2022-01-30", "0.0", "0.0909", null, "6929", "144", "622", "Rust", "0.25", "0.423334433846258", "0.8947535408993973", "0.6753604786604795", "0", "581.0", "0.0204052511415525", "143.0", null, null, "5", "1", "True", "0", "1", "1", "0.0", "0.087003043621574", "8.84361509218395", "4.976733742420574", "6.434546518787453", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["18", "0LNetworkCommunity/libra-legacy-v6", "596", "2022-02-06", "3.0", "0.4", "88.2078", "6932", "145", "629", "Rust", "0.8", "0.5", "0.8947535408993973", "0.971433492394052", "0", "581.0", "0.0204052511415525", "143.0", null, null, "6", "1", "True", "0", "1", "1", "1.3862943611198906", "0.3364722366212129", "8.84404789894249", "4.983606621708336", "6.4457198193855785", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["19", "0LNetworkCommunity/libra-legacy-v6", "597", "2022-02-13", "5.0", "0.5385", "10.4928", "6936", "146", "636", "Rust", "0.8571428571428572", "0.5345697571818813", "0.7788881796858763", "0.960435645828343", "0", "581.0", "0.0204052511415525", "143.0", null, null, "7", "1", "True", "0", "1", "1", "1.791759469228055", "0.4308079157799594", "8.844624683385302", "4.990432586778736", "6.456769655572163", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["20", "0LNetworkCommunity/libra-legacy-v6", "598", "2022-02-20", "1.0", "0.7143", "55.0633", "6936", "146", "643", "Rust", "0.6666666666666666", "0.5779344938555356", "0.995957821751555", "0.9985202013786708", "0", "581.0", "0.0204052511415525", "143.0", null, null, "8", "1", "True", "0", "1", "1", "0.6931471805599453", "0.5390048340312984", "8.844624683385302", "4.990432586778736", "6.467698726104354", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["21", "0LNetworkCommunity/libra-legacy-v6", "599", "2022-02-27", "0.0", "0.6", null, "6936", "146", "650", "Rust", "0.5", "0.5498339973124778", "0.995957821751555", "0.9966881176675184", "0", "581.0", "0.0204052511415525", "143.0", null, null, "9", "1", "True", "0", "1", "1", "0.0", "0.4700036292457356", "8.844624683385302", "4.990432586778736", "6.478509642208569", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["22", "0LNetworkCommunity/libra-legacy-v6", "600", "2022-03-06", "1.0", "0.0833", "273.6717", "6937", "147", "657", "Rust", "0.6666666666666666", "0.4214801929888155", "0.995957821751555", "0.9972223495979404", "0", "581.0", "0.0204052511415525", "143.0", null, null, "10", "1", "True", "0", "1", "1", "0.6931471805599453", "0.0800119379693846", "8.844768827529695", "4.997212273764115", "6.489204931325317", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["23", "0LNetworkCommunity/libra-legacy-v6", "601", "2022-03-13", "0.0", "0.5", null, "6937", "147", "664", "Rust", "0.5", "0.52497918747894", "0.995957821751555", "0.996341078414588", "0", "581.0", "0.0204052511415525", "143.0", null, null, "11", "1", "True", "0", "1", "1", "0.0", "0.4054651081081644", "8.844768827529695", "4.997212273764115", "6.499787040655854", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["24", "0LNetworkCommunity/libra-legacy-v6", "602", "2022-03-20", "0.0", null, null, "6937", "147", "671", "Rust", "0.125", "0.52497918747894", "0.995957821751555", "0.9749377536666451", "0", "581.0", "0.0204052511415525", "143.0", null, null, "12", "1", "True", "0", "1", "1", "0.0", null, "8.844768827529695", "4.997212273764115", "6.51025834052315", "1_0", "1_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["25", "pythonistaguild/twitchio", "571", "2021-08-15", "1.0", "1.0", "157.38", "274", "15", "1378", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-12", "0", "False", "0", "0", "0", "0.6931471805599453", "0.6931471805599453", "5.616771097666572", "2.772588722239781", "7.229113877793302", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["26", "pythonistaguild/twitchio", "572", "2021-08-22", "3.0", "1.0", "85.7594", "278", "17", "1385", "Python", "0.5714285714285714", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-11", "0", "False", "0", "0", "0", "1.3862943611198906", "0.6931471805599453", "5.631211781821365", "2.8903717578961645", "7.234177179749849", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["27", "pythonistaguild/twitchio", "573", "2021-08-29", "2.0", null, "364.9728", "282", "18", "1392", "Python", "0.7499999999999999", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-10", "0", "False", "0", "0", "0", "1.0986122886681098", null, "5.645446897643238", "2.9444389791664403", "7.239214973779806", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["28", "pythonistaguild/twitchio", "574", "2021-09-05", "0.0", "1.0", null, "285", "18", "1399", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-9", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.655991810819852", "2.9444389791664403", "7.24422751560335", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["29", "pythonistaguild/twitchio", "575", "2021-09-12", "2.0", "0.5", "93.5675", "291", "19", "1406", "Python", "0.75", "0.3775406687981454", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-8", "0", "False", "0", "0", "0", "1.0986122886681098", "0.4054651081081644", "5.676753802268282", "2.995732273553991", "7.249215057114389", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["30", "pythonistaguild/twitchio", "576", "2021-09-19", "0.0", "1.0", null, "291", "19", "1413", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-7", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.676753802268282", "2.995732273553991", "7.254177846456518", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["31", "pythonistaguild/twitchio", "577", "2021-09-26", "3.0", "1.0", "149.1795", "308", "21", "1420", "Python", "0.6666666666666666", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-6", "0", "False", "0", "0", "0", "1.3862943611198906", "0.6931471805599453", "5.733341276897746", "3.091042453358316", "7.259116128097101", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["32", "pythonistaguild/twitchio", "578", "2021-10-03", "0.0", "1.0", null, "308", "21", "1427", "Python", "0.5", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-5", "0", "False", "0", "0", "0", "0.0", "0.6931471805599453", "5.733341276897746", "3.091042453358316", "7.2640301428995295", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["33", "pythonistaguild/twitchio", "579", "2021-10-10", "0.0", "0.5", null, "308", "21", "1434", "Python", "0.25", "0.3775406687981454", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-4", "0", "False", "0", "0", "0", "0.0", "0.4054651081081644", "5.733341276897746", "3.091042453358316", "7.268920128193722", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["34", "pythonistaguild/twitchio", "580", "2021-10-17", "4.0", "1.0", "92.2178", "318", "22", "1441", "Python", "0.8333333333333334", "0.5", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-3", "0", "False", "0", "0", "0", "1.6094379124341005", "0.6931471805599453", "5.765191102784844", "3.1354942159291497", "7.273786317844895", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["35", "pythonistaguild/twitchio", "581", "2021-10-24", "0.0", "0.5", null, "321", "24", "1448", "Python", "0.2", "0.3775406687981454", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-2", "0", "False", "0", "0", "0", "0.0", "0.4054651081081644", "5.7745515455444085", "3.218875824868201", "7.278628942320682", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["36", "pythonistaguild/twitchio", "582", "2021-10-31", "2.0", "0.6667", "145.7067", "336", "24", "1455", "Python", "0.5000000000000001", "0.4667242581638324", "1.0", "1.0", "0", "581.0", "0.0204052511415525", "143.0", null, null, "-1", "0", "False", "0", "0", "0", "1.0986122886681098", "0.5108456235659934", "5.820082930352362", "3.218875824868201", "7.283448228756631", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["37", "pythonistaguild/twitchio", "583", "2021-11-07", "5.0", "0.6", "202.3827", "349", "24", "1462", "Python", "0.75", "0.401312339887548", "0.9604436295576684", "0.9799305083592806", "0", "581.0", "0.0204052511415525", "143.0", null, null, "0", "0", "False", "0", "0", "0", "1.791759469228055", "0.4700036292457356", "5.857933154483459", "3.218875824868201", "7.288244401020124", "0_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["38", "pythonistaguild/twitchio", "584", "2021-11-14", "0.0", null, null, "351", "24", "1469", "Python", "0.2", "0.401312339887548", "0.9745373266959764", "0.8651167492165106", "0", "581.0", "0.0204052511415525", "143.0", null, null, "1", "0", "True", "0", "1", "0", "0.0", null, "5.863631175598097", "3.218875824868201", "7.293017679772782", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["39", "pythonistaguild/twitchio", "585", "2021-11-21", "0.0", "1.0", null, "351", "24", "1476", "Python", "0.25", "0.401312339887548", "0.5960354914346854", "0.2479381779704038", "0", "581.0", "0.0204052511415525", "143.0", null, null, "2", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "5.863631175598097", "3.218875824868201", "7.29776828253138", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["40", "pythonistaguild/twitchio", "586", "2021-11-28", "3.0", "0.6667", "292.0997", "361", "25", "1483", "Python", "0.8", "0.324470983286606", "0.5960354914346854", "0.7392294680775993", "0", "581.0", "0.0204052511415525", "143.0", null, null, "3", "0", "True", "0", "1", "0", "1.3862943611198906", "0.5108456235659934", "5.891644211825772", "3.258096538021482", "7.302496423727326", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["41", "pythonistaguild/twitchio", "587", "2021-12-05", "2.0", "0.6667", "3.8865", "370", "26", "1490", "Python", "0.5000000000000001", "0.441938115070719", "0.3548651696325048", "0.3034291394451756", "0", "581.0", "0.0204052511415525", "143.0", null, null, "4", "0", "True", "0", "1", "0", "1.0986122886681098", "0.5108456235659934", "5.916202062607435", "3.295836866004329", "7.307202314764738", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["42", "pythonistaguild/twitchio", "588", "2021-12-12", "0.0", null, null, "370", "26", "1497", "Python", "0.5000000000000001", "0.324470983286606", "0.3548651696325048", "0.2089904604277709", "0", "581.0", "0.0204052511415525", "143.0", null, null, "5", "0", "True", "0", "1", "0", "0.0", null, "5.916202062607435", "3.295836866004329", "7.311886164077165", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["43", "pythonistaguild/twitchio", "589", "2021-12-19", "1.0", "0.3333", "2.3814", "375", "27", "1504", "Python", "0.3333333333333334", "0.324470983286606", "0.3548651696325048", "0.1166886361072576", "0", "581.0", "0.0204052511415525", "143.0", null, null, "6", "0", "True", "0", "1", "0", "0.6931471805599453", "0.2876570721392756", "5.929589143389895", "3.332204510175204", "7.316548177182976", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["44", "pythonistaguild/twitchio", "590", "2021-12-26", "0.0", "0.6667", null, "377", "28", "1511", "Python", "0.5000000000000001", "0.4013363661992121", "0.3548651696325048", "0.269409335365653", "0", "581.0", "0.0204052511415525", "143.0", null, null, "7", "0", "True", "0", "1", "0", "0.0", "0.5108456235659934", "5.934894195619588", "3.367295829986474", "7.321188556739478", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["45", "pythonistaguild/twitchio", "591", "2022-01-02", "0.0", null, null, "377", "28", "1518", "Python", "0.5000000000000001", "0.5250041250206465", "0.3548651696325048", "0.3780998063414457", "0", "581.0", "0.0204052511415525", "143.0", null, null, "8", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.325807502595773", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["46", "pythonistaguild/twitchio", "592", "2022-01-09", "0.0", null, null, "377", "28", "1525", "Python", "0.1666666666666667", "0.4013363661992121", "0.3548651696325048", "0.0686854732260053", "0", "581.0", "0.0204052511415525", "143.0", null, null, "9", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.330405211844402", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["47", "pythonistaguild/twitchio", "593", "2022-01-16", "0.0", "1.0", null, "377", "28", "1532", "Python", "0.5000000000000001", "0.5250041250206465", "0.3548651696325048", "0.3780998063414457", "0", "581.0", "0.0204052511415525", "143.0", null, null, "10", "0", "True", "0", "1", "0", "0.0", "0.6931471805599453", "5.934894195619588", "3.367295829986474", "7.334981878871814", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["48", "pythonistaguild/twitchio", "594", "2022-01-23", "0.0", null, null, "377", "28", "1539", "Python", "0.25", "0.4833561516109304", "0.3548651696325048", "0.1464232940158239", "0", "581.0", "0.0204052511415525", "143.0", null, null, "11", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.339537695407674", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"], ["49", "pythonistaguild/twitchio", "595", "2022-01-30", "0.0", null, null, "377", "28", "1546", "Python", "0.1428571428571429", "0.5000249999999792", "0.2715654479294401", "0.0585051507075485", "0", "581.0", "0.0204052511415525", "143.0", null, null, "12", "0", "True", "0", "1", "0", "0.0", null, "5.934894195619588", "3.367295829986474", "7.344072850573066", "1_0", "0_0", "8.84246002419529", "4.976733742420574", "6.376726947898627", "Rust", "saturation", "3.0", "1.3862943611198906", "6.366470447731438", "0.020199853432168523", "4.969813299576001"]], "shape": {"columns": 43, "rows": 5506407}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>...</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>578</td>\n", "      <td>2021-10-03</td>\n", "      <td>8.0</td>\n", "      <td>0.4000</td>\n", "      <td>39.6172</td>\n", "      <td>6815</td>\n", "      <td>137</td>\n", "      <td>503</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>8.842460</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>579</td>\n", "      <td>2021-10-10</td>\n", "      <td>13.0</td>\n", "      <td>0.8462</td>\n", "      <td>54.4169</td>\n", "      <td>6831</td>\n", "      <td>137</td>\n", "      <td>510</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>8.842460</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>580</td>\n", "      <td>2021-10-17</td>\n", "      <td>9.0</td>\n", "      <td>0.4762</td>\n", "      <td>12.0828</td>\n", "      <td>6842</td>\n", "      <td>137</td>\n", "      <td>517</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>8.842460</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>581</td>\n", "      <td>2021-10-24</td>\n", "      <td>8.0</td>\n", "      <td>0.7778</td>\n", "      <td>41.4331</td>\n", "      <td>6850</td>\n", "      <td>137</td>\n", "      <td>524</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>8.842460</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>582</td>\n", "      <td>2021-10-31</td>\n", "      <td>5.0</td>\n", "      <td>0.6667</td>\n", "      <td>54.8299</td>\n", "      <td>6857</td>\n", "      <td>137</td>\n", "      <td>531</td>\n", "      <td>Rust</td>\n", "      <td>...</td>\n", "      <td>8.842460</td>\n", "      <td>4.976734</td>\n", "      <td>6.376727</td>\n", "      <td>Rust</td>\n", "      <td>saturation</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>6.366470</td>\n", "      <td>0.020200</td>\n", "      <td>4.969813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506402</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>357</td>\n", "      <td>2017-07-09</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>353</td>\n", "      <td>16</td>\n", "      <td>818</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>4.644391</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506403</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>358</td>\n", "      <td>2017-07-16</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>353</td>\n", "      <td>16</td>\n", "      <td>825</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>4.644391</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506404</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>359</td>\n", "      <td>2017-07-23</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>353</td>\n", "      <td>16</td>\n", "      <td>832</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>4.644391</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506405</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>360</td>\n", "      <td>2017-07-30</td>\n", "      <td>1.0</td>\n", "      <td>1.0000</td>\n", "      <td>101.6147</td>\n", "      <td>354</td>\n", "      <td>17</td>\n", "      <td>839</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>4.644391</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5506406</th>\n", "      <td>1-liners/1-liners</td>\n", "      <td>361</td>\n", "      <td>2017-08-06</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>354</td>\n", "      <td>17</td>\n", "      <td>846</td>\n", "      <td>JavaScript</td>\n", "      <td>...</td>\n", "      <td>4.644391</td>\n", "      <td>2.079442</td>\n", "      <td>4.025352</td>\n", "      <td>C#</td>\n", "      <td>decelerating</td>\n", "      <td>3.0</td>\n", "      <td>1.386294</td>\n", "      <td>3.332205</td>\n", "      <td>0.016298</td>\n", "      <td>2.079442</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5506407 rows × 43 columns</p>\n", "</div>"], "text/plain": ["                                  repo_name  standardized_time_weeks  \\\n", "0        0LNetworkCommunity/libra-legacy-v6                      578   \n", "1        0LNetworkCommunity/libra-legacy-v6                      579   \n", "2        0LNetworkCommunity/libra-legacy-v6                      580   \n", "3        0LNetworkCommunity/libra-legacy-v6                      581   \n", "4        0LNetworkCommunity/libra-legacy-v6                      582   \n", "...                                     ...                      ...   \n", "5506402                   1-liners/1-liners                      357   \n", "5506403                   1-liners/1-liners                      358   \n", "5506404                   1-liners/1-liners                      359   \n", "5506405                   1-liners/1-liners                      360   \n", "5506406                   1-liners/1-liners                      361   \n", "\n", "           datetime  pr_throughput  pull_request_success_rate  time_to_merge  \\\n", "0        2021-10-03            8.0                     0.4000        39.6172   \n", "1        2021-10-10           13.0                     0.8462        54.4169   \n", "2        2021-10-17            9.0                     0.4762        12.0828   \n", "3        2021-10-24            8.0                     0.7778        41.4331   \n", "4        2021-10-31            5.0                     0.6667        54.8299   \n", "...             ...            ...                        ...            ...   \n", "5506402  2017-07-09            0.0                        NaN            NaN   \n", "5506403  2017-07-16            0.0                        NaN            NaN   \n", "5506404  2017-07-23            0.0                     1.0000            NaN   \n", "5506405  2017-07-30            1.0                     1.0000       101.6147   \n", "5506406  2017-08-06            0.0                     1.0000            NaN   \n", "\n", "         project_commits  project_contributors  project_age mainLanguage  ...  \\\n", "0                   6815                   137          503         Rust  ...   \n", "1                   6831                   137          510         Rust  ...   \n", "2                   6842                   137          517         Rust  ...   \n", "3                   6850                   137          524         Rust  ...   \n", "4                   6857                   137          531         Rust  ...   \n", "...                  ...                   ...          ...          ...  ...   \n", "5506402              353                    16          818   JavaScript  ...   \n", "5506403              353                    16          825   JavaScript  ...   \n", "5506404              353                    16          832   JavaScript  ...   \n", "5506405              354                    17          839   JavaScript  ...   \n", "5506406              354                    17          846   JavaScript  ...   \n", "\n", "         log_project_commits_before_treatment  \\\n", "0                                    8.842460   \n", "1                                    8.842460   \n", "2                                    8.842460   \n", "3                                    8.842460   \n", "4                                    8.842460   \n", "...                                       ...   \n", "5506402                              4.644391   \n", "5506403                              4.644391   \n", "5506404                              4.644391   \n", "5506405                              4.644391   \n", "5506406                              4.644391   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         4.976734   \n", "1                                         4.976734   \n", "2                                         4.976734   \n", "3                                         4.976734   \n", "4                                         4.976734   \n", "...                                            ...   \n", "5506402                                   2.079442   \n", "5506403                                   2.079442   \n", "5506404                                   2.079442   \n", "5506405                                   2.079442   \n", "5506406                                   2.079442   \n", "\n", "         log_project_age_before_treatment  project_main_language  \\\n", "0                                6.376727                   Rust   \n", "1                                6.376727                   Rust   \n", "2                                6.376727                   Rust   \n", "3                                6.376727                   Rust   \n", "4                                6.376727                   Rust   \n", "...                                   ...                    ...   \n", "5506402                          4.025352                     C#   \n", "5506403                          4.025352                     C#   \n", "5506404                          4.025352                     C#   \n", "5506405                          4.025352                     C#   \n", "5506406                          4.025352                     C#   \n", "\n", "         growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0          saturation        3.0       1.386294    6.366470   \n", "1          saturation        3.0       1.386294    6.366470   \n", "2          saturation        3.0       1.386294    6.366470   \n", "3          saturation        3.0       1.386294    6.366470   \n", "4          saturation        3.0       1.386294    6.366470   \n", "...               ...        ...            ...         ...   \n", "5506402  decelerating        3.0       1.386294    3.332205   \n", "5506403  decelerating        3.0       1.386294    3.332205   \n", "5506404  decelerating        3.0       1.386294    3.332205   \n", "5506405  decelerating        3.0       1.386294    3.332205   \n", "5506406  decelerating        3.0       1.386294    3.332205   \n", "\n", "         log_commit_percent  log_commits  \n", "0                  0.020200     4.969813  \n", "1                  0.020200     4.969813  \n", "2                  0.020200     4.969813  \n", "3                  0.020200     4.969813  \n", "4                  0.020200     4.969813  \n", "...                     ...          ...  \n", "5506402            0.016298     2.079442  \n", "5506403            0.016298     2.079442  \n", "5506404            0.016298     2.079442  \n", "5506405            0.016298     2.079442  \n", "5506406            0.016298     2.079442  \n", "\n", "[5506407 rows x 43 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["compiled_data['log_newcomers'] = np.log(compiled_data['newcomers'] + 1)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["\n", "compiled_data['log_tenure'] = np.log(compiled_data['tenure'] + 1)\n", "compiled_data['log_commit_percent'] = np.log(compiled_data['commit_percent'] + 1)\n", "compiled_data['log_commits'] = np.log(compiled_data['commits'] + 1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2440555/3274868643.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '8.84246002419529' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "/tmp/ipykernel_2440555/3274868643.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '4.976733742420574' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "/tmp/ipykernel_2440555/3274868643.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '6.376726947898627' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 12\u001b[0m\n\u001b[1;32m     10\u001b[0m     treated_row \u001b[38;5;241m=\u001b[39m treated_group\u001b[38;5;241m.\u001b[39miloc[\u001b[38;5;241m0\u001b[39m][[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlog_project_commits\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlog_project_contributors\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlog_project_age\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmainLanguage\u001b[39m\u001b[38;5;124m'\u001b[39m]]\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;66;03m# rename ['log_project_commits', 'log_project_contributors', 'log_project_age'] into [log_project_commits_before_treatment, log_project_contributors_before_treatment, log_project_age_before_treatment]\u001b[39;00m\n\u001b[0;32m---> 12\u001b[0m     \u001b[43mcompiled_data_test\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43mcompiled_data_test\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mcohort_id\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mcohort_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mlog_project_commits_before_treatment\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mlog_project_contributors_before_treatment\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mlog_project_age_before_treatment\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mproject_main_language\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m treated_row\u001b[38;5;241m.\u001b[39mvalues\n\u001b[1;32m     13\u001b[0m compiled_data_test\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/indexing.py:911\u001b[0m, in \u001b[0;36m_LocationIndexer.__setitem__\u001b[0;34m(self, key, value)\u001b[0m\n\u001b[1;32m    908\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_has_valid_setitem_indexer(key)\n\u001b[1;32m    910\u001b[0m iloc \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miloc\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39miloc\n\u001b[0;32m--> 911\u001b[0m \u001b[43miloc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_setitem_with_indexer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/indexing.py:1942\u001b[0m, in \u001b[0;36m_iLocIndexer._setitem_with_indexer\u001b[0;34m(self, indexer, value, name)\u001b[0m\n\u001b[1;32m   1939\u001b[0m \u001b[38;5;66;03m# align and set the values\u001b[39;00m\n\u001b[1;32m   1940\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m take_split_path:\n\u001b[1;32m   1941\u001b[0m     \u001b[38;5;66;03m# We have to operate column-wise\u001b[39;00m\n\u001b[0;32m-> 1942\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_setitem_with_indexer_split_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1943\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1944\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_setitem_single_block(indexer, value, name)\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/indexing.py:2016\u001b[0m, in \u001b[0;36m_iLocIndexer._setitem_with_indexer_split_path\u001b[0;34m(self, indexer, value, name)\u001b[0m\n\u001b[1;32m   2013\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(ilocs) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mlen\u001b[39m(value):\n\u001b[1;32m   2014\u001b[0m     \u001b[38;5;66;03m# We are setting multiple columns in a single row.\u001b[39;00m\n\u001b[1;32m   2015\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m loc, v \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(ilocs, value):\n\u001b[0;32m-> 2016\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_setitem_single_column\u001b[49m\u001b[43m(\u001b[49m\u001b[43mloc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpi\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2018\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(ilocs) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m com\u001b[38;5;241m.\u001b[39mis_null_slice(pi) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   2019\u001b[0m     \u001b[38;5;66;03m# This is a setitem-with-expansion, see\u001b[39;00m\n\u001b[1;32m   2020\u001b[0m     \u001b[38;5;66;03m#  test_loc_setitem_empty_append_expands_rows_mixed_dtype\u001b[39;00m\n\u001b[1;32m   2021\u001b[0m     \u001b[38;5;66;03m# e.g. df = DataFrame(columns=[\"x\", \"y\"])\u001b[39;00m\n\u001b[1;32m   2022\u001b[0m     \u001b[38;5;66;03m#  df[\"x\"] = df[\"x\"].astype(np.int64)\u001b[39;00m\n\u001b[1;32m   2023\u001b[0m     \u001b[38;5;66;03m#  df.loc[:, \"x\"] = [1, 2, 3]\u001b[39;00m\n\u001b[1;32m   2024\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_setitem_single_column(ilocs[\u001b[38;5;241m0\u001b[39m], value, pi)\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/indexing.py:2175\u001b[0m, in \u001b[0;36m_iLocIndexer._setitem_single_column\u001b[0;34m(self, loc, value, plane_indexer)\u001b[0m\n\u001b[1;32m   2165\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m dtype \u001b[38;5;241m==\u001b[39m np\u001b[38;5;241m.\u001b[39mvoid:\n\u001b[1;32m   2166\u001b[0m         \u001b[38;5;66;03m# This means we're expanding, with multiple columns, e.g.\u001b[39;00m\n\u001b[1;32m   2167\u001b[0m         \u001b[38;5;66;03m#     df = pd.DataFrame({'A': [1,2,3], 'B': [4,5,6]})\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2170\u001b[0m         \u001b[38;5;66;03m# Here, we replace those temporary `np.void` columns with\u001b[39;00m\n\u001b[1;32m   2171\u001b[0m         \u001b[38;5;66;03m# columns of the appropriate dtype, based on `value`.\u001b[39;00m\n\u001b[1;32m   2172\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39miloc[:, loc] \u001b[38;5;241m=\u001b[39m construct_1d_array_from_inferred_fill_value(\n\u001b[1;32m   2173\u001b[0m             value, \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj)\n\u001b[1;32m   2174\u001b[0m         )\n\u001b[0;32m-> 2175\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumn_setitem\u001b[49m\u001b[43m(\u001b[49m\u001b[43mloc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mplane_indexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2177\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_clear_item_cache()\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/internals/managers.py:1337\u001b[0m, in \u001b[0;36mBlockManager.column_setitem\u001b[0;34m(self, loc, idx, value, inplace_only)\u001b[0m\n\u001b[1;32m   1335\u001b[0m     col_mgr\u001b[38;5;241m.\u001b[39msetitem_inplace(idx, value)\n\u001b[1;32m   1336\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1337\u001b[0m     new_mgr \u001b[38;5;241m=\u001b[39m \u001b[43mcol_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msetitem\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43midx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1338\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39miset(loc, new_mgr\u001b[38;5;241m.\u001b[39m_block\u001b[38;5;241m.\u001b[39mvalues, inplace\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m   1340\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m needs_to_warn:\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/internals/managers.py:415\u001b[0m, in \u001b[0;36mBaseBlockManager.setitem\u001b[0;34m(self, indexer, value, warn)\u001b[0m\n\u001b[1;32m    411\u001b[0m     \u001b[38;5;66;03m# No need to split if we either set all columns or on a single block\u001b[39;00m\n\u001b[1;32m    412\u001b[0m     \u001b[38;5;66;03m# manager\u001b[39;00m\n\u001b[1;32m    413\u001b[0m     \u001b[38;5;28mself\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m--> 415\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43msetitem\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.12/site-packages/pandas/core/internals/managers.py:363\u001b[0m, in \u001b[0;36mBaseBlockManager.apply\u001b[0;34m(self, f, align_keys, **kwargs)\u001b[0m\n\u001b[1;32m    361\u001b[0m         applied \u001b[38;5;241m=\u001b[39m b\u001b[38;5;241m.\u001b[39mapply(f, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    362\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 363\u001b[0m         applied \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    364\u001b[0m     result_blocks \u001b[38;5;241m=\u001b[39m extend_blocks(applied, result_blocks)\n\u001b[1;32m    366\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39mfrom_blocks(result_blocks, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxes)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# generate the model with each cohort group of the project characteristics just before the treatment\n", "compiled_data_test['log_project_commits_before_treatment'] = 0\n", "compiled_data_test['log_project_contributors_before_treatment'] = 0\n", "compiled_data_test['log_project_age_before_treatment'] = 0\n", "compiled_data_test['project_main_language'] = ''\n", "for cohort_id, group in compiled_data_test.groupby('cohort_id'):\n", "    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]\n", "    if treated_group.empty:\n", "        continue\n", "    treated_row = treated_group.iloc[0][['log_project_commits', 'log_project_contributors', 'log_project_age', 'mainLanguage']]\n", "    # rename ['log_project_commits', 'log_project_contributors', 'log_project_age'] into [log_project_commits_before_treatment, log_project_contributors_before_treatment, log_project_age_before_treatment]\n", "    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "compiled_data_test"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "315261d7-7ea0-42bd-946a-6030e6ad3911", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.14483094787845605", "5.43372200355424"]], "shape": {"columns": 42, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 42 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  log_project_commits_before_treatment  \\\n", "0        ...                              6.641182   \n", "1        ...                              6.641182   \n", "2        ...                              6.641182   \n", "3        ...                              6.641182   \n", "4        ...                              6.641182   \n", "...      ...                                   ...   \n", "3503016  ...                              5.521461   \n", "3503017  ...                              5.521461   \n", "3503018  ...                              5.521461   \n", "3503019  ...                              5.521461   \n", "3503020  ...                              5.521461   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         2.484907   \n", "1                                         2.484907   \n", "2                                         2.484907   \n", "3                                         2.484907   \n", "4                                         2.484907   \n", "...                                            ...   \n", "3503016                                   3.295837   \n", "3503017                                   3.295837   \n", "3503018                                   3.295837   \n", "3503019                                   3.295837   \n", "3503020                                   3.295837   \n", "\n", "         log_project_age_before_treatment  project_main_language  \\\n", "0                                5.817111                   Rust   \n", "1                                5.817111                   Rust   \n", "2                                5.817111                   Rust   \n", "3                                5.817111                   Rust   \n", "4                                5.817111                   Rust   \n", "...                                   ...                    ...   \n", "3503016                          6.741701                     C#   \n", "3503017                          6.741701                     C#   \n", "3503018                          6.741701                     C#   \n", "3503019                          6.741701                     C#   \n", "3503020                          6.741701                     C#   \n", "\n", "         growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        decelerating       15.0       2.772589    6.751101   \n", "1        decelerating       15.0       2.772589    6.751101   \n", "2        decelerating       15.0       2.772589    6.751101   \n", "3        decelerating       15.0       2.772589    6.751101   \n", "4        decelerating       15.0       2.772589    6.751101   \n", "...               ...        ...            ...         ...   \n", "3503016        steady        1.0       0.693147    6.710523   \n", "3503017        steady        1.0       0.693147    6.710523   \n", "3503018        steady        1.0       0.693147    6.710523   \n", "3503019        steady        1.0       0.693147    6.710523   \n", "3503020        steady        1.0       0.693147    6.710523   \n", "\n", "         log_commit_percent  log_commits  \n", "0                  0.144831     5.433722  \n", "1                  0.144831     5.433722  \n", "2                  0.144831     5.433722  \n", "3                  0.144831     5.433722  \n", "4                  0.144831     5.433722  \n", "...                     ...          ...  \n", "3503016            0.018605     2.197225  \n", "3503017            0.018605     2.197225  \n", "3503018            0.018605     2.197225  \n", "3503019            0.018605     2.197225  \n", "3503020            0.018605     2.197225  \n", "\n", "[3503021 rows x 42 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "2e1d6fbc-d5da-4859-a235-5c2088809c12", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"]], "shape": {"columns": 42, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 42 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  log_project_commits_before_treatment  \\\n", "0        ...                              6.641182   \n", "1        ...                              6.641182   \n", "2        ...                              6.641182   \n", "3        ...                              6.641182   \n", "4        ...                              6.641182   \n", "...      ...                                   ...   \n", "3503016  ...                              5.521461   \n", "3503017  ...                              5.521461   \n", "3503018  ...                              5.521461   \n", "3503019  ...                              5.521461   \n", "3503020  ...                              5.521461   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         2.484907   \n", "1                                         2.484907   \n", "2                                         2.484907   \n", "3                                         2.484907   \n", "4                                         2.484907   \n", "...                                            ...   \n", "3503016                                   3.295837   \n", "3503017                                   3.295837   \n", "3503018                                   3.295837   \n", "3503019                                   3.295837   \n", "3503020                                   3.295837   \n", "\n", "         log_project_age_before_treatment  project_main_language  \\\n", "0                                5.817111                   Rust   \n", "1                                5.817111                   Rust   \n", "2                                5.817111                   Rust   \n", "3                                5.817111                   Rust   \n", "4                                5.817111                   Rust   \n", "...                                   ...                    ...   \n", "3503016                          6.741701                     C#   \n", "3503017                          6.741701                     C#   \n", "3503018                          6.741701                     C#   \n", "3503019                          6.741701                     C#   \n", "3503020                          6.741701                     C#   \n", "\n", "         growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        decelerating       15.0       2.772589    6.751101   \n", "1        decelerating       15.0       2.772589    6.751101   \n", "2        decelerating       15.0       2.772589    6.751101   \n", "3        decelerating       15.0       2.772589    6.751101   \n", "4        decelerating       15.0       2.772589    6.751101   \n", "...               ...        ...            ...         ...   \n", "3503016        steady        1.0       0.693147    6.710523   \n", "3503017        steady        1.0       0.693147    6.710523   \n", "3503018        steady        1.0       0.693147    6.710523   \n", "3503019        steady        1.0       0.693147    6.710523   \n", "3503020        steady        1.0       0.693147    6.710523   \n", "\n", "         log_commit_percent  log_commits  \n", "0                  0.144831     5.433722  \n", "1                  0.144831     5.433722  \n", "2                  0.144831     5.433722  \n", "3                  0.144831     5.433722  \n", "4                  0.144831     5.433722  \n", "...                     ...          ...  \n", "3503016            0.018605     2.197225  \n", "3503017            0.018605     2.197225  \n", "3503018            0.018605     2.197225  \n", "3503019            0.018605     2.197225  \n", "3503020            0.018605     2.197225  \n", "\n", "[3503021 rows x 42 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# newcomers_analysis.to_csv(\"../result/did_result_20250212/newcomers_analysis.csv\", index=False)\n", "test = pd.read_csv(\"../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers.csv\")\n", "test"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot the distribution of project_age_before_treatment\n", "plt.hist(test['project_age'], bins=100)\n", "plt.title('Distribution of project_age')\n", "plt.xlabel('project_age')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot the distribution of project_age_before_treatment\n", "plt.hist(test['log_project_age'], bins=100)\n", "plt.title('Distribution of log_project_age')\n", "plt.xlabel('log_project_age')\n", "plt.ylabel('Frequency')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["project_main_language\n", "Python        788338\n", "JavaScript    672799\n", "TypeScript    367813\n", "Go            338300\n", "Java          336657\n", "C++           292533\n", "C             208286\n", "PHP           198240\n", "C#            165256\n", "Rust          134799\n", "Name: count, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["test['project_main_language'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}