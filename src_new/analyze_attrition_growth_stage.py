import logging
import pandas as pd
import numpy as np
from scipy.optimize import curve_fit
from statsmodels.nonparametric.smoothers_lowess import lowess
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - Line %(lineno)d - %(message)s',
    # setting up the logging file ../logs/analyze_attrition_growth_stage.log
    handlers=[logging.StreamHandler(), logging.FileHandler('../logs/analyze_attrition_growth_stage.log')]
)
# ignore warnings for better readability
import warnings
warnings.filterwarnings('ignore')
# Load attritions data
attritions = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')

def analyze_repo_commits(repo_name):
    # 初始化环境和数据加载
    import pandas as pd
    import numpy as np
    from scipy.optimize import curve_fit
    from statsmodels.nonparametric.smoothers_lowess import lowess

    # 数据预处理（统一为月度数据）
    commits = pd.read_csv(f'../data/commits/{repo_name.replace("/","_")}_commits.csv')
    commits['date'] = pd.to_datetime(commits['date']).dt.to_period('M').dt.to_timestamp()
    commits = commits.groupby('date').size().resample('ME').sum().reset_index(name='counts')
    
    # 计算累积提交量
    commits['cumulative'] = commits['counts'].cumsum()
    start_date = commits['date'].min()
    
    # 生成完整时间序列（处理缺失月份）
    full_dates = pd.date_range(start=start_date, end=commits['date'].max(), freq='ME')
    commits = commits.set_index('date').reindex(full_dates).fillna({'counts':0, 'cumulative':0}).reset_index()
    commits['cumulative'] = commits['counts'].cumsum()
    commits.rename(columns={'index':'date'}, inplace=True)

    # 转换为相对月份数
    commits['months_since_start'] = (commits['date'] - start_date).apply(
        lambda x: x.days//30 + x.seconds//86400  # 精确月份计算
    )

    # 定义增长模型（使用月份作为时间单位）
    def logistic_model(t, K, r, t0):
        return K / (1 + np.exp(-r * (t - t0)))

    def gompertz_model(t, a, b, c):
        return a * np.exp(-b * np.exp(-c * t))

    models = {
        'linear': lambda t, a, b: a*t + b,
        'exponential': lambda t, a, b: a*np.exp(b*t),
        'logistic': logistic_model,
        'gompertz': gompertz_model
    }

    # 模型拟合
    x_data = commits['months_since_start'].values
    y_data = commits['cumulative'].values
    best_model = None
    best_score = float('inf')

    for name, func in models.items():
        try:
            params, _ = curve_fit(func, x_data, y_data, maxfev=5000)
            pred = func(x_data, *params)
            mse = np.mean((y_data - pred)**2)
            if mse < best_score:
                best_model = (name, func, params)
                best_score = mse
        except Exception as e:
            print(f"{name} model fit failed: {str(e)}")

    # 统一增长阶段分类函数
    def classify_growth_phase(months_data, counts_data, model_info=None):
        """统一月份数据分类逻辑"""
        if len(months_data) < 3:
            return "first 3 months"

        # 趋势平滑处理
        smooth_frac = max(0.1, min(0.4, 6/len(months_data)))
        smoothed = lowess(counts_data, months_data, frac=smooth_frac, it=3)

        # 计算月度增长率
        gr = np.diff(smoothed[:, 1]) / np.diff(smoothed[:, 0] + 1e-6)
        acceleration = np.diff(gr) if len(gr)>1 else np.array([0])

        # 时间衰减权重（最近6个月权重最大）
        weights = np.exp(np.linspace(0, 3, len(gr)))
        w_mean = np.average(gr, weights=weights)
        w_std = np.sqrt(np.average((gr - w_mean)**2, weights=weights))

        # 核心分类逻辑
        current_gr = gr[-1] if len(gr)>0 else 0
        current_accel = acceleration[-1] if len(acceleration)>0 else 0

        if current_gr > w_mean + 0.8*w_std:
            phase = "accelerating"
        elif current_gr > w_mean - 0.8*w_std:
            if current_accel > 0.15*w_mean:
                phase = "accelerating"
            elif current_accel < -0.15*w_mean:
                phase = "decelerating"
            else:
                phase = "steady"
        else:
            phase = "decelerating" if current_gr > 0 else "saturation"

        # 模型辅助判断
        if model_info:
            name, func, params = model_info
            try:
                current_val = func(months_data[-1], *params)
                if name == 'logistic' and current_val >= 0.93*params[0]:
                    phase = "saturation"
                elif name == 'gompertz' and current_val >= 0.95*params[0]:
                    phase = "saturation"
            except:
                pass

        # 最终校验
        # if phase == "accelerating" and np.mean(gr[-3:]) < w_mean:
        #     phase = "steady (volatile)"
        return phase

    # 人员流失时点处理
    attrition_data = attritions[attritions['repo_name'] == repo_name]
    burst_ids = attritions[attritions['repo_name'] == repo_name]['burst'].unique()
    burst_dev_stage = pd.DataFrame(columns=['attrition_date', 'attrition_developer', 'growth_phase'])
    
    for burst_id in burst_ids:
        burst_data = attrition_data[attrition_data['burst'] == burst_id]
        if len(burst_data) > 1: # choose the last attrition_date
            attrition_date = burst_data['attrition_date'].iloc[-1]
            developer = burst_data['attrition_developer'].iloc[-1]
        else: # just one attrition_date
            attrition_date = burst_data['attrition_date'].iloc[0]
            developer = burst_data['attrition_developer'].iloc[0]
        
        date = pd.to_datetime(attrition_date).to_period('M').to_timestamp()
        if date >= start_date:
            phase = classify_growth_phase(
                commits[commits['date'] <= date]['months_since_start'].values,
                commits[commits['date'] <= date]['cumulative'].values,
                best_model
            )
            
            # 更新attrition的growth_phase
            attritions.loc[attritions.burst == burst_id, 'growth_phase'] = phase
            
            # 记录到结果DataFrame
            new_row = pd.DataFrame({
                'attrition_date': [date],
                'attrition_developer': [developer],
                'growth_phase': [phase]
            })
            burst_dev_stage = pd.concat([burst_dev_stage, new_row], ignore_index=True)

    return burst_dev_stage

# 处理所有repo并保存结果
def process_all_repos():
    repo_names = attritions['repo_name'].unique().tolist()
    all_results = []
    
    for repo_name in repo_names:
        try:
            print(f"Processing {repo_name}...")
            results = analyze_repo_commits(repo_name)
            all_results.append(results)
        except Exception as e:
            print(f"Error processing {repo_name}: {e}")
    
    # 保存更新后的attritions数据
    attritions.to_csv('../data/attritions_20250227_add_burst_merged_add_growth_phase.csv', index=False)
    
    # 可选：合并所有结果
    if all_results:
        combined_results = pd.concat(all_results, ignore_index=True)
        combined_results.to_csv('../data/combined_attrition_growth_phases.csv', index=False)

# 运行处理程序
if __name__ == "__main__":
    process_all_repos()
