{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Aim: add other outcome variables to the original panel data 'compiled_data_test_with_features.csv'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "67a0671a-b6b8-4d47-a484-730bbef6a393", "rows": [["0", "sparklemotion/nokogiri", "2011-02-14", "1"], ["1", "sparklemotion/nokogiri", "2011-05-16", "1"], ["2", "sparklemotion/nokogiri", "2011-05-23", "1"], ["3", "sparklemotion/nokogiri", "2011-06-06", "1"], ["4", "sparklemotion/nokogiri", "2011-06-27", "1"], ["5", "sparklemotion/nokogiri", "2011-08-08", "2"], ["6", "sparklemotion/nokogiri", "2011-08-15", "1"], ["7", "sparklemotion/nokogiri", "2011-08-29", "1"], ["8", "sparklemotion/nokogiri", "2011-10-03", "1"], ["9", "sparklemotion/nokogiri", "2011-11-07", "8"], ["10", "sparklemotion/nokogiri", "2011-11-14", "1"], ["11", "sparklemotion/nokogiri", "2011-11-21", "4"], ["12", "sparklemotion/nokogiri", "2012-01-30", "7"], ["13", "sparklemotion/nokogiri", "2012-02-13", "2"], ["14", "sparklemotion/nokogiri", "2012-03-05", "1"], ["15", "sparklemotion/nokogiri", "2012-03-12", "2"], ["16", "sparklemotion/nokogiri", "2012-03-26", "2"], ["17", "sparklemotion/nokogiri", "2012-04-02", "1"], ["18", "sparklemotion/nokogiri", "2012-04-09", "1"], ["19", "sparklemotion/nokogiri", "2012-05-14", "1"], ["20", "sparklemotion/nokogiri", "2012-05-21", "1"], ["21", "sparklemotion/nokogiri", "2012-06-04", "2"], ["22", "sparklemotion/nokogiri", "2012-06-11", "2"], ["23", "sparklemotion/nokogiri", "2012-06-18", "1"], ["24", "sparklemotion/nokogiri", "2012-07-02", "2"], ["25", "sparklemotion/nokogiri", "2012-07-16", "1"], ["26", "sparklemotion/nokogiri", "2012-07-23", "1"], ["27", "sparklemotion/nokogiri", "2012-07-30", "1"], ["28", "sparklemotion/nokogiri", "2012-08-27", "1"], ["29", "sparklemotion/nokogiri", "2012-09-03", "1"], ["30", "sparklemotion/nokogiri", "2012-09-17", "1"], ["31", "sparklemotion/nokogiri", "2012-10-01", "3"], ["32", "sparklemotion/nokogiri", "2012-11-26", "1"], ["33", "sparklemotion/nokogiri", "2012-12-10", "1"], ["34", "sparklemotion/nokogiri", "2013-01-07", "1"], ["35", "sparklemotion/nokogiri", "2013-03-18", "1"], ["36", "sparklemotion/nokogiri", "2013-04-01", "1"], ["37", "sparklemotion/nokogiri", "2013-05-13", "3"], ["38", "sparklemotion/nokogiri", "2013-06-03", "1"], ["39", "sparklemotion/nokogiri", "2013-06-10", "2"], ["40", "sparklemotion/nokogiri", "2013-06-24", "1"], ["41", "sparklemotion/nokogiri", "2013-07-01", "3"], ["42", "sparklemotion/nokogiri", "2013-07-22", "1"], ["43", "sparklemotion/nokogiri", "2013-08-05", "2"], ["44", "sparklemotion/nokogiri", "2013-10-21", "4"], ["45", "sparklemotion/nokogiri", "2013-10-28", "4"], ["46", "sparklemotion/nokogiri", "2013-11-04", "1"], ["47", "sparklemotion/nokogiri", "2013-11-11", "1"], ["48", "sparklemotion/nokogiri", "2013-11-25", "1"], ["49", "sparklemotion/nokogiri", "2013-12-09", "2"]], "shape": {"columns": 3, "rows": 4864129}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "      <td>2011-02-14</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "      <td>2011-05-16</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "      <td>2011-05-23</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "      <td>2011-06-06</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "      <td>2011-06-27</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4864124</th>\n", "      <td>motiondivision/motion</td>\n", "      <td>2024-08-05</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4864125</th>\n", "      <td>motiondivision/motion</td>\n", "      <td>2024-08-12</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4864126</th>\n", "      <td>motiondivision/motion</td>\n", "      <td>2024-08-19</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4864127</th>\n", "      <td>motiondivision/motion</td>\n", "      <td>2024-08-26</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4864128</th>\n", "      <td>motiondivision/motion</td>\n", "      <td>2024-10-07</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4864129 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                      repo_name    datetime  pr_throughput\n", "0        sparklemotion/nokogiri  2011-02-14              1\n", "1        sparklemotion/nokogiri  2011-05-16              1\n", "2        sparklemotion/nokogiri  2011-05-23              1\n", "3        sparklemotion/nokogiri  2011-06-06              1\n", "4        sparklemotion/nokogiri  2011-06-27              1\n", "...                         ...         ...            ...\n", "4864124   motiondivision/motion  2024-08-05              2\n", "4864125   motiondivision/motion  2024-08-12              4\n", "4864126   motiondivision/motion  2024-08-19              2\n", "4864127   motiondivision/motion  2024-08-26              3\n", "4864128   motiondivision/motion  2024-10-07              1\n", "\n", "[4864129 rows x 3 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "productivity = pd.read_csv('../result/productivity_metrics_20250227.csv')\n", "productivity\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# save the list of repo_name into a dataframe\n", "repo_name = productivity['repo_name'].unique()\n", "repo_name = pd.DataFrame(repo_name)\n", "repo_name.columns = ['repo_name']\n", "repo_name.to_csv('../result/repo_name_list.csv', index=False)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "5d372d79-6267-4568-bcd8-fb7daf0ac80b", "rows": [["0", "sparklemotion/nokogiri"], ["1", "davidb/scala-maven-plugin"], ["2", "tcurdt/jdeb"], ["3", "junit-team/junit4"], ["4", "yui/yuicompressor"], ["5", "unclebob/fitnesse"], ["6", "connectbot/connectbot"], ["7", "bpellin/keepassdroid"], ["8", "rnewson/couchdb-lucene"], ["9", "nodebox/nodebox"], ["10", "cwensel/cascading"], ["11", "cucumber-attic/cuke4duke"], ["12", "bndtools/bndtools"], ["13", "twitter4j/twitter4j"], ["14", "magro/memcached-session-manager"], ["15", "caelum/vraptor"], ["16", "maxcom/lorsource"], ["17", "rzwitserloot/lombok"], ["18", "voldemort/voldemort"], ["19", "jdbi/jdbi"], ["20", "simpligility/android-maven-plugin"], ["21", "jblas-project/jblas"], ["22", "pocmo/yaaic"], ["23", "ccw-ide/ccw"], ["24", "novoda/android-demos"], ["25", "qos-ch/logback"], ["26", "qos-ch/slf4j"], ["27", "apache/shiro"], ["28", "haraldk/twelvemonkeys"], ["29", "fusesource/jansi"], ["30", "yaxim-org/yaxim"], ["31", "cucumber-attic/gherkin2"], ["32", "talklittle/reddit-is-fun"], ["33", "twilio/twilio-java"], ["34", "webmetrics/browsermob-proxy"], ["35", "sitemesh/sitemesh2"], ["36", "sirthias/parboiled"], ["37", "resty-gwt/resty-gwt"], ["38", "maven-nar/nar-maven-plugin"], ["39", "martint/jmxutils"], ["40", "jruby/joni"], ["41", "sanger-pathogens/artemis"], ["42", "notnoop/java-apns"], ["43", "trifork/erjang"], ["44", "torquebox/jruby-maven-plugins"], ["45", "jhy/jsoup"], ["46", "jberkel/sms-backup-plus"], ["47", "ervandew/eclim"], ["48", "sbt/junit-interface"], ["49", "j<PERSON><PERSON><PERSON>/ognl"]], "shape": {"columns": 1, "rows": 50541}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>davidb/scala-maven-plugin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>tcurdt/jdeb</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>junit-team/junit4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>yui/yuicompressor</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50536</th>\n", "      <td>wso2/product-micro-integrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50537</th>\n", "      <td>mir-evaluation/mir_eval</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50538</th>\n", "      <td>graphql-hive/console</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50539</th>\n", "      <td>pixi-viewport/pixi-viewport</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50540</th>\n", "      <td>motiondivision/motion</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>50541 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                           repo_name\n", "0             sparklemotion/nokogiri\n", "1          davidb/scala-maven-plugin\n", "2                        tcurdt/jdeb\n", "3                  junit-team/junit4\n", "4                  yui/yuicompressor\n", "...                              ...\n", "50536  wso2/product-micro-integrator\n", "50537        mir-evaluation/mir_eval\n", "50538           graphql-hive/console\n", "50539    pixi-viewport/pixi-viewport\n", "50540          motiondivision/motion\n", "\n", "[50541 rows x 1 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_list = pd.read_csv('../result/repo_name_list.csv')\n", "repo_list"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "377368b2-8568-43c0-a345-98623fda9fd7", "rows": [["0", "10up/autoshare-for-twitter", "486", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "49", "-12", "1", "False", "0", "0", "0", "197", "5", "342", "0.0", "5.288267030694535", "1.791759469228055", "5.83773044716594", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["1", "10up/autoshare-for-twitter", "487", "0.0", "1.0", "2.0", "-0.0349650349650349", "0.5", "-1.09861228866811", "0.3546612443924433", "0.3660254037844386", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "50", "-11", "1", "False", "0", "0", "0", "197", "5", "349", "0.0", "5.288267030694535", "1.791759469228055", "5.857933154483459", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["2", "10up/autoshare-for-twitter", "488", "4.0", "1.0", "2.0", "0.1223776223776223", "0.75", "0.9162907318741548", "0.8410806526182262", "0.6653477824119316", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "51", "-10", "1", "False", "0", "0", "0", "209", "5", "356", "1.6094379124341005", "5.3471075307174685", "1.791759469228055", "5.877735781779639", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["3", "10up/autoshare-for-twitter", "489", "1.0", "1.0", "2.0", "0.0979020979020979", "0.8333333333333334", "0.6931471805599448", "0.8214907876837801", "0.6405201949797534", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "52", "-9", "1", "False", "0", "0", "0", "210", "5", "363", "0.6931471805599453", "5.351858133476067", "1.791759469228055", "5.8971538676367405", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["4", "10up/autoshare-for-twitter", "490", "3.0", "1.0", "2.0", "0.1433566433566433", "1.0833333333333333", "1.38629436111989", "0.921984989635266", "0.8178456006794831", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "53", "-8", "1", "False", "0", "0", "0", "220", "5", "370", "1.3862943611198906", "5.3981627015177525", "1.791759469228055", "5.916202062607435", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["5", "10up/autoshare-for-twitter", "491", "3.0", "1.0", "2.0", "0.2587412587412587", "1.1666666666666667", "0.2876820724517805", "0.8106668070686921", "0.5831283861446792", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "54", "-7", "1", "False", "0", "0", "0", "249", "6", "377", "1.3862943611198906", "5.521460917862246", "1.9459101490553128", "5.934894195619588", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["6", "10up/autoshare-for-twitter", "492", "0.0", "1.0", "2.0", "0.1608391608391608", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "55", "-6", "1", "False", "0", "0", "0", "255", "6", "384", "0.0", "5.545177444479562", "1.9459101490553128", "5.953243334287784", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["7", "10up/autoshare-for-twitter", "493", "0.0", "1.0", "2.0", "0.0629370629370629", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "56", "-5", "1", "False", "0", "0", "0", "259", "6", "391", "0.0", "5.560681631015528", "1.9459101490553128", "5.971261839790462", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["8", "10up/autoshare-for-twitter", "494", "1.0", "1.0", "2.0", "0.0034965034965034", "1.25", "0.6931471805599451", "0.8746974870756378", "0.7040031411428227", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "57", "-4", "1", "False", "0", "0", "0", "261", "6", "398", "0.6931471805599453", "5.568344503761097", "1.9459101490553128", "5.988961416889863", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["9", "10up/autoshare-for-twitter", "495", "1.0", "1.0", "2.0", "0.0279720279720279", "1.1666666666666667", "-0.4054651081081645", "0.6816145482921148", "0.3838963480989594", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "58", "-3", "1", "False", "0", "0", "0", "275", "6", "405", "0.6931471805599453", "5.62040086571715", "1.9459101490553128", "6.0063531596017325", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["10", "10up/autoshare-for-twitter", "496", "0.0", "1.0", "2.0", "-0.0244755244755244", "1.0833333333333333", "-0.6931471805599454", "0.5963275109839462", "0.3206231694796373", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "59", "-2", "1", "False", "0", "0", "0", "306", "7", "412", "0.0", "5.726847747587197", "2.079441541679836", "6.023447592961032", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["11", "10up/autoshare-for-twitter", "497", "5.0", "1.0", "2.0", "0.0769230769230769", "1.5", "1.791759469228055", "0.964145027597638", "0.9362933095037254", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "60", "-1", "1", "False", "0", "0", "0", "309", "7", "419", "1.791759469228055", "5.736572297479192", "2.079441541679836", "6.040254711277414", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["12", "10up/autoshare-for-twitter", "498", "2.0", "1.0", "2.0", "0.0279720279720279", "1.6666666666666667", "1.0986122886681096", "0.9407704701088077", "0.8618832502903921", "1", "257.0", "0.2171837708830549", "182.0", "7.0", "1.0", "PHP", "437", "61", "0", "1", "False", "0", "0", "0", "336", "8", "426", "1.0986122886681096", "5.820082930352362", "2.197224577336219", "6.056784013228624", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["13", "10up/autoshare-for-twitter", "499", "1.0", "1.0", "2.0", "-0.0734265734265734", "1.75", "0.6931471805599452", "0.9200588708986858", "0.7708306705345104", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "62", "1", "1", "True", "0", "1", "1", "336", "8", "433", "0.6931471805599453", "5.820082930352362", "2.197224577336219", "6.073044534100404", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["14", "10up/autoshare-for-twitter", "500", "1.0", "1.0", "2.0", "0.0", "1.5", "-0.9162907318741552", "0.6419204615368317", "0.2019040735186648", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "63", "2", "1", "True", "0", "1", "1", "337", "8", "440", "0.6931471805599453", "5.823045895483019", "2.197224577336219", "6.089044875446846", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["15", "10up/autoshare-for-twitter", "501", "0.0", "1.0", "2.0", "-0.0804195804195804", "1.4166666666666667", "-0.6931471805599453", "0.6733815605777215", "0.2725033461986623", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "64", "3", "1", "True", "0", "1", "1", "337", "8", "447", "0.0", "5.823045895483019", "2.197224577336219", "6.104793232414985", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["16", "10up/autoshare-for-twitter", "502", "0.0", "1.0", "2.0", "-0.0629370629370629", "1.1666666666666667", "-1.3862943611198906", "0.4453127259526225", "0.1655715707900131", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "65", "4", "1", "True", "0", "1", "1", "337", "8", "454", "0.0", "5.823045895483019", "2.197224577336219", "6.12029741895095", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["17", "10up/autoshare-for-twitter", "503", "0.0", "1.0", "2.0", "-0.0244755244755244", "0.9166666666666666", "-1.3862943611198906", "0.3847043671232542", "0.2191254981927748", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "66", "5", "1", "True", "0", "1", "1", "337", "8", "461", "0.0", "5.823045895483019", "2.197224577336219", "6.135564891081739", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["18", "10up/autoshare-for-twitter", "504", "0.0", "1.0", "2.0", "-0.1013986013986013", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "67", "6", "1", "True", "0", "1", "1", "337", "8", "468", "0.0", "5.823045895483019", "2.197224577336219", "6.150602768446279", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["19", "10up/autoshare-for-twitter", "505", "0.0", "1.0", "2.0", "-0.1783216783216783", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "68", "7", "1", "True", "0", "1", "1", "337", "8", "475", "0.0", "5.823045895483019", "2.197224577336219", "6.16541785423142", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["20", "10up/autoshare-for-twitter", "506", "0.0", "1.0", "2.0", "-0.2097902097902098", "0.8333333333333334", "-0.6931471805599453", "0.5349892557559008", "0.3594798050202465", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "69", "8", "1", "True", "0", "1", "1", "337", "8", "482", "0.0", "5.823045895483019", "2.197224577336219", "6.180016653652572", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["21", "10up/autoshare-for-twitter", "507", "0.0", "1.0", "2.0", "-0.2342657342657342", "0.75", "-0.6931471805599453", "0.5142093777192814", "0.3728848808245891", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "70", "9", "1", "True", "0", "1", "1", "337", "8", "489", "0.0", "5.823045895483019", "2.197224577336219", "6.194405391104672", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["22", "10up/autoshare-for-twitter", "508", "0.0", "1.0", "2.0", "-0.2972027972027972", "0.75", "0.0", "0.679178699175393", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "71", "10", "1", "True", "0", "1", "1", "337", "8", "496", "0.0", "5.823045895483019", "2.197224577336219", "6.208590026096629", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["23", "10up/autoshare-for-twitter", "509", "0.0", "1.0", "2.0", "-0.1328671328671328", "0.3333333333333333", "-1.791759469228055", "0.1887081616597619", "0.3549723794374981", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "72", "11", "1", "True", "0", "1", "1", "337", "8", "503", "0.0", "5.823045895483019", "2.197224577336219", "6.222576268071369", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["24", "10up/autoshare-for-twitter", "510", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.1666666666666666", "-1.0986122886681096", "0.2825301567477209", "0.4543519511761902", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "73", "12", "1", "True", "0", "1", "1", "337", "8", "510", "0.0", "5.823045895483019", "2.197224577336219", "6.236369590203704", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["25", "AdaptiveCpp/AdaptiveCpp", "486", "0.0", "3.0", "6.0", "-0.0734265734265734", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "77", "-12", "0", "False", "0", "0", "0", "387", "9", "359", "0.0", "5.961005339623274", "2.302585092994045", "5.886104031450156", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["26", "AdaptiveCpp/AdaptiveCpp", "487", "1.0", "3.0", "6.0", "-0.0664335664335664", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "78", "-11", "0", "False", "0", "0", "0", "388", "9", "366", "0.6931471805599453", "5.963579343618446", "2.302585092994045", "5.90536184805457", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["27", "AdaptiveCpp/AdaptiveCpp", "488", "0.0", "3.0", "6.0", "-0.0524475524475524", "0.75", "-1.09861228866811", "0.4137189778658776", "0.3049238750315606", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "79", "-10", "0", "False", "0", "0", "0", "389", "9", "373", "0.0", "5.966146739123692", "2.302585092994045", "5.924255797414531", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["28", "AdaptiveCpp/AdaptiveCpp", "489", "0.0", "3.0", "6.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "80", "-9", "0", "False", "0", "0", "0", "389", "9", "380", "0.0", "5.966146739123692", "2.302585092994045", "5.9427993751267", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["29", "AdaptiveCpp/AdaptiveCpp", "490", "2.0", "3.0", "6.0", "-0.0489510489510489", "0.8333333333333334", "1.0986122886681091", "0.8734646144545724", "0.7141264037070777", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "81", "-8", "0", "False", "0", "0", "0", "391", "9", "387", "1.0986122886681096", "5.971261839790462", "2.302585092994045", "5.961005339623274", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["30", "AdaptiveCpp/AdaptiveCpp", "491", "2.0", "3.0", "6.0", "0.0489510489510489", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "82", "-7", "0", "False", "0", "0", "0", "394", "10", "394", "1.0986122886681096", "5.978885764901122", "2.3978952727983707", "5.978885764901122", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["31", "AdaptiveCpp/AdaptiveCpp", "492", "1.0", "3.0", "6.0", "0.0629370629370629", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "83", "-6", "0", "False", "0", "0", "0", "394", "10", "401", "0.6931471805599453", "5.978885764901122", "2.3978952727983707", "5.996452088619021", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["32", "AdaptiveCpp/AdaptiveCpp", "493", "1.0", "3.0", "6.0", "0.0314685314685314", "0.9166666666666666", "0.6931471805599448", "0.8333855399562498", "0.6537094706869915", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "84", "-5", "0", "False", "0", "0", "0", "396", "11", "408", "0.6931471805599453", "5.98393628068719", "2.4849066497880004", "6.013715156042801", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["33", "AdaptiveCpp/AdaptiveCpp", "494", "1.0", "3.0", "6.0", "0.0384615384615384", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "85", "-4", "0", "False", "0", "0", "0", "400", "11", "415", "0.6931471805599453", "5.993961427306569", "2.4849066497880004", "6.030685260261263", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["34", "AdaptiveCpp/AdaptiveCpp", "495", "4.0", "3.0", "6.0", "0.1608391608391608", "1.1666666666666667", "0.9162907318741546", "0.889235659522009", "0.7444078107515406", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "86", "-3", "0", "False", "0", "0", "0", "411", "12", "422", "1.6094379124341005", "6.021023349349526", "2.5649493574615367", "6.0473721790462776", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["35", "AdaptiveCpp/AdaptiveCpp", "496", "4.0", "3.0", "6.0", "0.3076923076923077", "1.3333333333333333", "0.5108256237659902", "0.8634398378798575", "0.6639843473284657", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "87", "-2", "0", "False", "0", "0", "0", "419", "13", "429", "1.6094379124341005", "6.040254711277414", "2.6390573296152584", "6.063785208687608", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["36", "AdaptiveCpp/AdaptiveCpp", "497", "2.0", "3.0", "6.0", "0.2727272727272727", "1.5", "1.0986122886681091", "0.9307722154980688", "0.8386095222035911", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "88", "-1", "0", "False", "0", "0", "0", "423", "13", "436", "1.0986122886681096", "6.049733455231958", "2.6390573296152584", "6.07993319509559", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["37", "AdaptiveCpp/AdaptiveCpp", "498", "2.0", "3.0", "6.0", "0.2237762237762237", "1.6666666666666667", "1.0986122886681091", "0.9407704701088077", "0.861883250290392", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "89", "0", "0", "False", "0", "0", "0", "427", "13", "443", "1.0986122886681096", "6.059123195581797", "2.6390573296152584", "6.095824562432225", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["38", "AdaptiveCpp/AdaptiveCpp", "499", "0.0", "3.0", "6.0", "0.1293706293706293", "1.5833333333333333", "-0.693147180559946", "0.7089285864699496", "0.2502117946664587", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "90", "1", "0", "True", "0", "1", "0", "430", "13", "450", "0.0", "6.066108090103747", "2.6390573296152584", "6.111467339502678", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["39", "AdaptiveCpp/AdaptiveCpp", "500", "0.0", "3.0", "6.0", "-0.0034965034965034", "1.5833333333333333", "-6.661338147750939e-16", "0.829676081356154", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "91", "2", "0", "True", "0", "1", "0", "431", "13", "457", "0.0", "6.06842558824411", "2.6390573296152584", "6.1268691841141845", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["40", "AdaptiveCpp/AdaptiveCpp", "501", "3.0", "3.0", "6.0", "-0.0209790209790209", "1.8333333333333333", "1.38629436111989", "0.9615662577192609", "0.927003081517424", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "92", "3", "0", "True", "0", "1", "0", "433", "13", "464", "1.3862943611198906", "6.073044534100404", "2.6390573296152584", "6.142037405587356", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["41", "AdaptiveCpp/AdaptiveCpp", "502", "3.0", "3.0", "6.0", "0.0314685314685314", "1.9166666666666667", "0.2876820724517803", "0.9006393499233735", "0.634458169632737", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "93", "4", "0", "True", "0", "1", "0", "437", "13", "471", "1.3862943611198906", "6.0822189103764455", "2.6390573296152584", "6.156978985585555", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["42", "AdaptiveCpp/AdaptiveCpp", "503", "1.0", "3.0", "6.0", "0.0", "1.8333333333333333", "-0.4054651081081649", "0.8065689432388163", "0.3222714364606489", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "94", "5", "0", "True", "0", "1", "0", "437", "13", "478", "0.6931471805599453", "6.0822189103764455", "2.6390573296152584", "6.171700597410915", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["43", "AdaptiveCpp/AdaptiveCpp", "504", "0.0", "3.0", "6.0", "-0.1083916083916083", "1.75", "-0.6931471805599458", "0.7420886557878206", "0.2291693294654893", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "95", "6", "0", "True", "0", "1", "0", "438", "13", "485", "0.0", "6.084499413075172", "2.6390573296152584", "6.186208623900494", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["44", "AdaptiveCpp/AdaptiveCpp", "505", "0.0", "3.0", "6.0", "-0.2097902097902098", "1.6666666666666667", "-0.6931471805599457", "0.7258204499338335", "0.2395323119764419", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "96", "7", "0", "True", "0", "1", "0", "438", "13", "492", "0.0", "6.084499413075172", "2.6390573296152584", "6.20050917404269", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["45", "AdaptiveCpp/AdaptiveCpp", "506", "0.0", "3.0", "6.0", "-0.3041958041958042", "1.5833333333333333", "-0.6931471805599457", "0.7089285864699497", "0.2502117946664588", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "97", "8", "0", "True", "0", "1", "0", "439", "14", "499", "0.0", "6.0867747269123065", "2.70805020110221", "6.214608098422191", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["46", "AdaptiveCpp/AdaptiveCpp", "507", "2.0", "3.0", "6.0", "-0.1783216783216783", "1.4166666666666667", "-0.5108256237659912", "0.7121483581398508", "0.3265854170022765", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "98", "9", "0", "True", "0", "1", "0", "442", "15", "506", "1.0986122886681096", "6.093569770045136", "2.772588722239781", "6.228511003591183", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["47", "AdaptiveCpp/AdaptiveCpp", "508", "0.0", "3.0", "6.0", "-0.1153846153846153", "1.0833333333333333", "-1.609437912434101", "0.371425890455925", "0.1488615992206787", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "99", "10", "0", "True", "0", "1", "0", "443", "16", "513", "0.0", "6.095824562432225", "2.833213344056216", "6.242223265455165", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["48", "AdaptiveCpp/AdaptiveCpp", "509", "1.0", "3.0", "6.0", "-0.0769230769230769", "1.0", "-0.4054651081081649", "0.6444049826448044", "0.3999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "100", "11", "0", "True", "0", "1", "0", "444", "16", "520", "0.6931471805599453", "6.09807428216624", "2.833213344056216", "6.255750041753367", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["49", "AdaptiveCpp/AdaptiveCpp", "510", "2.0", "3.0", "6.0", "0.0069930069930069", "1.0", "-5.551115123125785e-16", "0.7310585786300048", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "101", "12", "0", "True", "0", "1", "0", "446", "16", "527", "1.0986122886681096", "6.102558594613568", "2.833213344056216", "6.269096283706261", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"]], "shape": {"columns": 42, "rows": 203912}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "      <th>outlier</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>486</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.069930</td>\n", "      <td>0.666667</td>\n", "      <td>-6.931472e-01</td>\n", "      <td>0.493380</td>\n", "      <td>0.386488</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.034965</td>\n", "      <td>0.500000</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.354661</td>\n", "      <td>0.366025</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>488</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.122378</td>\n", "      <td>0.750000</td>\n", "      <td>9.162907e-01</td>\n", "      <td>0.841081</td>\n", "      <td>0.665348</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>489</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.097902</td>\n", "      <td>0.833333</td>\n", "      <td>6.931472e-01</td>\n", "      <td>0.821491</td>\n", "      <td>0.640520</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>490</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.143357</td>\n", "      <td>1.083333</td>\n", "      <td>1.386294e+00</td>\n", "      <td>0.921985</td>\n", "      <td>0.817846</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203907</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.388112</td>\n", "      <td>3.416667</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.910361</td>\n", "      <td>0.022897</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203908</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.038462</td>\n", "      <td>2.416667</td>\n", "      <td>-1.609438e+00</td>\n", "      <td>0.691519</td>\n", "      <td>0.020046</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203909</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.402098</td>\n", "      <td>2.916667</td>\n", "      <td>7.884574e-01</td>\n", "      <td>0.975993</td>\n", "      <td>0.908849</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203910</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.325175</td>\n", "      <td>2.916667</td>\n", "      <td>-6.661338e-16</td>\n", "      <td>0.948664</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203911</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>563</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.234266</td>\n", "      <td>3.250000</td>\n", "      <td>1.609438e+00</td>\n", "      <td>0.992305</td>\n", "      <td>0.994679</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203912 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                         repo_name  standardized_time_weeks  pr_throughput  \\\n", "0       10up/autoshare-for-twitter                      486            0.0   \n", "1       10up/autoshare-for-twitter                      487            0.0   \n", "2       10up/autoshare-for-twitter                      488            4.0   \n", "3       10up/autoshare-for-twitter                      489            1.0   \n", "4       10up/autoshare-for-twitter                      490            3.0   \n", "...                            ...                      ...            ...   \n", "203907     tree-sitter/tree-sitter                      559            1.0   \n", "203908     tree-sitter/tree-sitter                      560            2.0   \n", "203909     tree-sitter/tree-sitter                      561           10.0   \n", "203910     tree-sitter/tree-sitter                      562            2.0   \n", "203911     tree-sitter/tree-sitter                      563            4.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "0                       1.0                 2.0      -0.069930      0.666667   \n", "1                       1.0                 2.0      -0.034965      0.500000   \n", "2                       1.0                 2.0       0.122378      0.750000   \n", "3                       1.0                 2.0       0.097902      0.833333   \n", "4                       1.0                 2.0       0.143357      1.083333   \n", "...                     ...                 ...            ...           ...   \n", "203907                  1.0                13.0      -0.388112      3.416667   \n", "203908                  1.0                13.0       0.038462      2.416667   \n", "203909                  1.0                13.0       0.402098      2.916667   \n", "203910                  1.0                13.0       0.325175      2.916667   \n", "203911                  1.0                13.0       0.234266      3.250000   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "0                -6.931472e-01            0.493380                 0.386488   \n", "1                -1.098612e+00            0.354661                 0.366025   \n", "2                 9.162907e-01            0.841081                 0.665348   \n", "3                 6.931472e-01            0.821491                 0.640520   \n", "4                 1.386294e+00            0.921985                 0.817846   \n", "...                        ...                 ...                      ...   \n", "203907           -1.098612e+00            0.910361                 0.022897   \n", "203908           -1.609438e+00            0.691519                 0.020046   \n", "203909            7.884574e-01            0.975993                 0.908849   \n", "203910           -6.661338e-16            0.948664                 0.500000   \n", "203911            1.609438e+00            0.992305                 0.994679   \n", "\n", "        ...  time_cohort_effect  repo_cohort_effect  outlier  log_tenure  \\\n", "0       ...                 0_0                 1_0        0    5.552960   \n", "1       ...                 0_0                 1_0        0    5.552960   \n", "2       ...                 0_0                 1_0        0    5.552960   \n", "3       ...                 0_0                 1_0        0    5.552960   \n", "4       ...                 0_0                 1_0        0    5.552960   \n", "...     ...                 ...                 ...      ...         ...   \n", "203907  ...              1_4458              0_4458        0    5.955837   \n", "203908  ...              1_4458              0_4458        0    5.955837   \n", "203909  ...              1_4458              0_4458        0    5.955837   \n", "203910  ...              1_4458              0_4458        0    5.955837   \n", "203911  ...              1_4458              0_4458        0    5.955837   \n", "\n", "        log_commit_percent  log_commits log_project_commits_before_treatment  \\\n", "0                 0.196540     5.209486                             5.820083   \n", "1                 0.196540     5.209486                             5.820083   \n", "2                 0.196540     5.209486                             5.820083   \n", "3                 0.196540     5.209486                             5.820083   \n", "4                 0.196540     5.209486                             5.820083   \n", "...                    ...          ...                                  ...   \n", "203907            0.098761     5.153292                             7.368340   \n", "203908            0.098761     5.153292                             7.368340   \n", "203909            0.098761     5.153292                             7.368340   \n", "203910            0.098761     5.153292                             7.368340   \n", "203911            0.098761     5.153292                             7.368340   \n", "\n", "        log_project_contributors_before_treatment  \\\n", "0                                        2.197225   \n", "1                                        2.197225   \n", "2                                        2.197225   \n", "3                                        2.197225   \n", "4                                        2.197225   \n", "...                                           ...   \n", "203907                                   3.784190   \n", "203908                                   3.784190   \n", "203909                                   3.784190   \n", "203910                                   3.784190   \n", "203911                                   3.784190   \n", "\n", "        log_project_age_before_treatment  project_main_language  \n", "0                               6.056784                    PHP  \n", "1                               6.056784                    PHP  \n", "2                               6.056784                    PHP  \n", "3                               6.056784                    PHP  \n", "4                               6.056784                    PHP  \n", "...                                  ...                    ...  \n", "203907                          5.966147                     Go  \n", "203908                          5.966147                     Go  \n", "203909                          5.966147                     Go  \n", "203910                          5.966147                     Go  \n", "203911                          5.966147                     Go  \n", "\n", "[203912 rows x 42 columns]"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "compiled_data = pd.read_csv(\"../result/did_result_20250212/compiled_data_test_with_features.csv\")\n", "compiled_data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["repo_list = compiled_data['repo_name'].unique()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["productivity = pd.read_csv(\"../data/2025_0201_productivity.csv\")\n", "productivity[\"datetime\"] = pd.to_datetime(productivity[\"datetime\"])\n", "global_min_time = productivity[\"datetime\"].min()\n", "productivity[\"standardized_time_weeks\"] = (\n", "  (productivity[\"datetime\"] - global_min_time).dt.days // 7\n", ").astype(int)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'compiled_data' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# get the date period means of standardized_time_weeks\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m compiled_data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstandardized_time_weeks\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43mcompiled_data\u001b[49m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstandardized_time_weeks\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mint\u001b[39m)\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# print compiled data\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(compiled_data)\n", "\u001b[0;31mNameError\u001b[0m: name 'compiled_data' is not defined"]}], "source": ["# get the date period means of standardized_time_weeks\n", "compiled_data['standardized_time_weeks'] = compiled_data['standardized_time_weeks'].astype(int)\n", "# print compiled data\n", "print(compiled_data)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "import numpy as np \n", "\n", "client = MongoClient( \"mongodb://localhost:27017\" )\n", "db = client[\"disengagement\"]\n", "collection_pull_request = db[\"pull_requests_202501\"]\n", "collection_pr_comments = db[\"pr_comments_202501\"]\n", "\n", "def fetch_pull_requests(repo_name):\n", "    query = {\n", "        \"repo_name\": repo_name,\n", "    }\n", "    return list(collection_pull_request.find(query))\n", "  \n", "def fetch_pr_comments(repo_name):\n", "    query = {\n", "        \"repo_name\": repo_name,\n", "    }\n", "    comments = list(collection_pr_comments.find(query))\n", "\n", "    return comments\n", "  \n", "def process_pull_requests(pr_list):\n", "    if len(pr_list) == 0:\n", "        return pd.DataFrame(columns=['repo_name', 'number', 'created_at', 'merged_at', 'closed_at', 'user_login'])\n", "    df_pr = pd.DataFrame(pr_list)\n", "    date_cols = ['created_at', 'merged_at', 'closed_at']\n", "    for col in date_cols:\n", "        df_pr[col] = pd.to_datetime(df_pr.get(col, pd.NaT), errors='coerce')\n", "        # Remove timezone information\n", "        df_pr[col] = df_pr[col].dt.tz_localize(None)\n", "    df_pr['user_login'] = df_pr['user'].map(lambda x: x.get('login') if isinstance(x, dict) else None)\n", "    df_pr['pr_status'] = 'open'\n", "    df_pr.loc[df_pr['merged_at'].notnull(), 'pr_status'] = 'merged'\n", "    df_pr.loc[(df_pr['merged_at'].isnull()) & (df_pr['closed_at'].notnull()), 'pr_status'] = 'closed_without_merge'\n", "    return df_pr[['repo_name', 'number', 'created_at', 'merged_at', 'closed_at', 'user_login', 'pr_status']]\n", "\n", "def process_comments(comments_data):\n", "    if len(comments_data) == 0:\n", "        return pd.DataFrame(columns=['repo_name', 'pr_number', 'created_at', 'user_login'])\n", "    df_comments = pd.DataFrame(comments_data)\n", "    if 'created_at' not in df_comments.columns:\n", "        df_comments['created_at'] = pd.NaT\n", "    df_comments['created_at'] = pd.to_datetime(df_comments['created_at'], errors='coerce')\n", "    df_comments['created_at'] = df_comments['created_at'].dt.tz_localize(None)\n", "    df_comments['user_login'] = df_comments['user'].map(lambda x: x.get('login') if isinstance(x, dict) else None)\n", "    # Extract PR number from 'pull_request_url'\n", "    df_comments['pr_number'] = df_comments['pull_request_url'].fillna('').str.extract(r'/pull/(\\d+)$', expand=False)\n", "    df_comments['pr_number'] = df_comments['pr_number'].replace('', np.nan).astype('Int64')\n", "    return df_comments[['repo_name', 'pr_number', 'created_at', 'user_login']]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "number", "rawType": "int64", "type": "integer"}, {"name": "created_at", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "merged_at", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "closed_at", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "user_login", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "49ecb4d8-2b6e-4ad4-9e78-48e89d6cc25c", "rows": [["0", "10up/autoshare-for-twitter", "1", "2019-01-22 06:52:32", "2019-01-28 18:32:13", "2019-01-28 18:32:13", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["1", "10up/autoshare-for-twitter", "10", "2019-02-15 19:16:55", "2019-02-25 16:56:40", "2019-02-25 16:56:40", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["2", "10up/autoshare-for-twitter", "23", "2019-07-12 12:40:26", "2019-07-19 18:21:05", "2019-07-19 18:21:05", "johnwatkins0"], ["3", "10up/autoshare-for-twitter", "24", "2019-07-12 13:14:54", "2019-07-19 16:06:20", "2019-07-19 16:06:20", "johnwatkins0"], ["4", "10up/autoshare-for-twitter", "25", "2019-07-19 03:10:56", "2019-08-08 01:38:52", "2019-08-08 01:38:53", "johnwatkins0"], ["5", "10up/autoshare-for-twitter", "28", "2019-07-19 18:25:23", null, "2019-10-10 12:32:32", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["6", "10up/autoshare-for-twitter", "29", "2019-07-19 22:09:37", "2019-08-13 02:11:46", "2019-08-13 02:11:46", "johnwatkins0"], ["7", "10up/autoshare-for-twitter", "31", "2019-07-31 00:15:46", "2019-07-31 14:13:12", "2019-07-31 14:13:12", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["8", "10up/autoshare-for-twitter", "33", "2019-08-11 22:05:24", "2019-09-12 20:50:28", "2019-09-12 20:50:29", "johnwatkins0"], ["9", "10up/autoshare-for-twitter", "34", "2019-08-23 17:42:31", "2019-10-16 16:31:17", "2019-10-16 16:31:17", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["10", "10up/autoshare-for-twitter", "37", "2019-08-27 04:12:04", "2019-09-25 14:30:33", "2019-09-25 14:30:33", "johnwatkins0"], ["11", "10up/autoshare-for-twitter", "38", "2019-08-27 10:09:55", null, "2019-09-13 21:14:53", "dependabot[bot]"], ["12", "10up/autoshare-for-twitter", "41", "2019-09-13 21:12:58", "2019-09-13 21:13:43", "2019-09-13 21:13:43", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["13", "10up/autoshare-for-twitter", "42", "2019-09-16 12:46:38", "2019-09-27 15:40:41", "2019-09-27 15:40:42", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["14", "10up/autoshare-for-twitter", "44", "2019-09-17 23:17:31", "2019-09-27 18:48:41", "2019-09-27 18:48:42", "johnwatkins0"], ["15", "10up/autoshare-for-twitter", "45", "2019-10-10 02:40:26", "2019-10-11 17:30:45", "2019-10-11 17:30:46", "johnwatkins0"], ["16", "10up/autoshare-for-twitter", "47", "2019-10-16 16:49:23", "2019-10-16 16:51:14", "2019-10-16 16:51:14", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["17", "10up/autoshare-for-twitter", "48", "2019-10-17 17:13:50", "2019-12-12 02:40:08", "2019-12-12 02:40:08", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["18", "10up/autoshare-for-twitter", "49", "2019-10-17 17:26:39", "2019-11-15 02:42:33", "2019-11-15 02:42:33", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["19", "10up/autoshare-for-twitter", "50", "2019-10-21 15:49:10", "2019-10-21 16:49:43", "2019-10-21 16:49:43", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["20", "10up/autoshare-for-twitter", "52", "2019-11-14 21:38:26", "2019-11-15 17:17:54", "2019-11-15 17:17:55", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["21", "10up/autoshare-for-twitter", "54", "2019-12-11 22:32:54", "2019-12-12 02:47:45", "2019-12-12 02:47:45", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["22", "10up/autoshare-for-twitter", "58", "2019-12-17 04:22:16", "2019-12-18 03:28:55", "2019-12-18 03:28:55", "johnwatkins0"], ["23", "10up/autoshare-for-twitter", "61", "2020-01-16 15:13:39", "2020-01-17 03:33:25", "2020-01-17 03:33:25", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["24", "10up/autoshare-for-twitter", "62", "2020-01-16 15:38:10", "2020-01-17 03:33:53", "2020-01-17 03:33:53", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["25", "10up/autoshare-for-twitter", "63", "2020-01-16 16:25:05", "2020-01-17 15:01:54", "2020-01-17 15:01:54", "johnwatkins0"], ["26", "10up/autoshare-for-twitter", "64", "2020-01-16 16:57:20", null, "2020-01-16 16:59:17", "johnwatkins0"], ["27", "10up/autoshare-for-twitter", "65", "2020-01-17 16:37:50", "2020-01-17 16:49:12", "2020-01-17 16:49:12", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["28", "10up/autoshare-for-twitter", "67", "2020-01-23 15:43:40", "2020-01-24 16:56:47", "2020-01-24 16:56:47", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["29", "10up/autoshare-for-twitter", "70", "2020-01-28 23:34:49", "2020-01-29 16:20:50", "2020-01-29 16:20:50", "johnwatkins0"], ["30", "10up/autoshare-for-twitter", "71", "2020-01-29 00:16:19", "2020-01-30 02:21:26", "2020-01-30 02:21:26", "johnwatkins0"], ["31", "10up/autoshare-for-twitter", "73", "2020-01-30 23:16:39", "2020-02-03 14:33:45", "2020-02-03 14:33:45", "johnwatkins0"], ["32", "10up/autoshare-for-twitter", "74", "2020-01-30 23:28:04", "2020-01-31 02:53:29", "2020-01-31 02:53:29", "johnwatkins0"], ["33", "10up/autoshare-for-twitter", "76", "2020-02-03 18:39:56", "2020-02-03 21:38:52", "2020-02-03 21:38:53", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["34", "10up/autoshare-for-twitter", "77", "2020-02-03 22:13:47", "2020-02-03 22:25:59", "2020-02-03 22:25:59", "johnwatkins0"], ["35", "10up/autoshare-for-twitter", "78", "2020-02-04 14:10:49", "2020-03-06 21:04:36", "2020-03-06 21:04:36", "dinhtungdu"], ["36", "10up/autoshare-for-twitter", "82", "2020-02-18 16:12:48", "2020-02-24 20:26:26", "2020-02-24 20:26:26", "johnwatkins0"], ["37", "10up/autoshare-for-twitter", "83", "2020-03-05 00:19:21", null, "2020-03-08 18:28:53", "johnwatkins0"], ["38", "10up/autoshare-for-twitter", "84", "2020-03-08 18:28:32", "2020-03-18 02:09:22", "2020-03-18 02:09:22", "johnwatkins0"], ["39", "10up/autoshare-for-twitter", "86", "2020-03-08 19:36:15", "2020-03-20 02:34:29", "2020-03-20 02:34:29", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], ["40", "10up/autoshare-for-twitter", "89", "2020-03-13 17:37:16", null, "2020-03-16 20:53:40", "helen"], ["41", "10up/autoshare-for-twitter", "90", "2020-03-14 01:18:25", "2020-03-17 03:13:14", "2020-03-17 03:13:14", "dependabot[bot]"], ["42", "10up/autoshare-for-twitter", "93", "2020-03-20 02:38:48", "2020-03-20 02:50:46", "2020-03-20 02:50:46", "johnwatkins0"], ["43", "10up/autoshare-for-twitter", "96", "2020-03-20 04:48:41", "2020-03-20 13:59:10", "2020-03-20 13:59:11", "johnwatkins0"], ["44", "10up/autoshare-for-twitter", "97", "2020-03-24 01:26:26", "2020-03-24 01:37:42", "2020-03-24 01:37:42", "johnwatkins0"], ["45", "10up/autoshare-for-twitter", "98", "2020-03-24 22:00:22", "2020-03-25 19:08:18", "2020-03-25 19:08:18", "johnwatkins0"], ["46", "10up/autoshare-for-twitter", "99", "2020-03-27 22:23:42", "2020-03-30 03:55:05", "2020-03-30 03:55:05", "rickalee"], ["47", "10up/autoshare-for-twitter", "101", "2020-04-08 14:46:56", "2020-04-09 03:16:03", "2020-04-09 03:16:03", "dinhtungdu"], ["48", "10up/autoshare-for-twitter", "103", "2020-06-26 11:53:04", "2020-07-01 03:11:57", "2020-07-01 03:11:58", "dinhtungdu"], ["49", "10up/autoshare-for-twitter", "105", "2020-06-30 19:20:06", "2020-07-02 01:27:10", "2020-07-02 01:27:10", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]], "shape": {"columns": 6, "rows": 211}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>number</th>\n", "      <th>created_at</th>\n", "      <th>merged_at</th>\n", "      <th>closed_at</th>\n", "      <th>user_login</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>1</td>\n", "      <td>2019-01-22 06:52:32</td>\n", "      <td>2019-01-28 18:32:13</td>\n", "      <td>2019-01-28 18:32:13</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>10</td>\n", "      <td>2019-02-15 19:16:55</td>\n", "      <td>2019-02-25 16:56:40</td>\n", "      <td>2019-02-25 16:56:40</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>23</td>\n", "      <td>2019-07-12 12:40:26</td>\n", "      <td>2019-07-19 18:21:05</td>\n", "      <td>2019-07-19 18:21:05</td>\n", "      <td>john<PERSON>kins0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>24</td>\n", "      <td>2019-07-12 13:14:54</td>\n", "      <td>2019-07-19 16:06:20</td>\n", "      <td>2019-07-19 16:06:20</td>\n", "      <td>john<PERSON>kins0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>25</td>\n", "      <td>2019-07-19 03:10:56</td>\n", "      <td>2019-08-08 01:38:52</td>\n", "      <td>2019-08-08 01:38:53</td>\n", "      <td>john<PERSON>kins0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>348</td>\n", "      <td>2024-10-27 06:46:22</td>\n", "      <td>2024-10-27 17:26:28</td>\n", "      <td>2024-10-27 17:26:28</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>349</td>\n", "      <td>2024-11-06 18:00:21</td>\n", "      <td>NaT</td>\n", "      <td>2024-11-06 18:00:24</td>\n", "      <td>s3rgiosan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>352</td>\n", "      <td>2024-11-28 17:10:38</td>\n", "      <td>2024-12-02 15:34:28</td>\n", "      <td>2024-12-02 15:34:28</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>353</td>\n", "      <td>2024-12-16 01:15:04</td>\n", "      <td>2024-12-16 05:45:16</td>\n", "      <td>2024-12-16 05:45:17</td>\n", "      <td>s3rgiosan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>354</td>\n", "      <td>2024-12-16 13:24:05</td>\n", "      <td>2024-12-18 06:31:28</td>\n", "      <td>2024-12-18 06:31:28</td>\n", "      <td>s3rgiosan</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>211 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                      repo_name  number          created_at  \\\n", "0    10up/autoshare-for-twitter       1 2019-01-22 06:52:32   \n", "1    10up/autoshare-for-twitter      10 2019-02-15 19:16:55   \n", "2    10up/autoshare-for-twitter      23 2019-07-12 12:40:26   \n", "3    10up/autoshare-for-twitter      24 2019-07-12 13:14:54   \n", "4    10up/autoshare-for-twitter      25 2019-07-19 03:10:56   \n", "..                          ...     ...                 ...   \n", "206  10up/autoshare-for-twitter     348 2024-10-27 06:46:22   \n", "207  10up/autoshare-for-twitter     349 2024-11-06 18:00:21   \n", "208  10up/autoshare-for-twitter     352 2024-11-28 17:10:38   \n", "209  10up/autoshare-for-twitter     353 2024-12-16 01:15:04   \n", "210  10up/autoshare-for-twitter     354 2024-12-16 13:24:05   \n", "\n", "              merged_at           closed_at       user_login  \n", "0   2019-01-28 18:32:13 2019-01-28 18:32:13  <PERSON><PERSON><PERSON><PERSON><PERSON>  \n", "1   2019-02-25 16:56:40 2019-02-25 16:56:40  <PERSON><PERSON><PERSON><PERSON><PERSON>  \n", "2   2019-07-19 18:21:05 2019-07-19 18:21:05     john<PERSON>kins0  \n", "3   2019-07-19 16:06:20 2019-07-19 16:06:20     john<PERSON><PERSON>0  \n", "4   2019-08-08 01:38:52 2019-08-08 01:38:53     john<PERSON><PERSON>0  \n", "..                  ...                 ...              ...  \n", "206 2024-10-27 17:26:28 2024-10-27 17:26:28      <PERSON><PERSON><PERSON><PERSON><PERSON>  \n", "207                 NaT 2024-11-06 18:00:24        s3rgiosan  \n", "208 2024-12-02 15:34:28 2024-12-02 15:34:28      <PERSON><PERSON><PERSON><PERSON><PERSON>  \n", "209 2024-12-16 05:45:16 2024-12-16 05:45:17        s3rg<PERSON>an  \n", "210 2024-12-18 06:31:28 2024-12-18 06:31:28        s3rg<PERSON>an  \n", "\n", "[211 rows x 6 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_name_test = repo_list[0]\n", "pr_list = fetch_pull_requests(repo_name_test)\n", "comments_data = fetch_pr_comments(repo_name_test)\n", "df_pr = process_pull_requests(pr_list)\n", "df_comments = process_comments(comments_data)\n", "\n", "df_pr"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["df_pr"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["def calculate_pr_success_rate(df_pr):\n", "    if df_pr.empty:\n", "        return None\n", "    total_prs = len(df_pr)\n", "    merged_prs = len(df_pr.dropna(subset=['merged_at']))\n", "    if merged_prs == 0 and total_prs == 0:\n", "        return None\n", "    success_rate = merged_prs / total_prs\n", "    return success_rate\n", "\n", "\n", "def calculate_time_to_merge(df_pr):\n", "    if df_pr.empty:\n", "        return None\n", "    df_pr = df_pr.dropna(subset=['created_at', 'merged_at'])\n", "    if df_pr.empty:\n", "        return None\n", "    df_pr['time_to_merge'] = (df_pr['merged_at'] - df_pr['created_at']).dt.total_seconds() / 3600\n", "    return df_pr['time_to_merge'].mean()\n", "\n", "def calculate_time_to_first_comment(df_pr, df_comments):\n", "    if df_pr.empty:\n", "        return None\n", "    if df_comments.empty:\n", "        return None\n", "    df_comments = df_comments.dropna(subset=['created_at'])\n", "    if df_comments.empty:\n", "        return None\n", "    df_comments = df_comments.sort_values('created_at')\n", "    df_pr = df_pr.dropna(subset=['created_at'])\n", "    if df_pr.empty:\n", "        return None\n", "    first_review_times = []\n", "    for idx, row in df_pr.iterrows():\n", "        pr_number = row['number']\n", "        pr_comments = df_comments[df_comments['pr_number'] == pr_number]\n", "        if pr_comments.empty:\n", "            continue\n", "        first_review_time = (pr_comments['created_at'].min() - row['created_at']).total_seconds() / 3600\n", "        first_review_times.append(first_review_time)\n", "    if not first_review_times:\n", "        return None\n", "    return np.mean(first_review_times)"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Exception in thread Thread-351:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/anaconda3/envs/disengagement/lib/python3.11/threading.py\", line 1045, in _bootstrap_inner\n", "    self.run()\n", "  File \"/home/<USER>/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/process.py\", line 347, in run\n", "    self.terminate_broken(cause)\n", "  File \"/home/<USER>/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/process.py\", line 499, in terminate_broken\n", "    work_item.future.set_exception(bpe)\n", "  File \"/home/<USER>/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/_base.py\", line 559, in set_exception\n", "    raise InvalidStateError('{}: {!r}'.format(self._state, self))\n", "concurrent.futures._base.InvalidStateError: CANCELLED: <Future at 0x7ff2d48b8050 state=cancelled>\n"]}, {"ename": "BrokenProcessPool", "evalue": "A process in the process pool was terminated abruptly while the future was running or pending.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mBrokenProcessPool\u001b[0m                         Traceback (most recent call last)", "Cell \u001b[0;32mIn[59], line 81\u001b[0m\n\u001b[1;32m     79\u001b[0m \u001b[38;5;66;03m# 使用多进程并行处理\u001b[39;00m\n\u001b[1;32m     80\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m ProcessPoolExecutor() \u001b[38;5;28;01mas\u001b[39;00m executor:\n\u001b[0;32m---> 81\u001b[0m     results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(executor\u001b[38;5;241m.\u001b[39mmap(process_repo, repo_list))\n\u001b[1;32m     83\u001b[0m \u001b[38;5;66;03m# 合并最终结果\u001b[39;00m\n\u001b[1;32m     84\u001b[0m add_outcome_values \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mconcat(results, ignore_index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/process.py:620\u001b[0m, in \u001b[0;36m_chain_from_iterable_of_lists\u001b[0;34m(iterable)\u001b[0m\n\u001b[1;32m    614\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_chain_from_iterable_of_lists\u001b[39m(iterable):\n\u001b[1;32m    615\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    616\u001b[0m \u001b[38;5;124;03m    Specialized implementation of itertools.chain.from_iterable.\u001b[39;00m\n\u001b[1;32m    617\u001b[0m \u001b[38;5;124;03m    Each item in *iterable* should be a list.  This function is\u001b[39;00m\n\u001b[1;32m    618\u001b[0m \u001b[38;5;124;03m    careful not to keep references to yielded objects.\u001b[39;00m\n\u001b[1;32m    619\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 620\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m element \u001b[38;5;129;01min\u001b[39;00m iterable:\n\u001b[1;32m    621\u001b[0m         element\u001b[38;5;241m.\u001b[39mreverse()\n\u001b[1;32m    622\u001b[0m         \u001b[38;5;28;01mwhile\u001b[39;00m element:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/_base.py:619\u001b[0m, in \u001b[0;36mExecutor.map.<locals>.result_iterator\u001b[0;34m()\u001b[0m\n\u001b[1;32m    616\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m fs:\n\u001b[1;32m    617\u001b[0m     \u001b[38;5;66;03m# Careful not to keep a reference to the popped future\u001b[39;00m\n\u001b[1;32m    618\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 619\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m _result_or_cancel(fs\u001b[38;5;241m.\u001b[39mpop())\n\u001b[1;32m    620\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    621\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m _result_or_cancel(fs\u001b[38;5;241m.\u001b[39mpop(), end_time \u001b[38;5;241m-\u001b[39m time\u001b[38;5;241m.\u001b[39mmonotonic())\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/_base.py:317\u001b[0m, in \u001b[0;36m_result_or_cancel\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    315\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    316\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 317\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m fut\u001b[38;5;241m.\u001b[39mresult(timeout)\n\u001b[1;32m    318\u001b[0m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    319\u001b[0m         fut\u001b[38;5;241m.\u001b[39mcancel()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/_base.py:456\u001b[0m, in \u001b[0;36mFuture.result\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    454\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m CancelledError()\n\u001b[1;32m    455\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state \u001b[38;5;241m==\u001b[39m FINISHED:\n\u001b[0;32m--> 456\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__get_result()\n\u001b[1;32m    457\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    458\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTimeoutError\u001b[39;00m()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/concurrent/futures/_base.py:401\u001b[0m, in \u001b[0;36mFuture.__get_result\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    399\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception:\n\u001b[1;32m    400\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 401\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception\n\u001b[1;32m    402\u001b[0m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m    403\u001b[0m         \u001b[38;5;66;03m# Break a reference cycle with the exception in self._exception\u001b[39;00m\n\u001b[1;32m    404\u001b[0m         \u001b[38;5;28mself\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mBrokenProcessPool\u001b[0m: A process in the process pool was terminated abruptly while the future was running or pending."]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from concurrent.futures import ProcessPoolExecutor\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "def process_repo(repo_name):\n", "    # 获取数据\n", "    pr_list = fetch_pull_requests(repo_name)\n", "    pr_comments = fetch_pr_comments(repo_name)\n", "    \n", "    # 处理PR数据\n", "    pr_data = process_pull_requests(pr_list)\n", "    pr_data['standardized_time_weeks'] = (pr_data['created_at'] - global_min_time).dt.days // 7\n", "    \n", "    # 处理评论数据\n", "    pr_comments_data = process_comments(pr_comments)\n", "    if not pr_comments_data.empty:\n", "        pr_comments_data['standardized_time_weeks'] = (pr_comments_data['created_at'] - global_min_time).dt.days // 7\n", "    \n", "    # 获取时间范围\n", "    repo_start_time = compiled_data[compiled_data['repo_name'] == repo_name]['standardized_time_weeks'].min()\n", "    repo_end_time = compiled_data[compiled_data['repo_name'] == repo_name]['standardized_time_weeks'].max()\n", "    \n", "    # 预计算累积指标\n", "    time_range = np.arange(repo_start_time, repo_end_time + 1)\n", "    \n", "    # 计算各周指标\n", "    weekly_stats = []\n", "    for t in time_range:\n", "        current_prs = pr_data[pr_data['standardized_time_weeks'] == t]\n", "        \n", "        # 实时指标\n", "        pr_success_rate = calculate_pr_success_rate(current_prs)\n", "        time_to_merge = calculate_time_to_merge(current_prs)\n", "        time_to_first_comment = calculate_time_to_first_comment(current_prs, pr_comments_data)\n", "        \n", "        weekly_stats.append({\n", "            'repo_name': repo_name,\n", "            'standardized_time_weeks': t,\n", "            'pr_success_rate': pr_success_rate,\n", "            'time_to_merge': time_to_merge,\n", "            'time_to_first_comment': time_to_first_comment\n", "        })\n", "    \n", "    # 转换为DataFrame\n", "    weekly_df = pd.DataFrame(weekly_stats)\n", "    \n", "    # 预计算累积指标\n", "    time_bins = np.arange(repo_start_time, repo_end_time + 1)\n", "    \n", "    # 累计PR数量\n", "    pr_counts = pr_data.groupby('standardized_time_weeks').size().reindex(time_bins, fill_value=0).cumsum()\n", "    merged_counts = pr_data[pr_data['pr_status'] == 'merged'].groupby('standardized_time_weeks').size().reindex(time_bins, fill_value=0).cumsum()\n", "    closed_counts = pr_data[pr_data['pr_status'] != 'open'].groupby('standardized_time_weeks').size().reindex(time_bins, fill_value=0).cumsum()\n", "    open_counts = pr_data[pr_data['pr_status'] == 'open'].groupby('standardized_time_weeks').size().reindex(time_bins, fill_value=0).cumsum()\n", "    \n", "    # 累计评论数量\n", "    if not pr_comments_data.empty:\n", "        comment_counts = pr_comments_data.groupby('standardized_time_weeks').size().reindex(time_bins, fill_value=0).cumsum()\n", "    else:\n", "        comment_counts = pd.Series(0, index=time_bins)\n", "    \n", "    # 合并累积指标\n", "    weekly_df = weekly_df.merge(\n", "        pd.DataFrame({\n", "            'number_of_prs': pr_counts.values,\n", "            'number_of_merged_prs': merged_counts.values,\n", "            'number_of_closed_prs': closed_counts.values,\n", "            'number_of_open_prs': open_counts.values,\n", "            'number_of_pr_comments': comment_counts.values\n", "        }, index=time_bins),\n", "        left_on='standardized_time_weeks',\n", "        right_index=True\n", "    )\n", "    \n", "    return weekly_df\n", "\n", "# 使用多进程并行处理\n", "with ProcessPoolExecutor() as executor:\n", "    results = list(executor.map(process_repo, repo_list))\n", "\n", "# 合并最终结果\n", "add_outcome_values = pd.concat(results, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["add_outcome_values = pd.DataFrame(columns=['repo_name', 'standardized_time_weeks', 'pr_success_rate', 'time_to_merge', 'time_to_first_comment', 'number_of_prs', 'number_of_merged_prs', 'number_of_closed_prs', 'number_of_open_prs', 'number_of_pr_comments'])\n", "# ignore warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "for repo_name in repo_list:\n", "    pr_list = fetch_pull_requests(repo_name)\n", "    pr_comments = fetch_pr_comments(repo_name)\n", "    pr_data = process_pull_requests(pr_list)\n", "    repo_start_time = compiled_data[compiled_data['repo_name'] == repo_name]['standardized_time_weeks'].min()\n", "    # change the time into standardized_time_weeks\n", "    repo_end_time = compiled_data[compiled_data['repo_name'] == repo_name]['standardized_time_weeks'].max()\n", "    pr_data['standardized_time_weeks'] = (pr_data['created_at'] - global_min_time).dt.days // 7\n", "    pr_data['standardized_time_weeks'] = pr_data['standardized_time_weeks'].astype(int)\n", "    pr_comments_data = process_comments(pr_comments)\n", "    if not pr_comments_data.empty:\n", "        pr_comments_data['standardized_time_weeks'] = (pr_comments_data['created_at'] - global_min_time).dt.days // 7\n", "        pr_comments_data['standardized_time_weeks'] = pr_comments_data['standardized_time_weeks'].astype(int)\n", "    outcome_values = pd.DataFrame(columns=['repo_name', 'standardized_time_weeks', 'pr_success_rate', 'time_to_merge', 'time_to_first_comment', 'number_of_prs', 'number_of_merged_prs', 'number_of_closed_prs', 'number_of_open_prs', 'number_of_pr_comments'])\n", "    for t in range(repo_start_time, repo_end_time):\n", "        pr_data_selected = pr_data[pr_data['standardized_time_weeks'] == t]\n", "        pr_success_rate = calculate_pr_success_rate(pr_data_selected)\n", "        time_to_merge = calculate_time_to_merge(pr_data_selected)\n", "        time_to_first_comment = calculate_time_to_first_comment(pr_data_selected, pr_comments_data)\n", "        number_of_prs = len(pr_data[pr_data['standardized_time_weeks'] <= t])\n", "        number_of_merged_prs = len(pr_data[(pr_data['standardized_time_weeks'] <= t) & (pr_data['pr_status'] == 'merged')])\n", "        number_of_closed_prs = len(pr_data[(pr_data['standardized_time_weeks'] <= t) & (pr_data['pr_status'] != 'open')])\n", "        number_of_open_prs = len(pr_data[(pr_data['standardized_time_weeks'] <= t) & (pr_data['pr_status'] == 'open')])\n", "        if pr_comments_data.empty:\n", "            number_of_pr_comments = 0\n", "        else:\n", "            number_of_pr_comments = len(pr_comments_data[pr_comments_data['standardized_time_weeks'] <= t])\n", "        outcome_values = pd.concat([outcome_values, pd.DataFrame([{\n", "            'repo_name': repo_name,\n", "            'standardized_time_weeks': t,\n", "            'pr_success_rate': pr_success_rate,\n", "            'time_to_merge': time_to_merge,\n", "            'time_to_first_comment': time_to_first_comment,\n", "            'number_of_prs': number_of_prs,\n", "            'number_of_merged_prs': number_of_merged_prs,\n", "            'number_of_closed_prs': number_of_closed_prs,\n", "            'number_of_open_prs': number_of_open_prs,\n", "            'number_of_pr_comments': number_of_pr_comments\n", "        }])], ignore_index=True)\n", "    # print(outcome_values)\n", "    add_outcome_values = pd.concat([add_outcome_values, outcome_values], ignore_index=True)\n", "add_outcome_values.to_csv(\"../result/did_result_20250212/add_outcome_values.csv\", index=False)\n", "        \n", "        "]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["重复的组合数量: 34692\n"]}], "source": ["print(\"重复的组合数量:\", compiled_data.duplicated(subset=['repo_name', 'standardized_time_weeks']).sum())\n"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "object", "type": "unknown"}, {"name": "pr_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "time_to_first_comment", "rawType": "object", "type": "unknown"}, {"name": "number_of_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_merged_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_closed_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_open_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_pr_comments", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "e27b3860-3dc8-4281-a8c9-c37026cd6bae", "rows": [["0", "10up/autoshare-for-twitter", "441", null, null, null, "2", "2", "2", "0", "0"], ["1", "10up/autoshare-for-twitter", "442", null, null, null, "2", "2", "2", "0", "0"], ["2", "10up/autoshare-for-twitter", "443", null, null, null, "2", "2", "2", "0", "0"], ["3", "10up/autoshare-for-twitter", "444", null, null, null, "2", "2", "2", "0", "0"], ["4", "10up/autoshare-for-twitter", "445", null, null, null, "2", "2", "2", "0", "0"], ["5", "10up/autoshare-for-twitter", "446", null, null, null, "2", "2", "2", "0", "0"], ["6", "10up/autoshare-for-twitter", "447", null, null, null, "2", "2", "2", "0", "0"], ["7", "10up/autoshare-for-twitter", "448", null, null, null, "2", "2", "2", "0", "0"], ["8", "10up/autoshare-for-twitter", "449", null, null, null, "2", "2", "2", "0", "0"], ["9", "10up/autoshare-for-twitter", "450", null, null, null, "2", "2", "2", "0", "0"], ["10", "10up/autoshare-for-twitter", "451", null, null, null, "2", "2", "2", "0", "0"], ["11", "10up/autoshare-for-twitter", "452", null, null, null, "2", "2", "2", "0", "0"], ["12", "10up/autoshare-for-twitter", "453", null, null, null, "2", "2", "2", "0", "0"], ["13", "10up/autoshare-for-twitter", "454", null, null, null, "2", "2", "2", "0", "0"], ["14", "10up/autoshare-for-twitter", "455", null, null, null, "2", "2", "2", "0", "0"], ["15", "10up/autoshare-for-twitter", "456", null, null, null, "2", "2", "2", "0", "0"], ["16", "10up/autoshare-for-twitter", "457", null, null, null, "2", "2", "2", "0", "0"], ["17", "10up/autoshare-for-twitter", "458", null, null, null, "2", "2", "2", "0", "0"], ["18", "10up/autoshare-for-twitter", "459", null, null, null, "2", "2", "2", "0", "0"], ["19", "10up/autoshare-for-twitter", "460", null, null, null, "2", "2", "2", "0", "0"], ["20", "10up/autoshare-for-twitter", "461", "1.0", "172.26736111111111", null, "4", "4", "4", "0", "0"], ["21", "10up/autoshare-for-twitter", "462", "0.6666666666666666", "529.2506944444444", null, "7", "6", "7", "0", "0"], ["22", "10up/autoshare-for-twitter", "463", null, null, null, "7", "6", "7", "0", "0"], ["23", "10up/autoshare-for-twitter", "464", "1.0", "13.957222222222223", null, "8", "7", "8", "0", "0"], ["24", "10up/autoshare-for-twitter", "465", "1.0", "766.7511111111111", null, "9", "8", "9", "0", "0"], ["25", "10up/autoshare-for-twitter", "466", null, null, null, "9", "8", "9", "0", "0"], ["26", "10up/autoshare-for-twitter", "467", "1.0", "1294.8127777777777", null, "10", "9", "10", "0", "0"], ["27", "10up/autoshare-for-twitter", "468", "0.5", "706.3080555555556", null, "12", "10", "12", "0", "0"], ["28", "10up/autoshare-for-twitter", "469", null, null, null, "12", "10", "12", "0", "0"], ["29", "10up/autoshare-for-twitter", "470", "1.0", "0.0125", null, "13", "11", "13", "0", "0"], ["30", "10up/autoshare-for-twitter", "471", "1.0", "251.21013888888888", null, "15", "13", "15", "0", "0"], ["31", "10up/autoshare-for-twitter", "472", null, null, null, "15", "13", "15", "0", "0"], ["32", "10up/autoshare-for-twitter", "473", null, null, null, "15", "13", "15", "0", "0"], ["33", "10up/autoshare-for-twitter", "474", "1.0", "38.83861111111111", null, "16", "14", "16", "0", "0"], ["34", "10up/autoshare-for-twitter", "475", "1.0", "670.2447222222222", null, "19", "17", "19", "0", "0"], ["35", "10up/autoshare-for-twitter", "476", "1.0", "1.0091666666666668", null, "20", "18", "20", "0", "0"], ["36", "10up/autoshare-for-twitter", "477", null, null, null, "20", "18", "20", "0", "0"], ["37", "10up/autoshare-for-twitter", "478", null, null, null, "20", "18", "20", "0", "0"], ["38", "10up/autoshare-for-twitter", "479", "1.0", "19.657777777777778", null, "21", "19", "21", "0", "0"], ["39", "10up/autoshare-for-twitter", "480", null, null, null, "21", "19", "21", "0", "0"], ["40", "10up/autoshare-for-twitter", "481", null, null, null, "21", "19", "21", "0", "0"], ["41", "10up/autoshare-for-twitter", "482", null, null, null, "21", "19", "21", "0", "0"], ["42", "10up/autoshare-for-twitter", "483", "1.0", "4.2475", null, "22", "20", "22", "0", "0"], ["43", "10up/autoshare-for-twitter", "484", "1.0", "23.110833333333332", null, "23", "21", "23", "0", "0"], ["44", "10up/autoshare-for-twitter", "485", null, null, null, "23", "21", "23", "0", "0"], ["45", "10up/autoshare-for-twitter", "486", null, null, null, "23", "21", "23", "0", "0"], ["46", "10up/autoshare-for-twitter", "487", null, null, null, "23", "21", "23", "0", "0"], ["47", "10up/autoshare-for-twitter", "488", "0.8", "11.76527777777778", null, "28", "25", "28", "0", "0"], ["48", "10up/autoshare-for-twitter", "489", "1.0", "25.218611111111112", null, "29", "26", "29", "0", "0"], ["49", "10up/autoshare-for-twitter", "490", "1.0", "33.390208333333334", null, "33", "30", "33", "0", "0"]], "shape": {"columns": 10, "rows": 354807}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>time_to_first_comment</th>\n", "      <th>number_of_prs</th>\n", "      <th>number_of_merged_prs</th>\n", "      <th>number_of_closed_prs</th>\n", "      <th>number_of_open_prs</th>\n", "      <th>number_of_pr_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>441</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>442</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>443</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>444</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>445</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354802</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>558</td>\n", "      <td>0.888889</td>\n", "      <td>226.978576</td>\n", "      <td>None</td>\n", "      <td>441</td>\n", "      <td>370</td>\n", "      <td>439</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354803</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.000000</td>\n", "      <td>116.207315</td>\n", "      <td>None</td>\n", "      <td>444</td>\n", "      <td>373</td>\n", "      <td>442</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354804</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>1.000000</td>\n", "      <td>171.439583</td>\n", "      <td>None</td>\n", "      <td>446</td>\n", "      <td>375</td>\n", "      <td>444</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354805</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>0.777778</td>\n", "      <td>16.118135</td>\n", "      <td>None</td>\n", "      <td>455</td>\n", "      <td>382</td>\n", "      <td>453</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354806</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>0.800000</td>\n", "      <td>361.275417</td>\n", "      <td>None</td>\n", "      <td>460</td>\n", "      <td>386</td>\n", "      <td>458</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>354807 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                         repo_name standardized_time_weeks  pr_success_rate  \\\n", "0       10up/autoshare-for-twitter                     441              NaN   \n", "1       10up/autoshare-for-twitter                     442              NaN   \n", "2       10up/autoshare-for-twitter                     443              NaN   \n", "3       10up/autoshare-for-twitter                     444              NaN   \n", "4       10up/autoshare-for-twitter                     445              NaN   \n", "...                            ...                     ...              ...   \n", "354802     tree-sitter/tree-sitter                     558         0.888889   \n", "354803     tree-sitter/tree-sitter                     559         1.000000   \n", "354804     tree-sitter/tree-sitter                     560         1.000000   \n", "354805     tree-sitter/tree-sitter                     561         0.777778   \n", "354806     tree-sitter/tree-sitter                     562         0.800000   \n", "\n", "        time_to_merge time_to_first_comment number_of_prs  \\\n", "0                 NaN                  None             2   \n", "1                 NaN                  None             2   \n", "2                 NaN                  None             2   \n", "3                 NaN                  None             2   \n", "4                 NaN                  None             2   \n", "...               ...                   ...           ...   \n", "354802     226.978576                  None           441   \n", "354803     116.207315                  None           444   \n", "354804     171.439583                  None           446   \n", "354805      16.118135                  None           455   \n", "354806     361.275417                  None           460   \n", "\n", "       number_of_merged_prs number_of_closed_prs number_of_open_prs  \\\n", "0                         2                    2                  0   \n", "1                         2                    2                  0   \n", "2                         2                    2                  0   \n", "3                         2                    2                  0   \n", "4                         2                    2                  0   \n", "...                     ...                  ...                ...   \n", "354802                  370                  439                  2   \n", "354803                  373                  442                  2   \n", "354804                  375                  444                  2   \n", "354805                  382                  453                  2   \n", "354806                  386                  458                  2   \n", "\n", "       number_of_pr_comments  \n", "0                          0  \n", "1                          0  \n", "2                          0  \n", "3                          0  \n", "4                          0  \n", "...                      ...  \n", "354802                     0  \n", "354803                     0  \n", "354804                     0  \n", "354805                     0  \n", "354806                     0  \n", "\n", "[354807 rows x 10 columns]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["add_outcome_values"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (*********.py, line 1)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[64], line 1\u001b[0;36m\u001b[0m\n\u001b[0;31m    add_outcome_values_deduplicated =\u001b[0m\n\u001b[0m                                      ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": ["add_outcome_values_deduplicated = \n", "deduplicated_outcomes = add_outcome_values.groupby(\n", "    ['repo_name', 'standardized_time_weeks']\n", ").last().reset_index()\n", "deduplicated_outcomes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["2528"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["len(add_outcome_values['repo_name'].unique())"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["2528"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["len(compiled_data['repo_name'].unique())"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "object", "type": "unknown"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "pr_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "time_to_first_comment", "rawType": "object", "type": "unknown"}, {"name": "number_of_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_merged_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_closed_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_open_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_pr_comments", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "62060536-2101-4f72-818c-3bed5619b5da", "rows": [["0", "10up/autoshare-for-twitter", "486", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "49", "-12", "1", "False", "0", "0", "0", "197", "5", "342", "0.0", "5.288267030694535", "1.791759469228055", "5.83773044716594", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "23", "21", "23", "0", "0"], ["1", "10up/autoshare-for-twitter", "487", "0.0", "1.0", "2.0", "-0.0349650349650349", "0.5", "-1.09861228866811", "0.3546612443924433", "0.3660254037844386", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "50", "-11", "1", "False", "0", "0", "0", "197", "5", "349", "0.0", "5.288267030694535", "1.791759469228055", "5.857933154483459", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "23", "21", "23", "0", "0"], ["2", "10up/autoshare-for-twitter", "488", "4.0", "1.0", "2.0", "0.1223776223776223", "0.75", "0.9162907318741548", "0.8410806526182262", "0.6653477824119316", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "51", "-10", "1", "False", "0", "0", "0", "209", "5", "356", "1.6094379124341005", "5.3471075307174685", "1.791759469228055", "5.877735781779639", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.8", "11.76527777777778", null, "28", "25", "28", "0", "0"], ["3", "10up/autoshare-for-twitter", "489", "1.0", "1.0", "2.0", "0.0979020979020979", "0.8333333333333334", "0.6931471805599448", "0.8214907876837801", "0.6405201949797534", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "52", "-9", "1", "False", "0", "0", "0", "210", "5", "363", "0.6931471805599453", "5.351858133476067", "1.791759469228055", "5.8971538676367405", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "25.218611111111112", null, "29", "26", "29", "0", "0"], ["4", "10up/autoshare-for-twitter", "490", "3.0", "1.0", "2.0", "0.1433566433566433", "1.0833333333333333", "1.38629436111989", "0.921984989635266", "0.8178456006794831", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "53", "-8", "1", "False", "0", "0", "0", "220", "5", "370", "1.3862943611198906", "5.3981627015177525", "1.791759469228055", "5.916202062607435", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "33.390208333333334", null, "33", "30", "33", "0", "0"], ["5", "10up/autoshare-for-twitter", "491", "3.0", "1.0", "2.0", "0.2587412587412587", "1.1666666666666667", "0.2876820724517805", "0.8106668070686921", "0.5831283861446792", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "54", "-7", "1", "False", "0", "0", "0", "249", "6", "377", "1.3862943611198906", "5.521460917862246", "1.9459101490553128", "5.934894195619588", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "251.36064814814813", null, "36", "33", "36", "0", "0"], ["6", "10up/autoshare-for-twitter", "492", "0.0", "1.0", "2.0", "0.1608391608391608", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "55", "-6", "1", "False", "0", "0", "0", "255", "6", "384", "0.0", "5.545177444479562", "1.9459101490553128", "5.953243334287784", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "36", "33", "36", "0", "0"], ["7", "10up/autoshare-for-twitter", "493", "0.0", "1.0", "2.0", "0.0629370629370629", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "56", "-5", "1", "False", "0", "0", "0", "259", "6", "391", "0.0", "5.560681631015528", "1.9459101490553128", "5.971261839790462", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "148.22722222222222", null, "37", "34", "37", "0", "0"], ["8", "10up/autoshare-for-twitter", "494", "1.0", "1.0", "2.0", "0.0034965034965034", "1.25", "0.6931471805599451", "0.8746974870756378", "0.7040031411428227", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "57", "-4", "1", "False", "0", "0", "0", "261", "6", "398", "0.6931471805599453", "5.568344503761097", "1.9459101490553128", "5.988961416889863", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "37", "34", "37", "0", "0"], ["9", "10up/autoshare-for-twitter", "495", "1.0", "1.0", "2.0", "0.0279720279720279", "1.1666666666666667", "-0.4054651081081645", "0.6816145482921148", "0.3838963480989594", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "58", "-3", "1", "False", "0", "0", "0", "275", "6", "405", "0.6931471805599453", "5.62040086571715", "1.9459101490553128", "6.0063531596017325", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.6666666666666666", "247.32555555555555", null, "40", "36", "40", "0", "0"], ["10", "10up/autoshare-for-twitter", "496", "0.0", "1.0", "2.0", "-0.0244755244755244", "1.0833333333333333", "-0.6931471805599454", "0.5963275109839462", "0.3206231694796373", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "59", "-2", "1", "False", "0", "0", "0", "306", "7", "412", "0.0", "5.726847747587197", "2.079441541679836", "6.023447592961032", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "73.91361111111111", null, "42", "37", "42", "0", "0"], ["11", "10up/autoshare-for-twitter", "497", "5.0", "1.0", "2.0", "0.0769230769230769", "1.5", "1.791759469228055", "0.964145027597638", "0.9362933095037254", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "60", "-1", "1", "False", "0", "0", "0", "309", "7", "419", "1.791759469228055", "5.736572297479192", "2.079441541679836", "6.040254711277414", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "4.687083333333333", null, "44", "39", "44", "0", "0"], ["12", "10up/autoshare-for-twitter", "498", "2.0", "1.0", "2.0", "0.0279720279720279", "1.6666666666666667", "1.0986122886681096", "0.9407704701088077", "0.8618832502903921", "1", "257.0", "0.2171837708830549", "182.0", "7.0", "1.0", "PHP", "437", "61", "0", "1", "False", "0", "0", "0", "336", "8", "426", "1.0986122886681096", "5.820082930352362", "2.197224577336219", "6.056784013228624", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "24.94768518518519", null, "47", "42", "47", "0", "0"], ["13", "10up/autoshare-for-twitter", "499", "1.0", "1.0", "2.0", "-0.0734265734265734", "1.75", "0.6931471805599452", "0.9200588708986858", "0.7708306705345104", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "62", "1", "1", "True", "0", "1", "1", "336", "8", "433", "0.6931471805599453", "5.820082930352362", "2.197224577336219", "6.073044534100404", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "47", "42", "47", "0", "0"], ["14", "10up/autoshare-for-twitter", "500", "1.0", "1.0", "2.0", "0.0", "1.5", "-0.9162907318741552", "0.6419204615368317", "0.2019040735186648", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "63", "2", "1", "True", "0", "1", "1", "337", "8", "440", "0.6931471805599453", "5.823045895483019", "2.197224577336219", "6.089044875446846", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "12.485277777777778", null, "48", "43", "48", "0", "0"], ["15", "10up/autoshare-for-twitter", "501", "0.0", "1.0", "2.0", "-0.0804195804195804", "1.4166666666666667", "-0.6931471805599453", "0.6733815605777215", "0.2725033461986623", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "64", "3", "1", "True", "0", "1", "1", "337", "8", "447", "0.0", "5.823045895483019", "2.197224577336219", "6.104793232414985", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["16", "10up/autoshare-for-twitter", "502", "0.0", "1.0", "2.0", "-0.0629370629370629", "1.1666666666666667", "-1.3862943611198906", "0.4453127259526225", "0.1655715707900131", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "65", "4", "1", "True", "0", "1", "1", "337", "8", "454", "0.0", "5.823045895483019", "2.197224577336219", "6.12029741895095", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["17", "10up/autoshare-for-twitter", "503", "0.0", "1.0", "2.0", "-0.0244755244755244", "0.9166666666666666", "-1.3862943611198906", "0.3847043671232542", "0.2191254981927748", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "66", "5", "1", "True", "0", "1", "1", "337", "8", "461", "0.0", "5.823045895483019", "2.197224577336219", "6.135564891081739", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["18", "10up/autoshare-for-twitter", "504", "0.0", "1.0", "2.0", "-0.1013986013986013", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "67", "6", "1", "True", "0", "1", "1", "337", "8", "468", "0.0", "5.823045895483019", "2.197224577336219", "6.150602768446279", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["19", "10up/autoshare-for-twitter", "505", "0.0", "1.0", "2.0", "-0.1783216783216783", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "68", "7", "1", "True", "0", "1", "1", "337", "8", "475", "0.0", "5.823045895483019", "2.197224577336219", "6.16541785423142", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["20", "10up/autoshare-for-twitter", "506", "0.0", "1.0", "2.0", "-0.2097902097902098", "0.8333333333333334", "-0.6931471805599453", "0.5349892557559008", "0.3594798050202465", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "69", "8", "1", "True", "0", "1", "1", "337", "8", "482", "0.0", "5.823045895483019", "2.197224577336219", "6.180016653652572", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["21", "10up/autoshare-for-twitter", "507", "0.0", "1.0", "2.0", "-0.2342657342657342", "0.75", "-0.6931471805599453", "0.5142093777192814", "0.3728848808245891", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "70", "9", "1", "True", "0", "1", "1", "337", "8", "489", "0.0", "5.823045895483019", "2.197224577336219", "6.194405391104672", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["22", "10up/autoshare-for-twitter", "508", "0.0", "1.0", "2.0", "-0.2972027972027972", "0.75", "0.0", "0.679178699175393", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "71", "10", "1", "True", "0", "1", "1", "337", "8", "496", "0.0", "5.823045895483019", "2.197224577336219", "6.208590026096629", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["23", "10up/autoshare-for-twitter", "509", "0.0", "1.0", "2.0", "-0.1328671328671328", "0.3333333333333333", "-1.791759469228055", "0.1887081616597619", "0.3549723794374981", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "72", "11", "1", "True", "0", "1", "1", "337", "8", "503", "0.0", "5.823045895483019", "2.197224577336219", "6.222576268071369", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["24", "10up/autoshare-for-twitter", "510", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.1666666666666666", "-1.0986122886681096", "0.2825301567477209", "0.4543519511761902", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "73", "12", "1", "True", "0", "1", "1", "337", "8", "510", "0.0", "5.823045895483019", "2.197224577336219", "6.236369590203704", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["25", "AdaptiveCpp/AdaptiveCpp", "486", "0.0", "3.0", "6.0", "-0.0734265734265734", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "77", "-12", "0", "False", "0", "0", "0", "387", "9", "359", "0.0", "5.961005339623274", "2.302585092994045", "5.886104031450156", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "108", "100", "108", "0", "0"], ["26", "AdaptiveCpp/AdaptiveCpp", "487", "1.0", "3.0", "6.0", "-0.0664335664335664", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "78", "-11", "0", "False", "0", "0", "0", "388", "9", "366", "0.6931471805599453", "5.963579343618446", "2.302585092994045", "5.90536184805457", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "455.39222222222224", null, "109", "101", "109", "0", "0"], ["27", "AdaptiveCpp/AdaptiveCpp", "488", "0.0", "3.0", "6.0", "-0.0524475524475524", "0.75", "-1.09861228866811", "0.4137189778658776", "0.3049238750315606", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "79", "-10", "0", "False", "0", "0", "0", "389", "9", "373", "0.0", "5.966146739123692", "2.302585092994045", "5.924255797414531", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.3333333333333333", "1264.34", null, "112", "102", "112", "0", "0"], ["28", "AdaptiveCpp/AdaptiveCpp", "489", "0.0", "3.0", "6.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "80", "-9", "0", "False", "0", "0", "0", "389", "9", "380", "0.0", "5.966146739123692", "2.302585092994045", "5.9427993751267", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "112", "102", "112", "0", "0"], ["29", "AdaptiveCpp/AdaptiveCpp", "490", "2.0", "3.0", "6.0", "-0.0489510489510489", "0.8333333333333334", "1.0986122886681091", "0.8734646144545724", "0.7141264037070777", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "81", "-8", "0", "False", "0", "0", "0", "391", "9", "387", "1.0986122886681096", "5.971261839790462", "2.302585092994045", "5.961005339623274", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "15.10361111111111", null, "113", "103", "113", "0", "0"], ["30", "AdaptiveCpp/AdaptiveCpp", "491", "2.0", "3.0", "6.0", "0.0489510489510489", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "82", "-7", "0", "False", "0", "0", "0", "394", "10", "394", "1.0986122886681096", "5.978885764901122", "2.3978952727983707", "5.978885764901122", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "25.80388888888889", null, "116", "106", "116", "0", "0"], ["31", "AdaptiveCpp/AdaptiveCpp", "492", "1.0", "3.0", "6.0", "0.0629370629370629", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "83", "-6", "0", "False", "0", "0", "0", "394", "10", "401", "0.6931471805599453", "5.978885764901122", "2.3978952727983707", "5.996452088619021", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "116", "106", "116", "0", "0"], ["32", "AdaptiveCpp/AdaptiveCpp", "493", "1.0", "3.0", "6.0", "0.0314685314685314", "0.9166666666666666", "0.6931471805599448", "0.8333855399562498", "0.6537094706869915", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "84", "-5", "0", "False", "0", "0", "0", "396", "11", "408", "0.6931471805599453", "5.98393628068719", "2.4849066497880004", "6.013715156042801", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "158.70263888888888", null, "118", "108", "118", "0", "0"], ["33", "AdaptiveCpp/AdaptiveCpp", "494", "1.0", "3.0", "6.0", "0.0384615384615384", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "85", "-4", "0", "False", "0", "0", "0", "400", "11", "415", "0.6931471805599453", "5.993961427306569", "2.4849066497880004", "6.030685260261263", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "54.522777777777776", null, "121", "111", "121", "0", "0"], ["34", "AdaptiveCpp/AdaptiveCpp", "495", "4.0", "3.0", "6.0", "0.1608391608391608", "1.1666666666666667", "0.9162907318741546", "0.889235659522009", "0.7444078107515406", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "86", "-3", "0", "False", "0", "0", "0", "411", "12", "422", "1.6094379124341005", "6.021023349349526", "2.5649493574615367", "6.0473721790462776", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "86.71481481481482", null, "124", "114", "124", "0", "0"], ["35", "AdaptiveCpp/AdaptiveCpp", "496", "4.0", "3.0", "6.0", "0.3076923076923077", "1.3333333333333333", "0.5108256237659902", "0.8634398378798575", "0.6639843473284657", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "87", "-2", "0", "False", "0", "0", "0", "419", "13", "429", "1.6094379124341005", "6.040254711277414", "2.6390573296152584", "6.063785208687608", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "747.0770138888889", null, "128", "118", "128", "0", "0"], ["36", "AdaptiveCpp/AdaptiveCpp", "497", "2.0", "3.0", "6.0", "0.2727272727272727", "1.5", "1.0986122886681091", "0.9307722154980688", "0.8386095222035911", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "88", "-1", "0", "False", "0", "0", "0", "423", "13", "436", "1.0986122886681096", "6.049733455231958", "2.6390573296152584", "6.07993319509559", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.75", "262.4032407407407", null, "132", "121", "132", "0", "0"], ["37", "AdaptiveCpp/AdaptiveCpp", "498", "2.0", "3.0", "6.0", "0.2237762237762237", "1.6666666666666667", "1.0986122886681091", "0.9407704701088077", "0.861883250290392", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "89", "0", "0", "False", "0", "0", "0", "427", "13", "443", "1.0986122886681096", "6.059123195581797", "2.6390573296152584", "6.095824562432225", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "913.2144444444444", null, "133", "122", "133", "0", "0"], ["38", "AdaptiveCpp/AdaptiveCpp", "499", "0.0", "3.0", "6.0", "0.1293706293706293", "1.5833333333333333", "-0.693147180559946", "0.7089285864699496", "0.2502117946664587", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "90", "1", "0", "True", "0", "1", "0", "430", "13", "450", "0.0", "6.066108090103747", "2.6390573296152584", "6.111467339502678", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "382.2038888888889", null, "134", "123", "134", "0", "0"], ["39", "AdaptiveCpp/AdaptiveCpp", "500", "0.0", "3.0", "6.0", "-0.0034965034965034", "1.5833333333333333", "-6.661338147750939e-16", "0.829676081356154", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "91", "2", "0", "True", "0", "1", "0", "431", "13", "457", "0.0", "6.06842558824411", "2.6390573296152584", "6.1268691841141845", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "158.27305555555554", null, "135", "124", "135", "0", "0"], ["40", "AdaptiveCpp/AdaptiveCpp", "501", "3.0", "3.0", "6.0", "-0.0209790209790209", "1.8333333333333333", "1.38629436111989", "0.9615662577192609", "0.927003081517424", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "92", "3", "0", "True", "0", "1", "0", "433", "13", "464", "1.3862943611198906", "6.073044534100404", "2.6390573296152584", "6.142037405587356", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "135", "124", "135", "0", "0"], ["41", "AdaptiveCpp/AdaptiveCpp", "502", "3.0", "3.0", "6.0", "0.0314685314685314", "1.9166666666666667", "0.2876820724517803", "0.9006393499233735", "0.634458169632737", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "93", "4", "0", "True", "0", "1", "0", "437", "13", "471", "1.3862943611198906", "6.0822189103764455", "2.6390573296152584", "6.156978985585555", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "31.636203703703703", null, "138", "127", "138", "0", "0"], ["42", "AdaptiveCpp/AdaptiveCpp", "503", "1.0", "3.0", "6.0", "0.0", "1.8333333333333333", "-0.4054651081081649", "0.8065689432388163", "0.3222714364606489", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "94", "5", "0", "True", "0", "1", "0", "437", "13", "478", "0.6931471805599453", "6.0822189103764455", "2.6390573296152584", "6.171700597410915", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "139", "127", "139", "0", "0"], ["43", "AdaptiveCpp/AdaptiveCpp", "504", "0.0", "3.0", "6.0", "-0.1083916083916083", "1.75", "-0.6931471805599458", "0.7420886557878206", "0.2291693294654893", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "95", "6", "0", "True", "0", "1", "0", "438", "13", "485", "0.0", "6.084499413075172", "2.6390573296152584", "6.186208623900494", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "139", "127", "139", "0", "0"], ["44", "AdaptiveCpp/AdaptiveCpp", "505", "0.0", "3.0", "6.0", "-0.2097902097902098", "1.6666666666666667", "-0.6931471805599457", "0.7258204499338335", "0.2395323119764419", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "96", "7", "0", "True", "0", "1", "0", "438", "13", "492", "0.0", "6.084499413075172", "2.6390573296152584", "6.20050917404269", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "139", "127", "139", "0", "0"], ["45", "AdaptiveCpp/AdaptiveCpp", "506", "0.0", "3.0", "6.0", "-0.3041958041958042", "1.5833333333333333", "-0.6931471805599457", "0.7089285864699497", "0.2502117946664588", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "97", "8", "0", "True", "0", "1", "0", "439", "14", "499", "0.0", "6.0867747269123065", "2.70805020110221", "6.214608098422191", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "602.14", null, "141", "128", "141", "0", "0"], ["46", "AdaptiveCpp/AdaptiveCpp", "507", "2.0", "3.0", "6.0", "-0.1783216783216783", "1.4166666666666667", "-0.5108256237659912", "0.7121483581398508", "0.3265854170022765", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "98", "9", "0", "True", "0", "1", "0", "442", "15", "506", "1.0986122886681096", "6.093569770045136", "2.772588722239781", "6.228511003591183", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "301.78037037037035", null, "144", "131", "144", "0", "0"], ["47", "AdaptiveCpp/AdaptiveCpp", "508", "0.0", "3.0", "6.0", "-0.1153846153846153", "1.0833333333333333", "-1.609437912434101", "0.371425890455925", "0.1488615992206787", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "99", "10", "0", "True", "0", "1", "0", "443", "16", "513", "0.0", "6.095824562432225", "2.833213344056216", "6.242223265455165", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "128.98805555555555", null, "145", "132", "145", "0", "0"], ["48", "AdaptiveCpp/AdaptiveCpp", "509", "1.0", "3.0", "6.0", "-0.0769230769230769", "1.0", "-0.4054651081081649", "0.6444049826448044", "0.3999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "100", "11", "0", "True", "0", "1", "0", "444", "16", "520", "0.6931471805599453", "6.09807428216624", "2.833213344056216", "6.255750041753367", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "142.8438888888889", null, "147", "133", "147", "0", "0"], ["49", "AdaptiveCpp/AdaptiveCpp", "510", "2.0", "3.0", "6.0", "0.0069930069930069", "1.0", "-5.551115123125785e-16", "0.7310585786300048", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "101", "12", "0", "True", "0", "1", "0", "446", "16", "527", "1.0986122886681096", "6.102558594613568", "2.833213344056216", "6.269096283706261", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "40.95861111111111", null, "149", "134", "149", "0", "0"]], "shape": {"columns": 50, "rows": 203912}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>pr_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>time_to_first_comment</th>\n", "      <th>number_of_prs</th>\n", "      <th>number_of_merged_prs</th>\n", "      <th>number_of_closed_prs</th>\n", "      <th>number_of_open_prs</th>\n", "      <th>number_of_pr_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>486</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.069930</td>\n", "      <td>0.666667</td>\n", "      <td>-6.931472e-01</td>\n", "      <td>0.493380</td>\n", "      <td>0.386488</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>23</td>\n", "      <td>21</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.034965</td>\n", "      <td>0.500000</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.354661</td>\n", "      <td>0.366025</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>23</td>\n", "      <td>21</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>488</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.122378</td>\n", "      <td>0.750000</td>\n", "      <td>9.162907e-01</td>\n", "      <td>0.841081</td>\n", "      <td>0.665348</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>0.800000</td>\n", "      <td>11.765278</td>\n", "      <td>None</td>\n", "      <td>28</td>\n", "      <td>25</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>489</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.097902</td>\n", "      <td>0.833333</td>\n", "      <td>6.931472e-01</td>\n", "      <td>0.821491</td>\n", "      <td>0.640520</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>25.218611</td>\n", "      <td>None</td>\n", "      <td>29</td>\n", "      <td>26</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>490</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.143357</td>\n", "      <td>1.083333</td>\n", "      <td>1.386294e+00</td>\n", "      <td>0.921985</td>\n", "      <td>0.817846</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>33.390208</td>\n", "      <td>None</td>\n", "      <td>33</td>\n", "      <td>30</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203907</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.388112</td>\n", "      <td>3.416667</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.910361</td>\n", "      <td>0.022897</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>1.000000</td>\n", "      <td>116.207315</td>\n", "      <td>None</td>\n", "      <td>444</td>\n", "      <td>373</td>\n", "      <td>442</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203908</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.038462</td>\n", "      <td>2.416667</td>\n", "      <td>-1.609438e+00</td>\n", "      <td>0.691519</td>\n", "      <td>0.020046</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>1.000000</td>\n", "      <td>171.439583</td>\n", "      <td>None</td>\n", "      <td>446</td>\n", "      <td>375</td>\n", "      <td>444</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203909</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.402098</td>\n", "      <td>2.916667</td>\n", "      <td>7.884574e-01</td>\n", "      <td>0.975993</td>\n", "      <td>0.908849</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.777778</td>\n", "      <td>16.118135</td>\n", "      <td>None</td>\n", "      <td>455</td>\n", "      <td>382</td>\n", "      <td>453</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203910</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.325175</td>\n", "      <td>2.916667</td>\n", "      <td>-6.661338e-16</td>\n", "      <td>0.948664</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.800000</td>\n", "      <td>361.275417</td>\n", "      <td>None</td>\n", "      <td>460</td>\n", "      <td>386</td>\n", "      <td>458</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203911</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>563</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.234266</td>\n", "      <td>3.250000</td>\n", "      <td>1.609438e+00</td>\n", "      <td>0.992305</td>\n", "      <td>0.994679</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203912 rows × 50 columns</p>\n", "</div>"], "text/plain": ["                         repo_name standardized_time_weeks  pr_throughput  \\\n", "0       10up/autoshare-for-twitter                     486            0.0   \n", "1       10up/autoshare-for-twitter                     487            0.0   \n", "2       10up/autoshare-for-twitter                     488            4.0   \n", "3       10up/autoshare-for-twitter                     489            1.0   \n", "4       10up/autoshare-for-twitter                     490            3.0   \n", "...                            ...                     ...            ...   \n", "203907     tree-sitter/tree-sitter                     559            1.0   \n", "203908     tree-sitter/tree-sitter                     560            2.0   \n", "203909     tree-sitter/tree-sitter                     561           10.0   \n", "203910     tree-sitter/tree-sitter                     562            2.0   \n", "203911     tree-sitter/tree-sitter                     563            4.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "0                       1.0                 2.0      -0.069930      0.666667   \n", "1                       1.0                 2.0      -0.034965      0.500000   \n", "2                       1.0                 2.0       0.122378      0.750000   \n", "3                       1.0                 2.0       0.097902      0.833333   \n", "4                       1.0                 2.0       0.143357      1.083333   \n", "...                     ...                 ...            ...           ...   \n", "203907                  1.0                13.0      -0.388112      3.416667   \n", "203908                  1.0                13.0       0.038462      2.416667   \n", "203909                  1.0                13.0       0.402098      2.916667   \n", "203910                  1.0                13.0       0.325175      2.916667   \n", "203911                  1.0                13.0       0.234266      3.250000   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "0                -6.931472e-01            0.493380                 0.386488   \n", "1                -1.098612e+00            0.354661                 0.366025   \n", "2                 9.162907e-01            0.841081                 0.665348   \n", "3                 6.931472e-01            0.821491                 0.640520   \n", "4                 1.386294e+00            0.921985                 0.817846   \n", "...                        ...                 ...                      ...   \n", "203907           -1.098612e+00            0.910361                 0.022897   \n", "203908           -1.609438e+00            0.691519                 0.020046   \n", "203909            7.884574e-01            0.975993                 0.908849   \n", "203910           -6.661338e-16            0.948664                 0.500000   \n", "203911            1.609438e+00            0.992305                 0.994679   \n", "\n", "        ...  log_project_age_before_treatment  project_main_language  \\\n", "0       ...                          6.056784                    PHP   \n", "1       ...                          6.056784                    PHP   \n", "2       ...                          6.056784                    PHP   \n", "3       ...                          6.056784                    PHP   \n", "4       ...                          6.056784                    PHP   \n", "...     ...                               ...                    ...   \n", "203907  ...                          5.966147                     Go   \n", "203908  ...                          5.966147                     Go   \n", "203909  ...                          5.966147                     Go   \n", "203910  ...                          5.966147                     Go   \n", "203911  ...                          5.966147                     Go   \n", "\n", "        pr_success_rate  time_to_merge  time_to_first_comment  number_of_prs  \\\n", "0                   NaN            NaN                   None             23   \n", "1                   NaN            NaN                   None             23   \n", "2              0.800000      11.765278                   None             28   \n", "3              1.000000      25.218611                   None             29   \n", "4              1.000000      33.390208                   None             33   \n", "...                 ...            ...                    ...            ...   \n", "203907         1.000000     116.207315                   None            444   \n", "203908         1.000000     171.439583                   None            446   \n", "203909         0.777778      16.118135                   None            455   \n", "203910         0.800000     361.275417                   None            460   \n", "203911              NaN            NaN                    NaN            NaN   \n", "\n", "       number_of_merged_prs  number_of_closed_prs  number_of_open_prs  \\\n", "0                        21                    23                   0   \n", "1                        21                    23                   0   \n", "2                        25                    28                   0   \n", "3                        26                    29                   0   \n", "4                        30                    33                   0   \n", "...                     ...                   ...                 ...   \n", "203907                  373                   442                   2   \n", "203908                  375                   444                   2   \n", "203909                  382                   453                   2   \n", "203910                  386                   458                   2   \n", "203911                  NaN                   NaN                 NaN   \n", "\n", "        number_of_pr_comments  \n", "0                           0  \n", "1                           0  \n", "2                           0  \n", "3                           0  \n", "4                           0  \n", "...                       ...  \n", "203907                      0  \n", "203908                      0  \n", "203909                      0  \n", "203910                      0  \n", "203911                    NaN  \n", "\n", "[203912 rows x 50 columns]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "object", "type": "unknown"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "pr_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "time_to_first_comment", "rawType": "object", "type": "unknown"}, {"name": "number_of_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_merged_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_closed_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_open_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_pr_comments", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "ec97f4b2-8c46-4b61-8cb0-e2fbfd9bccf4", "rows": [["0", "10up/autoshare-for-twitter", "486", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "49", "-12", "1", "False", "0", "0", "0", "197", "5", "342", "0.0", "5.288267030694535", "1.791759469228055", "5.83773044716594", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "23", "21", "23", "0", "0"], ["1", "10up/autoshare-for-twitter", "487", "0.0", "1.0", "2.0", "-0.0349650349650349", "0.5", "-1.09861228866811", "0.3546612443924433", "0.3660254037844386", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "50", "-11", "1", "False", "0", "0", "0", "197", "5", "349", "0.0", "5.288267030694535", "1.791759469228055", "5.857933154483459", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "23", "21", "23", "0", "0"], ["2", "10up/autoshare-for-twitter", "488", "4.0", "1.0", "2.0", "0.1223776223776223", "0.75", "0.9162907318741548", "0.8410806526182262", "0.6653477824119316", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "51", "-10", "1", "False", "0", "0", "0", "209", "5", "356", "1.6094379124341005", "5.3471075307174685", "1.791759469228055", "5.877735781779639", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.8", "11.76527777777778", null, "28", "25", "28", "0", "0"], ["3", "10up/autoshare-for-twitter", "489", "1.0", "1.0", "2.0", "0.0979020979020979", "0.8333333333333334", "0.6931471805599448", "0.8214907876837801", "0.6405201949797534", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "52", "-9", "1", "False", "0", "0", "0", "210", "5", "363", "0.6931471805599453", "5.351858133476067", "1.791759469228055", "5.8971538676367405", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "25.218611111111112", null, "29", "26", "29", "0", "0"], ["4", "10up/autoshare-for-twitter", "490", "3.0", "1.0", "2.0", "0.1433566433566433", "1.0833333333333333", "1.38629436111989", "0.921984989635266", "0.8178456006794831", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "53", "-8", "1", "False", "0", "0", "0", "220", "5", "370", "1.3862943611198906", "5.3981627015177525", "1.791759469228055", "5.916202062607435", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "33.390208333333334", null, "33", "30", "33", "0", "0"], ["5", "10up/autoshare-for-twitter", "491", "3.0", "1.0", "2.0", "0.2587412587412587", "1.1666666666666667", "0.2876820724517805", "0.8106668070686921", "0.5831283861446792", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "54", "-7", "1", "False", "0", "0", "0", "249", "6", "377", "1.3862943611198906", "5.521460917862246", "1.9459101490553128", "5.934894195619588", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "251.36064814814813", null, "36", "33", "36", "0", "0"], ["6", "10up/autoshare-for-twitter", "492", "0.0", "1.0", "2.0", "0.1608391608391608", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "55", "-6", "1", "False", "0", "0", "0", "255", "6", "384", "0.0", "5.545177444479562", "1.9459101490553128", "5.953243334287784", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "36", "33", "36", "0", "0"], ["7", "10up/autoshare-for-twitter", "493", "0.0", "1.0", "2.0", "0.0629370629370629", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "56", "-5", "1", "False", "0", "0", "0", "259", "6", "391", "0.0", "5.560681631015528", "1.9459101490553128", "5.971261839790462", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "148.22722222222222", null, "37", "34", "37", "0", "0"], ["8", "10up/autoshare-for-twitter", "494", "1.0", "1.0", "2.0", "0.0034965034965034", "1.25", "0.6931471805599451", "0.8746974870756378", "0.7040031411428227", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "57", "-4", "1", "False", "0", "0", "0", "261", "6", "398", "0.6931471805599453", "5.568344503761097", "1.9459101490553128", "5.988961416889863", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "37", "34", "37", "0", "0"], ["9", "10up/autoshare-for-twitter", "495", "1.0", "1.0", "2.0", "0.0279720279720279", "1.1666666666666667", "-0.4054651081081645", "0.6816145482921148", "0.3838963480989594", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "58", "-3", "1", "False", "0", "0", "0", "275", "6", "405", "0.6931471805599453", "5.62040086571715", "1.9459101490553128", "6.0063531596017325", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.6666666666666666", "247.32555555555555", null, "40", "36", "40", "0", "0"], ["10", "10up/autoshare-for-twitter", "496", "0.0", "1.0", "2.0", "-0.0244755244755244", "1.0833333333333333", "-0.6931471805599454", "0.5963275109839462", "0.3206231694796373", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "59", "-2", "1", "False", "0", "0", "0", "306", "7", "412", "0.0", "5.726847747587197", "2.079441541679836", "6.023447592961032", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "73.91361111111111", null, "42", "37", "42", "0", "0"], ["11", "10up/autoshare-for-twitter", "497", "5.0", "1.0", "2.0", "0.0769230769230769", "1.5", "1.791759469228055", "0.964145027597638", "0.9362933095037254", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "60", "-1", "1", "False", "0", "0", "0", "309", "7", "419", "1.791759469228055", "5.736572297479192", "2.079441541679836", "6.040254711277414", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "4.687083333333333", null, "44", "39", "44", "0", "0"], ["12", "10up/autoshare-for-twitter", "498", "2.0", "1.0", "2.0", "0.0279720279720279", "1.6666666666666667", "1.0986122886681096", "0.9407704701088077", "0.8618832502903921", "1", "257.0", "0.2171837708830549", "182.0", "7.0", "1.0", "PHP", "437", "61", "0", "1", "False", "0", "0", "0", "336", "8", "426", "1.0986122886681096", "5.820082930352362", "2.197224577336219", "6.056784013228624", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "24.94768518518519", null, "47", "42", "47", "0", "0"], ["13", "10up/autoshare-for-twitter", "499", "1.0", "1.0", "2.0", "-0.0734265734265734", "1.75", "0.6931471805599452", "0.9200588708986858", "0.7708306705345104", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "62", "1", "1", "True", "0", "1", "1", "336", "8", "433", "0.6931471805599453", "5.820082930352362", "2.197224577336219", "6.073044534100404", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "47", "42", "47", "0", "0"], ["14", "10up/autoshare-for-twitter", "500", "1.0", "1.0", "2.0", "0.0", "1.5", "-0.9162907318741552", "0.6419204615368317", "0.2019040735186648", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "63", "2", "1", "True", "0", "1", "1", "337", "8", "440", "0.6931471805599453", "5.823045895483019", "2.197224577336219", "6.089044875446846", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "12.485277777777778", null, "48", "43", "48", "0", "0"], ["15", "10up/autoshare-for-twitter", "501", "0.0", "1.0", "2.0", "-0.0804195804195804", "1.4166666666666667", "-0.6931471805599453", "0.6733815605777215", "0.2725033461986623", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "64", "3", "1", "True", "0", "1", "1", "337", "8", "447", "0.0", "5.823045895483019", "2.197224577336219", "6.104793232414985", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["16", "10up/autoshare-for-twitter", "502", "0.0", "1.0", "2.0", "-0.0629370629370629", "1.1666666666666667", "-1.3862943611198906", "0.4453127259526225", "0.1655715707900131", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "65", "4", "1", "True", "0", "1", "1", "337", "8", "454", "0.0", "5.823045895483019", "2.197224577336219", "6.12029741895095", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["17", "10up/autoshare-for-twitter", "503", "0.0", "1.0", "2.0", "-0.0244755244755244", "0.9166666666666666", "-1.3862943611198906", "0.3847043671232542", "0.2191254981927748", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "66", "5", "1", "True", "0", "1", "1", "337", "8", "461", "0.0", "5.823045895483019", "2.197224577336219", "6.135564891081739", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["18", "10up/autoshare-for-twitter", "504", "0.0", "1.0", "2.0", "-0.1013986013986013", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "67", "6", "1", "True", "0", "1", "1", "337", "8", "468", "0.0", "5.823045895483019", "2.197224577336219", "6.150602768446279", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["19", "10up/autoshare-for-twitter", "505", "0.0", "1.0", "2.0", "-0.1783216783216783", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "68", "7", "1", "True", "0", "1", "1", "337", "8", "475", "0.0", "5.823045895483019", "2.197224577336219", "6.16541785423142", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["20", "10up/autoshare-for-twitter", "506", "0.0", "1.0", "2.0", "-0.2097902097902098", "0.8333333333333334", "-0.6931471805599453", "0.5349892557559008", "0.3594798050202465", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "69", "8", "1", "True", "0", "1", "1", "337", "8", "482", "0.0", "5.823045895483019", "2.197224577336219", "6.180016653652572", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["21", "10up/autoshare-for-twitter", "507", "0.0", "1.0", "2.0", "-0.2342657342657342", "0.75", "-0.6931471805599453", "0.5142093777192814", "0.3728848808245891", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "70", "9", "1", "True", "0", "1", "1", "337", "8", "489", "0.0", "5.823045895483019", "2.197224577336219", "6.194405391104672", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["22", "10up/autoshare-for-twitter", "508", "0.0", "1.0", "2.0", "-0.2972027972027972", "0.75", "0.0", "0.679178699175393", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "71", "10", "1", "True", "0", "1", "1", "337", "8", "496", "0.0", "5.823045895483019", "2.197224577336219", "6.208590026096629", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["23", "10up/autoshare-for-twitter", "509", "0.0", "1.0", "2.0", "-0.1328671328671328", "0.3333333333333333", "-1.791759469228055", "0.1887081616597619", "0.3549723794374981", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "72", "11", "1", "True", "0", "1", "1", "337", "8", "503", "0.0", "5.823045895483019", "2.197224577336219", "6.222576268071369", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["24", "10up/autoshare-for-twitter", "510", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.1666666666666666", "-1.0986122886681096", "0.2825301567477209", "0.4543519511761902", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "73", "12", "1", "True", "0", "1", "1", "337", "8", "510", "0.0", "5.823045895483019", "2.197224577336219", "6.236369590203704", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "48", "43", "48", "0", "0"], ["25", "AdaptiveCpp/AdaptiveCpp", "486", "0.0", "3.0", "6.0", "-0.0734265734265734", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "77", "-12", "0", "False", "0", "0", "0", "387", "9", "359", "0.0", "5.961005339623274", "2.302585092994045", "5.886104031450156", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "108", "100", "108", "0", "0"], ["26", "AdaptiveCpp/AdaptiveCpp", "487", "1.0", "3.0", "6.0", "-0.0664335664335664", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "78", "-11", "0", "False", "0", "0", "0", "388", "9", "366", "0.6931471805599453", "5.963579343618446", "2.302585092994045", "5.90536184805457", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "455.39222222222224", null, "109", "101", "109", "0", "0"], ["27", "AdaptiveCpp/AdaptiveCpp", "488", "0.0", "3.0", "6.0", "-0.0524475524475524", "0.75", "-1.09861228866811", "0.4137189778658776", "0.3049238750315606", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "79", "-10", "0", "False", "0", "0", "0", "389", "9", "373", "0.0", "5.966146739123692", "2.302585092994045", "5.924255797414531", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.3333333333333333", "1264.34", null, "112", "102", "112", "0", "0"], ["28", "AdaptiveCpp/AdaptiveCpp", "489", "0.0", "3.0", "6.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "80", "-9", "0", "False", "0", "0", "0", "389", "9", "380", "0.0", "5.966146739123692", "2.302585092994045", "5.9427993751267", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "112", "102", "112", "0", "0"], ["29", "AdaptiveCpp/AdaptiveCpp", "490", "2.0", "3.0", "6.0", "-0.0489510489510489", "0.8333333333333334", "1.0986122886681091", "0.8734646144545724", "0.7141264037070777", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "81", "-8", "0", "False", "0", "0", "0", "391", "9", "387", "1.0986122886681096", "5.971261839790462", "2.302585092994045", "5.961005339623274", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "15.10361111111111", null, "113", "103", "113", "0", "0"], ["30", "AdaptiveCpp/AdaptiveCpp", "491", "2.0", "3.0", "6.0", "0.0489510489510489", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "82", "-7", "0", "False", "0", "0", "0", "394", "10", "394", "1.0986122886681096", "5.978885764901122", "2.3978952727983707", "5.978885764901122", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "25.80388888888889", null, "116", "106", "116", "0", "0"], ["31", "AdaptiveCpp/AdaptiveCpp", "492", "1.0", "3.0", "6.0", "0.0629370629370629", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "83", "-6", "0", "False", "0", "0", "0", "394", "10", "401", "0.6931471805599453", "5.978885764901122", "2.3978952727983707", "5.996452088619021", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "116", "106", "116", "0", "0"], ["32", "AdaptiveCpp/AdaptiveCpp", "493", "1.0", "3.0", "6.0", "0.0314685314685314", "0.9166666666666666", "0.6931471805599448", "0.8333855399562498", "0.6537094706869915", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "84", "-5", "0", "False", "0", "0", "0", "396", "11", "408", "0.6931471805599453", "5.98393628068719", "2.4849066497880004", "6.013715156042801", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "158.70263888888888", null, "118", "108", "118", "0", "0"], ["33", "AdaptiveCpp/AdaptiveCpp", "494", "1.0", "3.0", "6.0", "0.0384615384615384", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "85", "-4", "0", "False", "0", "0", "0", "400", "11", "415", "0.6931471805599453", "5.993961427306569", "2.4849066497880004", "6.030685260261263", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "54.522777777777776", null, "121", "111", "121", "0", "0"], ["34", "AdaptiveCpp/AdaptiveCpp", "495", "4.0", "3.0", "6.0", "0.1608391608391608", "1.1666666666666667", "0.9162907318741546", "0.889235659522009", "0.7444078107515406", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "86", "-3", "0", "False", "0", "0", "0", "411", "12", "422", "1.6094379124341005", "6.021023349349526", "2.5649493574615367", "6.0473721790462776", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "86.71481481481482", null, "124", "114", "124", "0", "0"], ["35", "AdaptiveCpp/AdaptiveCpp", "496", "4.0", "3.0", "6.0", "0.3076923076923077", "1.3333333333333333", "0.5108256237659902", "0.8634398378798575", "0.6639843473284657", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "87", "-2", "0", "False", "0", "0", "0", "419", "13", "429", "1.6094379124341005", "6.040254711277414", "2.6390573296152584", "6.063785208687608", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "747.0770138888889", null, "128", "118", "128", "0", "0"], ["36", "AdaptiveCpp/AdaptiveCpp", "497", "2.0", "3.0", "6.0", "0.2727272727272727", "1.5", "1.0986122886681091", "0.9307722154980688", "0.8386095222035911", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "88", "-1", "0", "False", "0", "0", "0", "423", "13", "436", "1.0986122886681096", "6.049733455231958", "2.6390573296152584", "6.07993319509559", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.75", "262.4032407407407", null, "132", "121", "132", "0", "0"], ["37", "AdaptiveCpp/AdaptiveCpp", "498", "2.0", "3.0", "6.0", "0.2237762237762237", "1.6666666666666667", "1.0986122886681091", "0.9407704701088077", "0.861883250290392", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "89", "0", "0", "False", "0", "0", "0", "427", "13", "443", "1.0986122886681096", "6.059123195581797", "2.6390573296152584", "6.095824562432225", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "913.2144444444444", null, "133", "122", "133", "0", "0"], ["38", "AdaptiveCpp/AdaptiveCpp", "499", "0.0", "3.0", "6.0", "0.1293706293706293", "1.5833333333333333", "-0.693147180559946", "0.7089285864699496", "0.2502117946664587", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "90", "1", "0", "True", "0", "1", "0", "430", "13", "450", "0.0", "6.066108090103747", "2.6390573296152584", "6.111467339502678", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "382.2038888888889", null, "134", "123", "134", "0", "0"], ["39", "AdaptiveCpp/AdaptiveCpp", "500", "0.0", "3.0", "6.0", "-0.0034965034965034", "1.5833333333333333", "-6.661338147750939e-16", "0.829676081356154", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "91", "2", "0", "True", "0", "1", "0", "431", "13", "457", "0.0", "6.06842558824411", "2.6390573296152584", "6.1268691841141845", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "158.27305555555554", null, "135", "124", "135", "0", "0"], ["40", "AdaptiveCpp/AdaptiveCpp", "501", "3.0", "3.0", "6.0", "-0.0209790209790209", "1.8333333333333333", "1.38629436111989", "0.9615662577192609", "0.927003081517424", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "92", "3", "0", "True", "0", "1", "0", "433", "13", "464", "1.3862943611198906", "6.073044534100404", "2.6390573296152584", "6.142037405587356", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "135", "124", "135", "0", "0"], ["41", "AdaptiveCpp/AdaptiveCpp", "502", "3.0", "3.0", "6.0", "0.0314685314685314", "1.9166666666666667", "0.2876820724517803", "0.9006393499233735", "0.634458169632737", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "93", "4", "0", "True", "0", "1", "0", "437", "13", "471", "1.3862943611198906", "6.0822189103764455", "2.6390573296152584", "6.156978985585555", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "31.636203703703703", null, "138", "127", "138", "0", "0"], ["42", "AdaptiveCpp/AdaptiveCpp", "503", "1.0", "3.0", "6.0", "0.0", "1.8333333333333333", "-0.4054651081081649", "0.8065689432388163", "0.3222714364606489", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "94", "5", "0", "True", "0", "1", "0", "437", "13", "478", "0.6931471805599453", "6.0822189103764455", "2.6390573296152584", "6.171700597410915", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.0", null, null, "139", "127", "139", "0", "0"], ["43", "AdaptiveCpp/AdaptiveCpp", "504", "0.0", "3.0", "6.0", "-0.1083916083916083", "1.75", "-0.6931471805599458", "0.7420886557878206", "0.2291693294654893", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "95", "6", "0", "True", "0", "1", "0", "438", "13", "485", "0.0", "6.084499413075172", "2.6390573296152584", "6.186208623900494", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "139", "127", "139", "0", "0"], ["44", "AdaptiveCpp/AdaptiveCpp", "505", "0.0", "3.0", "6.0", "-0.2097902097902098", "1.6666666666666667", "-0.6931471805599457", "0.7258204499338335", "0.2395323119764419", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "96", "7", "0", "True", "0", "1", "0", "438", "13", "492", "0.0", "6.084499413075172", "2.6390573296152584", "6.20050917404269", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, null, null, "139", "127", "139", "0", "0"], ["45", "AdaptiveCpp/AdaptiveCpp", "506", "0.0", "3.0", "6.0", "-0.3041958041958042", "1.5833333333333333", "-0.6931471805599457", "0.7089285864699497", "0.2502117946664588", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "97", "8", "0", "True", "0", "1", "0", "439", "14", "499", "0.0", "6.0867747269123065", "2.70805020110221", "6.214608098422191", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "602.14", null, "141", "128", "141", "0", "0"], ["46", "AdaptiveCpp/AdaptiveCpp", "507", "2.0", "3.0", "6.0", "-0.1783216783216783", "1.4166666666666667", "-0.5108256237659912", "0.7121483581398508", "0.3265854170022765", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "98", "9", "0", "True", "0", "1", "0", "442", "15", "506", "1.0986122886681096", "6.093569770045136", "2.772588722239781", "6.228511003591183", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "301.78037037037035", null, "144", "131", "144", "0", "0"], ["47", "AdaptiveCpp/AdaptiveCpp", "508", "0.0", "3.0", "6.0", "-0.1153846153846153", "1.0833333333333333", "-1.609437912434101", "0.371425890455925", "0.1488615992206787", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "99", "10", "0", "True", "0", "1", "0", "443", "16", "513", "0.0", "6.095824562432225", "2.833213344056216", "6.242223265455165", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "128.98805555555555", null, "145", "132", "145", "0", "0"], ["48", "AdaptiveCpp/AdaptiveCpp", "509", "1.0", "3.0", "6.0", "-0.0769230769230769", "1.0", "-0.4054651081081649", "0.6444049826448044", "0.3999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "100", "11", "0", "True", "0", "1", "0", "444", "16", "520", "0.6931471805599453", "6.09807428216624", "2.833213344056216", "6.255750041753367", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "142.8438888888889", null, "147", "133", "147", "0", "0"], ["49", "AdaptiveCpp/AdaptiveCpp", "510", "2.0", "3.0", "6.0", "0.0069930069930069", "1.0", "-5.551115123125785e-16", "0.7310585786300048", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "101", "12", "0", "True", "0", "1", "0", "446", "16", "527", "1.0986122886681096", "6.102558594613568", "2.833213344056216", "6.269096283706261", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "40.95861111111111", null, "149", "134", "149", "0", "0"]], "shape": {"columns": 50, "rows": 203912}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>pr_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>time_to_first_comment</th>\n", "      <th>number_of_prs</th>\n", "      <th>number_of_merged_prs</th>\n", "      <th>number_of_closed_prs</th>\n", "      <th>number_of_open_prs</th>\n", "      <th>number_of_pr_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>486</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.069930</td>\n", "      <td>0.666667</td>\n", "      <td>-6.931472e-01</td>\n", "      <td>0.493380</td>\n", "      <td>0.386488</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>23</td>\n", "      <td>21</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.034965</td>\n", "      <td>0.500000</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.354661</td>\n", "      <td>0.366025</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>23</td>\n", "      <td>21</td>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>488</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.122378</td>\n", "      <td>0.750000</td>\n", "      <td>9.162907e-01</td>\n", "      <td>0.841081</td>\n", "      <td>0.665348</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>0.800000</td>\n", "      <td>11.765278</td>\n", "      <td>None</td>\n", "      <td>28</td>\n", "      <td>25</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>489</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.097902</td>\n", "      <td>0.833333</td>\n", "      <td>6.931472e-01</td>\n", "      <td>0.821491</td>\n", "      <td>0.640520</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>25.218611</td>\n", "      <td>None</td>\n", "      <td>29</td>\n", "      <td>26</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>490</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.143357</td>\n", "      <td>1.083333</td>\n", "      <td>1.386294e+00</td>\n", "      <td>0.921985</td>\n", "      <td>0.817846</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>33.390208</td>\n", "      <td>None</td>\n", "      <td>33</td>\n", "      <td>30</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203907</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.388112</td>\n", "      <td>3.416667</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.910361</td>\n", "      <td>0.022897</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>1.000000</td>\n", "      <td>116.207315</td>\n", "      <td>None</td>\n", "      <td>444</td>\n", "      <td>373</td>\n", "      <td>442</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203908</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.038462</td>\n", "      <td>2.416667</td>\n", "      <td>-1.609438e+00</td>\n", "      <td>0.691519</td>\n", "      <td>0.020046</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>1.000000</td>\n", "      <td>171.439583</td>\n", "      <td>None</td>\n", "      <td>446</td>\n", "      <td>375</td>\n", "      <td>444</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203909</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.402098</td>\n", "      <td>2.916667</td>\n", "      <td>7.884574e-01</td>\n", "      <td>0.975993</td>\n", "      <td>0.908849</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.777778</td>\n", "      <td>16.118135</td>\n", "      <td>None</td>\n", "      <td>455</td>\n", "      <td>382</td>\n", "      <td>453</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203910</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.325175</td>\n", "      <td>2.916667</td>\n", "      <td>-6.661338e-16</td>\n", "      <td>0.948664</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.800000</td>\n", "      <td>361.275417</td>\n", "      <td>None</td>\n", "      <td>460</td>\n", "      <td>386</td>\n", "      <td>458</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203911</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>563</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.234266</td>\n", "      <td>3.250000</td>\n", "      <td>1.609438e+00</td>\n", "      <td>0.992305</td>\n", "      <td>0.994679</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203912 rows × 50 columns</p>\n", "</div>"], "text/plain": ["                         repo_name standardized_time_weeks  pr_throughput  \\\n", "0       10up/autoshare-for-twitter                     486            0.0   \n", "1       10up/autoshare-for-twitter                     487            0.0   \n", "2       10up/autoshare-for-twitter                     488            4.0   \n", "3       10up/autoshare-for-twitter                     489            1.0   \n", "4       10up/autoshare-for-twitter                     490            3.0   \n", "...                            ...                     ...            ...   \n", "203907     tree-sitter/tree-sitter                     559            1.0   \n", "203908     tree-sitter/tree-sitter                     560            2.0   \n", "203909     tree-sitter/tree-sitter                     561           10.0   \n", "203910     tree-sitter/tree-sitter                     562            2.0   \n", "203911     tree-sitter/tree-sitter                     563            4.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "0                       1.0                 2.0      -0.069930      0.666667   \n", "1                       1.0                 2.0      -0.034965      0.500000   \n", "2                       1.0                 2.0       0.122378      0.750000   \n", "3                       1.0                 2.0       0.097902      0.833333   \n", "4                       1.0                 2.0       0.143357      1.083333   \n", "...                     ...                 ...            ...           ...   \n", "203907                  1.0                13.0      -0.388112      3.416667   \n", "203908                  1.0                13.0       0.038462      2.416667   \n", "203909                  1.0                13.0       0.402098      2.916667   \n", "203910                  1.0                13.0       0.325175      2.916667   \n", "203911                  1.0                13.0       0.234266      3.250000   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "0                -6.931472e-01            0.493380                 0.386488   \n", "1                -1.098612e+00            0.354661                 0.366025   \n", "2                 9.162907e-01            0.841081                 0.665348   \n", "3                 6.931472e-01            0.821491                 0.640520   \n", "4                 1.386294e+00            0.921985                 0.817846   \n", "...                        ...                 ...                      ...   \n", "203907           -1.098612e+00            0.910361                 0.022897   \n", "203908           -1.609438e+00            0.691519                 0.020046   \n", "203909            7.884574e-01            0.975993                 0.908849   \n", "203910           -6.661338e-16            0.948664                 0.500000   \n", "203911            1.609438e+00            0.992305                 0.994679   \n", "\n", "        ...  log_project_age_before_treatment  project_main_language  \\\n", "0       ...                          6.056784                    PHP   \n", "1       ...                          6.056784                    PHP   \n", "2       ...                          6.056784                    PHP   \n", "3       ...                          6.056784                    PHP   \n", "4       ...                          6.056784                    PHP   \n", "...     ...                               ...                    ...   \n", "203907  ...                          5.966147                     Go   \n", "203908  ...                          5.966147                     Go   \n", "203909  ...                          5.966147                     Go   \n", "203910  ...                          5.966147                     Go   \n", "203911  ...                          5.966147                     Go   \n", "\n", "        pr_success_rate  time_to_merge  time_to_first_comment  number_of_prs  \\\n", "0                   NaN            NaN                   None             23   \n", "1                   NaN            NaN                   None             23   \n", "2              0.800000      11.765278                   None             28   \n", "3              1.000000      25.218611                   None             29   \n", "4              1.000000      33.390208                   None             33   \n", "...                 ...            ...                    ...            ...   \n", "203907         1.000000     116.207315                   None            444   \n", "203908         1.000000     171.439583                   None            446   \n", "203909         0.777778      16.118135                   None            455   \n", "203910         0.800000     361.275417                   None            460   \n", "203911              NaN            NaN                    NaN            NaN   \n", "\n", "       number_of_merged_prs  number_of_closed_prs  number_of_open_prs  \\\n", "0                        21                    23                   0   \n", "1                        21                    23                   0   \n", "2                        25                    28                   0   \n", "3                        26                    29                   0   \n", "4                        30                    33                   0   \n", "...                     ...                   ...                 ...   \n", "203907                  373                   442                   2   \n", "203908                  375                   444                   2   \n", "203909                  382                   453                   2   \n", "203910                  386                   458                   2   \n", "203911                  NaN                   NaN                 NaN   \n", "\n", "        number_of_pr_comments  \n", "0                           0  \n", "1                           0  \n", "2                           0  \n", "3                           0  \n", "4                           0  \n", "...                       ...  \n", "203907                      0  \n", "203908                      0  \n", "203909                      0  \n", "203910                      0  \n", "203911                    NaN  \n", "\n", "[203912 rows x 50 columns]"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["# merge the data with compiled_data\n", "compiled_data = compiled_data.merge(add_outcome_values, on=['repo_name', 'standardized_time_weeks'], how='left')\n", "compiled_data"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "object", "type": "unknown"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "pr_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "time_to_first_comment", "rawType": "object", "type": "unknown"}, {"name": "number_of_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_merged_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_closed_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_open_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_pr_comments", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "189e8c2a-2e6e-4799-9f22-7b900a97ae1a", "rows": [["2", "10up/autoshare-for-twitter", "488", "4.0", "1.0", "2.0", "0.1223776223776223", "0.75", "0.9162907318741548", "0.8410806526182262", "0.6653477824119316", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "51", "-10", "1", "False", "0", "0", "0", "209", "5", "356", "1.6094379124341005", "5.3471075307174685", "1.791759469228055", "5.877735781779639", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.8", "11.76527777777778", null, "28", "25", "28", "0", "0"], ["3", "10up/autoshare-for-twitter", "489", "1.0", "1.0", "2.0", "0.0979020979020979", "0.8333333333333334", "0.6931471805599448", "0.8214907876837801", "0.6405201949797534", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "52", "-9", "1", "False", "0", "0", "0", "210", "5", "363", "0.6931471805599453", "5.351858133476067", "1.791759469228055", "5.8971538676367405", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "25.218611111111112", null, "29", "26", "29", "0", "0"], ["4", "10up/autoshare-for-twitter", "490", "3.0", "1.0", "2.0", "0.1433566433566433", "1.0833333333333333", "1.38629436111989", "0.921984989635266", "0.8178456006794831", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "53", "-8", "1", "False", "0", "0", "0", "220", "5", "370", "1.3862943611198906", "5.3981627015177525", "1.791759469228055", "5.916202062607435", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "33.390208333333334", null, "33", "30", "33", "0", "0"], ["5", "10up/autoshare-for-twitter", "491", "3.0", "1.0", "2.0", "0.2587412587412587", "1.1666666666666667", "0.2876820724517805", "0.8106668070686921", "0.5831283861446792", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "54", "-7", "1", "False", "0", "0", "0", "249", "6", "377", "1.3862943611198906", "5.521460917862246", "1.9459101490553128", "5.934894195619588", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "251.36064814814813", null, "36", "33", "36", "0", "0"], ["7", "10up/autoshare-for-twitter", "493", "0.0", "1.0", "2.0", "0.0629370629370629", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "56", "-5", "1", "False", "0", "0", "0", "259", "6", "391", "0.0", "5.560681631015528", "1.9459101490553128", "5.971261839790462", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "148.22722222222222", null, "37", "34", "37", "0", "0"], ["9", "10up/autoshare-for-twitter", "495", "1.0", "1.0", "2.0", "0.0279720279720279", "1.1666666666666667", "-0.4054651081081645", "0.6816145482921148", "0.3838963480989594", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "58", "-3", "1", "False", "0", "0", "0", "275", "6", "405", "0.6931471805599453", "5.62040086571715", "1.9459101490553128", "6.0063531596017325", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.6666666666666666", "247.32555555555555", null, "40", "36", "40", "0", "0"], ["10", "10up/autoshare-for-twitter", "496", "0.0", "1.0", "2.0", "-0.0244755244755244", "1.0833333333333333", "-0.6931471805599454", "0.5963275109839462", "0.3206231694796373", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "59", "-2", "1", "False", "0", "0", "0", "306", "7", "412", "0.0", "5.726847747587197", "2.079441541679836", "6.023447592961032", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "73.91361111111111", null, "42", "37", "42", "0", "0"], ["11", "10up/autoshare-for-twitter", "497", "5.0", "1.0", "2.0", "0.0769230769230769", "1.5", "1.791759469228055", "0.964145027597638", "0.9362933095037254", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "60", "-1", "1", "False", "0", "0", "0", "309", "7", "419", "1.791759469228055", "5.736572297479192", "2.079441541679836", "6.040254711277414", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "4.687083333333333", null, "44", "39", "44", "0", "0"], ["12", "10up/autoshare-for-twitter", "498", "2.0", "1.0", "2.0", "0.0279720279720279", "1.6666666666666667", "1.0986122886681096", "0.9407704701088077", "0.8618832502903921", "1", "257.0", "0.2171837708830549", "182.0", "7.0", "1.0", "PHP", "437", "61", "0", "1", "False", "0", "0", "0", "336", "8", "426", "1.0986122886681096", "5.820082930352362", "2.197224577336219", "6.056784013228624", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "24.94768518518519", null, "47", "42", "47", "0", "0"], ["14", "10up/autoshare-for-twitter", "500", "1.0", "1.0", "2.0", "0.0", "1.5", "-0.9162907318741552", "0.6419204615368317", "0.2019040735186648", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "63", "2", "1", "True", "0", "1", "1", "337", "8", "440", "0.6931471805599453", "5.823045895483019", "2.197224577336219", "6.089044875446846", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "12.485277777777778", null, "48", "43", "48", "0", "0"], ["26", "AdaptiveCpp/AdaptiveCpp", "487", "1.0", "3.0", "6.0", "-0.0664335664335664", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "78", "-11", "0", "False", "0", "0", "0", "388", "9", "366", "0.6931471805599453", "5.963579343618446", "2.302585092994045", "5.90536184805457", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "455.39222222222224", null, "109", "101", "109", "0", "0"], ["27", "AdaptiveCpp/AdaptiveCpp", "488", "0.0", "3.0", "6.0", "-0.0524475524475524", "0.75", "-1.09861228866811", "0.4137189778658776", "0.3049238750315606", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "79", "-10", "0", "False", "0", "0", "0", "389", "9", "373", "0.0", "5.966146739123692", "2.302585092994045", "5.924255797414531", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.3333333333333333", "1264.34", null, "112", "102", "112", "0", "0"], ["29", "AdaptiveCpp/AdaptiveCpp", "490", "2.0", "3.0", "6.0", "-0.0489510489510489", "0.8333333333333334", "1.0986122886681091", "0.8734646144545724", "0.7141264037070777", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "81", "-8", "0", "False", "0", "0", "0", "391", "9", "387", "1.0986122886681096", "5.971261839790462", "2.302585092994045", "5.961005339623274", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "15.10361111111111", null, "113", "103", "113", "0", "0"], ["30", "AdaptiveCpp/AdaptiveCpp", "491", "2.0", "3.0", "6.0", "0.0489510489510489", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "82", "-7", "0", "False", "0", "0", "0", "394", "10", "394", "1.0986122886681096", "5.978885764901122", "2.3978952727983707", "5.978885764901122", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "25.80388888888889", null, "116", "106", "116", "0", "0"], ["32", "AdaptiveCpp/AdaptiveCpp", "493", "1.0", "3.0", "6.0", "0.0314685314685314", "0.9166666666666666", "0.6931471805599448", "0.8333855399562498", "0.6537094706869915", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "84", "-5", "0", "False", "0", "0", "0", "396", "11", "408", "0.6931471805599453", "5.98393628068719", "2.4849066497880004", "6.013715156042801", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "158.70263888888888", null, "118", "108", "118", "0", "0"], ["33", "AdaptiveCpp/AdaptiveCpp", "494", "1.0", "3.0", "6.0", "0.0384615384615384", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "85", "-4", "0", "False", "0", "0", "0", "400", "11", "415", "0.6931471805599453", "5.993961427306569", "2.4849066497880004", "6.030685260261263", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "54.522777777777776", null, "121", "111", "121", "0", "0"], ["34", "AdaptiveCpp/AdaptiveCpp", "495", "4.0", "3.0", "6.0", "0.1608391608391608", "1.1666666666666667", "0.9162907318741546", "0.889235659522009", "0.7444078107515406", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "86", "-3", "0", "False", "0", "0", "0", "411", "12", "422", "1.6094379124341005", "6.021023349349526", "2.5649493574615367", "6.0473721790462776", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "86.71481481481482", null, "124", "114", "124", "0", "0"], ["35", "AdaptiveCpp/AdaptiveCpp", "496", "4.0", "3.0", "6.0", "0.3076923076923077", "1.3333333333333333", "0.5108256237659902", "0.8634398378798575", "0.6639843473284657", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "87", "-2", "0", "False", "0", "0", "0", "419", "13", "429", "1.6094379124341005", "6.040254711277414", "2.6390573296152584", "6.063785208687608", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "747.0770138888889", null, "128", "118", "128", "0", "0"], ["36", "AdaptiveCpp/AdaptiveCpp", "497", "2.0", "3.0", "6.0", "0.2727272727272727", "1.5", "1.0986122886681091", "0.9307722154980688", "0.8386095222035911", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "88", "-1", "0", "False", "0", "0", "0", "423", "13", "436", "1.0986122886681096", "6.049733455231958", "2.6390573296152584", "6.07993319509559", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.75", "262.4032407407407", null, "132", "121", "132", "0", "0"], ["37", "AdaptiveCpp/AdaptiveCpp", "498", "2.0", "3.0", "6.0", "0.2237762237762237", "1.6666666666666667", "1.0986122886681091", "0.9407704701088077", "0.861883250290392", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "89", "0", "0", "False", "0", "0", "0", "427", "13", "443", "1.0986122886681096", "6.059123195581797", "2.6390573296152584", "6.095824562432225", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "913.2144444444444", null, "133", "122", "133", "0", "0"], ["38", "AdaptiveCpp/AdaptiveCpp", "499", "0.0", "3.0", "6.0", "0.1293706293706293", "1.5833333333333333", "-0.693147180559946", "0.7089285864699496", "0.2502117946664587", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "90", "1", "0", "True", "0", "1", "0", "430", "13", "450", "0.0", "6.066108090103747", "2.6390573296152584", "6.111467339502678", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "382.2038888888889", null, "134", "123", "134", "0", "0"], ["39", "AdaptiveCpp/AdaptiveCpp", "500", "0.0", "3.0", "6.0", "-0.0034965034965034", "1.5833333333333333", "-6.661338147750939e-16", "0.829676081356154", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "91", "2", "0", "True", "0", "1", "0", "431", "13", "457", "0.0", "6.06842558824411", "2.6390573296152584", "6.1268691841141845", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "158.27305555555554", null, "135", "124", "135", "0", "0"], ["41", "AdaptiveCpp/AdaptiveCpp", "502", "3.0", "3.0", "6.0", "0.0314685314685314", "1.9166666666666667", "0.2876820724517803", "0.9006393499233735", "0.634458169632737", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "93", "4", "0", "True", "0", "1", "0", "437", "13", "471", "1.3862943611198906", "6.0822189103764455", "2.6390573296152584", "6.156978985585555", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "31.636203703703703", null, "138", "127", "138", "0", "0"], ["45", "AdaptiveCpp/AdaptiveCpp", "506", "0.0", "3.0", "6.0", "-0.3041958041958042", "1.5833333333333333", "-0.6931471805599457", "0.7089285864699497", "0.2502117946664588", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "97", "8", "0", "True", "0", "1", "0", "439", "14", "499", "0.0", "6.0867747269123065", "2.70805020110221", "6.214608098422191", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "602.14", null, "141", "128", "141", "0", "0"], ["46", "AdaptiveCpp/AdaptiveCpp", "507", "2.0", "3.0", "6.0", "-0.1783216783216783", "1.4166666666666667", "-0.5108256237659912", "0.7121483581398508", "0.3265854170022765", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "98", "9", "0", "True", "0", "1", "0", "442", "15", "506", "1.0986122886681096", "6.093569770045136", "2.772588722239781", "6.228511003591183", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "301.78037037037035", null, "144", "131", "144", "0", "0"], ["47", "AdaptiveCpp/AdaptiveCpp", "508", "0.0", "3.0", "6.0", "-0.1153846153846153", "1.0833333333333333", "-1.609437912434101", "0.371425890455925", "0.1488615992206787", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "99", "10", "0", "True", "0", "1", "0", "443", "16", "513", "0.0", "6.095824562432225", "2.833213344056216", "6.242223265455165", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "1.0", "128.98805555555555", null, "145", "132", "145", "0", "0"], ["48", "AdaptiveCpp/AdaptiveCpp", "509", "1.0", "3.0", "6.0", "-0.0769230769230769", "1.0", "-0.4054651081081649", "0.6444049826448044", "0.3999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "100", "11", "0", "True", "0", "1", "0", "444", "16", "520", "0.6931471805599453", "6.09807428216624", "2.833213344056216", "6.255750041753367", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "142.8438888888889", null, "147", "133", "147", "0", "0"], ["49", "AdaptiveCpp/AdaptiveCpp", "510", "2.0", "3.0", "6.0", "0.0069930069930069", "1.0", "-5.551115123125785e-16", "0.7310585786300048", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "101", "12", "0", "True", "0", "1", "0", "446", "16", "527", "1.0986122886681096", "6.102558594613568", "2.833213344056216", "6.269096283706261", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.5", "40.95861111111111", null, "149", "134", "149", "0", "0"], ["50", "18F/analytics.usa.gov", "226", "6.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "1", "-2", "1", "False", "1", "0", "0", "54", "5", "4", "1.9459101490553128", "4.007333185232471", "1.791759469228055", "1.6094379124341005", "0_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "14.663968253968253", null, "7", "7", "7", "0", "0"], ["51", "18F/analytics.usa.gov", "227", "31.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "2", "-1", "1", "False", "1", "0", "0", "187", "7", "11", "3.4657359027997265", "5.236441962829949", "2.079441541679836", "2.4849066497880004", "0_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.9696969696969697", "2.269592013888889", null, "40", "39", "40", "0", "0"], ["52", "18F/analytics.usa.gov", "228", "13.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "1", "15.0", "0.0284396617986164", "37.0", "16.0", "1.0", "JavaScript", "225", "3", "0", "1", "False", "1", "0", "0", "217", "8", "18", "2.6390573296152584", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "0_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "7.8372222222222225", null, "51", "50", "51", "0", "0"], ["53", "18F/analytics.usa.gov", "229", "4.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "4", "1", "1", "True", "1", "1", "1", "222", "8", "25", "1.6094379124341005", "5.407171771460119", "2.197224577336219", "3.2580965380214817", "1_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "34.09013888888889", null, "55", "54", "55", "0", "0"], ["60", "18F/analytics.usa.gov", "236", "6.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "11", "8", "1", "True", "1", "1", "1", "240", "11", "74", "1.9459101490553128", "5.484796933490655", "2.4849066497880004", "4.31748811353631", "1_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.6", "0.12050925925925926", null, "67", "60", "67", "0", "0"], ["61", "18F/analytics.usa.gov", "237", "4.0", "6.0", "6.0", "-1.2517482517482517", "5.333333333333333", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "12", "9", "1", "True", "1", "1", "1", "254", "12", "81", "1.6094379124341005", "5.541263545158426", "2.5649493574615367", "4.406719247264253", "1_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.8", "4.2884722222222225", null, "72", "64", "72", "0", "0"], ["65", "KSP-RO/RP-1", "219", "9.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "2", "-11", "0", "False", "1", "0", "0", "115", "3", "6", "2.302585092994045", "4.7535901911063645", "1.3862943611198906", "1.9459101490553128", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "2.543827160493827", null, "9", "9", "9", "0", "0"], ["66", "KSP-RO/RP-1", "220", "1.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "3", "-10", "0", "False", "1", "0", "0", "161", "4", "13", "0.6931471805599453", "5.087596335232384", "1.6094379124341005", "2.6390573296152584", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "46.48374999999999", null, "11", "11", "11", "0", "0"], ["67", "KSP-RO/RP-1", "221", "1.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "4", "-9", "0", "False", "1", "0", "0", "182", "4", "20", "0.6931471805599453", "5.2094861528414205", "1.6094379124341005", "3.044522437723423", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "375.96527777777777", null, "12", "12", "12", "0", "0"], ["69", "KSP-RO/RP-1", "223", "2.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "6", "-7", "0", "False", "1", "0", "0", "232", "4", "34", "1.0986122886681096", "5.4510384535657", "1.6094379124341005", "3.555348061489413", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "0.07083333333333333", null, "13", "13", "13", "0", "0"], ["72", "KSP-RO/RP-1", "226", "1.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "9", "-4", "0", "False", "1", "0", "0", "254", "5", "55", "0.6931471805599453", "5.541263545158426", "1.791759469228055", "4.025351690735149", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "1.4913888888888889", null, "14", "14", "14", "0", "0"], ["74", "KSP-RO/RP-1", "228", "4.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "11", "-2", "0", "False", "1", "0", "0", "266", "6", "69", "1.6094379124341005", "5.58724865840025", "1.9459101490553128", "4.248495242049359", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.8", "7.0211805555555555", null, "19", "18", "19", "0", "0"], ["75", "KSP-RO/RP-1", "229", "0.0", "9.0", "3.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "12", "-1", "0", "False", "1", "0", "0", "280", "7", "76", "0.0", "5.638354669333745", "2.079441541679836", "4.343805421853684", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.25", "181.44305555555556", null, "23", "19", "23", "0", "0"], ["76", "KSP-RO/RP-1", "230", "3.0", "9.0", "3.0", "-0.1993006993006993", "1.75", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "13", "0", "0", "False", "1", "0", "0", "287", "7", "83", "1.3862943611198906", "5.662960480135946", "2.079441541679836", "4.430816798843313", "0_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.5", "55.92402777777778", null, "27", "21", "27", "0", "0"], ["77", "KSP-RO/RP-1", "231", "4.0", "9.0", "3.0", "0.2167832167832167", "1.3333333333333333", "-0.6931471805599454", "0.6547955394827877", "0.2841036534166501", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "14", "1", "0", "True", "1", "1", "0", "321", "8", "90", "1.6094379124341005", "5.7745515455444085", "2.197224577336219", "4.51085950651685", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.5", "6.526736111111112", null, "35", "25", "35", "0", "0"], ["78", "KSP-RO/RP-1", "232", "2.0", "9.0", "3.0", "0.2272727272727272", "1.4166666666666667", "0.4054651081081641", "0.8608217265398879", "0.6397798828803531", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "15", "2", "0", "True", "1", "1", "0", "329", "8", "97", "1.0986122886681096", "5.799092654460526", "2.197224577336219", "4.584967478670571", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "57.511759259259264", null, "38", "28", "38", "0", "0"], ["79", "KSP-RO/RP-1", "233", "5.0", "9.0", "3.0", "0.3461538461538461", "1.75", "1.0986122886681096", "0.945246905877133", "0.8724249155928818", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "16", "3", "0", "True", "1", "1", "0", "355", "8", "104", "1.791759469228055", "5.87493073085203", "2.197224577336219", "4.653960350157523", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "18.284236111111113", null, "42", "32", "42", "0", "0"], ["80", "KSP-RO/RP-1", "234", "3.0", "9.0", "3.0", "0.3146853146853147", "2.0", "1.3862943611198904", "0.967273443634614", "0.9411764705882352", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "17", "4", "0", "True", "1", "1", "0", "366", "8", "111", "1.3862943611198906", "5.90536184805457", "2.197224577336219", "4.718498871295094", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "20.133472222222224", null, "46", "36", "46", "0", "0"], ["81", "KSP-RO/RP-1", "235", "10.0", "9.0", "3.0", "0.6223776223776224", "2.6666666666666665", "1.2992829841302609", "0.9814023930970812", "0.9696681217746398", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "18", "5", "0", "True", "1", "1", "0", "391", "8", "118", "2.3978952727983707", "5.971261839790462", "2.197224577336219", "4.77912349311153", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.9", "15.255123456790123", null, "56", "45", "56", "0", "0"], ["82", "KSP-RO/RP-1", "236", "6.0", "9.0", "3.0", "0.6293706293706294", "3.1666666666666665", "1.9459101490553128", "0.9940154810174574", "0.9978965039166016", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "19", "6", "0", "True", "1", "1", "0", "420", "9", "125", "1.9459101490553128", "6.042632833682381", "2.302585092994045", "4.836281906951478", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.8", "55.967326388888885", null, "66", "53", "66", "0", "0"], ["83", "KSP-RO/RP-1", "237", "1.0", "9.0", "3.0", "0.4020979020979021", "3.25", "0.6931471805599452", "0.9809816076332656", "0.9048856161460204", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "20", "7", "0", "True", "1", "1", "0", "435", "10", "132", "0.6931471805599453", "6.077642243349034", "2.3978952727983707", "4.890349128221754", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "82.0175", null, "67", "54", "67", "0", "0"], ["85", "KSP-RO/RP-1", "239", "3.0", "9.0", "3.0", "0.0874125874125874", "3.583333333333333", "1.3862943611198904", "0.9931021769520114", "0.9930879625559244", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "C#", "217", "22", "9", "0", "True", "1", "1", "0", "462", "12", "146", "1.3862943611198906", "6.137727054086234", "2.5649493574615367", "4.990432586778736", "1_1", "0_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "1.0", "3.241481481481482", null, "70", "57", "70", "0", "0"]], "shape": {"columns": 50, "rows": 99560}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>pr_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>time_to_first_comment</th>\n", "      <th>number_of_prs</th>\n", "      <th>number_of_merged_prs</th>\n", "      <th>number_of_closed_prs</th>\n", "      <th>number_of_open_prs</th>\n", "      <th>number_of_pr_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>488</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.122378</td>\n", "      <td>0.750000</td>\n", "      <td>9.162907e-01</td>\n", "      <td>0.841081</td>\n", "      <td>0.665348</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>0.800000</td>\n", "      <td>11.765278</td>\n", "      <td>None</td>\n", "      <td>28</td>\n", "      <td>25</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>489</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.097902</td>\n", "      <td>0.833333</td>\n", "      <td>6.931472e-01</td>\n", "      <td>0.821491</td>\n", "      <td>0.640520</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>25.218611</td>\n", "      <td>None</td>\n", "      <td>29</td>\n", "      <td>26</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>490</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.143357</td>\n", "      <td>1.083333</td>\n", "      <td>1.386294e+00</td>\n", "      <td>0.921985</td>\n", "      <td>0.817846</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>33.390208</td>\n", "      <td>None</td>\n", "      <td>33</td>\n", "      <td>30</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>491</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.258741</td>\n", "      <td>1.166667</td>\n", "      <td>2.876821e-01</td>\n", "      <td>0.810667</td>\n", "      <td>0.583128</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>251.360648</td>\n", "      <td>None</td>\n", "      <td>36</td>\n", "      <td>33</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>493</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.062937</td>\n", "      <td>1.166667</td>\n", "      <td>-2.220446e-16</td>\n", "      <td>0.762542</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>1.000000</td>\n", "      <td>148.227222</td>\n", "      <td>None</td>\n", "      <td>37</td>\n", "      <td>34</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203906</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>558</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.339161</td>\n", "      <td>3.750000</td>\n", "      <td>-1.335314e-01</td>\n", "      <td>0.973826</td>\n", "      <td>0.377366</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.888889</td>\n", "      <td>226.978576</td>\n", "      <td>None</td>\n", "      <td>441</td>\n", "      <td>370</td>\n", "      <td>439</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203907</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.388112</td>\n", "      <td>3.416667</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.910361</td>\n", "      <td>0.022897</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>1.000000</td>\n", "      <td>116.207315</td>\n", "      <td>None</td>\n", "      <td>444</td>\n", "      <td>373</td>\n", "      <td>442</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203908</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.038462</td>\n", "      <td>2.416667</td>\n", "      <td>-1.609438e+00</td>\n", "      <td>0.691519</td>\n", "      <td>0.020046</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>1.000000</td>\n", "      <td>171.439583</td>\n", "      <td>None</td>\n", "      <td>446</td>\n", "      <td>375</td>\n", "      <td>444</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203909</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.402098</td>\n", "      <td>2.916667</td>\n", "      <td>7.884574e-01</td>\n", "      <td>0.975993</td>\n", "      <td>0.908849</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.777778</td>\n", "      <td>16.118135</td>\n", "      <td>None</td>\n", "      <td>455</td>\n", "      <td>382</td>\n", "      <td>453</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203910</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.325175</td>\n", "      <td>2.916667</td>\n", "      <td>-6.661338e-16</td>\n", "      <td>0.948664</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.800000</td>\n", "      <td>361.275417</td>\n", "      <td>None</td>\n", "      <td>460</td>\n", "      <td>386</td>\n", "      <td>458</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>99560 rows × 50 columns</p>\n", "</div>"], "text/plain": ["                         repo_name standardized_time_weeks  pr_throughput  \\\n", "2       10up/autoshare-for-twitter                     488            4.0   \n", "3       10up/autoshare-for-twitter                     489            1.0   \n", "4       10up/autoshare-for-twitter                     490            3.0   \n", "5       10up/autoshare-for-twitter                     491            3.0   \n", "7       10up/autoshare-for-twitter                     493            0.0   \n", "...                            ...                     ...            ...   \n", "203906     tree-sitter/tree-sitter                     558            6.0   \n", "203907     tree-sitter/tree-sitter                     559            1.0   \n", "203908     tree-sitter/tree-sitter                     560            2.0   \n", "203909     tree-sitter/tree-sitter                     561           10.0   \n", "203910     tree-sitter/tree-sitter                     562            2.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "2                       1.0                 2.0       0.122378      0.750000   \n", "3                       1.0                 2.0       0.097902      0.833333   \n", "4                       1.0                 2.0       0.143357      1.083333   \n", "5                       1.0                 2.0       0.258741      1.166667   \n", "7                       1.0                 2.0       0.062937      1.166667   \n", "...                     ...                 ...            ...           ...   \n", "203906                  1.0                13.0      -0.339161      3.750000   \n", "203907                  1.0                13.0      -0.388112      3.416667   \n", "203908                  1.0                13.0       0.038462      2.416667   \n", "203909                  1.0                13.0       0.402098      2.916667   \n", "203910                  1.0                13.0       0.325175      2.916667   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "2                 9.162907e-01            0.841081                 0.665348   \n", "3                 6.931472e-01            0.821491                 0.640520   \n", "4                 1.386294e+00            0.921985                 0.817846   \n", "5                 2.876821e-01            0.810667                 0.583128   \n", "7                -2.220446e-16            0.762542                 0.500000   \n", "...                        ...                 ...                      ...   \n", "203906           -1.335314e-01            0.973826                 0.377366   \n", "203907           -1.098612e+00            0.910361                 0.022897   \n", "203908           -1.609438e+00            0.691519                 0.020046   \n", "203909            7.884574e-01            0.975993                 0.908849   \n", "203910           -6.661338e-16            0.948664                 0.500000   \n", "\n", "        ...  log_project_age_before_treatment  project_main_language  \\\n", "2       ...                          6.056784                    PHP   \n", "3       ...                          6.056784                    PHP   \n", "4       ...                          6.056784                    PHP   \n", "5       ...                          6.056784                    PHP   \n", "7       ...                          6.056784                    PHP   \n", "...     ...                               ...                    ...   \n", "203906  ...                          5.966147                     Go   \n", "203907  ...                          5.966147                     Go   \n", "203908  ...                          5.966147                     Go   \n", "203909  ...                          5.966147                     Go   \n", "203910  ...                          5.966147                     Go   \n", "\n", "        pr_success_rate  time_to_merge  time_to_first_comment  number_of_prs  \\\n", "2              0.800000      11.765278                   None             28   \n", "3              1.000000      25.218611                   None             29   \n", "4              1.000000      33.390208                   None             33   \n", "5              1.000000     251.360648                   None             36   \n", "7              1.000000     148.227222                   None             37   \n", "...                 ...            ...                    ...            ...   \n", "203906         0.888889     226.978576                   None            441   \n", "203907         1.000000     116.207315                   None            444   \n", "203908         1.000000     171.439583                   None            446   \n", "203909         0.777778      16.118135                   None            455   \n", "203910         0.800000     361.275417                   None            460   \n", "\n", "       number_of_merged_prs  number_of_closed_prs  number_of_open_prs  \\\n", "2                        25                    28                   0   \n", "3                        26                    29                   0   \n", "4                        30                    33                   0   \n", "5                        33                    36                   0   \n", "7                        34                    37                   0   \n", "...                     ...                   ...                 ...   \n", "203906                  370                   439                   2   \n", "203907                  373                   442                   2   \n", "203908                  375                   444                   2   \n", "203909                  382                   453                   2   \n", "203910                  386                   458                   2   \n", "\n", "        number_of_pr_comments  \n", "2                           0  \n", "3                           0  \n", "4                           0  \n", "5                           0  \n", "7                           0  \n", "...                       ...  \n", "203906                      0  \n", "203907                      0  \n", "203908                      0  \n", "203909                      0  \n", "203910                      0  \n", "\n", "[99560 rows x 50 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data_time_to_merge = compiled_data.copy()\n", "# exclude data with time_to_merge is None\n", "compiled_data_time_to_merge = compiled_data_time_to_merge.dropna(subset=['time_to_merge'])\n", "compiled_data_time_to_merge"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["compiled_data_time_to_merge['log_time_to_merge'] = np.log(compiled_data_time_to_merge['time_to_merge'] + 1)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["compiled_data_time_to_merge.to_csv(\"../result/did_result_20250212/compiled_data_test_with_features_outcome_time_to_merge.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["compiled_data_success_rate = compiled_data.copy()\n", "# exclude data with pr_success_rate is None\n", "compiled_data_success_rate = compiled_data_success_rate.dropna(subset=['pr_success_rate'])\n", "compiled_data_success_rate\n", "compiled_data_success_rate.to_csv(\"../result/did_result_20250212/compiled_data_test_with_features_outcome_success_rate.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["compiled_data_success_rate['log_pr_success_rate'] = np.log(compiled_data_success_rate['pr_success_rate'] + 1)\n", "compiled_data_success_rate.to_csv(\"../result/did_result_20250212/compiled_data_test_with_features_outcome_success_rate.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "object", "type": "unknown"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "pr_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "time_to_first_comment", "rawType": "object", "type": "unknown"}, {"name": "number_of_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_merged_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_closed_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_open_prs", "rawType": "object", "type": "unknown"}, {"name": "number_of_pr_comments", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "15db3362-e15e-4062-980a-c8403552f3cf", "rows": [["42", "AdaptiveCpp/AdaptiveCpp", "503", "1.0", "3.0", "6.0", "0.0", "1.8333333333333333", "-0.4054651081081649", "0.8065689432388163", "0.3222714364606489", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "94", "5", "0", "True", "0", "1", "0", "437", "13", "478", "0.6931471805599453", "6.0822189103764455", "2.6390573296152584", "6.171700597410915", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "0.0", null, null, "139", "127", "139", "0", "0"], ["54", "18F/analytics.usa.gov", "230", "0.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "5", "2", "1", "True", "1", "1", "1", "222", "8", "32", "0.0", "5.407171771460119", "2.197224577336219", "3.49650756146648", "1_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.0", null, null, "56", "54", "56", "0", "0"], ["57", "18F/analytics.usa.gov", "233", "0.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0", "15.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "8", "5", "1", "True", "1", "1", "1", "222", "8", "53", "0.0", "5.407171771460119", "2.197224577336219", "3.988984046564274", "1_1", "1_1", "0", "2.772588722239781", "0.0280427621756684", "3.6375861597263857", "5.384495062789089", "2.197224577336219", "2.9444389791664403", "JavaScript", "0.0", null, null, "57", "54", "57", "0", "0"], ["94", "18F/analytics.usa.gov", "294", "0.0", "6.0", "6.0", "-0.3251748251748251", "1.9166666666666667", "-1.09861228866811", "0.6938231825532107", "0.1085466036133243", "0", "71.0", "0.0284396617986164", "37.0", "0.0", "0.0", "JavaScript", "225", "69", "-7", "1", "False", "2", "0", "0", "435", "25", "480", "0.0", "6.077642243349034", "3.2580965380214817", "6.175867270105761", "0_2", "1_2", "0", "4.276666119016055", "0.0280427621756684", "3.6375861597263857", "6.244166900663736", "3.3672958299864737", "6.272877006546167", "JavaScript", "0.0", null, null, "157", "130", "157", "0", "0"], ["119", "backblaze/b2_command_line_tool", "297", "0.0", "1.0", "1.0", "-0.2272727272727272", "4.75", "-1.0986122886681102", "0.9747015381341512", "0.0053867680146281", "0", "71.0", "0.0284396617986164", "37.0", "0.0", "0.0", "Python", "271", "26", "-7", "0", "False", "2", "0", "0", "486", "14", "185", "0.0", "6.18826412308259", "2.70805020110221", "5.225746673713201", "0_2", "0_2", "0", "4.276666119016055", "0.0280427621756684", "3.6375861597263857", "6.244166900663736", "3.3672958299864737", "6.272877006546167", "JavaScript", "0.0", null, null, "107", "99", "107", "0", "0"], ["132", "backblaze/b2_command_line_tool", "310", "0.0", "1.0", "1.0", "-0.0209790209790209", "2.333333333333333", "-2.220446049250313e-16", "0.9116003227929416", "0.4999999999999999", "0", "71.0", "0.0284396617986164", "37.0", "0.0", "0.0", "Python", "271", "39", "6", "0", "True", "2", "1", "0", "622", "16", "276", "0.0", "6.434546518787453", "2.833213344056216", "5.6240175061873385", "1_2", "0_2", "0", "4.276666119016055", "0.0280427621756684", "3.6375861597263857", "6.244166900663736", "3.3672958299864737", "6.272877006546167", "JavaScript", "0.0", null, null, "136", "127", "136", "0", "0"], ["133", "backblaze/b2_command_line_tool", "311", "0.0", "1.0", "1.0", "0.0104895104895104", "1.9166666666666667", "-1.7917594692280552", "0.5311862630585047", "0.0312433876000322", "0", "71.0", "0.0284396617986164", "37.0", "0.0", "0.0", "Python", "271", "40", "7", "0", "True", "2", "1", "0", "640", "17", "283", "0.0", "6.46302945692067", "2.8903717578961645", "5.648974238161206", "1_2", "0_2", "0", "4.276666119016055", "0.0280427621756684", "3.6375861597263857", "6.244166900663736", "3.3672958299864737", "6.272877006546167", "JavaScript", "0.0", null, null, "137", "127", "137", "0", "0"], ["139", "18F/analytics.usa.gov", "593", "0.0", "6.0", "6.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "2583.0", "0.0676402767102229", "88.0", "0.0", "0.0", "JavaScript", "225", "368", "-12", "1", "False", "3", "0", "0", "779", "44", "2573", "0.0", "6.659293919683638", "3.80666248977032", "7.853216388156071", "0_3", "1_3", "0", "7.857093864902493", "0.0654508642418384", "4.48863636973214", "6.672032945461067", "3.828641396489095", "7.88532923927319", "JavaScript", "0.0", null, null, "506", "280", "505", "1", "0"], ["142", "18F/analytics.usa.gov", "596", "0.0", "6.0", "6.0", "0.0314685314685314", "0.0833333333333333", "-7.771561172376098e-16", "0.5208212853727427", "0.5", "0", "2583.0", "0.0676402767102229", "88.0", "0.0", "0.0", "JavaScript", "225", "371", "-9", "1", "False", "3", "0", "0", "780", "45", "2594", "0.0", "6.660575149839686", "3.828641396489095", "7.861341795599989", "0_3", "1_3", "0", "7.857093864902493", "0.0654508642418384", "4.48863636973214", "6.672032945461067", "3.828641396489095", "7.88532923927319", "JavaScript", "0.0", null, null, "509", "281", "508", "1", "0"], ["148", "18F/analytics.usa.gov", "602", "0.0", "6.0", "6.0", "-0.0104895104895104", "0.0833333333333333", "-7.771561172376098e-16", "0.5208212853727427", "0.5", "0", "2583.0", "0.0676402767102229", "88.0", "0.0", "0.0", "JavaScript", "225", "377", "-3", "1", "False", "3", "0", "0", "781", "45", "2636", "0.0", "6.661854740545311", "3.828641396489095", "7.877397186353287", "0_3", "1_3", "0", "7.857093864902493", "0.0654508642418384", "4.48863636973214", "6.672032945461067", "3.828641396489095", "7.88532923927319", "JavaScript", "0.0", null, null, "510", "281", "509", "1", "0"], ["222", "MoatLab/FEMU", "512", "0.0", "1.0", "1.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "526.0", "0.3163265306122449", "62.0", "0.0", "0.0", "C", "380", "132", "-4", "0", "False", "4", "0", "0", "101", "4", "743", "0.0", "4.624972813284271", "1.6094379124341005", "6.612041034833092", "0_4", "0_4", "0", "6.267200548541362", "0.2748449256911002", "4.143134726391533", "5.10594547390058", "3.555348061489413", "4.6443908991413725", "PHP", "0.0", null, null, "6", "3", "6", "0", "0"], ["270", "4catalyzer/found", "335", "0.0", "15.0", "3.0", "-0.3391608391608391", "1.25", "-1.3862943611198906", "0.4659790582732636", "0.1502211048223348", "0", "28.0", "0.0719041278295605", "54.0", "0.0", "0.0", "TypeScript", "311", "24", "6", "0", "True", "5", "1", "0", "86", "5", "109", "0.0", "4.465908118654584", "1.791759469228055", "4.700480365792416", "1_5", "0_5", "0", "3.3672958299864737", "0.0694366256544285", "4.007333185232471", "4.574710978503383", "1.3862943611198906", "3.295836866004329", "PHP", "0.0", null, null, "58", "56", "58", "0", "0"], ["344", "2amigos/yii2-usuario", "474", "0.0", "1.0", "2.0", "0.0209790209790209", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "1002.0", "0.2689747003994673", "202.0", "0.0", "0.0", "PHP", "328", "146", "5", "1", "True", "7", "1", "1", "561", "52", "1027", "0.0", "6.331501849893691", "3.9702919135521215", "6.93537044601511", "1_7", "1_7", "0", "6.910750787961935", "0.2382092518900673", "5.313205979041787", "6.322565239927284", "3.9318256327243257", "6.900730664045172", "PHP", "0.0", null, null, "160", "139", "160", "0", "0"], ["364", "10up/theme-scaffold", "472", "1.0", "1.0", "1.0", "0.0174825174825174", "0.5833333333333334", "-0.4054651081081647", "0.5443501919379388", "0.441143795691918", "0", "1002.0", "0.2689747003994673", "202.0", "0.0", "0.0", "PHP", "392", "80", "0", "0", "False", "7", "0", "0", "259", "28", "562", "0.6931471805599453", "5.560681631015528", "3.3672958299864737", "6.333279628139691", "0_7", "0_7", "0", "6.910750787961935", "0.2382092518900673", "5.313205979041787", "6.322565239927284", "3.9318256327243257", "6.900730664045172", "PHP", "0.0", null, null, "91", "77", "90", "1", "0"], ["429", "2dust/v2rayn", "491", "0.0", "1.0", "2.0", "-0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "C#", "464", "27", "-10", "1", "False", "9", "0", "0", "59", "2", "170", "0.0", "4.0943445622221", "1.0986122886681096", "5.141663556502659", "0_9", "1_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "21", "10", "21", "0", "0"], ["432", "2dust/v2rayn", "494", "0.0", "1.0", "2.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "C#", "464", "30", "-7", "1", "False", "9", "0", "0", "82", "4", "191", "0.0", "4.418840607796597", "1.6094379124341005", "5.2574953720277815", "0_9", "1_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "27", "12", "27", "0", "0"], ["442", "2dust/v2rayn", "504", "0.0", "1.0", "2.0", "0.0", "1.5", "0.0", "0.8175744761936437", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "C#", "464", "40", "3", "1", "True", "9", "1", "1", "232", "8", "261", "0.0", "5.4510384535657", "2.197224577336219", "5.568344503761097", "1_9", "1_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "57", "28", "57", "0", "0"], ["443", "2dust/v2rayn", "505", "0.0", "1.0", "2.0", "-0.1258741258741259", "1.5", "0.0", "0.8175744761936437", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "C#", "464", "41", "4", "1", "True", "9", "1", "1", "232", "8", "268", "0.0", "5.4510384535657", "2.197224577336219", "5.594711379601839", "1_9", "1_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "59", "28", "59", "0", "0"], ["444", "2dust/v2rayn", "506", "0.0", "1.0", "2.0", "-0.2517482517482518", "1.5", "0.0", "0.8175744761936437", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "C#", "464", "42", "5", "1", "True", "9", "1", "1", "232", "8", "275", "0.0", "5.4510384535657", "2.197224577336219", "5.62040086571715", "1_9", "1_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "60", "28", "60", "0", "0"], ["454", "adobe/xdm", "489", "2.0", "2.0", "1.0", "0.0314685314685314", "1.25", "0.0", "0.7772998611746911", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "JavaScript", "378", "111", "-10", "0", "False", "9", "0", "0", "2948", "99", "779", "1.0986122886681096", "7.9892214088152755", "4.605170185988091", "6.659293919683638", "0_9", "0_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "492", "363", "492", "0", "0"], ["461", "adobe/xdm", "496", "0.0", "2.0", "1.0", "0.0139860139860139", "0.8333333333333334", "-2.220446049250313e-16", "0.6970592839654073", "0.5", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "JavaScript", "378", "118", "-3", "0", "False", "9", "0", "0", "3004", "106", "828", "0.0", "8.008032846969307", "4.672828834461906", "6.720220155135295", "0_9", "0_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "510", "374", "510", "0", "0"], ["475", "adobe/xdm", "510", "3.0", "2.0", "1.0", "-0.1293706293706293", "3.083333333333333", "0.6931471805599457", "0.9776096525583622", "0.8944669524991697", "0", "60.0", "0.0631163708086785", "96.0", "0.0", "0.0", "JavaScript", "378", "132", "11", "0", "True", "9", "1", "0", "3131", "111", "926", "1.3862943611198906", "8.049427057110693", "4.718498871295094", "6.831953565565855", "1_9", "0_9", "0", "4.110873864173311", "0.061204567317814", "4.574710978503383", "5.393627546352361", "2.079441541679836", "5.484796933490655", "C#", "0.0", null, null, "566", "405", "566", "0", "0"], ["487", "2ndquadrant/pglogical", "429", "0.0", "1.0", "1.0", "-0.0069930069930069", "0.1666666666666666", "-2.220446049250313e-16", "0.5415704832167998", "0.5", "0", "1143.0", "0.1789215686274509", "146.0", "0.0", "0.0", "C", "296", "133", "-2", "1", "False", "10", "0", "0", "696", "29", "928", "0.0", "6.546785410760524", "3.4011973816621555", "6.834108738813838", "0_10", "1_10", "0", "7.042286171939743", "0.1646000957015993", "4.990432586778736", "6.548219102762372", "3.4011973816621555", "6.849066282633458", "C", "0.0", null, null, "24", "13", "23", "1", "0"], ["539", "2ndquadrant/pglogical", "570", "0.0", "1.0", "1.0", "-0.0034965034965034", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "1", "2161.0", "0.5477941176470589", "447.0", "33.0", "1.0", "C", "296", "274", "0", "1", "False", "11", "0", "0", "784", "39", "1915", "0.0", "6.665683717782408", "3.688879454113936", "7.557994958530806", "0_11", "1_11", "0", "7.6787889981991535", "0.4368307673863841", "6.104793232414985", "6.665683717782408", "3.688879454113936", "7.557994958530806", "C", "0.0", null, null, "43", "18", "40", "3", "0"], ["597", "2ndquadrant/pglogical", "592", "0.0", "1.0", "1.0", "-0.0489510489510489", "0.1666666666666666", "-2.220446049250313e-16", "0.5415704832167998", "0.5", "0", "2165.0", "0.0661764705882353", "54.0", "0.0", "0.0", "C", "296", "296", "8", "1", "True", "12", "1", "1", "790", "39", "2069", "0.0", "6.673297967767653", "3.688879454113936", "7.635303886259415", "1_12", "1_12", "0", "7.680637427560936", "0.0640788566845223", "4.007333185232471", "6.672032945461067", "3.688879454113936", "7.607878073278507", "C", "0.0", null, null, "48", "21", "45", "3", "0"], ["666", "ivark/antimatterdimensionssourcecode", "454", "0.0", "13.0", "2.0", "-0.1328671328671328", "9.0", "-1.0986122886681098", "0.9996299076068292", "5.0802682381629744e-05", "0", "48.0", "0.116504854368932", "48.0", "0.0", "0.0", "JavaScript", "395", "59", "2", "0", "True", "13", "1", "0", "4003", "23", "256", "0.0", "8.295049140435111", "3.1780538303479453", "5.549076084895219", "1_13", "0_13", "0", "3.8918202981106265", "0.1102031401336142", "3.8918202981106265", "5.805134968916488", "2.772588722239781", "6.849066282633458", "TypeScript", "0.0", null, null, "267", "252", "267", "0", "0"], ["672", "ivark/antimatterdimensionssourcecode", "460", "0.0", "13.0", "2.0", "-0.8601398601398601", "5.666666666666667", "-2.7725887222397816", "0.947552910955812", "1.5019427068971945e-07", "0", "48.0", "0.116504854368932", "48.0", "0.0", "0.0", "JavaScript", "395", "65", "8", "0", "True", "13", "1", "0", "4123", "23", "298", "0.0", "8.32457884513685", "3.1780538303479453", "5.700443573390686", "1_13", "0_13", "0", "3.8918202981106265", "0.1102031401336142", "3.8918202981106265", "5.805134968916488", "2.772588722239781", "6.849066282633458", "TypeScript", "0.0", null, null, "285", "268", "285", "0", "0"], ["676", "ivark/antimatterdimensionssourcecode", "464", "0.0", "13.0", "2.0", "-0.1993006993006993", "4.083333333333333", "-2.302585092994046", "0.8557892338439869", "8.253360616840612e-05", "0", "48.0", "0.116504854368932", "48.0", "0.0", "0.0", "JavaScript", "395", "69", "12", "0", "True", "13", "1", "0", "4282", "23", "326", "0.0", "8.36240897761537", "3.1780538303479453", "5.7899601708972535", "1_13", "0_13", "0", "3.8918202981106265", "0.1102031401336142", "3.8918202981106265", "5.805134968916488", "2.772588722239781", "6.849066282633458", "TypeScript", "0.0", null, null, "306", "284", "306", "0", "0"], ["720", "dotnet/signservice", "528", "0.0", "1.0", "2.0", "-0.4405594405594406", "2.0", "-0.6931471805599461", "0.7869860421615984", "0.1999999999999997", "0", "1446.0", "0.6262135922330098", "258.0", "0.0", "0.0", "C#", "313", "215", "6", "0", "True", "14", "1", "0", "640", "14", "1275", "0.0", "6.46302945692067", "2.70805020110221", "7.151485463904735", "1_14", "0_14", "0", "7.277247726631484", "0.4862543630354853", "5.556828061699537", "6.501289670540389", "3.091042453358316", "7.237778191923443", "TypeScript", "0.0", null, null, "253", "194", "253", "0", "0"], ["741", "4store/4store", "38", "0.0", "1.0", "3.0", "-0.1468531468531468", "0.6666666666666666", "-2.220446049250313e-16", "0.6607563687658171", "0.5", "0", "676.0", "0.0312093628088426", "24.0", "0.0", "0.0", "C", "-61", "99", "2", "1", "True", "15", "1", "1", "403", "14", "131", "0.0", "6.00141487796115", "2.70805020110221", "4.882801922586371", "1_15", "1_15", "0", "6.517671272912275", "0.0307322521292039", "3.218875824868201", "5.98393628068719", "2.70805020110221", "4.770684624465664", "C", "0.0", null, null, "12", "10", "12", "0", "0"], ["770", "stof/stofdoctrineextensionsbundle", "44", "0.0", "1.0", "1.0", "-0.0804195804195804", "0.5833333333333334", "0.0", "0.6418340450887311", "0.5", "0", "676.0", "0.0312093628088426", "24.0", "0.0", "0.0", "PHP", "9", "35", "6", "0", "True", "15", "1", "0", "86", "12", "176", "0.0", "4.465908118654584", "2.5649493574615367", "5.176149732573829", "1_15", "0_15", "0", "6.517671272912275", "0.0307322521292039", "3.218875824868201", "5.98393628068719", "2.70805020110221", "4.770684624465664", "C", "0.0", null, null, "22", "16", "22", "0", "0"], ["777", "4store/4store", "64", "0.0", "1.0", "3.0", "0.0139860139860139", "0.3333333333333333", "-0.6931471805599457", "0.4110046290252651", "0.4424933340244421", "0", "322.0", "0.1716514954486346", "132.0", "0.0", "0.0", "C", "-61", "125", "-12", "1", "False", "16", "0", "0", "601", "15", "313", "0.0", "6.400257445308821", "2.772588722239781", "5.749392985908253", "0_16", "1_16", "0", "5.777652323222656", "0.1584142881026939", "4.890349128221754", "6.437751649736401", "2.772588722239781", "5.986452005284438", "C", "0.0", null, null, "25", "20", "25", "0", "0"], ["878", "99x/serverless-dynamodb-local", "348", "0.0", "5.0", "1.0", "-0.1433566433566433", "0.5833333333333334", "-0.6931471805599457", "0.4725740935912819", "0.4002710512473005", "0", "18.0", "0.1380597014925373", "37.0", "0.0", "0.0", "JavaScript", "300", "48", "-11", "1", "False", "19", "0", "0", "137", "18", "335", "0.0", "4.927253685157204", "2.9444389791664403", "5.817111159963204", "0_19", "1_19", "0", "2.9444389791664403", "0.1293247960965549", "3.6375861597263857", "5.241747015059643", "3.218875824868201", "6.023447592961032", "JavaScript", "0.0", null, null, "47", "41", "47", "0", "0"], ["903", "aspnet/aspnetkatana", "349", "0.0", "7.0", "1.0", "-0.0384615384615384", "0.4166666666666667", "-2.220446049250313e-16", "0.6026853379784917", "0.5", "0", "18.0", "0.1380597014925373", "37.0", "0.0", "0.0", "C#", "335", "14", "-11", "0", "False", "19", "0", "0", "1724", "30", "102", "0.0", "7.45298232946546", "3.4339872044851463", "4.634728988229636", "0_19", "0_19", "0", "2.9444389791664403", "0.1293247960965549", "3.6375861597263857", "5.241747015059643", "3.218875824868201", "6.023447592961032", "JavaScript", "0.0", null, null, "18", "16", "18", "0", "0"], ["906", "aspnet/aspnetkatana", "352", "0.0", "7.0", "1.0", "-0.1433566433566433", "0.4166666666666667", "-2.220446049250313e-16", "0.6026853379784917", "0.5", "0", "18.0", "0.1380597014925373", "37.0", "0.0", "0.0", "C#", "335", "17", "-8", "0", "False", "19", "0", "0", "1724", "30", "123", "0.0", "7.45298232946546", "3.4339872044851463", "4.820281565605037", "0_19", "0_19", "0", "2.9444389791664403", "0.1293247960965549", "3.6375861597263857", "5.241747015059643", "3.218875824868201", "6.023447592961032", "JavaScript", "0.0", null, null, "19", "16", "19", "0", "0"], ["920", "aspnet/aspnetkatana", "366", "0.0", "7.0", "1.0", "-0.1153846153846153", "0.75", "-4.440892098500626e-16", "0.679178699175393", "0.4999999999999999", "0", "18.0", "0.1380597014925373", "37.0", "0.0", "0.0", "C#", "335", "31", "6", "0", "True", "19", "1", "0", "1736", "32", "221", "0.0", "7.459914766241105", "3.49650756146648", "5.402677381872279", "1_19", "0_19", "0", "2.9444389791664403", "0.1293247960965549", "3.6375861597263857", "5.241747015059643", "3.218875824868201", "6.023447592961032", "JavaScript", "0.0", null, null, "31", "25", "31", "0", "0"], ["929", "99x/serverless-dynamodb-local", "424", "0.0", "5.0", "1.0", "-0.2482517482517482", "0.75", "-5.551115123125785e-16", "0.6791786991753929", "0.4999999999999999", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "JavaScript", "300", "124", "-10", "1", "False", "20", "0", "0", "246", "38", "867", "0.0", "5.5093883366279774", "3.6635616461296463", "6.76619171466035", "0_20", "1_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "87", "74", "87", "0", "0"], ["940", "99x/serverless-dynamodb-local", "435", "0.0", "5.0", "1.0", "0.0664335664335664", "0.25", "-6.661338147750939e-16", "0.562176500885798", "0.5", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "JavaScript", "300", "135", "1", "1", "True", "20", "1", "1", "253", "41", "944", "0.0", "5.537334267018537", "3.737669618283368", "6.851184927493742", "1_20", "1_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "90", "76", "89", "1", "0"], ["941", "99x/serverless-dynamodb-local", "436", "0.0", "5.0", "1.0", "0.0454545454545454", "0.25", "-6.661338147750939e-16", "0.562176500885798", "0.5", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "JavaScript", "300", "136", "2", "1", "True", "20", "1", "1", "253", "41", "951", "0.0", "5.537334267018537", "3.737669618283368", "6.858565034791365", "1_20", "1_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "92", "76", "91", "1", "0"], ["942", "99x/serverless-dynamodb-local", "437", "0.0", "5.0", "1.0", "0.0244755244755244", "0.25", "-6.661338147750939e-16", "0.562176500885798", "0.5", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "JavaScript", "300", "137", "3", "1", "True", "20", "1", "1", "253", "41", "958", "0.0", "5.537334267018537", "3.737669618283368", "6.865891074883438", "1_20", "1_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "93", "76", "92", "1", "0"], ["944", "99x/serverless-dynamodb-local", "439", "0.0", "5.0", "1.0", "-0.0174825174825174", "0.25", "-6.661338147750939e-16", "0.562176500885798", "0.5", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "JavaScript", "300", "139", "5", "1", "True", "20", "1", "1", "254", "42", "972", "0.0", "5.541263545158426", "3.7612001156935615", "6.880384082186005", "1_20", "1_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "95", "77", "94", "1", "0"], ["950", "99x/serverless-dynamodb-local", "445", "0.0", "5.0", "1.0", "-0.0384615384615384", "0.0833333333333333", "-4.440892098500626e-16", "0.5208212853727429", "0.5", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "JavaScript", "300", "145", "11", "1", "True", "20", "1", "1", "254", "42", "1014", "0.0", "5.541263545158426", "3.7612001156935615", "6.922643891475888", "1_20", "1_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "96", "77", "94", "2", "0"], ["966", "aspnet/aspnetkatana", "436", "0.0", "7.0", "1.0", "0.0804195804195804", "0.4166666666666667", "-4.440892098500626e-16", "0.6026853379784917", "0.5", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "C#", "335", "101", "2", "0", "True", "20", "1", "0", "1762", "37", "711", "0.0", "7.47477218239787", "3.6375861597263857", "6.568077911411976", "1_20", "0_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "49", "40", "49", "0", "0"], ["972", "aspnet/aspnetkatana", "442", "0.0", "7.0", "1.0", "-0.0104895104895104", "0.4166666666666667", "-1.09861228866811", "0.3358271983546644", "0.387518506648932", "0", "930.0", "0.0671641791044776", "18.0", "0.0", "0.0", "C#", "335", "107", "8", "0", "True", "20", "1", "0", "1764", "38", "753", "0.0", "7.475905969367397", "3.6635616461296463", "6.625392368007956", "1_20", "0_20", "0", "6.836259277277067", "0.065004830308996", "2.9444389791664403", "5.537334267018537", "3.737669618283368", "6.843749949006225", "JavaScript", "0.0", null, null, "54", "42", "54", "0", "0"], ["1003", "embedded-graphics/embedded-graphics", "522", "0.0", "3.0", "2.0", "-0.2307692307692307", "2.6666666666666665", "2.220446049250313e-16", "0.935030830871336", "0.5000000000000001", "0", "36.0", "0.05", "22.0", "0.0", "0.0", "Rust", "387", "135", "-11", "0", "False", "21", "0", "0", "379", "23", "896", "0.0", "5.940171252720432", "3.1780538303479453", "6.799055862058796", "0_21", "0_21", "0", "3.610917912644224", "0.048790164169432", "3.1354942159291497", "5.099866427824198", "2.833213344056216", "5.062595033026967", "Python", "0.0", null, null, "231", "207", "231", "0", "0"], ["1004", "embedded-graphics/embedded-graphics", "523", "1.0", "3.0", "2.0", "-0.3706293706293706", "2.6666666666666665", "2.220446049250313e-16", "0.935030830871336", "0.5000000000000001", "0", "36.0", "0.05", "22.0", "0.0", "0.0", "Rust", "387", "136", "-10", "0", "False", "21", "0", "0", "382", "23", "903", "0.6931471805599453", "5.948034989180646", "3.1780538303479453", "6.806829360392176", "0_21", "0_21", "0", "3.610917912644224", "0.048790164169432", "3.1354942159291497", "5.099866427824198", "2.833213344056216", "5.062595033026967", "Python", "0.0", null, null, "232", "207", "231", "1", "0"], ["1021", "embedded-graphics/embedded-graphics", "540", "0.0", "3.0", "2.0", "-0.6503496503496503", "3.5", "-1.386294361119891", "0.8922281748191615", "0.0077519379844961", "0", "36.0", "0.05", "22.0", "0.0", "0.0", "Rust", "387", "153", "7", "0", "True", "21", "1", "0", "434", "23", "1022", "0.0", "6.075346031088684", "3.1780538303479453", "6.930494765951626", "1_21", "0_21", "0", "3.610917912644224", "0.048790164169432", "3.1354942159291497", "5.099866427824198", "2.833213344056216", "5.062595033026967", "Python", "0.0", null, null, "279", "250", "277", "2", "0"], ["1031", "GoogleCloudPlatform/healthcare", "531", "0.0", "1.0", "1.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "654.0", "0.3329297820823244", "275.0", "0.0", "0.0", "Python", "389", "142", "-8", "1", "False", "22", "0", "0", "744", "18", "803", "0.0", "6.61338421837956", "2.9444389791664403", "6.689599269178967", "0_22", "1_22", "0", "6.484635235635252", "0.2873793632017017", "5.62040086571715", "6.617402977974477", "2.9444389791664403", "6.756932389247553", "Python", "0.0", null, null, "440", "380", "434", "6", "0"], ["1043", "GoogleCloudPlatform/healthcare", "543", "0.0", "1.0", "1.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "654.0", "0.3329297820823244", "275.0", "0.0", "0.0", "Python", "389", "154", "4", "1", "True", "22", "1", "1", "750", "18", "887", "0.0", "6.621405651764134", "2.9444389791664403", "6.78897174299217", "1_22", "1_22", "0", "6.484635235635252", "0.2873793632017017", "5.62040086571715", "6.617402977974477", "2.9444389791664403", "6.756932389247553", "Python", "0.0", null, null, "441", "380", "435", "6", "0"], ["1049", "GoogleCloudPlatform/healthcare", "549", "0.0", "1.0", "1.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "654.0", "0.3329297820823244", "275.0", "0.0", "0.0", "Python", "389", "160", "10", "1", "True", "22", "1", "1", "751", "18", "929", "0.0", "6.62273632394984", "2.9444389791664403", "6.835184586147301", "1_22", "1_22", "0", "6.484635235635252", "0.2873793632017017", "5.62040086571715", "6.617402977974477", "2.9444389791664403", "6.756932389247553", "Python", "0.0", null, null, "443", "380", "437", "6", "0"]], "shape": {"columns": 50, "rows": 10579}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>pr_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>time_to_first_comment</th>\n", "      <th>number_of_prs</th>\n", "      <th>number_of_merged_prs</th>\n", "      <th>number_of_closed_prs</th>\n", "      <th>number_of_open_prs</th>\n", "      <th>number_of_pr_comments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>AdaptiveCpp/AdaptiveCpp</td>\n", "      <td>503</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>6.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.833333</td>\n", "      <td>-0.405465</td>\n", "      <td>0.806569</td>\n", "      <td>0.322271</td>\n", "      <td>...</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>139</td>\n", "      <td>127</td>\n", "      <td>139</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>18F/analytics.usa.gov</td>\n", "      <td>230</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.944439</td>\n", "      <td>JavaScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>56</td>\n", "      <td>54</td>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>18F/analytics.usa.gov</td>\n", "      <td>233</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>...</td>\n", "      <td>2.944439</td>\n", "      <td>JavaScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>57</td>\n", "      <td>54</td>\n", "      <td>57</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>18F/analytics.usa.gov</td>\n", "      <td>294</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>-0.325175</td>\n", "      <td>1.916667</td>\n", "      <td>-1.098612</td>\n", "      <td>0.693823</td>\n", "      <td>0.108547</td>\n", "      <td>...</td>\n", "      <td>6.272877</td>\n", "      <td>JavaScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>157</td>\n", "      <td>130</td>\n", "      <td>157</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>backblaze/b2_command_line_tool</td>\n", "      <td>297</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>-0.227273</td>\n", "      <td>4.750000</td>\n", "      <td>-1.098612</td>\n", "      <td>0.974702</td>\n", "      <td>0.005387</td>\n", "      <td>...</td>\n", "      <td>6.272877</td>\n", "      <td>JavaScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>107</td>\n", "      <td>99</td>\n", "      <td>107</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203835</th>\n", "      <td>zulip/zulip-desktop</td>\n", "      <td>532</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>-0.010490</td>\n", "      <td>0.250000</td>\n", "      <td>0.693147</td>\n", "      <td>0.719735</td>\n", "      <td>0.543214</td>\n", "      <td>...</td>\n", "      <td>7.341484</td>\n", "      <td>TypeScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>517</td>\n", "      <td>382</td>\n", "      <td>499</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203848</th>\n", "      <td>aio-libs/aiodocker</td>\n", "      <td>521</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>-0.251748</td>\n", "      <td>1.833333</td>\n", "      <td>-2.079442</td>\n", "      <td>0.438782</td>\n", "      <td>0.021619</td>\n", "      <td>...</td>\n", "      <td>7.341484</td>\n", "      <td>TypeScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>409</td>\n", "      <td>301</td>\n", "      <td>408</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203849</th>\n", "      <td>aio-libs/aiodocker</td>\n", "      <td>522</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>-0.269231</td>\n", "      <td>1.583333</td>\n", "      <td>-1.386294</td>\n", "      <td>0.549101</td>\n", "      <td>0.100203</td>\n", "      <td>...</td>\n", "      <td>7.341484</td>\n", "      <td>TypeScript</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>411</td>\n", "      <td>301</td>\n", "      <td>410</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203899</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>551</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.283217</td>\n", "      <td>3.583333</td>\n", "      <td>-0.693147</td>\n", "      <td>0.947359</td>\n", "      <td>0.077003</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>409</td>\n", "      <td>345</td>\n", "      <td>408</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203901</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>553</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.209790</td>\n", "      <td>3.666667</td>\n", "      <td>-0.693147</td>\n", "      <td>0.951363</td>\n", "      <td>0.072997</td>\n", "      <td>...</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>413</td>\n", "      <td>348</td>\n", "      <td>412</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10579 rows × 50 columns</p>\n", "</div>"], "text/plain": ["                             repo_name standardized_time_weeks  pr_throughput  \\\n", "42             AdaptiveCpp/AdaptiveCpp                     503            1.0   \n", "54               18F/analytics.usa.gov                     230            0.0   \n", "57               18F/analytics.usa.gov                     233            0.0   \n", "94               18F/analytics.usa.gov                     294            0.0   \n", "119     backblaze/b2_command_line_tool                     297            0.0   \n", "...                                ...                     ...            ...   \n", "203835             zulip/zulip-desktop                     532            1.0   \n", "203848              aio-libs/aiodocker                     521            0.0   \n", "203849              aio-libs/aiodocker                     522            0.0   \n", "203899         tree-sitter/tree-sitter                     551            0.0   \n", "203901         tree-sitter/tree-sitter                     553            0.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "42                      3.0                 6.0       0.000000      1.833333   \n", "54                      6.0                 6.0       0.000000      0.000000   \n", "57                      6.0                 6.0       0.000000      0.000000   \n", "94                      6.0                 6.0      -0.325175      1.916667   \n", "119                     1.0                 1.0      -0.227273      4.750000   \n", "...                     ...                 ...            ...           ...   \n", "203835                  1.0                 3.0      -0.010490      0.250000   \n", "203848                  1.0                 5.0      -0.251748      1.833333   \n", "203849                  1.0                 5.0      -0.269231      1.583333   \n", "203899                  1.0                13.0       0.283217      3.583333   \n", "203901                  1.0                13.0      -0.209790      3.666667   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "42                   -0.405465            0.806569                 0.322271   \n", "54                    0.000000            0.000000                 0.000000   \n", "57                    0.000000            0.000000                 0.000000   \n", "94                   -1.098612            0.693823                 0.108547   \n", "119                  -1.098612            0.974702                 0.005387   \n", "...                        ...                 ...                      ...   \n", "203835                0.693147            0.719735                 0.543214   \n", "203848               -2.079442            0.438782                 0.021619   \n", "203849               -1.386294            0.549101                 0.100203   \n", "203899               -0.693147            0.947359                 0.077003   \n", "203901               -0.693147            0.951363                 0.072997   \n", "\n", "        ...  log_project_age_before_treatment  project_main_language  \\\n", "42      ...                          6.056784                    PHP   \n", "54      ...                          2.944439             JavaScript   \n", "57      ...                          2.944439             JavaScript   \n", "94      ...                          6.272877             JavaScript   \n", "119     ...                          6.272877             JavaScript   \n", "...     ...                               ...                    ...   \n", "203835  ...                          7.341484             TypeScript   \n", "203848  ...                          7.341484             TypeScript   \n", "203849  ...                          7.341484             TypeScript   \n", "203899  ...                          5.966147                     Go   \n", "203901  ...                          5.966147                     Go   \n", "\n", "        pr_success_rate  time_to_merge  time_to_first_comment  number_of_prs  \\\n", "42                  0.0            NaN                   None            139   \n", "54                  0.0            NaN                   None             56   \n", "57                  0.0            NaN                   None             57   \n", "94                  0.0            NaN                   None            157   \n", "119                 0.0            NaN                   None            107   \n", "...                 ...            ...                    ...            ...   \n", "203835              0.0            NaN                   None            517   \n", "203848              0.0            NaN                   None            409   \n", "203849              0.0            NaN                   None            411   \n", "203899              0.0            NaN                   None            409   \n", "203901              0.0            NaN                   None            413   \n", "\n", "       number_of_merged_prs  number_of_closed_prs  number_of_open_prs  \\\n", "42                      127                   139                   0   \n", "54                       54                    56                   0   \n", "57                       54                    57                   0   \n", "94                      130                   157                   0   \n", "119                      99                   107                   0   \n", "...                     ...                   ...                 ...   \n", "203835                  382                   499                  18   \n", "203848                  301                   408                   1   \n", "203849                  301                   410                   1   \n", "203899                  345                   408                   1   \n", "203901                  348                   412                   1   \n", "\n", "        number_of_pr_comments  \n", "42                          0  \n", "54                          0  \n", "57                          0  \n", "94                          0  \n", "119                         0  \n", "...                       ...  \n", "203835                      0  \n", "203848                      0  \n", "203849                      0  \n", "203899                      0  \n", "203901                      0  \n", "\n", "[10579 rows x 50 columns]"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data_success_rate[compiled_data_success_rate['pr_success_rate'] == 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}