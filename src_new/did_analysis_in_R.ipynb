# Load required libraries
library(stats)
library(lme4)
library(readr)
library(ggplot2)
library(stargazer)
library(multiwayvcov)
library(lmtest)
library(MuMIn)
library(lmerTest)
library(survival)
library(ggpubr)
library(survminer)
library(car)
library(coxme)
# Read data
compiled_data_test <- read.csv("../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers_new.csv")
# compiled_data_test <- read.csv("../result/did_result_20250212/compiled_data_test_with_features.csv")
# compiled_data_test <- read.csv("../result/standardized_productivity_20250202/compiled_data_test_with_features.csv")



# # Model 1: Fixed Effects Only
# model_fixed_effects_only <- lmer(
#   log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
#     log_project_commits + log_project_contributors + log_project_age + 
#     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
#   REML = FALSE,
#   data = compiled_data_test
# )

# # Calculate VIF
# vif_model_fixed_effects_only <- vif(model_fixed_effects_only)
# print(vif_model_fixed_effects_only)

# # Summary of the model
# summary(model_fixed_effects_only)

# # Calculate R-squared values
# r.squaredGLMM(model_fixed_effects_only)


# 检查过滤后数据中是否存在所有语言类别
compiled_data_test_exclude_na_growth_phase %>%
  count(project_main_language) %>%
  print(n = Inf)


# Model 2: Fixed Effects + Developer Characteristics
model_fixed_effects_developer <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
                     is_treated:is_post_treatment:log_tenure +
                     is_treated:is_post_treatment:log_commit_percent +
                    #  is_treated:is_post_treatment:log_commits +
                     log_project_commits + log_project_contributors + log_project_age + 
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_developer <- vif(model_fixed_effects_developer)
print(vif_model_fixed_effects_developer)

# Summary of the model
summary(model_fixed_effects_developer)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_developer)



# Model 3: Fixed Effects + Project Characteristics
model_fixed_effects_project <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment:log_project_commits_before_treatment + is_treated:is_post_treatment:log_project_contributors_before_treatment +
                     is_treated:is_post_treatment:log_project_age_before_treatment + 
                                          is_treated:is_post_treatment:growth_phase +
                      is_treated:is_post_treatment:log_newcomers +
                     is_treated:is_post_treatment:project_main_language +
                                          log_project_commits + log_project_contributors + log_project_age + 
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_project <- vif(model_fixed_effects_project)
print(vif_model_fixed_effects_project)

# Summary of the model
summary(model_fixed_effects_project)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_project)

model_stage1 <- lmer(
  log_pr_throughput ~ 
    is_post_treatment + 
    is_treated +
    log_project_commits + 
    log_project_contributors + 
    log_project_age +
    growth_phase +
    log_newcomers +
    project_main_language +
    (1 | time_cohort_effect) + 
    (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# 检查VIF
vif_stage1 <- car::vif(model_stage1)
print(vif_stage1)


summary(model_stage1)

model_optimized <- lmer(
  log_pr_throughput ~ 
    (is_post_treatment * is_treated) : log_project_commits_before_treatment +
    (is_post_treatment * is_treated) : log_project_contributors_before_treatment +
    (is_post_treatment * is_treated) : log_project_age_before_treatment +
    (is_post_treatment * is_treated) : growth_phase +
    (is_post_treatment * is_treated) : log_newcomers +
    (is_post_treatment * is_treated) : project_main_language +
    log_project_commits + 
    log_project_contributors + 
    log_project_age +
    (1 | time_cohort_effect) + 
    (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)
vif_optimized <- car::vif(model_optimized)
print(vif_optimized)


summary(model_optimized)

model_optimized <- lmer(
  log_pr_throughput ~ 
    (is_post_treatment * is_treated) : log_project_commits_before_treatment +
    (is_post_treatment * is_treated) : log_project_contributors_before_treatment +
    (is_post_treatment * is_treated) : log_project_age_before_treatment +
    (is_post_treatment * is_treated) : growth_phase +
    (is_post_treatment * is_treated) : log_newcomers +
    (is_post_treatment * is_treated) : project_main_language +
    log_project_commits + 
    log_project_contributors + 
    log_project_age +
    (1 | time_cohort_effect) + 
    (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)


model_base <- lmer(
  log_pr_throughput ~ 
    is_post_treatment + 
    is_treated +
    log_project_commits + 
    log_project_contributors + 
    log_project_age +
    growth_phase +
    log_newcomers +
    project_main_language +
    (1 | time_cohort_effect) + 
    (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)


model_interaction <- update(
  model_base,
  . ~ . + 
    is_post_treatment:is_treated:log_project_commits_before_treatment +
    is_post_treatment:is_treated:log_project_contributors_before_treatment +
    is_post_treatment:is_treated:log_project_age_before_treatment +
    is_post_treatment:is_treated:growth_phase +
    is_post_treatment:is_treated:log_newcomers +
    is_post_treatment:is_treated:project_main_language
)

# 检查GVIF
vif_interaction <- car::vif(model_interaction)
print(vif_interaction)


summary(model_interaction)

library(lme4)      # 混合效应模型
library(lmerTest)  # 提供p值
library(dplyr)     # 数据操作
library(car)       # 共线性诊断


# 标准化连续变量（关键步骤：降低共线性）
compiled_data_test_clean <- compiled_data_test %>%
  mutate(
    across(
      c(log_project_commits, log_project_contributors, log_project_age, log_newcomers),
      ~ scale(.) %>% as.numeric()  # 标准化并转换为数值
    )
  ) %>%
  # 移除预处理变量（与处理后变量可能高度相关）
  select(-contains("_before_treatment"))  

# 确保分类变量为因子
compiled_data_test_clean <- compiled_data_test_clean %>%
  mutate(
    growth_phase = factor(growth_phase),
    project_main_language = factor(project_main_language),
    time_cohort_effect = factor(time_cohort_effect)  # 转换为因子以用作固定效应
  )


model_base <- lmer(
  log_pr_throughput ~ 
    is_post_treatment * is_treated +  # 双重差分核心项
    log_project_commits + 
    log_project_contributors +
    log_project_age +
    growth_phase +
    log_newcomers +
    project_main_language +
    (1 | repo_cohort_effect) +        # 仅保留仓库随机效应
    time_cohort_effect,               # 时间固定效应
  data = compiled_data_test_clean,
  control = lmerControl(optimizer = "bobyqa")  # 稳定优化器
)
model_language <- update(
  model_base,
  . ~ . + is_post_treatment:is_treated:project_main_language
)
# 共线性诊断
vif_language <- car::vif(model_language)
print(vif_language)


model_language <- update(
  model_base,
  . ~ . + is_post_treatment:is_treated:project_main_language
)
# 共线性诊断
vif_language <- car::vif(model_language)
print(vif_language)


# Model 4: Fixed Effects + Developer Characteristics + Project Characteristics
model_fixed_effects_developer_project <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment:log_tenure + is_treated:is_post_treatment:log_commit_percent + is_treated:is_post_treatment:log_commits +
                     is_treated:is_post_treatment:log_project_commits_before_treatment + is_treated:is_post_treatment:log_project_contributors_before_treatment +
                     is_treated:is_post_treatment:log_project_age_before_treatment + is_treated:is_post_treatment:growth_phase +
                      is_treated:is_post_treatment:log_newcomers +
                     is_treated:is_post_treatment:project_main_language +
                                          log_project_commits + log_project_contributors + log_project_age + 
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_developer_project <- vif(model_fixed_effects_developer_project)
print(vif_model_fixed_effects_developer_project)

# Summary of the model
summary(model_fixed_effects_developer_project)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_developer_project)

# Create a function to export model summaries to text files
export_model_summary <- function(model, filename) {
  sink(file = paste0("../result/model_summaries_with_newcomer_num/", filename, ".txt"))
  print(summary(model))
  print(r.squaredGLMM(model))
  sink()
}

# Create directory if it doesn't exist
dir.create("../result/model_summaries_with_newcomer_num", recursive = TRUE, showWarnings = FALSE)

# Export summaries for the three models
export_model_summary(model_fixed_effects_only, "model1_fixed_effects_only")
export_model_summary(model_fixed_effects_developer, "model2_fixed_effects_developer")
export_model_summary(model_fixed_effects_project, "model3_fixed_effects_project")
export_model_summary(model_fixed_effects_developer_project, "model4_fixed_effects_developer_project")

# Also export VIF results
# Create directory for VIF results
dir.create("../result/model_summaries", recursive = TRUE, showWarnings = FALSE)

# Export VIF results
sink(file = "../result/model_summaries/vif_results.txt")

cat("VIF for Model 1 (Fixed Effects Only):\n")
print(vif_model_fixed_effects_only)
cat("\n\nVIF for Model 2 (Fixed Effects + Developer Characteristics):\n")
print(vif_model_fixed_effects_developer)
cat("\n\nVIF for Model 3 (Fixed Effects + Project Characteristics):\n")
print(vif_model_fixed_effects_project)
cat("\n\nVIF for Model 4 (Fixed Effects + Developer Characteristics + Project Characteristics):\n")
print(vif_model_fixed_effects_developer_project)


cat("\n\nVIF Summary:\n")
vif_summary <- cbind(VIF = c(vif_model_fixed_effects_only, vif_model_fixed_effects_developer, 
                              vif_model_fixed_effects_project, vif_model_fixed_effects_developer_project))
print(vif_summary)


sink(file = "../result/model_summaries/vif_summary.txt")
print(vif_summary)
sink()

install.packages("eventstudyr")
library(eventstudyr)

# Load required libraries
library(eventstudyr)
library(ggplot2)

# Read data
compiled_data_test <- read.csv("../result/did_result_20250212/compiled_data_test.csv")
compiled_data_test <- compiled_data_test %>%
  group_by(cohort_id) %>%
  mutate(relativized_time = as.integer(relativized_time)) %>%
  ungroup()
# Estimate the event study model without control variables
estimates_without_controls <- EventStudy(
  estimator = "OLS",
  data = compiled_data_test,
  outcomevar = "log_pr_throughput",
  policyvar = "is_treated",
  idvar = "cohort_id",
  timevar = "relativized_time",
  pre = 8, post = 8,
  normalize = -1
)

library(tidyr)

compiled_data_test <- compiled_data_test %>%
  group_by(cohort_id) %>%
  complete(relativized_time = seq(min(relativized_time), max(relativized_time), by = 1)) %>%
  fill(log_pr_throughput, is_treated, log_tenure, log_commit_percent, log_commits, .direction = "down") %>%
  ungroup()


# Estimate the event study model with control variables
estimates_with_controls <- EventStudy(
  estimator = "OLS",
  data = compiled_data_test,
  outcomevar = "log_pr_throughput",
  policyvar = "is_treated",
  idvar = "cohort_id",
  timevar = "relativized_time",
  controls = c("log_project_commits", "log_project_contributors", "log_project_age"),
  pre =0, post = 4,
  normalize = -1
)

# Estimate the event study model with control variables
estimates_with_controls <- EventStudy(
  estimator = "OLS",
  data = compiled_data_test,
  outcomevar = "log_pr_throughput",
  policyvar = "is_treated",
  idvar = "cohort_id",
  timevar = "relativized_time",
  controls = c("log_project_commits", "log_project_contributors", "log_project_age"),
  pre =0, post = 4,
  normalize = -1
)

# Create event study plot without control variables
plt_without_controls <- EventStudyPlot(estimates = estimates_without_controls)
print(plt_without_controls)

# Create event study plot with control variables
plt_with_controls <- EventStudyPlot(estimates = estimates_with_controls)
print(plt_with_controls)

# Load required libraries
library(dplyr)
library(lme4)
library(car)
library(MuMIn)

# Read data

# Log transformations
compiled_data_test <- compiled_data_test %>%
  mutate(
    log_pr_throughput = log(pr_throughput + 1),
    log_project_commits = log(project_commits + 1),
    log_project_contributors = log(project_contributors + 1),
    log_project_age = log(project_age + 1),
    log_project_commits_before_treatment = log(project_commits_before_treatment + 1),
    log_project_contributors_before_treatment = log(project_contributors_before_treatment + 1),
    log_project_age_before_treatment = log(project_age_before_treatment + 1),
    log_tenure = log(tenure + 1),
    log_commit_percent = log(commit_percent + 1),
    log_commits = log(commits + 1)
  )

# Create interaction variables and fixed effects
compiled_data_test <- compiled_data_test %>%
  mutate(
    time_cohort_effect = paste0(is_post_treatment, "_", cohort_id),
    repo_cohort_effect = paste0(is_treated, "_", cohort_id)
  ) %>%
  mutate(
    time_cohort_effect = as.factor(time_cohort_effect),
    repo_cohort_effect = as.factor(repo_cohort_effect)
  )

# Model 1: Fixed Effects Only
model_fixed_effects_only <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_only <- vif(model_fixed_effects_only)
print(vif_model_fixed_effects_only)

# Summary of the model
summary(model_fixed_effects_only)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_only)

# Model 2: Fixed Effects + Developer Characteristics
model_fixed_effects_developer <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
                     log_tenure + log_commit_percent + log_commits +
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_developer <- vif(model_fixed_effects_developer)
print(vif_model_fixed_effects_developer)

# Summary of the model
summary(model_fixed_effects_developer)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_developer)

# Model 3: Fixed Effects + Project Characteristics
model_fixed_effects_project <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
                     log_project_commits_before_treatment + log_project_contributors_before_treatment +
                     log_project_age_before_treatment + project_main_language +
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_project <- vif(model_fixed_effects_project)
print(vif_model_fixed_effects_project)

# Summary of the model
summary(model_fixed_effects_project)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_project)

# Model 4: Fixed Effects + Developer Characteristics + Project Characteristics
model_fixed_effects_developer_project <- lmer(
  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
                     log_tenure + log_commit_percent + log_commits +
                     log_project_commits_before_treatment + log_project_contributors_before_treatment +
                     log_project_age_before_treatment + project_main_language +
                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),
  REML = FALSE,
  data = compiled_data_test
)

# Calculate VIF
vif_model_fixed_effects_developer_project <- vif(model_fixed_effects_developer_project)
print(vif_model_fixed_effects_developer_project)

# Summary of the model
summary(model_fixed_effects_developer_project)

# Calculate R-squared values
r.squaredGLMM(model_fixed_effects_developer_project)

# Load required libraries
library(fixest)  # Efficient regression with fixed effects
library(dplyr)   # Data manipulation

# Read data
# compiled_data_test <- read.csv("../result/did_result_20250212/compiled_data_test.csv")
compiled_data_test <- read.csv("../result/standardized_productivity_20250202/compiled_data_test_with_features.csv")
# Log transformations
compiled_data_test <- compiled_data_test %>%
  mutate(
    log_pr_throughput = log(pr_throughput + 1),
    log_project_commits = log(project_commits + 1),
    log_project_contributors = log(project_contributors + 1),
    log_project_age = log(project_age + 1)
  )

# Create time-cohort and repo-cohort effects
compiled_data_test <- compiled_data_test %>%
  mutate(
    time_cohort_effect = paste0(is_post_treatment, "_", cohort_id),
    repo_cohort_effect = paste0(is_treated, "_", cohort_id)
  ) %>%
  mutate(
    time_cohort_effect = as.factor(time_cohort_effect),
    repo_cohort_effect = as.factor(repo_cohort_effect)
  )

# Run OLS regression with fixed effects
model <- feols(
  log_pr_throughput ~ is_treated * is_post_treatment | time_cohort_effect + repo_cohort_effect,
  data = compiled_data_test,
  # cluster = ~cohort_id  # Optional: Cluster standard errors by cohort_id
)

# View results
summary(model)

# Load required libraries
library(fixest)  # Efficient regression with fixed effects
library(dplyr)   # Data manipulation

# Read data
compiled_data_test <- read.csv("../result/standardized_productivity_20250202/compiled_data_test_with_features.csv")

# Log transformations
compiled_data_test <- compiled_data_test %>%
  mutate(
    log_pr_throughput = log(pr_throughput + 1),
    log_project_commits = log(project_commits + 1),
    log_project_contributors = log(project_contributors + 1),
    log_project_age = log(project_age + 1)
  )

# Create interaction term manually
compiled_data_test <- compiled_data_test %>%
  mutate(interaction_term = is_treated * is_post_treatment)

# Run OLS regression with fixed effects
model <- feols(
  log_pr_throughput ~ interaction_term | cohort_id,
  data = compiled_data_test,
  cluster = ~cohort_id
)

# View results
summary(model)

# 检查变量的分布
table(compiled_data_test$is_treated)
table(compiled_data_test$is_post_treatment)

# Load required libraries
library(fixest)  # Efficient regression with fixed effects
library(dplyr)   # Data manipulation

# Read data
# compiled_data_test <- read.csv("../result/did_result_20250212/compiled_data_test.csv")
compiled_data_test <- read.csv("../result/standardized_productivity_20250202/compiled_data_test_with_features.csv")
# compiled_data_test <- read.csv("../result/standardized_productivity_20250202/compiled_data_test.csv")
# Log transformations
compiled_data_test <- compiled_data_test %>%
  mutate(
    log_pr_throughput = log(pr_throughput + 1),
    log_project_commits = log(project_commits + 1),
    log_project_contributors = log(project_contributors + 1),
    log_project_age = log(project_age + 1)
  )

# Create time-cohort and repo-cohort effects
compiled_data_test <- compiled_data_test %>%
  mutate(
    time_cohort_effect = paste0(is_post_treatment, "_", cohort_id),
    repo_cohort_effect = paste0(is_treated, "_", cohort_id)
  ) %>%
  mutate(
    time_cohort_effect = as.factor(time_cohort_effect),
    repo_cohort_effect = as.factor(repo_cohort_effect)
  )

# Run OLS regression with fixed effects
model <- feols(
  log_pr_throughput ~ is_treated * is_post_treatment | time_cohort_effect + repo_cohort_effect,
  data = compiled_data_test,
  cluster = ~cohort_id  # Optional: Cluster standard errors by cohort_id
)

# View results
summary(model)

model_with_features_left_core_dev <- feols(
  log_pr_throughput ~ is_treated * is_post_treatment * (log_tenure + log_commit_percent + log_commits) | time_cohort_effect + repo_cohort_effect,
  data = compiled_data_test,
  cluster = ~cohort_id  # Cluster standard errors by cohort_id (optional)
)

summary(model_with_features_left_core_dev)

model_with_features_project_characteristics <- feols(
  log_pr_throughput ~ is_treated * is_post_treatment * (log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language) | time_cohort_effect + repo_cohort_effect,
  data = compiled_data_test,
  cluster = ~cohort_id  # Cluster standard errors by cohort_id (optional)
)

summary(model_with_features_project_characteristics)

model_with_features_left_core_dev_and_project_characteristics <- feols(
  log_pr_throughput ~ is_treated * is_post_treatment * (log_tenure + log_commit_percent + log_commits + log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language) | time_cohort_effect + repo_cohort_effect,
  data = compiled_data_test,
  cluster = ~cohort_id  # Cluster standard errors by cohort_id (optional)
)

summary(model_with_features_left_core_dev_and_project_characteristics)