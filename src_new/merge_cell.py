# This is the merge cell code to add to your notebook:

merge_cell_code = '''
# Merge productivity_data_20250227 and productivity_data_20250730 according to specifications:
# 1. Use repo_name and standardized_time_weeks as index, keep only productivity_20250227
# 2. Add columns from productivity_data_20250730 that don't exist in productivity_20250227
# 3. For common columns, fill missing values in productivity_20250227 with values from productivity_data_20250730

# Set the index for both dataframes
productivity_20250227_indexed = productivity_data_20250227.set_index(['repo_name', 'standardized_time_weeks'])
productivity_20250730_indexed = productivity_data_20250730.set_index(['repo_name', 'standardized_time_weeks'])

# Get columns that exist in 20250730 but not in 20250227
new_columns = [col for col in productivity_data_20250730.columns 
               if col not in productivity_data_20250227.columns]

print(f"New columns to add: {new_columns}")

# Get common columns (excluding the index columns)
common_columns = [col for col in productivity_data_20250227.columns 
                  if col in productivity_data_20250730.columns and 
                  col not in ['repo_name', 'standardized_time_weeks']]

print(f"Common columns to merge: {len(common_columns)} columns")

# Start with productivity_20250227 as the base
merged_data = productivity_20250227_indexed.copy()

# Add new columns from productivity_data_20250730
for col in new_columns:
    merged_data[col] = productivity_20250730_indexed[col]

# For common columns, fill missing values in productivity_20250227 with values from productivity_data_20250730
for col in common_columns:
    # Get the corresponding column from 20250730
    col_20250730 = productivity_20250730_indexed[col]
    
    # Fill missing values in merged_data with values from 20250730
    merged_data[col] = merged_data[col].fillna(col_20250730)

# Reset index to get back to normal dataframe format
final_merged_data = merged_data.reset_index()

print(f"Final merged data shape: {final_merged_data.shape}")
print(f"Original productivity_data_20250227 shape: {productivity_data_20250227.shape}")
print(f"Original productivity_data_20250730 shape: {productivity_data_20250730.shape}")

# Display the first few rows to verify the merge
final_merged_data.head()
'''

print("Copy this code into a new cell in your notebook:")
print("=" * 50)
print(merge_cell_code)
print("=" * 50) 