{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f8606cf4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "9db4d05a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}], "ref": "2af49323-fdbc-459e-81f2-4acfaa17069b", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424"]], "shape": {"columns": 42, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>6.641182</td>\n", "      <td>2.484907</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>5.521461</td>\n", "      <td>3.295837</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 42 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  log_project_commits_before_treatment  \\\n", "0        ...                              6.641182   \n", "1        ...                              6.641182   \n", "2        ...                              6.641182   \n", "3        ...                              6.641182   \n", "4        ...                              6.641182   \n", "...      ...                                   ...   \n", "3503016  ...                              5.521461   \n", "3503017  ...                              5.521461   \n", "3503018  ...                              5.521461   \n", "3503019  ...                              5.521461   \n", "3503020  ...                              5.521461   \n", "\n", "         log_project_contributors_before_treatment  \\\n", "0                                         2.484907   \n", "1                                         2.484907   \n", "2                                         2.484907   \n", "3                                         2.484907   \n", "4                                         2.484907   \n", "...                                            ...   \n", "3503016                                   3.295837   \n", "3503017                                   3.295837   \n", "3503018                                   3.295837   \n", "3503019                                   3.295837   \n", "3503020                                   3.295837   \n", "\n", "         log_project_age_before_treatment  project_main_language  \\\n", "0                                5.817111                   Rust   \n", "1                                5.817111                   Rust   \n", "2                                5.817111                   Rust   \n", "3                                5.817111                   Rust   \n", "4                                5.817111                   Rust   \n", "...                                   ...                    ...   \n", "3503016                          6.741701                     C#   \n", "3503017                          6.741701                     C#   \n", "3503018                          6.741701                     C#   \n", "3503019                          6.741701                     C#   \n", "3503020                          6.741701                     C#   \n", "\n", "         growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        decelerating       15.0       2.772589    6.751101   \n", "1        decelerating       15.0       2.772589    6.751101   \n", "2        decelerating       15.0       2.772589    6.751101   \n", "3        decelerating       15.0       2.772589    6.751101   \n", "4        decelerating       15.0       2.772589    6.751101   \n", "...               ...        ...            ...         ...   \n", "3503016        steady        1.0       0.693147    6.710523   \n", "3503017        steady        1.0       0.693147    6.710523   \n", "3503018        steady        1.0       0.693147    6.710523   \n", "3503019        steady        1.0       0.693147    6.710523   \n", "3503020        steady        1.0       0.693147    6.710523   \n", "\n", "         log_commit_percent  log_commits  \n", "0                  0.144831     5.433722  \n", "1                  0.144831     5.433722  \n", "2                  0.144831     5.433722  \n", "3                  0.144831     5.433722  \n", "4                  0.144831     5.433722  \n", "...                     ...          ...  \n", "3503016            0.018605     2.197225  \n", "3503017            0.018605     2.197225  \n", "3503018            0.018605     2.197225  \n", "3503019            0.018605     2.197225  \n", "3503020            0.018605     2.197225  \n", "\n", "[3503021 rows x 42 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["original_combined_data = pd.read_csv(\"../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers.csv\")\n", "original_combined_data"]}, {"cell_type": "code", "execution_count": 3, "id": "2489263f", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "datetime", "rawType": "object", "type": "string"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}], "ref": "3c380deb-2aa7-483d-8a04-6a0277b9ccae", "rows": [["0", "openstreetmap/osm-binary", "2011-04-11", "0.0", "1.0", null, "32"], ["1", "openstreetmap/osm-binary", "2011-05-02", "1.0", null, "559.2764", "35"], ["2", "openstreetmap/osm-binary", "2011-05-23", "0.0", "0.0", null, "38"], ["3", "openstreetmap/osm-binary", "2011-06-06", "0.0", "1.0", null, "40"], ["4", "openstreetmap/osm-binary", "2011-10-17", "1.0", "0.0", "3229.9158", "59"], ["5", "openstreetmap/osm-binary", "2012-10-15", "0.0", "1.0", null, "111"], ["6", "openstreetmap/osm-binary", "2012-12-03", "1.0", "1.0", "31.0642", "118"], ["7", "openstreetmap/osm-binary", "2013-03-04", "0.0", "0.5", null, "131"], ["8", "openstreetmap/osm-binary", "2013-06-03", "0.0", "0.0", null, "144"], ["9", "openstreetmap/osm-binary", "2013-06-10", "0.0", "1.0", null, "145"], ["10", "openstreetmap/osm-binary", "2013-08-26", "0.0", "1.0", null, "156"], ["11", "openstreetmap/osm-binary", "2013-09-02", "5.0", null, "2828.6565", "157"], ["12", "openstreetmap/osm-binary", "2014-02-24", "1.0", "1.0", "56.4483", "182"], ["13", "openstreetmap/osm-binary", "2014-03-17", "1.0", "1.0", "3.7925", "185"], ["14", "openstreetmap/osm-binary", "2014-03-24", "1.0", null, "172.4311", "186"], ["15", "openstreetmap/osm-binary", "2015-06-08", "0.0", "1.0", null, "249"], ["16", "openstreetmap/osm-binary", "2015-06-22", "1.0", "1.0", "17.4894", "251"], ["17", "openstreetmap/osm-binary", "2015-07-13", "0.0", "0.0", null, "254"], ["18", "openstreetmap/osm-binary", "2015-08-10", "1.0", null, "1484.2341", "258"], ["19", "openstreetmap/osm-binary", "2015-09-14", "0.0", "0.0", null, "263"], ["20", "openstreetmap/osm-binary", "2016-01-25", "0.0", "0.0", null, "282"], ["21", "openstreetmap/osm-binary", "2016-08-29", "0.0", "1.0", null, "313"], ["22", "openstreetmap/osm-binary", "2017-08-21", "0.0", "0.0", null, "364"], ["23", "openstreetmap/osm-binary", "2018-11-26", "0.0", "0.0", null, "430"], ["24", "openstreetmap/osm-binary", "2019-07-22", "0.0", "1.0", null, "464"], ["25", "openstreetmap/osm-binary", "2019-10-14", "1.0", null, "1985.3317", "476"], ["26", "openstreetmap/osm-binary", "2019-12-30", "0.0", "0.0", null, "487"], ["27", "openstreetmap/osm-binary", "2020-03-16", "0.0", "1.0", null, "498"], ["28", "openstreetmap/osm-binary", "2020-08-17", "2.0", "1.0", "19238.7031", "520"], ["29", "openstreetmap/osm-binary", "2020-09-28", "4.0", "1.0", "222.7492", "526"], ["30", "openstreetmap/osm-binary", "2020-10-12", "1.0", "1.0", "20.8553", "528"], ["31", "openstreetmap/osm-binary", "2020-10-19", "4.0", "1.0", "5.9961", "529"], ["32", "openstreetmap/osm-binary", "2020-10-26", "1.0", "1.0", "1.3108", "530"], ["33", "openstreetmap/osm-binary", "2020-12-28", "4.0", "0.8571", "1.3204", "539"], ["34", "openstreetmap/osm-binary", "2021-01-04", "7.0", "0.8333", "8.8872", "540"], ["35", "openstreetmap/osm-binary", "2022-01-03", "1.0", "1.0", "10.4572", "592"], ["36", "openstreetmap/osm-binary", "2022-10-03", "0.0", "1.0", null, "631"], ["37", "openstreetmap/osm-binary", "2023-09-25", "0.0", "1.0", null, "682"], ["38", "openstreetmap/osm-binary", "2023-10-02", "2.0", null, "4352.7744", "683"], ["39", "openstreetmap/osm-binary", "2024-01-01", "1.0", "1.0", "2.2808", "696"], ["40", "openstreetmap/osm-binary", "2024-03-11", "0.0", "0.0", null, "706"], ["41", "openstreetmap/osm-binary", "2024-03-25", "1.0", "1.0", "12.6939", "708"], ["42", "openstreetmap/osm-binary", "2024-09-16", "0.0", "0.0", null, "733"], ["43", "openstreetmap/osm-binary", "2024-09-23", "1.0", "1.0", "2.6347", "734"], ["44", "openstreetmap/osm-binary", "2024-11-04", "0.0", "1.0", null, "740"], ["45", "openstreetmap/osm-binary", "2024-11-11", "1.0", null, "132.9567", "741"], ["46", "openstreetmap/osm-binary", "2024-11-18", "0.0", "0.0", null, "742"], ["47", "openstreetmap/osm-binary", "2024-12-09", "1.0", "1.0", "1.1525", "745"], ["48", "nysenate/openlegislation", "2015-01-19", "0.0", "1.0", null, "229"], ["49", "nysenate/openlegislation", "2015-01-26", "1.0", null, "43.8844", "230"]], "shape": {"columns": 6, "rows": 6770217}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>standardized_time_weeks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>openstreetmap/osm-binary</td>\n", "      <td>2011-04-11</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>openstreetmap/osm-binary</td>\n", "      <td>2011-05-02</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>559.2764</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>openstreetmap/osm-binary</td>\n", "      <td>2011-05-23</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "      <td>NaN</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>openstreetmap/osm-binary</td>\n", "      <td>2011-06-06</td>\n", "      <td>0.0</td>\n", "      <td>1.0000</td>\n", "      <td>NaN</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>openstreetmap/osm-binary</td>\n", "      <td>2011-10-17</td>\n", "      <td>1.0</td>\n", "      <td>0.0000</td>\n", "      <td>3229.9158</td>\n", "      <td>59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6770212</th>\n", "      <td>graphql-hive/console</td>\n", "      <td>2025-01-20</td>\n", "      <td>31.0</td>\n", "      <td>0.8214</td>\n", "      <td>49.5173</td>\n", "      <td>751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6770213</th>\n", "      <td>graphql-hive/console</td>\n", "      <td>2025-01-27</td>\n", "      <td>28.0</td>\n", "      <td>0.9231</td>\n", "      <td>94.7706</td>\n", "      <td>752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6770214</th>\n", "      <td>graphql-hive/console</td>\n", "      <td>2025-02-03</td>\n", "      <td>18.0</td>\n", "      <td>0.8846</td>\n", "      <td>25.8493</td>\n", "      <td>753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6770215</th>\n", "      <td>graphql-hive/console</td>\n", "      <td>2025-02-10</td>\n", "      <td>30.0</td>\n", "      <td>0.7222</td>\n", "      <td>40.6634</td>\n", "      <td>754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6770216</th>\n", "      <td>graphql-hive/console</td>\n", "      <td>2025-02-17</td>\n", "      <td>14.0</td>\n", "      <td>0.4545</td>\n", "      <td>39.7995</td>\n", "      <td>755</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6770217 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                        repo_name    datetime  pr_throughput  \\\n", "0        openstreetmap/osm-binary  2011-04-11            0.0   \n", "1        openstreetmap/osm-binary  2011-05-02            1.0   \n", "2        openstreetmap/osm-binary  2011-05-23            0.0   \n", "3        openstreetmap/osm-binary  2011-06-06            0.0   \n", "4        openstreetmap/osm-binary  2011-10-17            1.0   \n", "...                           ...         ...            ...   \n", "6770212      graphql-hive/console  2025-01-20           31.0   \n", "6770213      graphql-hive/console  2025-01-27           28.0   \n", "6770214      graphql-hive/console  2025-02-03           18.0   \n", "6770215      graphql-hive/console  2025-02-10           30.0   \n", "6770216      graphql-hive/console  2025-02-17           14.0   \n", "\n", "         pull_request_success_rate  time_to_merge  standardized_time_weeks  \n", "0                           1.0000            NaN                       32  \n", "1                              NaN       559.2764                       35  \n", "2                           0.0000            NaN                       38  \n", "3                           1.0000            NaN                       40  \n", "4                           0.0000      3229.9158                       59  \n", "...                            ...            ...                      ...  \n", "6770212                     0.8214        49.5173                      751  \n", "6770213                     0.9231        94.7706                      752  \n", "6770214                     0.8846        25.8493                      753  \n", "6770215                     0.7222        40.6634                      754  \n", "6770216                     0.4545        39.7995                      755  \n", "\n", "[6770217 rows x 6 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# combine with other productivity metrics\n", "\n", "productivity_data = pd.read_csv(\"../result/productivity_metrics_20250313.csv\")\n", "productivity_data"]}, {"cell_type": "code", "execution_count": 4, "id": "2d79eb30", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}], "ref": "f357618a-35fd-4f28-9e22-80f5b2b774eb", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895"], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "18.3589"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "25.7632"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "14.9636"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "361.1883"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "351.6383"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.9486"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "1.7981"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "21.5526"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.005"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.6445"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "49.2922"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "91.0883"], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null]], "shape": {"columns": 44, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>5.817111</td>\n", "      <td>Rust</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>6.741701</td>\n", "      <td>C#</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 44 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  log_project_age_before_treatment  project_main_language  \\\n", "0        ...                          5.817111                   Rust   \n", "1        ...                          5.817111                   Rust   \n", "2        ...                          5.817111                   Rust   \n", "3        ...                          5.817111                   Rust   \n", "4        ...                          5.817111                   Rust   \n", "...      ...                               ...                    ...   \n", "3503016  ...                          6.741701                     C#   \n", "3503017  ...                          6.741701                     C#   \n", "3503018  ...                          6.741701                     C#   \n", "3503019  ...                          6.741701                     C#   \n", "3503020  ...                          6.741701                     C#   \n", "\n", "         growth_phase  newcomers log_newcomers  log_tenure  \\\n", "0        decelerating       15.0      2.772589    6.751101   \n", "1        decelerating       15.0      2.772589    6.751101   \n", "2        decelerating       15.0      2.772589    6.751101   \n", "3        decelerating       15.0      2.772589    6.751101   \n", "4        decelerating       15.0      2.772589    6.751101   \n", "...               ...        ...           ...         ...   \n", "3503016        steady        1.0      0.693147    6.710523   \n", "3503017        steady        1.0      0.693147    6.710523   \n", "3503018        steady        1.0      0.693147    6.710523   \n", "3503019        steady        1.0      0.693147    6.710523   \n", "3503020        steady        1.0      0.693147    6.710523   \n", "\n", "         log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0                  0.144831     5.433722                        1.0   \n", "1                  0.144831     5.433722                        NaN   \n", "2                  0.144831     5.433722                        NaN   \n", "3                  0.144831     5.433722                        NaN   \n", "4                  0.144831     5.433722                        NaN   \n", "...                     ...          ...                        ...   \n", "3503016            0.018605     2.197225                        1.0   \n", "3503017            0.018605     2.197225                        NaN   \n", "3503018            0.018605     2.197225                        1.0   \n", "3503019            0.018605     2.197225                        1.0   \n", "3503020            0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge  \n", "0                  NaN  \n", "1             169.8895  \n", "2                  NaN  \n", "3                  NaN  \n", "4                  NaN  \n", "...                ...  \n", "3503016         3.1424  \n", "3503017            NaN  \n", "3503018        19.6208  \n", "3503019        36.3071  \n", "3503020            NaN  \n", "\n", "[3503021 rows x 44 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# add the productivity data of ['pull_request_success_rate', 'time_to_merge'] to the original combined data based on repo_name and standardized_time_weeks, rememeber there are duplicates in the combined data\n", "combined_data = pd.merge(original_combined_data, productivity_data[['repo_name', 'standardized_time_weeks', 'pull_request_success_rate', 'time_to_merge']], on=['repo_name', 'standardized_time_weeks'], how='left')\n", "combined_data"]}, {"cell_type": "code", "execution_count": 5, "id": "1cbef19d", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "cf518a4c-b617-4b8b-be08-b542fdfff314", "rows": [["pull_request_success_rate", "1688390"], ["time_to_merge", "1993410"]], "shape": {"columns": 1, "rows": 2}}, "text/plain": ["pull_request_success_rate    1688390\n", "time_to_merge                1993410\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# see if there are any empty values in the new columns\n", "combined_data[['pull_request_success_rate', 'time_to_merge']].isnull().sum()\n", "# fillna, 填0\n", "# combined_data[['pull_request_success_rate', 'time_to_merge']] = combined_data[['pull_request_success_rate', 'time_to_merge']].fillna(0)"]}, {"cell_type": "code", "execution_count": 6, "id": "fca8e120", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}], "ref": "ef6ea3bc-880a-4186-948b-a86b252f6f37", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895", "5.141017148795798", null], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "18.3589", "2.9631522620523985", "0.6931471805599453"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "25.7632", "3.287027809575607", "0.6931471805599453"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "14.9636", "2.770311130495723", "0.6931471805599453"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "361.1883", "5.8921642423323215", "0.6931471805599453"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "351.6383", "5.865442885732476", "0.6931471805599453"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.9486", "0.6671111660091038", "0.6931471805599453"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "1.7981", "1.0289406154187903", "0.6931471805599453"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "21.5526", "3.1158503586370414", "0.6931471805599453"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.005", "0.004987541511038968", "0.6931471805599453"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.6445", "0.49743638664697454", "0.6931471805599453"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "49.2922", "3.9178499954986576", "0.6931471805599453"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "91.0883", "4.522747899360907", null], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null]], "shape": {"columns": 46, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "      <td>1.421275</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "      <td>3.026300</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "      <td>3.619184</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 46 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        ...  decelerating       15.0       2.772589    6.751101   \n", "1        ...  decelerating       15.0       2.772589    6.751101   \n", "2        ...  decelerating       15.0       2.772589    6.751101   \n", "3        ...  decelerating       15.0       2.772589    6.751101   \n", "4        ...  decelerating       15.0       2.772589    6.751101   \n", "...      ...           ...        ...            ...         ...   \n", "3503016  ...        steady        1.0       0.693147    6.710523   \n", "3503017  ...        steady        1.0       0.693147    6.710523   \n", "3503018  ...        steady        1.0       0.693147    6.710523   \n", "3503019  ...        steady        1.0       0.693147    6.710523   \n", "3503020  ...        steady        1.0       0.693147    6.710523   \n", "\n", "        log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0                 0.144831     5.433722                        1.0   \n", "1                 0.144831     5.433722                        NaN   \n", "2                 0.144831     5.433722                        NaN   \n", "3                 0.144831     5.433722                        NaN   \n", "4                 0.144831     5.433722                        NaN   \n", "...                    ...          ...                        ...   \n", "3503016           0.018605     2.197225                        1.0   \n", "3503017           0.018605     2.197225                        NaN   \n", "3503018           0.018605     2.197225                        1.0   \n", "3503019           0.018605     2.197225                        1.0   \n", "3503020           0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge  log_time_to_merge  log_pull_request_success_rate  \n", "0                  NaN                NaN                       0.693147  \n", "1             169.8895           5.141017                            NaN  \n", "2                  NaN                NaN                            NaN  \n", "3                  NaN                NaN                            NaN  \n", "4                  NaN                NaN                            NaN  \n", "...                ...                ...                            ...  \n", "3503016         3.1424           1.421275                       0.693147  \n", "3503017            NaN                NaN                            NaN  \n", "3503018        19.6208           3.026300                       0.693147  \n", "3503019        36.3071           3.619184                       0.693147  \n", "3503020            NaN                NaN                            NaN  \n", "\n", "[3503021 rows x 46 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# log = log (x+1) \n", "import numpy as np\n", "combined_data['log_time_to_merge'] = combined_data['time_to_merge'].apply(lambda x: 0 if x == 0 else np.log(x + 1))\n", "combined_data['log_time_to_merge'].isnull().sum()\n", "\n", "combined_data['log_pull_request_success_rate'] = combined_data['pull_request_success_rate'].apply(lambda x: 0 if x == 0 else np.log(x + 1))\n", "combined_data['log_pull_request_success_rate'].isnull().sum()\n", "\n", "\n", "combined_data[['pull_request_success_rate', 'time_to_merge']].isnull().sum()\n", "\n", "combined_data"]}, {"cell_type": "code", "execution_count": null, "id": "7d4dc366", "metadata": {}, "outputs": [], "source": ["# fillna"]}, {"cell_type": "code", "execution_count": 25, "id": "34056727", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "log_newcomers", "rawType": "float64", "type": "float"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "pull_request_success_rate", "rawType": "float64", "type": "float"}, {"name": "time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_time_to_merge", "rawType": "float64", "type": "float"}, {"name": "log_pull_request_success_rate", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "e21df1b4-6713-4a56-bcfc-9bc8ffdbeb99", "rows": [["0", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "111", "-12", "1", "False", "0", "0", "0", "634", "8", "251", "0.0", "6.453624998892692", "2.19722457733622", "5.529429087511423", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["1", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "112", "-11", "1", "False", "0", "0", "0", "634", "8", "258", "0.6931471805599453", "6.453624998892692", "2.19722457733622", "5.556828061699537", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "169.8895", "5.141017148795798", null], ["2", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "113", "-10", "1", "False", "0", "0", "0", "634", "8", "265", "0.0", "6.453624998892692", "2.19722457733622", "5.583496308781699", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["3", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "114", "-9", "1", "False", "0", "0", "0", "642", "8", "272", "0.0", "6.466144724237619", "2.19722457733622", "5.60947179518496", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["4", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "115", "-8", "1", "False", "0", "0", "0", "642", "8", "279", "0.0", "6.466144724237619", "2.19722457733622", "5.634789603169249", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["5", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "116", "-7", "1", "False", "0", "0", "0", "644", "9", "286", "0.6931471805599453", "6.4692503167957724", "2.302585092994046", "5.659482215759621", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "18.3589", "2.9631522620523985", "0.6931471805599453"], ["6", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "117", "-6", "1", "False", "0", "0", "0", "645", "9", "293", "0.0", "6.470799503782602", "2.302585092994046", "5.683579767338681", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["7", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "118", "-5", "1", "False", "0", "0", "0", "656", "9", "300", "0.0", "6.48768401848461", "2.302585092994046", "5.707110264748875", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["8", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "119", "-4", "1", "False", "0", "0", "0", "663", "10", "307", "0.0", "6.498282149476434", "2.3978952727983707", "5.730099782973574", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["9", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "120", "-3", "1", "False", "0", "0", "0", "677", "10", "314", "1.0986122886681098", "6.519147287940395", "2.3978952727983707", "5.752572638825633", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "25.7632", "3.287027809575607", "0.6931471805599453"], ["10", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "121", "-2", "1", "False", "0", "0", "0", "681", "10", "321", "0.0", "6.525029657843462", "2.3978952727983707", "5.7745515455444085", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["11", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "122", "-1", "1", "False", "0", "0", "0", "720", "11", "328", "0.6931471805599453", "6.580639137284949", "2.4849066497880004", "5.796057750765372", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "14.9636", "2.770311130495723", "0.6931471805599453"], ["12", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123", "0", "1", "False", "0", "0", "0", "765", "11", "335", "0.0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "0_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["13", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "124", "1", "1", "True", "0", "1", "1", "768", "12", "342", "0.0", "6.645090969505644", "2.5649493574615367", "5.83773044716594", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["14", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "125", "2", "1", "True", "0", "1", "1", "768", "12", "349", "0.0", "6.645090969505644", "2.5649493574615367", "5.857933154483459", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["15", "01mf02/jaq", "661", "0.0", "-0.0279720279720279", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "126", "3", "1", "True", "0", "1", "1", "768", "12", "356", "0.0", "6.645090969505644", "2.5649493574615367", "5.877735781779639", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["16", "01mf02/jaq", "662", "0.0", "-0.0559440559440559", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "127", "4", "1", "True", "0", "1", "1", "768", "12", "363", "0.0", "6.645090969505644", "2.5649493574615367", "5.8971538676367405", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["17", "01mf02/jaq", "663", "0.0", "-0.0384615384615384", "0.25", "-0.6931471805599453", "0.3909913151594318", "0.4567863831370551", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "128", "5", "1", "True", "0", "1", "1", "768", "12", "370", "0.0", "6.645090969505644", "2.5649493574615367", "5.916202062607435", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["18", "01mf02/jaq", "664", "0.0", "-0.0594405594405594", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "129", "6", "1", "True", "0", "1", "1", "768", "12", "377", "0.0", "6.645090969505644", "2.5649493574615367", "5.934894195619588", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["19", "01mf02/jaq", "665", "0.0", "-0.0804195804195804", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "130", "7", "1", "True", "0", "1", "1", "768", "12", "384", "0.0", "6.645090969505644", "2.5649493574615367", "5.953243334287785", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "0.0", null, null, "0.0"], ["20", "01mf02/jaq", "666", "0.0", "-0.1013986013986013", "0.25", "0.0", "0.5621765008857981", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "131", "8", "1", "True", "0", "1", "1", "768", "12", "391", "0.0", "6.645090969505644", "2.5649493574615367", "5.971261839790462", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["21", "01mf02/jaq", "667", "0.0", "-0.0314685314685314", "0.0833333333333333", "-1.0986122886681098", "0.2659480223541233", "0.4771282169139496", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "132", "9", "1", "True", "0", "1", "1", "769", "13", "398", "0.0", "6.646390514847729", "2.6390573296152584", "5.988961416889864", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["22", "01mf02/jaq", "668", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "133", "10", "1", "True", "0", "1", "1", "777", "13", "405", "0.0", "6.656726524178391", "2.6390573296152584", "6.0063531596017325", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["23", "01mf02/jaq", "669", "7.0", "0.2692307692307692", "0.5833333333333334", "1.3862943611198904", "0.8775711182727681", "0.6918263816888619", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "134", "11", "1", "True", "0", "1", "1", "799", "14", "412", "2.079441541679836", "6.684611727667927", "2.70805020110221", "6.023447592961033", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "361.1883", "5.8921642423323215", "0.6931471805599453"], ["24", "01mf02/jaq", "670", "1.0", "0.2587412587412587", "0.6666666666666666", "0.6931471805599452", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Rust", "535", "135", "12", "1", "True", "0", "1", "1", "825", "14", "419", "0.6931471805599453", "6.716594773520978", "2.70805020110221", "6.040254711277414", "1_0", "1_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "351.6383", "5.865442885732476", "0.6931471805599453"], ["25", "Project-Babble/ProjectBabble", "647", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "29", "-12", "0", "False", "0", "0", "0", "36", "4", "174", "0.0", "3.610917912644224", "1.6094379124341005", "5.1647859739235145", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["26", "Project-Babble/ProjectBabble", "648", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "30", "-11", "0", "False", "0", "0", "0", "36", "4", "181", "0.0", "3.610917912644224", "1.6094379124341005", "5.204006687076795", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["27", "Project-Babble/ProjectBabble", "649", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "31", "-10", "0", "False", "0", "0", "0", "36", "4", "188", "0.0", "3.610917912644224", "1.6094379124341005", "5.241747015059643", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["28", "Project-Babble/ProjectBabble", "650", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "32", "-9", "0", "False", "0", "0", "0", "36", "4", "195", "0.0", "3.610917912644224", "1.6094379124341005", "5.278114659230517", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["29", "Project-Babble/ProjectBabble", "651", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "33", "-8", "0", "False", "0", "0", "0", "36", "4", "202", "0.0", "3.610917912644224", "1.6094379124341005", "5.313205979041787", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["30", "Project-Babble/ProjectBabble", "652", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "34", "-7", "0", "False", "0", "0", "0", "36", "4", "209", "0.0", "3.610917912644224", "1.6094379124341005", "5.3471075307174685", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["31", "Project-Babble/ProjectBabble", "653", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "35", "-6", "0", "False", "0", "0", "0", "36", "4", "216", "0.0", "3.610917912644224", "1.6094379124341005", "5.37989735354046", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["32", "Project-Babble/ProjectBabble", "654", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "36", "-5", "0", "False", "0", "0", "0", "36", "4", "223", "0.0", "3.610917912644224", "1.6094379124341005", "5.41164605185504", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["33", "Project-Babble/ProjectBabble", "655", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "37", "-4", "0", "False", "0", "0", "0", "41", "5", "230", "0.6931471805599453", "3.737669618283368", "1.791759469228055", "5.442417710521793", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.9486", "0.6671111660091038", "0.6931471805599453"], ["34", "Project-Babble/ProjectBabble", "656", "4.0", "0.1853146853146853", "0.4166666666666667", "1.6094379124341005", "0.8835107617296891", "0.6616373014898288", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "38", "-3", "0", "False", "0", "0", "0", "49", "6", "237", "1.6094379124341005", "3.912023005428146", "1.9459101490553128", "5.472270673671475", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "1.7981", "1.0289406154187903", "0.6931471805599453"], ["35", "Project-Babble/ProjectBabble", "657", "0.0", "0.1503496503496503", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "39", "-2", "0", "False", "0", "0", "0", "50", "6", "244", "0.0", "3.9318256327243257", "1.9459101490553128", "5.501258210544727", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["36", "Project-Babble/ProjectBabble", "658", "0.0", "0.1153846153846153", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "40", "-1", "0", "False", "0", "0", "0", "54", "6", "251", "0.0", "4.007333185232471", "1.9459101490553128", "5.529429087511423", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["37", "Project-Babble/ProjectBabble", "659", "0.0", "0.0804195804195804", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "41", "0", "0", "False", "0", "0", "0", "56", "6", "258", "0.0", "4.04305126783455", "1.9459101490553128", "5.556828061699537", "0_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["38", "Project-Babble/ProjectBabble", "660", "0.0", "0.0454545454545454", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "42", "1", "0", "True", "0", "1", "0", "57", "6", "265", "0.0", "4.060443010546419", "1.9459101490553128", "5.583496308781699", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["39", "Project-Babble/ProjectBabble", "661", "0.0", "0.0104895104895104", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "43", "2", "0", "True", "0", "1", "0", "60", "6", "272", "0.0", "4.110873864173311", "1.9459101490553128", "5.60947179518496", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["40", "Project-Babble/ProjectBabble", "662", "2.0", "0.0524475524475524", "0.5833333333333334", "1.0986122886681098", "0.843161991687051", "0.6549471989808647", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "44", "3", "0", "True", "0", "1", "0", "61", "6", "279", "1.0986122886681098", "4.127134385045092", "1.9459101490553128", "5.634789603169249", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "21.5526", "3.1158503586370414", "0.6931471805599453"], ["41", "Project-Babble/ProjectBabble", "663", "1.0", "0.0419580419580419", "0.6666666666666666", "0.6931471805599454", "0.7957294413470832", "0.6135117904356906", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "45", "4", "0", "True", "0", "1", "0", "64", "6", "286", "0.6931471805599453", "4.174387269895637", "1.9459101490553128", "5.659482215759621", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.005", "0.004987541511038968", "0.6931471805599453"], ["42", "Project-Babble/ProjectBabble", "664", "0.0", "-0.0139860139860139", "0.6666666666666666", "0.0", "0.6607563687658172", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "46", "5", "0", "True", "0", "1", "0", "69", "6", "293", "0.0", "4.248495242049359", "1.9459101490553128", "5.683579767338681", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["43", "Project-Babble/ProjectBabble", "665", "4.0", "0.0839160839160839", "1.0", "1.6094379124341005", "0.9314665231953944", "0.8333333333333334", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "47", "6", "0", "True", "0", "1", "0", "98", "6", "300", "1.6094379124341005", "4.59511985013459", "1.9459101490553128", "5.707110264748875", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "0.6445", "0.49743638664697454", "0.6931471805599453"], ["44", "Project-Babble/ProjectBabble", "666", "0.0", "0.0", "1.0", "0.0", "0.7310585786300049", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "48", "7", "0", "True", "0", "1", "0", "98", "6", "307", "0.0", "4.59511985013459", "1.9459101490553128", "5.730099782973574", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["45", "Project-Babble/ProjectBabble", "667", "0.0", "-0.0384615384615384", "0.9166666666666666", "-0.6931471805599453", "0.5556483770214198", "0.3462905293130085", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "49", "8", "0", "True", "0", "1", "0", "126", "6", "314", "0.0", "4.844187086458591", "1.9459101490553128", "5.752572638825633", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", null, null, "0.6931471805599453"], ["46", "Project-Babble/ProjectBabble", "668", "1.0", "0.1048951048951049", "0.6666666666666666", "-0.9162907318741552", "0.43791603164132", "0.3518629339894399", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "50", "9", "0", "True", "0", "1", "0", "129", "6", "321", "0.6931471805599453", "4.867534450455582", "1.9459101490553128", "5.7745515455444085", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", "1.0", "49.2922", "3.9178499954986576", "0.6931471805599453"], ["47", "Project-Babble/ProjectBabble", "669", "1.0", "0.0874125874125874", "0.75", "0.6931471805599453", "0.8089415373228858", "0.627115119175411", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "51", "10", "0", "True", "0", "1", "0", "130", "6", "328", "0.6931471805599453", "4.875197323201151", "1.9459101490553128", "5.796057750765372", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, "91.0883", "4.522747899360907", null], ["48", "Project-Babble/ProjectBabble", "670", "0.0", "0.0244755244755244", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "52", "11", "0", "True", "0", "1", "0", "130", "6", "335", "0.0", "4.875197323201151", "1.9459101490553128", "5.817111159963204", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null], ["49", "Project-Babble/ProjectBabble", "671", "0.0", "-0.0384615384615384", "0.75", "0.0", "0.679178699175393", "0.5", "0", "854.0", "0.1558441558441558", "228.0", "0.0", "0.0", "Python", "618", "53", "12", "0", "True", "0", "1", "0", "130", "6", "342", "0.0", "4.875197323201151", "1.9459101490553128", "5.83773044716594", "1_0", "0_0", "6.641182169740591", "2.4849066497880004", "5.817111159963204", "Rust", "decelerating", "15.0", "2.772588722239781", "6.75110146893676", "0.144830947878456", "5.43372200355424", null, null, null, null]], "shape": {"columns": 46, "rows": 3503021}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>...</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>log_newcomers</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>log_time_to_merge</th>\n", "      <th>log_pull_request_success_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>646</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>647</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.693147</td>\n", "      <td>0.684921</td>\n", "      <td>0.514437</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>169.8895</td>\n", "      <td>5.141017</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>648</td>\n", "      <td>0.0</td>\n", "      <td>0.031469</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>649</td>\n", "      <td>0.0</td>\n", "      <td>0.024476</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>650</td>\n", "      <td>0.0</td>\n", "      <td>0.017483</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>854.0</td>\n", "      <td>...</td>\n", "      <td>decelerating</td>\n", "      <td>15.0</td>\n", "      <td>2.772589</td>\n", "      <td>6.751101</td>\n", "      <td>0.144831</td>\n", "      <td>5.433722</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503016</th>\n", "      <td>eminence/procfs</td>\n", "      <td>483</td>\n", "      <td>3.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.386294</td>\n", "      <td>0.915776</td>\n", "      <td>0.800000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>3.1424</td>\n", "      <td>1.421275</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503017</th>\n", "      <td>eminence/procfs</td>\n", "      <td>484</td>\n", "      <td>0.0</td>\n", "      <td>-0.083916</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.731059</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503018</th>\n", "      <td>eminence/procfs</td>\n", "      <td>485</td>\n", "      <td>4.0</td>\n", "      <td>-0.013986</td>\n", "      <td>1.333333</td>\n", "      <td>1.609438</td>\n", "      <td>0.949921</td>\n", "      <td>0.895287</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>19.6208</td>\n", "      <td>3.026300</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503019</th>\n", "      <td>eminence/procfs</td>\n", "      <td>486</td>\n", "      <td>2.0</td>\n", "      <td>-0.003497</td>\n", "      <td>1.416667</td>\n", "      <td>0.405465</td>\n", "      <td>0.860822</td>\n", "      <td>0.639780</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>1.0</td>\n", "      <td>36.3071</td>\n", "      <td>3.619184</td>\n", "      <td>0.693147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3503020</th>\n", "      <td>eminence/procfs</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>0.059441</td>\n", "      <td>1.083333</td>\n", "      <td>-1.609438</td>\n", "      <td>0.371426</td>\n", "      <td>0.148862</td>\n", "      <td>0</td>\n", "      <td>820.0</td>\n", "      <td>...</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>0.693147</td>\n", "      <td>6.710523</td>\n", "      <td>0.018605</td>\n", "      <td>2.197225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3503021 rows × 46 columns</p>\n", "</div>"], "text/plain": ["               repo_name  standardized_time_weeks  pr_throughput  \\\n", "0             01mf02/jaq                      646            0.0   \n", "1             01mf02/jaq                      647            1.0   \n", "2             01mf02/jaq                      648            0.0   \n", "3             01mf02/jaq                      649            0.0   \n", "4             01mf02/jaq                      650            0.0   \n", "...                  ...                      ...            ...   \n", "3503016  eminence/procfs                      483            3.0   \n", "3503017  eminence/procfs                      484            0.0   \n", "3503018  eminence/procfs                      485            4.0   \n", "3503019  eminence/procfs                      486            2.0   \n", "3503020  eminence/procfs                      487            0.0   \n", "\n", "         rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0             0.000000      0.000000                0.000000   \n", "1             0.038462      0.083333                0.693147   \n", "2             0.031469      0.083333                0.000000   \n", "3             0.024476      0.083333                0.000000   \n", "4             0.017483      0.083333                0.000000   \n", "...                ...           ...                     ...   \n", "3503016       0.000000      1.000000                1.386294   \n", "3503017      -0.083916      1.000000                0.000000   \n", "3503018      -0.013986      1.333333                1.609438   \n", "3503019      -0.003497      1.416667                0.405465   \n", "3503020       0.059441      1.083333               -1.609438   \n", "\n", "         feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                  0.500000                 0.500000             0   854.0   \n", "1                  0.684921                 0.514437             0   854.0   \n", "2                  0.520821                 0.500000             0   854.0   \n", "3                  0.520821                 0.500000             0   854.0   \n", "4                  0.520821                 0.500000             0   854.0   \n", "...                     ...                      ...           ...     ...   \n", "3503016            0.915776                 0.800000             0   820.0   \n", "3503017            0.731059                 0.500000             0   820.0   \n", "3503018            0.949921                 0.895287             0   820.0   \n", "3503019            0.860822                 0.639780             0   820.0   \n", "3503020            0.371426                 0.148862             0   820.0   \n", "\n", "         ...  growth_phase  newcomers  log_newcomers  log_tenure  \\\n", "0        ...  decelerating       15.0       2.772589    6.751101   \n", "1        ...  decelerating       15.0       2.772589    6.751101   \n", "2        ...  decelerating       15.0       2.772589    6.751101   \n", "3        ...  decelerating       15.0       2.772589    6.751101   \n", "4        ...  decelerating       15.0       2.772589    6.751101   \n", "...      ...           ...        ...            ...         ...   \n", "3503016  ...        steady        1.0       0.693147    6.710523   \n", "3503017  ...        steady        1.0       0.693147    6.710523   \n", "3503018  ...        steady        1.0       0.693147    6.710523   \n", "3503019  ...        steady        1.0       0.693147    6.710523   \n", "3503020  ...        steady        1.0       0.693147    6.710523   \n", "\n", "        log_commit_percent  log_commits  pull_request_success_rate  \\\n", "0                 0.144831     5.433722                        1.0   \n", "1                 0.144831     5.433722                        NaN   \n", "2                 0.144831     5.433722                        NaN   \n", "3                 0.144831     5.433722                        NaN   \n", "4                 0.144831     5.433722                        NaN   \n", "...                    ...          ...                        ...   \n", "3503016           0.018605     2.197225                        1.0   \n", "3503017           0.018605     2.197225                        NaN   \n", "3503018           0.018605     2.197225                        1.0   \n", "3503019           0.018605     2.197225                        1.0   \n", "3503020           0.018605     2.197225                        NaN   \n", "\n", "         time_to_merge  log_time_to_merge  log_pull_request_success_rate  \n", "0                  NaN                NaN                       0.693147  \n", "1             169.8895           5.141017                            NaN  \n", "2                  NaN                NaN                            NaN  \n", "3                  NaN                NaN                            NaN  \n", "4                  NaN                NaN                            NaN  \n", "...                ...                ...                            ...  \n", "3503016         3.1424           1.421275                       0.693147  \n", "3503017            NaN                NaN                            NaN  \n", "3503018        19.6208           3.026300                       0.693147  \n", "3503019        36.3071           3.619184                       0.693147  \n", "3503020            NaN                NaN                            NaN  \n", "\n", "[3503021 rows x 46 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_data"]}, {"cell_type": "code", "execution_count": 7, "id": "6c965f6c", "metadata": {}, "outputs": [], "source": ["combined_data.to_csv(\"../result/did_result_20250408/compiled_data_test_with_features_and_growth_phase_and_newcomers_with_productivity.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}