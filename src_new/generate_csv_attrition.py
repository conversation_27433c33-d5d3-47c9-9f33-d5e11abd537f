import logging
import pandas as pd
from pymongo import MongoClient

# Configure logging
log_file = '../logs/attrition_csv_generation.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def generate_attrition_csv():
    """
    Extract attrition data from MongoDB and generate a CSV file.
    """
    # Connect to MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["repo_breaks_attritions"]

    logging.info("Fetching attrition data from MongoDB...")

    # Fetch all data from the collection
    repos = list(collection.find({}))

    if not repos:
        logging.warning("No attrition data found in the collection.")
        return

    # Extract attrition data for the CSV
    rows = []
    for repo in repos:
        repo_id = repo.get("_id")
        repo_name = repo.get("repo_name")
        attritions = repo.get("attritions", [])
        for attr in attritions:
            rows.append({
                "repo_id": repo_id,
                "repo_name": repo_name,
                "attrition": "attrition",
                "attrition_date": attr.get("attrition_time"),
                "attrition_developer": attr.get("dev_login")
            })

    # Convert to DataFrame and save as CSV
    df = pd.DataFrame(rows)
    output_csv_path = "../data/attritions_20250227.csv"
    try:
        df.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"Attrition CSV generated at {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to save attrition CSV: {e}")

if __name__ == "__main__":
    generate_attrition_csv()
