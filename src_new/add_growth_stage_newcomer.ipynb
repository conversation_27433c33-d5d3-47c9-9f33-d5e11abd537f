{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 20250226 Add control variables in attritions into the compiled data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "compiled_data = pd.read_csv(\"../result/did_result_20250227/compiled_data_test_with_features.csv\")\n", "attritions = pd.read_csv(\"../result/attritions_aggregated_to_burst_with_growth_phase_and_newcomers.csv\")\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "2c88514b-1626-412d-9bee-71ee1de59fc1", "rows": [["0", "10up/autoshare-for-twitter", "486", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "49", "-12", "1", "False", "0", "0", "0", "197", "5", "342", "0.0", "5.288267030694535", "1.791759469228055", "5.83773044716594", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["1", "10up/autoshare-for-twitter", "487", "0.0", "1.0", "2.0", "-0.0349650349650349", "0.5", "-1.09861228866811", "0.3546612443924433", "0.3660254037844386", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "50", "-11", "1", "False", "0", "0", "0", "197", "5", "349", "0.0", "5.288267030694535", "1.791759469228055", "5.857933154483459", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["2", "10up/autoshare-for-twitter", "488", "4.0", "1.0", "2.0", "0.1223776223776223", "0.75", "0.9162907318741548", "0.8410806526182262", "0.6653477824119316", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "51", "-10", "1", "False", "0", "0", "0", "209", "5", "356", "1.6094379124341005", "5.3471075307174685", "1.791759469228055", "5.877735781779639", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["3", "10up/autoshare-for-twitter", "489", "1.0", "1.0", "2.0", "0.0979020979020979", "0.8333333333333334", "0.6931471805599448", "0.8214907876837801", "0.6405201949797534", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "52", "-9", "1", "False", "0", "0", "0", "210", "5", "363", "0.6931471805599453", "5.351858133476067", "1.791759469228055", "5.8971538676367405", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["4", "10up/autoshare-for-twitter", "490", "3.0", "1.0", "2.0", "0.1433566433566433", "1.0833333333333333", "1.38629436111989", "0.921984989635266", "0.8178456006794831", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "53", "-8", "1", "False", "0", "0", "0", "220", "5", "370", "1.3862943611198906", "5.3981627015177525", "1.791759469228055", "5.916202062607435", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["5", "10up/autoshare-for-twitter", "491", "3.0", "1.0", "2.0", "0.2587412587412587", "1.1666666666666667", "0.2876820724517805", "0.8106668070686921", "0.5831283861446792", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "54", "-7", "1", "False", "0", "0", "0", "249", "6", "377", "1.3862943611198906", "5.521460917862246", "1.9459101490553128", "5.934894195619588", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["6", "10up/autoshare-for-twitter", "492", "0.0", "1.0", "2.0", "0.1608391608391608", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "55", "-6", "1", "False", "0", "0", "0", "255", "6", "384", "0.0", "5.545177444479562", "1.9459101490553128", "5.953243334287784", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["7", "10up/autoshare-for-twitter", "493", "0.0", "1.0", "2.0", "0.0629370629370629", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "56", "-5", "1", "False", "0", "0", "0", "259", "6", "391", "0.0", "5.560681631015528", "1.9459101490553128", "5.971261839790462", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["8", "10up/autoshare-for-twitter", "494", "1.0", "1.0", "2.0", "0.0034965034965034", "1.25", "0.6931471805599451", "0.8746974870756378", "0.7040031411428227", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "57", "-4", "1", "False", "0", "0", "0", "261", "6", "398", "0.6931471805599453", "5.568344503761097", "1.9459101490553128", "5.988961416889863", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["9", "10up/autoshare-for-twitter", "495", "1.0", "1.0", "2.0", "0.0279720279720279", "1.1666666666666667", "-0.4054651081081645", "0.6816145482921148", "0.3838963480989594", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "58", "-3", "1", "False", "0", "0", "0", "275", "6", "405", "0.6931471805599453", "5.62040086571715", "1.9459101490553128", "6.0063531596017325", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["10", "10up/autoshare-for-twitter", "496", "0.0", "1.0", "2.0", "-0.0244755244755244", "1.0833333333333333", "-0.6931471805599454", "0.5963275109839462", "0.3206231694796373", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "59", "-2", "1", "False", "0", "0", "0", "306", "7", "412", "0.0", "5.726847747587197", "2.079441541679836", "6.023447592961032", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["11", "10up/autoshare-for-twitter", "497", "5.0", "1.0", "2.0", "0.0769230769230769", "1.5", "1.791759469228055", "0.964145027597638", "0.9362933095037254", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "60", "-1", "1", "False", "0", "0", "0", "309", "7", "419", "1.791759469228055", "5.736572297479192", "2.079441541679836", "6.040254711277414", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["12", "10up/autoshare-for-twitter", "498", "2.0", "1.0", "2.0", "0.0279720279720279", "1.6666666666666667", "1.0986122886681096", "0.9407704701088077", "0.8618832502903921", "1", "257.0", "0.2171837708830549", "182.0", "7.0", "1.0", "PHP", "437", "61", "0", "1", "False", "0", "0", "0", "336", "8", "426", "1.0986122886681096", "5.820082930352362", "2.197224577336219", "6.056784013228624", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["13", "10up/autoshare-for-twitter", "499", "1.0", "1.0", "2.0", "-0.0734265734265734", "1.75", "0.6931471805599452", "0.9200588708986858", "0.7708306705345104", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "62", "1", "1", "True", "0", "1", "1", "336", "8", "433", "0.6931471805599453", "5.820082930352362", "2.197224577336219", "6.073044534100404", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["14", "10up/autoshare-for-twitter", "500", "1.0", "1.0", "2.0", "0.0", "1.5", "-0.9162907318741552", "0.6419204615368317", "0.2019040735186648", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "63", "2", "1", "True", "0", "1", "1", "337", "8", "440", "0.6931471805599453", "5.823045895483019", "2.197224577336219", "6.089044875446846", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["15", "10up/autoshare-for-twitter", "501", "0.0", "1.0", "2.0", "-0.0804195804195804", "1.4166666666666667", "-0.6931471805599453", "0.6733815605777215", "0.2725033461986623", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "64", "3", "1", "True", "0", "1", "1", "337", "8", "447", "0.0", "5.823045895483019", "2.197224577336219", "6.104793232414985", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["16", "10up/autoshare-for-twitter", "502", "0.0", "1.0", "2.0", "-0.0629370629370629", "1.1666666666666667", "-1.3862943611198906", "0.4453127259526225", "0.1655715707900131", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "65", "4", "1", "True", "0", "1", "1", "337", "8", "454", "0.0", "5.823045895483019", "2.197224577336219", "6.12029741895095", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["17", "10up/autoshare-for-twitter", "503", "0.0", "1.0", "2.0", "-0.0244755244755244", "0.9166666666666666", "-1.3862943611198906", "0.3847043671232542", "0.2191254981927748", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "66", "5", "1", "True", "0", "1", "1", "337", "8", "461", "0.0", "5.823045895483019", "2.197224577336219", "6.135564891081739", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["18", "10up/autoshare-for-twitter", "504", "0.0", "1.0", "2.0", "-0.1013986013986013", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "67", "6", "1", "True", "0", "1", "1", "337", "8", "468", "0.0", "5.823045895483019", "2.197224577336219", "6.150602768446279", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["19", "10up/autoshare-for-twitter", "505", "0.0", "1.0", "2.0", "-0.1783216783216783", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "68", "7", "1", "True", "0", "1", "1", "337", "8", "475", "0.0", "5.823045895483019", "2.197224577336219", "6.16541785423142", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["20", "10up/autoshare-for-twitter", "506", "0.0", "1.0", "2.0", "-0.2097902097902098", "0.8333333333333334", "-0.6931471805599453", "0.5349892557559008", "0.3594798050202465", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "69", "8", "1", "True", "0", "1", "1", "337", "8", "482", "0.0", "5.823045895483019", "2.197224577336219", "6.180016653652572", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["21", "10up/autoshare-for-twitter", "507", "0.0", "1.0", "2.0", "-0.2342657342657342", "0.75", "-0.6931471805599453", "0.5142093777192814", "0.3728848808245891", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "70", "9", "1", "True", "0", "1", "1", "337", "8", "489", "0.0", "5.823045895483019", "2.197224577336219", "6.194405391104672", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["22", "10up/autoshare-for-twitter", "508", "0.0", "1.0", "2.0", "-0.2972027972027972", "0.75", "0.0", "0.679178699175393", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "71", "10", "1", "True", "0", "1", "1", "337", "8", "496", "0.0", "5.823045895483019", "2.197224577336219", "6.208590026096629", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["23", "10up/autoshare-for-twitter", "509", "0.0", "1.0", "2.0", "-0.1328671328671328", "0.3333333333333333", "-1.791759469228055", "0.1887081616597619", "0.3549723794374981", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "72", "11", "1", "True", "0", "1", "1", "337", "8", "503", "0.0", "5.823045895483019", "2.197224577336219", "6.222576268071369", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["24", "10up/autoshare-for-twitter", "510", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.1666666666666666", "-1.0986122886681096", "0.2825301567477209", "0.4543519511761902", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "73", "12", "1", "True", "0", "1", "1", "337", "8", "510", "0.0", "5.823045895483019", "2.197224577336219", "6.236369590203704", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["25", "AdaptiveCpp/AdaptiveCpp", "486", "0.0", "3.0", "6.0", "-0.0734265734265734", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "77", "-12", "0", "False", "0", "0", "0", "387", "9", "359", "0.0", "5.961005339623274", "2.302585092994045", "5.886104031450156", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["26", "AdaptiveCpp/AdaptiveCpp", "487", "1.0", "3.0", "6.0", "-0.0664335664335664", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "78", "-11", "0", "False", "0", "0", "0", "388", "9", "366", "0.6931471805599453", "5.963579343618446", "2.302585092994045", "5.90536184805457", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["27", "AdaptiveCpp/AdaptiveCpp", "488", "0.0", "3.0", "6.0", "-0.0524475524475524", "0.75", "-1.09861228866811", "0.4137189778658776", "0.3049238750315606", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "79", "-10", "0", "False", "0", "0", "0", "389", "9", "373", "0.0", "5.966146739123692", "2.302585092994045", "5.924255797414531", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["28", "AdaptiveCpp/AdaptiveCpp", "489", "0.0", "3.0", "6.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "80", "-9", "0", "False", "0", "0", "0", "389", "9", "380", "0.0", "5.966146739123692", "2.302585092994045", "5.9427993751267", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["29", "AdaptiveCpp/AdaptiveCpp", "490", "2.0", "3.0", "6.0", "-0.0489510489510489", "0.8333333333333334", "1.0986122886681091", "0.8734646144545724", "0.7141264037070777", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "81", "-8", "0", "False", "0", "0", "0", "391", "9", "387", "1.0986122886681096", "5.971261839790462", "2.302585092994045", "5.961005339623274", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["30", "AdaptiveCpp/AdaptiveCpp", "491", "2.0", "3.0", "6.0", "0.0489510489510489", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "82", "-7", "0", "False", "0", "0", "0", "394", "10", "394", "1.0986122886681096", "5.978885764901122", "2.3978952727983707", "5.978885764901122", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["31", "AdaptiveCpp/AdaptiveCpp", "492", "1.0", "3.0", "6.0", "0.0629370629370629", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "83", "-6", "0", "False", "0", "0", "0", "394", "10", "401", "0.6931471805599453", "5.978885764901122", "2.3978952727983707", "5.996452088619021", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["32", "AdaptiveCpp/AdaptiveCpp", "493", "1.0", "3.0", "6.0", "0.0314685314685314", "0.9166666666666666", "0.6931471805599448", "0.8333855399562498", "0.6537094706869915", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "84", "-5", "0", "False", "0", "0", "0", "396", "11", "408", "0.6931471805599453", "5.98393628068719", "2.4849066497880004", "6.013715156042801", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["33", "AdaptiveCpp/AdaptiveCpp", "494", "1.0", "3.0", "6.0", "0.0384615384615384", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "85", "-4", "0", "False", "0", "0", "0", "400", "11", "415", "0.6931471805599453", "5.993961427306569", "2.4849066497880004", "6.030685260261263", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["34", "AdaptiveCpp/AdaptiveCpp", "495", "4.0", "3.0", "6.0", "0.1608391608391608", "1.1666666666666667", "0.9162907318741546", "0.889235659522009", "0.7444078107515406", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "86", "-3", "0", "False", "0", "0", "0", "411", "12", "422", "1.6094379124341005", "6.021023349349526", "2.5649493574615367", "6.0473721790462776", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["35", "AdaptiveCpp/AdaptiveCpp", "496", "4.0", "3.0", "6.0", "0.3076923076923077", "1.3333333333333333", "0.5108256237659902", "0.8634398378798575", "0.6639843473284657", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "87", "-2", "0", "False", "0", "0", "0", "419", "13", "429", "1.6094379124341005", "6.040254711277414", "2.6390573296152584", "6.063785208687608", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["36", "AdaptiveCpp/AdaptiveCpp", "497", "2.0", "3.0", "6.0", "0.2727272727272727", "1.5", "1.0986122886681091", "0.9307722154980688", "0.8386095222035911", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "88", "-1", "0", "False", "0", "0", "0", "423", "13", "436", "1.0986122886681096", "6.049733455231958", "2.6390573296152584", "6.07993319509559", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["37", "AdaptiveCpp/AdaptiveCpp", "498", "2.0", "3.0", "6.0", "0.2237762237762237", "1.6666666666666667", "1.0986122886681091", "0.9407704701088077", "0.861883250290392", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "89", "0", "0", "False", "0", "0", "0", "427", "13", "443", "1.0986122886681096", "6.059123195581797", "2.6390573296152584", "6.095824562432225", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["38", "AdaptiveCpp/AdaptiveCpp", "499", "0.0", "3.0", "6.0", "0.1293706293706293", "1.5833333333333333", "-0.693147180559946", "0.7089285864699496", "0.2502117946664587", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "90", "1", "0", "True", "0", "1", "0", "430", "13", "450", "0.0", "6.066108090103747", "2.6390573296152584", "6.111467339502678", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["39", "AdaptiveCpp/AdaptiveCpp", "500", "0.0", "3.0", "6.0", "-0.0034965034965034", "1.5833333333333333", "-6.661338147750939e-16", "0.829676081356154", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "91", "2", "0", "True", "0", "1", "0", "431", "13", "457", "0.0", "6.06842558824411", "2.6390573296152584", "6.1268691841141845", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["40", "AdaptiveCpp/AdaptiveCpp", "501", "3.0", "3.0", "6.0", "-0.0209790209790209", "1.8333333333333333", "1.38629436111989", "0.9615662577192609", "0.927003081517424", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "92", "3", "0", "True", "0", "1", "0", "433", "13", "464", "1.3862943611198906", "6.073044534100404", "2.6390573296152584", "6.142037405587356", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["41", "AdaptiveCpp/AdaptiveCpp", "502", "3.0", "3.0", "6.0", "0.0314685314685314", "1.9166666666666667", "0.2876820724517803", "0.9006393499233735", "0.634458169632737", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "93", "4", "0", "True", "0", "1", "0", "437", "13", "471", "1.3862943611198906", "6.0822189103764455", "2.6390573296152584", "6.156978985585555", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["42", "AdaptiveCpp/AdaptiveCpp", "503", "1.0", "3.0", "6.0", "0.0", "1.8333333333333333", "-0.4054651081081649", "0.8065689432388163", "0.3222714364606489", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "94", "5", "0", "True", "0", "1", "0", "437", "13", "478", "0.6931471805599453", "6.0822189103764455", "2.6390573296152584", "6.171700597410915", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["43", "AdaptiveCpp/AdaptiveCpp", "504", "0.0", "3.0", "6.0", "-0.1083916083916083", "1.75", "-0.6931471805599458", "0.7420886557878206", "0.2291693294654893", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "95", "6", "0", "True", "0", "1", "0", "438", "13", "485", "0.0", "6.084499413075172", "2.6390573296152584", "6.186208623900494", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["44", "AdaptiveCpp/AdaptiveCpp", "505", "0.0", "3.0", "6.0", "-0.2097902097902098", "1.6666666666666667", "-0.6931471805599457", "0.7258204499338335", "0.2395323119764419", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "96", "7", "0", "True", "0", "1", "0", "438", "13", "492", "0.0", "6.084499413075172", "2.6390573296152584", "6.20050917404269", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["45", "AdaptiveCpp/AdaptiveCpp", "506", "0.0", "3.0", "6.0", "-0.3041958041958042", "1.5833333333333333", "-0.6931471805599457", "0.7089285864699497", "0.2502117946664588", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "97", "8", "0", "True", "0", "1", "0", "439", "14", "499", "0.0", "6.0867747269123065", "2.70805020110221", "6.214608098422191", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["46", "AdaptiveCpp/AdaptiveCpp", "507", "2.0", "3.0", "6.0", "-0.1783216783216783", "1.4166666666666667", "-0.5108256237659912", "0.7121483581398508", "0.3265854170022765", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "98", "9", "0", "True", "0", "1", "0", "442", "15", "506", "1.0986122886681096", "6.093569770045136", "2.772588722239781", "6.228511003591183", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["47", "AdaptiveCpp/AdaptiveCpp", "508", "0.0", "3.0", "6.0", "-0.1153846153846153", "1.0833333333333333", "-1.609437912434101", "0.371425890455925", "0.1488615992206787", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "99", "10", "0", "True", "0", "1", "0", "443", "16", "513", "0.0", "6.095824562432225", "2.833213344056216", "6.242223265455165", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["48", "AdaptiveCpp/AdaptiveCpp", "509", "1.0", "3.0", "6.0", "-0.0769230769230769", "1.0", "-0.4054651081081649", "0.6444049826448044", "0.3999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "100", "11", "0", "True", "0", "1", "0", "444", "16", "520", "0.6931471805599453", "6.09807428216624", "2.833213344056216", "6.255750041753367", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"], ["49", "AdaptiveCpp/AdaptiveCpp", "510", "2.0", "3.0", "6.0", "0.0069930069930069", "1.0", "-5.551115123125785e-16", "0.7310585786300048", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "101", "12", "0", "True", "0", "1", "0", "446", "16", "527", "1.0986122886681096", "6.102558594613568", "2.833213344056216", "6.269096283706261", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP"]], "shape": {"columns": 42, "rows": 203912}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "      <th>outlier</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>486</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.069930</td>\n", "      <td>0.666667</td>\n", "      <td>-6.931472e-01</td>\n", "      <td>0.493380</td>\n", "      <td>0.386488</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.034965</td>\n", "      <td>0.500000</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.354661</td>\n", "      <td>0.366025</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>488</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.122378</td>\n", "      <td>0.750000</td>\n", "      <td>9.162907e-01</td>\n", "      <td>0.841081</td>\n", "      <td>0.665348</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>489</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.097902</td>\n", "      <td>0.833333</td>\n", "      <td>6.931472e-01</td>\n", "      <td>0.821491</td>\n", "      <td>0.640520</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>490</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.143357</td>\n", "      <td>1.083333</td>\n", "      <td>1.386294e+00</td>\n", "      <td>0.921985</td>\n", "      <td>0.817846</td>\n", "      <td>...</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>0</td>\n", "      <td>5.552960</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203907</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.388112</td>\n", "      <td>3.416667</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.910361</td>\n", "      <td>0.022897</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203908</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.038462</td>\n", "      <td>2.416667</td>\n", "      <td>-1.609438e+00</td>\n", "      <td>0.691519</td>\n", "      <td>0.020046</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203909</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.402098</td>\n", "      <td>2.916667</td>\n", "      <td>7.884574e-01</td>\n", "      <td>0.975993</td>\n", "      <td>0.908849</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203910</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.325175</td>\n", "      <td>2.916667</td>\n", "      <td>-6.661338e-16</td>\n", "      <td>0.948664</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203911</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>563</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.234266</td>\n", "      <td>3.250000</td>\n", "      <td>1.609438e+00</td>\n", "      <td>0.992305</td>\n", "      <td>0.994679</td>\n", "      <td>...</td>\n", "      <td>1_4458</td>\n", "      <td>0_4458</td>\n", "      <td>0</td>\n", "      <td>5.955837</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203912 rows × 42 columns</p>\n", "</div>"], "text/plain": ["                         repo_name  standardized_time_weeks  pr_throughput  \\\n", "0       10up/autoshare-for-twitter                      486            0.0   \n", "1       10up/autoshare-for-twitter                      487            0.0   \n", "2       10up/autoshare-for-twitter                      488            4.0   \n", "3       10up/autoshare-for-twitter                      489            1.0   \n", "4       10up/autoshare-for-twitter                      490            3.0   \n", "...                            ...                      ...            ...   \n", "203907     tree-sitter/tree-sitter                      559            1.0   \n", "203908     tree-sitter/tree-sitter                      560            2.0   \n", "203909     tree-sitter/tree-sitter                      561           10.0   \n", "203910     tree-sitter/tree-sitter                      562            2.0   \n", "203911     tree-sitter/tree-sitter                      563            4.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "0                       1.0                 2.0      -0.069930      0.666667   \n", "1                       1.0                 2.0      -0.034965      0.500000   \n", "2                       1.0                 2.0       0.122378      0.750000   \n", "3                       1.0                 2.0       0.097902      0.833333   \n", "4                       1.0                 2.0       0.143357      1.083333   \n", "...                     ...                 ...            ...           ...   \n", "203907                  1.0                13.0      -0.388112      3.416667   \n", "203908                  1.0                13.0       0.038462      2.416667   \n", "203909                  1.0                13.0       0.402098      2.916667   \n", "203910                  1.0                13.0       0.325175      2.916667   \n", "203911                  1.0                13.0       0.234266      3.250000   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "0                -6.931472e-01            0.493380                 0.386488   \n", "1                -1.098612e+00            0.354661                 0.366025   \n", "2                 9.162907e-01            0.841081                 0.665348   \n", "3                 6.931472e-01            0.821491                 0.640520   \n", "4                 1.386294e+00            0.921985                 0.817846   \n", "...                        ...                 ...                      ...   \n", "203907           -1.098612e+00            0.910361                 0.022897   \n", "203908           -1.609438e+00            0.691519                 0.020046   \n", "203909            7.884574e-01            0.975993                 0.908849   \n", "203910           -6.661338e-16            0.948664                 0.500000   \n", "203911            1.609438e+00            0.992305                 0.994679   \n", "\n", "        ...  time_cohort_effect  repo_cohort_effect  outlier  log_tenure  \\\n", "0       ...                 0_0                 1_0        0    5.552960   \n", "1       ...                 0_0                 1_0        0    5.552960   \n", "2       ...                 0_0                 1_0        0    5.552960   \n", "3       ...                 0_0                 1_0        0    5.552960   \n", "4       ...                 0_0                 1_0        0    5.552960   \n", "...     ...                 ...                 ...      ...         ...   \n", "203907  ...              1_4458              0_4458        0    5.955837   \n", "203908  ...              1_4458              0_4458        0    5.955837   \n", "203909  ...              1_4458              0_4458        0    5.955837   \n", "203910  ...              1_4458              0_4458        0    5.955837   \n", "203911  ...              1_4458              0_4458        0    5.955837   \n", "\n", "        log_commit_percent  log_commits log_project_commits_before_treatment  \\\n", "0                 0.196540     5.209486                             5.820083   \n", "1                 0.196540     5.209486                             5.820083   \n", "2                 0.196540     5.209486                             5.820083   \n", "3                 0.196540     5.209486                             5.820083   \n", "4                 0.196540     5.209486                             5.820083   \n", "...                    ...          ...                                  ...   \n", "203907            0.098761     5.153292                             7.368340   \n", "203908            0.098761     5.153292                             7.368340   \n", "203909            0.098761     5.153292                             7.368340   \n", "203910            0.098761     5.153292                             7.368340   \n", "203911            0.098761     5.153292                             7.368340   \n", "\n", "        log_project_contributors_before_treatment  \\\n", "0                                        2.197225   \n", "1                                        2.197225   \n", "2                                        2.197225   \n", "3                                        2.197225   \n", "4                                        2.197225   \n", "...                                           ...   \n", "203907                                   3.784190   \n", "203908                                   3.784190   \n", "203909                                   3.784190   \n", "203910                                   3.784190   \n", "203911                                   3.784190   \n", "\n", "        log_project_age_before_treatment  project_main_language  \n", "0                               6.056784                    PHP  \n", "1                               6.056784                    PHP  \n", "2                               6.056784                    PHP  \n", "3                               6.056784                    PHP  \n", "4                               6.056784                    PHP  \n", "...                                  ...                    ...  \n", "203907                          5.966147                     Go  \n", "203908                          5.966147                     Go  \n", "203909                          5.966147                     Go  \n", "203910                          5.966147                     Go  \n", "203911                          5.966147                     Go  \n", "\n", "[203912 rows x 42 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_id", "rawType": "object", "type": "string"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "attrition_developer", "rawType": "object", "type": "string"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "9d892f03-3525-488d-aaa7-e4f5b91499d3", "rows": [["0", "6746c7d7d305272efe6b63fb", "101loop/drf-user", "attrition", "2019-04-02", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "327.0", "0.28125", "36.0", "5", "1", "False", null, "steady", "0"], ["1", "6746c7d7d305272efe6b63fc", "10gic/vanitygen-plusplus", "attrition", "2018-09-21", "exploitagency", "595.0", "0.5380952380952381", "113.0", "6", "1", "False", null, "decelerating", "6"], ["2", "6746c7d7d305272efe6b63fd", "10up/autoshare-for-twitter", "attrition", "2020-03-25", "johnwatkins0", "257.0", "0.2171837708830549", "182.0", "7", "1", "False", null, "steady", "1"], ["3", "6746c7d7d305272efe6b63ff", "10xgenomics/vartrix", "attrition", "2019-06-24", "<PERSON><PERSON><PERSON>", "333.0", "0.5974842767295597", "95.0", "12", "1", "False", null, "decelerating", "0"], ["4", "6746c7d7d305272efe6b63ff", "10xgenomics/vartrix", "attrition", "2021-04-22", "pmarks", "1007.0", "0.2264150943396226", "36.0", "13", "1", "False", "668.0", "steady", "0"], ["5", "6746c7d7d305272efe6b6400", "1313e/cmasher", "attrition", "2021-11-30", "1313e", "744.0", "0.6104651162790697", "210.0", "14", "1", "False", null, "decelerating", "0"], ["6", "6746c7d7d305272efe6b6401", "1461748123/rocketbot", "attrition", "2016-07-31", "DetectiveSquirrel", "9.0", "0.3317191283292978", "137.0", "15", "1", "False", null, null, "19"], ["7", "6746c7d7d305272efe6b6402", "18F/analytics.usa.gov", "attrition", "2015-01-23", "cew821", "15.0", "0.0284396617986164", "37.0", "16", "1", "False", null, "first 3 months", "7"], ["8", "6746c7d7d305272efe6b6402", "18F/analytics.usa.gov", "attrition", "2016-06-16", "hbillings", "71.0", "0.0284396617986164", "37.0", "17", "1", "False", "510.0", "decelerating", "1"], ["9", "6746c7d7d305272efe6b6402", "18F/analytics.usa.gov", "attrition", "2022-04-12", "tdlowden", "2583.0", "0.0676402767102229", "88.0", "20", "1", "False", "783.0", "decelerating", "0"], ["10", "6746c7d7d305272efe6b6404", "24slides/laravel-saml2", "attrition", "2018-11-07", "aacotroneo", "1373.0", "0.1887755102040816", "37.0", "21", "1", "False", null, "steady", "11"], ["11", "6746c7d7d305272efe6b6404", "24slides/laravel-saml2", "attrition", "2020-07-01", "breart", "526.0", "0.3163265306122449", "62.0", "22", "1", "False", "602.0", "steady", "0"], ["12", "6746c7d7d305272efe6b6405", "2amigos/yii2-usuario", "attrition", "2017-01-11", "resurtm", "28.0", "0.0719041278295605", "54.0", "23", "1", "False", null, "first 3 months", "2"], ["13", "6746c7d7d305272efe6b6405", "2amigos/yii2-usuario", "attrition", "2018-02-09", "<PERSON><PERSON><PERSON><PERSON>", "192.0", "0.014647137150466", "11.0", "24", "2", "False", "394.0", "decelerating", "2"], ["14", "6746c7d7d305272efe6b6405", "2amigos/yii2-usuario", "attrition", "2018-02-14", "faenir", "187.0", "0.0119840213049267", "9.0", "24", "2", "False", "394.0", "decelerating", "2"], ["15", "6746c7d7d305272efe6b6405", "2amigos/yii2-usuario", "attrition", "2019-09-02", "tonydspaniard", "1002.0", "0.2689747003994673", "202.0", "27", "1", "False", "463.0", "decelerating", "6"], ["16", "6746c7d7d305272efe6b6405", "2amigos/yii2-usuario", "attrition", "2022-03-09", "a<PERSON><PERSON><PERSON>", "1492.0", "0.0199733688415446", "15.0", "30", "1", "False", "734.0", "saturation", "0"], ["17", "6746c7d7d305272efe6b6406", "2dust/v2rayn", "attrition", "2020-04-18", "yfdyh000", "60.0", "0.0631163708086785", "96.0", "31", "1", "False", null, "accelerating", "6"], ["18", "6746c7d7d305272efe6b6407", "2ndquadrant/pglogical", "attrition", "2018-12-14", "ringerc", "1143.0", "0.1789215686274509", "146.0", "32", "1", "False", null, "steady", "0"], ["19", "6746c7d7d305272efe6b6407", "2ndquadrant/pglogical", "attrition", "2021-08-10", "PJMODOS", "2161.0", "0.5477941176470589", "447.0", "33", "1", "False", "970.0", "decelerating", "0"], ["20", "6746c7d7d305272efe6b6407", "2ndquadrant/pglogical", "attrition", "2021-11-15", "pallavisontakke", "2165.0", "0.0661764705882353", "54.0", "34", "1", "False", "97.0", "decelerating", "0"], ["21", "6746c7d7d305272efe6b6409", "4catalyzer/found", "attrition", "2019-05-23", "dependabot-support", "48.0", "0.116504854368932", "48.0", "35", "1", "False", null, "accelerating", "6"], ["22", "6746c7d7d305272efe6b6409", "4catalyzer/found", "attrition", "2020-08-13", "taion", "1446.0", "0.6262135922330098", "258.0", "36", "1", "False", "448.0", "steady", "1"], ["23", "6746c7d7d305272efe6b640a", "4store/4store", "attrition", "2011-05-22", "tialaramex", "676.0", "0.0312093628088426", "24.0", "37", "1", "False", null, "accelerating", "0"], ["24", "6746c7d7d305272efe6b640a", "4store/4store", "attrition", "2012-02-21", "<PERSON><PERSON><PERSON><PERSON>", "322.0", "0.1716514954486346", "132.0", "38", "1", "False", "275.0", "decelerating", "1"], ["25", "6746c7d7d305272efe6b640a", "4store/4store", "attrition", "2014-02-07", "msalvadores", "1078.0", "0.0988296488946684", "76.0", "39", "1", "False", "717.0", "saturation", "0"], ["26", "6746c7d7d305272efe6b640e", "99x/serverless-dynamodb-local", "attrition", "2017-02-28", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "8.0", "0.0335820895522388", "9.0", "40", "1", "False", null, "accelerating", "8"], ["27", "6746c7d7d305272efe6b640e", "99x/serverless-dynamodb-local", "attrition", "2017-07-26", "lakindu95", "18.0", "0.1380597014925373", "37.0", "41", "1", "False", "148.0", "decelerating", "1"], ["28", "6746c7d7d305272efe6b640e", "99x/serverless-dynamodb-local", "attrition", "2018-12-31", "<PERSON><PERSON><PERSON><PERSON>", "930.0", "0.0671641791044776", "18.0", "44", "1", "False", "109.0", "decelerating", "1"], ["29", "6746c7d7d305272efe6b6417", "EpicGamesExt/BlenderTools", "attrition", "2020-12-19", "<PERSON><PERSON><PERSON><PERSON>", "36.0", "0.05", "22.0", "45", "1", "False", null, "decelerating", "3"], ["30", "6746c7d7d305272efe6b641a", "FalkorDB/FalkorDB", "attrition", "2022-05-30", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "1431.0", "0.213519313304721", "398.0", "46", "1", "False", null, "steady", "2"], ["31", "6746c7d7d305272efe6b641a", "FalkorDB/FalkorDB", "attrition", "2022-10-18", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "1219.0", "0.0740343347639485", "138.0", "47", "1", "False", "141.0", "steady", "3"], ["32", "6746c7d7d305272efe6b641d", "GoogleCloudPlatform/healthcare", "attrition", "2021-01-05", "<PERSON><PERSON><PERSON><PERSON>", "654.0", "0.3329297820823244", "275.0", "48", "1", "False", null, "saturation", "0"], ["33", "6746c7d7d305272efe6b641d", "GoogleCloudPlatform/healthcare", "attrition", "2021-09-16", "xingao267", "790.0", "0.12227602905569", "101.0", "49", "1", "False", "254.0", "saturation", "0"], ["34", "6746c7d7d305272efe6b641d", "GoogleCloudPlatform/healthcare", "attrition", "2023-06-05", "a-googler", "1927.0", "0.3716707021791767", "307.0", "50", "1", "False", "627.0", "saturation", "0"], ["35", "6746c7d7d305272efe6b641e", "<PERSON><PERSON>-Foundation/Spoke", "attrition", "2020-02-24", "gfodor", "530.0", "0.0744786494538232", "150.0", "51", "1", "False", null, "decelerating", "1"], ["36", "6746c7d7d305272efe6b641e", "<PERSON><PERSON>-Foundation/Spoke", "attrition", "2021-04-02", "robert<PERSON>", "1038.0", "0.5878848063555114", "1184.0", "52", "1", "False", "403.0", "saturation", "0"], ["37", "6746c7d7d305272efe6b641e", "<PERSON><PERSON>-Foundation/Spoke", "attrition", "2022-02-12", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "1345.0", "0.1613704071499503", "325.0", "53", "1", "False", "316.0", "saturation", "0"], ["38", "6746c7d7d305272efe6b6422", "JamBrain/JamBrain", "attrition", "2023-05-01", "local-minimum", "2183.0", "0.0922955561398895", "351.0", "54", "1", "False", null, "saturation", "1"], ["39", "6746c7d7d305272efe6b6423", "KSP-RO/RP-1", "attrition", "2016-04-13", "pjf", "524.0", "0.018348623853211", "114.0", "55", "1", "False", null, "steady", "35"], ["40", "6746c7d7d305272efe6b6423", "KSP-RO/RP-1", "attrition", "2017-02-02", "dearmoon9", "491.0", "0.0423305971350394", "263.0", "56", "1", "False", "295.0", "decelerating", "1"], ["41", "6746c7d7d305272efe6b6425", "LiskArchive/lisk-mobile", "attrition", "2020-03-10", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "729.0", "0.1506024096385542", "725.0", "59", "1", "False", null, "decelerating", "0"], ["42", "6746c7d7d305272efe6b6425", "LiskArchive/lisk-mobile", "attrition", "2022-03-21", "re<PERSON>a", "1485.0", "0.2295388450353136", "1105.0", "60", "1", "False", "741.0", "steady", "2"], ["43", "6746c7d7d305272efe6b6429", "Mirai-NET-Shelter/Mirai.Net", "attrition", "2023-01-24", "SinoAHpx", "545.0", "0.782608695652174", "216.0", "61", "1", "False", null, "steady", "0"], ["44", "6746c7d7d305272efe6b642a", "MirrorNetworking/Telepathy", "attrition", "2019-07-16", "p<PERSON><PERSON><PERSON>", "336.0", "0.0629213483146067", "28.0", "62", "1", "False", null, "steady", "1"], ["45", "6746c7d7d305272efe6b642d", "MyIntervals/PHP-CSS-Parser", "attrition", "2016-06-30", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "30.0", "0.0056657223796034", "4.0", "63", "1", "False", null, "steady", "0"], ["46", "6746c7d7d305272efe6b642e", "NeuroTechX/EEG-ExPy", "attrition", "2021-08-12", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "362.0", "0.0916030534351145", "36.0", "66", "1", "False", null, "steady", "0"], ["47", "6746c7d7d305272efe6b642e", "NeuroTechX/EEG-ExPy", "attrition", "2022-06-16", "<PERSON><PERSON><PERSON><PERSON>", "575.0", "0.1908396946564885", "75.0", "67", "1", "False", "308.0", "steady", "0"], ["48", "6746c7d7d305272efe6b642f", "OSMCha/osmcha-frontend", "attrition", "2017-10-26", "kepta", "198.0", "0.4448198198198198", "395.0", "68", "1", "False", null, "steady", "4"], ["49", "6746c7d7d305272efe6b6430", "OfficeDev/Microsoft-Teams-Samples", "attrition", "2022-05-06", "Nik<PERSON><PERSON>ain-MSFT", "267.0", "0.1008583690987124", "376.0", "69", "1", "False", null, "decelerating", "4"]], "shape": {"columns": 14, "rows": 5440}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_id</th>\n", "      <th>repo_name</th>\n", "      <th>attrition</th>\n", "      <th>attrition_date</th>\n", "      <th>attrition_developer</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6746c7d7d305272efe6b63fb</td>\n", "      <td>101loop/drf-user</td>\n", "      <td>attrition</td>\n", "      <td>2019-04-02</td>\n", "      <td>i<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>327.0</td>\n", "      <td>0.281250</td>\n", "      <td>36.0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6746c7d7d305272efe6b63fc</td>\n", "      <td>10gic/vanitygen-plusplus</td>\n", "      <td>attrition</td>\n", "      <td>2018-09-21</td>\n", "      <td>exploitagency</td>\n", "      <td>595.0</td>\n", "      <td>0.538095</td>\n", "      <td>113.0</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>decelerating</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6746c7d7d305272efe6b63fd</td>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>attrition</td>\n", "      <td>2020-03-25</td>\n", "      <td>john<PERSON>kins0</td>\n", "      <td>257.0</td>\n", "      <td>0.217184</td>\n", "      <td>182.0</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6746c7d7d305272efe6b63ff</td>\n", "      <td>10xgenomics/vartrix</td>\n", "      <td>attrition</td>\n", "      <td>2019-06-24</td>\n", "      <td>ifiddes</td>\n", "      <td>333.0</td>\n", "      <td>0.597484</td>\n", "      <td>95.0</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>decelerating</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6746c7d7d305272efe6b63ff</td>\n", "      <td>10xgenomics/vartrix</td>\n", "      <td>attrition</td>\n", "      <td>2021-04-22</td>\n", "      <td>pmarks</td>\n", "      <td>1007.0</td>\n", "      <td>0.226415</td>\n", "      <td>36.0</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>668.0</td>\n", "      <td>steady</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5435</th>\n", "      <td>6746c7dcd305272efe6b7375</td>\n", "      <td>zowe/vscode-extension-for-zowe</td>\n", "      <td>attrition</td>\n", "      <td>2020-05-13</td>\n", "      <td><PERSON>-<PERSON></td>\n", "      <td>399.0</td>\n", "      <td>0.042432</td>\n", "      <td>291.0</td>\n", "      <td>8663</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>accelerating</td>\n", "      <td>124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5436</th>\n", "      <td>6746c7dcd305272efe6b7376</td>\n", "      <td>zulip/zulip-desktop</td>\n", "      <td>attrition</td>\n", "      <td>2020-02-25</td>\n", "      <td>vsvipul</td>\n", "      <td>380.0</td>\n", "      <td>0.044705</td>\n", "      <td>84.0</td>\n", "      <td>8667</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>decelerating</td>\n", "      <td>110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5437</th>\n", "      <td>6746c7dcd305272efe6b7376</td>\n", "      <td>zulip/zulip-desktop</td>\n", "      <td>attrition</td>\n", "      <td>2020-05-19</td>\n", "      <td>priyank-p</td>\n", "      <td>898.0</td>\n", "      <td>0.033528</td>\n", "      <td>63.0</td>\n", "      <td>8668</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>84.0</td>\n", "      <td>steady</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5438</th>\n", "      <td>6746c7dcd305272efe6b7376</td>\n", "      <td>zulip/zulip-desktop</td>\n", "      <td>attrition</td>\n", "      <td>2020-09-05</td>\n", "      <td>aka<PERSON><PERSON><PERSON></td>\n", "      <td>1549.0</td>\n", "      <td>0.401809</td>\n", "      <td>755.0</td>\n", "      <td>8669</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>109.0</td>\n", "      <td>steady</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5439</th>\n", "      <td>6746c7dcd305272efe6b7377</td>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>attrition</td>\n", "      <td>2021-04-29</td>\n", "      <td>kaduartur</td>\n", "      <td>385.0</td>\n", "      <td>0.103802</td>\n", "      <td>172.0</td>\n", "      <td>8677</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>114.0</td>\n", "      <td>steady</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5440 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                       repo_id                       repo_name  attrition  \\\n", "0     6746c7d7d305272efe6b63fb                101loop/drf-user  attrition   \n", "1     6746c7d7d305272efe6b63fc        10gic/vanitygen-plusplus  attrition   \n", "2     6746c7d7d305272efe6b63fd      10up/autoshare-for-twitter  attrition   \n", "3     6746c7d7d305272efe6b63ff             10xgenomics/vartrix  attrition   \n", "4     6746c7d7d305272efe6b63ff             10xgenomics/vartrix  attrition   \n", "...                        ...                             ...        ...   \n", "5435  6746c7dcd305272efe6b7375  zowe/vscode-extension-for-zowe  attrition   \n", "5436  6746c7dcd305272efe6b7376             zulip/zulip-desktop  attrition   \n", "5437  6746c7dcd305272efe6b7376             zulip/zulip-desktop  attrition   \n", "5438  6746c7dcd305272efe6b7376             zulip/zulip-desktop  attrition   \n", "5439  6746c7dcd305272efe6b7377               zupit/ritchie-cli  attrition   \n", "\n", "     attrition_date attrition_developer  tenure  commit_percent  commits  \\\n", "0        2019-04-02          iamhssingh   327.0        0.281250     36.0   \n", "1        2018-09-21       exploitagency   595.0        0.538095    113.0   \n", "2        2020-03-25        j<PERSON>nwatkins0   257.0        0.217184    182.0   \n", "3        2019-06-24             <PERSON><PERSON><PERSON>   333.0        0.597484     95.0   \n", "4        2021-04-22              pmarks  1007.0        0.226415     36.0   \n", "...             ...                 ...     ...             ...      ...   \n", "5435     2020-05-13         <PERSON><PERSON><PERSON>   399.0        0.042432    291.0   \n", "5436     2020-02-25             vsvipul   380.0        0.044705     84.0   \n", "5437     2020-05-19           priyank-p   898.0        0.033528     63.0   \n", "5438     2020-09-05         <PERSON>shnimare  1549.0        0.401809    755.0   \n", "5439     2021-04-29           kaduartur   385.0        0.103802    172.0   \n", "\n", "      burst  attrition_count  gap_less_than_84  inter_burst_gap  growth_phase  \\\n", "0         5                1             False              NaN        steady   \n", "1         6                1             False              NaN  decelerating   \n", "2         7                1             False              NaN        steady   \n", "3        12                1             False              NaN  decelerating   \n", "4        13                1             False            668.0        steady   \n", "...     ...              ...               ...              ...           ...   \n", "5435   8663                1             False              NaN  accelerating   \n", "5436   8667                1             False              NaN  decelerating   \n", "5437   8668                1             False             84.0        steady   \n", "5438   8669                1             False            109.0        steady   \n", "5439   8677                1             False            114.0        steady   \n", "\n", "      newcomers  \n", "0             0  \n", "1             6  \n", "2             1  \n", "3             0  \n", "4             0  \n", "...         ...  \n", "5435        124  \n", "5436        110  \n", "5437          4  \n", "5438          2  \n", "5439          5  \n", "\n", "[5440 rows x 14 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["attritions"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["growth_phase\n", "steady          2761\n", "decelerating    1741\n", "accelerating     609\n", "maturation       212\n", "early phase       96\n", "Name: count, dtype: int64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["attritions.loc[attritions.growth_phase == \"maturation\", \"growth_phase\"] = \"maturation\"\n", "attritions.loc[attritions.growth_phase == \"plateauing\", \"growth_phase\"] = \"maturation\"\n", "attritions.loc[attritions.growth_phase == \"saturation\", \"growth_phase\"] = \"maturation\"\n", "attritions.loc[attritions.growth_phase == \"saturated\", \"growth_phase\"] = \"maturation\"\n", "\n", "attritions['growth_phase'].value_counts()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Create a mapping of burst to growth_phase and newcomers from attritions\n", "burst_to_data = {}\n", "for burst in attritions.burst.unique():\n", "  filtered_attritions = attritions[attritions.burst == burst]\n", "  burst_to_data[burst] = {\n", "    'growth_phase': filtered_attritions.growth_phase.values[0],\n", "    'newcomers': filtered_attritions.newcomers.values[0]\n", "  }\n", "\n", "# Create a mapping of cohort_id to burst\n", "cohort_to_burst = {}\n", "for burst in burst_to_data:\n", "  # Find one row with this burst to get its cohort_id\n", "  matching_rows = compiled_data[compiled_data.burst == burst]\n", "  if not matching_rows.empty:\n", "    cohort_id = matching_rows.iloc[0]['cohort_id']\n", "    cohort_to_burst[cohort_id] = burst\n", "\n", "# Now update all rows with the same cohort_id\n", "for cohort_id, burst in cohort_to_burst.items():\n", "  compiled_data.loc[compiled_data.cohort_id == cohort_id, 'growth_phase'] = burst_to_data[burst]['growth_phase']\n", "  compiled_data.loc[compiled_data.cohort_id == cohort_id, 'newcomers'] = burst_to_data[burst]['newcomers']\n", "    \n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["growth_phase\n", "steady            86590\n", "decelerating      66242\n", "accelerating      26965\n", "saturation        21601\n", "first 3 months     1274\n", "Name: count, dtype: int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data['growth_phase'].value_counts()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_first", "rawType": "float64", "type": "float"}, {"name": "pr_throughput_last", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}, {"name": "relativized_time", "rawType": "int64", "type": "integer"}, {"name": "is_treated", "rawType": "int64", "type": "integer"}, {"name": "post_treatment", "rawType": "bool", "type": "boolean"}, {"name": "cohort_id", "rawType": "int64", "type": "integer"}, {"name": "is_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "is_treated_post_treatment", "rawType": "int64", "type": "integer"}, {"name": "project_commits", "rawType": "int64", "type": "integer"}, {"name": "project_contributors", "rawType": "int64", "type": "integer"}, {"name": "project_age", "rawType": "int64", "type": "integer"}, {"name": "log_pr_throughput", "rawType": "float64", "type": "float"}, {"name": "log_project_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors", "rawType": "float64", "type": "float"}, {"name": "log_project_age", "rawType": "float64", "type": "float"}, {"name": "time_cohort_effect", "rawType": "object", "type": "string"}, {"name": "repo_cohort_effect", "rawType": "object", "type": "string"}, {"name": "outlier", "rawType": "int64", "type": "integer"}, {"name": "log_tenure", "rawType": "float64", "type": "float"}, {"name": "log_commit_percent", "rawType": "float64", "type": "float"}, {"name": "log_commits", "rawType": "float64", "type": "float"}, {"name": "log_project_commits_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_contributors_before_treatment", "rawType": "float64", "type": "float"}, {"name": "log_project_age_before_treatment", "rawType": "float64", "type": "float"}, {"name": "project_main_language", "rawType": "object", "type": "string"}, {"name": "attrition", "rawType": "object", "type": "unknown"}, {"name": "growth_phase", "rawType": "object", "type": "unknown"}, {"name": "newcomers", "rawType": "float64", "type": "float"}, {"name": "newcomers_bool", "rawType": "bool", "type": "boolean"}], "conversionMethod": "pd.DataFrame", "ref": "6db79e5c-3ca3-46a0-abf8-9350ea620a9b", "rows": [["0", "10up/autoshare-for-twitter", "486", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "49", "-12", "1", "False", "0", "0", "0", "197", "5", "342", "0.0", "5.288267030694535", "1.791759469228055", "5.83773044716594", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["1", "10up/autoshare-for-twitter", "487", "0.0", "1.0", "2.0", "-0.0349650349650349", "0.5", "-1.09861228866811", "0.3546612443924433", "0.3660254037844386", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "50", "-11", "1", "False", "0", "0", "0", "197", "5", "349", "0.0", "5.288267030694535", "1.791759469228055", "5.857933154483459", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["2", "10up/autoshare-for-twitter", "488", "4.0", "1.0", "2.0", "0.1223776223776223", "0.75", "0.9162907318741548", "0.8410806526182262", "0.6653477824119316", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "51", "-10", "1", "False", "0", "0", "0", "209", "5", "356", "1.6094379124341005", "5.3471075307174685", "1.791759469228055", "5.877735781779639", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["3", "10up/autoshare-for-twitter", "489", "1.0", "1.0", "2.0", "0.0979020979020979", "0.8333333333333334", "0.6931471805599448", "0.8214907876837801", "0.6405201949797534", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "52", "-9", "1", "False", "0", "0", "0", "210", "5", "363", "0.6931471805599453", "5.351858133476067", "1.791759469228055", "5.8971538676367405", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["4", "10up/autoshare-for-twitter", "490", "3.0", "1.0", "2.0", "0.1433566433566433", "1.0833333333333333", "1.38629436111989", "0.921984989635266", "0.8178456006794831", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "53", "-8", "1", "False", "0", "0", "0", "220", "5", "370", "1.3862943611198906", "5.3981627015177525", "1.791759469228055", "5.916202062607435", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["5", "10up/autoshare-for-twitter", "491", "3.0", "1.0", "2.0", "0.2587412587412587", "1.1666666666666667", "0.2876820724517805", "0.8106668070686921", "0.5831283861446792", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "54", "-7", "1", "False", "0", "0", "0", "249", "6", "377", "1.3862943611198906", "5.521460917862246", "1.9459101490553128", "5.934894195619588", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["6", "10up/autoshare-for-twitter", "492", "0.0", "1.0", "2.0", "0.1608391608391608", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "55", "-6", "1", "False", "0", "0", "0", "255", "6", "384", "0.0", "5.545177444479562", "1.9459101490553128", "5.953243334287784", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["7", "10up/autoshare-for-twitter", "493", "0.0", "1.0", "2.0", "0.0629370629370629", "1.1666666666666667", "-2.220446049250313e-16", "0.7625419716560974", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "56", "-5", "1", "False", "0", "0", "0", "259", "6", "391", "0.0", "5.560681631015528", "1.9459101490553128", "5.971261839790462", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["8", "10up/autoshare-for-twitter", "494", "1.0", "1.0", "2.0", "0.0034965034965034", "1.25", "0.6931471805599451", "0.8746974870756378", "0.7040031411428227", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "57", "-4", "1", "False", "0", "0", "0", "261", "6", "398", "0.6931471805599453", "5.568344503761097", "1.9459101490553128", "5.988961416889863", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["9", "10up/autoshare-for-twitter", "495", "1.0", "1.0", "2.0", "0.0279720279720279", "1.1666666666666667", "-0.4054651081081645", "0.6816145482921148", "0.3838963480989594", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "58", "-3", "1", "False", "0", "0", "0", "275", "6", "405", "0.6931471805599453", "5.62040086571715", "1.9459101490553128", "6.0063531596017325", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["10", "10up/autoshare-for-twitter", "496", "0.0", "1.0", "2.0", "-0.0244755244755244", "1.0833333333333333", "-0.6931471805599454", "0.5963275109839462", "0.3206231694796373", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "59", "-2", "1", "False", "0", "0", "0", "306", "7", "412", "0.0", "5.726847747587197", "2.079441541679836", "6.023447592961032", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["11", "10up/autoshare-for-twitter", "497", "5.0", "1.0", "2.0", "0.0769230769230769", "1.5", "1.791759469228055", "0.964145027597638", "0.9362933095037254", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "60", "-1", "1", "False", "0", "0", "0", "309", "7", "419", "1.791759469228055", "5.736572297479192", "2.079441541679836", "6.040254711277414", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["12", "10up/autoshare-for-twitter", "498", "2.0", "1.0", "2.0", "0.0279720279720279", "1.6666666666666667", "1.0986122886681096", "0.9407704701088077", "0.8618832502903921", "1", "257.0", "0.2171837708830549", "182.0", "7.0", "1.0", "PHP", "437", "61", "0", "1", "False", "0", "0", "0", "336", "8", "426", "1.0986122886681096", "5.820082930352362", "2.197224577336219", "6.056784013228624", "0_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", "attrition", "steady", "1.0", "True"], ["13", "10up/autoshare-for-twitter", "499", "1.0", "1.0", "2.0", "-0.0734265734265734", "1.75", "0.6931471805599452", "0.9200588708986858", "0.7708306705345104", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "62", "1", "1", "True", "0", "1", "1", "336", "8", "433", "0.6931471805599453", "5.820082930352362", "2.197224577336219", "6.073044534100404", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["14", "10up/autoshare-for-twitter", "500", "1.0", "1.0", "2.0", "0.0", "1.5", "-0.9162907318741552", "0.6419204615368317", "0.2019040735186648", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "63", "2", "1", "True", "0", "1", "1", "337", "8", "440", "0.6931471805599453", "5.823045895483019", "2.197224577336219", "6.089044875446846", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["15", "10up/autoshare-for-twitter", "501", "0.0", "1.0", "2.0", "-0.0804195804195804", "1.4166666666666667", "-0.6931471805599453", "0.6733815605777215", "0.2725033461986623", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "64", "3", "1", "True", "0", "1", "1", "337", "8", "447", "0.0", "5.823045895483019", "2.197224577336219", "6.104793232414985", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["16", "10up/autoshare-for-twitter", "502", "0.0", "1.0", "2.0", "-0.0629370629370629", "1.1666666666666667", "-1.3862943611198906", "0.4453127259526225", "0.1655715707900131", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "65", "4", "1", "True", "0", "1", "1", "337", "8", "454", "0.0", "5.823045895483019", "2.197224577336219", "6.12029741895095", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["17", "10up/autoshare-for-twitter", "503", "0.0", "1.0", "2.0", "-0.0244755244755244", "0.9166666666666666", "-1.3862943611198906", "0.3847043671232542", "0.2191254981927748", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "66", "5", "1", "True", "0", "1", "1", "337", "8", "461", "0.0", "5.823045895483019", "2.197224577336219", "6.135564891081739", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["18", "10up/autoshare-for-twitter", "504", "0.0", "1.0", "2.0", "-0.1013986013986013", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "67", "6", "1", "True", "0", "1", "1", "337", "8", "468", "0.0", "5.823045895483019", "2.197224577336219", "6.150602768446279", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["19", "10up/autoshare-for-twitter", "505", "0.0", "1.0", "2.0", "-0.1783216783216783", "0.9166666666666666", "0.0", "0.7143624294910559", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "68", "7", "1", "True", "0", "1", "1", "337", "8", "475", "0.0", "5.823045895483019", "2.197224577336219", "6.16541785423142", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["20", "10up/autoshare-for-twitter", "506", "0.0", "1.0", "2.0", "-0.2097902097902098", "0.8333333333333334", "-0.6931471805599453", "0.5349892557559008", "0.3594798050202465", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "69", "8", "1", "True", "0", "1", "1", "337", "8", "482", "0.0", "5.823045895483019", "2.197224577336219", "6.180016653652572", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["21", "10up/autoshare-for-twitter", "507", "0.0", "1.0", "2.0", "-0.2342657342657342", "0.75", "-0.6931471805599453", "0.5142093777192814", "0.3728848808245891", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "70", "9", "1", "True", "0", "1", "1", "337", "8", "489", "0.0", "5.823045895483019", "2.197224577336219", "6.194405391104672", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["22", "10up/autoshare-for-twitter", "508", "0.0", "1.0", "2.0", "-0.2972027972027972", "0.75", "0.0", "0.679178699175393", "0.5", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "71", "10", "1", "True", "0", "1", "1", "337", "8", "496", "0.0", "5.823045895483019", "2.197224577336219", "6.208590026096629", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["23", "10up/autoshare-for-twitter", "509", "0.0", "1.0", "2.0", "-0.1328671328671328", "0.3333333333333333", "-1.791759469228055", "0.1887081616597619", "0.3549723794374981", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "72", "11", "1", "True", "0", "1", "1", "337", "8", "503", "0.0", "5.823045895483019", "2.197224577336219", "6.222576268071369", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["24", "10up/autoshare-for-twitter", "510", "0.0", "1.0", "2.0", "-0.0699300699300699", "0.1666666666666666", "-1.0986122886681096", "0.2825301567477209", "0.4543519511761902", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "PHP", "437", "73", "12", "1", "True", "0", "1", "1", "337", "8", "510", "0.0", "5.823045895483019", "2.197224577336219", "6.236369590203704", "1_0", "1_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["25", "AdaptiveCpp/AdaptiveCpp", "486", "0.0", "3.0", "6.0", "-0.0734265734265734", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "77", "-12", "0", "False", "0", "0", "0", "387", "9", "359", "0.0", "5.961005339623274", "2.302585092994045", "5.886104031450156", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["26", "AdaptiveCpp/AdaptiveCpp", "487", "1.0", "3.0", "6.0", "-0.0664335664335664", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "78", "-11", "0", "False", "0", "0", "0", "388", "9", "366", "0.6931471805599453", "5.963579343618446", "2.302585092994045", "5.90536184805457", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["27", "AdaptiveCpp/AdaptiveCpp", "488", "0.0", "3.0", "6.0", "-0.0524475524475524", "0.75", "-1.09861228866811", "0.4137189778658776", "0.3049238750315606", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "79", "-10", "0", "False", "0", "0", "0", "389", "9", "373", "0.0", "5.966146739123692", "2.302585092994045", "5.924255797414531", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["28", "AdaptiveCpp/AdaptiveCpp", "489", "0.0", "3.0", "6.0", "-0.0699300699300699", "0.6666666666666666", "-0.6931471805599457", "0.493380258345448", "0.3864882095643093", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "80", "-9", "0", "False", "0", "0", "0", "389", "9", "380", "0.0", "5.966146739123692", "2.302585092994045", "5.9427993751267", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["29", "AdaptiveCpp/AdaptiveCpp", "490", "2.0", "3.0", "6.0", "-0.0489510489510489", "0.8333333333333334", "1.0986122886681091", "0.8734646144545724", "0.7141264037070777", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "81", "-8", "0", "False", "0", "0", "0", "391", "9", "387", "1.0986122886681096", "5.971261839790462", "2.302585092994045", "5.961005339623274", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["30", "AdaptiveCpp/AdaptiveCpp", "491", "2.0", "3.0", "6.0", "0.0489510489510489", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "82", "-7", "0", "False", "0", "0", "0", "394", "10", "394", "1.0986122886681096", "5.978885764901122", "2.3978952727983707", "5.978885764901122", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["31", "AdaptiveCpp/AdaptiveCpp", "492", "1.0", "3.0", "6.0", "0.0629370629370629", "0.8333333333333334", "-4.440892098500626e-16", "0.6970592839654073", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "83", "-6", "0", "False", "0", "0", "0", "394", "10", "401", "0.6931471805599453", "5.978885764901122", "2.3978952727983707", "5.996452088619021", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["32", "AdaptiveCpp/AdaptiveCpp", "493", "1.0", "3.0", "6.0", "0.0314685314685314", "0.9166666666666666", "0.6931471805599448", "0.8333855399562498", "0.6537094706869915", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "84", "-5", "0", "False", "0", "0", "0", "396", "11", "408", "0.6931471805599453", "5.98393628068719", "2.4849066497880004", "6.013715156042801", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["33", "AdaptiveCpp/AdaptiveCpp", "494", "1.0", "3.0", "6.0", "0.0384615384615384", "0.9166666666666666", "-4.440892098500626e-16", "0.7143624294910558", "0.4999999999999999", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "85", "-4", "0", "False", "0", "0", "0", "400", "11", "415", "0.6931471805599453", "5.993961427306569", "2.4849066497880004", "6.030685260261263", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["34", "AdaptiveCpp/AdaptiveCpp", "495", "4.0", "3.0", "6.0", "0.1608391608391608", "1.1666666666666667", "0.9162907318741546", "0.889235659522009", "0.7444078107515406", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "86", "-3", "0", "False", "0", "0", "0", "411", "12", "422", "1.6094379124341005", "6.021023349349526", "2.5649493574615367", "6.0473721790462776", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["35", "AdaptiveCpp/AdaptiveCpp", "496", "4.0", "3.0", "6.0", "0.3076923076923077", "1.3333333333333333", "0.5108256237659902", "0.8634398378798575", "0.6639843473284657", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "87", "-2", "0", "False", "0", "0", "0", "419", "13", "429", "1.6094379124341005", "6.040254711277414", "2.6390573296152584", "6.063785208687608", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["36", "AdaptiveCpp/AdaptiveCpp", "497", "2.0", "3.0", "6.0", "0.2727272727272727", "1.5", "1.0986122886681091", "0.9307722154980688", "0.8386095222035911", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "88", "-1", "0", "False", "0", "0", "0", "423", "13", "436", "1.0986122886681096", "6.049733455231958", "2.6390573296152584", "6.07993319509559", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["37", "AdaptiveCpp/AdaptiveCpp", "498", "2.0", "3.0", "6.0", "0.2237762237762237", "1.6666666666666667", "1.0986122886681091", "0.9407704701088077", "0.861883250290392", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "89", "0", "0", "False", "0", "0", "0", "427", "13", "443", "1.0986122886681096", "6.059123195581797", "2.6390573296152584", "6.095824562432225", "0_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["38", "AdaptiveCpp/AdaptiveCpp", "499", "0.0", "3.0", "6.0", "0.1293706293706293", "1.5833333333333333", "-0.693147180559946", "0.7089285864699496", "0.2502117946664587", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "90", "1", "0", "True", "0", "1", "0", "430", "13", "450", "0.0", "6.066108090103747", "2.6390573296152584", "6.111467339502678", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["39", "AdaptiveCpp/AdaptiveCpp", "500", "0.0", "3.0", "6.0", "-0.0034965034965034", "1.5833333333333333", "-6.661338147750939e-16", "0.829676081356154", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "91", "2", "0", "True", "0", "1", "0", "431", "13", "457", "0.0", "6.06842558824411", "2.6390573296152584", "6.1268691841141845", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["40", "AdaptiveCpp/AdaptiveCpp", "501", "3.0", "3.0", "6.0", "-0.0209790209790209", "1.8333333333333333", "1.38629436111989", "0.9615662577192609", "0.927003081517424", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "92", "3", "0", "True", "0", "1", "0", "433", "13", "464", "1.3862943611198906", "6.073044534100404", "2.6390573296152584", "6.142037405587356", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["41", "AdaptiveCpp/AdaptiveCpp", "502", "3.0", "3.0", "6.0", "0.0314685314685314", "1.9166666666666667", "0.2876820724517803", "0.9006393499233735", "0.634458169632737", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "93", "4", "0", "True", "0", "1", "0", "437", "13", "471", "1.3862943611198906", "6.0822189103764455", "2.6390573296152584", "6.156978985585555", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["42", "AdaptiveCpp/AdaptiveCpp", "503", "1.0", "3.0", "6.0", "0.0", "1.8333333333333333", "-0.4054651081081649", "0.8065689432388163", "0.3222714364606489", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "94", "5", "0", "True", "0", "1", "0", "437", "13", "478", "0.6931471805599453", "6.0822189103764455", "2.6390573296152584", "6.171700597410915", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["43", "AdaptiveCpp/AdaptiveCpp", "504", "0.0", "3.0", "6.0", "-0.1083916083916083", "1.75", "-0.6931471805599458", "0.7420886557878206", "0.2291693294654893", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "95", "6", "0", "True", "0", "1", "0", "438", "13", "485", "0.0", "6.084499413075172", "2.6390573296152584", "6.186208623900494", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["44", "AdaptiveCpp/AdaptiveCpp", "505", "0.0", "3.0", "6.0", "-0.2097902097902098", "1.6666666666666667", "-0.6931471805599457", "0.7258204499338335", "0.2395323119764419", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "96", "7", "0", "True", "0", "1", "0", "438", "13", "492", "0.0", "6.084499413075172", "2.6390573296152584", "6.20050917404269", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["45", "AdaptiveCpp/AdaptiveCpp", "506", "0.0", "3.0", "6.0", "-0.3041958041958042", "1.5833333333333333", "-0.6931471805599457", "0.7089285864699497", "0.2502117946664588", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "97", "8", "0", "True", "0", "1", "0", "439", "14", "499", "0.0", "6.0867747269123065", "2.70805020110221", "6.214608098422191", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["46", "AdaptiveCpp/AdaptiveCpp", "507", "2.0", "3.0", "6.0", "-0.1783216783216783", "1.4166666666666667", "-0.5108256237659912", "0.7121483581398508", "0.3265854170022765", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "98", "9", "0", "True", "0", "1", "0", "442", "15", "506", "1.0986122886681096", "6.093569770045136", "2.772588722239781", "6.228511003591183", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["47", "AdaptiveCpp/AdaptiveCpp", "508", "0.0", "3.0", "6.0", "-0.1153846153846153", "1.0833333333333333", "-1.609437912434101", "0.371425890455925", "0.1488615992206787", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "99", "10", "0", "True", "0", "1", "0", "443", "16", "513", "0.0", "6.095824562432225", "2.833213344056216", "6.242223265455165", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["48", "AdaptiveCpp/AdaptiveCpp", "509", "1.0", "3.0", "6.0", "-0.0769230769230769", "1.0", "-0.4054651081081649", "0.6444049826448044", "0.3999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "100", "11", "0", "True", "0", "1", "0", "444", "16", "520", "0.6931471805599453", "6.09807428216624", "2.833213344056216", "6.255750041753367", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"], ["49", "AdaptiveCpp/AdaptiveCpp", "510", "2.0", "3.0", "6.0", "0.0069930069930069", "1.0", "-5.551115123125785e-16", "0.7310585786300048", "0.4999999999999998", "0", "257.0", "0.2171837708830549", "182.0", "0.0", "0.0", "C++", "409", "101", "12", "0", "True", "0", "1", "0", "446", "16", "527", "1.0986122886681096", "6.102558594613568", "2.833213344056216", "6.269096283706261", "1_0", "0_0", "0", "5.552959584921617", "0.1965398057962336", "5.2094861528414205", "5.820082930352362", "2.197224577336219", "6.056784013228624", "PHP", null, "steady", "1.0", "False"]], "shape": {"columns": 46, "rows": 203912}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>pr_throughput_first</th>\n", "      <th>pr_throughput_last</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>...</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "      <th>attrition</th>\n", "      <th>growth_phase</th>\n", "      <th>newcomers</th>\n", "      <th>newcomers_bool</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>486</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.069930</td>\n", "      <td>0.666667</td>\n", "      <td>-6.931472e-01</td>\n", "      <td>0.493380</td>\n", "      <td>0.386488</td>\n", "      <td>...</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>487</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>-0.034965</td>\n", "      <td>0.500000</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.354661</td>\n", "      <td>0.366025</td>\n", "      <td>...</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>488</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.122378</td>\n", "      <td>0.750000</td>\n", "      <td>9.162907e-01</td>\n", "      <td>0.841081</td>\n", "      <td>0.665348</td>\n", "      <td>...</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>489</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.097902</td>\n", "      <td>0.833333</td>\n", "      <td>6.931472e-01</td>\n", "      <td>0.821491</td>\n", "      <td>0.640520</td>\n", "      <td>...</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>490</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.143357</td>\n", "      <td>1.083333</td>\n", "      <td>1.386294e+00</td>\n", "      <td>0.921985</td>\n", "      <td>0.817846</td>\n", "      <td>...</td>\n", "      <td>0.196540</td>\n", "      <td>5.209486</td>\n", "      <td>5.820083</td>\n", "      <td>2.197225</td>\n", "      <td>6.056784</td>\n", "      <td>PHP</td>\n", "      <td>NaN</td>\n", "      <td>steady</td>\n", "      <td>1.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203907</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>559</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>-0.388112</td>\n", "      <td>3.416667</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>0.910361</td>\n", "      <td>0.022897</td>\n", "      <td>...</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>maturation</td>\n", "      <td>5.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203908</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>560</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.038462</td>\n", "      <td>2.416667</td>\n", "      <td>-1.609438e+00</td>\n", "      <td>0.691519</td>\n", "      <td>0.020046</td>\n", "      <td>...</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>maturation</td>\n", "      <td>5.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203909</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>561</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.402098</td>\n", "      <td>2.916667</td>\n", "      <td>7.884574e-01</td>\n", "      <td>0.975993</td>\n", "      <td>0.908849</td>\n", "      <td>...</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>maturation</td>\n", "      <td>5.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203910</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>562</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.325175</td>\n", "      <td>2.916667</td>\n", "      <td>-6.661338e-16</td>\n", "      <td>0.948664</td>\n", "      <td>0.500000</td>\n", "      <td>...</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>maturation</td>\n", "      <td>5.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203911</th>\n", "      <td>tree-sitter/tree-sitter</td>\n", "      <td>563</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>0.234266</td>\n", "      <td>3.250000</td>\n", "      <td>1.609438e+00</td>\n", "      <td>0.992305</td>\n", "      <td>0.994679</td>\n", "      <td>...</td>\n", "      <td>0.098761</td>\n", "      <td>5.153292</td>\n", "      <td>7.368340</td>\n", "      <td>3.784190</td>\n", "      <td>5.966147</td>\n", "      <td>Go</td>\n", "      <td>NaN</td>\n", "      <td>maturation</td>\n", "      <td>5.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203912 rows × 46 columns</p>\n", "</div>"], "text/plain": ["                         repo_name  standardized_time_weeks  pr_throughput  \\\n", "0       10up/autoshare-for-twitter                      486            0.0   \n", "1       10up/autoshare-for-twitter                      487            0.0   \n", "2       10up/autoshare-for-twitter                      488            4.0   \n", "3       10up/autoshare-for-twitter                      489            1.0   \n", "4       10up/autoshare-for-twitter                      490            3.0   \n", "...                            ...                      ...            ...   \n", "203907     tree-sitter/tree-sitter                      559            1.0   \n", "203908     tree-sitter/tree-sitter                      560            2.0   \n", "203909     tree-sitter/tree-sitter                      561           10.0   \n", "203910     tree-sitter/tree-sitter                      562            2.0   \n", "203911     tree-sitter/tree-sitter                      563            4.0   \n", "\n", "        pr_throughput_first  pr_throughput_last  rolling_slope  rolling_mean  \\\n", "0                       1.0                 2.0      -0.069930      0.666667   \n", "1                       1.0                 2.0      -0.034965      0.500000   \n", "2                       1.0                 2.0       0.122378      0.750000   \n", "3                       1.0                 2.0       0.097902      0.833333   \n", "4                       1.0                 2.0       0.143357      1.083333   \n", "...                     ...                 ...            ...           ...   \n", "203907                  1.0                13.0      -0.388112      3.416667   \n", "203908                  1.0                13.0       0.038462      2.416667   \n", "203909                  1.0                13.0       0.402098      2.916667   \n", "203910                  1.0                13.0       0.325175      2.916667   \n", "203911                  1.0                13.0       0.234266      3.250000   \n", "\n", "        rolling_rate_of_change  feature_sigmod_add  feature_sigmod_multiply  \\\n", "0                -6.931472e-01            0.493380                 0.386488   \n", "1                -1.098612e+00            0.354661                 0.366025   \n", "2                 9.162907e-01            0.841081                 0.665348   \n", "3                 6.931472e-01            0.821491                 0.640520   \n", "4                 1.386294e+00            0.921985                 0.817846   \n", "...                        ...                 ...                      ...   \n", "203907           -1.098612e+00            0.910361                 0.022897   \n", "203908           -1.609438e+00            0.691519                 0.020046   \n", "203909            7.884574e-01            0.975993                 0.908849   \n", "203910           -6.661338e-16            0.948664                 0.500000   \n", "203911            1.609438e+00            0.992305                 0.994679   \n", "\n", "        ...  log_commit_percent  log_commits  \\\n", "0       ...            0.196540     5.209486   \n", "1       ...            0.196540     5.209486   \n", "2       ...            0.196540     5.209486   \n", "3       ...            0.196540     5.209486   \n", "4       ...            0.196540     5.209486   \n", "...     ...                 ...          ...   \n", "203907  ...            0.098761     5.153292   \n", "203908  ...            0.098761     5.153292   \n", "203909  ...            0.098761     5.153292   \n", "203910  ...            0.098761     5.153292   \n", "203911  ...            0.098761     5.153292   \n", "\n", "        log_project_commits_before_treatment  \\\n", "0                                   5.820083   \n", "1                                   5.820083   \n", "2                                   5.820083   \n", "3                                   5.820083   \n", "4                                   5.820083   \n", "...                                      ...   \n", "203907                              7.368340   \n", "203908                              7.368340   \n", "203909                              7.368340   \n", "203910                              7.368340   \n", "203911                              7.368340   \n", "\n", "        log_project_contributors_before_treatment  \\\n", "0                                        2.197225   \n", "1                                        2.197225   \n", "2                                        2.197225   \n", "3                                        2.197225   \n", "4                                        2.197225   \n", "...                                           ...   \n", "203907                                   3.784190   \n", "203908                                   3.784190   \n", "203909                                   3.784190   \n", "203910                                   3.784190   \n", "203911                                   3.784190   \n", "\n", "        log_project_age_before_treatment  project_main_language attrition  \\\n", "0                               6.056784                    PHP       NaN   \n", "1                               6.056784                    PHP       NaN   \n", "2                               6.056784                    PHP       NaN   \n", "3                               6.056784                    PHP       NaN   \n", "4                               6.056784                    PHP       NaN   \n", "...                                  ...                    ...       ...   \n", "203907                          5.966147                     Go       NaN   \n", "203908                          5.966147                     Go       NaN   \n", "203909                          5.966147                     Go       NaN   \n", "203910                          5.966147                     Go       NaN   \n", "203911                          5.966147                     Go       NaN   \n", "\n", "        growth_phase  newcomers  newcomers_bool  \n", "0             steady        1.0           False  \n", "1             steady        1.0           False  \n", "2             steady        1.0           False  \n", "3             steady        1.0           False  \n", "4             steady        1.0           False  \n", "...              ...        ...             ...  \n", "203907    maturation        5.0           False  \n", "203908    maturation        5.0           False  \n", "203909    maturation        5.0           False  \n", "203910    maturation        5.0           False  \n", "203911    maturation        5.0           False  \n", "\n", "[203912 rows x 46 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["growth_phase\n", "steady          103611\n", "decelerating     66583\n", "accelerating     22793\n", "maturation        8457\n", "early phase       1274\n", "Name: count, dtype: int64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data['growth_phase'].value_counts()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["newcomers\n", "0.0      60113\n", "1.0      28402\n", "2.0      18436\n", "3.0      12969\n", "4.0       9763\n", "         ...  \n", "90.0        50\n", "108.0       50\n", "107.0       50\n", "136.0       35\n", "89.0        31\n", "Name: count, Length: 130, dtype: int64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data['newcomers'].value_counts()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["#  'newcomer_bool' = yes or no\n", "compiled_data['newcomer_bool'] = 'no'\n", "compiled_data.loc[compiled_data.newcomers > 0, 'newcomer_bool'] = 'yes'"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["newcomer_bool\n", "yes    142660\n", "no      61252\n", "Name: count, dtype: int64"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data['newcomer_bool'].value_counts()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# newcomers_analysis.to_csv(\"../result/did_result_20250212/newcomers_analysis.csv\", index=False)\n", "compiled_data.to_csv(\"../result/did_result_20250212/compiled_data_test_with_features_and_growth_phase_and_newcomers.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}