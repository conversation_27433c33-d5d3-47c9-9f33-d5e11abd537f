{"cells": [{"cell_type": "markdown", "id": "88196dc9", "metadata": {}, "source": ["1. file path\n", "/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit180_processed_ultra_optimized.csv\n", "/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit270_processed_ultra_optimized.csv\n", "/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit450_processed_ultra_optimized.csv"]}, {"cell_type": "code", "execution_count": 1, "id": "91b1188d", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘lme4’ was built under R version 4.3.3”\n", "Loading required package: Matrix\n", "\n", "Warning message:\n", "“package ‘Matrix’ was built under R version 4.3.3”\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘stargazer’ was built under R version 4.3.3”\n", "\n", "Please cite as: \n", "\n", "\n", " <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2022). stargazer: Well-Formatted Regression and Summary Statistics Tables.\n", "\n", " R package version 5.2.3. https://CRAN.R-project.org/package=stargazer \n", "\n", "\n", "Warning message:\n", "“package ‘lmtest’ was built under R version 4.3.3”\n", "Loading required package: zoo\n", "\n", "\n", "Attaching package: ‘zoo’\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    as.Date, as.Date.numeric\n", "\n", "\n", "Warning message:\n", "“package ‘MuMIn’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘lmerTest’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘lmerTest’\n", "\n", "\n", "The following object is masked from ‘package:lme4’:\n", "\n", "    lmer\n", "\n", "\n", "The following object is masked from ‘package:stats’:\n", "\n", "    step\n", "\n", "\n", "Warning message:\n", "“package ‘ggpubr’ was built under R version 4.3.3”\n", "Registered S3 method overwritten by 'broom':\n", "  method        from \n", "  nobs.multinom MuMIn\n", "\n", "Warning message:\n", "“package ‘survminer’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘survminer’\n", "\n", "\n", "The following object is masked from ‘package:survival’:\n", "\n", "    myeloma\n", "\n", "\n", "Loading required package: carData\n", "\n", "Warning message:\n", "“package ‘coxme’ was built under R version 4.3.3”\n", "Loading required package: bdsmatrix\n", "\n", "\n", "Attaching package: ‘bdsmatrix’\n", "\n", "\n", "The following object is masked from ‘package:base’:\n", "\n", "    backsolve\n", "\n", "\n", "Registered S3 methods overwritten by 'coxme':\n", "  method        from \n", "  formula.coxme MuMIn\n", "  logLik.lmekin Mu<PERSON>n\n", "\n", "\n", "Attaching package: ‘dplyr’\n", "\n", "\n", "The following object is masked from ‘package:car’:\n", "\n", "    recode\n", "\n", "\n", "The following objects are masked from ‘package:stats’:\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n"]}], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "library(dplyr)"]}, {"cell_type": "code", "execution_count": 8, "id": "9833ae4f", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# # Limit 450\n", "# limit = 450\n", "# compiled_data_test <- read.csv(\"/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit450_processed_ultra_optimized.csv\")\n", "\n", "# # <PERSON>it 270\n", "# limit = 270\n", "# compiled_data_test <- read.csv(\"/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit270_processed_ultra_optimized.csv\")\n", "\n", "# Limit 180\n", "# limit = 365\n", "# compiled_data_test <- read.csv(\"/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit365_processed.csv\")\n", "\n", "compiled_data_test <- read.csv(\"/home/<USER>/repo/disengagement/result/20250730_did_result_psm_matching/compiled_data_test_limit450_processed.csv\")"]}, {"cell_type": "code", "execution_count": 9, "id": "4d58be72", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "    log_newcomers = scale(log_newcomers),\n", "  )\n", "\n", "  # 移除含有缺失值的观测（确保数据清洁）\n", "  # tidyr::drop_na()\n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fe2cf1c6", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "  accelerating first 3 months     saturation         steady \n", "        898657           3863         372913        4314696 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# # exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "# compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'),]\n", "# table(compiled_data_test$growth_phase)"]}, {"cell_type": "markdown", "id": "9b0914d5", "metadata": {}, "source": ["## 1. Main Treatment Effect"]}, {"cell_type": "markdown", "id": "12aee18c", "metadata": {}, "source": ["### 1.1 main-PR throughput"]}, {"cell_type": "code", "execution_count": 10, "id": "c9fa983f", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.127375                     1.057389 \n", "         log_project_commits     log_project_contributors \n", "                    1.895297                     2.166476 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.423118                     1.171607 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", "12862775 12862910 -6431378 12862755  5078663 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.8161 -0.7139 -0.1008  0.6720  6.4797 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " time_cohort_effect (Intercept) 0.007335 0.08565 \n", " repo_cohort_effect (Intercept) 0.268738 0.51840 \n", " Residual                       0.703620 0.83882 \n", "Number of obs: 5078673, groups:  \n", "time_cohort_effect, 69170; repo_cohort_effect, 69170\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   1.025e+00  2.883e-03  6.581e+04  355.61   <2e-16\n", "is_post_treatment            -6.809e-02  1.051e-03  4.297e+04  -64.76   <2e-16\n", "is_treated                    9.079e-02  4.180e-03  7.256e+04   21.72   <2e-16\n", "log_project_commits           3.791e-01  6.335e-04  4.988e+06  598.46   <2e-16\n", "log_project_contributors      1.299e-01  6.762e-04  5.016e+06  192.08   <2e-16\n", "log_project_age              -3.029e-01  5.793e-04  4.980e+06 -522.91   <2e-16\n", "is_post_treatment:is_treated -8.860e-02  1.984e-03  4.982e+06  -44.65   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.179                                                 \n", "is_treated  -0.673  0.077                                          \n", "lg_prjct_cm -0.003 -0.006  0.020                                   \n", "lg_prjct_cn  0.004  0.009 -0.023 -0.596                            \n", "log_prjct_g  0.023 -0.085 -0.031 -0.101      -0.364                \n", "is_pst_tr:_  0.057 -0.323 -0.228  0.001      -0.004       0.015    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.1498537</td><td>0.3894211</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.1498537 & 0.3894211\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.1498537 | 0.3894211 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.1498537 0.3894211"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 1: Fixed Effects Only\n", "model_fixed_effects_only <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": null, "id": "674a82c8", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# Create directory if it doesn't exist\n", "dir.create(f\"../result/20250629_did_result/attrition_{limit}\", recursive = TRUE, showWarnings = FALSE)\n", "# Save model results to file\n", "sink(f\"../result/20250629_did_result/attrition_{limit}/did_main_pr_throughput.txt\")\n", "\n", "cat(\"=== DID Model Results: Main PR Throughput ===\\n\\n\")\n", "\n", "cat(\"Model Formula:\\n\")\n", "cat(\"log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\\n\")\n", "cat(\"  log_project_commits + log_project_contributors + log_project_age +\\n\")\n", "cat(\"  (1 | time_cohort_effect) + (1 | repo_cohort_effect)\\n\\n\")\n", "\n", "cat(\"VIF Values:\\n\")\n", "print(vif_model_fixed_effects_only)\n", "cat(\"\\n\")\n", "\n", "cat(\"Model Summary:\\n\")\n", "print(summary(model_fixed_effects_only))\n", "cat(\"\\n\")\n", "\n", "cat(\"R-squared Values:\\n\")\n", "print(r.squaredGLMM(model_fixed_effects_only))\n", "cat(\"\\n\")\n", "\n", "cat(\"=== End of Results ===\\n\")\n", "\n", "sink()\n"]}, {"cell_type": "markdown", "id": "a82bf4c0", "metadata": {}, "source": ["### 1.2 main-PR accept rate"]}, {"cell_type": "code", "execution_count": 7, "id": "5cdcc6c0", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.062806                     1.092481 \n", "         log_project_commits     log_project_contributors \n", "                    1.801815                     1.803744 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.135818                     1.146255 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-22585686 -22585542  11292853 -22585706  12940457 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-6.4132 -0.0315  0.2628  0.5317  4.7396 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " time_cohort_effect (Intercept) 0.0003913 0.01978 \n", " repo_cohort_effect (Intercept) 0.0023132 0.04810 \n", " Residual                       0.0097369 0.09868 \n", "Number of obs: 12940467, groups:  \n", "time_cohort_effect, 187502; repo_cohort_effect, 187502\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   6.381e-01  1.753e-04  1.901e+05 3639.73   <2e-16\n", "is_post_treatment             2.944e-03  1.101e-04  1.080e+05   26.73   <2e-16\n", "is_treated                   -4.263e-03  2.440e-04  1.816e+05  -17.47   <2e-16\n", "log_project_commits          -9.411e-03  4.717e-05  1.211e+07 -199.51   <2e-16\n", "log_project_contributors     -1.217e-02  4.706e-05  1.254e+07 -258.59   <2e-16\n", "log_project_age               8.255e-03  3.808e-05  1.243e+07  216.76   <2e-16\n", "is_post_treatment:is_treated -2.921e-03  1.432e-04  1.270e+07  -20.40   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.312                                                 \n", "is_treated  -0.623  0.071                                          \n", "lg_prjct_cm -0.010 -0.004  0.038                                   \n", "lg_prjct_cn  0.016 -0.005 -0.045 -0.622                            \n", "log_prjct_g  0.028 -0.042 -0.054 -0.149      -0.145                \n", "is_pst_tr:_  0.073 -0.238 -0.281  0.003      -0.002       0.006    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.02591632</td><td>0.2376625</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.02591632 & 0.2376625\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.02591632 | 0.2376625 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.02591632 0.2376625"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model_fixed_effects_only <- lmer(\n", "  log_pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": null, "id": "71643ac4", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# Create directory if it doesn't exist\n", "dir.create(f\"../result/20250629_did_result/attrition_{limit}\", recursive = TRUE, showWarnings = FALSE)\n", "# Save model results to file\n", "sink(f\"../result/20250629_did_result/attrition_{limit}/did_main_pr_accept_rate.txt\")\n", "\n", "cat(\"=== DID Model Results: Main PR Accept Rate ===\\n\\n\")\n", "\n", "cat(\"Model Formula:\\n\")\n", "cat(\"log_pr_accept_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\\n\")\n", "cat(\"  log_project_commits + log_project_contributors + log_project_age +\\n\")\n", "cat(\"  (1 | time_cohort_effect) + (1 | repo_cohort_effect)\\n\\n\")\n", "\n", "cat(\"VIF Values:\\n\")\n", "print(vif_model_fixed_effects_only)\n", "cat(\"\\n\")\n", "\n", "cat(\"Model Summary:\\n\")\n", "print(summary(model_fixed_effects_only))\n", "cat(\"\\n\")\n", "\n", "cat(\"R-squared Values:\\n\")\n", "print(r.squaredGLMM(model_fixed_effects_only))\n", "cat(\"\\n\")\n", "\n", "cat(\"=== End of Results ===\\n\")\n", "\n", "sink()\n"]}, {"cell_type": "markdown", "id": "9187a318", "metadata": {}, "source": ["### 1.3 main-PR time to merge"]}, {"cell_type": "code", "execution_count": 8, "id": "f0595b63", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.041974                     1.074066 \n", "         log_project_commits     log_project_contributors \n", "                    1.799189                     1.799083 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.132556                     1.109140 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", " 54525570  54525714 -27262775  54525550  12940457 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2232 -0.7322 -0.0020  0.6893  4.7716 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " time_cohort_effect (Intercept) 0.2545   0.5044  \n", " repo_cohort_effect (Intercept) 1.1355   1.0656  \n", " Residual                       3.7455   1.9353  \n", "Number of obs: 12940467, groups:  \n", "time_cohort_effect, 187502; repo_cohort_effect, 187502\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.387e+00  3.944e-03  2.066e+05  858.93   <2e-16\n", "is_post_treatment             1.210e-01  2.625e-03  1.058e+05   46.09   <2e-16\n", "is_treated                    2.053e-01  5.305e-03  1.740e+05   38.70   <2e-16\n", "log_project_commits          -1.497e-01  9.297e-04  1.249e+07 -161.07   <2e-16\n", "log_project_contributors      4.647e-01  9.264e-04  1.273e+07  501.61   <2e-16\n", "log_project_age               1.061e-01  7.503e-04  1.269e+07  141.36   <2e-16\n", "is_post_treatment:is_treated  3.905e-02  2.811e-03  1.270e+07   13.89   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.331                                                 \n", "is_treated  -0.615  0.053                                          \n", "lg_prjct_cm -0.009 -0.003  0.034                                   \n", "lg_prjct_cn  0.014 -0.005 -0.041 -0.621                            \n", "log_prjct_g  0.024 -0.035 -0.049 -0.149      -0.143                \n", "is_pst_tr:_  0.064 -0.197 -0.254  0.003      -0.002       0.006    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.03895718</td><td>0.2990735</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.03895718 & 0.2990735\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.03895718 | 0.2990735 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.03895718 0.2990735"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model_fixed_effects_only <- lmer(\n", "  log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9c8c4f08", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DID Model Results: Main PR Time to Merge ===\n", "\n", "Model Formula:\n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "  log_project_commits + log_project_contributors + log_project_age +\n", "  (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "\n", "VIF Values:\n", "           is_post_treatment                   is_treated \n", "                    1.041974                     1.074066 \n", "         log_project_commits     log_project_contributors \n", "                    1.799189                     1.799083 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.132556                     1.109140 \n", "\n", "Model Summary:\n", "Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", " 54525570  54525714 -27262775  54525550  12940457 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-4.2232 -0.7322 -0.0020  0.6893  4.7716 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " time_cohort_effect (Intercept) 0.2545   0.5044  \n", " repo_cohort_effect (Intercept) 1.1355   1.0656  \n", " Residual                       3.7455   1.9353  \n", "Number of obs: 12940467, groups:  \n", "time_cohort_effect, 187502; repo_cohort_effect, 187502\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.387e+00  3.944e-03  2.066e+05  858.93   <2e-16\n", "is_post_treatment             1.210e-01  2.625e-03  1.058e+05   46.09   <2e-16\n", "is_treated                    2.053e-01  5.305e-03  1.740e+05   38.70   <2e-16\n", "log_project_commits          -1.497e-01  9.297e-04  1.249e+07 -161.07   <2e-16\n", "log_project_contributors      4.647e-01  9.264e-04  1.273e+07  501.61   <2e-16\n", "log_project_age               1.061e-01  7.503e-04  1.269e+07  141.36   <2e-16\n", "is_post_treatment:is_treated  3.905e-02  2.811e-03  1.270e+07   13.89   <2e-16\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.331                                                 \n", "is_treated  -0.615  0.053                                          \n", "lg_prjct_cm -0.009 -0.003  0.034                                   \n", "lg_prjct_cn  0.014 -0.005 -0.041 -0.621                            \n", "log_prjct_g  0.024 -0.035 -0.049 -0.149      -0.143                \n", "is_pst_tr:_  0.064 -0.197 -0.254  0.003      -0.002       0.006    \n", "\n", "R-squared Values:\n", "            R2m       R2c\n", "[1,] 0.03895718 0.2990735\n", "\n", "=== End of Results ===\n", "File successfully created: ../result/20250629_did_result/attrition_180/did_main_pr_time_to_merge.txt \n", "File size: 0 bytes\n"]}], "source": ["# Create directory if it doesn't exist\n", "dir.create(\"../result/20250629_did_result/attrition_180\", recursive = TRUE, showWarnings = FALSE)\n", "\n", "# Save model results to file\n", "file_path <- \"../result/20250629_did_result/attrition_180/did_main_pr_time_to_merge.txt\"\n", "sink(file_path)\n", "\n", "cat(\"=== DID Model Results: Main PR Time to Merge ===\\n\\n\")\n", "\n", "cat(\"Model Formula:\\n\")\n", "cat(\"log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\\n\")\n", "cat(\"  log_project_commits + log_project_contributors + log_project_age +\\n\")\n", "cat(\"  (1 | time_cohort_effect) + (1 | repo_cohort_effect)\\n\\n\")\n", "\n", "cat(\"VIF Values:\\n\")\n", "print(vif_model_fixed_effects_only)\n", "cat(\"\\n\")\n", "\n", "cat(\"Model Summary:\\n\")\n", "print(summary(model_fixed_effects_only))\n", "cat(\"\\n\")\n", "\n", "cat(\"R-squared Values:\\n\")\n", "print(r.squaredGLMM(model_fixed_effects_only))\n", "cat(\"\\n\")\n", "\n", "cat(\"=== End of Results ===\\n\")\n", "\n", "sink()\n", "\n"]}, {"cell_type": "markdown", "id": "c15d1dff", "metadata": {}, "source": ["## 2. Moderating Effect"]}, {"cell_type": "code", "execution_count": 51, "id": "cd73340d", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)\n", "compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)\n", "# # set level of project_main_language\n", "compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = \"JavaScript\")\n", "compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = \"steady\")\n", "contrasts(compiled_data_test$project_main_language) <- \"contr.sum\"\n", "contrasts(compiled_data_test$growth_phase) <- \"contr.sum\""]}, {"cell_type": "code", "execution_count": 22, "id": "6a3bab44", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "        steady   accelerating   decelerating first 3 months     saturation \n", "       5442052        1552886        4399893           6204        1539432 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# exclude empty growth phase or ''\n", "compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'),]\n", "table(compiled_data_test$growth_phase)"]}, {"cell_type": "code", "execution_count": 25, "id": "916e2e27", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "JavaScript          C         C#        C++         Go       Java        PHP \n", "   2440428     776305     616861    1086488    1234730    1241379     716567 \n", "    Python       Rust TypeScript \n", "   2854352     541960    1431397 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["table(compiled_data_test$project_main_language )"]}, {"cell_type": "code", "execution_count": null, "id": "4381a4a3", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "eacf1550", "metadata": {}, "source": ["### 2.1 moderating-pr throughput\n"]}, {"cell_type": "code", "execution_count": 43, "id": "e659c6ef", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一步：成功從公式中提取了以下變量名：\n", " [1] \"log_pr_throughput\"                        \n", " [2] \"is_post_treatment\"                        \n", " [3] \"is_treated\"                               \n", " [4] \"log_tenure_c\"                             \n", " [5] \"log_commit_percent_c\"                     \n", " [6] \"log_commits_c\"                            \n", " [7] \"log_newcomers\"                            \n", " [8] \"log_project_commits_before_treatment\"     \n", " [9] \"log_project_contributors_before_treatment\"\n", "[10] \"log_project_age_before_treatment\"         \n", "[11] \"growth_phase\"                             \n", "[12] \"project_main_language\"                    \n", "[13] \"log_project_commits\"                      \n", "[14] \"log_project_contributors\"                 \n", "[15] \"log_project_age\"                          \n", "[16] \"time_cohort_effect\"                       \n", "[17] \"repo_cohort_effect\"                       \n", "\n", "第二步：成功創建了只包含模型變量的縮小版數據框 'subset_data'。\n", "\n", "第三步：開始對縮小後的 'subset_data' 進行深度偵錯...\n", "是否存在空列名:  FALSE \n", "\n", "第四步：在縮小後的數據框上，重新統計缺失值...\n", "                  變量名 缺失值數量\n", "1      log_pr_throughput          0\n", "2      is_post_treatment          0\n", "3             is_treated          0\n", "4                            216318\n", "5                                 0\n", "6                                 0\n", "7          log_newcomers          0\n", "8                          12940467\n", "9                          12940467\n", "10                         12940467\n", "11          growth_phase          0\n", "12 project_main_language          0\n", "13                                0\n", "14                                0\n", "15                                0\n", "16    time_cohort_effect          0\n", "17    repo_cohort_effect          0\n"]}], "source": ["# --------------------【請運行這個完整的程式碼塊】--------------------\n", "\n", "# 步驟 1: 獲取公式中所有變量名 (這步不變)\n", "# 確保 my_formula 物件仍然存在\n", "model_vars <- all.vars(my_formula)\n", "\n", "cat(\"第一步：成功從公式中提取了以下變量名：\\n\")\n", "print(model_vars)\n", "cat(\"\\n\") # 換行讓輸出更清晰\n", "\n", "\n", "# 步驟 2: 【執行您的想法】根據變量名，創建一個只包含相關列的【新】數據框\n", "# 我們使用[, model_vars]語法來安全地選取列\n", "# 使用 try() 來避免因列名不存在而直接報錯\n", "subset_data <- NULL # 先清空\n", "try({\n", "  subset_data <- compiled_data_test[, model_vars]\n", "}, silent = TRUE)\n", "\n", "if (is.null(subset_data)) {\n", "  cat(\"第二步失敗：無法根據公式中的變量名來創建數據子集。\\n\")\n", "  cat(\"請檢查第一步輸出的變量名，是否都能在 compiled_data_test 中找到對應的列。\\n\")\n", "} else {\n", "  cat(\"第二步：成功創建了只包含模型變量的縮小版數據框 'subset_data'。\\n\\n\")\n", "\n", "  # 步驟 3: 【關鍵偵錯】現在，我們只對這個新的、更小的 subset_data 進行偵錯\n", "\n", "  cat(\"第三步：開始對縮小後的 'subset_data' 進行深度偵錯...\\n\")\n", "\n", "  # 檢查新數據框的列名是否存在空值\n", "  any_blank_names <- any(colnames(subset_data) == \"\")\n", "  cat(\"是否存在空列名: \", any_blank_names, \"\\n\")\n", "\n", "  if (any_blank_names) {\n", "    blank_col_indices <- which(colnames(subset_data) == \"\")\n", "    cat(\"空列名的位置 (在縮小後的數據框中是第幾列): \", blank_col_indices, \"\\n\")\n", "  }\n", "  cat(\"\\n\")\n", "\n", "  # 步驟 4: 最後，再次運行缺失值檢查，但這次是在這個乾淨的數據子集上\n", "  cat(\"第四步：在縮小後的數據框上，重新統計缺失值...\\n\")\n", "  na_counts_subset <- colSums(is.na(subset_data))\n", "\n", "  na_df_subset <- data.frame(\n", "    變量名 = names(na_counts_subset),\n", "    缺失值數量 = na_counts_subset,\n", "    row.names = NULL\n", "  )\n", "  \n", "  # 打印最終的診斷結果\n", "  print(na_df_subset)\n", "}\n", "# --------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "id": "30d3c98a", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "05034323", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): object 'model_fixed_effects_developer_repo' not found\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): object 'model_fixed_effects_developer_repo' not found\nTraceback:\n", "1. car::vif(model_fixed_effects_developer_repo, type = \"predictor\", \n .     singular.ok = TRUE)"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["model_fixed_effects_repo_add_two <- lmer(\n", "  log_pr_throughput ~ \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    \n", "    # Repo\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:project_main_language +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "\n", "vif_model <- car::vif(\n", "  model_fixed_effects_developer_repo,\n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_repo_add_two,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_fixed_effects_repo_add_two, \n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 将模型公式定义为一个独立对象\n", "my_formula <- formula(\n", "  log_pr_throughput ~\n", "    # 主效应\n", " # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # Core Dev\n", "    is_post_treatment:is_treated:log_tenure_c +\n", "    is_post_treatment:is_treated:log_commit_percent_c +\n", "    is_post_treatment:is_treated:log_commits_c +\n", "    \n", "\n", "    # Repo\n", "\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:project_main_language +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", ")"]}, {"cell_type": "markdown", "id": "62a90d8a", "metadata": {}, "source": ["### 2.2 moderating-pr accept rate"]}, {"cell_type": "code", "execution_count": null, "id": "6c0e77fb", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["model_fixed_effects_repo_add_two <- lmer(\n", "  log_pull_request_success_rate ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # 三重交互项（标准化后）\n", "\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_fixed_effects_repo_add_two, \n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_fixed_effects_repo_add_two) %>% plot()\n", "# performance::model_performance(model_fixed_effects_repo_add_two) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_repo_add_two,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_fixed_effects_repo_add_two, \n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}, {"cell_type": "markdown", "id": "060261d1", "metadata": {}, "source": ["### 2.3 moderating-pr time to merge"]}, {"cell_type": "code", "execution_count": null, "id": "ee038e4b", "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["model_fixed_effects_repo_add_two <- lmer(\n", "  log_time_to_merge ~  \n", "    # 主效应\n", "    is_post_treatment + is_treated +  # 包含二阶交互\n", "    \n", "    # 三重交互项（标准化后）\n", "\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    \n", "    is_post_treatment:is_treated:project_main_language +\n", "    is_post_treatment:is_treated:growth_phase +\n", "\n", "    # 项目层面控制变量（已标准化）\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    \n", "    # 随机效应\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  \n", "  data = compiled_data_test,\n", "  REML = FALSE,\n", "  control = ctrl\n", ")\n", "\n", "# 计算VIF（使用car包改进方法）\n", "vif_model <- car::vif(\n", "  model_fixed_effects_repo_add_two, \n", "  type = \"predictor\",  # 适用于混合模型\n", "  singular.ok = TRUE    # 允许奇异值\n", ")\n", "print(vif_model)\n", "\n", "# 模型诊断（新增部分）\n", "# performance::check_collinearity(model_fixed_effects_repo_add_two) %>% plot()\n", "# performance::model_performance(model_fixed_effects_repo_add_two) %>% print()\n", "\n", "# 模型总结（优化显示）\n", "summary(model_fixed_effects_repo_add_two,\n", "        cor.max = 0.5,  # 仅显示|cor|>0.5的参数相关\n", "        signif.stars = TRUE)\n", "\n", "# R-squared计算（使用更稳健的方法）\n", "MuMIn::r.squaredGLMM(\n", "  model_fixed_effects_repo_add_two, \n", "  null = lmer(log_pr_throughput ~ 1 + (1|repo_cohort_effect), \n", "             data = compiled_data_test) # 更合理的空模型\n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 5}