#!/usr/bin/env python3
"""
将 productivity 数据中的 pull_request_success_rate 和 time_to_merge 列
合并到 compiled_data_test_limit 文件中，基于 repo_name 和 standardized_time_weeks
保持 NaN 值不变
"""

import pandas as pd
import numpy as np
import sys
import os

def merge_productivity_columns(compiled_data_path, productivity_data_path, output_path):
    """
    合并 productivity 数据中的两列到 compiled_data 中
    
    Args:
        compiled_data_path: compiled_data 文件路径
        productivity_data_path: productivity 数据文件路径  
        output_path: 输出文件路径
    """
    print("=" * 60)
    print("合并 productivity 列数据")
    print("=" * 60)
    
    # 读取 compiled_data
    print(f"读取 compiled_data: {compiled_data_path}")
    if not os.path.exists(compiled_data_path):
        print(f"❌ 文件不存在: {compiled_data_path}")
        return False
        
    compiled_data = pd.read_csv(compiled_data_path)
    print(f"  形状: {compiled_data.shape}")
    print(f"  列数: {len(compiled_data.columns)}")
    
    # 读取 productivity 数据
    print(f"\n读取 productivity 数据: {productivity_data_path}")
    if not os.path.exists(productivity_data_path):
        print(f"❌ 文件不存在: {productivity_data_path}")
        return False
        
    productivity_data = pd.read_csv(productivity_data_path)
    print(f"  形状: {productivity_data.shape}")
    print(f"  列数: {len(productivity_data.columns)}")
    
    # 检查关键列是否存在
    required_merge_cols = ['repo_name', 'standardized_time_weeks']
    required_productivity_cols = ['pull_request_success_rate', 'time_to_merge']
    
    # 检查合并键
    missing_compiled_cols = [col for col in required_merge_cols if col not in compiled_data.columns]
    missing_productivity_cols = [col for col in required_merge_cols + required_productivity_cols 
                                if col not in productivity_data.columns]
    
    if missing_compiled_cols:
        print(f"❌ compiled_data 缺失列: {missing_compiled_cols}")
        return False
        
    if missing_productivity_cols:
        print(f"❌ productivity_data 缺失列: {missing_productivity_cols}")
        return False
    
    print("✅ 所有必需列都存在")
    
    # 检查是否已经存在这两列
    existing_cols = [col for col in required_productivity_cols if col in compiled_data.columns]
    if existing_cols:
        print(f"⚠️  compiled_data 中已存在列: {existing_cols}")
        print("将会被覆盖")
    
    # 准备要合并的数据
    productivity_subset = productivity_data[required_merge_cols + required_productivity_cols].copy()
    
    print(f"\nproductivity 数据中的缺失值情况:")
    for col in required_productivity_cols:
        null_count = productivity_subset[col].isnull().sum()
        null_percent = (null_count / len(productivity_subset)) * 100
        print(f"  {col}: {null_count:,} ({null_percent:.2f}%)")
    
    # 执行左连接合并
    print(f"\n执行合并...")
    print(f"  合并键: {required_merge_cols}")
    print(f"  合并列: {required_productivity_cols}")
    
    # 记录合并前的形状
    original_shape = compiled_data.shape
    
    # 执行合并 - 使用 left join 保持 compiled_data 的所有记录
    merged_data = compiled_data.merge(
        productivity_subset,
        on=required_merge_cols,
        how='left',
        suffixes=('', '_new')
    )
    
    # 如果存在重复列名，使用新的数据覆盖
    for col in required_productivity_cols:
        if f'{col}_new' in merged_data.columns:
            merged_data[col] = merged_data[f'{col}_new']
            merged_data.drop(f'{col}_new', axis=1, inplace=True)
    
    print(f"✅ 合并完成")
    print(f"  原始形状: {original_shape}")
    print(f"  合并后形状: {merged_data.shape}")
    
    # 检查合并结果
    print(f"\n合并后的缺失值情况:")
    for col in required_productivity_cols:
        null_count = merged_data[col].isnull().sum()
        null_percent = (null_count / len(merged_data)) * 100
        print(f"  {col}: {null_count:,} ({null_percent:.2f}%)")
    
    # 验证合并的正确性
    print(f"\n验证合并结果:")
    
    # 检查是否有匹配的记录
    matched_records = merged_data[required_productivity_cols].notna().any(axis=1).sum()
    total_records = len(merged_data)
    match_rate = (matched_records / total_records) * 100
    
    print(f"  总记录数: {total_records:,}")
    print(f"  有匹配数据的记录: {matched_records:,} ({match_rate:.2f}%)")
    print(f"  完全无匹配的记录: {total_records - matched_records:,}")
    
    # 保存结果
    print(f"\n保存合并结果到: {output_path}")
    merged_data.to_csv(output_path, index=False)
    
    print("✅ 合并完成！")
    return True

def main():
    """主函数"""
    # 文件路径配置
    base_dir = '/home/<USER>/repo/disengagement'
    
    # compiled_data 文件路径 - 需要根据实际情况调整
    compiled_data_path = f'{base_dir}/result/did_result_20250310/productivity_20250310_with_propensity_scores_with_attritions.csv'
    
    # productivity 数据文件路径 - 需要根据实际情况调整
    productivity_data_path = f'{base_dir}/result/did_result_20250310/productivity_20250310_with_propensity_scores_with_attritions.csv'
    
    # 输出文件路径
    output_path = compiled_data_path.replace('.csv', '_with_productivity_columns.csv')
    
    print("文件路径配置:")
    print(f"  compiled_data: {compiled_data_path}")
    print(f"  productivity_data: {productivity_data_path}")
    print(f"  输出文件: {output_path}")
    
    # 检查文件是否存在
    if not os.path.exists(compiled_data_path):
        print(f"❌ compiled_data 文件不存在，请检查路径")
        print("请提供正确的 compiled_data_test_limit 文件路径")
        return
    
    if not os.path.exists(productivity_data_path):
        print(f"❌ productivity 数据文件不存在，请检查路径")
        print("请提供正确的 productivity 数据文件路径")
        return
    
    # 执行合并
    success = merge_productivity_columns(compiled_data_path, productivity_data_path, output_path)
    
    if success:
        print(f"\n🎉 成功！现在可以使用 {output_path} 文件运行 add_derived_variables 函数")
    else:
        print(f"\n❌ 合并失败，请检查错误信息")

if __name__ == "__main__":
    main()
