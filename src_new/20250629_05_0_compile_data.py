import pandas as pd
import os
import logging
import numpy as np
import gc
import pickle
from typing import Dict, List
import time
import argparse

def setup_logging():
    """设置日志配置"""
    log_dir = "../logs/compile_data"
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志文件
    log_file = os.path.join(log_dir, "compile_data.log")
    
    # 配置logger
    logger = logging.getLogger('compile_data')
    logger.setLevel(logging.INFO)
    
    # 移除现有handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 添加文件handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s")
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 添加控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

def compile_data_from_matched_pairs(
    matched_pairs: Dict,
    productivity: pd.DataFrame,
    window_size: int,
    batch_size: int = 1000,
    log_interval: int = 1000
) -> pd.DataFrame:
    """从匹配结果编译数据 - 优化版本"""
    # 获取logger
    logger = logging.getLogger('compile_data')
    logger.info(f"Starting compilation of {len(matched_pairs)} matched pairs")
    
    # 为提高性能, 先对productivity按repo_name进行分组
    repo_groups = dict(tuple(productivity.groupby('repo_name')))
    
    # 批处理键值以减少内存使用
    all_keys = list(matched_pairs.keys())
    total_batches = (len(all_keys) + batch_size - 1) // batch_size
    
    all_data = []
    processed = 0
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_keys))
        batch_keys = all_keys[start_idx:end_idx]
        
        batch_data = []
        
        for item_key in batch_keys:
            matched_data = matched_pairs[item_key]
            burst_id = matched_data["burst"]
            repo_name = matched_data["repo_name"]
            treatment_time = matched_data["treatment_time"]
            control_groups = matched_data["controls"]
            
            # 只在repo_groups里有数据时处理
            if repo_name in repo_groups:
                # 处理treatment数据 - 使用预先分组的数据
                repo_data = repo_groups[repo_name]
                treatment_mask = (
                    (repo_data['standardized_time_weeks'] >= treatment_time - window_size) & 
                    (repo_data['standardized_time_weeks'] <= treatment_time + window_size)
                )
                treatment_productivity = repo_data[treatment_mask].copy()
                
                if not treatment_productivity.empty:
                    treatment_productivity['relativized_time'] = treatment_productivity['standardized_time_weeks'] - treatment_time
                    treatment_productivity['is_treated'] = 1
                    treatment_productivity['post_treatment'] = treatment_productivity['relativized_time'] > 0
                    treatment_productivity['cohort_id'] = processed
                    batch_data.append(treatment_productivity)
                
                    # 处理control数据
                    for c in control_groups:
                        control_repo = c['repo_name']
                        control_time = c['matched_time']
                        
                        if control_repo in repo_groups:
                            control_repo_data = repo_groups[control_repo]
                            control_mask = (
                                (control_repo_data['standardized_time_weeks'] >= control_time - window_size) & 
                                (control_repo_data['standardized_time_weeks'] <= control_time + window_size)
                            )
                            control_data = control_repo_data[control_mask].copy()
                            
                            if not control_data.empty:
                                control_data['relativized_time'] = control_data['standardized_time_weeks'] - control_time
                                control_data['is_treated'] = 0
                                control_data['post_treatment'] = control_data['relativized_time'] > 0
                                control_data['cohort_id'] = processed
                                batch_data.append(control_data)
                    
                    processed += 1
            
            # 每处理一定数量记录日志
            if processed % log_interval == 0:
                logger.info(f"Processed {processed}/{len(matched_pairs)} matched pairs")
        
        # 合并当前批次并添加到结果
        if batch_data:
            batch_result = pd.concat(batch_data)
            all_data.append(batch_result)
            
            # 清理内存
            del batch_data
            gc.collect()
            
        logger.info(f"Completed batch {batch_idx+1}/{total_batches}, total processed: {processed}")
    
    # 合并所有批次数据
    if all_data:
        logger.info(f"Concatenating all {len(all_data)} batches")
        final_data = pd.concat(all_data)
        logger.info(f"Final compiled data shape: {final_data.shape}")
        return final_data
    else:
        logger.warning("No data to compile")
        return pd.DataFrame()

def load_matched_pairs(file_path: str) -> Dict:
    """加载已保存的matched_pairs文件"""
    logger = logging.getLogger('compile_data')
    logger.info(f"Loading matched pairs from: {file_path}")
    
    try:
        with open(file_path, "rb") as f:
            matched_pairs = pickle.load(f)
        logger.info(f"Successfully loaded {len(matched_pairs)} matched pairs")
        return matched_pairs
    except Exception as e:
        logger.error(f"Failed to load matched pairs from {file_path}: {e}")
        raise

def load_productivity_data(file_path: str) -> pd.DataFrame:
    """加载生产力数据"""
    logger = logging.getLogger('compile_data')
    logger.info(f"Loading productivity data from: {file_path}")
    
    try:
        productivity = pd.read_csv(file_path)
        # 转换类型
        productivity["standardized_time_weeks"] = productivity["standardized_time_weeks"].astype(int)
        logger.info(f"Successfully loaded productivity data with shape: {productivity.shape}")
        return productivity
    except Exception as e:
        logger.error(f"Failed to load productivity data from {file_path}: {e}")
        raise

def process_single_compilation(
    limit: int,
    window: int,
    output_dir: str,
    batch_size: int = 1000,
    log_interval: int = 1000
) -> bool:
    """处理单个limit和window的编译任务"""
    logger = logging.getLogger('compile_data')
    
    start_time = time.time()
    logger.info(f"Starting compilation for limit: {limit}, window: {window}")
    
    try:
        # 构建文件路径
        matched_pairs_file = os.path.join(output_dir, f"matched_pairs_limit{limit}_window{window}.pkl")
        productivity_file = os.path.join(output_dir, f"productivity_with_propensity_scores_with_attritions_{limit}.csv")
        output_file = os.path.join(output_dir, f"compiled_data_test_limit{limit}_window{window}.csv")
        
        # 检查输入文件是否存在
        if not os.path.exists(matched_pairs_file):
            logger.error(f"Matched pairs file not found: {matched_pairs_file}")
            return False
            
        if not os.path.exists(productivity_file):
            logger.error(f"Productivity file not found: {productivity_file}")
            return False
        
        # 加载数据
        matched_pairs = load_matched_pairs(matched_pairs_file)
        productivity = load_productivity_data(productivity_file)
        
        # 编译数据
        logger.info(f"Starting data compilation for {len(matched_pairs)} matched pairs")
        compiled_data = compile_data_from_matched_pairs(
            matched_pairs, 
            productivity, 
            window,
            batch_size=batch_size,
            log_interval=log_interval
        )
        
        if not compiled_data.empty:
            # 添加额外的列
            compiled_data_test = compiled_data.copy()
            compiled_data_test['is_post_treatment'] = compiled_data_test['post_treatment'].astype(int)
            compiled_data_test['is_treated'] = compiled_data_test['is_treated'].astype(int)
            compiled_data_test['is_treated_post_treatment'] = compiled_data_test['is_treated'] * compiled_data_test['post_treatment']

            # 保存结果
            compiled_data_test.to_csv(output_file, index=False)
            logger.info(f"Compiled data saved to {output_file}")
            
            total_time = time.time() - start_time
            logger.info(f"Compilation completed for limit {limit}, window {window} in {total_time:.1f} seconds")
            return True
        else:
            logger.warning(f"No compiled data for limit {limit}, window {window}")
            return False
            
    except Exception as e:
        logger.error(f"Error processing limit {limit}, window {window}: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Compile data from matched pairs')
    parser.add_argument('--limit', type=int, required=True, help='Attrition limit')
    parser.add_argument('--window', type=int, default=12, help='Window size (default: 12)')
    parser.add_argument('--output_dir', type=str, default='../result/20250730_did_result/', help='Output directory')
    parser.add_argument('--batch_size', type=int, default=1000, help='Batch size for processing')
    parser.add_argument('--log_interval', type=int, default=1000, help='Logging interval')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    logger.info(f"Starting compilation process")
    logger.info(f"Limit: {args.limit}")
    logger.info(f"Window: {args.window}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info(f"Log interval: {args.log_interval}")
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 处理编译任务
    success = process_single_compilation(
        limit=args.limit,
        window=args.window,
        output_dir=args.output_dir,
        batch_size=args.batch_size,
        log_interval=args.log_interval
    )
    
    if success:
        logger.info("Compilation process completed successfully")
    else:
        logger.error("Compilation process failed")
        exit(1)

if __name__ == "__main__":
    print(f"Script started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    main()
    print(f"Script completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}") 