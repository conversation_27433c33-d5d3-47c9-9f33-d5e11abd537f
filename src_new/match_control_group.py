# import pandas as pd
# import os
# import logging
# import numpy as np
# from sklearn.neighbors import NearestNeighbors
# import gc
# import psutil
# from typing import Dict, List, Set, Tuple, Generator
# log_dir = "../logs"
# os.makedirs(log_dir, exist_ok=True)
# # Set up logging
# logging.basicConfig(
#     level=logging.INFO,
#     format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
#     handlers=[
#         logging.FileHandler(os.path.join(log_dir, "psm_2025_0227.log")),
#         logging.StreamHandler(),
#     ],
# )

# output_dir = "../result/did_result_20250227/"
# os.makedirs(output_dir, exist_ok=True)

# # Read the data
# repo_info = pd.read_csv('../data/sample_projects_total.csv')
# attritions = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')
# productivity = pd.read_csv('../data/2025_0227_productivity.csv')
# productivity["datetime"] = pd.to_datetime(productivity["datetime"])
# global_min_time = productivity["datetime"].min()
# productivity["standardized_time_weeks"] = (
#   (productivity["datetime"] - global_min_time).dt.days // 7
# ).astype(int)
# p_test_attrition = pd.read_csv(output_dir + "p_vary_time_attrition_20250227.csv")
# p_test = pd.read_csv('../result/p_test.csv')
# productivity_week = productivity.copy()
# # Sort and filter the productivity data
# productivity_week = productivity_week.sort_values(by=['repo_name', 'datetime'], ascending=False)
# productivity_week = productivity_week.drop_duplicates(subset=['repo_name', 'standardized_time_weeks'])
# productivity_week = productivity_week.drop(columns=['datetime'])

# def memory_monitor(func):
#     """监控函数执行期间的内存使用情况的装饰器"""
#     def wrapper(*args, **kwargs):
#         process = psutil.Process()
#         memory_before = process.memory_info().rss / 1024 / 1024  # MB
#         result = func(*args, **kwargs)
#         memory_after = process.memory_info().rss / 1024 / 1024  # MB
#         logging.info(f"Memory usage: {memory_after-memory_before:.2f}MB")
#         return result
#     return wrapper

# def group_by_burst(
#     df: pd.DataFrame,
#     burst_column: str = "burst"
# ) -> Generator[Tuple[str, pd.DataFrame], None, None]:
#     """按burst分组返回数据的生成器"""
#     for burst, group in df.groupby(burst_column):
#         yield burst, group

# def get_control_candidates(
#     control_data: pd.DataFrame,
#     treatment_time: float,
#     time_window: float = 4.0
# ) -> pd.DataFrame:
#     """获取时间窗口内的控制组候选"""
#     mask = (
#         (control_data["standardized_time_weeks"] >= treatment_time - time_window) &
#         (control_data["standardized_time_weeks"] <= treatment_time + time_window)
#     )
#     return control_data[mask]

# def batch_process(
#     array: np.ndarray,
#     batch_size: int = 1000
# ) -> Generator[np.ndarray, None, None]:
#     """批量处理数组的生成器"""
#     for i in range(0, len(array), batch_size):
#         yield array[i:i + batch_size]

# @memory_monitor
# def compile_control_group_psm_knn(
#     treatment_repos_with_left_date: pd.DataFrame,
#     candidate_repos: Set[str],
#     productivity_metric_data: pd.DataFrame,
#     n_neighbors: int,
#     timewindow_weeks: int,
#     feature_columns: List[str],
#     extra_candidates: int = 10,
#     batch_size: int = 1000
# ) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
#     """
#     优化后的PSM-KNN匹配实现
#     - 按burst分组处理数据
#     - 使用生成器减少内存占用
#     - 批量处理大型矩阵计算
#     - 及时清理不需要的数据
#     """
#     logging.info("Starting optimized PSM with NearestNeighbors...")

#     # 预处理控制组数据
#     available_controls = set(candidate_repos)
#     control_data = productivity_metric_data[
#         productivity_metric_data["repo_name"].isin(available_controls)
#     ].copy()
    
#     # 预计算控制组离职时间
#     control_treatment_weeks = (
#         productivity_metric_data[
#             (productivity_metric_data["someone_left"] == 1) &
#             (productivity_metric_data["repo_name"].isin(available_controls))
#         ]
#         .groupby("repo_name")["standardized_time_weeks"]
#         .apply(sorted)
#         .to_dict()
#     )

#     matched_pairs = {}
    
#     # 按burst分组处理
#     for burst, treatment_group in group_by_burst(treatment_repos_with_left_date):
#         logging.info(f"Processing burst: {burst}")
        
#         # 获取该burst的treatment数据
#         treatment_mask = (
#             productivity_metric_data["repo_name"].isin(set(treatment_group["repo_name"])) &
#             (productivity_metric_data["someone_left"] == 1) &
#             (productivity_metric_data["burst"] == burst)
#         )
#         treatment_features_df = productivity_metric_data[treatment_mask].copy()
        
#         if treatment_features_df.empty:
#             continue

#         # 处理每个treatment样本
#         for _, t_row in treatment_features_df.iterrows():
#             t_time = t_row["standardized_time_weeks"]
#             t_repo = t_row["repo_name"]
            
#             # 获取时间窗口内的控制组候选
#             control_candidates = get_control_candidates(control_data, t_time)
#             if len(control_candidates) < n_neighbors:
#                 logging.warning(f"Insufficient candidates for {t_repo} at time {t_time}")
#                 continue

#             # 构建特征矩阵
#             X_control = control_candidates[feature_columns].values
#             X_treatment = t_row[feature_columns].values.reshape(1, -1)

#             # 使用批量处理进行KNN匹配
#             nn_model = NearestNeighbors(
#                 n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
#                 algorithm='auto'
#             )
#             nn_model.fit(X_control)
            
#             matched_controls = []
#             used_repos = set()

#             # 分批处理近邻搜索
#             for batch in batch_process(X_treatment, batch_size):
#                 if len(matched_controls) >= n_neighbors:
#                     break
                    
#                 distances, indices = nn_model.kneighbors(
#                     batch,
#                     n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
#                     return_distance=True
#                 )

#                 # 处理匹配结果
#                 for idx in indices[0]:
#                     if len(matched_controls) >= n_neighbors:
#                         break

#                     control_row = control_candidates.iloc[idx]
#                     control_repo = control_row["repo_name"]
#                     control_time = control_row["standardized_time_weeks"]

#                     # 跳过已使用的仓库
#                     if control_repo in used_repos:
#                         continue

#                     # 验证时间窗口约束
#                     treatment_weeks = control_treatment_weeks.get(control_repo, [])
#                     if treatment_weeks:
#                         window_start = control_time - timewindow_weeks
#                         window_end = control_time + timewindow_weeks
#                         import bisect
#                         left = bisect.bisect_left(treatment_weeks, window_start)
#                         right = bisect.bisect_right(treatment_weeks, window_end)
#                         if left < right:
#                             continue

#                     matched_controls.append({
#                         "repo_name": control_repo,
#                         "matched_time": control_time,
#                         "features": control_row[feature_columns].values
#                     })
#                     used_repos.add(control_repo)

#             # 保存匹配结果
#             if matched_controls:
#                 matched_pairs[f"{burst}_{t_repo}"] = {
#                     "burst": burst,
#                     "repo_name": t_repo,
#                     "treatment_time": t_time,
#                     "controls": matched_controls,
#                     "treatment_features": t_row[feature_columns].values,
#                     "control_features": np.array([c["features"] for c in matched_controls])
#                 }
#                 logging.info(f"Matched burst:{burst}-repo:{t_repo} with {len(matched_controls)} controls")
#             else:
#                 logging.warning(f"No valid controls for {t_repo}")

#         # 清理当前burst的临时数据
#         gc.collect()

#     logging.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")
#     return matched_pairs, treatment_features_df, control_data

# p_test = p_test.fillna(0)
# p_test_attrition = p_test_attrition.fillna(0)

# # 转换类型
# if 'burst' in p_test_attrition.columns:
#     p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)

# # 调用 PSM 函数进行匹配
# matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
#     p_test_attrition,
#     p_test['repo_name'].tolist(),
#     p_test,
#     n_neighbors=5,
#     timewindow_weeks=12,
#     feature_columns=['feature_sigmod_add']
# )


# compiled_data = []
# window_size = 12
# cohort_id = 0
# for treatment_repo, matched_data in matched_pairs.items():
#     burst_id = matched_data["burst"]
#     repo_name = matched_data["repo_name"]
#     treatment_time = matched_data["treatment_time"]
#     control_groups = matched_data["controls"]
#     # load treatment and control data
#     treatment_productivity = p_test[p_test['repo_name'] == repo_name].copy()
#     control_productivity = p_test[p_test['repo_name'].isin([c['repo_name'] for c in control_groups])].copy()
    
#     # only keep the rows within the time window for both treatment and control groups
#     treatment_productivity = treatment_productivity[
#         (treatment_productivity['standardized_time_weeks'] >= treatment_time - window_size) &
#         (treatment_productivity['standardized_time_weeks'] <= treatment_time + window_size)
#     ]
#     # add ‘relativized_time’, ‘is_treated’, and ‘post_treatment’ columns
#     treatment_productivity['relativized_time'] = treatment_productivity['standardized_time_weeks'] - treatment_time
#     treatment_productivity['is_treated'] = 1
#     treatment_productivity['post_treatment'] = treatment_productivity['relativized_time'] > 0
    
#     # add ‘cohort_id’ to all dataframes
#     treatment_productivity['cohort_id'] = cohort_id
    
#     # add data to compiled_data within the time window
#     compiled_data.append(treatment_productivity)
#     control_dfs = []
#     for c in control_groups:
#         control_productivity = control_productivity[
#             (control_productivity['repo_name'] == c['repo_name']) &
#             (control_productivity['standardized_time_weeks'] >= c['matched_time'] - window_size) &
#             (control_productivity['standardized_time_weeks'] <= c['matched_time'] + window_size)
#         ]
#         control_productivity['relativized_time'] = control_productivity['standardized_time_weeks'] - c['matched_time']
#         control_productivity['is_treated'] = 0
#         control_productivity['post_treatment'] = control_productivity['relativized_time'] > 0
#         control_dfs.append(control_productivity)
#     # add 'cohort_id' to all dataframes
#     for df in control_dfs:
#         df['cohort_id'] = cohort_id
#     # add control data to compiled_data
#     compiled_data.extend(control_dfs)  
#     cohort_id += 1      
# compiled_data = pd.concat(compiled_data)

# compiled_data_test = compiled_data.copy()
# compiled_data_test['is_post_treatment'] = compiled_data_test['post_treatment'].astype(int)
# compiled_data_test['is_treated'] = compiled_data_test['is_treated'].astype(int)
# compiled_data_test['is_treated_post_treatment'] = compiled_data_test['is_treated'] * compiled_data_test['post_treatment']

# compiled_data_test = compiled_data_test.merge(
#     productivity_week[['repo_name', 'standardized_time_weeks', 'project_commits', 'project_contributors', 'project_age']],
#     on=['repo_name', 'standardized_time_weeks']
# )

# compiled_data_test.to_csv(output_dir + "compiled_data_test.csv", index=False)