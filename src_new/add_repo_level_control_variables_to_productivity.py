import pandas as pd
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from multiprocessing import cpu_count
import logging

from pymongo import MongoClient

def get_processed_commit_file_repo_name(repo_name):
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
        return None
    return repo_commit

# Set up logging configuration
logging.basicConfig(
    level=logging.INFO,  # Change to DEBUG for more detailed logs
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Log to console
        logging.FileHandler('../logs/process_commits.log', mode='w')  # Log to file
    ]
)

# Pre-load commit data in parallel for all projects (only do it once)
commit_cache = {}

# Reading necessary CSV files
logging.info('Loading data...')
repo_info = pd.read_csv('../data/sample_projects_total.csv')
# attrition = pd.read_csv('../data/attritions_20250227.csv')
productivity = pd.read_csv('../result/productivity_metrics_20250312.csv')
bot_list = pd.read_csv('../data/bot_developer_list_original.csv')
logging.info('Data loaded successfully.')

def load_commits(project):
    try:
        # Read the CSV and cache it
        # commits = pd.read_csv(f'../data/processed_commits/{project.replace("/", "_")}_processed_commits.csv')
        commits = get_processed_commit_file_repo_name(project)
        logging.info(f'Loaded commits for {project}.')
        # exclude merge commits which len of parent_shas is 2
        # commits = commits[commits['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)
        # if commits['author_login'] is empty, fill it with 'author_name'
        # commits['author_login'] = commits['author_login'].fillna(commits['author_name'])
        return commits
    except Exception as e:
        # logging.error(f"Error loading commits for {project}: {e}")
        return None

def get_commits_for_all_projects(repo_names, batch_size=100):
    """Load commits in batches to avoid memory overload."""
    total_repos = len(repo_names)
    logging.info(f'Starting commit loading in batches (batch size: {batch_size})...')

    for i in range(0, total_repos, batch_size):
        batch_repos = repo_names[i:i + batch_size]
        logging.info(f'Loading batch {i // batch_size + 1} of {len(batch_repos)} repositories...')

        with ThreadPoolExecutor(max_workers=cpu_count()) as executor:
            futures = {repo: executor.submit(load_commits, repo) for repo in batch_repos}
            for future in as_completed(futures.values()):
                result = future.result()
                if result is not None:
                    repo = list(futures.keys())[list(futures.values()).index(future)]
                    commit_cache[repo] = result

        logging.info(f'Batch {i // batch_size + 1} loaded successfully.')
    logging.info('All commits loaded.')

# Load commits for all repositories in parallel, in smaller batches
# get_commits_for_all_projects(repo_info['repo_name'], batch_size=100)  # You can adjust the batch size

import pandas as pd
import logging

# Assuming commit_cache, productivity, repo_info, etc. are defined elsewhere

def process_repo(repo):
    logging.info(f'Starting processing for {repo}...')
    
    # Check if the repo exists in the commit cache
    # if repo not in commit_cache:
    #     logging.warning(f"Commits for {repo} not found in cache.")
    #     return None

    commits = load_commits(repo)
    if commits is None:
        logging.warning(f"Commits for {repo} not found.")
        return None
    productivity_repo = productivity[productivity['repo_name'] == repo].copy()
    info_repo = repo_info[repo_info['name'] == repo]

    # Check if there's any productivity data for the repo
    if productivity_repo.empty:
        logging.warning(f"{repo} has no productivity data.")
        return None

    # Ensure the datetime columns are in the correct format
    productivity_repo['datetime'] = pd.to_datetime(productivity_repo['datetime']).dt.tz_localize('UTC')
    commits['date'] = pd.to_datetime(commits['date']).dt.floor('D')

    # Ensure sha and author_login columns have the correct data type
    commits['sha'] = commits['sha'].astype(str)
    commits['author_login'] = commits['author_login'].astype(str)

    # Get the start and end dates of the project
    start_date = productivity_repo['datetime'].min()
    end_date = productivity_repo['datetime'].max()

    # Generate a full date range from the start to the end date
    date_range = pd.DataFrame(
        pd.date_range(start=start_date, end=end_date, freq='D', tz='UTC'),
        columns=['datetime']
    )

    # Calculate daily commit counts and the set of contributors
    commits_daily = (
        commits.groupby('date')
        .agg(
            project_commits=('sha', 'count'),
            project_contributors=('author_login', lambda x: set(x))
        )
        .sort_index()
        .reset_index()
    )

    # Calculate the cumulative number of contributors
    cumulative_contributors = set()
    contributors_list = []

    for contributors in commits_daily['project_contributors']:
        cumulative_contributors.update(contributors)
        contributors_list.append(len(cumulative_contributors))

    commits_daily['project_contributors'] = contributors_list

    # Make commits cumulative by applying cumsum (cumulative sum)
    commits_daily['project_commits'] = commits_daily['project_commits'].cumsum()

    # Merge the full date range with the commit data
    commits_cum_full = pd.merge(
        date_range, commits_daily, left_on='datetime', right_on='date', how='left'
    ).drop(columns=['date'])

    # Fill missing values (NaNs) using forward fill and ensure cumulative data is correct
    commits_cum_full['project_commits'] = (
        commits_cum_full['project_commits'].fillna(method='ffill').fillna(0).astype(int)
    )
    commits_cum_full['project_contributors'] = (
        commits_cum_full['project_contributors'].fillna(method='ffill').fillna(0).astype(int)
    )

    # Calculate the project age in days
    commits_cum_full['project_age'] = (commits_cum_full['datetime'] - start_date).dt.days

    # Add the main language of the repo
    commits_cum_full['mainLanguage'] = info_repo['mainLanguage'].values[0]

    # Merge the productivity data with the commit data
    productivity_repo = pd.merge(productivity_repo, commits_cum_full, on='datetime', how='right')

    # Standardize the datetime format
    productivity_repo['datetime'] = productivity_repo['datetime'].dt.strftime('%Y-%m-%d')

    # Fill missing PR throughput values
    productivity_repo['pr_throughput'] = productivity_repo['pr_throughput'].fillna(0)
    productivity_repo['repo_name'] = repo

    logging.info(f'Finished processing for {repo}.')
    return productivity_repo


# Parallel execution for processing repositories
logging.info('Starting parallel processing of repositories...')
results = []
client = MongoClient('mongodb://localhost:27017/')  # 替换为你的 MongoDB 实例的 URI
db = client['disengagement']  # 选择 disengagement 数据库
cache_collection = db["progress_cache"]
finished_projects = cache_collection.find({
        "commits_finished": 1,
        "pr_finished": 1,
        "pr_review_finished": 1
    }, {"repo_name": 1})
repos = [project["repo_name"] for project in finished_projects]
with ThreadPoolExecutor(max_workers=80) as executor:
    futures = [executor.submit(process_repo, repo) for repo in repos]
    for future in as_completed(futures):
        res = future.result()
        if res is not None:
            results.append(res)

# Concatenate results to form the final dataframe
logging.info('Concatenating results...')
new_productivity = pd.concat(results, ignore_index=True)

# Save the final dataframe to a CSV file
logging.info('Saving results to CSV...')
new_productivity.to_csv('../data/2025_0312_productivity.csv', index=False)
logging.info('Process completed successfully.')
