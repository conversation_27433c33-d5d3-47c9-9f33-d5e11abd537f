{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "# Read data"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.388360                     1.492888 \n", "         log_project_commits     log_project_contributors \n", "                    1.979949                     2.733770 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.441130                     1.361873 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "1502861.1 1502975.6 -751420.5 1502841.1    696674 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-6.6394 -0.5758 -0.0983  0.6242  7.3283 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " time_cohort_effect (Intercept) 0.03784  0.1945  \n", " repo_cohort_effect (Intercept) 0.62448  0.7902  \n", " Residual                       0.42725  0.6536  \n", "Number of obs: 696684, groups:  \n", "time_cohort_effect, 27914; repo_cohort_effect, 27914\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   1.498e+00  7.864e-03  3.089e+04  190.46  < 2e-16\n", "is_post_treatment            -6.265e-02  3.309e-03  2.394e+04  -18.93  < 2e-16\n", "is_treated                   -4.906e-01  1.172e-02  2.898e+04  -41.87  < 2e-16\n", "log_project_commits           3.801e-01  6.326e-03  3.495e+04   60.08  < 2e-16\n", "log_project_contributors      3.611e-01  7.494e-03  3.662e+04   48.18  < 2e-16\n", "log_project_age              -2.645e-01  4.915e-03  5.086e+04  -53.81  < 2e-16\n", "is_post_treatment:is_treated -2.115e-02  3.167e-03  6.708e+05   -6.68  2.4e-11\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.266                                                 \n", "is_treated  -0.758  0.146                                          \n", "lg_prjct_cm -0.081 -0.008  0.109                                   \n", "lg_prjct_cn  0.340 -0.035 -0.447 -0.589                            \n", "log_prjct_g  0.069 -0.180 -0.058 -0.119      -0.277                \n", "is_pst_tr:_  0.134 -0.501 -0.174  0.022       0.012       0.097    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.2134337</td><td>0.6915645</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.2134337 & 0.6915645\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.2134337 | 0.6915645 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.2134337 0.6915645"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compiled_data_test <- read.csv(\"../result/did_result_20250408/did_data_group_5.csv\")\n", "# cohort_id_project_contributors_lt_1000\n", "# 加载必要的包（新增dplyr用于数据处理）\n", "library(dplyr)\n", "\n", "# 数据预处理部分新增标准化步骤\n", "compiled_data_test <- compiled_data_test %>%\n", "  # 对连续型解释变量进行中心化标准化\n", "  mutate(\n", "    log_tenure_c = scale(log_tenure),\n", "    log_commit_percent_c = scale(log_commit_percent),\n", "    log_commits_c = scale(log_commits),\n", "    # 保持项目层面变量不做标准化（视情况而定）\n", "    log_project_commits = scale(log_project_commits),\n", "    log_project_contributors = scale(log_project_contributors),\n", "    log_project_age = scale(log_project_age),\n", "    log_project_commits_before_treatment = scale(log_project_commits_before_treatment),\n", "    log_project_contributors_before_treatment = scale(log_project_contributors_before_treatment),\n", "    log_project_age_before_treatment = scale(log_project_age_before_treatment),\n", "  )\n", "  # 移除含有缺失值的观测（确保数据清洁）\n", "  # 不需要去除空值，因此不做任何处理\n", "# 优化控制参数设置\n", "ctrl <- lmerControl(\n", "  optimizer = \"nloptwrap\",\n", "  optCtrl = list(\n", "    maxeval = 1e5,    # 增大最大迭代次数\n", "    xtol_abs = 1e-8,  # 降低参数收敛阈值\n", "    ftol_abs = 1e-8   # 降低目标函数收敛阈值\n", "  ),\n", "  calc.derivs = FALSE # 关闭导数计算加速\n", ")\n", "\n", "# # exclude empty growth phase or ''\n", "# compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '',]\n", "# # exclude project with growth phase not in ['accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'    ]\n", "# compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'),]\n", "# table(compiled_data_test$growth_phase)\n", "# Model 1: Fixed Effects Only\n", "model_fixed_effects_only <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$log_time_to_merge) & compiled_data_test$log_time_to_merge != '',]\n", "compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$log_pull_request_success_rate) & compiled_data_test$log_pull_request_success_rate != '',]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.631090                     1.128411 \n", "         log_project_commits     log_project_contributors \n", "                    1.883902                     2.168654 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.512877                     1.715346 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "1117285.2 1117390.8 -558632.6 1117265.2    285293 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-3.8754 -0.6166  0.0024  0.6060  4.7582 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 1.05011  1.0247  \n", " time_cohort_effect (Intercept) 0.08332  0.2886  \n", " Residual                       2.51305  1.5853  \n", "Number of obs: 285303, groups:  \n", "repo_cohort_effect, 27293; time_cohort_effect, 27048\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   3.195e+00  1.138e-02  3.081e+04 280.686  < 2e-16\n", "is_post_treatment            -5.475e-02  9.575e-03  1.910e+04  -5.717 1.10e-08\n", "is_treated                    1.862e-01  1.559e-02  2.708e+04  11.944  < 2e-16\n", "log_project_commits           2.358e-02  1.005e-02  2.485e+04   2.346 0.018980\n", "log_project_contributors      4.413e-01  1.057e-02  2.415e+04  41.756  < 2e-16\n", "log_project_age               2.859e-02  8.612e-03  2.937e+04   3.320 0.000903\n", "is_post_treatment:is_treated -5.221e-02  1.249e-02  2.694e+05  -4.180 2.92e-05\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment            ***\n", "is_treated                   ***\n", "log_project_commits          *  \n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.385                                                 \n", "is_treated  -0.684  0.226                                          \n", "lg_prjct_cm -0.078 -0.006  0.039                                   \n", "lg_prjct_cn  0.004 -0.019 -0.043 -0.567                            \n", "log_prjct_g  0.099 -0.084 -0.018 -0.134      -0.379                \n", "is_pst_tr:_  0.230 -0.609 -0.332 -0.011       0.008       0.002    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.06682927</td><td>0.3568844</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.06682927 & 0.3568844\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.06682927 | 0.3568844 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.06682927 0.3568844"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 1: Fixed Effects Only\n", "model_fixed_effects_only <- lmer(\n", "  log_time_to_merge ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           is_post_treatment                   is_treated \n", "                    1.664017                     1.172894 \n", "         log_project_commits     log_project_contributors \n", "                    1.883857                     2.166659 \n", "             log_project_age is_post_treatment:is_treated \n", "                    1.512716                     1.792851 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: log_pull_request_success_rate ~ is_post_treatment + is_treated +  \n", "    is_treated:is_post_treatment + log_project_commits + log_project_contributors +  \n", "    log_project_age + (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "Control: ctrl\n", "\n", "      AIC       BIC    logLik  deviance  df.resid \n", "-351564.7 -351459.1  175792.4 -351584.7    285293 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-5.3202 -0.1928  0.2515  0.4900  3.0865 \n", "\n", "Random effects:\n", " Groups             Name        Variance  Std.Dev.\n", " repo_cohort_effect (Intercept) 0.0042179 0.06495 \n", " time_cohort_effect (Intercept) 0.0004059 0.02015 \n", " Residual                       0.0150057 0.12250 \n", "Number of obs: 285303, groups:  \n", "repo_cohort_effect, 27293; time_cohort_effect, 27048\n", "\n", "Fixed effects:\n", "                               Estimate Std. Error         df t value Pr(>|t|)\n", "(Intercept)                   6.172e-01  7.774e-04  3.176e+04 793.898  < 2e-16\n", "is_post_treatment            -4.141e-04  7.232e-04  2.052e+04  -0.573    0.567\n", "is_treated                    1.063e-03  1.061e-03  2.785e+04   1.002    0.316\n", "log_project_commits           6.053e-03  6.746e-04  2.358e+04   8.973  < 2e-16\n", "log_project_contributors     -2.308e-02  7.073e-04  2.241e+04 -32.637  < 2e-16\n", "log_project_age               3.778e-03  5.837e-04  2.712e+04   6.471 9.88e-11\n", "is_post_treatment:is_treated -3.781e-03  9.601e-04  2.722e+05  -3.938 8.21e-05\n", "                                \n", "(Intercept)                  ***\n", "is_post_treatment               \n", "is_treated                      \n", "log_project_commits          ***\n", "log_project_contributors     ***\n", "log_project_age              ***\n", "is_post_treatment:is_treated ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g\n", "is_pst_trtm -0.427                                                 \n", "is_treated  -0.682  0.260                                          \n", "lg_prjct_cm -0.084 -0.005  0.035                                   \n", "lg_prjct_cn -0.004 -0.016 -0.038 -0.566                            \n", "log_prjct_g  0.106 -0.075 -0.017 -0.137      -0.379                \n", "is_pst_tr:_  0.263 -0.622 -0.380 -0.012       0.007       0.002    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.01863977</td><td>0.2498032</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.01863977 & 0.2498032\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.01863977 | 0.2498032 |\n", "\n"], "text/plain": ["     R2m        R2c      \n", "[1,] 0.01863977 0.2498032"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 1: Fixed Effects Only\n", "model_fixed_effects_only <- lmer(\n", "  log_pull_request_success_rate ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "    log_project_commits + log_project_contributors + log_project_age + \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test,\n", "  control = ctrl\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 2}