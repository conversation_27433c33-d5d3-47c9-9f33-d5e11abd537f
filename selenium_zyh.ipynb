{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 环境配置"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: roman in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (5.0)\n", "Requirement already satisfied: selenium in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (4.31.0)\n", "Requirement already satisfied: urllib3<3,>=1.26 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from urllib3[socks]<3,>=1.26->selenium) (2.3.0)\n", "Requirement already satisfied: trio~=0.17 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium) (0.29.0)\n", "Requirement already satisfied: trio-websocket~=0.9 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium) (0.12.2)\n", "Requirement already satisfied: certifi>=2021.10.8 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium) (2025.1.31)\n", "Requirement already satisfied: typing_extensions~=4.9 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium) (4.12.2)\n", "Requirement already satisfied: websocket-client~=1.8 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from selenium) (1.8.0)\n", "Requirement already satisfied: attrs>=23.2.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium) (25.3.0)\n", "Requirement already satisfied: sortedcontainers in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium) (2.4.0)\n", "Requirement already satisfied: idna in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium) (3.10)\n", "Requirement already satisfied: outcome in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium) (1.3.0.post0)\n", "Requirement already satisfied: sniffio>=1.3.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio~=0.17->selenium) (1.3.1)\n", "Requirement already satisfied: wsproto>=0.14 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from trio-websocket~=0.9->selenium) (1.2.0)\n", "Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from urllib3[socks]<3,>=1.26->selenium) (1.7.1)\n", "Requirement already satisfied: h11<1,>=0.9.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium) (0.14.0)\n", "Collecting pandas\n", "  Downloading pandas-2.2.3-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (89 kB)\n", "Requirement already satisfied: numpy>=1.26.0 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from pandas) (2.2.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from pandas) (2.9.0.post0)\n", "Collecting pytz>=2020.1 (from pandas)\n", "  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas)\n", "  Downloading tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Downloading pandas-2.2.3-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (12.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m12.7/12.7 MB\u001b[0m \u001b[31m1.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading pytz-2025.2-py2.py3-none-any.whl (509 kB)\n", "Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)\n", "Installing collected packages: pytz, tzdata, pandas\n", "Successfully installed pandas-2.2.3 pytz-2025.2 tzdata-2025.2\n"]}], "source": ["!pip install roman\n", "!pip install selenium\n", "!pip install pandas"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 使用selenium获取文件对应的URL\n", "from selenium import webdriver\n", "from selenium.webdriver.support.ui import WebDriverWait, Select\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "import time\n", "\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.common.exceptions import UnexpectedAlertPresentException, NoAlertPresentException\n", "\n", "import pandas as pd\n", "import roman"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting webdriver-manager\n", "  Downloading webdriver_manager-4.0.2-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: requests in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from webdriver-manager) (2.32.3)\n", "Collecting python-dotenv (from webdriver-manager)\n", "  Downloading python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: packaging in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from webdriver-manager) (24.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from requests->webdriver-manager) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from requests->webdriver-manager) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from requests->webdriver-manager) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/.conda/envs/r_env/lib/python3.13/site-packages (from requests->webdriver-manager) (2025.1.31)\n", "Downloading webdriver_manager-4.0.2-py2.py3-none-any.whl (27 kB)\n", "Downloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)\n", "Installing collected packages: python-dotenv, webdriver-manager\n", "Successfully installed python-dotenv-1.1.0 webdriver-manager-4.0.2\n"]}], "source": ["!pip install webdriver-manager\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "service = Service(ChromeDriverManager().install())"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"ename": "WebDriverException", "evalue": "Message: unknown error: Chrome failed to start: exited abnormally.\n  (unknown error: DevToolsActivePort file doesn't exist)\n  (The process started from chrome location /usr/bin/google-chrome is no longer running, so ChromeDriver is assuming that Chrome has crashed.)\nStacktrace:\n#0 0x618d60819403 <unknown>\n#1 0x618d6061f778 <unknown>\n#2 0x618d60643fa9 <unknown>\n#3 0x618d6063f12b <unknown>\n#4 0x618d6067a83a <unknown>\n#5 0x618d606748f3 <unknown>\n#6 0x618d6064a0d8 <unknown>\n#7 0x618d6064b205 <unknown>\n#8 0x618d60860e3d <unknown>\n#9 0x618d60863db6 <unknown>\n#10 0x618d6084a13e <unknown>\n#11 0x618d608649b5 <unknown>\n#12 0x618d6083e970 <unknown>\n#13 0x618d60881228 <unknown>\n#14 0x618d608813bf <unknown>\n#15 0x618d6089babe <unknown>\n#16 0x78175b294ac3 <unknown>\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mWebDriverException\u001b[0m                        Traceback (most recent call last)", "Cell \u001b[0;32mIn[17], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;66;03m# 创建 ChromeOptions 对象\u001b[39;00m\n\u001b[1;32m      6\u001b[0m chrome_options \u001b[38;5;241m=\u001b[39m Options()\n\u001b[0;32m----> 7\u001b[0m driver \u001b[38;5;241m=\u001b[39m \u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchrome_options\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      8\u001b[0m driver\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://www.google.com\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chrome/webdriver.py:45\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     42\u001b[0m service \u001b[38;5;241m=\u001b[39m service \u001b[38;5;28;01mif\u001b[39;00m service \u001b[38;5;28;01melse\u001b[39;00m Service()\n\u001b[1;32m     43\u001b[0m options \u001b[38;5;241m=\u001b[39m options \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;28;01melse\u001b[39;00m Options()\n\u001b[0;32m---> 45\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbrowser_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDesiredCapabilities\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCHROME\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbrowserName\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     47\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvendor_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgoog\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     49\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     50\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     51\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chromium/webdriver.py:66\u001b[0m, in \u001b[0;36mChromiumDriver.__init__\u001b[0;34m(self, browser_name, vendor_prefix, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     57\u001b[0m executor \u001b[38;5;241m=\u001b[39m ChromiumRemoteConnection(\n\u001b[1;32m     58\u001b[0m     remote_server_addr\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mservice\u001b[38;5;241m.\u001b[39mservice_url,\n\u001b[1;32m     59\u001b[0m     browser_name\u001b[38;5;241m=\u001b[39mbrowser_name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     62\u001b[0m     ignore_proxy\u001b[38;5;241m=\u001b[39moptions\u001b[38;5;241m.\u001b[39m_ignore_local_proxy,\n\u001b[1;32m     63\u001b[0m )\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 66\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexecutor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m     68\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquit()\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:250\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, command_executor, keep_alive, file_detector, options, locator_converter, web_element_cls, client_config)\u001b[0m\n\u001b[1;32m    248\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_authenticator_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    249\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstart_client()\n\u001b[0;32m--> 250\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcapabilities\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    251\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fedcm \u001b[38;5;241m=\u001b[39m FedCM(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m    253\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_websocket_connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:342\u001b[0m, in \u001b[0;36mWebDriver.start_session\u001b[0;34m(self, capabilities)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Creates a new session with the desired capabilities.\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \n\u001b[1;32m    335\u001b[0m \u001b[38;5;124;03mParameters:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    338\u001b[0m \u001b[38;5;124;03m    - A capabilities dict to start the session with.\u001b[39;00m\n\u001b[1;32m    339\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    341\u001b[0m caps \u001b[38;5;241m=\u001b[39m _create_caps(capabilities)\n\u001b[0;32m--> 342\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mNEW_SESSION\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaps\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession_id \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    344\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcaps \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcapabilities\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:429\u001b[0m, in \u001b[0;36mWebDriver.execute\u001b[0;34m(self, driver_command, params)\u001b[0m\n\u001b[1;32m    427\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_executor\u001b[38;5;241m.\u001b[39mexecute(driver_command, params)\n\u001b[1;32m    428\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[0;32m--> 429\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43merror_handler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    430\u001b[0m     response[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_unwrap_value(response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    431\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/errorhandler.py:232\u001b[0m, in \u001b[0;36mErrorHandler.check_response\u001b[0;34m(self, response)\u001b[0m\n\u001b[1;32m    230\u001b[0m         alert_text \u001b[38;5;241m=\u001b[39m value[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124malert\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    231\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[0;32m--> 232\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[0;31mWebDriverException\u001b[0m: Message: unknown error: Chrome failed to start: exited abnormally.\n  (unknown error: DevToolsActivePort file doesn't exist)\n  (The process started from chrome location /usr/bin/google-chrome is no longer running, so ChromeDriver is assuming that Chrome has crashed.)\nStacktrace:\n#0 0x618d60819403 <unknown>\n#1 0x618d6061f778 <unknown>\n#2 0x618d60643fa9 <unknown>\n#3 0x618d6063f12b <unknown>\n#4 0x618d6067a83a <unknown>\n#5 0x618d606748f3 <unknown>\n#6 0x618d6064a0d8 <unknown>\n#7 0x618d6064b205 <unknown>\n#8 0x618d60860e3d <unknown>\n#9 0x618d60863db6 <unknown>\n#10 0x618d6084a13e <unknown>\n#11 0x618d608649b5 <unknown>\n#12 0x618d6083e970 <unknown>\n#13 0x618d60881228 <unknown>\n#14 0x618d608813bf <unknown>\n#15 0x618d6089babe <unknown>\n#16 0x78175b294ac3 <unknown>\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.chrome.service import Service\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "# 创建 ChromeOptions 对象\n", "chrome_options = Options()\n", "driver = webdriver.Chrome(service=service, options=chrome_options)\n", "driver.get(\"https://www.google.com\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发生错误: Message: unknown error: DevToolsActivePort file doesn't exist\n", "Stacktrace:\n", "#0 0x5af89ba40403 <unknown>\n", "#1 0x5af89b846778 <unknown>\n", "#2 0x5af89b86dcc2 <unknown>\n", "#3 0x5af89b86a08b <unknown>\n", "#4 0x5af89b86612b <unknown>\n", "#5 0x5af89b8a183a <unknown>\n", "#6 0x5af89b89b8f3 <unknown>\n", "#7 0x5af89b8710d8 <unknown>\n", "#8 0x5af89b872205 <unknown>\n", "#9 0x5af89ba87e3d <unknown>\n", "#10 0x5af89ba8adb6 <unknown>\n", "#11 0x5af89ba7113e <unknown>\n", "#12 0x5af89ba8b9b5 <unknown>\n", "#13 0x5af89ba65970 <unknown>\n", "#14 0x5af89baa8228 <unknown>\n", "#15 0x5af89baa83bf <unknown>\n", "#16 0x5af89bac2abe <unknown>\n", "#17 0x7beb2fc94ac3 <unknown>\n", "\n"]}], "source": ["try:\n", "    # 配置Chrome选项\n", "    chrome_options = Options()\n", "    chrome_options.add_argument('--no-sandbox')\n", "    chrome_options.add_argument('--disable-dev-shm-usage')\n", "    chrome_options.add_argument('--headless')  # 无界面模式，取消注释启用\n", "\n", "    # 初始化Chrome驱动\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "    \n", "    # 设置等待时间\n", "    # wait = WebDriverWait(driver, 10)\n", "    \n", "    # 访问目标网站\n", "    driver.get('http://documents.un.org')\n", "    print(\"成功访问网站\")\n", "    \n", "except Exception as e:\n", "    print(f\"发生错误: {e}\")\n", "    \n", "finally:\n", "    if 'driver' in locals():\n", "        driver.quit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 使用selenium模拟用户访问"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def get_un_documents_zh(symbol):\n", "\n", "    # 配置Chrome选项\n", "    chrome_options = Options()\n", "    chrome_options.add_argument('--no-sandbox')\n", "    chrome_options.add_argument('--disable-dev-shm-usage')\n", "    chrome_options.add_argument('--headless')  # 无界面模式，取消注释启用。因为有了这个，所以pdf_url返回是about:blank。\n", "\n", "    # 打开联合国正式文件库的检索页面\n", "    driver = webdriver.Chrome(options=chrome_options) # 初始化Chrome驱动\n", "    # wait =  WebDriverWait(driver, 10)\n", "    driver.get('http://documents.un.org') # 访问目标网站\n", "    driver.implicitly_wait(10)\n", "\n", "    # 键入检索条件\n", "    input_box = driver.find_element(By.ID, \"symbol\")\n", "    input_box.clear()\n", "    input_box.send_keys(symbol)\n", "    # select_element = driver.find_element(By.ID, \"language\") # 通过ID定位\n", "    # select = Select(select_element)\n", "    # select.select_by_value(\"zh\")\n", "\n", "    # 点击检索按钮\n", "    search_button = driver.find_element(By.ID, \"btnSearch\")  # 定位到搜索按钮\n", "    # 使用 JavaScript 强制点击按钮\n", "    driver.execute_script(\"arguments[0].click();\", search_button) # 最开始报错应该是windowsize的问题，windowsize过小遮挡住了检索按钮\n", "    # search_button.click()\n", "\n", "    # 等待搜索结果加载完成\n", "    time.sleep(2)\n", "\n", "    # 获取所有\"Details\"按钮 -> 这一步是为了选择对应语言的决议文件\n", "    details_buttons = driver.find_elements(By.CLASS_NAME, \"btn-link-sm\")\n", "\n", "###########################################################################################################################################################################################################################\n", "    if len(details_buttons) == 0:\n", "        print('没有找到任何文件储存位置')\n", "    else:\n", "        print(f\"找到 {len(details_buttons)} 个文件储存位置\")  # 打印找到的按钮数量\n", "        # 遍历所有\"Details\"按钮并点击\n", "        for i in range(0, len(details_buttons)):\n", "            time.sleep(1)\n", "\n", "            # 使用 JavaScript 强制点击按钮\n", "            driver.execute_script(\"arguments[0].click();\", details_buttons[i])\n", "            time.sleep(2)  # 等待页面加载\n", "                \n", "            # 等待 \"Chinese\" 文本出现，使用显式等待\n", "            chinese_tag = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, \"//h4[contains(text(), 'Chinese')]\"))\n", "            )\n", "            # print(\"找到 'Chinese' 标签\")  # 打印信息确认找到了 Chinese\n", "\n", "            # 获取包含 \"Chinese PDF\" \"Chinese Doc\" 的元素\n", "            pdf_icon = chinese_tag.find_element(By.XPATH, \"./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-pdf')]\")\n", "            # doc_icon = chinese_tag.find_element(By.XPATH, \"./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-doc')]\")\n", "            \n", "            try:\n", "                print(\"点击 'Chinese PDF' 图标\")  # 打印信息确认点击了 PDF 图标\n", "                pdf_icon.click()\n", "                # print(\"点击 'Chinese Doc' 图标\")\n", "                # doc_icon.click()\n", "                \n", "                # 等待新窗口打开\n", "                WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > 1)\n", "                # 切换到新窗口\n", "                window_handles = driver.window_handles\n", "                pdf_window = window_handles[-1]  # 新打开的 PDF 窗口\n", "                driver.switch_to.window(pdf_window)\n", "                # 获取 PDF 链接\n", "                time.sleep(30) # 确保 PDF 页面加载完成\n", "\n", "                pdf_url = driver.current_url\n", "                print(f\"PDF 链接: {pdf_url}\")\n", "                \n", "                # 关闭 PDF 窗口，返回主窗口\n", "                driver.close()\n", "                driver.switch_to.window(window_handles[0])  # 切换回主窗口\n", "                print(\"已返回主窗口\")\n", "                # driver.execute_script(\"arguments[0].click();\", button)\n", "                \n", "                break  # 找到后停止遍历\n", "\n", "            except Exception as e:\n", "                pdf_url = 'None'\n", "                print(f\"无法点击 'Chinese PDF' 图标\") # ，错误: {e}\n", "        \n", "        driver.quit()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"ename": "WebDriverException", "evalue": "Message: unknown error: DevToolsActivePort file doesn't exist\nStacktrace:\n#0 0x5a3350da9403 <unknown>\n#1 0x5a3350baf778 <unknown>\n#2 0x5a3350bd6cc2 <unknown>\n#3 0x5a3350bd308b <unknown>\n#4 0x5a3350bcf12b <unknown>\n#5 0x5a3350c0a83a <unknown>\n#6 0x5a3350c048f3 <unknown>\n#7 0x5a3350bda0d8 <unknown>\n#8 0x5a3350bdb205 <unknown>\n#9 0x5a3350df0e3d <unknown>\n#10 0x5a3350df3db6 <unknown>\n#11 0x5a3350dda13e <unknown>\n#12 0x5a3350df49b5 <unknown>\n#13 0x5a3350dce970 <unknown>\n#14 0x5a3350e11228 <unknown>\n#15 0x5a3350e113bf <unknown>\n#16 0x5a3350e2babe <unknown>\n#17 0x737b96894ac3 <unknown>\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mWebDriverException\u001b[0m                        Traceback (most recent call last)", "Cell \u001b[0;32mIn[20], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mget_un_documents_zh\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mA/RES/47/52\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[19], line 10\u001b[0m, in \u001b[0;36mget_un_documents_zh\u001b[0;34m(symbol)\u001b[0m\n\u001b[1;32m      7\u001b[0m chrome_options\u001b[38;5;241m.\u001b[39madd_argument(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m--headless\u001b[39m\u001b[38;5;124m'\u001b[39m)  \u001b[38;5;66;03m# 无界面模式，取消注释启用。因为有了这个，所以pdf_url返回是about:blank。\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# 打开联合国正式文件库的检索页面\u001b[39;00m\n\u001b[0;32m---> 10\u001b[0m driver \u001b[38;5;241m=\u001b[39m \u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchrome_options\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;66;03m# 初始化Chrome驱动\u001b[39;00m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# wait =  WebDriverWait(driver, 10)\u001b[39;00m\n\u001b[1;32m     12\u001b[0m driver\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttp://documents.un.org\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;66;03m# 访问目标网站\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chrome/webdriver.py:45\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     42\u001b[0m service \u001b[38;5;241m=\u001b[39m service \u001b[38;5;28;01mif\u001b[39;00m service \u001b[38;5;28;01melse\u001b[39;00m Service()\n\u001b[1;32m     43\u001b[0m options \u001b[38;5;241m=\u001b[39m options \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;28;01melse\u001b[39;00m Options()\n\u001b[0;32m---> 45\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbrowser_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDesiredCapabilities\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCHROME\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbrowserName\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     47\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvendor_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgoog\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     49\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     50\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     51\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/chromium/webdriver.py:66\u001b[0m, in \u001b[0;36mChromiumDriver.__init__\u001b[0;34m(self, browser_name, vendor_prefix, options, service, keep_alive)\u001b[0m\n\u001b[1;32m     57\u001b[0m executor \u001b[38;5;241m=\u001b[39m ChromiumRemoteConnection(\n\u001b[1;32m     58\u001b[0m     remote_server_addr\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mservice\u001b[38;5;241m.\u001b[39mservice_url,\n\u001b[1;32m     59\u001b[0m     browser_name\u001b[38;5;241m=\u001b[39mbrowser_name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     62\u001b[0m     ignore_proxy\u001b[38;5;241m=\u001b[39moptions\u001b[38;5;241m.\u001b[39m_ignore_local_proxy,\n\u001b[1;32m     63\u001b[0m )\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 66\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcommand_executor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexecutor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m     68\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquit()\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:250\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[0;34m(self, command_executor, keep_alive, file_detector, options, locator_converter, web_element_cls, client_config)\u001b[0m\n\u001b[1;32m    248\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_authenticator_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    249\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstart_client()\n\u001b[0;32m--> 250\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcapabilities\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    251\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fedcm \u001b[38;5;241m=\u001b[39m FedCM(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m    253\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_websocket_connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:342\u001b[0m, in \u001b[0;36mWebDriver.start_session\u001b[0;34m(self, capabilities)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Creates a new session with the desired capabilities.\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \n\u001b[1;32m    335\u001b[0m \u001b[38;5;124;03mParameters:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    338\u001b[0m \u001b[38;5;124;03m    - A capabilities dict to start the session with.\u001b[39;00m\n\u001b[1;32m    339\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    341\u001b[0m caps \u001b[38;5;241m=\u001b[39m _create_caps(capabilities)\n\u001b[0;32m--> 342\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mNEW_SESSION\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcaps\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession_id \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    344\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcaps \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcapabilities\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:429\u001b[0m, in \u001b[0;36mWebDriver.execute\u001b[0;34m(self, driver_command, params)\u001b[0m\n\u001b[1;32m    427\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_executor\u001b[38;5;241m.\u001b[39mexecute(driver_command, params)\n\u001b[1;32m    428\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[0;32m--> 429\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43merror_handler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    430\u001b[0m     response[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_unwrap_value(response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    431\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/.conda/envs/r_env/lib/python3.13/site-packages/selenium/webdriver/remote/errorhandler.py:232\u001b[0m, in \u001b[0;36mErrorHandler.check_response\u001b[0;34m(self, response)\u001b[0m\n\u001b[1;32m    230\u001b[0m         alert_text \u001b[38;5;241m=\u001b[39m value[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124malert\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    231\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[0;32m--> 232\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[0;31mWebDriverException\u001b[0m: Message: unknown error: DevToolsActivePort file doesn't exist\nStacktrace:\n#0 0x5a3350da9403 <unknown>\n#1 0x5a3350baf778 <unknown>\n#2 0x5a3350bd6cc2 <unknown>\n#3 0x5a3350bd308b <unknown>\n#4 0x5a3350bcf12b <unknown>\n#5 0x5a3350c0a83a <unknown>\n#6 0x5a3350c048f3 <unknown>\n#7 0x5a3350bda0d8 <unknown>\n#8 0x5a3350bdb205 <unknown>\n#9 0x5a3350df0e3d <unknown>\n#10 0x5a3350df3db6 <unknown>\n#11 0x5a3350dda13e <unknown>\n#12 0x5a3350df49b5 <unknown>\n#13 0x5a3350dce970 <unknown>\n#14 0x5a3350e11228 <unknown>\n#15 0x5a3350e113bf <unknown>\n#16 0x5a3350e2babe <unknown>\n#17 0x737b96894ac3 <unknown>\n"]}], "source": ["get_un_documents_zh(\"A/RES/47/52\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_un_documents_ar(symbol):\n", "\n", "    # 配置Chrome选项\n", "    chrome_options = Options()\n", "    chrome_options.add_argument('--no-sandbox')\n", "    chrome_options.add_argument('--disable-dev-shm-usage')\n", "    chrome_options.add_argument('--headless')  # 无界面模式，取消注释启用\n", "\n", "    # 打开联合国正式文件库的检索页面\n", "    driver = webdriver.Chrome(options=chrome_options) # 初始化Chrome驱动\n", "    # wait =  WebDriverWait(driver, 10)\n", "    driver.get('http://documents.un.org') # 访问目标网站\n", "    driver.implicitly_wait(10)\n", "\n", "    # 键入检索条件\n", "    input_box = driver.find_element(By.ID, \"symbol\")\n", "    input_box.clear()\n", "    input_box.send_keys(symbol)\n", "    # select_element = driver.find_element(By.ID, \"language\") # 通过ID定位\n", "    # select = Select(select_element)\n", "    # select.select_by_value(\"zh\")\n", "\n", "    # 点击检索按钮\n", "    search_button = driver.find_element(By.ID, \"btnSearch\")  # 定位到搜索按钮\n", "    # 使用 JavaScript 强制点击按钮\n", "    driver.execute_script(\"arguments[0].click();\", search_button) # 最开始报错应该是windowsize的问题，windowsize过小遮挡住了检索按钮\n", "    # search_button.click()\n", "\n", "    # 等待搜索结果加载完成\n", "    time.sleep(2)\n", "\n", "    # 获取所有\"Details\"按钮 -> 这一步是为了选择对应语言的决议文件\n", "    details_buttons = driver.find_elements(By.CLASS_NAME, \"btn-link-sm\") \n", "    print(f\"找到 {len(details_buttons)} 个文件储存位置\")  # 打印找到的按钮数量\n", "\n", "    # 遍历所有\"Details\"按钮并点击\n", "    for button in details_buttons:\n", "        try:\n", "            time.sleep(1)\n", "\n", "            # 使用 JavaScript 强制点击按钮\n", "            driver.execute_script(\"arguments[0].click();\", button)\n", "            time.sleep(2)  # 等待页面加载\n", "\n", "            # 等待 \"Arabic\" 文本出现，使用显式等待\n", "            arabic_tag = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, \"//h4[contains(text(), 'Arabic')]\"))\n", "            )\n", "            print(\"找到 'Arabic' 标签\")  # 打印信息确认找到了 Arabic\n", "\n", "            # 获取包含 \"Arabic PDF\" 的元素并点击\n", "            pdf_icon = arabic_tag.find_element(By.XPATH, \"./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-pdf')]\")\n", "            print(\"点击 'Arabic PDF' 图标\")  # 打印信息确认点击了 PDF 图标\n", "            pdf_icon.click()\n", "\n", "            # 等待新窗口打开\n", "            WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > 1)\n", "\n", "            # 切换到新窗口\n", "            window_handles = driver.window_handles\n", "            pdf_window = window_handles[-1]  # 新打开的 PDF 窗口\n", "            driver.switch_to.window(pdf_window)\n", "\n", "            # 获取 PDF 链接\n", "            time.sleep(2)  # 等待页面加载\n", "            pdf_url = driver.current_url\n", "            print(f\"PDF 链接: {pdf_url}\")\n", "\n", "            return pdf_url\n", "            \n", "            # 关闭 PDF 窗口，返回主窗口\n", "            driver.close()\n", "            driver.switch_to.window(window_handles[0])  # 切换回主窗口\n", "            print(\"已返回主窗口\")\n", "            driver.execute_script(\"arguments[0].click();\", button)\n", "            \n", "            break  # 找到后停止遍历\n", "        \n", "        except Exception as e:\n", "            print(f\"在卡片中未找到 'Arabic' 或 PDF 图标，错误: {e}\")\n", "            return 'None'\n", "    \n", "    driver.quit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 - 补充安理会决议 \n", "- 缺失数据的dataframe结构为：  \n", "Year Number \n", "- 文件编号规则为：  \n", "S/RES/Number(Year)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 中文文件\n", "df_s_zh = pd.read_csv('un_sres_missing_zh.csv')\n", "# df_s_zh.head(5)\n", "\n", "for row in df_s_zh.itertuples():\n", "    ls_s_zh = []\n", "    symbol = 'S/RES/{row.Number}({row.Year})'.format(row=row)\n", "    # print(symbol)\n", "    get_un_documents_zh(symbol, ls_s_zh)\n", "\n", "    df_s_zh.loc[:, 'URL'] = ls_s_zh[0] # 代码似乎有点问题，但是结果是可以的\n", "\n", "df_s_zh.to_csv('un_sres_missing_zh.csv', index = False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 阿拉伯语文件\n", "df_s_ar = pd.read_csv('un_sres_missing_ar.csv') # 共529份缺失文件，分为6份\n", "\n", "df_s_ar_1 = df_s_ar.iloc[0:100, :].copy()\n", "df_s_ar_2 = df_s_ar.iloc[100:200, :].copy()\n", "df_s_ar_3 = df_s_ar.iloc[200:300, :].copy()\n", "df_s_ar_4 = df_s_ar.iloc[300:400, :].copy()\n", "df_s_ar_5 = df_s_ar.iloc[400:500, :].copy()\n", "df_s_ar_6 = df_s_ar.iloc[500:, :].copy()\n", "\n", "urls = []\n", "\n", "for i in range(1,7):\n", "    df_s_ar_i = df_s_ar.iloc[(i-1)*100:i*100, :].copy() # 对最后一个不适用\n", "    for row in df_s_ar_i.itertuples(): # 奇怪的是，总是循环到100+就报错，所以先以100为单位分批次进行循环！\n", "        symbol = f'S/RES/{row.Number}({row.Year})'\n", "        url = get_un_documents_ar(symbol)\n", "        urls.append(url)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 运行到最后一份缺失文件的时候报错了，直接人工查看，发现该文件的阿拉伯语版不存在\n", "# urls.append('None')\n", "# len(urls)\n", "\n", "df_s_ar['URL'] = urls\n", "df_s_ar.tail(5)\n", "\n", "df_s_ar.to_csv('un_sres_missing_ar.csv', index = False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2 - 补充大会决议 \n", "- 缺失数据的dataframe结构为：  \n", "Session Number  \n", "- 文件编号规则为：  \n", "前30届为  \n", "后30届为"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 中文文件\n", "df_a_zh = pd.read_csv('un_ares_missing_zh.csv')\n", "# df_a_zh.head(5)\n", "\n", "df_a_zh_1 = df_a_zh.iloc[0:100, :].copy()\n", "df_a_zh_2 = df_a_zh.iloc[100:200, :].copy()\n", "df_a_zh_3 = df_a_zh.iloc[200:303, :].copy()\n", "\n", "df_ls = [df_a_zh_1, df_a_zh_2, df_a_zh_3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls = []\n", "\n", "for df_a_zh_i in df_ls:\n", "    for row in df_a_zh_i.itertuples():\n", "        if row.Session <= 30:\n", "            Session = roman.toRoman(row.Session)\n", "            symbol =f'A/RES/{row.Number}({Session})'\n", "        else:\n", "            symbol = f'A/RES/{row.Session}/{row.Number}'\n", "        # print(symbol)\n", "        \n", "        url = get_un_documents_zh(symbol)\n", "        urls.append(url)"]}], "metadata": {"kernelspec": {"display_name": "r_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}