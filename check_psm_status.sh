#!/bin/bash

# PSM匹配脚本状态检查器

echo "=========================================="
echo "PSM Matching Script Status Check"
echo "Checked at: $(date)"
echo "=========================================="

# 检查Python进程是否在运行
echo "Checking for running PSM matching processes..."
ps aux | grep 20250629_PSM_matching_multi_limits.py | grep -v grep

if [ $? -eq 0 ]; then
    echo "✓ PSM matching script is running"
else
    echo "✗ PSM matching script is not running"
fi

echo ""

# 显示最新的日志文件
echo "Latest log files:"
ls -la logs/psm_matching_*.log 2>/dev/null | tail -5

echo ""

# 显示最新的日志内容（最后20行）
echo "Latest log content (last 20 lines):"
if [ -f logs/psm_matching_*.log ]; then
    tail -20 logs/psm_matching_$(ls -t logs/psm_matching_*.log | head -1 | xargs basename) 2>/dev/null
else
    echo "No log files found"
fi

echo ""

# 检查缓存文件
echo "Cache files:"
ls -la cache/psm_20250629/ 2>/dev/null | head -10

echo ""

# 检查输出文件
echo "Output files:"
ls -la result/20250629_did_result/compiled_data_test_limit*.csv 2>/dev/null | head -10

echo "==========================================" 