# 使用selenium获取文件对应的URL
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import time

from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import UnexpectedAlertPresentException, NoAlertPresentException

import pandas as pd
import roman

def get_un_documents_zh(symbol):

    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--headless')  # 无界面模式，取消注释启用。因为有了这个，所以pdf_url返回是about:blank。

    # 打开联合国正式文件库的检索页面
    driver = webdriver.Chrome(options=chrome_options) # 初始化Chrome驱动
    # wait =  WebDriverWait(driver, 10)
    driver.get('http://documents.un.org') # 访问目标网站
    driver.implicitly_wait(10)

    # 键入检索条件
    input_box = driver.find_element(By.ID, "symbol")
    input_box.clear()
    input_box.send_keys(symbol)
    # select_element = driver.find_element(By.ID, "language") # 通过ID定位
    # select = Select(select_element)
    # select.select_by_value("zh")

    # 点击检索按钮
    search_button = driver.find_element(By.ID, "btnSearch")  # 定位到搜索按钮
    # 使用 JavaScript 强制点击按钮
    driver.execute_script("arguments[0].click();", search_button) # 最开始报错应该是windowsize的问题，windowsize过小遮挡住了检索按钮
    # search_button.click()

    # 等待搜索结果加载完成
    time.sleep(2)

    # 获取所有"Details"按钮 -> 这一步是为了选择对应语言的决议文件
    details_buttons = driver.find_elements(By.CLASS_NAME, "btn-link-sm")

###########################################################################################################################################################################################################################
    if len(details_buttons) == 0:
        print('没有找到任何文件储存位置')
    else:
        print(f"找到 {len(details_buttons)} 个文件储存位置")  # 打印找到的按钮数量
        # 遍历所有"Details"按钮并点击
        for i in range(0, len(details_buttons)):
            time.sleep(1)

            # 使用 JavaScript 强制点击按钮
            driver.execute_script("arguments[0].click();", details_buttons[i])
            time.sleep(2)  # 等待页面加载
                
            # 等待 "Chinese" 文本出现，使用显式等待
            chinese_tag = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//h4[contains(text(), 'Chinese')]"))
            )
            # print("找到 'Chinese' 标签")  # 打印信息确认找到了 Chinese

            # 获取包含 "Chinese PDF" "Chinese Doc" 的元素
            pdf_icon = chinese_tag.find_element(By.XPATH, "./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-pdf')]")
            # doc_icon = chinese_tag.find_element(By.XPATH, "./following-sibling::div[@class='download-icons']//i[contains(@class, 'bxs-file-doc')]")
            
            try:
                print("点击 'Chinese PDF' 图标")  # 打印信息确认点击了 PDF 图标
                pdf_icon.click()
                # print("点击 'Chinese Doc' 图标")
                # doc_icon.click()
                
                # 等待新窗口打开
                WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > 1)
                # 切换到新窗口
                window_handles = driver.window_handles
                pdf_window = window_handles[-1]  # 新打开的 PDF 窗口
                driver.switch_to.window(pdf_window)
                
                # 获取 PDF 链接
                time.sleep(30) # 确保 PDF 页面加载完成

                pdf_url = driver.current_url
                print(f"PDF 链接: {pdf_url}")
                
                # 关闭 PDF 窗口，返回主窗口
                driver.close()
                driver.switch_to.window(window_handles[0])  # 切换回主窗口
                print("已返回主窗口")
                # driver.execute_script("arguments[0].click();", button)
                
                break  # 找到后停止遍历

            except Exception as e:
                pdf_url = 'None'
                print(f"无法点击 'Chinese PDF' 图标") # ，错误: {e}
        
        driver.quit()

get_un_documents_zh("A/RES/47/52")