#!/usr/bin/env python3
"""
测试内存优化后的PSM匹配函数
"""
import pandas as pd
import numpy as np
import logging
import time
import gc
import psutil
from datetime import datetime
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_memory_usage():
    """获取当前内存使用量(MB)"""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024

def test_memory_optimized_psm():
    """测试内存优化后的PSM算法"""
    print("="*60)
    print("TESTING MEMORY-OPTIMIZED PSM ALGORITHM")
    print("="*60)
    
    # 监控内存使用
    initial_memory = get_memory_usage()
    print(f"Initial memory usage: {initial_memory:.1f}MB")
    
    start_time = time.time()
    
    # 读取测试数据
    test_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365_test_sample.csv'
    print(f"Loading test data from: {test_file}")
    
    if not os.path.exists(test_file):
        print(f"Error: Test file not found: {test_file}")
        return
    
    p_test = pd.read_csv(test_file)
    print(f"Test data shape: {p_test.shape}")
    
    after_load_memory = get_memory_usage()
    print(f"Memory after loading data: {after_load_memory:.1f}MB (+{after_load_memory-initial_memory:.1f}MB)")
    
    # 数据预处理 - 完全按照原始逻辑
    p_test_attrition = p_test[p_test['someone_left'] == 1].copy()
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'].notnull()]
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'] != 0.5]
    
    print(f"Attrition data shape after filtering: {p_test_attrition.shape}")
    
    p_test = p_test.fillna(0)
    p_test_attrition = p_test_attrition.fillna(0)
    
    # 转换类型
    if 'burst' in p_test_attrition.columns:
        p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)
    
    after_preprocess_memory = get_memory_usage()
    print(f"Memory after preprocessing: {after_preprocess_memory:.1f}MB (+{after_preprocess_memory-after_load_memory:.1f}MB)")
    
    # 导入优化后的函数
    print("Importing optimized PSM function...")
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "psm_module", 
            "src_new_new/20250629_04_PSM_matching_multi_limits_improved.py"
        )
        psm_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(psm_module)
        
        print("Successfully imported PSM module")
        
        # 调用优化后的PSM函数进行匹配
        print("Starting PSM matching...")
        before_psm_memory = get_memory_usage()
        print(f"Memory before PSM: {before_psm_memory:.1f}MB")
        
        matched_pairs, treatment_features_df, control_features_df = psm_module.compile_control_group_psm_knn(
            p_test_attrition,
            p_test['repo_name'].tolist(),
            p_test,
            n_neighbors=5,
            timewindow_weeks=12,
            feature_columns=['feature_sigmod_add'],
            extra_candidates=10,
            batch_size=1000
        )
        
        after_psm_memory = get_memory_usage()
        print(f"Memory after PSM: {after_psm_memory:.1f}MB (+{after_psm_memory-before_psm_memory:.1f}MB)")
        
    except Exception as e:
        print(f"Error during PSM matching: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # 强制垃圾回收
    gc.collect()
    final_memory = get_memory_usage()
    
    print(f"\n=== MEMORY-OPTIMIZED PSM RESULTS ===")
    print(f"Processing time: {processing_time:.2f} seconds")
    print(f"Total matched pairs: {len(matched_pairs)}")
    print(f"Treatment features shape: {treatment_features_df.shape}")
    print(f"Control features shape: {control_features_df.shape}")
    print(f"Peak memory usage: {after_psm_memory:.1f}MB")
    print(f"Final memory usage: {final_memory:.1f}MB")
    print(f"Memory increase during PSM: {after_psm_memory-before_psm_memory:.1f}MB")
    
    # 显示一些详细信息
    if matched_pairs:
        first_key = list(matched_pairs.keys())[0]
        first_match = matched_pairs[first_key]
        print(f"\nSample match (burst {first_key}):")
        print(f"  Repo: {first_match['repo_name']}")
        print(f"  Treatment time: {first_match['treatment_time']}")
        print(f"  Number of controls: {len(first_match['controls'])}")
        print(f"  Control repos: {[c['repo_name'] for c in first_match['controls']]}")
        
        # 检查数据类型
        print(f"\nData type information:")
        print(f"  Treatment features dtype: {first_match['treatment_features'].dtype}")
        print(f"  Control features dtype: {first_match['control_features'].dtype}")
    
    return matched_pairs, processing_time, after_psm_memory

def compare_with_original():
    """与原始结果进行比较"""
    print("\n" + "="*60)
    print("COMPARING WITH ORIGINAL RESULTS")
    print("="*60)
    
    # 检查是否存在原始结果
    original_file = 'test_results_original.pkl'
    if not os.path.exists(original_file):
        print(f"Original results file not found: {original_file}")
        print("Please run the original PSM test first to generate comparison baseline")
        return
    
    # 加载原始结果
    import pickle
    with open(original_file, 'rb') as f:
        original_data = pickle.load(f)
    
    # 运行优化版本
    optimized_results = test_memory_optimized_psm()
    if optimized_results is None:
        print("Optimized version failed, cannot compare")
        return
    
    matched_pairs, processing_time, peak_memory = optimized_results
    
    # 比较结果
    original_pairs = original_data['matched_pairs']
    
    print(f"\nComparison Results:")
    print(f"Original pairs count: {len(original_pairs)}")
    print(f"Optimized pairs count: {len(matched_pairs)}")
    print(f"Original processing time: {original_data['processing_time']:.2f}s")
    print(f"Optimized processing time: {processing_time:.2f}s")
    print(f"Time difference: {processing_time - original_data['processing_time']:+.2f}s")
    
    # 检查匹配对是否相同
    if len(original_pairs) != len(matched_pairs):
        print("❌ Different number of matched pairs!")
        return
    
    # 检查键是否相同
    if set(original_pairs.keys()) != set(matched_pairs.keys()):
        print("❌ Different keys in matched pairs!")
        return
    
    print("✅ Same number of pairs and same keys")
    
    # 详细比较几个匹配对
    sample_keys = list(original_pairs.keys())[:3]  # 检查前3个
    all_match = True
    
    for key in sample_keys:
        orig = original_pairs[key]
        opt = matched_pairs[key]
        
        # 比较控制组
        orig_controls = set(c['repo_name'] for c in orig['controls'])
        opt_controls = set(c['repo_name'] for c in opt['controls'])
        
        if orig_controls != opt_controls:
            print(f"❌ Different control repos for key {key}")
            print(f"  Original: {orig_controls}")
            print(f"  Optimized: {opt_controls}")
            all_match = False
    
    if all_match:
        print("✅ Sample matches are identical!")
    else:
        print("❌ Some differences found in matches")

if __name__ == "__main__":
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Available memory: {psutil.virtual_memory().available / 1024 / 1024 / 1024:.1f}GB")
    
    try:
        # 首先测试优化版本
        result = test_memory_optimized_psm()
        if result:
            print("✅ Memory-optimized PSM test completed successfully!")
            
            # 如果有原始结果，进行比较
            compare_with_original()
        else:
            print("❌ Memory-optimized PSM test failed!")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
