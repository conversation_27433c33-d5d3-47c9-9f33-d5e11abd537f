import pandas as pd
from pymongo import MongoClient
import requests
import logging
from logging.handlers import RotatingFileHandler
from threading import Thread, Lock
import queue
import time
from collections import deque, defaultdict
import re
from config import MONGO_URI, GITHUB_TOKENS

# MongoDB Configuration
client = MongoClient(MONGO_URI)
db = client["disengagement"]
pull_requests_collection = db["pull_requests_202501"]
cache_collection = db["pr_progress_cache"]

# Logging Configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = RotatingFileHandler('fetch_commits.log', maxBytes=10*1024*1024, backupCount=5)
handler.setLevel(logging.DEBUG)
logger.addHandler(handler)

# Token Management
class TokenManager:
    def __init__(self, tokens):
        self.tokens = deque(tokens)
        self.unavailable = {}  # token -> time when it becomes available again
        self.rate_limits = {token: {'remaining': 5000, 'reset_time': time.time()} for token in tokens}

    def get_available_token(self):
        while True:
            token = self.tokens.popleft()
            self.tokens.append(token)
            if token not in self.unavailable and self.rate_limits[token]['remaining'] > 0:
                logger.debug(f"Token {token} is available.")
                return token
            logger.debug(f"No available token, waiting for tokens to reset.")
            time.sleep(1)

    def update_rate_limit(self, token, headers):
        remaining = headers.get('X-RateLimit-Remaining', self.rate_limits[token]['remaining'])
        reset = headers.get('X-RateLimit-Reset', self.rate_limits[token]['reset_time'])
        self.rate_limits[token]['remaining'] = int(remaining)
        self.rate_limits[token]['reset_time'] = int(reset)

    def mark_unavailable(self, token, retry_after):
        self.unavailable[token] = time.time() + int(retry_after)

token_manager = TokenManager(GITHUB_TOKENS)

# Cache Management
cache_lock = Lock()

def load_progress_cache(repo):
    cache = cache_collection.find_one({"repo_name": repo})
    if cache:
        fetched_pages = cache.get("fetched_pages", [])
        fetched_pr_numbers = cache.get("fetched_pr_numbers", [])
        pr_status_all = cache.get("pr_status_all", 0)
        logger.info(f"Loaded cache for repo: {repo}. Fetched pages: {len(fetched_pages)}, PRs: {len(fetched_pr_numbers)}")
        return {
            "fetched_pages": fetched_pages,
            "fetched_pr_numbers": fetched_pr_numbers,
            "pr_status_all": pr_status_all
        }
    else:
        save_progress_cache(repo, fetched_pages=[], fetched_pr_numbers=[], pr_status_all=0)
        logger.info(f"Initialized new cache for repo: {repo}.")
        return {
            "fetched_pages": [],
            "fetched_pr_numbers": [],
            "pr_status_all": 0
        }

def save_progress_cache(repo, fetched_pages=None, fetched_pr_numbers=None, pr_status_all=None):
    with cache_lock:
        update = {}
        if fetched_pages is not None:
            update["fetched_pages"] = fetched_pages
        if fetched_pr_numbers is not None:
            update["fetched_pr_numbers"] = fetched_pr_numbers
        if pr_status_all is not None:
            update["pr_status_all"] = pr_status_all
        if update:
            cache_collection.update_one(
                {"repo_name": repo},
                {"$set": update},
                upsert=True
            )
    logger.info(f"Progress cache updated for repo: {repo}.")

# Ensure indexes exist
def ensure_indexes():
    pull_requests_collection.create_index(
        [("repo_name", 1), ("number", 1)],
        unique=True,
        name="unique_repo_name_number"
    )
    logger.info("Ensured unique index on pull_requests for (repo_name, number).")

# Get total pages
def get_total_pages(repo, pr_state="all"):
    base_url = f"https://api.github.com/repos/{repo}/pulls"
    headers = {
        'Authorization': f'token {token_manager.get_available_token()}',
        'Accept': 'application/vnd.github.v3+json'
    }
    url = f"{base_url}?state={pr_state}&per_page=100"
    response = requests.get(url, headers=headers, timeout=10)
    if response.status_code == 200:
        links = response.headers.get('Link', '')
        if 'rel="last"' in links:
            last_page_url = [link for link in links.split(',') if 'rel="last"' in link][0]
            last_page_url = last_page_url.split('page=')[-1].split('>')[0].strip()
            return int(last_page_url)
        else:
            return 1
    else:
        logger.error(f"Failed to get total pages for {repo}, state={pr_state}")
        return 1

# Get existing PR numbers
def get_existing_pr_numbers(repo):
    existing_prs = pull_requests_collection.find({"repo_name": repo}, {"number": 1})
    return {pr["number"] for pr in existing_prs}

# Threaded fetch function
def threaded_fetch(repo, base_url, collection, data_type, pr_state="all"):
    task_queue = queue.Queue()
    completed_pages = set()
    threads = []
    max_threads = 4  # 调整线程数量

    cache = load_progress_cache(repo)
    fetched_pages = set(cache.get("fetched_pages", []))
    fetched_pr_numbers = set(cache.get("fetched_pr_numbers", []))

    existing_prs = get_existing_pr_numbers(repo)
    fetched_pr_numbers.update(existing_prs)

    pr_status_all = cache.get("pr_status_all", 0)

    if pr_status_all == 1:
        logger.info(f"Skipping {repo}, PRs with state={pr_state} already fetched.")
        return

    total_pages = get_total_pages(repo, pr_state)
    for page in range(1, total_pages + 1):
        if page not in fetched_pages:
            task_queue.put(page)
    logger.info(f"Starting threaded fetch for {data_type} in repo {repo}. Total pages: {total_pages}. Fetched pages: {len(fetched_pages)}")

    pr_numbers_lock = Lock()

    def worker():
        while not task_queue.empty():
            try:
                page = task_queue.get(timeout=5)
                if page in completed_pages:
                    task_queue.task_done()
                    continue
                logger.debug(f"Worker processing page {page} for {repo}.")
                token = token_manager.get_available_token()
                headers = {
                    'Authorization': f'token {token}',
                    'Accept': 'application/vnd.github.v3+json'
                }
                url = f"{base_url}?page={page}&per_page=100&state={pr_state}"
                try:
                    response = requests.get(url, headers=headers, timeout=10)
                    response.raise_for_status()
                except requests.exceptions.RequestException as e:
                    logger.error(f"Request error on page {page} for {data_type}: {e}")
                    task_queue.put(page)
                    task_queue.task_done()
                    continue

                token_manager.update_rate_limit(token, response.headers)

                items = response.json()

                if response.status_code == 403:
                    retry_after = response.headers.get('Retry-After', 60)
                    token_manager.mark_unavailable(token, retry_after)
                    logger.warning(f"Token {token} hit rate limit. Switching to next token.")
                    task_queue.put(page)
                    task_queue.task_done()
                    continue

                if not isinstance(items, list):
                    logger.error(f"Unexpected response on page {page} for {repo}, state={pr_state}: {items}")
                    completed_pages.add(page)
                    task_queue.task_done()
                    continue
              
                for item in items:
                    pr_number = item['number']
                    if pr_number not in fetched_pr_numbers:
                        item['repo_name'] = repo
                        try:
                            collection.insert_one(item)
                        except Exception as e:
                            if "E11000 duplicate key error" in str(e):
                                logger.info(f"PR {pr_number} in {repo} already exists.")
                            else:
                                logger.error(f"Error inserting PR {pr_number} in {repo}: {e}")
                        with pr_numbers_lock:
                            fetched_pr_numbers.add(pr_number)
                            save_progress_cache(repo, fetched_pr_numbers=list(fetched_pr_numbers))
                        logger.debug(f"Added PR number {pr_number} to fetched_pr_numbers.")

                logger.info(f"Fetched {len(items)} PRs for {repo}, page {page}, state={pr_state}.")
                completed_pages.add(page)
                fetched_pages.add(page)
                task_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"Error on page {page} for {data_type}: {e}")
                task_queue.task_done()

    for _ in range(min(max_threads, total_pages - len(fetched_pages))):
        t = Thread(target=worker)
        t.start()
        threads.append(t)

    task_queue.join()
    for t in threads:
        t.join()

    save_progress_cache(repo, fetched_pages=list(fetched_pages), fetched_pr_numbers=list(fetched_pr_numbers), pr_status_all=1 if len(fetched_pages) == total_pages else 0)
    if len(fetched_pages) == total_pages:
        logger.info(f"Finished fetching PRs for {repo}, state={pr_state}.")
    else:
        logger.warning(f"Not all pages were fetched for {repo}. Fetched {len(fetched_pages)} out of {total_pages} pages.")

# Main function
def main():
    ensure_indexes()
    sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
    project_names = sample_projects[['repo_name', 'commits', 'totalPullRequests']].to_dict(orient='records')

    for project in project_names:
        repo = project['repo_name']
        cache = load_progress_cache(repo)
        if cache.get("pr_status_all", 0) == 1:
            logger.info(f"Skipping {repo}, PRs with state=all already fetched.")
            continue

        base_url = f"https://api.github.com/repos/{repo}/pulls"
        threaded_fetch(repo, base_url, pull_requests_collection, "pull_request", pr_state="all")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Program stopped with error: {e}")
        raise