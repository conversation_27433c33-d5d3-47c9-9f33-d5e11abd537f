import logging
import pandas as pd
from pymongo import MongoClient
from multiprocessing import Pool

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("deduplication.log", mode="w", encoding="utf-8"),
    ],
)

WINDOWS_IP = "localhost"
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")

db = client["disengagement"]
commits_collection = db["commits"]
cache_collection = db["progress_cache"]
pull_requests_collection = db["pull_requests"]
pr_comments_collection = db["pr_comments"]

# Load project names
sample_projects = pd.read_csv("../data/sample_projects_quartiles.csv")
project_names = sample_projects["name"].tolist()
logging.info(f"Loaded {len(project_names)} project names for processing.")


# Create necessary indexes for efficient querying
def create_indexes():
    logging.info("Creating indexes on collections...")

    # Commits collection
    commits_collection.create_index([("repo_name", 1), ("sha", 1)], unique=False)
    logging.info("Created index on 'repo_name' and 'sha' for commits_collection.")

    # Cache collection
    cache_collection.create_index([("repo_name", 1)], unique=False)
    logging.info("Created index on 'repo_name' for cache_collection.")

    # PR comments collection
    pr_comments_collection.create_index([("repo_name", 1), ("id", 1)], unique=False)
    logging.info("Created index on 'repo_name' and 'id' for pr_comments_collection.")

    # Pull requests collection
    pull_requests_collection.create_index([("repo_name", 1), ("number", 1)], unique=False)
    logging.info("Created index on 'repo_name' and 'number' for pull_requests_collection.")

    logging.info("Indexes created successfully.")


# Deduplication logic for each collection
def deduplicate_commits_for_repo(repo):
    pipeline = [
        {"$match": {"repo_name": repo}},
        {
            "$group": {
                "_id": {"sha": "$sha", "repo_name": "$repo_name"},
                "ids": {"$push": "$_id"},
                "count": {"$sum": 1},
            }
        },
        {"$match": {"count": {"$gt": 1}}},
    ]
    duplicates = list(commits_collection.aggregate(pipeline))
    if not duplicates:
        return 0

    # Remove duplicates
    ids_to_delete = [dup["ids"][1:] for dup in duplicates]
    flat_ids = [item for sublist in ids_to_delete for item in sublist]  # Flatten list
    if flat_ids:
        commits_collection.delete_many({"_id": {"$in": flat_ids}})
    return len(flat_ids)


def deduplicate_cache_for_repo(repo):
    docs = list(cache_collection.find({"repo_name": repo}))
    if len(docs) <= 1:
        return 0

    # Sort by the number of attributes (retain the document with the most attributes)
    docs.sort(key=lambda x: len(x.keys()), reverse=True)
    ids_to_delete = [doc["_id"] for doc in docs[1:]]

    if ids_to_delete:
        cache_collection.delete_many({"_id": {"$in": ids_to_delete}})
    return len(ids_to_delete)


def deduplicate_pr_comments_for_repo(repo):
    pipeline = [
        {"$match": {"repo_name": repo}},
        {
            "$group": {
                "_id": {"repo_name": "$repo_name", "id": "$id"},
                "ids": {"$push": "$_id"},
                "count": {"$sum": 1},
            }
        },
        {"$match": {"count": {"$gt": 1}}},
    ]
    duplicates = list(pr_comments_collection.aggregate(pipeline))
    if not duplicates:
        return 0

    # Remove duplicates
    ids_to_delete = [dup["ids"][1:] for dup in duplicates]
    flat_ids = [item for sublist in ids_to_delete for item in sublist]  # Flatten list
    if flat_ids:
        pr_comments_collection.delete_many({"_id": {"$in": flat_ids}})
    return len(flat_ids)


def deduplicate_pull_requests_for_repo(repo):
    pipeline = [
        {"$match": {"repo_name": repo}},
        {
            "$group": {
                "_id": {"repo_name": "$repo_name", "number": "$number"},
                "ids": {"$push": "$_id"},
                "count": {"$sum": 1},
            }
        },
        {"$match": {"count": {"$gt": 1}}},
    ]
    duplicates = list(pull_requests_collection.aggregate(pipeline))
    if not duplicates:
        return 0

    # Remove duplicates
    ids_to_delete = [dup["ids"][1:] for dup in duplicates]
    flat_ids = [item for sublist in ids_to_delete for item in sublist]  # Flatten list
    if flat_ids:
        pull_requests_collection.delete_many({"_id": {"$in": flat_ids}})
    return len(flat_ids)


def deduplicate_repo(repo):
    logging.info(f"Processing repository '{repo}'")
    cache_deleted = deduplicate_cache_for_repo(repo)
    pull_requests_deleted = deduplicate_pull_requests_for_repo(repo)
    commits_deleted = deduplicate_commits_for_repo(repo)
    pr_comments_deleted = deduplicate_pr_comments_for_repo(repo)

    logging.info(
        f"Repository '{repo}': Removed {commits_deleted} from commits, "
        f"{cache_deleted} from cache, {pr_comments_deleted} from pr_comments, "
        f"{pull_requests_deleted} from pull_requests"
    )


# Process in parallel using multiprocessing
if __name__ == "__main__":
    create_indexes()  # Ensure indexes are created before querying

    with Pool(processes=8) as pool:  # Adjust number of processes based on your CPU
        pool.map(deduplicate_repo, project_names)
