import logging
import pandas as pd
from pymongo import MongoClient
import os
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("generate_csv.log", mode="w", encoding="utf-8"),
    ],
)

WINDOWS_IP = "localhost"
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")

db = client["disengagement"]
commits_collection = db["commits"]

def fetch_commits_and_generate_csv(repo_name, output_csv_path):
    """
    Fetch commits for a specific repo from MongoDB and generate a CSV file.

    Parameters:
        repo_name (str): The repository name to filter commits.
        output_csv_path (str): Path to the output CSV file.
    """
    logging.info(f"Fetching commits for repository: {repo_name}")

    # Query MongoDB for commits
    try:
        commits = list(commits_collection.find({"repo_name": repo_name}))
    except Exception as e:
        logging.error(f"Error querying commits for {repo_name}: {e}")
        return

    if not commits:
        logging.warning(f"No commits found for repository: {repo_name}")
        return

    # Convert commits to a DataFrame
    logging.info(f"Found {len(commits)} commits for repository: {repo_name}")

    # Extract needed fields including parents, author_login, and committer_login
    rows = []
    for commit in commits:
        commit_author = commit.get("author") or {}
        commit_committer = commit.get("committer") or {}
        commit_data = commit.get("commit") or {}
        commit_author_data = commit_data.get("author") or {}
        commit_committer_data = commit_data.get("committer") or {}

        rows.append({
            "sha": commit.get("sha"),
            "author_name": commit_author_data.get("name"),
            "author_email": commit_author_data.get("email"),
            "author_login": commit_author.get("login"),
            "committer_name": commit_committer_data.get("name"),
            "committer_email": commit_committer_data.get("email"),
            "committer_login": commit_committer.get("login"),
            "date": commit_author_data.get("date"),
            "message": commit_data.get("message"),
            "repo_name": commit.get("repo_name"),
            "parent_count": len(commit.get("parents", [])),
            "parent_shas": [parent.get("sha") for parent in commit.get("parents", []) if parent.get("sha")],
        })

    df = pd.DataFrame(rows)

    # Save to CSV
    try:
        df.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"CSV file generated at: {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to generate CSV for {repo_name}: {e}")

# Main function to process repositories and generate CSVs
def main():
    # Load project names
    try:
        sample_projects = pd.read_csv("../data/sample_projects_quartiles.csv")
        project_names = sample_projects["name"].tolist()
        logging.info(f"Loaded {len(project_names)} project names for processing.")
    except Exception as e:
        logging.error(f"Failed to load project names: {e}")
        return

    # Process each project and generate a CSV
    for repo_name in project_names:
        # if csv file already exists, skip
        if os.path.exists(f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"):
            logging.info(f"CSV file already exists for {repo_name}, skipping.")
            continue
        output_csv_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
        fetch_commits_and_generate_csv(repo_name, output_csv_path)

if __name__ == "__main__":
    main()
