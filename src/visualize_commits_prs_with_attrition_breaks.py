import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from pymongo import MongoClient


# Configure logging
log_file = '../logs/repo_visualization.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


def load_data(repo_name):
    """
    Load commit and PR data for a given repository.
    Assumes CSV files are stored locally.
    """
    try:
        commit_file = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
        pr_file = f"../data/pr/{repo_name.replace('/', '_')}_prs.csv"

        # Load commit data
        commits = pd.read_csv(commit_file)
        commits['date'] = pd.to_datetime(commits['date'], errors='coerce')

        # Load PR data
        prs = pd.read_csv(pr_file)
        prs['created_at'] = pd.to_datetime(prs['created_at'], errors='coerce')

        return commits, prs
    except Exception as e:
        logging.error(f"Error loading data for repo {repo_name}: {e}")
        return None, None


def plot_repo_activity(repo_name, breaks, attritions):
    """
    Plot the commit and PR frequency time series, with breaks and attrition points marked.
    """
    # Load commit and PR data
    commits, prs = load_data(repo_name)
    if commits is None or prs is None:
        logging.error(f"Unable to generate plot for repo {repo_name}. Missing data.")
        return

    # Group data by month
    commits_grouped = commits.groupby(commits['date'].dt.to_period('M')).size()
    prs_grouped = prs.groupby(prs['created_at'].dt.to_period('M')).size()

    # Convert to time series
    commits_series = commits_grouped.to_timestamp()
    prs_series = prs_grouped.to_timestamp()

    # Plot
    plt.figure(figsize=(14, 8))

    # Plot commit time series
    plt.plot(commits_series.index, commits_series.values, label='Commit Frequency', color='blue', linewidth=2)

    # Plot PR time series
    plt.plot(prs_series.index, prs_series.values, label='PR Frequency', color='green', linewidth=2)

    # Mark breaks as shaded regions
    for brk in breaks:
        start_date = pd.to_datetime(brk['start_time'])
        end_date = pd.to_datetime(brk['end_time'])
        plt.axvspan(start_date, end_date, color='red', alpha=0.3, label='Break Period' if 'Break Period' not in plt.gca().get_legend_handles_labels()[1] else None)

    # Mark attritions as vertical lines
    for attr in attritions:
        attr_date = pd.to_datetime(attr['attrition_time'])
        plt.axvline(attr_date, color='orange', linestyle='--', label='Attrition Point' if 'Attrition Point' not in plt.gca().get_legend_handles_labels()[1] else None)

    # Add labels and legend
    plt.title(f"Repo Activity Time Series: {repo_name}", fontsize=16)
    plt.xlabel("Date", fontsize=14)
    plt.ylabel("Frequency", fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True)
    plt.tight_layout()

    # Save plot
    output_file = f"../visualizations/{repo_name.replace('/', '_')}_activity_plot.png"
    plt.savefig(output_file)
    logging.info(f"Visualization saved for repo {repo_name} at {output_file}")
    plt.show()


def get_breaks_and_attritions(repo_name):
    """
    Retrieve breaks and attritions for a given repo from MongoDB.
    """
    try:
        client = MongoClient("mongodb://localhost:27017/")
        db = client["disengagement"]
        collection = db["repo_breaks_attritions"]

        # Fetch data for the specified repo
        repo_data = collection.find_one({"repo_name": repo_name})
        if repo_data is None:
            logging.warning(f"No data found for repo {repo_name}")
            return [], []

        breaks = repo_data.get("breaks", [])
        attritions = repo_data.get("attritions", [])
        return breaks, attritions
    except Exception as e:
        logging.error(f"Error fetching breaks and attritions for repo {repo_name}: {e}")
        return [], []


def main():
    # Example usage
    repo_name = "example_repo"  # Replace with the desired repository name

    # Retrieve breaks and attritions from MongoDB
    breaks, attritions = get_breaks_and_attritions(repo_name)

    # Generate the plot
    plot_repo_activity(repo_name, breaks, attritions)


if __name__ == "__main__":
    main()
