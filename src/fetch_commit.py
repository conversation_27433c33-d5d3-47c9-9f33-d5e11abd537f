import pandas as pd
from pymongo import MongoClient
import requests
import json
import time
import os
import logging
from logging.handlers import RotatingFileHandler

# config MongoDB
WINDOWS_IP = 'localhost'
PORT = 27017

# connect to MongoDB
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")

# config logging
log_file = 'fetch_commits.log'
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=5)
logger.addHandler(handler)

# config GitHub API
github_tokens = [
    '*********************************************************************************************',
    '*********************************************************************************************',
    '*********************************************************************************************',
]

# connect to MongoDB
db = client["disengagement"]
commits_collection = db["commits"]
cache_collection = db["progress_cache"]
pull_requests_collection = db["pull_requests"]
pr_comments_collection = db["pr_comments"]


# Load progress from MongoDB
def load_progress_cache(repo):
    cache = cache_collection.find_one({"repo_name": repo})
    if cache:
        return cache
    else:
        return {"repo_name": repo, "commit": 0, "pull_request": 0, "pr_comment": 0}

# Save progress to MongoDB
def save_progress_cache(repo, commit_page, pr_page=0, pr_comment_page=0):
    cache_collection.update_one(
        {"repo_name": repo},
        {"$set": {"commit": commit_page, "pull_request": pr_page, "pr_comment": pr_comment_page}},
        upsert=True
    )

# load sample_projects
# sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
# project_names = sample_projects['name'].tolist()
def fetch_commits(repo, tokens):
    """
    Fetch all commits from a repository through GitHub API and save to MongoDB.

    Parameters:
    - repo: Repository name in the format of 'owner/repo'
    - tokens: List of GitHub personal access tokens

    Returns:
    - None
    """
    # GitHub API URL
    base_url = f"https://api.github.com/repos/{repo}/commits"
    headers = {
        'Authorization': f'token {tokens[0]}',
        'Accept': 'application/vnd.github.v3+json'
    }

    cache = load_progress_cache(repo)
    page = cache.get("commit", 1)
    commits_fetched = 0
    fetch_count = 0

    while True:
        url = f"{base_url}?page={page}&per_page=100"
        all_tokens_limited = True
        for token in tokens:
            headers['Authorization'] = f'token {token}'
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                commits = response.json()
                if not commits:
                    return  # No more commits to fetch
                all_tokens_limited = False
                break
            except requests.exceptions.HTTPError as http_err:
                if response.status_code == 403:  # Rate limit exceeded
                    reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                    wait_time = max(0, reset_time - time.time())
                    logger.warning(f"Token {token} rate limited, trying next token...")
                    continue
                else:
                    logger.error(f"HTTP error occurred for {repo} with token {token}: {http_err}")
                    save_logs()
                    return
            except Exception as e:
                logger.error(f"Failed to fetch commits for {repo} with token {token}: {e}")
                save_logs()
                return

        if all_tokens_limited:
            reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
            wait_time = max(0, reset_time - time.time())
            logger.warning(f"All tokens rate limited, waiting for {wait_time} seconds...")
            save_logs()
            time.sleep(wait_time)
            continue

        # save commit json to MongoDB
        for commit in commits:
            commit['repo_name'] = repo  # Add repo_name to each commit
            try:
                commits_collection.insert_one(commit)
            except Exception as e:
                logger.error(f"Failed to save commit to MongoDB: {e}")
                save_logs()

        commits_fetched += len(commits)
        save_progress_cache(repo, page)
        logger.info(f"Saved {len(commits)} commits for {repo} to MongoDB")
        page += 1
        fetch_count += 1

        if fetch_count % 10 == 0:
            save_logs()

        if len(commits) < 100:
            break  # Less than 100 commits means no more pages

    logger.info(f"Total commits fetched for {repo}: {commits_fetched}")
    save_logs()

# connect to MongoDB
db = client["disengagement"]
commits_collection = db["commits"]
pull_requests_collection = db["pull_requests"]
pr_comments_collection = db["pr_comments"]
cache_collection = db["progress_cache"]

def fetch_pull_requests(repo, tokens):
    """
    Fetch all pull requests from a repository through GitHub API and save to MongoDB.

    Parameters:
    - repo: Repository name in the format of 'owner/repo'
    - tokens: List of GitHub personal access tokens

    Returns:
    - None
    """
    base_url = f"https://api.github.com/repos/{repo}/pulls"
    headers = {
        'Authorization': f'token {tokens[0]}',
        'Accept': 'application/vnd.github.v3+json'
    }

    cache = load_progress_cache(repo)
    page = cache.get("pull_request", 1)
    prs_fetched = 0
    fetch_count = 0

    while True:
        url = f"{base_url}?page={page}&per_page=100&state=all"
        all_tokens_limited = True
        for token in tokens:
            headers['Authorization'] = f'token {token}'
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                pull_requests = response.json()
                if not pull_requests:
                    logger.info(f"All pull requests fetched for {repo}")
                    save_progress_cache(repo, commit_page=cache.get("commit", 1), pr_page=page)
                    return
                all_tokens_limited = False
                break
            except requests.exceptions.HTTPError as http_err:
                if response.status_code == 403:
                    reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                    wait_time = max(0, reset_time - time.time())
                    logger.warning(f"Token {token} rate limited, trying next token...")
                    continue
                else:
                    logger.error(f"HTTP error occurred for {repo} with token {token}: {http_err}")
                    save_logs()
                    return
            except Exception as e:
                logger.error(f"Failed to fetch pull requests for {repo} with token {token}: {e}")
                save_logs()
                return

        if all_tokens_limited:
            reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
            wait_time = max(0, reset_time - time.time())
            logger.warning(f"All tokens rate limited, waiting for {wait_time} seconds...")
            save_logs()
            time.sleep(wait_time)
            continue

        for pr in pull_requests:
            pr['repo_name'] = repo
            try:
                pull_requests_collection.insert_one(pr)
            except Exception as e:
                logger.error(f"Failed to save pull request to MongoDB: {e}")
                save_logs()

        prs_fetched += len(pull_requests)
        save_progress_cache(repo, commit_page=cache.get("commit", 1), pr_page=page)
        logger.info(f"Saved {len(pull_requests)} pull requests for {repo} to MongoDB")
        page += 1
        fetch_count += 1

        if fetch_count % 10 == 0:
            save_logs()

        if len(pull_requests) < 100:
            break

    logger.info(f"Total pull requests fetched for {repo}: {prs_fetched}")
    save_logs()

def fetch_pr_review_comments(repo, tokens):
    """
    Fetch all pull request review comments from a repository through GitHub API and save to MongoDB.

    Parameters:
    - repo: Repository name in the format of 'owner/repo'
    - tokens: List of GitHub personal access tokens

    Returns:
    - None
    """
    base_url = f"https://api.github.com/repos/{repo}/pulls/comments"
    headers = {
        'Authorization': f'token {tokens[0]}',
        'Accept': 'application/vnd.github.v3+json'
    }

    cache = load_progress_cache(repo)
    page = cache.get("pr_comment", 1)
    comments_fetched = 0
    fetch_count = 0

    while True:
        url = f"{base_url}?page={page}&per_page=100"
        all_tokens_limited = True
        for token in tokens:
            headers['Authorization'] = f'token {token}'
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                comments = response.json()
                if not comments:
                    logger.info(f"All pull request review comments fetched for {repo}")
                    save_progress_cache(repo, commit_page=cache.get("commit", 1), pr_page=cache.get("pull_request", 1), pr_comment_page=page)
                    return
                all_tokens_limited = False
                break
            except requests.exceptions.HTTPError as http_err:
                if response.status_code == 403:
                    reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                    wait_time = max(0, reset_time - time.time())
                    logger.warning(f"Token {token} rate limited, trying next token...")
                    continue
                else:
                    logger.error(f"HTTP error occurred for {repo} with token {token}: {http_err}")
                    save_logs()
                    return
            except Exception as e:
                logger.error(f"Failed to fetch pull request review comments for {repo} with token {token}: {e}")
                save_logs()
                return

        if all_tokens_limited:
            reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
            wait_time = max(0, reset_time - time.time())
            logger.warning(f"All tokens rate limited, waiting for {wait_time} seconds...")
            save_logs()
            time.sleep(wait_time)
            continue

        for comment in comments:
            comment['repo_name'] = repo
            try:
                pr_comments_collection.insert_one(comment)
            except Exception as e:
                logger.error(f"Failed to save comment to MongoDB: {e}")
                save_logs()

        comments_fetched += len(comments)
        save_progress_cache(repo, commit_page=cache.get("commit", 1), pr_page=cache.get("pull_request", 1), pr_comment_page=page)
        logger.info(f"Saved {len(comments)} pull request review comments for {repo} to MongoDB")
        page += 1
        fetch_count += 1

        if fetch_count % 10 == 0:
            save_logs()

        if len(comments) < 100:
            break

    logger.info(f"Total pull request review comments fetched for {repo}: {comments_fetched}")
    save_logs()

def save_logs():
    for handler in logger.handlers:
        handler.flush()


def main():
    # Load sample projects
    sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
    project_names = sample_projects['name'].tolist()

    # project_names = ['0xproject/0x-launch-kit-backend']
    for project in project_names:
        # Load progress cache
        cache = load_progress_cache(project)

        # Check if commits, pull requests, and pull request review comments are already fetched
        if cache.get("commit", 0) == -1 and cache.get("pull_request", 0) == -1 and cache.get("pr_comment", 0) == -1:
            logger.info(f"Skipping {project}, already fetched.")
            continue

        # Fetch commits
        fetch_commits(project, github_tokens)
        # Fetch pull requests
        fetch_pull_requests(project, github_tokens)
        # Fetch pull request review comments
        fetch_pr_review_comments(project, github_tokens)

        # Mark project as fully fetched
        save_progress_cache(project, commit_page=-1, pr_page=-1, pr_comment_page=-1)


if __name__ == "__main__":
    main()