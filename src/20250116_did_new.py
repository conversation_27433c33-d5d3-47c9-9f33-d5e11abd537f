import pandas as pd
import numpy as np
import os
import logging
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.neighbors import NearestNeighbors
import statsmodels.api as sm
import matplotlib.pyplot as plt
from itertools import product

# Set up logging
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "psm_2025_0116.log")),
        logging.StreamHandler(),
    ],
)

output_dir = "../result/standardized_productivity_2025011603/"
os.makedirs(output_dir, exist_ok=True)


def save_data(data, filename):
    """Save data to a CSV file."""
    filepath = os.path.join(output_dir, filename)
    data.to_csv(filepath, index=False)
    logging.info(f"Saved {filepath}")


def sigmoid(x):
    """Sigmoid function."""
    return 1 / (1 + np.exp(-x))


def match_all_treatments_psm(
    treatment_repos_with_attrition_date,
    all_repo_names,
    productivity,
    n_neighbors,
    timewindow_weeks,
    feature_columns=["feature_relative_increase", "feature_magnitude"],
):
    """
    Perform propensity score matching (PSM) to match treatment and control repositories.
    """
    logging.info("Starting propensity score matching...")

    treatment_repos = set(treatment_repos_with_attrition_date["repo_name"].tolist())
    available_controls = set(all_repo_names)
    logging.info(f"Initial available controls: {len(available_controls)} repositories.")

    treatment_features_df = productivity[
        (productivity["repo_name"].isin(treatment_repos))
        & (productivity["is_treated"] == 1)
    ]
    save_data(treatment_features_df, "treatment_features.csv")

    control_features_df = productivity[
        (productivity["repo_name"].isin(available_controls))
    ]
    save_data(control_features_df, "control_features.csv")

    missing_treatments = treatment_repos - set(treatment_features_df["repo_name"])
    if missing_treatments:
        for repo in missing_treatments:
            logging.warning(
                f"Treatment repo {repo} has no valid feature values at treatment time."
            )
        treatment_features_df = treatment_features_df[
            ~treatment_features_df["repo_name"].isin(missing_treatments)
        ]

    logging.info(
        f"Number of treatment repositories with valid features: {len(treatment_features_df)}"
    )
    logging.info(
        f"Number of control repositories with valid features: {len(control_features_df)}"
    )

    removed_treatment = len(treatment_features_df) - len(
        treatment_features_df.dropna(subset=feature_columns)
    )
    # removed_control = len(control_features_df) - len(control_features_df.dropna(subset=feature_columns))
    logging.info(
        f"Removed {removed_treatment} rows with NaN values from treatment features."
    )
    # logging.info(f"Removed {removed_control} rows with NaN values from control features.")
    treatment_features_df = treatment_features_df.dropna(subset=feature_columns)
    control_features_df = control_features_df.dropna(subset=feature_columns)

    X_control = control_features_df[feature_columns].values

    nbrs = NearestNeighbors(n_neighbors=8, algorithm="auto", metric="euclidean").fit(
        X_control
    )

    matched_pairs = {}
    for index, row in treatment_features_df.iterrows():
        t_repo = row["repo_name"]
        t_time = row["standardized_time_weeks"]
        matched_controls = []
        distances, indices = nbrs.kneighbors([row[feature_columns].values])
        matched_repos = []
        count = 0
        for idx in indices[0]:
            count += 1
            if count > n_neighbors:
                break
            if control_features_df.iloc[idx]["repo_name"] in matched_repos:
                logging.info(
                    f"Control repo {control_features_df.iloc[idx]['repo_name']} is already matched. Skipping..."
                )
                continue
            is_treatment_within_timewindow = (
                productivity[
                    (
                        productivity["repo_name"]
                        == control_features_df.iloc[idx]["repo_name"]
                    )
                    & (productivity["standardized_time_weeks"] > t_time)
                    & (
                        productivity["standardized_time_weeks"]
                        <= t_time + timewindow_weeks
                    )
                    & (productivity["is_treated"] == 1)
                ].shape[0]
                > 0
            )
            if is_treatment_within_timewindow:
                logging.info(
                    f"For Treatment repo {t_repo}, Control repo {control_features_df.iloc[idx]['repo_name']} is also under treatment within {timewindow_weeks} weeks. Skipping..."
                )
                continue
            if control_features_df.iloc[idx]["repo_name"] == t_repo:
                logging.info(
                    f"Control repo {control_features_df.iloc[idx]['repo_name']} is the same as the treatment repo {t_repo}. Skipping..."
                )
                continue
            control_repo = control_features_df.iloc[idx]["repo_name"]
            control_time = control_features_df.iloc[idx]["standardized_time_weeks"]
            matched_controls.append(
                {
                    "repo_name": control_repo,
                    "matched_time": control_time,
                    "features": control_features_df.iloc[idx][feature_columns].values,
                }
            )
        if len(matched_controls) == 0:
            logging.warning(f"No matched controls for treatment repo {t_repo}.")
            continue
        matched_pairs[t_repo] = {
            "treatment_time": t_time,
            "controls": matched_controls,
            "treatment_features": row[feature_columns].values,
            "control_features": control_features_df.iloc[indices[0]][
                feature_columns
            ].values,
        }

    logging.info("Propensity score matching completed.")
    logging.info(f"Total matched pairs: {len(matched_pairs)}")
    return matched_pairs, treatment_features_df, control_features_df


def visualize_trends(
    final_data, metric_column="pr_throughput", timewindow_weeks=None, n_neighbors=None
):
    """Visualize trends for treatment and control groups."""
    treatment_data = final_data[final_data["is_treatment"] == 1]
    control_data = final_data[final_data["is_treatment"] == 0]

    def compute_stats(group_data):
        grouped = group_data.groupby("relative_time_weeks")
        mean = grouped[metric_column].mean()
        std = grouped[metric_column].std()
        count = grouped[metric_column].count()
        sem = std / np.sqrt(count)
        return mean, mean - sem, mean + sem

    treatment_mean, treatment_lower, treatment_upper = compute_stats(treatment_data)
    control_mean, control_lower, control_upper = compute_stats(control_data)

    plt.figure(figsize=(12, 6))
    plt.plot(treatment_mean.index, treatment_mean, label="Treatment Mean", color="blue")
    plt.fill_between(
        treatment_mean.index,
        treatment_lower,
        treatment_upper,
        color="blue",
        alpha=0.2,
        label="Treatment 95% CI",
    )
    plt.plot(control_mean.index, control_mean, label="Control Mean", color="green")
    plt.fill_between(
        control_mean.index,
        control_lower,
        control_upper,
        color="green",
        alpha=0.2,
        label="Control 95% CI",
    )
    plt.axvline(0, color="red", linestyle="--", label="Treatment Start (time=0)")
    plt.title(f"{metric_column.capitalize()} Over Time (Treatment vs. Control)")
    plt.xlabel("Standardized Time (Weeks)")
    plt.ylabel(metric_column.capitalize())
    plt.legend()

    plot_filename = (
        f"{metric_column}_trend_plot_tw{timewindow_weeks}_nn{n_neighbors}.png"
    )
    plot_path = os.path.join(output_dir, plot_filename)
    plt.savefig(plot_path)
    logging.info(f"Saved plot to {plot_path}.")
    plt.show()


if __name__ == "__main__":
    metric_column = "pr_throughput"
    # timewindow_weeks_list = [12]
    timewindow_weeks_list = [8, 10, 12, 14]
    n_neighbors_list = [5, 6, 7]

    repo_info = pd.read_csv("../data/sample_projects_quartiles.csv")
    attrition = pd.read_csv("../result/attritions.csv")
    productivity = pd.read_csv("../data/2025_0116_pr_throughput_productivity.csv")

    productivity["datetime"] = pd.to_datetime(productivity["datetime"])
    global_min_time = productivity["datetime"].min()
    productivity["standardized_time_weeks"] = (
        (productivity["datetime"] - global_min_time).dt.days // 7
    ).astype(int)

    weekly_productivity = (
        productivity.groupby(["repo_name", "standardized_time_weeks"])["pr_throughput"]
        .sum()
        .reset_index()
    )

    min_week = weekly_productivity["standardized_time_weeks"].min()
    max_week = weekly_productivity["standardized_time_weeks"].max()
    all_weeks = range(min_week, max_week + 1)
    all_repos = weekly_productivity["repo_name"].unique()

    index = pd.MultiIndex.from_product(
        [all_repos, all_weeks], names=["repo_name", "standardized_time_weeks"]
    )
    complete_df = pd.DataFrame(index=index).reset_index()

    weekly_productivity = complete_df.merge(
        weekly_productivity, on=["repo_name", "standardized_time_weeks"], how="left"
    ).fillna(0)
    weekly_productivity["pr_throughput"] = weekly_productivity["pr_throughput"].astype(
        int
    )

    attrition["attrition_date"] = pd.to_datetime(attrition["attrition_date"])
    attrition["standardized_attrition_week"] = (
        (attrition["attrition_date"] - global_min_time).dt.days // 7
    ).astype(int)
    attrition_map = attrition.set_index("repo_name")[
        "standardized_attrition_week"
    ].to_dict()

    def assign_is_treated(row, attrition_map):
        if row["repo_name"] in attrition_map:
            return (
                1
                if row["standardized_time_weeks"] == attrition_map[row["repo_name"]]
                else 0
            )
        else:
            return 0

    weekly_productivity["is_treated"] = weekly_productivity.apply(
        assign_is_treated, args=(attrition_map,), axis=1
    )

    weekly_productivity = weekly_productivity.sort_values(
        ["repo_name", "standardized_time_weeks"]
    )
    weekly_productivity["O_i_t_minus_1"] = weekly_productivity.groupby("repo_name")[
        "pr_throughput"
    ].shift(1)
    weekly_productivity["I_it"] = np.log(
        (weekly_productivity["pr_throughput"] + 1)
        / (weekly_productivity["O_i_t_minus_1"] + 1)
    ).fillna(0)

    # window_size = 12  # weeks

    attrition_counts = attrition["repo_name"].value_counts().reset_index()
    attrition_counts.columns = ["repo_name", "attrition_count"]
    repos_with_one_attrition = attrition_counts[
        attrition_counts["attrition_count"] == 1
    ]
    treatment_repos_with_attrition_date = pd.merge(
        repos_with_one_attrition, attrition, on="repo_name", how="left"
    )
    all_repo_names = weekly_productivity["repo_name"].unique()

    for timewindow_weeks in timewindow_weeks_list:
        for n_neighbors in n_neighbors_list:
            logging.info(
                f"Starting analysis with timewindow_weeks={timewindow_weeks} and n_neighbors={n_neighbors}"
            )

            # if there  already exists pr

            weekly_productivity["sum_I_it_last_12_weeks"] = weekly_productivity.groupby(
                "repo_name"
            )["I_it"].transform(
                lambda x: x.shift(1)
                .rolling(window=timewindow_weeks, min_periods=timewindow_weeks)
                .sum()
            )
            weekly_productivity[
                "sum_pr_throughput_last_12_weeks"
            ] = weekly_productivity.groupby("repo_name")["pr_throughput"].transform(
                lambda x: x.shift(1)
                .rolling(window=timewindow_weeks, min_periods=timewindow_weeks)
                .sum()
            )
            weekly_productivity["sum_I_it_last_12_weeks"] = weekly_productivity.groupby(
                "repo_name"
            )["I_it"].transform(
                lambda x: x.shift(1)
                .rolling(window=timewindow_weeks, min_periods=timewindow_weeks)
                .sum()
            )
            weekly_productivity[
                "sum_pr_throughput_last_12_weeks"
            ] = weekly_productivity.groupby("repo_name")["pr_throughput"].transform(
                lambda x: x.shift(1)
                .rolling(window=timewindow_weeks, min_periods=timewindow_weeks)
                .sum()
            )
            weekly_productivity["sum_I_it_last_12_weeks"] = weekly_productivity.groupby(
                "repo_name"
            )["I_it"].transform(
                lambda x: x.shift(1)
                .rolling(window=timewindow_weeks, min_periods=timewindow_weeks)
                .sum()
            )
            weekly_productivity[
                "sum_pr_throughput_last_12_weeks"
            ] = weekly_productivity.groupby("repo_name")["pr_throughput"].transform(
                lambda x: x.shift(1)
                .rolling(window=timewindow_weeks, min_periods=timewindow_weeks)
                .sum()
            )
            weekly_productivity["feature_relative_increase"] = weekly_productivity[
                "sum_I_it_last_12_weeks"
            ]
            weekly_productivity["feature_magnitude"] = weekly_productivity[
                "sum_pr_throughput_last_12_weeks"
            ]
            weekly_productivity["feature_sigmod"] = sigmoid(
                weekly_productivity["feature_relative_increase"]
                + weekly_productivity["feature_magnitude"]
            )
            save_data(
                weekly_productivity,
                f"standardized_productivity_parallel_{timewindow_weeks}",
            )

            matched_pairs, treatment_features, control_features = (
                match_all_treatments_psm(
                    treatment_repos_with_attrition_date,
                    all_repo_names,
                    weekly_productivity,
                    n_neighbors,
                    timewindow_weeks,
                    feature_columns=["feature_sigmod"],
                )
            )
            with open(
                os.path.join(
                    output_dir,
                    f"matched_pairs_tw{timewindow_weeks}_nn{n_neighbors}.txt",
                ),
                "w",
            ) as f:
                f.write(str(matched_pairs))

            combined_data = []
            for t_repo, match_info in matched_pairs.items():
                t_time = match_info["treatment_time"]
                controls = match_info["controls"]
                t_data = weekly_productivity[
                    weekly_productivity["repo_name"] == t_repo
                ].copy()
                t_data["relative_time_weeks"] = (
                    t_data["standardized_time_weeks"] - t_time
                )
                t_data["is_treatment"] = 1
                t_data["post_treatment"] = (t_data["relative_time_weeks"] > 0).astype(
                    int
                )
                t_data["is_treatment_post_treatment"] = (
                    t_data["is_treatment"] * t_data["post_treatment"]
                )
                t_data = t_data[
                    t_data["relative_time_weeks"].between(
                        -timewindow_weeks, timewindow_weeks + 1
                    )
                ]
                control_dfs = []
                for control in controls:
                    c_repo = control["repo_name"]
                    c_matched_time = control["matched_time"]
                    c_data = weekly_productivity[
                        weekly_productivity["repo_name"] == c_repo
                    ].copy()
                    c_data["relative_time_weeks"] = (
                        c_data["standardized_time_weeks"] - c_matched_time
                    )
                    c_data["is_treatment"] = 0
                    c_data["post_treatment"] = (
                        c_data["relative_time_weeks"] > 0
                    ).astype(int)
                    c_data["is_treatment_post_treatment"] = (
                        c_data["is_treatment"] * c_data["post_treatment"]
                    )
                    c_data = c_data[
                        c_data["relative_time_weeks"].between(
                            -timewindow_weeks, timewindow_weeks + 1
                        )
                    ]
                    control_dfs.append(c_data)
                if control_dfs:
                    combined_set = pd.concat([t_data] + control_dfs, ignore_index=True)
                    combined_data.append(combined_set)
                else:
                    logging.warning(
                        f"No valid controls for treatment repository {t_repo}."
                    )

            if combined_data:
                final_data = pd.concat(combined_data, ignore_index=True)
                X = sm.add_constant(
                    final_data[
                        [
                            "post_treatment",
                            "is_treatment",
                            "is_treatment_post_treatment",
                        ]
                    ]
                )
                y = final_data[metric_column]
                model = sm.OLS(y, X).fit()
                save_data(
                    final_data, f"final_data_tw{timewindow_weeks}_nn{n_neighbors}.csv"
                )
                with open(
                    os.path.join(
                        output_dir,
                        f"regression_summary_tw{timewindow_weeks}_nn{n_neighbors}.txt",
                    ),
                    "w",
                ) as f:
                    f.write(model.summary().as_text())
                visualize_trends(
                    final_data, metric_column, timewindow_weeks, n_neighbors
                )
            else:
                logging.warning("No matched data found for DiD analysis.")
            logging.info(
                f"Analysis completed for timewindow_weeks={timewindow_weeks} and n_neighbors={n_neighbors}"
            )
