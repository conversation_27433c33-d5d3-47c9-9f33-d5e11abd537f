# import libraries
import pandas as pd
import numpy as np
import os
import logging
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.neighbors import NearestNeighbors
import statsmodels.api as sm
import matplotlib.pyplot as plt

# set up logging
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "psm_2025_0121.log")),
        logging.StreamHandler(),
    ],
)

# set output directory
output_dir = "../result/standardized_productivity_20250121/"
os.makedirs(output_dir, exist_ok=True)

# set save data function
def save_data(data, filename):
    """Save data to a CSV file."""
    filepath = os.path.join(output_dir, filename)
    data.to_csv(filepath, index=False)
    logging.info(f"Saved {filepath}")
    
    
# set sigmod function
def sigmod(x):
    return 1 / (1 + np.exp(-x))

# set functions to match treated and control units
def compile_control_group_psm(
    treatment_repos_with_attrition_date,
    all_repo_names,
    productivity_metric_data,
    n_neighbors,
    timewindow_weeks,
    feature_columns,
):
    """
    Perform propensity score matching (PSM) to match treatment and control repositories.
    """
    logging.info("Starting propensity score matching...")

    treatment_repos = set(treatment_repos_with_attrition_date["repo_name"].tolist())
    available_controls = set(all_repo_names)
    logging.info(f"Initial available controls: {len(available_controls)} repositories.")

    treatment_features_df = productivity_metric_data[
        (productivity_metric_data["repo_name"].isin(treatment_repos))
        & (productivity_metric_data["is_treated"] == 1)
    ]
    control_features_df = productivity_metric_data[
        (productivity_metric_data["repo_name"].isin(available_controls))
    ]
    # TODO need test to see the result of X_control
    X_control = control_features_df[feature_columns].values
    # find the top n_neighbors closest control repositories not using knn, but use matrix multiplication


    nbrs = NearestNeighbors(n_neighbors=10, algorithm="auto", metric="euclidean").fit(
        X_control
    )

    matched_pairs = {}
    for index, row in treatment_features_df.iterrows():
        t_repo = row["repo_name"]
        t_time = row["standardized_time_weeks"]
        matched_controls = []
        distances, indices = nbrs.kneighbors([row[feature_columns].values])
        matched_repos = []
        count = 0
        for idx in indices[0]:
            count += 1
            if count > n_neighbors:
                break
            if control_features_df.iloc[idx]["repo_name"] in matched_repos:
                logging.info(
                    f"Control repo {control_features_df.iloc[idx]['repo_name']} is already matched. Skipping..."
                )
                continue
            is_treatment_within_timewindow = (
                productivity_metric_data[
                    (
                        productivity_metric_data["repo_name"]
                        == control_features_df.iloc[idx]["repo_name"]
                    )
                    & (productivity_metric_data["standardized_time_weeks"] > t_time)
                    & (
                        productivity_metric_data["standardized_time_weeks"]
                        <= t_time + timewindow_weeks
                    )
                    & (productivity_metric_data["is_treated"] == 1)
                ].shape[0]
                > 0
            )
            if is_treatment_within_timewindow:
                logging.info(
                    f"For Treatment repo {t_repo}, Control repo {control_features_df.iloc[idx]['repo_name']} is also under treatment within {timewindow_weeks} weeks. Skipping..."
                )
                continue
            if control_features_df.iloc[idx]["repo_name"] == t_repo:
                logging.info(
                    f"Control repo {control_features_df.iloc[idx]['repo_name']} is the same as the treatment repo {t_repo}. Skipping..."
                )
                continue
            control_repo = control_features_df.iloc[idx]["repo_name"]
            control_time = control_features_df.iloc[idx]["standardized_time_weeks"]
            matched_controls.append(
                {
                    "repo_name": control_repo,
                    "matched_time": control_time,
                    "features": control_features_df.iloc[idx][feature_columns].values,
                }
            )
        if len(matched_controls) == 0:
            logging.warning(f"No matched controls for treatment repo {t_repo}.")
            continue
        matched_pairs[t_repo] = {
            "treatment_time": t_time,
            "controls": matched_controls,
            "treatment_features": row[feature_columns].values,
            "control_features": control_features_df.iloc[indices[0]][
                feature_columns
            ].values,
        }

    logging.info("Propensity score matching completed.")
    logging.info(f"Total matched pairs: {len(matched_pairs)}")
    return matched_pairs, treatment_features_df, control_features_df

def visualize_trends(
    final_data, metric_column="pr_throughput", timewindow_weeks=None, n_neighbors=None
):
    """Visualize trends for treatment and control groups."""
    treatment_data = final_data[final_data["is_treatment"] == 1]
    control_data = final_data[final_data["is_treatment"] == 0]

    def compute_stats(group_data):
        grouped = group_data.groupby("relative_time_weeks")
        mean = grouped[metric_column].mean()
        std = grouped[metric_column].std()
        count = grouped[metric_column].count()
        sem = std / np.sqrt(count)
        return mean, mean - sem, mean + sem

    treatment_mean, treatment_lower, treatment_upper = compute_stats(treatment_data)
    control_mean, control_lower, control_upper = compute_stats(control_data)

    plt.figure(figsize=(12, 6))
    plt.plot(treatment_mean.index, treatment_mean, label="Treatment Mean", color="blue")
    plt.fill_between(
        treatment_mean.index,
        treatment_lower,
        treatment_upper,
        color="blue",
        alpha=0.2,
        label="Treatment 95% CI",
    )
    plt.plot(control_mean.index, control_mean, label="Control Mean", color="green")
    plt.fill_between(
        control_mean.index,
        control_lower,
        control_upper,
        color="green",
        alpha=0.2,
        label="Control 95% CI",
    )
    plt.axvline(0, color="red", linestyle="--", label="Treatment Start (time=0)")
    plt.title(f"{metric_column.capitalize()} Over Time (Treatment vs. Control)")
    plt.xlabel("Standardized Time (Weeks)")
    plt.ylabel(metric_column.capitalize())
    plt.legend()

    plot_filename = (
        f"{metric_column}_trend_plot_tw{timewindow_weeks}_nn{n_neighbors}.pdf"
    )
    plot_path = os.path.join(output_dir, plot_filename)
    plt.savefig(plot_path)
    logging.info(f"Saved plot to {plot_path}.")
    plt.show()
    
def unify_repo_productivity_data(productivity_data):
  '''fill blank values in uniformed timeweeks using vectorized operations'''
  logging.info("Unifying productivity data...")
  
  # Create all possible combinations of repos and time weeks
  all_repos = productivity_data['repo_name'].unique()
  time_range = range(
    int(productivity_data['standardized_time_weeks'].min()),
    int(productivity_data['standardized_time_weeks'].max()) + 1
  )
  
  # Create a cross join using pandas
  repo_df = pd.DataFrame({'repo_name': all_repos})
  time_df = pd.DataFrame({'standardized_time_weeks': time_range})
  unified_frame = repo_df.assign(key=1).merge(time_df.assign(key=1), on='key').drop('key', axis=1)
  
  # Merge with original data, filling missing values
  unified_productivity_data = unified_frame.merge(
    productivity_data,
    on=['repo_name', 'standardized_time_weeks'],
    how='left'
  )
  
  # Fill missing values with 0
  unified_productivity_data['pr_throughput'] = unified_productivity_data['pr_throughput'].fillna(0)
  
  # aggregate the data by standardized_time_weeks, summing pr_throughput, remaining columns are the same
  unified_productivity_data = unified_productivity_data.groupby(['repo_name', 'standardized_time_weeks']).agg({
    'pr_throughput': 'sum',
  }).reset_index()
  # 去除多余的rows，即repo的pr_throughput从>0开始的rows，到最后一个>0的rows
  repo_first_time = unified_productivity_data[unified_productivity_data['pr_throughput'] > 0].groupby('repo_name').first().reset_index()
  repo_last_time = unified_productivity_data[unified_productivity_data['pr_throughput'] > 0].groupby('repo_name').last().reset_index()
  repo_time_range = repo_first_time.merge(repo_last_time, on='repo_name', suffixes=('_first', '_last'))
  unified_productivity_data = unified_productivity_data.merge(repo_time_range, on='repo_name')
  unified_productivity_data = unified_productivity_data[
    (unified_productivity_data['standardized_time_weeks'] >= unified_productivity_data['standardized_time_weeks_first']) &
    (unified_productivity_data['standardized_time_weeks'] <= unified_productivity_data['standardized_time_weeks_last'])
  ]
  
  # remove the intermediate columns
  unified_productivity_data = unified_productivity_data.drop(columns=['standardized_time_weeks_first', 'standardized_time_weeks_last'])

  
  return unified_productivity_data.reset_index(drop=True)

  
# features for calculation

## 1. rolling slope trend
def calculate_rolling_slope_window(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling slope for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the slope for.
        output_column (str): Name of the output column for slopes.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling slope column.
    """
    
    def rolling_slope(df, column, window):
        y = df[column].values
        n = window
        if len(y) < n:
            return pd.Series([np.nan] * len(y), index=df.index)  # If not enough data, return NaN
        
        x = np.arange(1, n + 1)
        sum_x = np.sum(x)
        sum_x2 = np.sum(x ** 2)
        denominator = n * sum_x2 - sum_x ** 2
        
        sum_y = np.convolve(y, np.ones(n), 'valid')
        sum_xy = np.convolve(y, x[::-1], 'valid')
        
        slopes = (n * sum_xy - sum_x * sum_y) / denominator
        return pd.Series([np.nan] * (n - 1) + list(slopes), index=df.index)
    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling slope calculation for each group
    data[output_column] = data.groupby(group_column).apply(
        lambda group: rolling_slope(group, target_column, window_size)
    ).reset_index(level=0, drop=True)
    
    return data

## 2. rolling mean
def calculating_rolling_mean(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling mean for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the mean for.
        output_column (str): Name of the output column for means.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling mean column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling mean calculation for each group
    data[output_column] = data.groupby(group_column)[target_column].transform(
        lambda x: x.rolling(window=window_size, min_periods=window_size).mean()
    )
    
    return data

## 3. rolling rate of change
def calculate_rolling_rate_of_change(data, group_column, target_column, window_size, output_column, sort_column):
    """
    Calculate the rolling rate of change for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the rate of change for.
        output_column (str): Name of the output column for rate of change.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling rate of change column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling rate of change calculation for each group
    data['O_i_t_minus_1'] = data.groupby(group_column)[target_column].shift(1)
    data['I_it'] = np.log(
      (data[target_column] + 1) / (data['O_i_t_minus_1'] + 1)
    )
    data['sum_I_it_last_windows'] = data.groupby(group_column)['I_it'].transform(
      lambda x: x.rolling(window=window_size, min_periods=window_size).sum()
    )
    data[output_column] =data['sum_I_it_last_windows']
    
    # remove intermediate columns
    data = data.drop(columns=['O_i_t_minus_1', 'I_it', 'sum_I_it_last_windows'])
    
    return data
    


if __name__ == "__main__":
  