import os
import time
from truckfactor.compute import main
import pandas as pd
from git import Repo, GitCommandError, rmtree
import logging
from pymongo import MongoClient
client = MongoClient('localhost', 27017)
db = client['disengagement']
collection = db['truckfactor']
# set index to repo_name
collection.create_index("repo_name")

# set up logging, also output in cmd
logging.basicConfig(filename='../logs/core_truckfactor.log', level=logging.INFO, format='%(asctime)s - %(message)s', datefmt='%d-%b-%y %H:%M:%S', filemode='w')


sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
sample_projects
# build a loop to iterate over the sample_projects file
# and clone the repositories
for index, _ in sample_projects.iterrows():
    repo_name = sample_projects["repo_name"][index]
    # if the repo is already in the database, skip it
    if collection.find_one({"repo_name": repo_name}):
        logging.info(f"Skipping {repo_name} as it is already in the database")
        continue
    repo_path = f"https://github.com/{repo_name}"
    default_branch = sample_projects["defaultBranch"][index]
    cloned_repo_name = repo_name.replace('/', '_')
    path_to_clone = f"../../cloned_repo/{cloned_repo_name}"

    # Create the directory if it doesn't exist
    if not os.path.exists(path_to_clone):
        os.makedirs(path_to_clone)

    # Clone the repository with progress reporting
    try:
        start_time = time.time()
        logging.info(f"Cloning {repo_name} to {path_to_clone}")
        Repo.clone_from(repo_path, to_path=path_to_clone, branch=default_branch)
        logging.info(f"Cloning took {time.time() - start_time} seconds")
    except GitCommandError as e:
        logging.error(f"Error cloning repository: {e}")
        # delete the cloned repository
        try:
          rmtree(path_to_clone)
        except FileNotFoundError:
          print("File not found")
        # retry for 3 times
        for i in range(2):
            try:
                logging.info(f"Retrying cloning {repo_name} to {path_to_clone}")
                Repo.clone_from(repo_path, to_path=path_to_clone, branch=default_branch)
                logging.info(f"Cloning took {time.time() - start_time} seconds")
                break
            except GitCommandError as e:
                logging.error(f"Error cloning repository: {e}")
                # delete the cloned repository
                try:
                  rmtree(path_to_clone)
                except FileNotFoundError:
                  print("File not found")
        continue
    # Proceed with your analysis
    logging.info(f"Calculating truck factor for {repo_name}")
    start_time = time.time()
    try:
      truckfactor, commit_sha, author = main(path_to_clone)
    except Exception as e:
      logging.error(f"Error calculating truck factor: {e}")
      continue
    logging.info(f"Calculation took {time.time() - start_time} seconds")
    # save the result in mongoDB
    data = {
        "repo_name": repo_name,
        "truckfactor": truckfactor,
        "commit_sha": commit_sha,
        "author": author,
    }
    collection.insert_one(data)
    logging.info(f"Succesfully calculated {index+1} numbers")
    logging.info(f"Truck factor for {repo_name} is {truckfactor}")
    # delete the cloned repository
    try:
      rmtree(path_to_clone)
    except FileNotFoundError:
      print("File not found")
    logging.info(f"Deleted {path_to_clone}")
