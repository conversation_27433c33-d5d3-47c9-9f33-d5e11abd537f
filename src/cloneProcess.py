import os
import subprocess
from git import Repo, GitCommandError
import logging
import pandas as pd
# read sample_projects file
# sample repo to download git repo by prdiller
sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
sample_projects
# Configure logging
logging.basicConfig(level=logging.INFO)

# Repository details
index = 0
repo_name = sample_projects["repo_name"][index]
repo_path = f"https://github.com/{repo_name}"
default_branch = sample_projects["defaultBranch"][index]
cloned_repo_name = repo_name.replace('/', '_')
path_to_clone = f"../../cloned_repo/{cloned_repo_name}"

# Create the directory if it doesn't exist
if not os.path.exists(path_to_clone):
    os.makedirs(path_to_clone)

# Clone the repository with progress reporting
# try:
#     Repo.clone_from(repo_path, to_path=path_to_clone, branch=default_branch)
# except GitCommandError as e:
#     logging.error(f"Error cloning repository: {e}")

# Define the path to the truckfactor-tool repository
truckfactor_tool_path = "../../Truck-Factor/gittruckfactor/"  # Update this path

# Function to build the Java code
def build_truckfactor_tool(tool_path):
    build_dir = os.path.join(tool_path, 'gittruckfactor')
    os.chdir(build_dir)
    try:
        subprocess.run(['mvn', 'package'], check=True)
        logging.info("Java code built successfully.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error building Java code: {e}")
    finally:
        os.chdir(tool_path)

# Function to extract commit and file information
def extract_commit_info(tool_path, git_repo_path):
    scripts_dir = os.path.join(tool_path, 'scripts')
    os.chdir(scripts_dir)
    try:
        subprocess.run(['sh', 'commit_log_script.sh', os.path.relpath(git_repo_path, scripts_dir)], check=True)
        logging.info("Commit and file information extracted successfully.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error extracting commit info: {e}")
    finally:
        os.chdir(tool_path)

# Optional function to extract files to discard using Linguist library
def extract_linguist_info(tool_path, git_repo_path):
    # Define the path to the scripts directory
    scripts_dir = os.path.join(tool_path, 'scripts')
    print(f"Scripts directory: {scripts_dir}")  # Debugging output

    # Check if the scripts directory exists
    if not os.path.exists(scripts_dir):
        logging.error(f"Error: Scripts directory does not exist at {scripts_dir}")
        return

    # Change to the scripts directory
    os.chdir(scripts_dir)
    try:
        # Run the linguist_script.sh script
        subprocess.run(['sh', 'linguist_script.sh', os.path.relpath(git_repo_path, scripts_dir)], check=True)
        logging.info("Files to discard extracted using Linguist library.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error extracting linguist info: {e}")
    finally:
        # Return to the original tool path
        os.chdir(tool_path)

# Function to execute the gittruckfactor tool and capture output
def execute_truckfactor_tool(tool_path, git_repo_path, git_repo_fullname):
    jar_path = os.path.join(tool_path, 'gittruckfactor', 'target', 'gittruckfactor-1.0.jar')
    try:
        result = subprocess.run(['java', '-jar', jar_path, git_repo_path, git_repo_fullname],
                                check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output = result.stdout.decode('utf-8')
        logging.info("Truck factor tool executed successfully.")
        return output
    except subprocess.CalledProcessError as e:
        logging.error(f"Error executing truck factor tool: {e}")
        logging.error(f"Error output: {e.stderr.decode('utf-8')}")
        return None

# Main script execution
if __name__ == "__main__":
    # Build the truckfactor tool
    # build_truckfactor_tool(truckfactor_tool_path)
    
    # Extract commit and file information
    extract_commit_info(truckfactor_tool_path, path_to_clone)
    
    # Optional: Extract files to discard using Linguist library
    extract_linguist_info(truckfactor_tool_path, path_to_clone)
    
    # Execute the truckfactor tool and capture the output
    tool_output = execute_truckfactor_tool(truckfactor_tool_path, path_to_clone, repo_name)
    
    if tool_output:
        # Store or use the tool output as needed
        print("Truck Factor Tool Output:")
        print(tool_output)
        # Example: Save output to a file
        # with open('truck_factor_output.txt', 'w') as f:
        #     f.write(tool_output)
    else:
        logging.warning("No output received from the truck factor tool.")