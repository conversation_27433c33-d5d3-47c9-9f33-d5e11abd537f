import pandas as pd
from pymongo import MongoClient
import requests
import logging
from logging.handlers import RotatingFileHandler
from threading import Thread, Lock
import queue
import time

# MongoDB 配置
WINDOWS_IP = 'localhost'
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")
db = client["disengagement"]
commits_collection = db["commits"]
cache_collection = db["progress_cache"]
pull_requests_collection = db["pull_requests"]
pr_comments_collection = db["pr_comments"]

# GitHub API tokens
github_tokens = [
    '*********************************************************************************************',
    '*********************************************************************************************',
    '*********************************************************************************************',
]

# 日志配置
log_file = 'fetch_commits.log'
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
handler = RotatingFileHandler(log_file, maxBytes=10 * 1024 * 1024, backupCount=5)
logger.addHandler(handler)

# Token 管理
token_lock = Lock()
token_index = 0


def get_next_token():
    global token_index
    with token_lock:
        token = github_tokens[token_index]
        token_index = (token_index + 1) % len(github_tokens)
        logger.info(f"Switching to token index {token_index}.")
    return token


# 进度缓存管理
cache_lock = Lock()


def load_progress_cache(repo):
    """加载进度缓存"""
    caches = list(cache_collection.find({"repo_name": repo}))
    if caches:
        cache = max(caches, key=lambda x: len(x))
        if "pr_status_all" not in cache:  # 添加 pr_status_all 属性
            cache["pr_status_all"] = 0
            save_progress_cache(repo, pr_status_all=0)
        logger.info(f"Loaded cache for repo: {repo}.")
        return cache
    else:
        logger.info(f"Initializing new cache for repo: {repo}.")
        new_cache = {
            "repo_name": repo,
            "commit": 1,
            "pull_request": 1,
            "pr_comment": 1,
            "commit_num": 0,
            "pr_num": 0,
            "pr_review_num": 0,
            "commit_page_num": -1,
            "pull_request_page_num": -1,
            "pr_review_page_num": -1,
            "commits_finished": 0,
            "pr_finished": 0,
            "pr_review_finished": 0,
            "closed_pr": 0,
            "pr_status_all": 0,  # 新增属性，用于标记 state=all 的 PR 爬取状态
        }
        save_progress_cache(repo, **new_cache)
        return new_cache


def save_progress_cache(repo, **kwargs):
    """保存进度缓存"""
    update_data = {key: value for key, value in kwargs.items() if value is not None}
    cache_collection.update_one(
        {"repo_name": repo},
        {"$set": update_data},
        upsert=True
    )
    logger.info(f"Progress cache updated for repo: {repo} with data: {update_data}")


def ensure_indexes():
    """确保 pull_requests 集合中 repo_name 和 number 有复合唯一索引"""
    pull_requests_collection.create_index(
        [("repo_name", 1), ("number", 1)], 
        unique=True, 
        name="unique_repo_name_number"
    )
    logger.info("Ensured unique index on pull_requests for (repo_name, number).")


def threaded_fetch(repo, base_url, collection, page_key, data_type, estimated_total=None, pr_state="all"):
    task_queue = queue.Queue()
    total_count = [0]
    completed_tasks = set()
    thread_list = []
    max_retries = 3
    max_threads = 16
    task_lock = Lock()
    rate_limit_tokens = {}

    headers = {
        'Authorization': f'token {get_next_token()}',
        'Accept': 'application/vnd.github.v3+json'
    }

    def binary_search_page():
        page_num_key = f"{data_type}_page_num"
        cache = load_progress_cache(repo)
        if cache.get(page_num_key, -1) != -1:
            logger.info(f"Using cached page_num for {data_type}: {cache[page_num_key]}")
            return cache[page_num_key]

        low, high = 1, (estimated_total // 100 + 1) if estimated_total else 2
        max_pages = 10000

        while low <= high:
            mid = (low + high) // 2
            url = f"{base_url}?page={mid}&per_page=100&state={pr_state}"
            try:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                items = response.json()

                if not items or len(items) < 100:
                    high = mid - 1
                else:
                    low = mid + 1
            except Exception as e:
                logger.error(f"Error during binary search for {data_type}: {e}")
                break

        save_progress_cache(repo, **{page_num_key: high})
        logger.info(f"Final page for {data_type} in repo {repo}: {high}")
        return high

    def update_thread_count():
        num_tasks = task_queue.qsize()
        return min(max_threads, num_tasks)

    final_page = binary_search_page()
    start_page = load_progress_cache(repo).get(page_key, 1)
    for page in range(start_page, final_page + 1):
        task_queue.put(page)

    logger.info(f"Starting threaded fetch for {data_type} in repo {repo}. Total pages: {final_page}.")

    def worker():
        while not task_queue.empty():
            try:
                page = task_queue.get(timeout=5)
                if page in completed_tasks:
                    task_queue.task_done()
                    continue

                logger.info(f"Fetching {data_type} for {repo}, page {page}, state={pr_state}.")
                retry_count = 0
                while retry_count < max_retries:
                    try:
                        token = get_next_token()
                        headers['Authorization'] = f'token {token}'
                        url = f"{base_url}?page={page}&per_page=100&state={pr_state}"
                        response = requests.get(url, headers=headers, timeout=10)

                        if response.status_code == 403:
                            reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                            wait_time = reset_time - int(time.time())
                            logger.warning(f"Token {token} hit rate limit. Waiting {wait_time} seconds.")
                            time.sleep(wait_time + 1)
                            retry_count += 1
                            continue

                        response.raise_for_status()
                        items = response.json()

                        if not items:
                            logger.info(f"No more PRs for {repo}, page {page}, state={pr_state}.")
                            break

                        for item in items:
                            item['repo_name'] = repo
                            try:
                                collection.insert_one(item)  # 插入数据，若违反唯一约束则报错
                            except Exception as e:
                                if "E11000 duplicate key error" in str(e):
                                    logger.info(
                                        f"PR {item['number']} in {repo} already exists, skipping. "
                                        f"Duplicate details: {e.details}"
                                    )
                                else:
                                    logger.error(f"Error inserting PR {item['number']} in {repo}: {e}")
                                continue

                        with cache_lock:
                            total_count[0] += len(items)

                        logger.info(f"Fetched {len(items)} PRs for {repo}, page {page}, state={pr_state}.")
                        save_progress_cache(repo, **{page_key: page})
                        break
                    except requests.exceptions.Timeout:
                        retry_count += 1
                        logger.warning(f"Timeout occurred on page {page}, retry {retry_count}.")
                    except Exception as e:
                        logger.error(f"Error on page {page} for {data_type}: {e}")
                        break

                completed_tasks.add(page)
                task_queue.task_done()
            except queue.Empty:
                logger.info(f"Task queue is empty for {data_type} in repo {repo}.")
                break

    for _ in range(update_thread_count()):
        t = Thread(target=worker)
        t.start()
        thread_list.append(t)

    task_queue.join()
    for t in thread_list:
        t.join()

    if pr_state == "all":
        save_progress_cache(repo, pr_status_all=1)  # 标记 state=all 爬取完成
    logger.info(f"Finished fetching PRs for {repo}, state={pr_state}. Total fetched: {total_count[0]}")
    return total_count[0]


def main():
    # 确保复合索引已创建
    ensure_indexes()

    sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
    project_names = sample_projects[['name', 'commits', 'totalPullRequests']].to_dict(orient='records')

    for project in project_names:
        repo = project['name']
        estimated_prs = project.get('totalPullRequests', 0)
        logger.info(f"Processing {repo}, estimated PRs: {estimated_prs}.")

        cache = load_progress_cache(repo)

        if cache.get("pr_status_all", 0) == 1:
            logger.info(f"Skipping {repo}, PRs with state=all already fetched.")
            continue

        base_url = f"https://api.github.com/repos/{repo}/pulls"
        threaded_fetch(repo, base_url, pull_requests_collection, "pull_request", "pull_request", estimated_prs, pr_state="all")


if __name__ == "__main__":
    main()
