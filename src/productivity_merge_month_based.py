import os
import pandas as pd
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("merge_csv_files.log", mode="w", encoding="utf-8"),
    ],
)

def merge_csv_files(input_directory, output_file):
    """
    Merge all CSV files in a specified directory into one total CSV file.

    Args:
        input_directory (str): The directory containing CSV files to merge.
        output_file (str): The path for the merged output CSV file.

    Returns:
        None
    """
    logging.info(f"Starting to merge CSV files in directory: {input_directory}")

    # Initialize an empty list to store DataFrames
    dataframes = []

    # Loop through all files in the directory
    for file_name in os.listdir(input_directory):
        # Check if the file is a CSV
        if file_name.endswith(".csv"):
            file_path = os.path.join(input_directory, file_name)
            try:
                # Read the CSV file into a DataFrame
                logging.info(f"Reading file: {file_name}")
                df = pd.read_csv(file_path)

                # Add a column to identify the source file (optional)
                df["source_file"] = file_name

                # Append the DataFrame to the list
                dataframes.append(df)
            except Exception as e:
                logging.error(f"Failed to read file {file_name}: {e}")
                continue

    # Concatenate all DataFrames into one
    if dataframes:
        logging.info("Merging DataFrames.")
        merged_df = pd.concat(dataframes, ignore_index=True)

        # Save the merged DataFrame to a single CSV file
        try:
            logging.info(f"Saving merged CSV to: {output_file}")
            merged_df.to_csv(output_file, index=False, encoding="utf-8")
            logging.info("Merged CSV file saved successfully.")
        except Exception as e:
            logging.error(f"Failed to save merged CSV file: {e}")
    else:
        logging.warning("No CSV files found to merge.")

if __name__ == "__main__":
    # Specify the directory containing the generated CSV files
    input_directory = os.path.join("..", "result", "productivity", "month_based")

    # Specify the output file for the merged CSV
    output_file = os.path.join("..", "result", "productivity", "merged_pr_productivity_metrics.csv")

    # Call the merge function
    merge_csv_files(input_directory, output_file)
