# Import required libraries
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import os
from statsmodels.formula.api import ols

# Create directories for saving outputs if they don't exist
os.makedirs('../result/DiD_analysis/outputs', exist_ok=True)
os.makedirs('../result/DiD_analysis/plots', exist_ok=True)

# Configure logging
logging.basicConfig(
    filename='../result/DiD_analysis/outputs/analysis.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)


def load_data(attritions_path, productivity_path):
    logging.info("Loading datasets...")
    attritions = pd.read_csv(attritions_path)
    productivity = pd.read_csv(productivity_path)
    logging.info(f"Attritions dataset loaded with {attritions.shape[0]} records.")
    logging.info(f"Productivity dataset loaded with {productivity.shape[0]} records.")
    # Save loaded data
    attritions.to_csv('../result/DiD_analysis/outputs/attritions_loaded.csv', index=False)
    productivity.to_csv('../result/DiD_analysis/outputs/productivity_loaded.csv', index=False)
    return attritions, productivity


def preprocess_dates(attritions, productivity):
    logging.info("Converting date columns to datetime format...")
    attritions['attribution_date'] = pd.to_datetime(attritions['attribution_date'])
    productivity['Date'] = pd.to_datetime(productivity['Date'])
    # Save after date conversion
    attritions.to_csv('../result/DiD_analysis/outputs/attritions_dates_converted.csv', index=False)
    productivity.to_csv('../result/DiD_analysis/outputs/productivity_dates_converted.csv', index=False)
    logging.info("Date conversion completed.")
    return attritions, productivity


def group_attrition_events(attritions, productivity):
    logging.info("Grouping attrition events by repository...")
    attritions_grouped = attritions.groupby('repo_name')['attribution_date'].apply(list).reset_index()
    merged_data = pd.merge(productivity, attritions_grouped, on='repo_name', how='left')
    merged_data['attribution_date'] = merged_data['attribution_date'].apply(lambda x: x if isinstance(x, list) else [])
    # Save grouped data
    merged_data.to_csv('../result/DiD_analysis/outputs/merged_data_grouped.csv', index=False)
    logging.info("Grouping of attrition events completed.")
    return merged_data


def assign_multi_event_window(row, time_window):
    if not row['attribution_date']:
        return 'No Event'
    distances = [(row['Date'] - pd.to_datetime(event)).days // 30 for event in row['attribution_date']]
    if any(-time_window <= dist <= 0 for dist in distances):
        return 'Pre-Event'
    elif any(0 < dist <= time_window for dist in distances):
        return 'Post-Event'
    return 'No Effect'


def assign_time_windows(merged_data, time_windows_months):
    logging.info("Assigning time windows for multi-event attrition data...")
    for window in time_windows_months:
        window_col = f'window_{window}'
        merged_data[window_col] = merged_data.apply(
            lambda row: assign_multi_event_window(row, window), axis=1
        )
        # Save each window assignment
        merged_data[['repo_name', 'Date', 'window_' + str(window)]].to_csv(
            f'../result/DiD_analysis/outputs/assigned_window_{window}m.csv', index=False
        )
        logging.info(f"Time window {window} months assigned and saved.")
    return merged_data


def create_treatment_indicators(merged_data):
    logging.info("Creating treatment and post-treatment indicators...")
    # Treated: 1 if repository has at least one attrition event
    merged_data['treated'] = merged_data['attribution_date'].apply(lambda x: 1 if len(x) >= 1 else 0)

    # Post-treatment: 1 if the date is after the first attrition event
    merged_data['first_attrition_date'] = merged_data['attribution_date'].apply(
        lambda x: min(x) if len(x) >= 1 else pd.NaT
    )
    merged_data['post_treatment'] = (merged_data['Date'] > merged_data['first_attrition_date']).astype(int)

    # Interaction term
    merged_data['DiD'] = merged_data['treated'] * merged_data['post_treatment']

    # Save indicators
    merged_data.to_csv('../result/DiD_analysis/outputs/merged_data_with_indicators.csv', index=False)
    logging.info("Treatment indicators created and saved.")
    return merged_data


def confirm_sample_periods(merged_data, time_windows_months):
    logging.info("Confirming sample numbers and periods...")
    total_time_days = (merged_data['Date'].max() - merged_data['Date'].min()).days
    periods = {window: total_time_days // (window * 30) for window in time_windows_months}
    for window, period in periods.items():
        logging.info(f"Time window: {window} months => Number of periods: {period}")
        # Save periods info
    periods_df = pd.DataFrame(list(periods.items()), columns=['Time Window (Months)', 'Number of Periods'])
    periods_df.to_csv('../result/DiD_analysis/outputs/periods_info.csv', index=False)
    logging.info("Sample numbers and periods confirmed and saved.")
    return periods


def group_repositories(merged_data):
    logging.info("Grouping repositories based on number of attrition events...")
    merged_data['attrition_count'] = merged_data['attribution_date'].apply(len)
    groups = merged_data['attrition_count'].value_counts().sort_index()
    logging.info(f"Attrition groups:\n{groups}")
    # Save groups information
    groups.to_csv('../result/DiD_analysis/outputs/attrition_groups.csv', header=['Count'])
    return merged_data


def separate_attrition_groups(merged_data):
    logging.info("Separating repositories with 1 and multiple attrition events...")
    single_attrition = merged_data[merged_data['attrition_count'] == 1]
    multiple_attrition = merged_data[merged_data['attrition_count'] > 1]
    # Save separated groups
    single_attrition.to_csv('../result/DiD_analysis/outputs/single_attrition.csv', index=False)
    multiple_attrition.to_csv('../result/DiD_analysis/outputs/multiple_attrition.csv', index=False)
    logging.info(
        f"Separated into {single_attrition.shape[0]} single attrition and {multiple_attrition.shape[0]} multiple attrition repositories.")
    return single_attrition, multiple_attrition


def perform_DiD_analysis(merged_data, time_windows_months, metrics):
    logging.info("Starting Difference-in-Differences analysis...")
    did_results = []
    for metric in metrics:
        logging.info(f"Analyzing metric: {metric}")
        for window in time_windows_months:
            logging.info(f"Analyzing time window: {window} months...")
            window_col = f'window_{window}'
            model_data = merged_data[merged_data[window_col] != 'No Effect'].dropna(
                subset=[metric, 'treated', 'post_treatment', 'DiD'])

            # Confirm sample sizes
            treated_count = model_data['treated'].sum()
            control_count = model_data.shape[0] - treated_count
            logging.info(f"Time Window {window}m: Treated={treated_count}, Control={control_count}")
            window_summary = model_data[[window_col]].value_counts().reset_index(name='Count')
            window_summary.to_csv(f'../result/DiD_analysis/outputs/window_{window}m_summary.csv', index=False)

            # Fit OLS model
            formula = f'{metric} ~ treated + post_treatment + DiD'
            model = ols(formula, data=model_data).fit()
            logging.info(f"Model summary for {metric} with {window}m window:\n{model.summary()}")

            # Save model summary
            with open(f'../result/DiD_analysis/outputs/model_summary_{metric}_{window}m.txt', 'w') as f:
                f.write(model.summary().as_text())

            # Collect results
            did_results.append({
                'Metric': metric,
                'Time Window (Months)': window,
                'Coefficient_treated': model.params.get('treated', None),
                'Coefficient_post_treatment': model.params.get('post_treatment', None),
                'Coefficient_DiD': model.params.get('DiD', None),
                'p-value_DiD': model.pvalues.get('DiD', None),
                'R-squared': model.rsquared
            })

            # **优化绘图部分**
            # 创建一个新的列 'Group'，将 'treated' 转换为 'Control' 和 'Treated'
            trend_data = model_data.groupby(['Date', 'treated'])[metric].mean().reset_index()
            trend_data['Group'] = trend_data['treated'].map({0: 'Control', 1: 'Treated'})

            plt.figure(figsize=(10, 6))
            sns.lineplot(data=trend_data, x='Date', y=metric, hue='Group', marker='o',
                         palette={'Control': 'blue', 'Treated': 'orange'})
            plt.title(f'DiD Result: {metric} ({window}-Month Window)')
            plt.ylabel(f'Average {metric}')
            plt.xlabel('Date')
            plt.legend(title='Group')  # Seaborn 自动生成图例，无需手动指定 labels
            plt.grid(axis='y')
            plt.tight_layout()
            plot_path = f'../result/DiD_analysis/plots/DiD_{metric}_{window}m.png'
            plt.savefig(plot_path)
            plt.close()
            logging.info(f"DiD plot saved to {plot_path}")
    # Save all DiD results
    did_results_df = pd.DataFrame(did_results)
    did_results_df.to_csv('../result/DiD_analysis/outputs/DiD_results_summary.csv', index=False)
    logging.info("Difference-in-Differences analysis completed and results saved.")
    return did_results_df


def parallel_trends_test(merged_data, metrics):
    logging.info("Starting Parallel Trends Test...")
    for metric in metrics:
        logging.info(f"Testing parallel trends for metric: {metric}")
        # Filter pre-treatment data
        pre_treatment_data = merged_data[
            (merged_data['treated'] == 1) &
            (merged_data['Date'] <= merged_data['first_attrition_date'])
            ]
        pre_trends = pre_treatment_data.groupby(['Date', 'treated'])[metric].mean().reset_index()
        pre_trends['Group'] = pre_trends['treated'].map({0: 'Control', 1: 'Treated'})

        plt.figure(figsize=(10, 6))
        sns.lineplot(data=pre_trends, x='Date', y=metric, hue='Group', marker='o',
                     palette={'Control': 'blue', 'Treated': 'orange'})
        plt.title(f'Parallel Trends Test: {metric}')
        plt.ylabel(f'Average {metric}')
        plt.xlabel('Date')
        plt.legend(title='Group')  # Seaborn 自动生成图例，无需手动指定 labels
        plt.grid(axis='y')
        plt.tight_layout()
        plot_path = f'../result/DiD_analysis/plots/Parallel_Trends_{metric}.png'
        plt.savefig(plot_path)
        plt.close()
        logging.info(f"Parallel trends plot for {metric} saved to {plot_path}")
    logging.info("Parallel Trends Test completed.")
    return


# def parallel_trends_test(merged_data, metrics):
#     logging.info("Starting Parallel Trends Test...")
#     for metric in metrics:
#         logging.info(f"Testing parallel trends for metric: {metric}")
#         # Filter pre-treatment data
#         pre_treatment_data = merged_data[
#             (merged_data['treated'] == 1) &
#             (merged_data['Date'] <= merged_data['first_attrition_date'])
#             ]
#         pre_trends = pre_treatment_data.groupby(['Date', 'treated'])[metric].mean().reset_index()
#
#         # Plot parallel trends
#         plt.figure(figsize=(10, 6))
#         sns.lineplot(data=pre_trends, x='Date', y=metric, hue='treated', marker='o')
#         plt.title(f'Parallel Trends Test: {metric}')
#         plt.ylabel(f'Average {metric}')
#         plt.xlabel('Date')
#         plt.legend(title='Group', labels=['Control', 'Treated'])
#         plt.grid(axis='y')
#         plt.tight_layout()
#         plot_path = f'../result/DiD_analysis/plots/Parallel_Trends_{metric}.png'
#         plt.savefig(plot_path)
#         plt.close()
#         logging.info(f"Parallel trends plot for {metric} saved to {plot_path}")
#     logging.info("Parallel Trends Test completed.")
#     return


def main():
    # Paths to datasets
    attritions_path = '../result/attritions.csv'
    productivity_path = '../result/productivity/merged_pr_productivity_metrics.csv'

    # Load data
    attritions, productivity = load_data(attritions_path, productivity_path)

    # Preprocess dates
    attritions, productivity = preprocess_dates(attritions, productivity)

    # Group attrition events
    merged_data = group_attrition_events(attritions, productivity)

    # Define time windows in months
    time_windows_months = [3, 6, 12]

    # Assign time windows
    merged_data = assign_time_windows(merged_data, time_windows_months)

    # Create treatment indicators
    merged_data = create_treatment_indicators(merged_data)

    # Confirm sample numbers and periods
    periods = confirm_sample_periods(merged_data, time_windows_months)

    # Group repositories based on attrition count
    merged_data = group_repositories(merged_data)

    # Separate repositories with single and multiple attrition events
    single_attrition, multiple_attrition = separate_attrition_groups(merged_data)

    # Define metrics to analyze
    metrics = ['ACCEPT_RATE', 'AVG_TIME_TO_MERGE', 'AVG_TIME_TO_REACT']

    # Perform DiD analysis
    did_results = perform_DiD_analysis(merged_data, time_windows_months, metrics)

    # Perform Parallel Trends Test
    parallel_trends_test(merged_data, metrics)

    logging.info("All analyses completed successfully.")


if __name__ == "__main__":
    main()
