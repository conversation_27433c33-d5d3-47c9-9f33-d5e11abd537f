import logging
import pandas as pd
from pymongo import MongoClient
import os

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/generate_csv_pr.log", mode="w", encoding="utf-8"),
    ],
)

WINDOWS_IP = "localhost"
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")

db = client["disengagement"]
pr_collection = db["pull_requests"]

def fetch_pr_and_generate_csv(repo_name, output_csv_path):
    """
    Fetch pull requests for a specific repo from MongoDB and generate a CSV file.

    Parameters:
        repo_name (str): The repository name to filter pull requests.
        output_csv_path (str): Path to the output CSV file.
    """
    logging.info(f"Fetching pull requests for repository: {repo_name}")

    # Query MongoDB for pull requests
    try:
        prs = list(pr_collection.find({"repo_name": repo_name}))
    except Exception as e:
        logging.error(f"Error querying pull requests for {repo_name}: {e}")
        return

    if not prs:
        logging.warning(f"No pull requests found for repository: {repo_name}")
        return

    # Convert pull requests to a DataFrame
    logging.info(f"Found {len(prs)} pull requests for repository: {repo_name}")

    # Extract needed fields
    rows = []
    for pr in prs:
        pr_user = pr.get("user") or {}
        requested_reviewers = pr.get("requested_reviewers") or []
        requested_reviewer_logins = [reviewer.get("login") for reviewer in requested_reviewers] if requested_reviewers else []
        rows.append({
            "number": pr.get("number"),
            "title": pr.get("title"),
            "body": pr.get("body"),
            "state": pr.get("state"),
            "created_at": pr.get("created_at") or "",
            "closed_at": pr.get("closed_at") or "",
            "merged_at": pr.get("merged_at") or "",
            "pr_author_login": pr_user.get("login") or "",
            "pr_requested_reviewer_logins": ", ".join(requested_reviewer_logins) if requested_reviewer_logins else "",
            "repo_name": pr.get("repo_name"),
        })

    # Save the DataFrame as a CSV file
    df = pd.DataFrame(rows)
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
        df.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"Pull requests CSV generated at {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to save pull requests CSV: {e}")

def main():
    # Fetch pull requests for all repositories
    repos = list(pr_collection.distinct("repo_name"))
    if not repos:
        logging.warning("No repositories found in the collection.")
        return

    for repo in repos:
        # replace "/" with "_"
        output_csv_path = os.path.join("../data/pullrequests", f"{repo.replace('/', '_')}_pull_requests.csv")
        fetch_pr_and_generate_csv(repo, output_csv_path)

    # 处理 sample_projects_quartiles.csv 中的仓库
    try:
        sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
        sample_repo_name = sample_projects["name"].tolist()
        diff_repo = list(set(sample_repo_name) - set(repos))
    except Exception as e:
        logging.error(f"Error reading sample_projects_quartiles.csv: {e}")
        return

    # generate empty csv file names with diff_repo
    for repo in diff_repo:
        output_csv_path = os.path.join("../data/pullrequests", f"{repo.replace('/', '_')}_pull_requests.csv")
        df = pd.DataFrame()
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
            df.to_csv(output_csv_path, index=False, encoding="utf-8")
            logging.info(f"Empty pull requests CSV generated at {output_csv_path}")
        except Exception as e:
            logging.error(f"Failed to save empty pull requests CSV for {repo}: {e}")
    logging.info("All pull requests CSVs generated.")

if __name__ == "__main__":
    main()
