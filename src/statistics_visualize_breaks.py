import os
import pandas as pd
import matplotlib.pyplot as plt
import logging


# Configure logging
log_file = '../logs/breaks_analysis.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def ensure_directory_exists(directory):
    """
    Ensure the specified directory exists, creating it if necessary.
    """
    if not os.path.exists(directory):
        os.makedirs(directory)

def analyze_and_export_break_statistics():
    """
    Analyze break frequency and average duration for each repository, and generate a CSV file.
    """
    input_csv_path = "../result/breaks.csv"
    output_csv_path = "../result/break_statistics.csv"

    # Ensure the output directory exists
    ensure_directory_exists("../result/")

    # Load breaks data from the CSV
    try:
        df = pd.read_csv(input_csv_path)
    except Exception as e:
        logging.error(f"Failed to load breaks CSV: {e}")
        return None

    if df.empty:
        logging.warning("No data found in the breaks CSV.")
        return None

    # Calculate break counts and average durations per repository
    break_statistics = (
        df.groupby("repo_name")
        .agg(
            break_count=("duration", "size"),
            avg_duration=("duration", "mean")
        )
        .reset_index()
        .sort_values(by="break_count", ascending=False)
    )

    # Print summary statistics
    logging.info("Break Statistics Summary:")
    logging.info(break_statistics.describe())

    # Export to CSV
    try:
        break_statistics.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"Break statistics CSV generated at {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to save break statistics CSV: {e}")

    # Return the statistics DataFrame for further analysis
    return break_statistics

def plot_break_statistics(break_statistics):
    """
    Plot distributions for break frequency and average duration across repositories,
    using revised bin ranges based on prior data analysis.
    """
    # Ensure the output directory exists
    ensure_directory_exists("../result/figures/")

    total_repos = 3999  # Total number of repositories

    # Plot 1: Break frequency distribution with revised bins
    plt.figure(figsize=(10, 6))
    bins = [1, 5, 10, 30, 100, 500, float('inf')]
    labels = ["1-5", "6-10", "11-30", "31-100", "101-500", "501+"]
    break_statistics["count_range"] = pd.cut(
        break_statistics["break_count"], bins=bins, labels=labels, right=False
    )
    frequency_distribution = break_statistics["count_range"].value_counts().sort_index()

    # Calculate "no breaks" value
    no_breaks = total_repos - frequency_distribution.sum()
    frequency_distribution.loc["no breaks"] = no_breaks

    # Plot frequency distribution
    bars = plt.bar(frequency_distribution.index, frequency_distribution.values, color="skyblue", edgecolor="black")

    # Add annotations for count and percentage
    for bar, count in zip(bars, frequency_distribution.values):
        percentage = f"{(count / total_repos * 100):.1f}%"
        plt.text(
            bar.get_x() + bar.get_width() / 2,
            bar.get_height() + 1,
            f"{count}\n({percentage})",
            ha="center", fontsize=12
        )

    plt.xlabel("Break Frequency Range", fontsize=14)
    plt.ylabel("Number of Repositories", fontsize=14)
    plt.title("Break Frequency Distribution", fontsize=16)
    plt.tight_layout()
    frequency_output_path = "../result/figures/break_frequency_distribution.pdf"
    plt.savefig(frequency_output_path)
    logging.info(f"Break frequency distribution plot saved at {frequency_output_path}")
    plt.show()

    # Plot 2: Break average duration distribution with revised bins
    plt.figure(figsize=(10, 6))
    bins = [0, 50, 100, 200, 500, 1000, float('inf')]
    labels = ["1-50", "51-100", "101-200", "201-500", "501-1000", "1001+"]
    break_statistics["duration_range"] = pd.cut(
        break_statistics["avg_duration"], bins=bins, labels=labels, right=False
    )
    duration_distribution = break_statistics["duration_range"].value_counts().sort_index()
    duration_distribution.loc['no breaks'] = total_repos - duration_distribution.sum()
    # Plot duration distribution
    bars = plt.bar(duration_distribution.index, duration_distribution.values, color="orange", edgecolor="black")

    # Add annotations for count and percentage
    for bar, count in zip(bars, duration_distribution.values):
        percentage = f"{(count / total_repos * 100):.1f}%"
        plt.text(
            bar.get_x() + bar.get_width() / 2,
            bar.get_height() + 1,
            f"{count}\n({percentage})",
            ha="center", fontsize=12
        )

    plt.xlabel("Average Break Duration Range (Days)", fontsize=14)
    plt.ylabel("Number of Repositories", fontsize=14)
    plt.title("Break Average Duration Distribution", fontsize=16)
    plt.tight_layout()
    duration_output_path = "../result/figures/break_average_duration_distribution.pdf"
    plt.savefig(duration_output_path)
    logging.info(f"Break average duration distribution plot saved at {duration_output_path}")
    plt.show()

    # Save summary statistics as CSV
    summary_output_path = "../result/break_summary_statistics.csv"
    summary_stats = break_statistics.describe()
    try:
        summary_stats.to_csv(summary_output_path)
        logging.info(f"Break summary statistics saved at {summary_output_path}")
    except Exception as e:
        logging.error(f"Failed to save break summary statistics CSV: {e}")

def main():
    # Analyze break statistics and export CSV
    break_statistics = analyze_and_export_break_statistics()

    if break_statistics is not None:
        # Generate and save distribution plots
        plot_break_statistics(break_statistics)

if __name__ == "__main__":
    main()
