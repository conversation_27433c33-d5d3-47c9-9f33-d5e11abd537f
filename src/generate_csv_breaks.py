import logging
import pandas as pd
from pymongo import MongoClient

# Configure logging
log_file = '../logs/breaks_extraction.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def extract_breaks_and_generate_csv():
    """
    Extracts breaks from the existing MongoDB collection and generates a CSV file.
    """
    # Connect to MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["repo_breaks_attritions"]

    logging.info("Fetching breaks data from MongoDB...")

    # Fetch all documents from the collection
    repos = list(collection.find({}))

    if not repos:
        logging.warning("No breaks data found in the collection.")
        return

    # Extract breaks data for the CSV
    rows = []
    for repo in repos:
        repo_name = repo.get("repo_name")
        breaks = repo.get("breaks", [])
        for brk in breaks:
            rows.append({
                "repo_name": repo_name,
                "start_time": brk.get("start_time"),
                "end_time": brk.get("end_time"),
                "duration": brk.get("duration"),
                "dev_login": brk.get("dev_login")
            })

    # Convert to DataFrame and save as CSV
    df = pd.DataFrame(rows)
    output_csv_path = "../result/breaks.csv"
    try:
        df.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"Breaks CSV generated at {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to generate breaks CSV: {e}")

if __name__ == "__main__":
    extract_breaks_and_generate_csv()
