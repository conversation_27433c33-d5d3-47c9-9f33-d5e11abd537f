import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.formula.api as smf
from linearmodels.panel import PanelOLS
import matplotlib.pyplot as plt
import seaborn as sns
import logging

# Set up logging
logging.basicConfig(
    filename='../result/did_analysis.log',  # Log file
    level=logging.INFO,  # Log level
    format='%(asctime)s - %(levelname)s - %(message)s'  # Log format
)

# Load the data
logging.info("Loading data...")
did_data = pd.read_csv("../result/did_analysis_data.csv")
logging.info("Data loaded successfully.")

# Ensure correct data types for time and repo_name
did_data['time'] = pd.to_datetime(did_data['time'], format='%Y-%m-%d')
did_data['repo_name'] = did_data['repo_name'].astype('category')

# Create interaction term
did_data['treated_cumulative'] = did_data['cumulative_attrition'] * did_data['ever_attrited']


# Function to run DID analysis with a variable Y (dependent variable)
def run_did_analysis(Y):
    """
    Perform DID analysis, where Y is the dependent variable (e.g., NUM_MERGED_PR, ACCEPT_RATE, etc.)
    """
    logging.info(f"Running DID analysis for {Y}...")

    # 2. DID Model Estimation: Fixed effects regression
    logging.info(f"Starting regression for {Y}...")
    did_model = smf.ols(
        formula=f'{Y} ~ treated_cumulative + C(time) + C(repo_name) + cumulative_attrition + ever_attrited',
        data=did_data
    ).fit()
    logging.info(f"DID regression for {Y} completed.")

    # Output DID model results
    with open(f'../result/did_model_summary_{Y}.txt', 'w') as f:
        f.write(did_model.summary().as_text())  # Save regression results to file
    logging.info(f"Saved DID model results for {Y}.")

    # 3. Dynamic Effect Estimation: Create dummy variables for each time window
    event_time_vars = [f'event_time_{k}' for k in range(-6, 7) if k != 0]
    event_time_formula = ' + '.join(event_time_vars)

    # Dynamic effect model
    logging.info(f"Starting dynamic effect estimation for {Y}...")
    dynamic_formula = f"{Y} ~ {event_time_formula} + C(time) + C(repo_name)"
    dynamic_model = smf.ols(formula=dynamic_formula, data=did_data).fit()
    logging.info(f"Dynamic effect estimation for {Y} completed.")

    # Output dynamic effect model results
    with open(f'../result/dynamic_model_summary_{Y}.txt', 'w') as f:
        f.write(dynamic_model.summary().as_text())  # Save dynamic effect model results to file
    logging.info(f"Saved dynamic model results for {Y}.")

    # 4. Parallel Trends Test: Check interaction term
    logging.info(f"Running parallel trends test for {Y}...")
    did_model_parallel_trends = smf.ols(
        formula=f'{Y} ~ treated_cumulative * C(time) + C(repo_name) + cumulative_attrition + ever_attrited',
        data=did_data
    ).fit()
    logging.info(f"Parallel trends test for {Y} completed.")

    # Output parallel trends test results
    with open(f'../result/did_model_parallel_trends_summary_{Y}.txt', 'w') as f:
        f.write(did_model_parallel_trends.summary().as_text())  # Save parallel trends test results to file
    logging.info(f"Saved parallel trends test results for {Y}.")

    # 5. Fixed Effects (Panel Data): Using PanelOLS
    logging.info(f"Running panel data model for {Y}...")
    did_data_panel = did_data.set_index(['repo_name', 'time'])
    panel_model = PanelOLS.from_formula(
        f'{Y} ~ treated_cumulative + cumulative_attrition + ever_attrited + EntityEffect + TimeEffect',
        data=did_data_panel)
    panel_model_res = panel_model.fit()
    logging.info(f"Panel data model for {Y} completed.")

    # Output panel data model results
    with open(f'../result/panel_model_summary_{Y}.txt', 'w') as f:
        f.write(panel_model_res.summary().as_text())  # Save panel model results to file
    logging.info(f"Saved panel model results for {Y}.")

    # 6. Heteroscedasticity Test
    logging.info(f"Running heteroscedasticity test for {Y}...")
    from statsmodels.stats.diagnostic import het_breuschpagan
    from statsmodels.tools.tools import add_constant

    # Calculate residuals
    X = add_constant(did_data[['treated_cumulative', 'cumulative_attrition', 'ever_attrited']])
    model = sm.OLS(did_data[Y], X).fit()
    residuals = model.resid

    # Breusch-Pagan test
    bp_test = het_breuschpagan(residuals, X)

    # Output heteroscedasticity test results
    with open(f'../result/breusch_pagan_test_result_{Y}.txt', 'w') as f:
        f.write(f"Breusch-Pagan Test: {bp_test}\n")  # Save heteroscedasticity test results to file
    logging.info(f"Saved heteroscedasticity test results for {Y}.")

    # Output regression coefficients, t-values, and p-values
    regression_results = model.summary()

    # Save regression results to file
    with open(f'../result/OLS_regression_results_{Y}.txt', 'w') as f:
        f.write(regression_results.as_text())
    logging.info(f"Saved OLS regression results for {Y}.")

    logging.info(f"All intermediate results and regression outputs have been saved to '../result' folder ({Y})")


# Run analysis for different dependent variables (Y)
logging.info("Starting analysis for NUM_MERGED_PR...")
run_did_analysis("NUM_MERGED_PR")  # Example: Analyze NUM_MERGED_PR
logging.info("Completed analysis for NUM_MERGED_PR.")

logging.info("Starting analysis for ACCEPT_RATE...")
run_did_analysis("ACCEPT_RATE")  # Example: Analyze ACCEPT_RATE
logging.info("Completed analysis for ACCEPT_RATE.")

logging.info("Starting analysis for AVG_TIME_TO_REACT...")
run_did_analysis("AVG_TIME_TO_REACT")  # Example: Analyze AVG_TIME_TO_REACT
logging.info("Completed analysis for AVG_TIME_TO_REACT.")

logging.info("Starting analysis for AVG_TIME_TO_MERGE...")
run_did_analysis("AVG_TIME_TO_MERGE")  # Example: Analyze AVG_TIME_TO_MERGE
logging.info("Completed analysis for AVG_TIME_TO_MERGE.")


# Parallel Trends Test Visualization: Only for Parallel Trends
def plot_parallel_trends():
    """
    Visualize parallel trends for the treated and control groups for different Y variables.
    """
    # List of dependent variables to analyze
    y_variables = ['NUM_MERGED_PR', 'ACCEPT_RATE', 'AVG_TIME_TO_REACT', 'AVG_TIME_TO_MERGE']

    for Y in y_variables:
        logging.info(f"Plotting parallel trends for {Y}...")
        # For parallel trends visualization, we select data around the event window
        window = 6  # months before and after attrition
        parallel_trends_data = did_data[did_data['time'].dt.month.isin(range(-window, window + 1))]

        # Plot the trends for the treated and control groups
        plt.figure(figsize=(10, 6))
        sns.lineplot(data=parallel_trends_data, x='time', y=Y, hue='ever_attrited', errorbar=None)
        plt.title(f"Parallel Trends for Treated and Control Groups ({Y})")
        plt.xlabel('Time')
        plt.ylabel(Y)
        plt.savefig(f'../result/parallel_trend_plot_{Y}.png')  # Save the plot to a file
        plt.show()
        logging.info(f"Saved parallel trends plot for {Y}.")


# Run the Parallel Trends Visualization for all Y variables
logging.info("Starting parallel trends visualization...")
plot_parallel_trends()
logging.info("Completed parallel trends visualization.")
