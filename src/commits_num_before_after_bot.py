# write a code to count the commits before and after excluding the bot developers
import pandas as pd
def get_commit_file_repo_name(repo_name):
    file_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    repo_commit = pd.read_csv(file_path)
    # Exclude merge commits with those who have two values in the columns named 'parent_shas'
    repo_commit = repo_commit[repo_commit['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)
    # Exclude bot accounts
    # bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')
    # bot_developers = bot_developers['bot_name'].tolist()
    # repo_commit = repo_commit[~repo_commit['author_login'].isin(bot_developers)].reset_index(drop=True)
    return repo_commit

#
sample_projects = pd.read_csv("../data/sample_projects_quartiles.csv")
project_names = sample_projects["name"].tolist()

Total_num_commits = 0
Total_num_commits_after = 0
bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')
bot_developers = bot_developers['bot_name'].tolist()
for project in project_names:
    try:
        commits = get_commit_file_repo_name(project)
    except Exception as e:
        print(f"Error in {project}: {e}")
        continue
    Total_num_commits += len(commits)
    commits = commits[~commits['author_login'].isin(bot_developers)].reset_index(drop=True)
    Total_num_commits_after += len(commits)

print(Total_num_commits, Total_num_commits_after, Total_num_commits - Total_num_commits_after, (Total_num_commits - Total_num_commits_after) / Total_num_commits)