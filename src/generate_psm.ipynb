{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:48:49.094447Z", "start_time": "2025-01-13T07:48:48.262273Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import logging\n", "from sklearn.preprocessing import MinMaxScaler, StandardScaler\n", "log_dir = \"../logs\"\n", "os.makedirs(log_dir, exist_ok=True)\n", "\n", "logging.basicConfig(\n", "  level=logging.INFO,\n", "  # add filename and code line number, time, log level, message\n", "  format=\"%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s\",\n", "  handlers=[\n", "    logging.FileHandler(os.path.join(log_dir, \"psm.log\")),\n", "    logging.StreamHandler(),\n", "  ],\n", ")\n", "\n", "output_dir = \"../result/standardized_productivity/\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "def save_data(data, filename):\n", "  filepath = os.path.join(output_dir, filename)\n", "  data.to_csv(filepath, index=False)\n", "  logging.info(f\"Saved {filepath}\")\n", "  \n", "def sigmod(x):\n", "  \"\"\"Sigmod function\"\"\"\n", "  return 1 / (1 + np.exp(-x))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:52:46.168204Z", "start_time": "2025-01-13T07:52:46.155654Z"}}, "outputs": [], "source": ["from sklearn.neighbors import NearestNeighbors\n", "def match_all_treatments_psm(\n", "    treatment_repos_with_attrition_date,\n", "    all_repo_names,\n", "    productivity,\n", "    n_neighbors,\n", "    timewindow_weeks,\n", "    feature_columns=[\"feature_relative_increase\", \"feature_magnitude\"]\n", "):\n", "    \"\"\"\n", "    使用两个特征进行倾向评分匹配 (PSM) 来匹配处理组和对照组仓库，并记录匹配时的时间点。\n", "\n", "    参数:\n", "    - treatment_repos_with_attrition_date (DataFrame): 包含处理组仓库及其离职日期的数据。\n", "    - all_repo_names (list): 所有仓库的名称列表。\n", "    - productivity (DataFrame): 包含生产力指标和特征的数据。\n", "    - n_neighbors (int): 每个处理组匹配的对照组数量。\n", "    - feature_columns (list): 用于匹配的特征列名。\n", "\n", "    返回:\n", "    - matched_pairs (dict): 处理组仓库与匹配的对照组仓库及其对应时间点的字典。\n", "    \"\"\"\n", "\n", "    logging.info(\"Starting propensity score matching...\")\n", "\n", "    # 初始化可用的对照组仓库集合\n", "    treatment_repos = set(treatment_repos_with_attrition_date['repo_name'].tolist())\n", "    available_controls = set(all_repo_names)\n", "    logging.info(f\"Initial available controls: {len(available_controls)} repositories.\")\n", "\n", "    # 提取处理组特征，仅在治疗时间\n", "    logging.info(\"Extracting treatment group features at treatment times...\")\n", "    # get the treatment features at the treatment time\n", "    treatment_features_df = productivity[\n", "        (productivity['repo_name'].isin(treatment_repos)) &\n", "        (productivity['is_treated'] == 1)\n", "    ]\n", "    # for debug, save treatment_features_df to file\n", "    save_data(treatment_features_df, \"treatment_features.csv\")\n", "    # 提取对照组特征，所有时间单位，以便进行匹配\n", "    logging.info(\"Extracting control group features at all time units...\")\n", "    control_features_df = productivity[\n", "        (productivity['repo_name'].isin(available_controls)) \n", "    ]\n", "    # for debug, save control_features_df to file   \n", "    save_data(control_features_df, \"control_features.csv\")\n", "    # 检查缺失的处理组\n", "    missing_treatments = treatment_repos - set(treatment_features_df['repo_name'])\n", "    if missing_treatments:\n", "        for repo in missing_treatments:\n", "            logging.warning(f\"Treatment repo {repo} has no valid feature values at treatment time.\")\n", "        treatment_features_df = treatment_features_df[~treatment_features_df['repo_name'].isin(missing_treatments)]\n", "\n", "    logging.info(f\"Number of treatment repositories with valid features at treatment time: {len(treatment_features_df)}\")\n", "    logging.info(f\"Number of control repositories with valid features at all time units: {len(control_features_df)}\")\n", "\n", "    # 将包含 NaN 的行去除，确保特征矩阵中没有缺失值, report the number of rows removed\n", "    # report the number of rows removed\n", "    removed_treatment = len(treatment_features_df) - len(treatment_features_df.dropna(subset=feature_columns))\n", "    removed_control = len(control_features_df) - len(control_features_df.dropna(subset=feature_columns))\n", "    logging.info(f\"Removed {removed_treatment} rows with NaN values from treatment features.\")\n", "    logging.info(f\"Removed {removed_control} rows with NaN values from control features.\")\n", "    treatment_features_df = treatment_features_df.dropna(subset=feature_columns)\n", "    control_features_df = control_features_df.dropna(subset=feature_columns)\n", "\n", "    # 准备特征矩阵\n", "    # X_treatment = treatment_features_df[feature_columns].values\n", "    X_control = control_features_df[feature_columns].values\n", "    # exclude nan values\n", "    # X_treatment = X_treatment[~np.isnan(X_treatment).any(axis=1)]\n", "    # 初始化 NearestNeighbors 模型\n", "    logging.info(\"Performing nearest neighbors matching...\")\n", "    nbrs = NearestNeighbors(n_neighbors=12, algorithm='auto', metric='euclidean').fit(X_control)\n", "\n", "    # 获取匹配的对照组仓库名称和时间点\n", "    matched_pairs = {}\n", "    for index, row in treatment_features_df.iterrows():\n", "        t_repo = row['repo_name']\n", "        t_time = row['standardized_time_weeks']\n", "        # 找到最近的邻居\n", "        # if the treatment values contain nan, skip\n", "        matched_controls = []\n", "        distances, indices = nbrs.kneighbors([row[feature_columns].values])\n", "        matched_repos = []\n", "        count = 0\n", "        for idx in indices[0]:\n", "            count += 1\n", "            if count > n_neighbors:\n", "                break\n", "            # check if the repo is already matched\n", "            if control_features_df.iloc[idx]['repo_name'] in matched_repos:\n", "                logging.info(f\"Control repo {control_features_df.iloc[idx]['repo_name']} is already matched. Skipping...\")\n", "                continue\n", "                        \n", "            # check if there are 'is_treatment' == True within the timewindow weeks to avoid the control repo also under treatment\n", "            is_treatmant_within_timewindow = productivity[\n", "                (productivity['repo_name'] == control_features_df.iloc[idx]['repo_name']) &\n", "                (productivity['standardized_time_weeks'] > t_time ) &\n", "                (productivity['standardized_time_weeks'] <= t_time + timewindow_weeks) &\n", "                (productivity['is_treated'] == 1)\n", "            ].shape[0] > 0\n", "            if is_treatmant_within_timewindow:\n", "                logging.info(f\"For Treatment repo {t_repo}, Control repo {control_features_df.iloc[idx]['repo_name']} is also under treatment within {timewindow_weeks} weeks.Skipping...\")\n", "                continue     \n", "            # repo_name is not equal to the treatment repo\n", "            if control_features_df.iloc[idx]['repo_name'] == t_repo:\n", "                logging.info(f\"Control repo {control_features_df.iloc[idx]['repo_name']} is the same as the treatment repo {t_repo}. Skipping...\")\n", "                continue\n", "            control_repo = control_features_df.iloc[idx]['repo_name']\n", "            control_time = control_features_df.iloc[idx]['standardized_time_weeks']\n", "            # 如果对照时间的对照组的数据的长度，不满足大于 time_window_weeks，则跳过\n", "            \n", "            matched_controls.append({\n", "                'repo_name': control_repo,\n", "                'matched_time': control_time,\n", "                # save feature values for matched controls\n", "                'features': control_features_df.iloc[idx][feature_columns].values\n", "            })\n", "            # remove the matched control from the available controls\n", "            # logging.debug(f\"Matched control {control_repo} with time {control_time} to treatment {t_repo}.\")\n", "        \n", "        if len(matched_controls) == 0:\n", "            logging.warning(f\"No matched controls for treatment repo {t_repo}.\")\n", "            continue\n", "        matched_pairs[t_repo] = {\n", "            'treatment_time': t_time,\n", "            'controls': matched_controls,\n", "            # save the values of the features for the treatment and matched controls\n", "            'treatment_features': row[feature_columns].values,\n", "            'control_features': control_features_df.iloc[indices[0]][feature_columns].values\n", "        }\n", "\n", "    # 检查匹配结果\n", "    # for t_repo, match_info in matched_pairs.items():\n", "    #     if len(match_info['controls']) < n_neighbors:\n", "            # logging.warning(f\"Treatment repo {t_repo} has only {len(match_info['controls'])} controls, expected {n_neighbors}.\")            \n", "    logging.info(\"Propensity score matching completed.\")\n", "    logging.info(f\"Total matched pairs: {len(matched_pairs)}\")\n", "\n", "    return matched_pairs, treatment_features_df, control_features_df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:54:53.598012Z", "start_time": "2025-01-13T07:54:53.066337Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-15 23:33:34,504 [4244901864.py:7] INFO: Loading data...\n", "2025-01-15 23:33:34,999 [4244901864.py:11] INFO: Data loaded successfully.\n", "2025-01-15 23:33:35,000 [4244901864.py:21] INFO: Preprocessing data...\n", "2025-01-15 23:33:35,050 [4244901864.py:28] INFO: Data preprocessing completed.\n"]}], "source": ["if __name__ == \"__main__\":\n", "    metric_column = \"pr_throughput\"\n", "    timewindow_weeks = 12\n", "    n_neighbors = 5\n", "\n", "    # 加载数据\n", "    logging.info(\"Loading data...\")\n", "    repo_info = pd.read_csv(\"../data/sample_projects_quartiles.csv\")\n", "    attrition = pd.read_csv(\"../result/attritions.csv\")\n", "    productivity = pd.read_csv(\"../result/productivity_metrics_202501015.csv\")\n", "    logging.info(\"Data loaded successfully.\")\n", "\n", "    # # 数据完整性检查\n", "    required_columns = [\"repo_name\", \"datetime\", \"datetime\", \"pr_throughput\"]\n", "    for col in required_columns:\n", "        if col not in productivity.columns:\n", "            logging.error(f\"Missing column: {col}\")\n", "            raise ValueError(f\"Missing column: {col}\")\n", "\n", "    # # 数据预处理\n", "    logging.info(\"Preprocessing data...\")\n", "    attrition[\"attrition_date\"] = pd.to_datetime(attrition[\"attrition_date\"])\n", "    productivity[\"time\"] = pd.to_datetime(productivity[\"datetime\"])\n", "    global_min_time = productivity[\"time\"].min()\n", "    productivity[\"standardized_time_weeks\"] = (\n", "        (productivity[\"time\"] - global_min_time).dt.days // 7\n", "    ).astype(int)\n", "    logging.info(\"Data preprocessing completed.\")\n", "\n", "    # # 标记 is_treated\n", "    attrition_dict = attrition.set_index(\"repo_name\")[\"attrition_date\"].to_dict()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-14 13:09:16,031 [98661165.py:15] INFO: Calculating I_it...\n", "2025-01-14 13:09:17,612 [98661165.py:32] INFO: Normalizing features...\n", "2025-01-14 13:09:21,139 [3401252484.py:25] INFO: Saved ../result/standardized_productivity/standardized_productivity_parallel.csv\n"]}], "source": ["# 标记 is_treated\n", "def mark_is_treatment(row):\n", "    \"\"\"mark only the first attrition in the time unit\"\"\"\n", "    attrition_date = attrition_dict.get(row[\"repo_name\"], pd.NaT)\n", "    # standardize the time unit and mark the attrition time unit\n", "    if pd.isnull(attrition_date):\n", "        return 0\n", "    else:\n", "        attrition_time_unit = (attrition_date - global_min_time).days // 7\n", "        return 1 if row[\"standardized_time_weeks\"] == attrition_time_unit else 0\n", "\n", "productivity[\"is_treated\"] = productivity.apply(mark_is_treatment, axis=1)\n", "\n", "# 计算 I_it\n", "logging.info(\"Calculating I_it...\")\n", "productivity = productivity.sort_values([\"repo_name\", \"time\"])\n", "productivity[\"O_it\"] = productivity[metric_column]\n", "productivity[\"O_i_t_minus_1\"] = productivity.groupby(\"repo_name\")[\n", "    metric_column\n", "].shift(1)\n", "productivity[\"I_it\"] = np.log(\n", "    (productivity[\"O_it\"] + 1) / (productivity[\"O_i_t_minus_1\"] + 1)\n", ").<PERSON>na(0)\n", "\n", "# feature engineering with time window\n", "productivity[\"sum_I_it_last_12_weeks\"] = productivity.groupby(\"repo_name\")[\n", "    \"I_it\"\n", "].transform(lambda x: x.shift(1).rolling(window=timewindow_weeks, min_periods=timewindow_weeks).sum())\n", "productivity[\"sum_pr_throughput_last_12_weeks\"] = productivity.groupby(\"repo_name\")[\n", "    metric_column\n", "].transform(lambda x: x.shift(1).rolling(window=timewindow_weeks, min_periods=timewindow_weeks).sum())\n", "logging.info(\"Normalizing features...\")\n", "\n", "# 对长尾分布的 sum_pr_throughput_last_12_weeks 进行对数变换后归一化\n", "# scaler_minmax = MinMaxScaler()\n", "# productivity['log_sum_pr_throughput_last_12_weeks'] = np.log1p(productivity['sum_pr_throughput_last_12_weeks'])  # 对数变换\n", "# productivity['normalized_pr_throughput_last_12_weeks'] = scaler_minmax.fit_transform(productivity[['log_sum_pr_throughput_last_12_weeks']])\n", "\n", "# 对接近正态分布的 sum_I_it_last_12_weeks 进行标准化\n", "# scaler_standard = StandardScaler()\n", "# productivity['normalized_I_it_last_12_weeks'] = scaler_standard.fit_transform(productivity[['sum_I_it_last_12_weeks']])\n", "\n", "# 创建特征 feature_relative_increase 和 feature_magnitude\n", "# productivity['feature_relative_increase'] = productivity['normalized_I_it_last_12_weeks']\n", "# productivity['feature_magnitude'] = productivity['normalized_pr_throughput_last_12_weeks']\n", "# 保存标准化数据\n", "productivity['feature_relative_increase'] = productivity['sum_I_it_last_12_weeks']\n", "productivity['feature_magnitude'] = productivity['sum_pr_throughput_last_12_weeks']\n", "productivity['feature_sigmod'] = sigmod(productivity['feature_relative_increase']+productivity['feature_magnitude'])\n", "save_data(productivity, \"standardized_productivity_parallel.csv\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:54:55.879926Z", "start_time": "2025-01-13T07:54:55.838723Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-14 13:09:39,464 [2699456043.py:2] INFO: Extracting treatment repos...\n", "2025-01-14 13:09:39,493 [2699456043.py:13] INFO: Found 927 treatment repos.\n"]}], "source": ["# 提取处理组仓库\n", "logging.info(\"Extracting treatment repos...\")\n", "attrition_counts = attrition['repo_name'].value_counts().reset_index()\n", "attrition_counts.columns = ['repo_name', 'attrition_count']\n", "repos_with_one_attrition = attrition_counts[attrition_counts['attrition_count'] == 1]\n", "# repos_with_one_attrition\n", "treatment_repos_with_attrition_date = pd.merge(\n", "    repos_with_one_attrition, attrition, on=\"repo_name\", how=\"left\"\n", ")\n", "control_groups = repo_info\n", "\n", "all_repo_names = productivity[\"repo_name\"].unique()\n", "logging.info(f\"Found {len(treatment_repos_with_attrition_date)} treatment repos.\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:55:40.541362Z", "start_time": "2025-01-13T07:54:58.352679Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-14 13:12:31,886 [4153959341.py:1] INFO: Matching controls sequentially...\n", "2025-01-14 13:12:32,713 [92437865.py:24] INFO: Starting propensity score matching...\n", "2025-01-14 13:12:32,715 [92437865.py:29] INFO: Initial available controls: 3971 repositories.\n", "2025-01-14 13:12:32,717 [92437865.py:32] INFO: Extracting treatment group features at treatment times...\n", "2025-01-14 13:12:32,728 [92437865.py:39] INFO: Extracting control group features at all time units...\n", "2025-01-14 13:12:32,756 [92437865.py:47] WARNING: Treatment repo qmx/jitescript has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,757 [92437865.py:47] WARNING: Treatment repo plapointe6/espmqttclient has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,758 [92437865.py:47] WARNING: Treatment repo calvinmetcalf/immediate has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,759 [92437865.py:47] WARNING: Treatment repo facebookresearch/pyrobot has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,760 [92437865.py:47] WARNING: Treatment repo telegram-foss-team/telegram-foss has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,760 [92437865.py:47] WARNING: Treatment repo buggedcom/phpvideotoolkit-v2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,762 [92437865.py:47] WARNING: Treatment repo kwn/number-to-words has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,763 [92437865.py:47] WARNING: Treatment repo tldr-pages/tldr-c-client has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,764 [92437865.py:47] WARNING: Treatment repo gphoto/gphoto2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,764 [92437865.py:47] WARNING: Treatment repo ca98am79/connect-dynamodb has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,765 [92437865.py:47] WARNING: Treatment repo adrienpoupa/vinylmusicplayer has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,766 [92437865.py:47] WARNING: Treatment repo icculus/sdl_sound has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,767 [92437865.py:47] WARNING: Treatment repo dspinellis/umlgraph has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,768 [92437865.py:47] WARNING: Treatment repo duckdb/sqlite_scanner has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,770 [92437865.py:47] WARNING: Treatment repo alexanderwallin/node-gettext has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,771 [92437865.py:47] WARNING: Treatment repo spring-projects/spring-social-twitter has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,772 [92437865.py:47] WARNING: Treatment repo mydumper/mydumper has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,774 [92437865.py:47] WARNING: Treatment repo freeotp/freeotp-android has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,776 [92437865.py:47] WARNING: Treatment repo yubico/python-yubico has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,777 [92437865.py:47] WARNING: Treatment repo safe-global/safe-modules has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,780 [92437865.py:47] WARNING: Treatment repo azure/iot-central-firmware has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,782 [92437865.py:47] WARNING: Treatment repo bcomnes/npm-run-all2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,783 [92437865.py:47] WARNING: Treatment repo deck-of-cards/deck-of-cards has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,786 [92437865.py:47] WARNING: Treatment repo intro-skipper/intro-skipper has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,788 [92437865.py:47] WARNING: Treatment repo tensorflow/playground has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,790 [92437865.py:47] WARNING: Treatment repo zkemail/zk-email-verify has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,793 [92437865.py:47] WARNING: Treatment repo inception-project/inception has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,795 [92437865.py:47] WARNING: Treatment repo rust-sailfish/sailfish has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,797 [92437865.py:47] WARNING: Treatment repo primefaces/primeflex has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,799 [92437865.py:47] WARNING: Treatment repo telegrambot/api has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,801 [92437865.py:47] WARNING: Treatment repo erpalma/throttled has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,803 [92437865.py:47] WARNING: Treatment repo Mirai-NET-Shelter/Mirai.Net has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,805 [92437865.py:47] WARNING: Treatment repo 101loop/drf-user has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,807 [92437865.py:47] WARNING: Treatment repo hjiang/jsonxx has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,810 [92437865.py:47] WARNING: Treatment repo boostorg/geometry has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,812 [92437865.py:47] WARNING: Treatment repo jamestkhan/mundus has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,813 [92437865.py:47] WARNING: Treatment repo atlas0fd00m/rfcat has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,814 [92437865.py:47] WARNING: Treatment repo w3develops/w3develops has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,815 [92437865.py:47] WARNING: Treatment repo orlp/slotmap has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,817 [92437865.py:47] WARNING: Treatment repo openxenmanager/openxenmanager has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,819 [92437865.py:47] WARNING: Treatment repo jshttp/basic-auth has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,821 [92437865.py:47] WARNING: Treatment repo softuni/csharp-web has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,822 [92437865.py:47] WARNING: Treatment repo tryolabs/requestium has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,823 [92437865.py:47] WARNING: Treatment repo conda/pycosat has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,825 [92437865.py:47] WARNING: Treatment repo axiak/pybloomfiltermmap has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,827 [92437865.py:47] WARNING: Treatment repo jbroadway/elefant has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,828 [92437865.py:47] WARNING: Treatment repo balderdashy/sails has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,829 [92437865.py:47] WARNING: Treatment repo scholarslab/neatline has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,830 [92437865.py:47] WARNING: Treatment repo TheMulhima/Lumafly has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,832 [92437865.py:47] WARNING: Treatment repo sphereserver/source has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,835 [92437865.py:47] WARNING: Treatment repo freeradius/pam_radius has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,836 [92437865.py:47] WARNING: Treatment repo yse/easy_profiler has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,839 [92437865.py:47] WARNING: Treatment repo jshttp/methods has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,840 [92437865.py:47] WARNING: Treatment repo draquet/polyglot has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,841 [92437865.py:47] WARNING: Treatment repo alacritty/vte has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,844 [92437865.py:47] WARNING: Treatment repo eprosima/fast-cdr has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,846 [92437865.py:47] WARNING: Treatment repo MirrorNetworking/Telepathy has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,848 [92437865.py:47] WARNING: Treatment repo opis/json-schema has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,850 [92437865.py:47] WARNING: Treatment repo css/csso has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,852 [92437865.py:47] WARNING: Treatment repo phpids/phpids has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,853 [92437865.py:47] WARNING: Treatment repo ipld/go-ipld-prime has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,855 [92437865.py:47] WARNING: Treatment repo winfsp/sshfs-win has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,857 [92437865.py:47] WARNING: Treatment repo arduino/arduino-language-server has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,859 [92437865.py:47] WARNING: Treatment repo rainwayapp/bebop has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,861 [92437865.py:47] WARNING: Treatment repo blackfireio/player has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,862 [92437865.py:47] WARNING: Treatment repo ripe-ncc/ripe-atlas-software-probe has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,863 [92437865.py:47] WARNING: Treatment repo cakephp-bootstrap/cakephp3-bootstrap-helpers has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,865 [92437865.py:47] WARNING: Treatment repo robertabcd/pttchrome has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,866 [92437865.py:47] WARNING: Treatment repo droe/sslsplit has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,868 [92437865.py:47] WARNING: Treatment repo gookit/color has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,869 [92437865.py:47] WARNING: Treatment repo siccity/gltfutility has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,870 [92437865.py:47] WARNING: Treatment repo zblogcn/zblogphp has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,872 [92437865.py:47] WARNING: Treatment repo caplin/flexlayout has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,873 [92437865.py:47] WARNING: Treatment repo lxerxa/actionview has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,874 [92437865.py:47] WARNING: Treatment repo jreese/znc-push has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,875 [92437865.py:47] WARNING: Treatment repo jansc/ncgopher has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,876 [92437865.py:47] WARNING: Treatment repo paxa/fast_excel has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,877 [92437865.py:47] WARNING: Treatment repo ethz-adrl/ifopt has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,879 [92437865.py:47] WARNING: Treatment repo julianhille/muhammarajs has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,880 [92437865.py:47] WARNING: Treatment repo ape-project/ape_server has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,882 [92437865.py:47] WARNING: Treatment repo lwjglx/lwjgl3-awt has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,884 [92437865.py:47] WARNING: Treatment repo expatfile/next-runtime-env has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,893 [92437865.py:47] WARNING: Treatment repo webtorrent/magnet-uri has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,895 [92437865.py:47] WARNING: Treatment repo stoken-dev/stoken has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,898 [92437865.py:47] WARNING: Treatment repo studio3t/robomongo has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,902 [92437865.py:47] WARNING: Treatment repo jpcsp/jpcsp has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,903 [92437865.py:47] WARNING: Treatment repo ps2dev/ps2sdk has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,905 [92437865.py:47] WARNING: Treatment repo dennis<PERSON>/beanstalkd_view has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,907 [92437865.py:47] WARNING: Treatment repo nmodbus/nmodbus has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,909 [92437865.py:47] WARNING: Treatment repo pwmt/zathura has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,914 [92437865.py:47] WARNING: Treatment repo ibc/libsdptransform has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,916 [92437865.py:47] WARNING: Treatment repo netenglabs/suzieq has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,919 [92437865.py:47] WARNING: Treatment repo espresense/espresense has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,921 [92437865.py:47] WARNING: Treatment repo etcd-cpp-apiv3/etcd-cpp-apiv3 has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,923 [92437865.py:47] WARNING: Treatment repo peter-murray/node-hue-api has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,925 [92437865.py:47] WARNING: Treatment repo minishmaker/randomizer has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,926 [92437865.py:47] WARNING: Treatment repo bloodhoundad/sharphound has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,929 [92437865.py:47] WARNING: Treatment repo haxefoundation/neko has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,929 [92437865.py:47] WARNING: Treatment repo rustls/tokio-rustls has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,931 [92437865.py:47] WARNING: Treatment repo aaronpowell/httpstatus has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,932 [92437865.py:47] WARNING: Treatment repo eclipse/paho.mqtt-sn.embedded-c has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,933 [92437865.py:47] WARNING: Treatment repo hibri/httpmock has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,935 [92437865.py:47] WARNING: Treatment repo leetcode-opensource/ayanami has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,939 [92437865.py:47] WARNING: Treatment repo envirodiy/arduino-sdi-12 has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,941 [92437865.py:47] WARNING: Treatment repo nicolas-chaulet/torch-points3d has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,942 [92437865.py:47] WARNING: Treatment repo jcrodriguez-dis/moodle-mod_vpl has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,943 [92437865.py:47] WARNING: Treatment repo bublejs/buble has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,945 [92437865.py:47] WARNING: Treatment repo samsung/escargot has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,947 [92437865.py:47] WARNING: Treatment repo libharu/libharu has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,949 [92437865.py:47] WARNING: Treatment repo linkedin/spyglass has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,951 [92437865.py:47] WARNING: Treatment repo team-tea-time/laravel-forum has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,952 [92437865.py:47] WARNING: Treatment repo stefanprodan/aspnetcoreratelimit has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,953 [92437865.py:47] WARNING: Treatment repo acidb/mobiscroll has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,955 [92437865.py:47] WARNING: Treatment repo phalapi/phalapi has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,957 [92437865.py:47] WARNING: Treatment repo jdiamond/nustache has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,960 [92437865.py:47] WARNING: Treatment repo davidgiven/ack has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,962 [92437865.py:47] WARNING: Treatment repo alphaleonis/alphafs has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,963 [92437865.py:47] WARNING: Treatment repo rustless/rustless has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,966 [92437865.py:47] WARNING: Treatment repo dozennn/steam-rom-manager has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,968 [92437865.py:47] WARNING: Treatment repo postsharp/postsharp.samples has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,970 [92437865.py:47] WARNING: Treatment repo fex-team/kityminder has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,972 [92437865.py:47] WARNING: Treatment repo rabbitmq/rabbitmq-java-client has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,972 [92437865.py:47] WARNING: Treatment repo sirjuddington/slade has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,975 [92437865.py:47] WARNING: Treatment repo frameworkcomputer/inputmodule-rs has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,977 [92437865.py:47] WARNING: Treatment repo qb64-phoenix-edition/qb64pe has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,979 [92437865.py:47] WARNING: Treatment repo anhskohbo/no-captcha has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,981 [92437865.py:47] WARNING: Treatment repo gojp/goreportcard has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,983 [92437865.py:47] WARNING: Treatment repo azure/aspnet-redis-providers has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,984 [92437865.py:47] WARNING: Treatment repo awslabs/fargatecli has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,986 [92437865.py:47] WARNING: Treatment repo signalapp/libsignal-service-java has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,988 [92437865.py:47] WARNING: Treatment repo azure-samples/ms-identity-aspnet-daemon-webapp has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,989 [92437865.py:47] WARNING: Treatment repo jsonfx/jsonfx has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,991 [92437865.py:47] WARNING: Treatment repo jest-community/jest-watch-typeahead has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,992 [92437865.py:47] WARNING: Treatment repo opendbdiff/opendbdiff has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,993 [92437865.py:47] WARNING: Treatment repo jamesramm/longclaw has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,994 [92437865.py:47] WARNING: Treatment repo pillarjs/router has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,995 [92437865.py:47] WARNING: Treatment repo digimezzo/dopamine-windows has no valid feature values at treatment time.\n", "2025-01-14 13:12:32,998 [92437865.py:47] WARNING: Treatment repo reserve-protocol/protocol has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,003 [92437865.py:47] WARNING: Treatment repo angular-redux/store has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,004 [92437865.py:47] WARNING: Treatment repo bogardo/mailgun has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,007 [92437865.py:47] WARNING: Treatment repo totaldebug/atomic-calendar-revive has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,009 [92437865.py:47] WARNING: Treatment repo nikitacartes/easyauth has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,011 [92437865.py:47] WARNING: Treatment repo npm/npm-registry-couchapp has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,013 [92437865.py:47] WARNING: Treatment repo xiph/speexdsp has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,016 [92437865.py:47] WARNING: Treatment repo peculiarventures/pkcs11js has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,018 [92437865.py:47] WARNING: Treatment repo graphql-python/graphql-ws has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,020 [92437865.py:47] WARNING: Treatment repo rancher/remotedialer has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,022 [92437865.py:47] WARNING: Treatment repo winauth/winauth has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,023 [92437865.py:47] WARNING: Treatment repo laravel-ardent/ardent has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,025 [92437865.py:47] WARNING: Treatment repo pangeo-data/xesmf has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,026 [92437865.py:47] WARNING: Treatment repo networkblockdevice/nbd has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,027 [92437865.py:47] WARNING: Treatment repo theseer/phpdox has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,029 [92437865.py:47] WARNING: Treatment repo tools-life/taskwiki has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,032 [92437865.py:47] WARNING: Treatment repo yyzybb537/libgo has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,034 [92437865.py:47] WARNING: Treatment repo google/corgi has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,036 [92437865.py:47] WARNING: Treatment repo jiangdongguo/androidusbcamera has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,038 [92437865.py:47] WARNING: Treatment repo cncd/pipeline has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,039 [92437865.py:47] WARNING: Treatment repo pyladies/pyladies-kit has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,040 [92437865.py:47] WARNING: Treatment repo i-rinat/apulse has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,042 [92437865.py:47] WARNING: Treatment repo zalando/spring-cloud-config-aws-kms has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,045 [92437865.py:47] WARNING: Treatment repo mediacloud/backend has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,048 [92437865.py:47] WARNING: Treatment repo assertj/assertj-db has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,049 [92437865.py:47] WARNING: Treatment repo fornever/xaml-math has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,050 [92437865.py:47] WARNING: Treatment repo safchain/ethtool has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,054 [92437865.py:47] WARNING: Treatment repo sainteos/tmxparser has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,055 [92437865.py:47] WARNING: Treatment repo pofider/phantom-html-to-pdf has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,056 [92437865.py:47] WARNING: Treatment repo c42f/displaz has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,058 [92437865.py:47] WARNING: Treatment repo ericf/express-handlebars has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,062 [92437865.py:47] WARNING: Treatment repo scverse/muon has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,063 [92437865.py:47] WARNING: Treatment repo pingpong-labs/modules has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,065 [92437865.py:47] WARNING: Treatment repo gocraft/work has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,066 [92437865.py:47] WARNING: Treatment repo dain/leveldb has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,068 [92437865.py:47] WARNING: Treatment repo one-language/one has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,069 [92437865.py:47] WARNING: Treatment repo xiaofaye/woocommerce.net has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,072 [92437865.py:47] WARNING: Treatment repo simpod/grafanajsondatasource has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,073 [92437865.py:47] WARNING: Treatment repo finitespace/bme280 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,074 [92437865.py:47] WARNING: Treatment repo importcjj/mobc has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,075 [92437865.py:47] WARNING: Treatment repo amyreese/znc-push has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,076 [92437865.py:47] WARNING: Treatment repo zotero/zotero-standalone-build has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,077 [92437865.py:47] WARNING: Treatment repo fashionfreedom/seamly2d has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,081 [92437865.py:47] WARNING: Treatment repo jumpsuit/jumpstate has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,083 [92437865.py:47] WARNING: Treatment repo rust-osdev/multiboot2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,084 [92437865.py:47] WARNING: Treatment repo 10gic/vanitygen-plusplus has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,086 [92437865.py:47] WARNING: Treatment repo forwardemail/forwardemail.net has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,087 [92437865.py:47] WARNING: Treatment repo nitroshare/nitroshare-desktop has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,088 [92437865.py:47] WARNING: Treatment repo databendlabs/openraft has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,089 [92437865.py:47] WARNING: Treatment repo kbknapp/cargo-graph has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,091 [92437865.py:47] WARNING: Treatment repo quamotion/madb has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,093 [92437865.py:47] WARNING: Treatment repo patroclos/pamix has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,096 [92437865.py:47] WARNING: Treatment repo deepmedia/Transcoder has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,098 [92437865.py:47] WARNING: Treatment repo angular-wizard/angular-wizard has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,099 [92437865.py:47] WARNING: Treatment repo nvidiagameworks/dxvk-remix has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,100 [92437865.py:47] WARNING: Treatment repo js-data/js-data-angular has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,101 [92437865.py:47] WARNING: Treatment repo mozilla-services/lua_sandbox has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,104 [92437865.py:47] WARNING: Treatment repo 1313e/cmasher has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,107 [92437865.py:47] WARNING: Treatment repo zhouhaoyi/informer2020 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,110 [92437865.py:47] WARNING: Treatment repo nextcloud/cms_pico has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,113 [92437865.py:47] WARNING: Treatment repo scotttrinh/angular-localforage has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,116 [92437865.py:47] WARNING: Treatment repo stevemacenski/slam_toolbox has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,117 [92437865.py:47] WARNING: Treatment repo nexus-mods/vortex has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,118 [92437865.py:47] WARNING: Treatment repo espressif/esp-hosted has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,120 [92437865.py:47] WARNING: Treatment repo mikeerickson/phpunit-pretty-result-printer has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,122 [92437865.py:47] WARNING: Treatment repo hyperf/hyperf-skeleton has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,123 [92437865.py:47] WARNING: Treatment repo opennmt/ctranslate2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,126 [92437865.py:47] WARNING: Treatment repo jrl-umi3218/rbdyn has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,128 [92437865.py:47] WARNING: Treatment repo ethereum/fe has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,130 [92437865.py:47] WARNING: Treatment repo omf2097/openomf has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,132 [92437865.py:47] WARNING: Treatment repo googlefonts/roboto has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,133 [92437865.py:47] WARNING: Treatment repo sshnet/ssh.net has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,134 [92437865.py:47] WARNING: Treatment repo smeighan/xlights has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,137 [92437865.py:47] WARNING: Treatment repo chisel-team/chisel has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,139 [92437865.py:47] WARNING: Treatment repo vibalijoshi/list-of-opportunities has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,140 [92437865.py:47] WARNING: Treatment repo openxc/uds-c has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,142 [92437865.py:47] WARNING: Treatment repo wargus/stargus has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,143 [92437865.py:47] WARNING: Treatment repo nextgenusfs/funannotate has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,143 [92437865.py:47] WARNING: Treatment repo taycaldwell/riot-api-java has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,146 [92437865.py:47] WARNING: Treatment repo cntools/libsurvive has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,147 [92437865.py:47] WARNING: Treatment repo mindorksopensource/android-mvp-architecture has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,147 [92437865.py:47] WARNING: Treatment repo angular/dgeni has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,148 [92437865.py:47] WARNING: Treatment repo angelozerr/angularjs-eclipse has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,149 [92437865.py:47] WARNING: Treatment repo kakserpom/phpdaemon has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,150 [92437865.py:47] WARNING: Treatment repo nativefier/nativefier has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,151 [92437865.py:47] WARNING: Treatment repo spring-io/initializr has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,152 [92437865.py:47] WARNING: Treatment repo aliyun/aliyun-openapi-net-sdk has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,152 [92437865.py:47] WARNING: Treatment repo rough-stuff/wired-elements has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,153 [92437865.py:47] WARNING: Treatment repo graphitemaster/gmqcc has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,154 [92437865.py:47] WARNING: Treatment repo carml/carml has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,155 [92437865.py:47] WARNING: Treatment repo crccheck/raphael-svg-import-classic has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,156 [92437865.py:47] WARNING: Treatment repo rpotter12/whatsapp-play has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,158 [92437865.py:47] WARNING: Treatment repo izhaorui/zr.admin.net has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,159 [92437865.py:47] WARNING: Treatment repo nilsmagnus/wsdl2java has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,159 [92437865.py:47] WARNING: Treatment repo cmu-sei/pharos has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,160 [92437865.py:47] WARNING: Treatment repo metajack/libstrophe has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,161 [92437865.py:47] WARNING: Treatment repo opennars/opennars has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,163 [92437865.py:47] WARNING: Treatment repo techreborn/techreborn has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,163 [92437865.py:47] WARNING: Treatment repo samuelmeuli/mini-diary has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,164 [92437865.py:47] WARNING: Treatment repo kazade/kazmath has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,166 [92437865.py:47] WARNING: Treatment repo lzjun567/zhihu-api has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,167 [92437865.py:47] WARNING: Treatment repo gossi/php-code-generator has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,168 [92437865.py:47] WARNING: Treatment repo xivapi/xivapi.com has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,169 [92437865.py:47] WARNING: Treatment repo roamjs/workbench has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,170 [92437865.py:47] WARNING: Treatment repo satyrius/gonx has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,171 [92437865.py:47] WARNING: Treatment repo swi-prolog/swipl-devel has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,171 [92437865.py:47] WARNING: Treatment repo dnglab/dnglab has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,172 [92437865.py:47] WARNING: Treatment repo assetripper/assetripper has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,174 [92437865.py:47] WARNING: Treatment repo adobe/aem-component-generator has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,175 [92437865.py:47] WARNING: Treatment repo jamesmoss/flywheel has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,175 [92437865.py:47] WARNING: Treatment repo meganz/megasync has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,177 [92437865.py:47] WARNING: Treatment repo chensiliang/rotatephotoview has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,178 [92437865.py:47] WARNING: Treatment repo flashpointproject/launcher has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,179 [92437865.py:47] WARNING: Treatment repo pdepend/pdepend has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,179 [92437865.py:47] WARNING: Treatment repo madsmtm/objc2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,180 [92437865.py:47] WARNING: Treatment repo chrome-php/chrome has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,181 [92437865.py:47] WARNING: Treatment repo dji-sdk/mobile-sdk-android has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,183 [92437865.py:47] WARNING: Treatment repo collabnix/dockerlabs has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,184 [92437865.py:47] WARNING: Treatment repo liftoff-sr/cipster has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,185 [92437865.py:47] WARNING: Treatment repo egorbo/toasts.forms.plugin has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,186 [92437865.py:47] WARNING: Treatment repo sharpdx/sharpdx has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,188 [92437865.py:47] WARNING: Treatment repo box-project/box2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,190 [92437865.py:47] WARNING: Treatment repo prismlibrary/prism has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,191 [92437865.py:47] WARNING: Treatment repo mopidy/mopidy-soundcloud has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,192 [92437865.py:47] WARNING: Treatment repo lumateam/luma3ds has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,192 [92437865.py:47] WARNING: Treatment repo open-power/petitboot has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,193 [92437865.py:47] WARNING: Treatment repo dynamorio/drmemory has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,194 [92437865.py:47] WARNING: Treatment repo erusev/parsedown has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,195 [92437865.py:47] WARNING: Treatment repo etda/e-taxinvoice-pdfgen has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,196 [92437865.py:47] WARNING: Treatment repo opentracing/opentracing-csharp has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,197 [92437865.py:47] WARNING: Treatment repo mihara/rasterpropmonitor has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,198 [92437865.py:47] WARNING: Treatment repo lkuza2/java-speech-api has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,200 [92437865.py:47] WARNING: Treatment repo ikvm-revived/ikvm has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,201 [92437865.py:47] WARNING: Treatment repo webui-dev/deno-webui has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,201 [92437865.py:47] WARNING: Treatment repo andrewkeig/express-validation has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,202 [92437865.py:47] WARNING: Treatment repo wfh45678/radar has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,204 [92437865.py:47] WARNING: Treatment repo libinjection/libinjection has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,205 [92437865.py:47] WARNING: Treatment repo immobiliare/backstage-plugin-gitlab has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,207 [92437865.py:47] WARNING: Treatment repo headmyshoulder/odeint-v2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,208 [92437865.py:47] WARNING: Treatment repo jfilter/react-native-onboarding-swiper has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,208 [92437865.py:47] WARNING: Treatment repo inlet/react-pixi has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,209 [92437865.py:47] WARNING: Treatment repo gerard/ext4fuse has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,210 [92437865.py:47] WARNING: Treatment repo dotnetinstaller/dotnetinstaller has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,211 [92437865.py:47] WARNING: Treatment repo mapasculturais/mapasculturais has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,212 [92437865.py:47] WARNING: Treatment repo multisnow/mcomix3 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,213 [92437865.py:47] WARNING: Treatment repo nfroidure/ttf2woff2 has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,215 [92437865.py:47] WARNING: Treatment repo sargon/trayer-srg has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,216 [92437865.py:47] WARNING: Treatment repo tylertreat/comcast has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,218 [92437865.py:47] WARNING: Treatment repo genie-team/graphql-genie has no valid feature values at treatment time.\n", "2025-01-14 13:12:33,221 [92437865.py:50] INFO: Number of treatment repositories with valid features at treatment time: 641\n", "2025-01-14 13:12:33,222 [92437865.py:51] INFO: Number of control repositories with valid features at all time units: 447008\n", "2025-01-14 13:12:33,269 [92437865.py:63] INFO: Performing nearest neighbors matching...\n", "2025-01-14 13:12:33,449 [92437865.py:103] INFO: Control repo 10up/autoshare-for-twitter is the same as the treatment repo 10up/autoshare-for-twitter. Skipping...\n", "2025-01-14 13:12:34,224 [92437865.py:99] INFO: For Treatment repo academysoftwarefoundation/aswf-docker, Control repo actix/actix is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:34,371 [92437865.py:103] INFO: Control repo air-verse/air is the same as the treatment repo air-verse/air. Skipping...\n", "2025-01-14 13:12:34,554 [92437865.py:99] INFO: For Treatment repo airspy/airspyhf, Control repo activiti/activiti is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:34,570 [92437865.py:99] INFO: For Treatment repo airspy/airspyhf, Control repo aio-libs/aiosmtpd is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:35,309 [92437865.py:103] INFO: Control repo anomalyinnovations/serverless-bundle is the same as the treatment repo anomalyinnovations/serverless-bundle. Skipping...\n", "2025-01-14 13:12:36,209 [92437865.py:103] INFO: Control repo auth0/auth0-react is the same as the treatment repo auth0/auth0-react. Skipping...\n", "2025-01-14 13:12:37,504 [92437865.py:99] INFO: For Treatment repo awslabs/ktf, Control repo ROCm/AMDMIGraphX is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:37,848 [92437865.py:99] INFO: For Treatment repo babassl/babassl, Control repo graphql-rust/juniper is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:39,537 [92437865.py:99] INFO: For Treatment repo cartercommunity/carter, Control repo alexcrichton/toml-rs is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:40,401 [92437865.py:103] INFO: Control repo cloudwego/volo is the same as the treatment repo cloudwego/volo. Skipping...\n", "2025-01-14 13:12:43,947 [92437865.py:99] INFO: For Treatment repo electron/osx-sign, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:45,434 [92437865.py:103] INFO: Control repo fico7489/laravel-eloquent-join is the same as the treatment repo fico7489/laravel-eloquent-join. Skipping...\n", "2025-01-14 13:12:45,714 [92437865.py:99] INFO: For Treatment repo flame/blis, Control repo activiti/activiti is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:47,202 [92437865.py:99] INFO: For Treatment repo go-auth0/auth0, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:47,245 [92437865.py:99] INFO: For Treatment repo go-auth0/auth0, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:48,555 [92437865.py:99] INFO: For Treatment repo haddocking/haddock3, Control repo OfficeDev/Microsoft-Teams-Sam<PERSON> is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:49,636 [92437865.py:99] INFO: For Treatment repo illumina/expansionhunter, Control repo dethcrypto/typechain is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:49,741 [92437865.py:99] INFO: For Treatment repo includable/react-native-email-link, Control repo EpicGamesExt/BlenderTools is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:50,574 [92437865.py:103] INFO: Control repo iter-tools/iter-tools is the same as the treatment repo iter-tools/iter-tools. Skipping...\n", "2025-01-14 13:12:51,392 [92437865.py:99] INFO: For Treatment repo jikan-me/jikan, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:52,151 [92437865.py:99] INFO: For Treatment repo jvoisin/php-malware-finder, Control repo 10up/theme-scaffold is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:53,082 [92437865.py:99] INFO: For Treatment repo kubernetes-sigs/alibaba-cloud-csi-driver, Control repo banzaicloud/koperator is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:54,285 [92437865.py:99] INFO: For Treatment repo mapfish/mapfish-print, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:54,300 [92437865.py:99] INFO: For Treatment repo mapfish/mapfish-print, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:54,318 [92437865.py:99] INFO: For Treatment repo mapfish/mapfish-print, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:54,336 [92437865.py:99] INFO: For Treatment repo mapfish/mapfish-print, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:54,352 [92437865.py:99] INFO: For Treatment repo mapfish/mapfish-print, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:54,352 [92437865.py:117] WARNING: No matched controls for treatment repo mapfish/mapfish-print.\n", "2025-01-14 13:12:54,575 [92437865.py:99] INFO: For Treatment repo marcocasamento/hangfire.redis.stackexchange, Control repo a5-/gamerfood_csgo is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:55,393 [92437865.py:99] INFO: For Treatment repo microsoft/azure-spring-apps-training, Control repo marimerllc/csla is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:55,466 [92437865.py:99] INFO: For Treatment repo microsoft/azure-spring-cloud-training, Control repo marimerllc/csla is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:55,481 [92437865.py:103] INFO: Control repo microsoft/azure-spring-cloud-training is the same as the treatment repo microsoft/azure-spring-cloud-training. Skipping...\n", "2025-01-14 13:12:55,558 [92437865.py:99] INFO: For Treatment repo microsoft/azurlshortener, Control repo ad-oliviero/uwufetch is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:56,001 [92437865.py:99] INFO: For Treatment repo microsoft/verisol, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:56,032 [92437865.py:99] INFO: For Treatment repo microsoft/verisol, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:58,967 [92437865.py:99] INFO: For Treatment repo octokit/request-action, Control repo activiti/activiti is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:58,984 [92437865.py:99] INFO: For Treatment repo octokit/request-action, Control repo actix/actix is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:59,305 [92437865.py:103] INFO: Control repo openapitools/openapi-style-validator is the same as the treatment repo openapitools/openapi-style-validator. Skipping...\n", "2025-01-14 13:12:59,380 [92437865.py:99] INFO: For Treatment repo openbazaar/openbazaar-go, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:59,395 [92437865.py:99] INFO: For Treatment repo openbazaar/openbazaar-go, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:59,412 [92437865.py:99] INFO: For Treatment repo openbazaar/openbazaar-go, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:59,429 [92437865.py:99] INFO: For Treatment repo openbazaar/openbazaar-go, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:59,446 [92437865.py:99] INFO: For Treatment repo openbazaar/openbazaar-go, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:12:59,447 [92437865.py:117] WARNING: No matched controls for treatment repo openbazaar/openbazaar-go.\n", "2025-01-14 13:13:01,048 [92437865.py:103] INFO: Control repo pipelinedb/pipelinedb is the same as the treatment repo pipelinedb/pipelinedb. Skipping...\n", "2025-01-14 13:13:01,410 [92437865.py:99] INFO: For Treatment repo plotly/plotly.rs, Control repo amocrm/amocrm-api-php is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:01,444 [92437865.py:99] INFO: For Treatment repo plotly/plotly.rs, Control repo amocrm/amocrm-api-php is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:03,141 [92437865.py:99] INFO: For Treatment repo remix/partridge, Control repo 2dust/v2rayn is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:03,156 [92437865.py:99] INFO: For Treatment repo remix/partridge, Control repo 10up/autoshare-for-twitter is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:04,212 [92437865.py:99] INFO: For Treatment repo saltierl/carball, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:04,228 [92437865.py:99] INFO: For Treatment repo saltierl/carball, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:04,244 [92437865.py:99] INFO: For Treatment repo saltierl/carball, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:04,262 [92437865.py:99] INFO: For Treatment repo saltierl/carball, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:04,279 [92437865.py:99] INFO: For Treatment repo saltierl/carball, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:04,280 [92437865.py:117] WARNING: No matched controls for treatment repo saltierl/carball.\n", "2025-01-14 13:13:04,327 [92437865.py:99] INFO: For Treatment repo saptarshisarkar12/drifty, Control repo apache/sedona is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:05,438 [92437865.py:99] INFO: For Treatment repo silexlabs/unifile, Control repo aws-samples/aws-big-data-blog is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:05,556 [92437865.py:99] INFO: For Treatment repo sinnerschrader/feature-hub, Control repo 2dust/v2rayn is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:05,572 [92437865.py:99] INFO: For Treatment repo sinnerschrader/feature-hub, Control repo 2dust/v2rayn is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:05,855 [92437865.py:99] INFO: For Treatment repo slic3r/slic3r, Control repo ash-rs/ash is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:06,930 [92437865.py:99] INFO: For Treatment repo springdoc/springdoc-openapi, Control repo 10up/theme-scaffold is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:07,025 [92437865.py:99] INFO: For Treatment repo sqmk/phue, Control repo a5-/gamerfood_csgo is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:07,171 [92437865.py:103] INFO: Control repo stevearc/pypicloud is the same as the treatment repo stevearc/pypicloud. Skipping...\n", "2025-01-14 13:13:09,523 [92437865.py:99] INFO: For Treatment repo typedb/typeql, Control repo 4catalyzer/found is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:09,786 [92437865.py:99] INFO: For Treatment repo ucl/stir, Control repo Screenly/Anthias is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:10,693 [92437865.py:99] INFO: For Treatment repo vmware/clarity, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:10,708 [92437865.py:99] INFO: For Treatment repo vmware/clarity, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:10,725 [92437865.py:99] INFO: For Treatment repo vmware/clarity, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:10,741 [92437865.py:99] INFO: For Treatment repo vmware/clarity, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:10,757 [92437865.py:99] INFO: For Treatment repo vmware/clarity, Control repo 0xproject/0x-launch-kit-frontend is also under treatment within 12 weeks.Skipping...\n", "2025-01-14 13:13:10,758 [92437865.py:117] WARNING: No matched controls for treatment repo vmware/clarity.\n", "2025-01-14 13:13:12,992 [92437865.py:131] WARNING: Treatment repo 10up/autoshare-for-twitter has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,993 [92437865.py:131] WARNING: Treatment repo academysoftwarefoundation/aswf-docker has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,994 [92437865.py:131] WARNING: Treatment repo air-verse/air has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,995 [92437865.py:131] WARNING: Treatment repo airspy/airspyhf has only 3 controls, expected 5.\n", "2025-01-14 13:13:12,995 [92437865.py:131] WARNING: Treatment repo anomalyinnovations/serverless-bundle has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,996 [92437865.py:131] WARNING: Treatment repo auth0/auth0-react has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,996 [92437865.py:131] WARNING: Treatment repo awslabs/ktf has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,997 [92437865.py:131] WARNING: Treatment repo babassl/babassl has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,998 [92437865.py:131] WARNING: Treatment repo cartercommunity/carter has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,999 [92437865.py:131] WARNING: Treatment repo cloudwego/volo has only 4 controls, expected 5.\n", "2025-01-14 13:13:12,999 [92437865.py:131] WARNING: Treatment repo electron/osx-sign has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,000 [92437865.py:131] WARNING: Treatment repo fico7489/laravel-eloquent-join has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,001 [92437865.py:131] WARNING: Treatment repo flame/blis has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,002 [92437865.py:131] WARNING: Treatment repo go-auth0/auth0 has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,003 [92437865.py:131] WARNING: Treatment repo haddocking/haddock3 has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,004 [92437865.py:131] WARNING: Treatment repo illumina/expansionhunter has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,005 [92437865.py:131] WARNING: Treatment repo includable/react-native-email-link has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,006 [92437865.py:131] WARNING: Treatment repo iter-tools/iter-tools has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,006 [92437865.py:131] WARNING: Treatment repo jikan-me/jikan has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,008 [92437865.py:131] WARNING: Treatment repo jvoisin/php-malware-finder has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,009 [92437865.py:131] WARNING: Treatment repo kubernetes-sigs/alibaba-cloud-csi-driver has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,010 [92437865.py:131] WARNING: Treatment repo marcocasamento/hangfire.redis.stackexchange has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,012 [92437865.py:131] WARNING: Treatment repo microsoft/azure-spring-apps-training has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,013 [92437865.py:131] WARNING: Treatment repo microsoft/azure-spring-cloud-training has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,014 [92437865.py:131] WARNING: Treatment repo microsoft/azurlshortener has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,016 [92437865.py:131] WARNING: Treatment repo microsoft/verisol has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,017 [92437865.py:131] WARNING: Treatment repo octokit/request-action has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,018 [92437865.py:131] WARNING: Treatment repo openapitools/openapi-style-validator has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,019 [92437865.py:131] WARNING: Treatment repo pipelinedb/pipelinedb has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,019 [92437865.py:131] WARNING: Treatment repo plotly/plotly.rs has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,021 [92437865.py:131] WARNING: Treatment repo remix/partridge has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,022 [92437865.py:131] WARNING: Treatment repo saptarshisarkar12/drifty has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,023 [92437865.py:131] WARNING: Treatment repo silexlabs/unifile has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,024 [92437865.py:131] WARNING: Treatment repo sinnerschrader/feature-hub has only 3 controls, expected 5.\n", "2025-01-14 13:13:13,025 [92437865.py:131] WARNING: Treatment repo slic3r/slic3r has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,025 [92437865.py:131] WARNING: Treatment repo springdoc/springdoc-openapi has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,026 [92437865.py:131] WARNING: Treatment repo sqmk/phue has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,026 [92437865.py:131] WARNING: Treatment repo stevearc/pypicloud has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,027 [92437865.py:131] WARNING: Treatment repo typedb/typeql has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,028 [92437865.py:131] WARNING: Treatment repo ucl/stir has only 4 controls, expected 5.\n", "2025-01-14 13:13:13,030 [92437865.py:132] INFO: Propensity score matching completed.\n", "2025-01-14 13:13:13,031 [92437865.py:133] INFO: Total matched pairs: 526\n"]}], "source": ["logging.info(\"Matching controls sequentially...\")\n", "productivity = pd.read_csv(\"../result/standardized_productivity/standardized_productivity_parallel.csv\")\n", "matched_pairs, treatment_features, control_features = match_all_treatments_psm(\n", "    # treatment_repos_with_attrition_date, all_repo_names, productivity, n_neighbors, feature_columns=[\"feature_relative_increase\", \"feature_magnitude\"])\n", "    treatment_repos_with_attrition_date, all_repo_names, productivity, n_neighbors, timewindow_weeks, feature_columns=[\"feature_sigmod\"])\n", "    # treatment_repos_with_attrition_date, all_repo_names, productivity, n_neighbors, feature_columns=[\"feature_relative_increase\"])\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# save the matched pairs in txt file\n", "with open(\"../result/standardized_productivity/matched_pairs.txt\", \"w\") as f:\n", "    f.write(str(matched_pairs))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:55:51.498552Z", "start_time": "2025-01-13T07:55:51.489166Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-14 13:13:32,685 [1178918856.py:8] INFO: Number of treatment repositories: 526\n", "2025-01-14 13:13:32,687 [1178918856.py:9] INFO: Number of control repositories: 2582\n", "2025-01-14 13:13:32,688 [1178918856.py:10] INFO: Average number of matched controls per treatment: 4.908745247148289\n"]}], "source": ["# show the result of matched pairs in number of treatment and control and average ratio\n", "n_treatment = len(matched_pairs)\n", "n_control = 0\n", "n_matched = 0\n", "for t_repo, match_info in matched_pairs.items():\n", "    n_control += len(match_info['controls'])\n", "    n_matched += 1\n", "logging.info(f\"Number of treatment repositories: {n_treatment}\")\n", "logging.info(f\"Number of control repositories: {n_control}\")\n", "logging.info(f\"Average number of matched controls per treatment: {n_control/n_treatment}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:55:58.387910Z", "start_time": "2025-01-13T07:55:56.237488Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-14 13:14:20,780 [3401252484.py:25] INFO: Saved ../result/standardized_productivity/final_data_with_controls.csv\n", "2025-01-14 13:14:20,797 [1022679076.py:89] INFO: Panel DiD analysis with control variables completed.\n"]}], "source": ["import statsmodels.api as sm\n", "combined_data = []\n", "\n", "# Iterate over each treatment repository and its controls\n", "for t_repo, match_info in matched_pairs.items():\n", "    t_time = match_info['treatment_time']\n", "    controls = match_info['controls']\n", "    \n", "    # Get data for treatment repository\n", "    t_data = productivity[productivity['repo_name'] == t_repo].copy()\n", "    t_data['relative_time_weeks'] = t_data['standardized_time_weeks'] - t_time\n", "    t_data['is_treatment'] = 1\n", "    t_data['post_treatment'] = (t_data['relative_time_weeks'] > 0).astype(int)\n", "    t_data['is_treatment_post_treatment'] = t_data['is_treatment'] * t_data['post_treatment']\n", "    # limit in the time window\n", "    t_data = t_data[\n", "        t_data['relative_time_weeks'].between(-timewindow_weeks - 1, timewindow_weeks + 1)\n", "    ] \n", "    \n", "    # List to hold data for controls\n", "    control_dfs = []\n", "    for control in controls:\n", "        c_repo = control['repo_name']\n", "        c_matched_time = control['matched_time']\n", "        \n", "        c_data = productivity[productivity['repo_name'] == c_repo].copy()\n", "        c_data['relative_time_weeks'] = c_data['standardized_time_weeks'] - c_matched_time\n", "        c_data['is_treatment'] = 0\n", "        c_data['post_treatment'] = (c_data['relative_time_weeks'] > 0).astype(int)\n", "        c_data['is_treatment_post_treatment'] = c_data['is_treatment'] * c_data['post_treatment']\n", "        # limit in the time window\n", "        c_data = c_data[\n", "            c_data['relative_time_weeks'].between(-timewindow_weeks- 1, timewindow_weeks + 1)\n", "        ]\n", "        control_dfs.append(c_data)\n", "    \n", "    # Combine treatment and control data\n", "    if control_dfs:\n", "        combined_set = pd.concat([t_data] + control_dfs, ignore_index=True)\n", "        combined_data.append(combined_set)\n", "    else:\n", "        logging.warning(f\"No valid controls for treatment repository {t_repo}.\")\n", "repo_info = pd.read_csv(\"../data/sample_projects_quartiles.csv\")\n", "# Concatenate all combined sets into final_data\n", "if combined_data:\n", "    final_data = pd.concat(combined_data, ignore_index=True)\n", "    \n", "    # # Define control variables\n", "    control_vars = [\n", "        # \"commits\",\n", "        # \"releases\",\n", "        # \"forks\",\n", "        # \"mainLanguage\",\n", "        # \"stargazers\",\n", "        # \"totalIssues\",\n", "        # \"totalPullRequests\",\n", "        # \"duration\",\n", "    ]\n", "    \n", "    # # Handle categorical variables\n", "    # if \"mainLanguage\" in final_data.columns:\n", "    #     final_data = pd.get_dummies(final_data, columns=[\"mainLanguage\"])\n", "    #     control_vars += [col for col in final_data.columns if col.startswith(\"mainLanguage_\")]\n", "    \n", "    # # # Ensure control variables are in float format\n", "    # for var in control_vars:\n", "    #     if var == \"duration\":\n", "    #         final_data[var] = final_data[var].apply(lambda x: float(x.split()[0]) / 7)  # Convert days to weeks\n", "    #     if var.startswith(\"mainLanguage_\"):\n", "    #         final_data[var] = final_data[var].astype(float)\n", "    \n", "    \n", "    # Define the regression model\n", "    # control_vars = []\n", "    X = sm.add_constant(\n", "        final_data[\n", "            [\"post_treatment\", \"is_treatment\", \"is_treatment_post_treatment\"] + control_vars\n", "        ]\n", "    )\n", "    y = final_data[metric_column]  # Ensure metric_column is defined\n", "    \n", "    # Run OLS regression\n", "    model = sm.OLS(y, X).fit()\n", "    \n", "    # Save results\n", "    save_data(final_data, \"final_data_with_controls.csv\")\n", "    with open(os.path.join(output_dir, \"regression_summary_with_controls.txt\"), \"w\") as f:\n", "        f.write(model.summary().as_text())\n", "    logging.info(\"Panel DiD analysis with control variables completed.\")\n", "else:\n", "    logging.warning(\"No matched data found for DiD analysis.\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:56:02.894281Z", "start_time": "2025-01-13T07:56:02.606053Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-14 13:14:36,238 [1965871641.py:57] INFO: Saved plot to ../result/standardized_productivity/pr_throughput_trend_plot.png.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "def visualize_trends(final_data, metric_column=\"pr_throughput\"):\n", "    # Group data into treatment and control groups\n", "    treatment_data = final_data[final_data[\"is_treatment\"] == 1]\n", "    control_data = final_data[final_data[\"is_treatment\"] == 0]\n", "\n", "    # Function to compute mean and standard deviation by time\n", "    def compute_stats(group_data):\n", "        grouped = group_data.groupby(\"relative_time_weeks\")\n", "        mean = grouped[metric_column].mean()\n", "        std = grouped[metric_column].std()\n", "        count = grouped[metric_column].count()\n", "        sem = std / np.sqrt(count)  # Standard error of the mean\n", "        return mean, mean - sem, mean + sem\n", "\n", "    # Calculate stats for treatment and control groups\n", "    treatment_mean, treatment_lower, treatment_upper = compute_stats(treatment_data)\n", "    control_mean, control_lower, control_upper = compute_stats(control_data)\n", "\n", "    # Create the plots\n", "    plt.figure(figsize=(12, 6))\n", "\n", "    # Plot for treatment group\n", "    plt.plot(treatment_mean.index, treatment_mean, label=\"Treatment Mean\", color=\"blue\")\n", "    plt.fill_between(\n", "        treatment_mean.index,\n", "        treatment_lower,\n", "        treatment_upper,\n", "        color=\"blue\",\n", "        alpha=0.2,\n", "        label=\"Treatment 95% CI\",\n", "    )\n", "\n", "    # Plot for control group\n", "    plt.plot(control_mean.index, control_mean, label=\"Control Mean\", color=\"green\")\n", "    plt.fill_between(\n", "        control_mean.index,\n", "        control_lower,\n", "        control_upper,\n", "        color=\"green\",\n", "        alpha=0.2,\n", "        label=\"Control 95% CI\",\n", "    )\n", "\n", "    # Add vertical line for treatment start\n", "    plt.axvline(0, color=\"red\", linestyle=\"--\", label=\"Treatment Start (time=0)\")\n", "\n", "    # Add titles and labels\n", "    plt.title(f\"{metric_column.capitalize()} Over Time (Treatment vs. Control)\")\n", "    plt.xlabel(\"Standardized Time (Weeks)\")\n", "    plt.ylabel(metric_column.capitalize())\n", "    plt.legend()\n", "\n", "    # Save and show the plot\n", "    plot_path = os.path.join(output_dir, f\"{metric_column}_trend_plot.png\")\n", "    plt.savefig(plot_path)\n", "    logging.info(f\"Saved plot to {plot_path}.\")\n", "    plt.show()\n", "\n", "visualize_trends(final_data, metric_column=\"pr_throughput\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:56:13.059468Z", "start_time": "2025-01-13T07:56:13.036733Z"}}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>OLS Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>      <td>pr_throughput</td>  <th>  R-squared:         </th>  <td>   0.027</td>  \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                   <td>OLS</td>       <th>  Adj. R-squared:    </th>  <td>   0.027</td>  \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>Least Squares</td>  <th>  F-statistic:       </th>  <td>   405.3</td>  \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>             <td><PERSON><PERSON>, 14 Jan 2025</td> <th>  Prob (F-statistic):</th>  <td>9.82e-260</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                 <td>13:19:59</td>     <th>  Log-Likelihood:    </th> <td>-1.5871e+05</td>\n", "</tr>\n", "<tr>\n", "  <th>No. Observations:</th>      <td> 43739</td>      <th>  AIC:               </th>  <td>3.174e+05</td> \n", "</tr>\n", "<tr>\n", "  <th>Df Residuals:</th>          <td> 43735</td>      <th>  BIC:               </th>  <td>3.175e+05</td> \n", "</tr>\n", "<tr>\n", "  <th>Df Model:</th>              <td>     3</td>      <th>                     </th>      <td> </td>     \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>      <td>nonrobust</td>    <th>                     </th>      <td> </td>     \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "               <td></td>                  <th>coef</th>     <th>std err</th>      <th>t</th>      <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>                       <td>    6.0638</td> <td>    0.062</td> <td>   97.786</td> <td> 0.000</td> <td>    5.942</td> <td>    6.185</td>\n", "</tr>\n", "<tr>\n", "  <th>post_treatment</th>              <td>   -3.3273</td> <td>    0.096</td> <td>  -34.777</td> <td> 0.000</td> <td>   -3.515</td> <td>   -3.140</td>\n", "</tr>\n", "<tr>\n", "  <th>is_treatment</th>                <td>   -1.3315</td> <td>    0.161</td> <td>   -8.267</td> <td> 0.000</td> <td>   -1.647</td> <td>   -1.016</td>\n", "</tr>\n", "<tr>\n", "  <th>is_treatment_post_treatment</th> <td>    3.7194</td> <td>    0.248</td> <td>   15.006</td> <td> 0.000</td> <td>    3.234</td> <td>    4.205</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Omnibus:</th>       <td>80392.627</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON>:     </th>   <td>   0.472</td>   \n", "</tr>\n", "<tr>\n", "  <th>Prob(Omnibus):</th>  <td> 0.000</td>   <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>243528870.555</td>\n", "</tr>\n", "<tr>\n", "  <th>Skew:</th>           <td>13.469</td>   <th>  Prob(JB):          </th>   <td>    0.00</td>   \n", "</tr>\n", "<tr>\n", "  <th>Kurtosis:</th>       <td>367.556</td>  <th>  Cond. No.          </th>   <td>    7.13</td>   \n", "</tr>\n", "</table><br/><br/>Notes:<br/>[1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/latex": ["\\begin{center}\n", "\\begin{tabular}{lclc}\n", "\\toprule\n", "\\textbf{Dep. Variable:}                 &  pr\\_throughput  & \\textbf{  R-squared:         } &       0.027    \\\\\n", "\\textbf{Model:}                         &       OLS        & \\textbf{  Adj. R-squared:    } &       0.027    \\\\\n", "\\textbf{Method:}                        &  Least Squares   & \\textbf{  F-statistic:       } &       405.3    \\\\\n", "\\textbf{Date:}                          & <PERSON><PERSON>, 14 Jan 2025 & \\textbf{  Prob (F-statistic):} &   9.82e-260    \\\\\n", "\\textbf{Time:}                          &     13:19:59     & \\textbf{  Log-Likelihood:    } &  -1.5871e+05   \\\\\n", "\\textbf{No. Observations:}              &       43739      & \\textbf{  AIC:               } &   3.174e+05    \\\\\n", "\\textbf{Df Residuals:}                  &       43735      & \\textbf{  BIC:               } &   3.175e+05    \\\\\n", "\\textbf{Df Model:}                      &           3      & \\textbf{                     } &                \\\\\n", "\\textbf{Covariance Type:}               &    nonrobust     & \\textbf{                     } &                \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lcccccc}\n", "                                        & \\textbf{coef} & \\textbf{std err} & \\textbf{t} & \\textbf{P$> |$t$|$} & \\textbf{[0.025} & \\textbf{0.975]}  \\\\\n", "\\midrule\n", "\\textbf{const}                          &       6.0638  &        0.062     &    97.786  &         0.000        &        5.942    &        6.185     \\\\\n", "\\textbf{post\\_treatment}                &      -3.3273  &        0.096     &   -34.777  &         0.000        &       -3.515    &       -3.140     \\\\\n", "\\textbf{is\\_treatment}                  &      -1.3315  &        0.161     &    -8.267  &         0.000        &       -1.647    &       -1.016     \\\\\n", "\\textbf{is\\_treatment\\_post\\_treatment} &       3.7194  &        0.248     &    15.006  &         0.000        &        3.234    &        4.205     \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lclc}\n", "\\textbf{Omnibus:}       & 80392.627 & \\textbf{  <PERSON><PERSON><PERSON><PERSON><PERSON>:     } &       0.472    \\\\\n", "\\textbf{Prob(Omnibus):} &    0.000  & \\textbf{  <PERSON><PERSON><PERSON><PERSON> (JB):  } & 243528870.555  \\\\\n", "\\textbf{Skew:}          &   13.469  & \\textbf{  Prob(JB):          } &        0.00    \\\\\n", "\\textbf{<PERSON><PERSON>:}      &  367.556  & \\textbf{  Cond. No.          } &        7.13    \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "%\\caption{OLS Regression Results}\n", "\\end{center}\n", "\n", "Notes: \\newline\n", " [1] Standard Errors assume that the covariance matrix of the errors is correctly specified."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:          pr_throughput   R-squared:                       0.027\n", "Model:                            OLS   Adj. R-squared:                  0.027\n", "Method:                 Least Squares   F-statistic:                     405.3\n", "Date:                <PERSON><PERSON>, 14 Jan 2025   Prob (F-statistic):          9.82e-260\n", "Time:                        13:19:59   Log-Likelihood:            -1.5871e+05\n", "No. Observations:               43739   AIC:                         3.174e+05\n", "Df Residuals:                   43735   BIC:                         3.175e+05\n", "Df Model:                           3                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           6.0638      0.062     97.786      0.000       5.942       6.185\n", "post_treatment                 -3.3273      0.096    -34.777      0.000      -3.515      -3.140\n", "is_treatment                   -1.3315      0.161     -8.267      0.000      -1.647      -1.016\n", "is_treatment_post_treatment     3.7194      0.248     15.006      0.000       3.234       4.205\n", "==============================================================================\n", "Omnibus:                    80392.627   <PERSON><PERSON><PERSON>-<PERSON>:                   0.472\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):        243528870.555\n", "Skew:                          13.469   Prob(JB):                         0.00\n", "Kurtosis:                     367.556   Cond. No.                         7.13\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\"\"\""]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["model.summary()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>diffs_per_engineer</th>\n", "      <th>time</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>is_treated</th>\n", "      <th>O_it</th>\n", "      <th>...</th>\n", "      <th>I_it</th>\n", "      <th>sum_I_it_last_12_weeks</th>\n", "      <th>sum_pr_throughput_last_12_weeks</th>\n", "      <th>feature_relative_increase</th>\n", "      <th>feature_magnitude</th>\n", "      <th>feature_sigmod</th>\n", "      <th>relative_time_weeks</th>\n", "      <th>is_treatment</th>\n", "      <th>post_treatment</th>\n", "      <th>is_treatment_post_treatment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>2020-01-13</td>\n", "      <td>4.0</td>\n", "      <td>0.8</td>\n", "      <td>0 days 11:45:55</td>\n", "      <td>4.0</td>\n", "      <td>2020-01-13</td>\n", "      <td>489</td>\n", "      <td>0</td>\n", "      <td>4.0</td>\n", "      <td>...</td>\n", "      <td>0.916291</td>\n", "      <td>-4.996004e-16</td>\n", "      <td>15.0</td>\n", "      <td>-4.996004e-16</td>\n", "      <td>15.0</td>\n", "      <td>1.000000</td>\n", "      <td>-10</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>2020-01-20</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1 days 01:13:07</td>\n", "      <td>1.0</td>\n", "      <td>2020-01-20</td>\n", "      <td>490</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>-0.916291</td>\n", "      <td>9.162907e-01</td>\n", "      <td>18.0</td>\n", "      <td>9.162907e-01</td>\n", "      <td>18.0</td>\n", "      <td>1.000000</td>\n", "      <td>-9</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>2020-01-27</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>0 days 15:25:31</td>\n", "      <td>3.0</td>\n", "      <td>2020-01-27</td>\n", "      <td>491</td>\n", "      <td>0</td>\n", "      <td>3.0</td>\n", "      <td>...</td>\n", "      <td>0.693147</td>\n", "      <td>6.931472e-01</td>\n", "      <td>19.0</td>\n", "      <td>6.931472e-01</td>\n", "      <td>19.0</td>\n", "      <td>1.000000</td>\n", "      <td>-8</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>2020-02-03</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>1 days 06:09:24.666666666</td>\n", "      <td>3.0</td>\n", "      <td>2020-02-03</td>\n", "      <td>492</td>\n", "      <td>0</td>\n", "      <td>3.0</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>1.386294e+00</td>\n", "      <td>22.0</td>\n", "      <td>1.386294e+00</td>\n", "      <td>22.0</td>\n", "      <td>1.000000</td>\n", "      <td>-7</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>2020-02-17</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2020-02-17</td>\n", "      <td>494</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>-1.386294</td>\n", "      <td>2.876821e-01</td>\n", "      <td>23.0</td>\n", "      <td>2.876821e-01</td>\n", "      <td>23.0</td>\n", "      <td>1.000000</td>\n", "      <td>-5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43734</th>\n", "      <td>actix/actix</td>\n", "      <td>2022-10-24</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>0 days 10:13:26</td>\n", "      <td>3.0</td>\n", "      <td>2022-10-24</td>\n", "      <td>634</td>\n", "      <td>0</td>\n", "      <td>3.0</td>\n", "      <td>...</td>\n", "      <td>1.386294</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>11.0</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>11.0</td>\n", "      <td>0.999950</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43735</th>\n", "      <td>actix/actix</td>\n", "      <td>2023-01-23</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0 days 00:48:33</td>\n", "      <td>1.0</td>\n", "      <td>2023-01-23</td>\n", "      <td>647</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>-0.693147</td>\n", "      <td>1.386294e+00</td>\n", "      <td>14.0</td>\n", "      <td>1.386294e+00</td>\n", "      <td>14.0</td>\n", "      <td>1.000000</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43736</th>\n", "      <td>2dust/v2rayn</td>\n", "      <td>2021-06-28</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2 days 22:39:00</td>\n", "      <td>1.0</td>\n", "      <td>2021-06-28</td>\n", "      <td>565</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>0.693147</td>\n", "      <td>-4.440892e-16</td>\n", "      <td>12.0</td>\n", "      <td>-4.440892e-16</td>\n", "      <td>12.0</td>\n", "      <td>0.999994</td>\n", "      <td>-13</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43737</th>\n", "      <td>2dust/v2rayn</td>\n", "      <td>2021-08-23</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2021-08-23</td>\n", "      <td>573</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>-0.693147</td>\n", "      <td>6.931472e-01</td>\n", "      <td>13.0</td>\n", "      <td>6.931472e-01</td>\n", "      <td>13.0</td>\n", "      <td>0.999999</td>\n", "      <td>-5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43738</th>\n", "      <td>2dust/v2rayn</td>\n", "      <td>2021-09-27</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>37 days 11:20:17</td>\n", "      <td>1.0</td>\n", "      <td>2021-09-27</td>\n", "      <td>578</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>0.693147</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>11.0</td>\n", "      <td>-1.098612e+00</td>\n", "      <td>11.0</td>\n", "      <td>0.999950</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>43739 rows × 21 columns</p>\n", "</div>"], "text/plain": ["                        repo_name    datetime  pr_throughput  \\\n", "0      10up/autoshare-for-twitter  2020-01-13            4.0   \n", "1      10up/autoshare-for-twitter  2020-01-20            1.0   \n", "2      10up/autoshare-for-twitter  2020-01-27            3.0   \n", "3      10up/autoshare-for-twitter  2020-02-03            3.0   \n", "4      10up/autoshare-for-twitter  2020-02-17            0.0   \n", "...                           ...         ...            ...   \n", "43734                 actix/actix  2022-10-24            3.0   \n", "43735                 actix/actix  2023-01-23            1.0   \n", "43736                2dust/v2rayn  2021-06-28            1.0   \n", "43737                2dust/v2rayn  2021-08-23            0.0   \n", "43738                2dust/v2rayn  2021-09-27            1.0   \n", "\n", "       pull_request_success_rate              time_to_merge  \\\n", "0                            0.8            0 days 11:45:55   \n", "1                            1.0            1 days 01:13:07   \n", "2                            1.0            0 days 15:25:31   \n", "3                            1.0  1 days 06:09:24.666666666   \n", "4                            1.0                        NaN   \n", "...                          ...                        ...   \n", "43734                        1.0            0 days 10:13:26   \n", "43735                        1.0            0 days 00:48:33   \n", "43736                        1.0            2 days 22:39:00   \n", "43737                        1.0                        NaN   \n", "43738                        1.0           37 days 11:20:17   \n", "\n", "       diffs_per_engineer        time  standardized_time_weeks  is_treated  \\\n", "0                     4.0  2020-01-13                      489           0   \n", "1                     1.0  2020-01-20                      490           0   \n", "2                     3.0  2020-01-27                      491           0   \n", "3                     3.0  2020-02-03                      492           0   \n", "4                     0.0  2020-02-17                      494           0   \n", "...                   ...         ...                      ...         ...   \n", "43734                 3.0  2022-10-24                      634           0   \n", "43735                 1.0  2023-01-23                      647           1   \n", "43736                 1.0  2021-06-28                      565           0   \n", "43737                 0.0  2021-08-23                      573           0   \n", "43738                 1.0  2021-09-27                      578           0   \n", "\n", "       O_it  ...      I_it  sum_I_it_last_12_weeks  \\\n", "0       4.0  ...  0.916291           -4.996004e-16   \n", "1       1.0  ... -0.916291            9.162907e-01   \n", "2       3.0  ...  0.693147            6.931472e-01   \n", "3       3.0  ...  0.000000            1.386294e+00   \n", "4       0.0  ... -1.386294            2.876821e-01   \n", "...     ...  ...       ...                     ...   \n", "43734   3.0  ...  1.386294           -1.098612e+00   \n", "43735   1.0  ... -0.693147            1.386294e+00   \n", "43736   1.0  ...  0.693147           -4.440892e-16   \n", "43737   0.0  ... -0.693147            6.931472e-01   \n", "43738   1.0  ...  0.693147           -1.098612e+00   \n", "\n", "       sum_pr_throughput_last_12_weeks  feature_relative_increase  \\\n", "0                                 15.0              -4.996004e-16   \n", "1                                 18.0               9.162907e-01   \n", "2                                 19.0               6.931472e-01   \n", "3                                 22.0               1.386294e+00   \n", "4                                 23.0               2.876821e-01   \n", "...                                ...                        ...   \n", "43734                             11.0              -1.098612e+00   \n", "43735                             14.0               1.386294e+00   \n", "43736                             12.0              -4.440892e-16   \n", "43737                             13.0               6.931472e-01   \n", "43738                             11.0              -1.098612e+00   \n", "\n", "       feature_magnitude  feature_sigmod  relative_time_weeks  is_treatment  \\\n", "0                   15.0        1.000000                  -10             1   \n", "1                   18.0        1.000000                   -9             1   \n", "2                   19.0        1.000000                   -8             1   \n", "3                   22.0        1.000000                   -7             1   \n", "4                   23.0        1.000000                   -5             1   \n", "...                  ...             ...                  ...           ...   \n", "43734               11.0        0.999950                    0             0   \n", "43735               14.0        1.000000                   13             0   \n", "43736               12.0        0.999994                  -13             0   \n", "43737               13.0        0.999999                   -5             0   \n", "43738               11.0        0.999950                    0             0   \n", "\n", "       post_treatment  is_treatment_post_treatment  \n", "0                   0                            0  \n", "1                   0                            0  \n", "2                   0                            0  \n", "3                   0                            0  \n", "4                   0                            0  \n", "...               ...                          ...  \n", "43734               0                            0  \n", "43735               1                            0  \n", "43736               0                            0  \n", "43737               0                            0  \n", "43738               0                            0  \n", "\n", "[43739 rows x 21 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["final_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "disengagement", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}