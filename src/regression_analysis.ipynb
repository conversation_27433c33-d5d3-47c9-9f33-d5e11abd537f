{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import os\n", "import logging\n", "from sklearn.preprocessing import MinMaxScaler, StandardScaler\n", "from sklearn.neighbors import NearestNeighbors\n", "import statsmodels.api as sm\n", "import matplotlib.pyplot as plt\n", "\n", "# set up logging\n", "log_dir = \"../logs\"\n", "os.makedirs(log_dir, exist_ok=True)\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s\",\n", "    handlers=[\n", "        logging.FileHandler(os.path.join(log_dir, \"psm_2025_0121.log\")),\n", "        logging.StreamHandler(),\n", "    ],\n", ")\n", "\n", "# set output directory\n", "output_dir = \"../result/standardized_productivity_20250121/\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# set save data function\n", "def save_data(data, filename):\n", "    \"\"\"Save data to a CSV file.\"\"\"\n", "    filepath = os.path.join(output_dir, filename)\n", "    data.to_csv(filepath, index=False)\n", "    logging.info(f\"Saved {filepath}\")"]}], "metadata": {"kernelspec": {"display_name": "disengagement", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}