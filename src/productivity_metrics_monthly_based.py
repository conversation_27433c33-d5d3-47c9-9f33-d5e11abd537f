import logging
import pandas as pd
from datetime import datetime
from pandas.tseries.offsets import DateOffset  # Replaced relativedelta with DateOffset
import os
import time  # Added to track elapsed time

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/generate_csv_pr.log", mode="w", encoding="utf-8"),
    ],
)


def generate_time_series(pr_data, comment_data, time_window_months, repo_name):
    """
    Generate time series data for PR productivity metrics with monthly granularity.

    Args:
        pr_data (DataFrame): Pull request data.
        comment_data (DataFrame): Pull request comments data.
        time_window_months (int): The size of the rolling time window in months.

    Returns:
        DataFrame: A time series of monthly productivity metrics.
    """
    # logging.debug("Starting generate_time_series function.")

    # Convert time columns to datetime type and floor to month start ('MS') to get 'yyyy-mm'
    logging.debug("Converting 'created_at' and 'merged_at' columns to datetime and flooring to 'MS'.")
    pr_data['created_at'] = pd.to_datetime(pr_data['created_at'], errors='coerce').dt.to_period('M').dt.to_timestamp()
    pr_data['merged_at'] = pd.to_datetime(pr_data['merged_at'], errors='coerce').dt.to_period('M').dt.to_timestamp()
    comment_data['created_at'] = pd.to_datetime(comment_data['created_at'], errors='coerce').dt.to_period('M').dt.to_timestamp()

    # Handle possible NaT after conversion
    # pr_data['created_at'].fillna(pd.Timestamp('1970-01-01'), inplace=True)
    # comment_data['created_at'].fillna(pd.Timestamp('1970-01-01'), inplace=True)

    # Set the start and end dates based on the PR data
    start_date = pr_data['created_at'].min()
    end_date = pr_data['created_at'].max()
    logging.info(f"Time series will range from {start_date.date()} to {end_date.date()}.")

    # Create a monthly date range
    date_range = pd.date_range(start=start_date, end=end_date, freq='MS')  # 'MS' stands for Month Start

    # Precompute the earliest comment time for each PR
    logging.debug("Grouping comments to find the earliest comment time for each PR.")
    first_comments = comment_data.groupby('pr_id')['created_at'].min().reset_index()
    pr_data = pr_data.merge(
        first_comments,
        how='left',
        left_on='number',
        right_on='pr_id',
        suffixes=('_pr', '_comment')
    )

    # Calculate time differences in hours
    logging.debug("Calculating time to first reaction and time to merge in hours.")
    pr_data['time_to_first_reaction'] = (pr_data['created_at_comment'] - pr_data['created_at_pr']).dt.total_seconds() / 3600
    pr_data['time_to_merge'] = (pr_data['merged_at'] - pr_data['created_at_pr']).dt.total_seconds() / 3600

    # Do NOT fill missing values with 0 to exclude them from averages
    # pr_data['time_to_first_reaction'] = pr_data['time_to_first_reaction'].fillna(0)
    # pr_data['time_to_merge'] = pr_data['time_to_merge'].fillna(0)

    # Set the PR creation time as the index for efficient slicing
    pr_data.set_index('created_at_pr', inplace=True)
    pr_data.sort_index(inplace=True)  # Ensure the index is sorted

    # Initialize the list to store metrics
    time_series_metrics = []
    logging.debug("Initialized time_series_metrics list.")

    for current_date in date_range:
        try:
            window_start = current_date - DateOffset(months=time_window_months)
            logging.debug(f"Processing month: {current_date.strftime('%Y-%m')} with window start: {window_start.strftime('%Y-%m')}.")

            # Ensure window_start does not go before start_date
            if window_start < start_date:
                window_start = start_date
                logging.debug(f"Adjusted window_start to start_date: {window_start.strftime('%Y-%m')}.")

            # Filter PRs within the current time window
            window_prs = pr_data.loc[window_start:current_date]
            if window_prs.empty:
                logging.debug(f"No PRs found in window from {window_start.strftime('%Y-%m')} to {current_date.strftime('%Y-%m')}.")
                # add empty value to time_series_metrics
                time_series_metrics.append(
                    {
                        'repo_name': repo_name,
                        "Date": current_date.strftime('%Y-%m'),
                        "NUM_PR": 0,
                        "NUM_MERGED_PR": 0,
                        "ACCEPT_RATE": 0,
                        "AVG_TIME_TO_REACT": 0,
                        "AVG_TIME_TO_MERGE": 0,
                    }
                )
                continue


            total_prs = len(window_prs)
            merged_prs = window_prs[window_prs['merged_at'].notna()]
            merged_count = len(merged_prs)
            acceptance_rate = (merged_count / total_prs * 100) if total_prs > 0 else 0

            logging.debug(
                f"Month: {current_date.strftime('%Y-%m')}, Total PRs: {total_prs}, Merged PRs: {merged_count}, Acceptance Rate: {acceptance_rate:.2f}%."
            )

            # Calculate average times
            # Only include PRs with comments for time_to_first_reaction
            prs_with_comments = window_prs['time_to_first_reaction'].notna()
            avg_time_to_first_reaction = window_prs.loc[prs_with_comments, 'time_to_first_reaction'].mean() if prs_with_comments.any() else None
            count_prs_with_comments = prs_with_comments.sum()

            # Only include merged PRs for time_to_merge
            prs_merged = window_prs['merged_at'].notna()
            avg_time_to_merge = window_prs.loc[prs_merged, 'time_to_merge'].mean() if prs_merged.any() else None
            count_prs_merged = prs_merged.sum()

            if avg_time_to_first_reaction is not None:
                logging.debug(f"Avg Time to React: {avg_time_to_first_reaction:.2f} hours over {count_prs_with_comments} PRs with comments.")
            else:
                logging.debug("No PRs with comments in this window for Avg Time to React.")

            if avg_time_to_merge is not None:
                logging.debug(f"Avg Time to Merge: {avg_time_to_merge:.2f} hours over {count_prs_merged} merged PRs.")
            else:
                logging.debug("No merged PRs in this window for Avg Time to Merge.")

            # Assuming 'repo_name' is present in pr_data; adjust if necessary

            time_series_metrics.append({
                'repo_name': repo_name,
                "Date": current_date.strftime('%Y-%m'),
                "NUM_PR": total_prs,
                "NUM_MERGED_PR": merged_count,
                "ACCEPT_RATE": acceptance_rate,
                "AVG_TIME_TO_REACT": avg_time_to_first_reaction,
                "AVG_TIME_TO_MERGE": avg_time_to_merge,
            })
        except Exception as e:
            logging.error(f"Error processing month {current_date.strftime('%Y-%m')}: {e}")

    logging.debug("Completed generating time series metrics.")
    return pd.DataFrame(time_series_metrics)


def main():
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logging.info("Starting main function.")

    sample_projects_path = os.path.join('..', 'data', 'sample_projects_quartiles.csv')
    error_repos = []

    # Record the start time
    start_time = time.time()

    # Read sample projects
    try:
        logging.info(f"Reading sample projects from {sample_projects_path}.")
        sample_projects = pd.read_csv(sample_projects_path)
        logging.info("Successfully read sample projects.")
    except Exception as e:
        logging.error(f"Failed to read sample_projects file: {e}")
        return

    repo_names = sample_projects["name"].tolist()
    total_repos = len(repo_names)
    logging.info(f"Found {total_repos} repositories to process.")

    for idx, repo_name in enumerate(repo_names, 1):
        # Calculate elapsed time
        elapsed_seconds = time.time() - start_time
        elapsed_time = time.strftime("%H:%M:%S", time.gmtime(elapsed_seconds))
        logging.info(f"Processing repository {idx}/{total_repos}: {repo_name}. Elapsed time: {elapsed_time}.")

        pr_comments_path = os.path.join('../data/pr_comments', f"{repo_name.replace('/', '_')}_pr_comments.csv")
        pull_requests_path = os.path.join('../data/pullrequests', f"{repo_name.replace('/', '_')}_pull_requests.csv")

        # Read PR comments
        try:
            logging.info(f"Reading PR comments from {pr_comments_path}.")
            pr_comments = pd.read_csv(pr_comments_path)
            logging.info(f"Successfully read PR comments for {repo_name}.")
        except Exception as e:
            logging.error(f"Failed to read PR comments file {pr_comments_path}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to read PR comments file: {e}"})
            continue

        # Read Pull Requests
        try:
            logging.info(f"Reading Pull Requests from {pull_requests_path}.")
            pull_requests = pd.read_csv(pull_requests_path)
            logging.info(f"Successfully read Pull Requests for {repo_name}.")
        except Exception as e:
            logging.error(f"Failed to read Pull Requests file {pull_requests_path}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to read Pull Requests file: {e}"})
            continue

        time_window = 1  # Rolling time window in months
        logging.info(f"Using a time window of {time_window} months for {repo_name}.")

        # Check if PR data is empty
        if pull_requests.empty:
            error_repos.append({"repo_name": repo_name, "reason": "PR data is empty"})
            logging.warning(f"{repo_name}'s PR data is empty. Skipping.")
            continue

        # Check if comments data is empty
        if pr_comments.empty:
            error_repos.append({"repo_name": repo_name, "reason": "PR comments data is empty"})
            logging.warning(f"{repo_name}'s PR comments data is empty. Skipping.")
            continue

        # Generate time series data
        try:
            logging.info(f"Generating time series data for {repo_name}.")
            time_series_data = generate_time_series(pull_requests, pr_comments, time_window, repo_name)
            logging.info(f"Successfully generated time series data for {repo_name}.")
        except Exception as e:
            logging.error(f"Failed to generate time series data for {repo_name}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to generate time series data: {e}"})
            continue

        # Save time series data
        output_dir = os.path.join('..', 'result', 'productivity', 'month_based')
        os.makedirs(output_dir, exist_ok=True)
        output_csv_path = os.path.join(output_dir, f"{repo_name.replace('/', '_')}_pr_productivity_metrics.csv")

        try:
            logging.info(f"Saving time series data to {output_csv_path}.")
            time_series_data.to_csv(output_csv_path, index=False, encoding="utf-8")
            logging.info(f"Generated productivity metrics CSV: {output_csv_path}")
        except Exception as e:
            logging.error(f"Failed to save productivity metrics CSV for {repo_name}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to save productivity metrics CSV: {e}"})
            continue

    # Save error repositories information
    if error_repos:
        error_repos_df = pd.DataFrame(error_repos)
        error_repos_csv_path = os.path.join('..', 'result', 'productivity', 'month_based', 'error_repos.csv')
        try:
            os.makedirs(os.path.dirname(error_repos_csv_path), exist_ok=True)
            logging.info(f"Saving error repositories information to {error_repos_csv_path}.")
            error_repos_df.to_csv(error_repos_csv_path, index=False, encoding="utf-8")
            logging.info(f"Generated error repositories CSV: {error_repos_csv_path}")
        except Exception as e:
            logging.error(f"Failed to save error repositories CSV: {e}")
    else:
        logging.info("No error repositories to record.")

    # Calculate and log total elapsed time
    total_elapsed_seconds = time.time() - start_time
    total_elapsed_time = time.strftime("%H:%M:%S", time.gmtime(total_elapsed_seconds))
    logging.info(f"Main function completed. Total elapsed time: {total_elapsed_time}.")


if __name__ == "__main__":
    main()
