import pandas as pd
from pymongo import MongoClient
import requests
import time
import logging
from logging.handlers import RotatingFileHandler
from concurrent.futures import ThreadPoolExecutor

# MongoDB Configuration
client = MongoClient("mongodb://localhost:27017/")
db = client["disengagement_total"]
commits_collection = db["commits"]
pull_requests_collection = db["pull_requests"]
pr_comments_collection = db["pr_comments"]
cache_collection = db["progress_cache"]

# Logging Configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = RotatingFileHandler('fetch_commits.log', maxBytes=10 * 1024 * 1024, backupCount=5)
handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# GitHub API Tokens
github_tokens = [
    '*********************************************************************************************',
    '*********************************************************************************************',
    '*********************************************************************************************',
]

# Ensure Unique Indexes
def ensure_unique_indexes():
    commits_collection.create_index([("sha", 1)], unique=True)
    pull_requests_collection.create_index([("number", 1)], unique=True)
    pr_comments_collection.create_index([("id", 1)], unique=True)
    logger.info("Unique indexes ensured for collections.")

# Load progress from MongoDB
def load_progress_cache(repo):
    cache = cache_collection.find_one({"repo_name": repo})
    if cache:
        return cache
    else:
        return {
            "repo_name": repo,
            "commit_page": 0,
            "pull_request_page": 0,
            "pr_comment_page": 0,
            "commit_finished": False,
            "pull_request_finished": False,
            "pr_comment_finished": False
        }

# Save progress to MongoDB
def save_progress_cache(repo, commit_page=None, pull_request_page=None, pr_comment_page=None,
                        commit_finished=None, pull_request_finished=None, pr_comment_finished=None):
    update = {}
    if commit_page is not None:
        update["commit_page"] = commit_page
    if pull_request_page is not None:
        update["pull_request_page"] = pull_request_page
    if pr_comment_page is not None:
        update["pr_comment_page"] = pr_comment_page
    if commit_finished is not None:
        update["commit_finished"] = commit_finished
    if pull_request_finished is not None:
        update["pull_request_finished"] = pull_request_finished
    if pr_comment_finished is not None:
        update["pr_comment_finished"] = pr_comment_finished
    cache_collection.update_one(
        {"repo_name": repo},
        {"$set": update},
        upsert=True
    )
    logger.info(f"Progress cache updated for {repo}.")

# Check if repository exists
def repo_exists(repo, tokens):
    url = f"https://api.github.com/repos/{repo}"
    headers = {'Accept': 'application/vnd.github.v3+json'}
    for token in tokens:
        headers['Authorization'] = f'token {token}'
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return True
        except requests.exceptions.HTTPError as http_err:
            if response.status_code == 403:
                logger.warning(f"Token {token} rate limited for {repo}.")
                continue
            elif response.status_code == 404:
                logger.warning(f"Repository {repo} not found.")
                return False
            else:
                logger.error(f"HTTP error {http_err} for {repo}.")
                return False
    return False

# Estimate total pages for pull requests
def estimate_pr_pages(repo, tokens):
    url = f"https://api.github.com/repos/{repo}/pulls?per_page=1&state=all"
    headers = {'Accept': 'application/vnd.github.v3+json'}
    for token in tokens:
        headers['Authorization'] = f'token {token}'
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            links = response.headers.get('Link', '')
            if 'rel="last"' in links:
                last_page_url = [link for link in links.split(',') if 'rel="last"' in link][0]
                last_page_url = last_page_url.split(';')[0].strip()
                last_page_url = last_page_url.strip('<>')
                page_param = last_page_url.split('page=')[-1]
                total_pages = int(page_param)
                return total_pages
            else:
                return 1
        except requests.exceptions.HTTPError as http_err:
            if response.status_code == 403:
                logger.warning(f"Token {token} rate limited for {repo}.")
                continue
            else:
                logger.error(f"HTTP error {http_err} for {repo}.")
                return -1
    return -1

# Fetch Commits
def fetch_commits(repo, tokens):
    base_url = f"https://api.github.com/repos/{repo}/commits"
    cache = load_progress_cache(repo)
    page = cache.get("commit_page", 1)
    commits_fetched = 0
    token_reset_times = {}

    logger.info(f"Starting to fetch commits for {repo} from page {page}")
    while True:
        url = f"{base_url}?page={page}&per_page=100"
        available_tokens = [t for t in tokens if t not in token_reset_times or token_reset_times[t] <= time.time()]

        if not available_tokens:
            min_wait = min(token_reset_times.values()) - time.time() if token_reset_times else 0
            if min_wait > 0:
                logger.warning(f"All tokens rate limited, waiting for {min_wait:.1f} seconds...")
                time.sleep(min_wait)
            available_tokens = tokens
            token_reset_times.clear()

        success = False
        for token in available_tokens:
            headers = {
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                commits = response.json()
                success = True
                break
            except requests.exceptions.HTTPError as http_err:
                if response.status_code == 403:
                    reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                    token_reset_times[token] = reset_time
                    logger.warning(f"Token {token} rate limited until {reset_time}")
                    continue
                else:
                    logger.error(f"HTTP error {http_err} for {repo} with token {token}.")
                    return

        if not success:
            logger.error(f"Failed to fetch commits for {repo} at page {page}.")
            break

        if not commits:
            logger.info(f"No more commits to fetch for {repo}.")
            save_progress_cache(repo, commit_finished=True)
            break

        for commit in commits:
            commit['repo_name'] = repo
            try:
                commits_collection.update_one({'sha': commit['sha']}, {'$set': commit}, upsert=True)
            except Exception as e:
                logger.error(f"Failed to save commit to MongoDB: {e}")
                continue
        commits_fetched += len(commits)
        save_progress_cache(repo, commit_page=page)
        logger.info(f"Saved {len(commits)} commits for {repo} to MongoDB, page {page}.")
        page += 1

    logger.info(f"Finished fetching commits for {repo}, total fetched: {commits_fetched}")

# Fetch Pull Requests
def fetch_pull_requests(repo, tokens):
    base_url = f"https://api.github.com/repos/{repo}/pulls"
    cache = load_progress_cache(repo)
    page = cache.get("pull_request_page", 1)
    prs_fetched = 0
    token_reset_times = {}
    total_pr_pages = estimate_pr_pages(repo, tokens)

    logger.info(f"Starting to fetch pull requests for {repo} from page {page}, total pages: {total_pr_pages}")
    while page <= total_pr_pages:
        url = f"{base_url}?page={page}&per_page=100&state=all"
        available_tokens = [t for t in tokens if t not in token_reset_times or token_reset_times[t] <= time.time()]

        if not available_tokens:
            min_wait = min(token_reset_times.values()) - time.time() if token_reset_times else 0
            if min_wait > 0:
                logger.warning(f"All tokens rate limited, waiting for {min_wait:.1f} seconds...")
                time.sleep(min_wait)
            available_tokens = tokens
            token_reset_times.clear()

        success = False
        for token in available_tokens:
            headers = {
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                pull_requests = response.json()
                success = True
                break
            except requests.exceptions.HTTPError as http_err:
                if response.status_code == 403:
                    reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                    token_reset_times[token] = reset_time
                    logger.warning(f"Token {token} rate limited until {reset_time}")
                    continue
                else:
                    logger.error(f"HTTP error {http_err} for {repo} with token {token}.")
                    return

        if not success:
            logger.error(f"Failed to fetch pull requests for {repo} at page {page}.")
            break

        if not pull_requests:
            logger.info(f"No more pull requests to fetch for {repo}.")
            save_progress_cache(repo, pull_request_finished=True)
            break

        for pr in pull_requests:
            pr['repo_name'] = repo
            try:
                pull_requests_collection.update_one({'number': pr['number']}, {'$set': pr}, upsert=True)
            except Exception as e:
                logger.error(f"Failed to save pull request to MongoDB: {e}")
                continue
        prs_fetched += len(pull_requests)
        save_progress_cache(repo, pull_request_page=page)
        logger.info(f"Saved {len(pull_requests)} pull requests for {repo} to MongoDB, page {page}/{total_pr_pages}.")
        page += 1

    logger.info(f"Finished fetching pull requests for {repo}, total fetched: {prs_fetched}")

# Fetch Pull Request Review Comments
def fetch_pr_review_comments(repo, tokens):
    base_url = f"https://api.github.com/repos/{repo}/pulls/comments"
    cache = load_progress_cache(repo)
    page = cache.get("pr_comment_page", 1)
    comments_fetched = 0
    token_reset_times = {}

    logger.info(f"Starting to fetch pull request review comments for {repo} from page {page}")
    while True:
        url = f"{base_url}?page={page}&per_page=100"
        available_tokens = [t for t in tokens if t not in token_reset_times or token_reset_times[t] <= time.time()]

        if not available_tokens:
            min_wait = min(token_reset_times.values()) - time.time() if token_reset_times else 0
            if min_wait > 0:
                logger.warning(f"All tokens rate limited, waiting for {min_wait:.1f} seconds...")
                time.sleep(min_wait)
            available_tokens = tokens
            token_reset_times.clear()

        success = False
        for token in available_tokens:
            headers = {
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                comments = response.json()
                success = True
                break
            except requests.exceptions.HTTPError as http_err:
                if response.status_code == 403:
                    reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                    token_reset_times[token] = reset_time
                    logger.warning(f"Token {token} rate limited until {reset_time}")
                    continue
                else:
                    logger.error(f"HTTP error {http_err} for {repo} with token {token}.")
                    return

        if not success:
            logger.error(f"Failed to fetch pull request review comments for {repo} at page {page}.")
            break

        if not comments:
            logger.info(f"No more pull request review comments to fetch for {repo}.")
            save_progress_cache(repo, pr_comment_finished=True)
            break

        for comment in comments:
            comment['repo_name'] = repo
            try:
                pr_comments_collection.update_one({'id': comment['id']}, {'$set': comment}, upsert=True)
            except Exception as e:
                logger.error(f"Failed to save pull request comment to MongoDB: {e}")
                continue
        comments_fetched += len(comments)
        save_progress_cache(repo, pr_comment_page=page)
        logger.info(f"Saved {len(comments)} pull request review comments for {repo} to MongoDB, page {page}.")
        page += 1

    logger.info(f"Finished fetching pull request review comments for {repo}, total fetched: {comments_fetched}")

# Process a single repository
def process_repo(project):
    if not repo_exists(project, github_tokens):
        logger.warning(f"Skipping {project}, repository does not exist or is inaccessible.")
        return

    cache = load_progress_cache(project)

    # Fetch commits if not finished
    if not cache.get("commit_finished", False):
        fetch_commits(project, github_tokens)

    # Fetch pull requests if not finished
    if not cache.get("pull_request_finished", False):
        fetch_pull_requests(project, github_tokens)

    # Fetch pull request review comments if not finished
    if not cache.get("pr_comment_finished", False):
        fetch_pr_review_comments(project, github_tokens)

    # Mark project as fully fetched if all parts are finished
    if cache.get("commit_finished", False) and \
       cache.get("pull_request_finished", False) and \
       cache.get("pr_comment_finished", False):
        save_progress_cache(project, commit_finished=True, pull_request_finished=True, pr_comment_finished=True)
        logger.info(f"Completed fetching all data for {project}.")

# Main Function
def main():
    ensure_unique_indexes()
    # Load sample projects
    sample_projects = pd.read_csv('../data/sample_projects_total.csv')
    project_names = sample_projects['name'].tolist()

    with ThreadPoolExecutor(max_workers=5) as executor:
        for project in project_names:
            executor.submit(process_repo, project)

if __name__ == "__main__":
    main()