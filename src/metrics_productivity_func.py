import pandas as pd
from pymongo import MongoClient
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from pymongo import MongoClient
from datetime import datetime
# add loggings
import logging


logging.basicConfig(level=logging.INFO)
class PullRequestMetrics:
    def __init__(self, repo_name, time_unit, db_name='disengagement', collection_pr='pull_requests_202501', collection_comments='pr_comments'):
        self.repo_name = repo_name if isinstance(repo_name, list) else [repo_name]
        self.time_unit = time_unit
        self.freq_mapping = {
            'weekly': 'W',
            'biweekly': '2W',
            'monthly': 'M'
        }
        self.client = MongoClient('localhost', 27017)
        self.db = self.client[db_name]
        self.collection_pr = self.db[collection_pr]
        self.collection_comments = self.db[collection_comments]

    def fetch_pull_requests(self):
        query = {'repo_name': {'$in': self.repo_name}} if self.repo_name else {}
        pr_data = list(self.collection_pr.find(query))
        return pr_data

    def fetch_comments(self):
        query = {'repo_name': {'$in': self.repo_name}} if self.repo_name else {}
        comments_data = list(self.collection_comments.find(query))
        return comments_data

    def process_pull_requests(self, pr_data):
        df_pr = pd.DataFrame(pr_data)
        date_cols = ['created_at', 'merged_at', 'closed_at']
        for col in date_cols:
            df_pr[col] = pd.to_datetime(df_pr.get(col, pd.NaT), errors='coerce')
            # Remove timezone information
            df_pr[col] = df_pr[col].dt.tz_localize(None)
        df_pr['user_login'] = df_pr['user'].map(lambda x: x.get('login') if isinstance(x, dict) else None)
        # df_pr['user_type'] = df_pr['user'].map(lambda x: x.get('type') if isinstance(x, dict) else None)
        # judgge pr status by evaluating the 'merged_at' and 'closed_at' columns
        # if merged_at is not NaT, then the pr is merged
        # if merged_at is NaT and closed_at is not NaT, then the pr is closed without merge
        # if both merged_at and closed_at are NaT, then the pr is open
        df_pr['pr_status'] = 'open'
        df_pr.loc[df_pr['merged_at'].notnull(), 'pr_status'] = 'merged'
        df_pr.loc[(df_pr['merged_at'].isnull()) & (df_pr['closed_at'].notnull()), 'pr_status'] = 'closed_without_merge'

        df_pr['number'] = df_pr['number'].astype(int)
        # show the statistics of pr about number of merged, closed and open prs and total prs
        logging.info(f"Number of merged PRs: {df_pr[df_pr['pr_status'] == 'merged'].shape[0]}")
        logging.info(f"Number of closed PRs without merge: {df_pr[df_pr['pr_status'] == 'closed_without_merge'].shape[0]}")
        logging.info(f"Number of open PRs: {df_pr[df_pr['pr_status'] == 'open'].shape[0]}")
        logging.info(f"Total number of PRs: {df_pr.shape[0]}")
        
        return df_pr[['repo_name', 'created_at', 'merged_at', 'closed_at', 'pr_status', 'user_login', 'number']]

    def process_comments(self, comments_data):
        df_comments = pd.DataFrame(comments_data)
        if 'created_at' not in df_comments.columns:
            df_comments['created_at'] = pd.NaT
        df_comments['created_at'] = pd.to_datetime(df_comments['created_at'], errors='coerce')
        df_comments['created_at'] = df_comments['created_at'].dt.tz_localize(None)
        df_comments['user_login'] = df_comments['user'].map(lambda x: x.get('login') if isinstance(x, dict) else None)
        # Extract PR number from 'pull_request_url'
        df_comments['pr_number'] = df_comments['pull_request_url'].fillna('').str.extract(r'/pull/(\d+)$', expand=False)
        df_comments['pr_number'] = df_comments['pr_number'].replace('', np.nan).astype('Int64')
        return df_comments[['repo_name', 'pr_number', 'created_at', 'user_login']]

    def calculate_pr_throughput(self, df_pr):
        df_merged = df_pr[df_pr['pr_status'] == 'merged']
        freq = self.freq_mapping.get(self.time_unit, 'W')
        df_merged['datetime'] = df_merged['merged_at'].dt.to_period(freq)
        throughput = df_merged.groupby(['repo_name', 'datetime']).size().reset_index(name='pr_throughput')
        return throughput

    def calculate_pull_request_success_rate(self, df_pr):
        df_success = df_pr[df_pr['pr_status'].isin(['merged', 'closed_without_merge'])]
        freq = self.freq_mapping.get(self.time_unit, 'W')
        # Ensure 'created_at' is timezone-naive
        df_success['created_at'] = df_success['created_at'].dt.tz_localize(None)
        df_success['datetime'] = df_success['created_at'].dt.to_period(freq)
        merged_count = df_success[df_success['pr_status'] == 'merged'].groupby(['repo_name', 'datetime']).size().reset_index(name='merged_prs')
        total_count = df_success.groupby(['repo_name', 'datetime']).size().reset_index(name='total_prs')
        success_rate = pd.merge(merged_count, total_count, on=['repo_name', 'datetime'])
        success_rate['pull_request_success_rate'] = success_rate['merged_prs'] / success_rate['total_prs']
        return success_rate[['repo_name', 'datetime', 'pull_request_success_rate']]

    def calculate_time_to_merge(self, df_pr):
        df_merged = df_pr[df_pr['pr_status'] == 'merged']
        # Ensure 'merged_at' and 'created_at' are timezone-naive
        df_merged['merged_at'] = df_merged['merged_at'].dt.tz_localize(None)
        df_merged['created_at'] = df_merged['created_at'].dt.tz_localize(None)
        df_merged['time_to_merge'] = df_merged['merged_at'] - df_merged['created_at']
        freq = self.freq_mapping.get(self.time_unit, 'W')
        df_merged['datetime'] = df_merged['merged_at'].dt.to_period(freq)
        time_to_merge = df_merged.groupby(['repo_name', 'datetime'])['time_to_merge'].mean().reset_index()
        return time_to_merge

    def calculate_diffs_per_engineer(self, df_pr):
        df_merged = df_pr[(df_pr['pr_status'] == 'merged')]
        freq = self.freq_mapping.get(self.time_unit, 'W')
        df_merged['datetime'] = df_merged['merged_at'].dt.to_period(freq)
        diffs = df_merged.groupby(['repo_name', 'datetime']).size().reset_index(name='diffs_per_engineer')
        return diffs

    def calculate_time_to_first_review(self, df_pr, df_comments):
        # Merge PRs and comments based on repo_name and pr_number
        df_merged = pd.merge(df_pr, df_comments, left_on=['repo_name', 'number'], right_on=['repo_name', 'pr_number'], how='left', suffixes=('_pr', '_comment'))
        # Exclude self-reviews
        df_merged = df_merged[df_merged['user_login_pr'] != df_merged['user_login_comment']]
        # Find the earliest review per PR
        df_first_review = df_merged.groupby(['repo_name', 'number'])['created_at_comment'].min().reset_index()
        # Merge back to get PR creation times
        df_pr_first_review = pd.merge(df_pr, df_first_review, on=['repo_name', 'number'], how='left')
        # Calculate time to first review
        df_pr_first_review['time_to_first_review'] = df_pr_first_review['created_at_comment'] - df_pr_first_review['created_at']
        # Exclude negative or null times
        df_pr_first_review = df_pr_first_review[df_pr_first_review['time_to_first_review'].notnull() & (df_pr_first_review['time_to_first_review'] >= pd.Timedelta(0))]
        # Group by time unit and repo_name
        freq = self.freq_mapping.get(self.time_unit, 'W')
        # Ensure 'created_at' is timezone-naive
        df_pr_first_review['created_at'] = df_pr_first_review['created_at'].dt.tz_localize(None)
        # Use .loc to avoid chained assignment
        df_pr_first_review.loc[:, 'datetime'] = df_pr_first_review['created_at'].dt.to_period(freq)
        # Calculate average time to first review
        time_to_first_review = df_pr_first_review.groupby(['repo_name', 'datetime'])['time_to_first_review'].mean().reset_index()
        return time_to_first_review

    def plot_metrics(self, df):
        sns.set(style="whitegrid")
        unique_repos = df['repo_name'].unique()

        for repo in unique_repos:
            repo_df = df[df['repo_name'] == repo]
            metrics = ['pr_throughput', 'pull_request_success_rate', 'time_to_merge', 'diffs_per_engineer', 'time_to_first_review']
            n_metrics = len(metrics)
            fig, axes = plt.subplots(nrows=n_metrics, ncols=1, figsize=(12, 6*n_metrics), sharex=True)
            fig.suptitle(f"Pull Request Metrics for {repo}", fontsize=16)

            for ax, metric in zip(axes, metrics):
                if metric not in repo_df.columns:
                    ax.set_visible(False)
                    continue

                # Ensure datetime is sorted
                repo_df = repo_df.sort_values(by='datetime')

                # Convert timedelta to numeric if necessary
                if metric in ['time_to_merge', 'time_to_first_review']:
                    repo_df[metric] = repo_df[metric].dt.total_seconds() / (24*3600)  # Convert to days

                # Plot
                ax.plot(repo_df['datetime'], repo_df[metric], marker='o', label=metric)

                # Annotations
                if not repo_df[metric].isnull().all():
                    max_val = repo_df[metric].max()
                    max_idx = repo_df[metric].idxmax()
                    ax.annotate(f'Max: {max_val:.2f}', xy=(repo_df.loc[max_idx, 'datetime'], max_val),
                                xytext=(5, 5), textcoords='offset points')

                # Customize subplot
                ax.set_title(metric)
                ax.set_ylabel(metric)
                ax.grid(True)
                ax.legend()
                ax.tick_params(axis='x', rotation=45)

            # Adjust layout
            plt.tight_layout(rect=[0, 0.03, 1, 0.95])
            plt.show()
            
    def generate_timeseries_dataframe(self):
        # Fetch and process PR data
        pr_data = self.fetch_pull_requests()
        df_pr = self.process_pull_requests(pr_data)
        # Fetch and process comments data
        # comments_data = self.fetch_comments()
        # df_comments = self.process_comments(comments_data)
        # Calculate metrics
        throughput = self.calculate_pr_throughput(df_pr)
        # success_rate = self.calculate_pull_request_success_rate(df_pr)
        # time_to_merge = self.calculate_time_to_merge(df_pr)
        # diffs_per_engineer = self.calculate_diffs_per_engineer(df_pr)
        # time_to_first_review = self.calculate_time_to_first_review(df_pr, df_comments)
        # Combine all metrics
        # combined_df = pd.merge(throughput, success_rate, on=['repo_name', 'datetime'], how='outer')
        # combined_df = pd.merge(combined_df, time_to_merge, on=['repo_name', 'datetime'], how='outer')
        # combined_df = pd.merge(combined_df, diffs_per_engineer, on=['repo_name', 'datetime'], how='outer')
        # combined_df = pd.merge(combined_df, time_to_first_review, on=['repo_name', 'datetime'], how='outer')
        # Convert period to datetime
        combined_df = throughput
        combined_df['datetime'] = combined_df['datetime'].dt.start_time
        # Fill NaN in specific numeric columns with zero
        # numeric_fill_cols = ['pr_throughput', 'diffs_per_engineer']

        # combined_df[numeric_fill_cols] = combined_df[numeric_fill_cols].fillna(0)
        # Forward-fill 'pull_request_success_rate' to handle NaN
        combined_df = combined_df.sort_values(by=['repo_name', 'datetime'])
        combined_df['pr_throughput'] = combined_df['pr_throughput'].fillna(0)
        # combined_df['pull_request_success_rate'] = combined_df['pull_request_success_rate'].fillna(method='ffill')
        return combined_df
    
    @staticmethod
    def export_repo_productivity_to_csv(repo_list, time_unit, output_file):
        all_data = []
        for repo_name in repo_list:
            try:
                pr_metrics = PullRequestMetrics(repo_name, time_unit)
                df = pr_metrics.generate_timeseries_dataframe()
                all_data.append(df)
            except Exception as e:
                print(f"Failed to process {repo_name}: {e}")
                continue
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df.to_csv(output_file, index=False)
            print(f"Productivity metrics exported to {output_file}")
        else:
            print("No data to export.")
def main():
  # test the PullRequestMetrics class
#   repo_name = 'duckdb/duckdb'
#   time_unit = 'weekly'
#   pr_metrics = PullRequestMetrics(repo_name, time_unit)
#   timeseries_df = pr_metrics.generate_timeseries_dataframe()
#   # plot the metrics
#   pr_metrics.plot_metrics(timeseries_df)
    repo_info = pd.read_csv('../data/sample_projects_quartiles.csv')
    time_unit = 'weekly'
    output_file = '../result/productivity_metrics_20250228.csv'
    PullRequestMetrics.export_repo_productivity_to_csv(repo_info['repo_name'], time_unit, output_file)
if __name__ == '__main__':
    # do not show warnings in the output
    import warnings
    warnings.filterwarnings("ignore")
    main()