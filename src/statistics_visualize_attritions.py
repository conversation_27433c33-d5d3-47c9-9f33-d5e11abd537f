# import os
# import pandas as pd
# import matplotlib.pyplot as plt
# import logging
#
# # Configure logging
# log_file = '../logs/attrition_analysis.log'
#
# logging.basicConfig(
#     filename=log_file,
#     level=logging.INFO,
#     format="%(asctime)s - %(levelname)s - %(message)s"
# )
#
# def plot_attrition_distribution(attrition_counts):
#     """
#     Plot a distribution of the attrition counts across repositories with revised bin ranges and annotations.
#     """
#     output_plot_path = "../result/figures/attrition_distribution.pdf"
#
#     # Define revised bins for attrition count ranges
#     bins = [0, 1, 2, 5, 10, 20, float('inf')]
#     labels = ["1", "2", "3-5", "6-10", "11-20", "21+"]
#
#     # Create a new column for the bins
#     attrition_counts["count_range"] = pd.cut(
#         attrition_counts["attrition_count"], bins=bins, labels=labels, right=False
#     )
#
#     # Group by bins and count the number of repos in each range
#     distribution = attrition_counts["count_range"].value_counts().sort_index()
#
#     # Calculate percentages
#     # total_repos = distribution.sum()
#     print(distribution.sum())
#     total_repos = 3999
#     percentages = (distribution / total_repos * 100).round(2)
#
#     # Plot
#     plt.figure(figsize=(10, 6))
#     bars = plt.bar(distribution.index, distribution.values, color="skyblue", edgecolor="black")
#
#     # Add annotations for each bar (count and percentage)
#     for bar, count, percentage in zip(bars, distribution.values, percentages.values):
#         plt.text(
#             bar.get_x() + bar.get_width() / 2,  # X position (center of the bar)
#             bar.get_height() + 1,  # Y position (slightly above the bar)
#             f"{count}\n({percentage}%)",  # Text to display
#             ha="center", fontsize=12, color="black"
#         )
#
#     # Customize plot
#     plt.xlabel("Attrition Count Range", fontsize=14)
#     plt.ylabel("Number of Repositories", fontsize=14)
#     plt.title("Attrition Count Distribution", fontsize=16)
#     plt.xticks(fontsize=12)
#     plt.yticks(fontsize=12)
#     plt.grid(axis="y", linestyle="--", alpha=0.7)
#     plt.tight_layout()
#
#     # Save plot
#     plt.savefig(output_plot_path)
#     logging.info(f"Attrition distribution plot saved at {output_plot_path}")
#     plt.show()
#
#     # Save distribution as CSV for further analysis
#     distribution_output_path = "../result/attrition_distribution.csv"
#     try:
#         distribution.to_csv(distribution_output_path, index=True, encoding="utf-8")
#         logging.info(f"Attrition distribution data saved at {distribution_output_path}")
#     except Exception as e:
#         logging.error(f"Failed to save attrition distribution CSV: {e}")
#
# def analyze_and_export_attrition_counts():
#     """
#     Analyze attrition counts for each repository, generate a CSV file,
#     and produce a frequency distribution of attrition counts.
#     """
#     input_csv_path = "../result/attritions.csv"
#     output_csv_path = "../result/attrition_statistics.csv"
#
#     # Load attrition data from the CSV
#     try:
#         df = pd.read_csv(input_csv_path)
#     except Exception as e:
#         logging.error(f"Failed to load attritions CSV: {e}")
#         return None
#
#     if df.empty:
#         logging.warning("No data found in the attritions CSV.")
#         return None
#
#     # Count attritions per repository and sort by count (descending order)
#     attrition_counts = (
#         df.groupby("repo_name")
#         .size()
#         .reset_index(name="attrition_count")
#         .sort_values(by="attrition_count", ascending=False)
#     )
#
#     # Add summary statistics for logging and analysis
#     logging.info("Attrition Counts Summary Statistics:")
#     logging.info(attrition_counts.describe())
#
#     # Export to CSV
#     try:
#         attrition_counts.to_csv(output_csv_path, index=False, encoding="utf-8")
#         logging.info(f"Attrition statistics CSV generated at {output_csv_path}")
#     except Exception as e:
#         logging.error(f"Failed to save attrition statistics CSV: {e}")
#
#     # Return the attrition counts DataFrame for further analysis
#     return attrition_counts
#
# def main():
#     # Analyze attrition counts and export CSV
#     attrition_counts = analyze_and_export_attrition_counts()
#
#     if attrition_counts is not None:
#         # Generate and save attrition distribution plot
#         plot_attrition_distribution(attrition_counts)
#
# if __name__ == "__main__":
#     main()

import os
import pandas as pd
import matplotlib.pyplot as plt
import logging

# Configure logging
log_file = '../logs/attrition_analysis.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def plot_attrition_distribution(attrition_counts):
    """
    Plot a distribution of the attrition counts across repositories with revised bin ranges and annotations.
    """
    output_plot_path = "../result/figures/attrition_distribution.pdf"

    # Define revised bins for attrition count ranges
    bins = [1, 2, 5, 10, 20, float('inf')]
    labels = ["2", "3-5", "6-10", "11-20", "21+"]

    # Create a new column for the bins
    attrition_counts["count_range"] = pd.cut(
        attrition_counts["attrition_count"], bins=bins, labels=labels, right=False
    )

    # Group by bins and count the number of repos in each range
    distribution = attrition_counts["count_range"].value_counts().sort_index()

    # Adjust the '0' category
    total_repos = 3999
    missing_repos = total_repos - distribution.sum()
    distribution.loc["No attrition"] = missing_repos

    # Calculate percentages
    percentages = (distribution / total_repos * 100).round(2)

    # Plot
    plt.figure(figsize=(10, 6))
    bars = plt.bar(distribution.index, distribution.values, color="skyblue", edgecolor="black")

    # Add annotations for each bar (count and percentage)
    for bar, count, percentage in zip(bars, distribution.values, percentages.values):
        plt.text(
            bar.get_x() + bar.get_width() / 2,  # X position (center of the bar)
            bar.get_height() + 1,  # Y position (slightly above the bar)
            f"{count}\n({percentage}%)",  # Text to display
            ha="center", fontsize=12, color="black"
        )

    # Customize plot
    plt.xlabel("Attrition Count Range", fontsize=14)
    plt.ylabel("Number of Repositories", fontsize=14)
    plt.title("Attrition Count Distribution", fontsize=16)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    plt.grid(axis="y", linestyle="--", alpha=0.7)
    plt.tight_layout()

    # Save plot
    plt.savefig(output_plot_path)
    logging.info(f"Attrition distribution plot saved at {output_plot_path}")
    plt.show()

    # Save distribution as CSV for further analysis
    distribution_output_path = "../result/attrition_distribution.csv"
    try:
        distribution.to_csv(distribution_output_path, index=True, encoding="utf-8")
        logging.info(f"Attrition distribution data saved at {distribution_output_path}")
    except Exception as e:
        logging.error(f"Failed to save attrition distribution CSV: {e}")

def analyze_and_export_attrition_counts():
    """
    Analyze attrition counts for each repository, generate a CSV file,
    and produce a frequency distribution of attrition counts.
    """
    input_csv_path = "../result/attritions.csv"
    output_csv_path = "../result/attrition_statistics.csv"

    # Load attrition data from the CSV
    try:
        df = pd.read_csv(input_csv_path)
    except Exception as e:
        logging.error(f"Failed to load attritions CSV: {e}")
        return None

    if df.empty:
        logging.warning("No data found in the attritions CSV.")
        return None

    # Count attritions per repository and sort by count (descending order)
    attrition_counts = (
        df.groupby("repo_name")
        .size()
        .reset_index(name="attrition_count")
        .sort_values(by="attrition_count", ascending=False)
    )

    # Add summary statistics for logging and analysis
    logging.info("Attrition Counts Summary Statistics:")
    logging.info(attrition_counts.describe())

    # Export to CSV
    try:
        attrition_counts.to_csv(output_csv_path, index=False, encoding="utf-8")
        logging.info(f"Attrition statistics CSV generated at {output_csv_path}")
    except Exception as e:
        logging.error(f"Failed to save attrition statistics CSV: {e}")

    # Return the attrition counts DataFrame for further analysis
    return attrition_counts

def main():
    # Analyze attrition counts and export CSV
    attrition_counts = analyze_and_export_attrition_counts()

    if attrition_counts is not None:
        # Generate and save attrition distribution plot
        plot_attrition_distribution(attrition_counts)

if __name__ == "__main__":
    main()
