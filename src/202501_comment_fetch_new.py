import pandas as pd
from pymongo import MongoClient
import requests
import logging
from logging.handlers import RotatingFileHandler
from threading import Thread, Lock
import queue
import time
from collections import deque
from config import MONGO_URI, GITHUB_TOKENS

# MongoDB Configuration
client = MongoClient(MONGO_URI)
db = client["disengagement"]
pull_request_comments_collection = db["pull_request_comments_202501"]
cache_collection = db["pr_comments_cache"]

# Logging Configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = RotatingFileHandler('../logs/fetch_pr_comments.log', maxBytes=10*1024*1024, backupCount=5)
handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# Token Management
class TokenManager:
    def __init__(self, tokens):
        self.tokens = deque(tokens)
        self.unavailable = {}  # token -> time when it becomes available again
        self.rate_limits = {token: {'remaining': 5000, 'reset_time': time.time()} for token in tokens}

    def get_available_token(self):
        while True:
            token = self.tokens.popleft()
            self.tokens.append(token)
            if token not in self.unavailable and self.rate_limits[token]['remaining'] > 0:
                logger.debug(f"Token {token} is available.")
                return token
            logger.debug(f"No available token, waiting for tokens to reset.")
            time.sleep(1)

    def update_rate_limit(self, token, headers):
        remaining = headers.get('X-RateLimit-Remaining', self.rate_limits[token]['remaining'])
        reset = headers.get('X-RateLimit-Reset', self.rate_limits[token]['reset_time'])
        self.rate_limits[token]['remaining'] = int(remaining)
        self.rate_limits[token]['reset_time'] = int(reset)

    def mark_unavailable(self, token, retry_after):
        self.unavailable[token] = time.time() + int(retry_after)



# Token Management (Reuse the existing TokenManager class)
token_manager = TokenManager(GITHUB_TOKENS)

# Cache Management (Reuse the existing cache functions)
cache_lock = Lock()

def load_progress_cache(repo):
    cache = cache_collection.find_one({"repo_name": repo})
    if cache:
        fetched_pages = cache.get("fetched_pages", [])
        fetched_comment_ids = cache.get("fetched_comment_ids", [])
        total_comments = cache.get("total_comments", -1)
        if isinstance(fetched_pages, int):
            fetched_pages = [fetched_pages]
        logger.info(f"Loaded cache for repo: {repo}. Fetched pages: {len(fetched_pages)}, Comments: {len(fetched_comment_ids)}")
        return {
            "fetched_pages": fetched_pages,
            "fetched_comment_ids": fetched_comment_ids,
            "total_comments": total_comments,
        }
    else:
        save_progress_cache(repo, fetched_pages=[], fetched_comment_ids=[], total_comments=-1)
        logger.info(f"Initialized new cache for repo: {repo}.")
        return {
            "fetched_pages": [],
            "fetched_comment_ids": [],
            "total_comments": -1,
        }

def save_progress_cache(repo, fetched_pages=None, fetched_comment_ids=None, total_comments=None):
    with cache_lock:
        update = {}
        if fetched_pages is not None:
            update["fetched_pages"] = fetched_pages
            logger.debug(f"Updating fetched_pages for {repo}: {fetched_pages}")
        if fetched_comment_ids is not None:
            update["fetched_comment_ids"] = fetched_comment_ids
            logger.debug(f"Updating fetched_comment_ids for {repo}: {fetched_comment_ids}")
        if total_comments is not None:
            update["total_comments"] = total_comments
            logger.debug(f"Updating total_comments for {repo}: {total_comments}")
        if update:
            cache_collection.update_one(
                {"repo_name": repo},
                {"$set": update},
                upsert=True
            )
    logger.info(f"Progress cache updated for repo: {repo}.")

# Ensure indexes exist
def ensure_indexes():
    pull_request_comments_collection.create_index(
        [("repo_name", 1), ("id", 1)],
        unique=True,
        name="unique_repo_name_comment_id"
    )
    logger.info("Ensured unique index on pull_request_comments for (repo_name, id).")

# Get total pages for comments
def get_total_pages(repo, per_page=100):
    base_url = f"https://api.github.com/repos/{repo}/pulls/comments"
    headers = {
        'Authorization': f'token {token_manager.get_available_token()}',
        'Accept': 'application/vnd.github.v3+json'
    }
    url = f"{base_url}?per_page={per_page}"
    response = requests.get(url, headers=headers, timeout=10)
    if response.status_code == 200:
        links = response.headers.get('Link', '')
        if 'rel="last"' in links:
            last_page_url = [link for link in links.split(',') if 'rel="last"' in link][0]
            last_page_url = last_page_url.split('page=')[-1].split('>')[0].strip()
            return int(last_page_url)
        else:
            return 1
    else:
        logger.error(f"Failed to get total pages for comments in {repo}")
        return 1

# Get existing comment IDs
def get_existing_comment_ids(repo):
    existing_comments = pull_request_comments_collection.find({"repo_name": repo}, {"id": 1})
    return {comment["id"] for comment in existing_comments}

# Threaded fetch function for comments
def threaded_fetch_comments(repo, base_url, collection):
    task_queue = queue.Queue()
    completed_pages = set()
    threads = []
    max_threads = 16  # Adjust thread count as needed

    cache = load_progress_cache(repo)
    fetched_pages = set(cache.get("fetched_pages", []))
    fetched_comment_ids = set(cache.get("fetched_comment_ids", []))
    total_comments = cache.get("total_comments", -1)

    existing_comment_ids = get_existing_comment_ids(repo)
    if len(fetched_comment_ids) < len(existing_comment_ids):
        fetched_comment_ids = fetched_comment_ids.union(existing_comment_ids)
        save_progress_cache(repo, fetched_pages=list(fetched_pages), fetched_comment_ids=list(fetched_comment_ids))

    if total_comments == -1:
        total_comments = get_total_pages(repo, 100)

    if len(fetched_comment_ids) == total_comments:
        logger.info(f"Skipping {repo}, all comments already fetched.")
        save_progress_cache(repo, fetched_pages=list(fetched_pages), fetched_comment_ids=list(fetched_comment_ids))
        return

    logger.info(f"Total comments for {repo}: {total_comments}")
    total_pages = get_total_pages(repo, 100)
    fetched_pages.add(total_pages)
    save_progress_cache(repo, fetched_pages=list(fetched_pages), fetched_comment_ids=list(fetched_comment_ids), total_comments=total_comments)

    for page in range(1, total_pages + 1):
        task_queue.put(page)

    logger.info(f"Starting threaded fetch for comments in repo {repo}. Total pages: {total_pages}. Total comments: {total_comments}, fetched comments: {len(fetched_comment_ids)}")

    comment_ids_lock = Lock()

    def worker():
        while not task_queue.empty():
            try:
                page = task_queue.get(timeout=1)
                if page in completed_pages:
                    task_queue.task_done()
                    continue
                logger.debug(f"Worker processing page {page} for {repo}.")
                token = token_manager.get_available_token()
                headers = {
                    'Authorization': f'token {token}',
                    'Accept': 'application/vnd.github.v3+json'
                }
                url = f"{base_url}?page={page}&per_page=100"
                try:
                    response = requests.get(url, headers=headers, timeout=10)
                    response.raise_for_status()
                except requests.exceptions.RequestException as e:
                    logger.error(f"Request error on page {page} for comments: {e}")
                    task_queue.put(page)
                    task_queue.task_done()
                    continue

                token_manager.update_rate_limit(token, response.headers)

                if response.status_code == 403:
                    retry_after = response.headers.get('Retry-After', 60)
                    token_manager.mark_unavailable(token, retry_after)
                    logger.warning(f"Token {token} hit rate limit. Switching to next token.")
                    task_queue.put(page)
                    task_queue.task_done()
                    continue

                items = response.json()

                if not isinstance(items, list):
                    logger.error(f"Unexpected response on page {page} for {repo}: {items}")
                    completed_pages.add(page)
                    task_queue.task_done()
                    continue

                logger.debug(f"Received {len(items)} comments on page {page} for {repo}.")
                for item in items:
                    comment_id = item['id']
                    if comment_id not in fetched_comment_ids:
                        item['repo_name'] = repo
                        try:
                            collection.insert_one(item)
                        except Exception as e:
                            if "E11000 duplicate key error" in str(e):
                                logger.info(f"Comment {comment_id} in {repo} already exists.")
                            else:
                                logger.error(f"Error inserting comment {comment_id} in {repo}: {e}")
                        with comment_ids_lock:
                            fetched_comment_ids.add(comment_id)
                            logger.debug(f"Added comment ID {comment_id} to fetched_comment_ids.")

                logger.info(f"Fetched {len(items)} comments for {repo}, page {page}.")
                completed_pages.add(page)
                fetched_pages.add(page)
                task_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"Error on page {page} for comments: {e}")
                task_queue.task_done()

    num_threads = min(max_threads, (total_comments - len(fetched_comment_ids) + 99) // 100)
    if num_threads <= 0:
        logger.info(f"No threads needed for {repo}. Total comments: {total_comments}, Fetched comments: {len(fetched_comment_ids)}")
        save_progress_cache(repo, fetched_pages=list(fetched_pages), fetched_comment_ids=list(fetched_comment_ids))
        return

    logger.info(f"Creating {num_threads} threads for {repo}. Total comments: {total_comments}, Fetched comments: {len(fetched_comment_ids)}")
    for _ in range(num_threads):
        t = Thread(target=worker)
        t.start()
        threads.append(t)

    task_queue.join()
    if not task_queue.empty():
        logger.warning(f"Task queue not empty after timeout for {repo}. Remaining tasks: {task_queue.qsize()}")

    for t in threads:
        t.join()

    if len(fetched_comment_ids) == total_comments:
        logger.info(f"Finished fetching comments for {repo}.")
    else:
        logger.warning(f"Not all pages were fetched for {repo}. Fetched {len(fetched_pages)} out of {total_pages} pages.")

# Main function for fetching comments
def main_comments():
    ensure_indexes()
    sample_projects = pd.read_csv('../data/sample_projects_total.csv')
    project_names = sample_projects[['repo_name', 'commits', 'totalPullRequests']].to_dict(orient='records')

    for project in project_names:
        repo = project['repo_name']
        cache = load_progress_cache(repo)
        if cache.get("total_comments", -1) == len(cache.get("fetched_comment_ids", [])):
            logger.info(f"Skipping {repo}, all comments already fetched.")
            continue

        base_url = f"https://api.github.com/repos/{repo}/pulls/comments"
        threaded_fetch_comments(repo, base_url, pull_request_comments_collection)

if __name__ == "__main__":
    try:
        main_comments()
    except Exception as e:
        logger.error(f"Program stopped with error: {e}")
        raise