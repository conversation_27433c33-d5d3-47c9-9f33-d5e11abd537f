import pandas as pd
import numpy as np

# 导入数据
repo_info = pd.read_csv('../data/sample_projects_quartiles.csv')
attrition = pd.read_csv('../result/attritions.csv')
productivity = pd.read_csv('../result/productivity/merged_pr_productivity_metrics.csv')

# 转换日期格式
attrition['attrition_date'] = pd.to_datetime(attrition['attrition_date'])
productivity['time'] = pd.to_datetime(productivity['Date'])

# 确保数据按时间排序
attrition = attrition.sort_values(['repo_name', 'attrition_date'])
productivity = productivity.sort_values(['repo_name', 'time'])

# 合并仓库基本信息到生产力数据
df = productivity.merge(repo_info, on='repo_name', how='left')

# 合并Attrition数据
df = df.merge(attrition, on='repo_name', how='left')

# 填充缺失值（没有流失事件）
df['attrition_date'] = df['attrition_date'].fillna(pd.NaT)

# 创建指示变量，是否在当前时间点有流失事件
df['attrition_event'] = np.where(df['time'] == df['attrition_date'], 1, 0)

# 计算累计流失次数
df['cumulative_attrition'] = df.groupby('repo_name')['attrition_event'].cumsum()

# 创建一个指示变量，表示是否已经发生过至少一次流失
df['ever_attrited'] = (df['cumulative_attrition'] > 0).astype(int)

# 创建交互项：累计流失次数 * 已经流失
df['treated_cumulative'] = df['cumulative_attrition'] * df['ever_attrited']

# 替换1900-01-01的时间点为NaN
df.loc[df['attrition_date'] == pd.Timestamp('1900-01-01'), 'attrition_date'] = np.nan

# 重置索引以便操作
df = df.reset_index(drop=True)

# 创建相对时间变量
def assign_relative_time(group):
    event_times = group[group['attrition_event'] == 1]['time'].tolist()
    if not event_times:
        group['relative_time'] = np.nan
    else:
        # 对于每个时间点，计算与最近流失事件的相对月份差
        def compute_relative_time(x):
            diffs = [(x - event).days // 30 for event in event_times]
            diffs = [diff for diff in diffs if diff >= -6 and diff <= 6]  # 设定事件窗口范围
            if diffs:
                return min(diffs, key=lambda d: abs(d))
            else:
                return np.nan
        group['relative_time'] = group['time'].apply(compute_relative_time)
    return group

df = df.groupby('repo_name').apply(assign_relative_time)

# 定义事件窗口
window = 6

# 创建虚拟变量
for k in range(-window, window + 1):
    df[f'event_time_{k}'] = np.where(df['relative_time'] == k, 1, 0)

# 选择需要导出的变量
export_columns = [
    'repo_name', 'time', 'NUM_MERGED_PR', 'ACCEPT_RATE',
    'AVG_TIME_TO_REACT', 'AVG_TIME_TO_MERGE', 'contributors',
    'commits', 'stargazers', 'totalPullRequests', 'totalIssues', 'createdAt',
    'cumulative_attrition', 'ever_attrited', 'treated_cumulative'
]

# 包含事件研究虚拟变量
event_vars = [f'event_time_{k}' for k in range(-window, window + 1) if k != 0]
export_columns += event_vars

# 选择并导出
final_df = df[export_columns]
final_df.to_csv('../result/did_analysis_data.csv', index=False)

print("数据已成功导出为 'did_analysis_data.csv'")

