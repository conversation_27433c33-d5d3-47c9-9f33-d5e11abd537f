import pandas as pd
import numpy as np
import statsmodels.api as sm
from joblib import Parallel, delayed, Memory
import logging
import os
from datetime import timedelta

# 配置日志
log_dir = '../logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'analysis.log')),
        logging.StreamHandler()
    ]
)

# 创建缓存目录
cache_dir = './cache'
if not os.path.exists(cache_dir):
    os.makedirs(cache_dir)
memory = Memory(location=cache_dir, verbose=0)

# 加载数据
logging.info("Loading data...")
repo_info = pd.read_csv('../data/sample_projects_quartiles.csv')
attrition = pd.read_csv('../result/attritions.csv')
productivity = pd.read_csv('../result/productivity_metrics_202501015.csv')
logging.info("Data loaded successfully.")

# 数据预处理
logging.info("Preprocessing data...")
attrition['attrition_date'] = pd.to_datetime(attrition['attrition_date'])
productivity['time'] = pd.to_datetime(productivity['datetime'])
productivity['time_to_merge'] = pd.to_timedelta(productivity['time_to_merge']).dt.total_seconds()
logging.info("Data preprocessing completed.")

# 按仓库名称和时间排序
logging.info("Sorting data by repo_name and time...")
attrition = attrition.sort_values(['repo_name', 'attrition_date'])
productivity = productivity.sort_values(['repo_name', 'time'])
logging.info("Data sorting completed.")

# 获取每个仓库的流失事件数量
logging.info("Calculating attrition counts...")
attrition_counts = attrition['repo_name'].value_counts().reset_index()
attrition_counts.columns = ['repo_name', 'attrition_count']
logging.info("Attrition counts calculated.")

# 提取只有一个流失事件的仓库
logging.info("Extracting repos with one attrition event...")
repos_with_one_attrition = attrition_counts[attrition_counts['attrition_count'] == 1]
logging.info(f"Found {len(repos_with_one_attrition)} repos with one attrition event.")

# 设置处理组
logging.info("Setting up treatment group...")
treatment_repos_with_attrition_date = pd.merge(repos_with_one_attrition, attrition, on='repo_name', how='left')
logging.info("Treatment group setup completed.")

# 控制组为所有仓库
control_groups = repo_info

# 预计算控制仓库的趋势斜率
@memory.cache
def precompute_control_trends(control_groups, productivity, metric_column):
    """
    预计算所有控制仓库的趋势斜率。
    """
    logging.info("Precomputing control trends...")
    control_trends = {}
    for repo in control_groups['repo_name']:
        control_data = productivity[productivity['repo_name'] == repo]
        if control_data.empty:
            control_trends[repo] = None
            continue
        # 确保没有零值
        control_data = control_data[control_data[metric_column] != 0]
        if control_data.empty:
            control_trends[repo] = None
            continue
        # 将时间转换为数值（例如，自1970年以来的秒数）
        time_numeric = (control_data['time'] - pd.Timestamp('1970-01-01')).dt.total_seconds()
        metric = control_data[metric_column].values
        # 使用numpy计算斜率
        cov_xy = np.cov(time_numeric, metric, ddof=0)[0][1]
        var_x = np.var(time_numeric, ddof=0)
        if var_x == 0:
            slope = None
        else:
            slope = cov_xy / var_x
        control_trends[repo] = slope
    logging.info("Control trends precomputed.")
    return control_trends

# 预计算控制仓库的趋势斜率
logging.info("Precomputing control trends for time_to_merge...")
control_trends = precompute_control_trends(control_groups, productivity, 'time_to_merge')
logging.info("Control trends precomputed successfully.")

# 处理单个处理仓库的函数
def process_treatment_repo(treatment_repo, treatment_attrition_date, control_trends, productivity, attrition, timewindow_weeks, metric_column):
    """
    处理单个处理仓库，选择符合条件的控制仓库。
    """
    logging.info(f"Processing treatment repo: {treatment_repo}")
    window_start = treatment_attrition_date - timedelta(weeks=timewindow_weeks)
    window_end = treatment_attrition_date + timedelta(weeks=timewindow_weeks)
    
    # 获取在禁止时间窗口内有流失事件的控制仓库
    overlapping_control_repos = attrition[
        (attrition['repo_name'].isin(control_groups['repo_name'])) &
        (attrition['attrition_date'] >= window_start) &
        (attrition['attrition_date'] <= window_end)
    ]['repo_name'].unique()
    
    # 符合条件的控制仓库
    eligible_control_repos = set(control_groups['repo_name']) - set(overlapping_control_repos)
    
    # 进一步过滤，确保控制仓库有足够的生产力数据
    eligible_control_repos = [repo for repo in eligible_control_repos if not productivity[
        (productivity['repo_name'] == repo) & (productivity['time'] < window_start)
    ].empty]
    
    # 获取处理仓库的数据并计算趋势斜率
    treatment_data = productivity[
        (productivity['repo_name'] == treatment_repo) & 
        (productivity['time'] < treatment_attrition_date)
    ]
    # 使用numpy计算斜率
    time_numeric = (treatment_data['time'] - pd.Timestamp('1970-01-01')).dt.total_seconds()
    metric = treatment_data[metric_column].values
    cov_xy = np.cov(time_numeric, metric, ddof=0)[0][1]
    var_x = np.var(time_numeric, ddof=0)
    if var_x == 0:
        treatment_trend = None
    else:
        treatment_trend = cov_xy / var_x
    if treatment_trend is None:
        logging.warning(f"No trend available for treatment repo {treatment_repo}.")
        return {'treatment_repo': treatment_repo, 'selected_controls': []}
    
    # 选择趋势斜率相似的控制仓库
    selected_controls = []
    for control_repo in eligible_control_repos:
        control_slope = control_trends.get(control_repo, None)
        if control_slope is None:
            continue
        if abs(control_slope - treatment_trend) < 0.05:
            selected_controls.append(control_repo)
            if len(selected_controls) >= 5:
                break
    logging.info(f"Selected {len(selected_controls)} control repos for treatment repo {treatment_repo}.")
    return {'treatment_repo': treatment_repo, 'selected_controls': selected_controls}

# 面板差分回归函数
def perform_panel_diff_in_diff(treatment_repos_with_attrition_date, control_groups, productivity, attrition, metric_column, timewindow_weeks=12):
    """
    执行面板差分回归（DiD）分析。
    """
    logging.info("Performing panel difference-in-differences analysis...")
    combined_data = []

    # 并行处理所有处理仓库
    results = Parallel(n_jobs=-1)(delayed(process_treatment_repo)(
        row['repo_name'], row['attrition_date'], control_trends, productivity, attrition, timewindow_weeks, metric_column
    ) for idx, row in treatment_repos_with_attrition_date.iterrows())

    # 收集每个处理仓库的选择结果
    selected_controls_per_treatment = {res['treatment_repo']: res['selected_controls'] for res in results}

    # 组合处理组和对照组数据
    for treatment_repo, selected_controls in selected_controls_per_treatment.items():
        if not selected_controls:
            continue
        treatment_attrition_date = attrition.loc[attrition['repo_name'] == treatment_repo, 'attrition_date'].values[0]
        repos = [treatment_repo] + selected_controls
        subset = productivity[productivity['repo_name'].isin(repos)].copy()
        subset['standardized_time_weeks'] = (subset['time'] - treatment_attrition_date).dt.days // 7
        subset = subset[subset['standardized_time_weeks'].between(-timewindow_weeks, timewindow_weeks)]
        subset['post_treatment'] = (subset['standardized_time_weeks'] >= 0).astype(int)
        subset['is_treatment'] = (subset['repo_name'] == treatment_repo).astype(int)
        subset['is_treatment_post_treatment'] = subset['post_treatment'] * subset['is_treatment']
        combined_data.append(subset)

    # 合并所有数据
    final_data = pd.concat(combined_data)
    logging.info(f"Final data shape: {final_data.shape}")

    # 运行面板差分回归
    independent_vars = ['post_treatment', 'is_treatment', 'is_treatment_post_treatment']
    final_data = final_data.dropna(subset=independent_vars + [metric_column])
    model = sm.OLS(final_data[metric_column], sm.add_constant(final_data[independent_vars])).fit()

    logging.info("Panel DiD analysis completed.")
    return model, final_data

# 主函数
if __name__ == "__main__":
    # 执行面板差分回归
    metric_column = 'time_to_merge'
    timewindow_weeks = 12
    logging.info("Starting panel DiD regression...")
    model, final_data = perform_panel_diff_in_diff(
        treatment_repos_with_attrition_date, control_groups, productivity, attrition, metric_column, timewindow_weeks
    )

    # 输出回归结果
    logging.info("Panel DiD regression results:")
    print(model.summary())