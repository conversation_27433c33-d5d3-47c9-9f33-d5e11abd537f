import pymongo
import pandas as pd

# Establish a connection to MongoDB
client = pymongo.MongoClient('localhost', 27017)

# Access the database and collection
db = client['disengagement']
collection = db['pull_requests_202501']

# Retrieve only documents where merged_at is not null
pull_requests = collection.find({"merged_at": {"$ne": None}})

# Extract the required fields
data = []
for pr in pull_requests:
    data.append({
        'number': pr.get('number'),
        'user_login': pr.get('user', {}).get('login'),
        'created_at': pr.get('created_at'),
        'updated_at': pr.get('updated_at'),
        'closed_at': pr.get('closed_at'),
        'merged_at': pr.get('merged_at'),
        'repo_name': pr.get('repo_name')
    })

# Create a DataFrame
df_pr = pd.DataFrame(data)

# Convert date fields to datetime
date_cols = ['created_at', 'updated_at', 'closed_at', 'merged_at']
df_pr[date_cols] = df_pr[date_cols].apply(pd.to_datetime, errors='coerce')

# Generate the CSV file
df_pr.to_csv('../data/2025_0116_pull_requests.csv', index=False)