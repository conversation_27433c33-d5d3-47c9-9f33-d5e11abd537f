import logging
from pymongo import MongoClient
import pandas as pd

# Configure logging
log_file = '../logs/core_developer_breaks_multi.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


def get_commit_file_repo_name(repo_name):
    file_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    repo_commit = pd.read_csv(file_path)
    # Exclude merge commits with those who have two values in the columns named 'parent_shas'
    repo_commit = repo_commit[repo_commit['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)
    # Exclude bot accounts
    bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')
    bot_developers = bot_developers['bot_name'].tolist()
    repo_commit = repo_commit[~repo_commit['author_login'].isin(bot_developers)].reset_index(drop=True)
    return repo_commit


def get_pauses_list_with_start_end_dates(commits_dates_sorted):
    pauses = []
    column_names = ['id', 'length', 'start_date', 'end_date']
    id = 0
    for i in range(1, len(commits_dates_sorted)):
        start_date = commits_dates_sorted.iloc[i - 1]
        end_date = commits_dates_sorted.iloc[i]
        if (end_date - start_date).days == 0:
            continue
        pause_length = (end_date - start_date).days
        pauses.append([id, pause_length, start_date, end_date])
        id += 1
    pauses_df = pd.DataFrame(pauses, columns=column_names)
    return pauses_df


def calculate_t_fov(pauses, total_duration):
    if len(pauses) < 4:
        return None
    pauses_days = pd.Series(pauses['length'].tolist())
    Q1 = pauses_days.quantile(0.25)
    Q3 = pauses_days.quantile(0.75)
    IQR = Q3 - Q1
    Tfov = Q3 + 3 * IQR
    return Tfov


def process_repo_data_and_store_multi(project_names, core_devs_data, attrition_thresholds):
    """
    Process data for each repository and store results in multiple MongoDB collections for different attrition thresholds.
    """
    logging.info("Connecting to MongoDB.")
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collections = {}
    for threshold in attrition_thresholds:
        col_name = f"developer_breaks_attrition_{threshold}d"
        collections[threshold] = db[col_name]
        collections[threshold].drop()
        collections[threshold].create_index([("repo_name", 1), ("core_dev_id", 1)], unique=True)
        collections[threshold].create_index([("core_dev_login", 1)])
        collections[threshold].create_index([("repo_name", 1)])
        logging.info(f"Prepared collection: {col_name}")

    logging.info("Processing core developers data.")
    core_devs_data['core_developers'] = core_devs_data['core_developers'].apply(
        lambda x: x if isinstance(x, list) else eval(x)
    )

    project_counter = 0
    for repo_name in project_names:
        logging.info(f"Processing repository: {repo_name} (Project ID: {project_counter})")
        try:
            repo_commits = get_commit_file_repo_name(repo_name)
        except Exception as e:
            logging.error(f"Error fetching commit data for repository '{repo_name}': {e}")
            continue
        try:
            core_developers = core_devs_data[core_devs_data['repo_name'] == repo_name]['core_developers'].iloc[0]
        except Exception as e:
            logging.error(f"Error fetching core developers for repository '{repo_name}': {e}")
            continue
        core_dev_counter = 0
        repo_commits['date'] = pd.to_datetime(repo_commits['date'], errors='coerce')
        for core_dev in core_developers:
            dev_commits = repo_commits[repo_commits['author_login'] == core_dev].copy()
            dev_commits['date'] = pd.to_datetime(dev_commits['date'], errors='coerce')
            dev_commits = dev_commits.dropna(subset=['date'])
            if not pd.api.types.is_datetime64_any_dtype(dev_commits['date']):
                dev_commits['date'] = pd.to_datetime(dev_commits['date'], errors='coerce')
            dev_commits['date'] = dev_commits['date'].dt.normalize()
            dev_commits = dev_commits.sort_values(by='date')
            commit_number = len(dev_commits)
            pauses = get_pauses_list_with_start_end_dates(dev_commits['date'])
            if pauses.empty:
                logging.info(f"No pauses detected for core developer {core_dev} in repository {repo_name}.")
                continue
            total_duration = (dev_commits['date'].max() - dev_commits['date'].min()).days
            t_fov = calculate_t_fov(pauses, total_duration)
            Dev_Breaks = set()
            for _, p in pauses.iterrows():
                if t_fov and p['length'] > t_fov:
                    Dev_Breaks.add((p['id'], p['start_date'], p['end_date'], p['length'], t_fov))
            Dev_Breaks = [
                {'id': b[0], 'start_date': b[1], 'end_date': b[2], 'pause_duration': b[3], 't_fov': b[4]}
                for b in Dev_Breaks
            ]
            Dev_Breaks = sorted(Dev_Breaks, key=lambda x: x['start_date'])
            last_commit_date = dev_commits['date'].max()
            for threshold in attrition_thresholds:
                dev_output = {
                    "repo_id": project_counter,
                    "repo_name": repo_name,
                    "core_dev_id": core_dev_counter,
                    "core_dev_login": core_dev,
                    "commit_number": commit_number,
                    "Breaks": Dev_Breaks,
                    "Attrition": None,
                }
                if (repo_commits['date'].max() - last_commit_date).days > threshold:
                    dev_output["Attrition"] = {
                        "attrition_date": str(last_commit_date.date()),
                        "threshold_days": threshold
                    }
                try:
                    collections[threshold].insert_one(dev_output)
                except Exception as e:
                    logging.error(f"Error storing data for core developer {core_dev} in MongoDB (threshold {threshold}): {e}")
            core_dev_counter += 1
        project_counter += 1
    logging.info("Data processing completed for all thresholds.")

def main():
    sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
    sample_repo = sample_projects['name'].tolist()
    core_devs_data = pd.read_csv('../data/core_developer_list_4000_repo.csv')
    attrition_thresholds = [270, 450, 180]  # days
    process_repo_data_and_store_multi(sample_repo, core_devs_data, attrition_thresholds)

if __name__ == "__main__":
    main() 