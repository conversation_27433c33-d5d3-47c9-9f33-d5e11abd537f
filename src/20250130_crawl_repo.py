import pandas as pd
from pymongo import MongoClient
import requests
import logging
from logging.handlers import RotatingFileHandler
from threading import Thread, Lock
import queue
import time
import re
# ----------------------------
#        MongoDB 配置
# ----------------------------
WINDOWS_IP = 'localhost'
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")
db = client["disengagement"]

# check if db exists
if "commits" not in db.list_collection_names():
    db.create_collection("commits")
if "pull_requests" not in db.list_collection_names():
    db.create_collection("pull_requests")
if "pr_comments" not in db.list_collection_names():
    db.create_collection("pr_comments")
if "progress_cache" not in db.list_collection_names():
    db.create_collection("progress_cache")


commits_collection = db["commits"]
pull_requests_collection = db["pull_requests"] 
pr_comments_collection = db["pr_comments"]
cache_collection = db["progress_cache"]


# ----------------------------
#     GitHub API tokens
# ----------------------------
github_tokens = [
    '*********************************************************************************************',
    '*********************************************************************************************',
    '*********************************************************************************************',
]

# ----------------------------
#          日志配置
# ----------------------------
log_file = 'fetch_commits.log'
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
handler = RotatingFileHandler(log_file, maxBytes=10 * 1024 * 1024, backupCount=5)
logger.addHandler(handler)

# 用来在多线程下安全切换 token
token_lock = Lock()
token_index = 0

def get_next_token():
    """
    获取下一个 token，轮询方式。
    """
    global token_index
    with token_lock:
        token = github_tokens[token_index]
        token_index = (token_index + 1) % len(github_tokens)
    return token

# ----------------------------
#      进度缓存管理函数
# ----------------------------
cache_lock = Lock()

def load_progress_cache(repo):
    """加载进度缓存，如果没有则返回默认结构。"""
    cache = cache_collection.find_one({"repo_name": repo})
    if cache:
        return cache
    else:
        return {
            "repo_name": repo,
            "commit": 1,
            "pull_request": 1,
            "pr_comment": 1,
            "commit_num": 0,
            "pr_num": 0,
            "pr_review_num": 0,
            "commit_page_num": -1,
            "pull_request_page_num": -1,
            "pr_review_page_num": -1,
            "commits_finished": 0,
            "pr_finished": 0,
            "pr_review_finished": 0,
            "repo_unavailable": 0,  # 新增字段，用于标记仓库无法访问
        }

def save_progress_cache(repo, **kwargs):
    """保存进度缓存。"""
    update_data = {key: value for key, value in kwargs.items() if value is not None}
    cache_collection.update_one(
        {"repo_name": repo},
        {"$set": update_data},
        upsert=True
    )

# ----------------------------
#    检查仓库是否存在的函数
# ----------------------------
def check_repo_exists(repo):
    """
    Checks repository availability with enhanced error handling and smart caching
    Returns True if accessible, False with proper error classification
    """
    cache = load_progress_cache(repo)
    
    if cache.get("repo_unavailable"):
        if time.time() - cache.get("last_checked", 0) < 3600:  # 1-hour cache
            return False
     # 配置重试次数
    max_retries = 3
    retry_count = 0
    rate_limited_tokens = {}
    while retry_count < max_retries:
        token = get_next_token()
        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        try:
            resp = requests.get(f"https://api.github.com/repos/{repo}", 
                            headers=headers, 
                            timeout=10)
            
            if resp.status_code == 200:
                save_progress_cache(repo, repo_unavailable=0)  # Reset cache
                return True
                
            elif resp.status_code == 404:
                logger.warning(f"Repo {repo} not found")
                save_progress_cache(repo, repo_unavailable=1, last_checked=time.time())
                return False
                
            elif resp.status_code == 403:
                reset_time = int(resp.headers.get('X-RateLimit-Reset', time.time() + 60))
                wait_sec = reset_time - time.time() + 1
                rate_limited_tokens[token] = reset_time
                if wait_sec > 0:
                    logger.warning(f"[{repo}] Token rate-limited. Waiting for {wait_sec:.1f} seconds.")
                if len(rate_limited_tokens) >= len(github_tokens):
                    # 如果所有 token 都被限流，则等待最短时间
                    earliest_reset = min(rate_limited_tokens.values())
                    wait_sec = earliest_reset - time.time() + 1
                    if wait_sec > 0:
                        logger.warning(f"All tokens rate-limited. Waiting {wait_sec:.1f} seconds.")
                        time.sleep(wait_sec)
                    # 清空标记，重新开始
                    rate_limited_tokens.clear()
                retry_count += 1
                continue
            else:
                logger.warning(f"Temporary error checking {repo}: {resp.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Transient error checking {repo}: {e}")
            return False


# ----------------------------
#    从 Link Header 中解析页数
# ---------------------------
def get_final_page_from_link(link_header: str) -> int:
    """
    从 GitHub API 的 Link Header 中，解析出 rel="last" 对应的 page 参数。
    如果找不到 rel="last"，则返回 1。
    """
    # Link 形如：
    # <https://api.github.com/repositories/12345/commits?per_page=1&page=2>; rel="next",
    # <https://api.github.com/repositories/12345/commits?per_page=1&page=34>; rel="last"
    if not link_header:
        return 1
    
    # 逐段解析
    # 典型 pattern:  <...page=34>; rel="last"
    links = link_header.split(',')
    for l in links:
        if 'rel="last"' in l:
            # 使用正则搜索 page=xx
            match = re.search(r'page=(\d+)>', l)
            if match:
                return int(match.group(1))
    return 1

# ----------------------------
#    确定某个 data_type 的总页数
# ----------------------------
def determine_total_pages(repo: str, base_url: str, data_type: str, 
                          need_state_all=False, 
                          per_page=1) -> int:
    """
    确定某个 data_type (commits, pull_requests, pr_comments等) 的总页数。
    - 若缓存中已有，则直接返回；
    - 若缓存中没有，则通过请求 Link Header 获取并缓存。
    - 需要时可加上 state=all，例如 pull requests。
    - 默认用 per_page=1 来确保Link header一定会返回(若超1页)。
    在确认页面的时候，如果遇到token被限制，则等待token恢复后再重试。
    """
    cache = load_progress_cache(repo)
    page_num_key = f"{data_type}_page_num"
    rate_limited_tokens = {}
    if cache.get(page_num_key, -1) != -1:
        # 已经缓存过 final_page
        final_page = cache[page_num_key]
        return final_page

    # 配置重试次数
    max_retries = 3
    retry_count = 0

    params = {'per_page': per_page}
    if need_state_all:
        params['state'] = 'all'

    while retry_count < max_retries:
        token = get_next_token()
        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        if token in rate_limited_tokens:
            reset_time = rate_limited_tokens[token]
            now = time.time()
            if now < reset_time:
                time.sleep(1)
                retry_count += 1
                continue
            else:
                rate_limited_tokens.remove(token)
                
        try:
            resp = requests.get(base_url, headers=headers, params=params, timeout=10)
            
            # 如果遇到403，可能是token被限流，尝试等待重置时间后重试
            if resp.status_code == 403:
                reset_time = int(resp.headers.get('X-RateLimit-Reset', time.time() + 60))
                wait_sec = reset_time - time.time() + 1
                rate_limited_tokens.add(token)
                if wait_sec > 0:
                    logger.warning(f"[{repo}] Token rate-limited. Waiting for {wait_sec:.1f} seconds.")
                if len(rate_limited_tokens) >= len(github_tokens):
                    # 如果所有 token 都被限流，则等待最短时间
                    earliest_reset = min(rate_limited_tokens)
                    wait_sec = earliest_reset - time.time() + 1
                    if wait_sec > 0:
                        logger.warning(f"All tokens rate-limited. Waiting {wait_sec:.1f} seconds.")
                        time.sleep(wait_sec)
                    # 清空标记，重新开始
                    rate_limited_tokens.clear()
                retry_count += 1
                continue

            if resp.status_code == 404:
                logger.warning(f"[{repo}] {data_type} got status=404, mark unavailable in cache.")
                save_progress_cache(repo, repo_unavailable=1)
                return 0

            resp.raise_for_status()
            link_header = resp.headers.get('Link', '')
            total_num = get_final_page_from_link(link_header)
            # 当 per_page=1 时，GitHub返回的 total_num表示实际条数，换算成100条一页的数量
            final_page = (total_num + 99) // 100
            save_progress_cache(repo, **{page_num_key: final_page})
            save_progress_cache(repo, **{f"{data_type}_num": total_num})
            return final_page

        except Exception as e:
            logger.error(f"[{repo}] determine_total_pages error for {data_type}: {e}")
            retry_count += 1

    # 如果多次重试仍失败，写回缓存，避免无限重试
    save_progress_cache(repo, **{page_num_key: 1})
    return 1

# ----------------------------
#   通用的分页爬取多线程函数
# ----------------------------
def threaded_fetch(
    repo,
    base_url,
    collection,
    page_key,
    data_type,
    final_page,
    need_state_all=False
):
    """
    通用的多线程抓取函数。
    - repo: 仓库名
    - base_url: 对应的 API base，比如 commits 或 pulls
    - collection: 存放数据的 MongoDB 集合
    - page_key: 在缓存中存放当前页码的字段，如 'commit', 'pull_request', 'pr_comment'
    - data_type: 日志使用，比如 'commits', 'pull_request' 等
    - final_page: 最终页数
    - need_state_all: 是否需要在请求参数中加上 state=all（针对pulls）
    """

    task_queue = queue.Queue()
    total_count = [0]  # list 是可变对象，用于在多线程中共享计数
    completed_tasks = set()  # 防止重复处理
    thread_list = []

    # 并发相关配置
    max_retries = 3  # 每个任务最多重试次数
    max_threads = 48  # 线程数可以适当减小，避免瞬间把所有 token 用光

    # 用于管理 token 的 Rate Limit 状态
    rate_limit_tokens = {}

    # Step1: 根据当前已经抓取到的页面、以及 estimated_total，确定要抓到的“末页”。
    cache = load_progress_cache(repo)
    start_page = cache.get(page_key, 1)
    
    # for page in range(start_page, final_page + 1):
    #     task_queue.put(page)    
    # 你可以选择移除这个“简单判断”逻辑，直接使用 while + 判断返回结果是否 <100 条 进行爬取
    # if estimated_total and estimated_total < 800:
    #     final_page = 8
    # else:
    #     # 如果没有预估量，就给一个比较保守的上限，比如 1000
    #     # 或者你可以使用 while 循环“爬到 <100 条时就停止”的方式
    #     final_page = 1000

    # 如果之前已经用二分法/其他方法找到了最终页，则使用缓存值
    # if cache.get(f"{data_type}_page_num", -1) != -1:
    #     final_page = cache[f"{data_type}_page_num"]

    logger.info(f"[{repo}] data_type={data_type}, start_page={start_page}, final_page={final_page}")

    # Step2: 初始化任务队列
    for page in range(start_page, final_page + 1):
        task_queue.put(page)

    # Step3: Worker 函数
    def worker():
        while True:
            try:
                page = task_queue.get(timeout=3)
            except queue.Empty:
                break  # 没有任务了，退出

            if page in completed_tasks:
                task_queue.task_done()
                continue

            retry_count = 0
            while retry_count < max_retries:
                token = get_next_token()
                headers = {
                    'Authorization': f'token {token}',
                    'Accept': 'application/vnd.github.v3+json'
                }

                # 如果该 token 在当前窗口期已被标记为限流，则跳过等待下一个 token
                if token in rate_limit_tokens:
                    reset_time = rate_limit_tokens[token]
                    now = time.time()
                    if now < reset_time:
                        # 等待 1 秒重试 或者直接 break 改用下个 token
                        time.sleep(1)
                        retry_count += 1
                        continue
                    else:
                        # 如果已经过了 reset_time，则移除标记
                        del rate_limit_tokens[token]

                # 拼出请求的 url
                url = f"{base_url}?page={page}&per_page=100"
                if need_state_all:
                    url += "&state=all"

                try:
                    resp = requests.get(url, headers=headers, timeout=15)

                    # 如果触发 rate limit (403) 或者其他
                    if resp.status_code == 403:
                        # 获取重置时间
                        reset_time = int(resp.headers.get('X-RateLimit-Reset', time.time() + 60))
                        rate_limit_tokens[token] = reset_time
                        logger.warning(f"[{repo}] Token {token} hit rate limit. Next reset at {reset_time}.")

                        # 如果所有 token 都被限流，则等待最短时间
                        if len(rate_limit_tokens) >= len(github_tokens):
                            earliest_reset = min(rate_limit_tokens.values())
                            wait_sec = earliest_reset - time.time() + 1
                            if wait_sec > 0:
                                logger.warning(f"All tokens rate-limited. Waiting {wait_sec:.1f} seconds.")
                                time.sleep(wait_sec)
                            # 清空标记，重新开始
                            rate_limit_tokens.clear()

                        retry_count += 1
                        continue

                    # 对于其他非 200, 尝试抛出异常
                    
                    resp.raise_for_status()

                    items = resp.json()
                    # 如果当页数据为空或小于 100，后面可以考虑停止爬取
                    # 如果你想在这里做 “若 len(items) < 100 -> break” 的逻辑也可以

                    # 写入数据库
                    for item in items:
                        item['repo_name'] = repo
                        collection.insert_one(item)

                    # 更新计数
                    with cache_lock:
                        total_count[0] += len(items)

                    logger.info(f"[{repo}] [Page {page}/{final_page} {data_type}] fetched {len(items)} items")

                    # 更新缓存：当前抓到第几页
                    save_progress_cache(repo, **{page_key: page})

                    # 如果返回的数据少于 100，可以认为后面没有了
                    if len(items) < 100:
                        # 更新一下 data_type_page_num 以便下次不用再爬
                        save_progress_cache(repo, **{f"{data_type}_page_num": page})
                        # 剩余任务直接丢弃
                        while not task_queue.empty():
                            task_queue.get()
                            task_queue.task_done()
                        break

                    # 成功爬完此页, 跳出 retry
                    break

                except requests.exceptions.Timeout:
                    logger.warning(f"[{repo}] Request timeout on page {page}. Retrying...")
                    retry_count += 1

                except Exception as e:
                    logger.error(f"[{repo}] Error on page {page}/{data_type}: {e}")
                    # 这里根据需求，决定是直接 break，还是 retry
                    retry_count += 1

            completed_tasks.add(page)
            task_queue.task_done()

    # Step4: 启动多线程
    num_threads = min(max_threads, task_queue.qsize())
    logger.info(f"[{repo}] Starting {num_threads} threads for {data_type}")
    for _ in range(num_threads):
        t = Thread(target=worker, daemon=True)
        thread_list.append(t)
        t.start()

    # Step5: 等待所有任务完成
    task_queue.join()

    # Step6: 收尾：统计总数、在缓存中标记完成
    for t in thread_list:
        t.join()

    # 标记最终的 total_count
    save_progress_cache(repo, **{f"{data_type}_num": total_count[0]})

    # 同时标记 finished=1
    if data_type == "commits":
        save_progress_cache(repo, commits_finished=1)
    elif data_type == "pull_request":
        save_progress_cache(repo, pr_finished=1)
    elif data_type == "pr_review_comments":
        save_progress_cache(repo, pr_review_finished=1)

    return total_count[0]

# ----------------------------
#      三种数据的爬取函数
# ----------------------------
def fetch_commits(repo):
    """
    先从 determine_total_pages 中获取/确认最终页数，
    然后调用你的多线程爬取函数 threaded_fetch。
    """
    base_url = f"https://api.github.com/repos/{repo}/commits"
    # 确定最大页数
    final_page = determine_total_pages(repo, base_url, "commit",
                                       need_state_all=False, 
                                       per_page=1)
    logger.info(f"[{repo}] commits => final_page = {final_page}")

    # 再做真正的抓取
    total_commits = threaded_fetch(
        repo=repo,
        base_url=base_url,
        collection=commits_collection,
        page_key="commit",              # 在缓存里对应的 "当前已经抓到第几页"
        data_type="commits",
        final_page=final_page,          # 直接用我们上面获取到的 final_page
        need_state_all=False
    )
    return total_commits

def fetch_pull_requests(repo):
    base_url = f"https://api.github.com/repos/{repo}/pulls"
    final_page = determine_total_pages(repo, base_url, "pull_request",
                                       need_state_all=True, 
                                       per_page=1)
    logger.info(f"[{repo}] pull requests => final_page = {final_page}")

    total_prs = threaded_fetch(
        repo=repo,
        base_url=base_url,
        collection=pull_requests_collection,
        page_key="pull_request",
        data_type="pull_request",
        final_page=final_page,
        need_state_all=True
    )
    return total_prs


def fetch_pull_review_comments(repo):
    base_url = f"https://api.github.com/repos/{repo}/pulls/comments"
    final_page = determine_total_pages(repo, base_url, "pr_review",
                                       need_state_all=False, 
                                       per_page=1)
    logger.info(f"[{repo}] pr review comments => final_page = {final_page}")

    total_comments = threaded_fetch(
        repo=repo,
        base_url=base_url,
        collection=pr_comments_collection,
        page_key="pr_comment",
        data_type="pr_review_comments",
        final_page=final_page,
        need_state_all=False
    )
    return total_comments


# ----------------------------
#           主函数
# ----------------------------
def main():
    # 读取项目数据
    sample_projects = pd.read_csv('../data/sample_projects_total.csv')
    # 假设 csv 中有  name, commits, totalPullRequests, pr_comments 等字段
    project_names = sample_projects[['name', 'commits', 'totalPullRequests']].fillna(0).to_dict(orient='records')
    # 从数据库中读取已经爬取过的项目，跳过这些
    client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")
    db = client["disengagement"]
    cache_collection = db["progress_cache"]
    # select all repo_name with commits_finished=1 and pr_finished=1 and pr_review_finished=1
    finished_projects = cache_collection.find({
        "commits_finished": 1,
        "pr_finished": 1,
        "pr_review_finished": 1
    }, {"repo_name": 1})
    finished_projects = [p["repo_name"] for p in finished_projects]
    project_names = [p for p in project_names if p['name'] not in finished_projects]
    # 输出finished_projects 数量和剩余项目数量
    logger.info(f"Finished projects: {len(finished_projects)}, Remaining projects: {len(project_names)}")

    for project in project_names:
        repo = project['name']
        estimated_commits = project.get('commits', 0)
        estimated_prs = project.get('totalPullRequests', 0)
        estimated_pr_review_comments = project.get('pr_comments', 0)

        cache = load_progress_cache(repo)
        # 如果已经标记不可用则跳过
        if cache.get("repo_unavailable", 0) == 1:
            logger.info(f"Skipping {repo}, previously marked as unavailable.")
            continue

        # 检查是否可访问
        if not check_repo_exists(repo):
            logger.info(f"Skipping {repo}, it's unavailable.")
            continue

        # 如果已经爬完（commits, PR, PR comments）
        if (cache.get("commits_finished", 0) == 1 and
            cache.get("pr_finished", 0) == 1 and
            cache.get("pr_review_finished", 0) == 1):
            logger.info(f"Skipping {repo}, already fully processed.")
            continue

        logger.info(f"---------- Start Processing: {repo} ----------")

        # Commits
        if cache.get("commits_finished", 0) != 1:
            fetch_commits(repo)
        else:
            logger.info(f"[{repo}] Commits already fetched.")

        # Pull Requests
        if cache.get("pr_finished", 0) != 1:
            fetch_pull_requests(repo)
        else:
            logger.info(f"[{repo}] Pull requests already fetched.")

        # PR review comments
        if cache.get("pr_review_finished", 0) != 1:
            fetch_pull_review_comments(repo)
        else:
            logger.info(f"[{repo}] PR review comments already fetched.")

        logger.info(f"========== Finished Processing: {repo} ==========\n")

if __name__ == "__main__":
    main()
