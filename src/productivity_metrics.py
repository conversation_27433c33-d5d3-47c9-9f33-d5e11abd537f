import logging
import pandas as pd
from datetime import datetime, timedelta
import os
import time  # Added to track elapsed time


def generate_time_series(pr_data, comment_data, time_window_days):
    """
    Generate time series data for PR productivity metrics with daily granularity.

    Args:
        pr_data (DataFrame): Pull request data.
        comment_data (DataFrame): Pull request comments data.
        time_window_days (int): The size of the rolling time window in days.

    Returns:
        DataFrame: A time series of daily productivity metrics.
    """
    logging.debug("Starting generate_time_series function.")

    # Convert time columns to datetime type
    logging.debug("Converting 'created_at' and 'merged_at' columns to datetime.")
    pr_data['created_at'] = pd.to_datetime(pr_data['created_at'])
    pr_data['merged_at'] = pd.to_datetime(pr_data['merged_at'], errors='coerce')
    comment_data['created_at'] = pd.to_datetime(comment_data['created_at'])

    # Set the start and end dates based on the PR data
    start_date = pr_data['created_at'].min().floor('D')
    end_date = pr_data['created_at'].max().ceil('D')
    logging.info(f"Time series will range from {start_date.date()} to {end_date.date()}.")

    # Create a date range
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')

    # Precompute the earliest comment time for each PR
    logging.debug("Grouping comments to find the earliest comment time for each PR.")
    first_comments = comment_data.groupby('pr_id')['created_at'].min().reset_index()
    pr_data = pr_data.merge(first_comments, how='left', left_on='number', right_on='pr_id')

    # Calculate time differences in hours
    logging.debug("Calculating time to first reaction and time to merge in hours.")
    pr_data['time_to_first_reaction'] = (pr_data['created_at_y'] - pr_data['created_at_x']).dt.total_seconds() / 3600
    pr_data['time_to_merge'] = (pr_data['merged_at'] - pr_data['created_at_x']).dt.total_seconds() / 3600

    # Fill missing values
    pr_data['time_to_first_reaction'] = pr_data['time_to_first_reaction'].fillna(0)
    pr_data['time_to_merge'] = pr_data['time_to_merge'].fillna(0)

    # Set the PR creation time as the index for efficient slicing
    pr_data.set_index('created_at_x', inplace=True)

    # Initialize the list to store metrics
    time_series_metrics = []
    logging.debug("Initialized time_series_metrics list.")

    for current_date in date_range:
        window_start = current_date - timedelta(days=time_window_days)
        logging.debug(f"Processing date: {current_date.date()} with window start: {window_start.date()}.")

        # Filter PRs within the current time window
        window_prs = pr_data.loc[window_start:current_date]
        total_prs = len(window_prs)
        merged_prs = window_prs[window_prs['merged_at'].notna()]
        merged_count = len(merged_prs)
        acceptance_rate = (merged_count / total_prs * 100) if total_prs > 0 else 0

        logging.debug(
            f"Date: {current_date.date()}, Total PRs: {total_prs}, Merged PRs: {merged_count}, Acceptance Rate: {acceptance_rate:.2f}%.")

        # Calculate average times
        avg_time_to_first_reaction = window_prs['time_to_first_reaction'].mean() if total_prs > 0 else None
        avg_time_to_merge = window_prs['time_to_merge'].mean() if merged_count > 0 else None

        if avg_time_to_first_reaction is not None:
            logging.debug(f"Avg Time to React: {avg_time_to_first_reaction:.2f} hours.")
        if avg_time_to_merge is not None:
            logging.debug(f"Avg Time to Merge: {avg_time_to_merge:.2f} hours.")

        # Assuming 'repo_name' is present in pr_data; adjust if necessary
        repo_name = pr_data['repo_name'].iloc[0] if 'repo_name' in pr_data.columns else "Unknown"

        time_series_metrics.append({
            'repo_name': repo_name,
            "Date": current_date.strftime('%Y-%m-%d'),
            "NUM_PR": total_prs,
            "NUM_MERGED_PR": merged_count,
            "ACCEPT_RATE": acceptance_rate,
            "AVG_TIME_TO_REACT": avg_time_to_first_reaction,
            "AVG_TIME_TO_MERGE": avg_time_to_merge,
        })

    logging.debug("Completed generating time series metrics.")
    return pd.DataFrame(time_series_metrics)


def main():
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logging.info("Starting main function.")

    sample_projects_path = os.path.join('..', 'data', 'sample_projects_quartiles.csv')
    error_repos = []

    # Record the start time
    start_time = time.time()

    # Read sample projects
    try:
        logging.info(f"Reading sample projects from {sample_projects_path}.")
        sample_projects = pd.read_csv(sample_projects_path)
        logging.info("Successfully read sample projects.")
    except Exception as e:
        logging.error(f"Failed to read sample_projects file: {e}")
        return

    repo_names = sample_projects["name"].tolist()
    total_repos = len(repo_names)
    logging.info(f"Found {total_repos} repositories to process.")

    for idx, repo_name in enumerate(repo_names, 1):
        # Calculate elapsed time
        elapsed_seconds = time.time() - start_time
        elapsed_time = time.strftime("%H:%M:%S", time.gmtime(elapsed_seconds))
        logging.info(f"Processing repository {idx}/{total_repos}: {repo_name}. Elapsed time: {elapsed_time}.")

        pr_comments_path = os.path.join('../data/pr_comments', f"{repo_name.replace('/', '_')}_pr_comments.csv")
        pull_requests_path = os.path.join('../data/pullrequests', f"{repo_name.replace('/', '_')}_pull_requests.csv")

        # Read PR comments
        try:
            logging.info(f"Reading PR comments from {pr_comments_path}.")
            pr_comments = pd.read_csv(pr_comments_path)
            logging.info(f"Successfully read PR comments for {repo_name}.")
        except Exception as e:
            logging.error(f"Failed to read PR comments file {pr_comments_path}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to read PR comments file: {e}"})
            continue

        # Read Pull Requests
        try:
            logging.info(f"Reading Pull Requests from {pull_requests_path}.")
            pull_requests = pd.read_csv(pull_requests_path)
            logging.info(f"Successfully read Pull Requests for {repo_name}.")
        except Exception as e:
            logging.error(f"Failed to read Pull Requests file {pull_requests_path}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to read Pull Requests file: {e}"})
            continue

        time_window = 30
        logging.info(f"Using a time window of {time_window} days for {repo_name}.")

        # Check if PR data is empty
        if pull_requests.empty:
            error_repos.append({"repo_name": repo_name, "reason": "PR data is empty"})
            logging.warning(f"{repo_name}'s PR data is empty. Skipping.")
            continue

        # Check if comments data is empty
        if pr_comments.empty:
            error_repos.append({"repo_name": repo_name, "reason": "PR comments data is empty"})
            logging.warning(f"{repo_name}'s PR comments data is empty. Skipping.")
            continue

        # Generate time series data
        try:
            logging.info(f"Generating time series data for {repo_name}.")
            time_series_data = generate_time_series(pull_requests, pr_comments, time_window)
            logging.info(f"Successfully generated time series data for {repo_name}.")
        except Exception as e:
            logging.error(f"Failed to generate time series data for {repo_name}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to generate time series data: {e}"})
            continue

        # Save time series data
        output_dir = os.path.join('..', 'result', 'productivity')
        os.makedirs(output_dir, exist_ok=True)
        output_csv_path = os.path.join(output_dir, f"{repo_name.replace('/', '_')}_pr_productivity_metrics.csv")

        try:
            logging.info(f"Saving time series data to {output_csv_path}.")
            time_series_data.to_csv(output_csv_path, index=False, encoding="utf-8")
            logging.info(f"Generated productivity metrics CSV: {output_csv_path}")
        except Exception as e:
            logging.error(f"Failed to save productivity metrics CSV for {repo_name}: {e}")
            error_repos.append({"repo_name": repo_name, "reason": f"Failed to save productivity metrics CSV: {e}"})
            continue

    # Save error repositories information
    if error_repos:
        error_repos_df = pd.DataFrame(error_repos)
        error_repos_csv_path = os.path.join('..', 'result', 'productivity', 'error_repos.csv')
        try:
            os.makedirs(os.path.dirname(error_repos_csv_path), exist_ok=True)
            logging.info(f"Saving error repositories information to {error_repos_csv_path}.")
            error_repos_df.to_csv(error_repos_csv_path, index=False, encoding="utf-8")
            logging.info(f"Generated error repositories CSV: {error_repos_csv_path}")
        except Exception as e:
            logging.error(f"Failed to save error repositories CSV: {e}")
    else:
        logging.info("No error repositories to record.")

    # Calculate and log total elapsed time
    total_elapsed_seconds = time.time() - start_time
    total_elapsed_time = time.strftime("%H:%M:%S", time.gmtime(total_elapsed_seconds))
    logging.info(f"Main function completed. Total elapsed time: {total_elapsed_time}.")


if __name__ == "__main__":
    main()
