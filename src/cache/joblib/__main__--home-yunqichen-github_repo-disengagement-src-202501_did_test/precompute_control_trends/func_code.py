# first line: 69
@memory.cache
def precompute_control_trends(control_groups, productivity, metric_column):
    """
    预计算所有控制仓库的趋势斜率。
    """
    logging.info("Precomputing control trends...")
    control_trends = {}
    for repo in control_groups['repo_name']:
        control_data = productivity[productivity['repo_name'] == repo]
        if control_data.empty:
            control_trends[repo] = None
            continue
        # 确保没有零值
        control_data = control_data[control_data[metric_column] != 0]
        if control_data.empty:
            control_trends[repo] = None
            continue
        # 将时间转换为数值（例如，自1970年以来的秒数）
        time_numeric = (control_data['time'] - pd.Timestamp('1970-01-01')).dt.total_seconds()
        metric = control_data[metric_column].values
        # 使用numpy计算斜率
        cov_xy = np.cov(time_numeric, metric, ddof=0)[0][1]
        var_x = np.var(time_numeric, ddof=0)
        if var_x == 0:
            slope = None
        else:
            slope = cov_xy / var_x
        control_trends[repo] = slope
    logging.info("Control trends precomputed.")
    return control_trends
