import pandas as pd
from pymongo import MongoClient
import requests
import logging
from logging.handlers import RotatingFileHandler
from threading import Thread, Lock
import queue
import time

# MongoDB 配置
WINDOWS_IP = 'localhost'
PORT = 27017
client = MongoClient(f"mongodb://{WINDOWS_IP}:{PORT}/")
db = client["disengagement"]
commits_collection = db["commits"]
cache_collection = db["progress_cache"]
pull_requests_collection = db["pull_requests"]
pr_comments_collection = db["pr_comments"]

# GitHub API tokens
github_tokens = [
    '*********************************************************************************************',
    '*********************************************************************************************',
    '*********************************************************************************************',
]

# 日志配置
log_file = 'fetch_commits.log'
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
handler = RotatingFileHandler(log_file, maxBytes=10 * 1024 * 1024, backupCount=5)
logger.addHandler(handler)

# Token 管理
token_lock = Lock()
token_index = 0


def get_next_token():
    global token_index
    with token_lock:
        token = github_tokens[token_index]
        token_index = (token_index + 1) % len(github_tokens)
        logger.info(f"Switching to token index {token_index}.")
    return token


# 进度缓存管理
cache_lock = Lock()


def load_progress_cache(repo):
    """加载进度缓存"""
    caches = list(cache_collection.find({"repo_name": repo}))
    if caches:
        cache = max(caches, key=lambda x: len(x))
        if "closed_pr" not in cache:
            cache["closed_pr"] = 0
            save_progress_cache(repo, closed_pr=0)
        else:
            cache["closed_pr"] = 0
            save_progress_cache(repo, closed_pr=0)
        logger.info(f"Loaded cache for repo: {repo}.")
        return cache
    else:
        logger.info(f"Initializing new cache for repo: {repo}.")
        new_cache = {
            "repo_name": repo,
            "commit": 1,
            "pull_request": 1,
            "pr_comment": 1,
            "commit_num": 0,
            "pr_num": 0,
            "pr_review_num": 0,
            "commit_page_num": -1,
            "pull_request_page_num": -1,
            "pr_review_page_num": -1,
            "commits_finished": 0,
            "pr_finished": 0,
            "pr_review_finished": 0,
            "closed_pr": 0,  # 标记 closed PR 未爬取
        }
        save_progress_cache(repo, **new_cache)
        return new_cache


def save_progress_cache(repo, **kwargs):
    """保存进度缓存"""
    update_data = {key: value for key, value in kwargs.items() if value is not None}
    cache_collection.update_one(
        {"repo_name": repo},
        {"$set": update_data},
        upsert=True
    )
    logger.info(f"Progress cache updated for repo: {repo} with data: {update_data}")


def threaded_fetch(repo, base_url, collection, page_key, data_type, estimated_total=None):
    task_queue = queue.Queue()
    total_count = [0]
    completed_tasks = set()
    thread_list = []
    max_retries = 3
    max_threads = 16
    task_lock = Lock()
    rate_limit_tokens = {}

    headers = {
        'Authorization': f'token {get_next_token()}',
        'Accept': 'application/vnd.github.v3+json'
    }

    def binary_search_page():
        page_num_key = f"{data_type}_page_num"
        cache = load_progress_cache(repo)
        if cache.get(page_num_key, -1) != -1:
            logger.info(f"Using cached page_num for {data_type}: {cache[page_num_key]}")
            return cache[page_num_key]

        low, high = 1, (estimated_total // 100 + 1) if estimated_total else 2
        max_pages = 10000

        while low <= high:
            mid = (low + high) // 2
            url = f"{base_url}?page={mid}&per_page=100&state=closed"
            try:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                items = response.json()

                if not items or len(items) < 100:
                    high = mid - 1
                else:
                    low = mid + 1
            except Exception as e:
                logger.error(f"Error during binary search for {data_type}: {e}")
                break

        save_progress_cache(repo, **{page_num_key: high})
        logger.info(f"Final page for {data_type} in repo {repo}: {high}")
        return high

    def update_thread_count():
        """动态计算线程数"""
        num_tasks = task_queue.qsize()
        return min(max_threads, num_tasks)

    # cache_final_page = load_progress_cache(repo).get(f"{data_type}_page_num", -1)
    if estimated_total/100 < 8:
        final_page = 8
    else:
        final_page = binary_search_page()

    start_page = load_progress_cache(repo).get(page_key, 1)
    for page in range(start_page, final_page + 1):
        task_queue.put(page)

    logger.info(f"Starting threaded fetch for {data_type} in repo {repo}. Total pages: {final_page}.")

    def worker():
        while not task_queue.empty():
            try:
                page = task_queue.get(timeout=5)
                if page in completed_tasks:
                    task_queue.task_done()
                    continue

                logger.info(f"Fetching {data_type} for {repo}, page {page}.")
                retry_count = 0
                while retry_count < max_retries:
                    try:
                        token = get_next_token()
                        headers['Authorization'] = f'token {token}'
                        url = f"{base_url}?page={page}&per_page=100&state=closed"
                        response = requests.get(url, headers=headers, timeout=10)

                        if response.status_code == 403:
                            reset_time = int(response.headers.get('X-RateLimit-Reset', time.time() + 60))
                            wait_time = reset_time - int(time.time())
                            logger.warning(f"Token {token} hit rate limit. Waiting {wait_time} seconds.")
                            time.sleep(wait_time + 1)
                            retry_count += 1
                            continue

                        response.raise_for_status()
                        items = response.json()

                        if not items:
                            logger.info(f"No more closed {data_type}s for {repo}, page {page}.")
                            break

                        for item in items:
                            item['repo_name'] = repo
                            collection.insert_one(item)

                        with cache_lock:
                            total_count[0] += len(items)

                        logger.info(f"Fetched {len(items)} closed {data_type}s for {repo}, page {page}.")
                        save_progress_cache(repo, **{page_key: page})
                        break
                    except requests.exceptions.Timeout:
                        retry_count += 1
                        logger.warning(f"Timeout occurred on page {page}, retry {retry_count}.")
                    except Exception as e:
                        logger.error(f"Error on page {page} for {data_type}: {e}")
                        break

                completed_tasks.add(page)
                task_queue.task_done()
            except queue.Empty:
                logger.info(f"Task queue is empty for {data_type} in repo {repo}.")
                break

    for _ in range(update_thread_count()):
        t = Thread(target=worker)
        t.start()
        thread_list.append(t)

    task_queue.join()
    for t in thread_list:
        t.join()

    save_progress_cache(repo, closed_pr=1)
    logger.info(f"Finished fetching closed pull requests for {repo}. Total fetched: {total_count[0]}")
    return total_count[0]


def main():
    sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
    project_names = sample_projects[['name', 'commits', 'totalPullRequests']].to_dict(orient='records')

    for project in project_names:
        repo = project['name']
        estimated_prs = project.get('totalPullRequests', 0)
        logger.info(f"Processing {repo}, estimated PRs: {estimated_prs}.")

        cache = load_progress_cache(repo)

        if cache.get("closed_pr", 0) == 1:
            logger.info(f"Skipping {repo}, closed PRs already fetched.")
            continue

        base_url = f"https://api.github.com/repos/{repo}/pulls"
        threaded_fetch(repo, base_url, pull_requests_collection, "pull_request", "pull_request", estimated_prs)


if __name__ == "__main__":
    main()
