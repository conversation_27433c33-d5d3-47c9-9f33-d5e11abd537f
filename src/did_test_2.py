# Import required libraries
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import os
from statsmodels.formula.api import ols
from statsmodels.stats.anova import anova_lm

# Create directories for saving outputs if they don't exist
os.makedirs('../result/DiD_analysis/outputs', exist_ok=True)
os.makedirs('../result/DiD_analysis/plots', exist_ok=True)

# Configure logging
logging.basicConfig(
    filename='../result/DiD_analysis/outputs/analysis.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)


def load_data(attritions_path, productivity_path):
    logging.info("Loading datasets...")
    attritions = pd.read_csv(attritions_path)
    productivity = pd.read_csv(productivity_path)
    logging.info(f"Attritions dataset loaded with {attritions.shape[0]} records.")
    logging.info(f"Productivity dataset loaded with {productivity.shape[0]} records.")
    return attritions, productivity


def preprocess_dates(attritions, productivity):
    logging.info("Converting date columns to datetime format...")
    attritions['attribution_date'] = pd.to_datetime(attritions['attribution_date'])
    productivity['Date'] = pd.to_datetime(productivity['Date'])
    logging.info("Date conversion completed.")
    return attritions, productivity


def assign_multi_event_window(row, time_window):
    """Assign event windows based on attribution dates within a specified time window."""
    if not row['attribution_date']:
        return 'No Event'
    distances = [(row['Date'] - pd.to_datetime(event)).days // 30 for event in row['attribution_date']]
    if any(-time_window <= dist <= 0 for dist in distances):
        return 'Pre-Event'
    elif any(0 < dist <= time_window for dist in distances):
        return 'Post-Event'
    return 'No Effect'


def assign_time_windows(merged_data, time_windows_months):
    logging.info("Assigning time windows...")
    for window in time_windows_months:
        window_col = f'window_{window}'
        merged_data[window_col] = merged_data.apply(
            lambda row: assign_multi_event_window(row, window), axis=1
        )
        logging.info(f"Time window {window} months assigned.")
    return merged_data


def joint_parallel_trends_test(data, metric, treated_col, time_col):
    logging.info("Performing joint parallel trends test...")
    pre_treatment_data = data[data[time_col] < data[treated_col]]

    # Creating dummy variables for pre-treatment periods
    pre_treatment_data['pre_period'] = pre_treatment_data[time_col].dt.to_period('M')
    formula = f"{metric} ~ C(pre_period)"
    model = ols(formula, data=pre_treatment_data).fit()

    # Conducting ANOVA test for joint significance
    anova_results = anova_lm(model)
    logging.info(f"ANOVA results for joint parallel trends test:\n{anova_results}")

    # Plotting pre-trends for visual inspection
    plt.figure(figsize=(10, 6))
    sns.lineplot(data=pre_treatment_data, x='pre_period', y=metric, ci=None)
    plt.title(f"Parallel Trends Test for {metric}")
    plt.xlabel("Pre-Treatment Period")
    plt.ylabel(f"Mean {metric}")
    plt.grid(axis='y')
    plt.tight_layout()
    plot_path = f'../result/DiD_analysis/plots/Parallel_Trends_Test_{metric}.png'
    plt.savefig(plot_path)
    plt.close()
    logging.info(f"Parallel trends plot saved at {plot_path}")
    return anova_results


def perform_DiD_analysis(merged_data, time_windows_months, metrics):
    logging.info("Starting DiD analysis...")
    for metric in metrics:
        for window in time_windows_months:
            window_col = f'window_{window}'
            model_data = merged_data[merged_data[window_col] != 'No Effect']
            model_data = model_data.dropna(subset=[metric, 'treated', 'post_treatment', 'DiD'])

            # Adding interaction term
            formula = f"{metric} ~ treated + post_treatment + DiD"
            model = ols(formula, data=model_data).fit()
            logging.info(f"Model for {metric} in {window} months:\n{model.summary()}")
    return


def main():
    # Paths to datasets
    attritions_path = '../result/attritions.csv'
    productivity_path = '../result/productivity/merged_pr_productivity_metrics.csv'

    # Load and preprocess data
    attritions, productivity = load_data(attritions_path, productivity_path)
    attritions, productivity = preprocess_dates(attritions, productivity)

    # Further processing
    time_windows_months = [3, 6, 12]
    metrics = ['ACCEPT_RATE', 'AVG_TIME_TO_MERGE', 'AVG_TIME_TO_REACT']

    # Assign time windows
    merged_data = assign_time_windows(productivity, time_windows_months)

    # Perform Difference-in-Differences analysis
    perform_DiD_analysis(merged_data, time_windows_months, metrics)

    # Joint test for parallel trends
    for metric in metrics:
        joint_parallel_trends_test(merged_data, metric, 'treated', 'Date')


if __name__ == "__main__":
    main()
