import pandas as pd
from pymongo import MongoClient
import requests
import logging
from logging.handlers import RotatingFileHandler
from threading import Thread, Lock
import queue
import time
from collections import deque

# MongoDB Configuration
client = MongoClient("mongodb://localhost:27017/")
db = client["disengagement"]
pull_requests_collection = db["pull_requests"]
cache_collection = db["progress_cache"]

# GitHub API tokens
github_tokens = deque([
    '*********************************************************************************************',
    '*********************************************************************************************',
    '*********************************************************************************************',
])

# Logging Configuration
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = RotatingFileHandler('fetch_commits.log', maxBytes=10*1024*1024, backupCount=5)
logger.addHandler(handler)

# Token Management
token_lock = Lock()

def get_next_token():
    with token_lock:
        token = github_tokens.popleft()
        github_tokens.append(token)
        logger.info(f"Switching to token: {token}")
    return token

# Cache Management
cache_lock = Lock()

def load_progress_cache(repo):
    cache = cache_collection.find_one({"repo_name": repo})
    if cache:
        logger.info(f"Loaded cache for repo: {repo}.")
        return cache
    else:
        new_cache = {
            "repo_name": repo,
            "pull_request_page_num": 0,
            "pr_status_all": 0,
            # Other fields initialization...
        }
        save_progress_cache(repo, **new_cache)
        logger.info(f"Initialized new cache for repo: {repo}.")
        return new_cache

def save_progress_cache(repo, **kwargs):
    with cache_lock:
        cache_collection.update_one(
            {"repo_name": repo},
            {"$set": kwargs},
            upsert=True
        )
    logger.info(f"Progress cache updated for repo: {repo} with data: {kwargs}")

# Ensure Indexes
def ensure_indexes():
    pull_requests_collection.create_index(
        [("repo_name", 1), ("number", 1)],
        unique=True,
        name="unique_repo_name_number"
    )
    logger.info("Ensured unique index on pull_requests for (repo_name, number).")

# Get Total Pages Using Link Header
def get_total_pages(repo, pr_state="all"):
    base_url = f"https://api.github.com/repos/{repo}/pulls"
    headers = {
        'Authorization': f'token {get_next_token()}',
        'Accept': 'application/vnd.github.v3+json'
    }
    url = f"{base_url}?state={pr_state}&per_page=1"
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        links = response.headers.get('Link', '')
        if 'rel="last"' in links:
            last_page_url = [link for link in links.split(',') if 'rel="last"' in link][0]
            last_page_url = last_page_url.split('page=')[-1].split('>')[0].strip()
            return int(last_page_url)
        else:
            return 1
    else:
        logger.error(f"Failed to get total pages for {repo}, state={pr_state}")
        return 1

# Threaded Fetch with Enhanced Error Handling
def threaded_fetch(repo, base_url, collection, page_key, data_type, pr_state="all"):
    task_queue = queue.Queue()
    completed_pages = set()
    threads = []
    max_threads = 40

    total_pages = get_total_pages(repo, pr_state)
    for page in range(1, total_pages + 1):
        task_queue.put(page)
    logger.info(f"Starting threaded fetch for {data_type} in repo {repo}. Total pages: {total_pages}.")

    def worker():
        while not task_queue.empty():
            try:
                page = task_queue.get(timeout=5)
                if page in completed_pages:
                    task_queue.task_done()
                    continue

                headers = {
                    'Authorization': f'token {get_next_token()}',
                    'Accept': 'application/vnd.github.v3+json'
                }
                url = f"{base_url}?page={page}&per_page=100&state={pr_state}"
                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 403:
                    logger.warning(f"Token hit rate limit. Switching to next token.")
                    get_next_token()
                    task_queue.put(page)
                    task_queue.task_done()
                    continue
                response.raise_for_status()
                items = response.json()

                if not items:
                    logger.info(f"No more PRs for {repo}, page {page}, state={pr_state}.")
                    completed_pages.add(page)
                    task_queue.task_done()
                    continue

                for item in items:
                    item['repo_name'] = repo
                    try:
                        collection.insert_one(item)
                    except Exception as e:
                        if "E11000 duplicate key error" in str(e):
                            logger.info(f"PR {item['number']} in {repo} already exists.")
                        else:
                            logger.error(f"Error inserting PR {item['number']} in {repo}: {e}")
                logger.info(f"Fetched {len(items)} PRs for {repo}, page {page}, state={pr_state}.")
                completed_pages.add(page)
                save_progress_cache(repo, **{page_key: page})
                task_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"Error on page {page} for {data_type}: {e}")
                task_queue.task_done()

    for _ in range(min(max_threads, total_pages)):
        t = Thread(target=worker)
        t.start()
        threads.append(t)

    task_queue.join()
    for t in threads:
        t.join()

    save_progress_cache(repo, pr_status_all=1)
    logger.info(f"Finished fetching PRs for {repo}, state={pr_state}.")

# Main Function
def main():
    ensure_indexes()
    sample_projects = pd.read_csv('../data/sample_projects_total.csv')
    project_names = sample_projects[['repo_name', 'commits', 'totalPullRequests']].to_dict(orient='records')

    for project in project_names:
        repo = project['repo_name']
        cache = load_progress_cache(repo)
        if cache.get("pr_status_all", 0) == 1:
            logger.info(f"Skipping {repo}, PRs with state=all already fetched.")
            continue

        base_url = f"https://api.github.com/repos/{repo}/pulls"
        threaded_fetch(repo, base_url, pull_requests_collection, "pull_request_page_num", "pull_request", pr_state="all")

if __name__ == "__main__":
    main()