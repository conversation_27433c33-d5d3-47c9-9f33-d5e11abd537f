{"cells": [{"cell_type": "code", "execution_count": 10, "id": "3421c9cc73131e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The truck factor of ../../Truck-Factor (614600ed6b36bb8f452bc6eab7c9b95320f5d7be) is: 1\n", "with author(s): <PERSON><PERSON><PERSON><PERSON>\n"]}], "source": ["import os.path\n", "from truckfactor.compute import main\n", "import pandas as pd\n", "import pydriller\n", "truckfactor, commit_sha, author = main('../../Truck-Factor')\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fab8a528", "metadata": {"ExecuteTime": {"end_time": "2024-12-11T08:05:00.997107Z", "start_time": "2024-12-11T08:05:00.481956Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# read sample_projects file\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m# sample repo to download git repo by prdiller\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m sample_projects \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m../data/sample_projects_quartiles.csv\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      4\u001b[0m sample_projects\n", "\u001b[0;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["# read sample_projects file\n", "# sample repo to download git repo by prdiller\n", "sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')\n", "sample_projects"]}, {"cell_type": "code", "execution_count": 34, "id": "edf83188", "metadata": {}, "outputs": [{"data": {"text/plain": ["('mixer/ftl-sdk',\n", " 'https://github.com/mixer/ftl-sdk',\n", " 'master',\n", " '../../cloned_repo/mixer_ftl-sdk')"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_name = sample_projects[\"repo_name\"][0]\n", "repo_path = f\"https://github.com/{repo_name}\"\n", "default_branch = sample_projects[\"defaultBranch\"][0]\n", "# 使用下划线替换斜杠\n", "cloned_repo_name = repo_name.replace('/', '_')\n", "# 创建目录\n", "if not os.path.exists(f\"../../cloned_repo/{cloned_repo_name}\"):\n", "    os.makedirs(f\"../../cloned_repo/{cloned_repo_name}\")\n", "path_to_clone = f\"../../cloned_repo/{cloned_repo_name}\"\n", "repo_name, repo_path, default_branch, path_to_clone"]}, {"cell_type": "code", "execution_count": 41, "id": "186cecd1", "metadata": {}, "outputs": [{"data": {"text/plain": ["<generator object Repository.traverse_commits at 0x7f111cf3ca40>"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["repo = pydriller.Repository(\n", "    path_to_repo=repo_path,\n", "    only_in_branch=default_branch,\n", "    clone_repo_to=path_to_clone,\n", "    num_workers=16,\n", ")\n", "repo.traverse_commits()"]}, {"cell_type": "markdown", "id": "06083cf9", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce1b28d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["09fc8aadc86a7fe0d99c9a997b9a7198333bc013\n", "<PERSON>\n", "micha<PERSON>@mcprohosting.com\n", "Initial Commit\n", "\n", "Signed-off-by: <PERSON> <<EMAIL>>\n", "2015-12-14 08:16:58-05:00\n", "2015-12-14 08:16:58-05:00\n"]}, {"ename": "AttributeError", "evalue": "'Commit' object has no attribute 'modifications'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[37], line 8\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28mprint\u001b[39m(commit\u001b[38;5;241m.\u001b[39mauthor_date)\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28mprint\u001b[39m(commit\u001b[38;5;241m.\u001b[39mcommitter_date)\n\u001b[0;32m----> 8\u001b[0m \u001b[38;5;28mprint\u001b[39m(commit\u001b[38;5;241m.\u001b[39mmodifications)\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28mprint\u001b[39m(commit\u001b[38;5;241m.\u001b[39min_main_branch)\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28mprint\u001b[39m(commit\u001b[38;5;241m.\u001b[39mmerge)\n", "\u001b[0;31mAttributeError\u001b[0m: 'Commit' object has no attribute 'modifications'"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4448b05", "metadata": {"notebookRunGroups": {"groupValue": "1"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["fatal: not a git repository (or any of the parent directories): .git\n", "Seems to be an empty repository. Cannot compute truck factor for it.\n"]}, {"ename": "SystemExit", "evalue": "1", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[0;31mSystemExit\u001b[0m\u001b[0;31m:\u001b[0m 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/disengagement/lib/python3.11/site-packages/IPython/core/interactiveshell.py:3585: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": ["truckfactor, commit_sha, author = main(path_to_clone)\n"]}, {"cell_type": "code", "execution_count": null, "id": "0301483a", "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[63], line 21\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;66;03m# Clone the repository with progress reporting\u001b[39;00m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 21\u001b[0m     Repo\u001b[38;5;241m.\u001b[39mclone_from(repo_path, to_path\u001b[38;5;241m=\u001b[39mpath_to_clone, branch\u001b[38;5;241m=\u001b[39mdefault_branch)\n\u001b[1;32m     22\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m GitCommandError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m     23\u001b[0m     logging\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mError cloning repository: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/git/repo/base.py:1525\u001b[0m, in \u001b[0;36mRepo.clone_from\u001b[0;34m(cls, url, to_path, progress, env, multi_options, allow_unsafe_protocols, allow_unsafe_options, **kwargs)\u001b[0m\n\u001b[1;32m   1523\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m env \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1524\u001b[0m     git\u001b[38;5;241m.\u001b[39mupdate_environment(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39menv)\n\u001b[0;32m-> 1525\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_clone(\n\u001b[1;32m   1526\u001b[0m     git,\n\u001b[1;32m   1527\u001b[0m     url,\n\u001b[1;32m   1528\u001b[0m     to_path,\n\u001b[1;32m   1529\u001b[0m     GitCmdObjectDB,\n\u001b[1;32m   1530\u001b[0m     progress,\n\u001b[1;32m   1531\u001b[0m     multi_options,\n\u001b[1;32m   1532\u001b[0m     allow_unsafe_protocols\u001b[38;5;241m=\u001b[39mallow_unsafe_protocols,\n\u001b[1;32m   1533\u001b[0m     allow_unsafe_options\u001b[38;5;241m=\u001b[39mallow_unsafe_options,\n\u001b[1;32m   1534\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m   1535\u001b[0m )\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/git/repo/base.py:1391\u001b[0m, in \u001b[0;36mRepo._clone\u001b[0;34m(cls, git, url, path, odb_default_type, progress, multi_options, allow_unsafe_protocols, allow_unsafe_options, **kwargs)\u001b[0m\n\u001b[1;32m   1383\u001b[0m     handle_process_output(\n\u001b[1;32m   1384\u001b[0m         proc,\n\u001b[1;32m   1385\u001b[0m         \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1388\u001b[0m         decode_streams\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m   1389\u001b[0m     )\n\u001b[1;32m   1390\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1391\u001b[0m     (stdout, stderr) \u001b[38;5;241m=\u001b[39m proc\u001b[38;5;241m.\u001b[39mcommunicate()\n\u001b[1;32m   1392\u001b[0m     cmdline \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(proc, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124margs\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   1393\u001b[0m     cmdline \u001b[38;5;241m=\u001b[39m remove_password_if_present(cmdline)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/subprocess.py:1209\u001b[0m, in \u001b[0;36mPopen.communicate\u001b[0;34m(self, input, timeout)\u001b[0m\n\u001b[1;32m   1206\u001b[0m     endtime \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m   1208\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1209\u001b[0m     stdout, stderr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_communicate(\u001b[38;5;28minput\u001b[39m, endtime, timeout)\n\u001b[1;32m   1210\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[1;32m   1211\u001b[0m     \u001b[38;5;66;03m# https://bugs.python.org/issue25942\u001b[39;00m\n\u001b[1;32m   1212\u001b[0m     \u001b[38;5;66;03m# See the detailed comment in .wait().\u001b[39;00m\n\u001b[1;32m   1213\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/subprocess.py:2115\u001b[0m, in \u001b[0;36mPopen._communicate\u001b[0;34m(self, input, endtime, orig_timeout)\u001b[0m\n\u001b[1;32m   2108\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timeout(endtime, orig_timeout,\n\u001b[1;32m   2109\u001b[0m                         stdout, stderr,\n\u001b[1;32m   2110\u001b[0m                         skip_check_and_raise\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m   2111\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(  \u001b[38;5;66;03m# Impossible :)\u001b[39;00m\n\u001b[1;32m   2112\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_check_timeout(..., skip_check_and_raise=True) \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m   2113\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfailed to raise TimeoutExpired.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m-> 2115\u001b[0m ready \u001b[38;5;241m=\u001b[39m selector\u001b[38;5;241m.\u001b[39mselect(timeout)\n\u001b[1;32m   2116\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timeout(endtime, orig_timeout, stdout, stderr)\n\u001b[1;32m   2118\u001b[0m \u001b[38;5;66;03m# XXX Rewrite these to use non-blocking I/O on the file\u001b[39;00m\n\u001b[1;32m   2119\u001b[0m \u001b[38;5;66;03m# objects; they are no longer using C stdio!\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/selectors.py:415\u001b[0m, in \u001b[0;36m_PollLikeSelector.select\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    413\u001b[0m ready \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    414\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 415\u001b[0m     fd_event_list \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_selector\u001b[38;5;241m.\u001b[39mpoll(timeout)\n\u001b[1;32m    416\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n\u001b[1;32m    417\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ready\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": []}, {"cell_type": "code", "execution_count": 55, "id": "a703e09e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The truck factor of ../../cloned_repo/vuestorefront_vue-storefront (ca1bf1a709ad26db7f046124ebe4894d2e7a1e5e) is: 7\n", "with author(s): <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n"]}, {"data": {"text/plain": ["(7,\n", " None,\n", " ['<PERSON><PERSON>',\n", "  '<PERSON><PERSON>',\n", "  'pkarw',\n", "  '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "  '<PERSON><PERSON><PERSON>',\n", "  '<PERSON><PERSON>',\n", "  'lje<PERSON><PERSON>'])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["res = main(path_to_clone)\n", "res"]}], "metadata": {"kernelspec": {"display_name": "disengagement", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 5, "nbformat_minor": 9}