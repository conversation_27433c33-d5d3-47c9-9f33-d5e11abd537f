import logging
from pymongo import MongoClient
import pandas as pd

# Configure logging
log_file = '../logs/core_developer_breaks.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


def get_commit_file_repo_name(repo_name):
    file_path = f"../data/commits/{repo_name.replace('/', '_')}_commits.csv"
    repo_commit = pd.read_csv(file_path)
    # Exclude merge commits with those who have two values in the columns named 'parent_shas'
    repo_commit = repo_commit[repo_commit['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)
    # Exclude bot accounts
    bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')
    bot_developers = bot_developers['bot_name'].tolist()
    repo_commit = repo_commit[~repo_commit['author_login'].isin(bot_developers)].reset_index(drop=True)
    return repo_commit


# Function to get pauses list with start and end dates
# Input: commits_dates_sorted: a pandas series of commit dates sorted in ascending order
# Output: pauses_df: a pandas dataframe with columns [id, length, start_date, end_date]
def get_pauses_list_with_start_end_dates(commits_dates_sorted):
    pauses = []
    column_names = ['id', 'length', 'start_date', 'end_date']
    id = 0
    for i in range(1, len(commits_dates_sorted)):
        start_date = commits_dates_sorted.iloc[i - 1]
        end_date = commits_dates_sorted.iloc[i]
        if (end_date - start_date).days == 0:
            continue
        pause_length = (end_date - start_date).days
        pauses.append([id, pause_length, start_date, end_date])
        id += 1
    pauses_df = pd.DataFrame(pauses, columns=column_names)
    return pauses_df


def calculate_t_fov(pauses, total_duration):
    """Calculate threshold for break detection (t_fov) using total time span."""
    if len(pauses) < 4:
        return None

    # Calculate IQR based on pause lengths
    pauses_days = pd.Series(pauses['length'].tolist())  # Convert to Series
    Q1 = pauses_days.quantile(0.25)
    Q3 = pauses_days.quantile(0.75)
    IQR = Q3 - Q1
    Tfov = Q3 + 3 * IQR

    # Optionally scale by total_duration (e.g., total duration could be in years, months)
    return Tfov


def process_repo_data_and_store(project_names, core_devs_data):
    """
    Process data for each repository based on its commits and core developers,
    then store the results in MongoDB incrementally.
    """

    def process_repo_data(project_names, core_devs_data, collection):
        logging.info("Processing core developers data.")
        core_devs_data['core_developers'] = core_devs_data['core_developers'].apply(
            lambda x: x if isinstance(x, list) else eval(x)
        )

        project_counter = 0
        for repo_name in project_names:
            logging.info(f"Processing repository: {repo_name} (Project ID: {project_counter})")

            try:
                repo_commits = get_commit_file_repo_name(repo_name)
            except Exception as e:
                logging.error(f"Error fetching commit data for repository '{repo_name}': {e}")
                continue

            try:
                core_developers = core_devs_data[core_devs_data['repo_name'] == repo_name]['core_developers'].iloc[0]
            except Exception as e:
                logging.error(f"Error fetching core developers for repository '{repo_name}': {e}")
                continue

            core_dev_counter = 0
            repo_commits['date'] = pd.to_datetime(repo_commits['date'], errors='coerce')
            for core_dev in core_developers:
                dev_commits = repo_commits[repo_commits['author_login'] == core_dev].copy()

                # Convert 'date' to datetime if it's not already
                dev_commits['date'] = pd.to_datetime(dev_commits['date'], errors='coerce')
                dev_commits = dev_commits.dropna(subset=['date'])  # Drop any rows where the date is NaT (Not a Time)

                # Ensure that 'date' is in datetime format before proceeding
                if not pd.api.types.is_datetime64_any_dtype(dev_commits['date']):
                    dev_commits['date'] = pd.to_datetime(dev_commits['date'], errors='coerce')

                # Normalize the date to remove the time portion (keeping only the day)
                dev_commits['date'] = dev_commits['date'].dt.normalize()  # Normalize to day level

                dev_commits = dev_commits.sort_values(by='date')

                # Calculate the number of commits for this developer
                commit_number = len(dev_commits)

                # Detect pauses (differences between commits)
                pauses = get_pauses_list_with_start_end_dates(dev_commits['date'])
                if pauses.empty:
                    logging.info(f"No pauses detected for core developer {core_dev} in repository {repo_name}.")
                    continue

                # Calculate total duration for the developer's commit activity
                total_duration = (dev_commits['date'].max() - dev_commits['date'].min()).days

                # Calculate t_fov based on the entire time span
                t_fov = calculate_t_fov(pauses, total_duration)

                # Process breaks
                Dev_Breaks = set()  # Use a set to automatically remove duplicates
                for _, p in pauses.iterrows():
                    if t_fov and p['length'] > t_fov:
                        Dev_Breaks.add((p['id'], p['start_date'], p['end_date'], p['length'], t_fov))

                # Convert breaks to list of dictionaries
                Dev_Breaks = [
                    {'id': b[0], 'start_date': b[1], 'end_date': b[2], 'pause_duration': b[3], 't_fov': b[4]}
                    for b in Dev_Breaks
                ]
                # sort the breaks by start_date
                Dev_Breaks = sorted(Dev_Breaks, key=lambda x: x['start_date'])
                dev_output = {
                    "repo_id": project_counter,
                    "repo_name": repo_name,
                    "core_dev_id": core_dev_counter,
                    "core_dev_login": core_dev,
                    "commit_number": commit_number,  # Add commit number here
                    "Breaks": Dev_Breaks,
                    "Attrition": None,
                }

                # Check for attrition (if the developer hasn't committed in the last year)
                last_commit_date = dev_commits['date'].max()
                if (repo_commits['date'].max() - last_commit_date).days > 365:
                    dev_output["Attrition"] = {
                        "attrition_date": str(last_commit_date.date())
                    }
                try:
                    collection.insert_one(dev_output)
                except Exception as e:
                    logging.error(f"Error storing data for core developer {core_dev} in MongoDB: {e}")
                core_dev_counter += 1
            project_counter += 1

    logging.info("Connecting to MongoDB.")
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["developer_breaks"]
    collection.drop()
    collection.create_index([("repo_name", 1), ("core_dev_id", 1)], unique=True)
    collection.create_index([("core_dev_login", 1)])
    collection.create_index([("repo_name", 1)])
    logging.info("Starting data processing for all repositories.")
    process_repo_data(project_names, core_devs_data, collection)
    logging.info("Data processing completed.")


def main():
    sample_projects = pd.read_csv('../data/sample_projects_quartiles.csv')
    sample_repo = sample_projects['name'].tolist()
    # sample_repo = sample_repo[0:20]
    core_devs_data = pd.read_csv('../data/core_developer_list_4000_repo.csv')
    process_repo_data_and_store(sample_repo, core_devs_data)


if __name__ == "__main__":
    main()
