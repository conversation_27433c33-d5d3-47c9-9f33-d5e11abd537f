import pandas as pd
import numpy as np
from sklearn.neighbors import KNeighborsRegressor
from sklearn.preprocessing import StandardScaler
from joblib import Parallel, delayed
import os
import logging
import statsmodels.api as sm

# 配置日志
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "did_analysis_parallel.log")),
        logging.StreamHandler(),
    ],
)

# 设置输出目录
output_dir = "../result/did_result_parallel/"
os.makedirs(output_dir, exist_ok=True)


# 保存数据到文件
def save_data(data, filename):
    filepath = os.path.join(output_dir, filename)
    data.to_csv(filepath, index=False)
    logging.info(f"Saved {filename} to {filepath}.")


# SIGMOID函数
def sigmoid(x):
    return 1 / (1 + np.exp(-x))


# 计算单个仓库的特征（用于PSM）
def compute_repo_features_for_psm(
    repo_name, productivity_df, metric_column="pr_throughput"
):
    repo_data = productivity_df[productivity_df["repo_name"] == repo_name].copy()
    if repo_data.empty:
        return {
            "repo_name": repo_name,
            "sum_I_it_last_12_weeks": np.nan,
            "sum_pr_throughput_last_12_weeks": np.nan,
        }

    repo_data = repo_data.sort_values("standardized_time_weeks")
    repo_data["sum_I_it_last_12_weeks"] = (
        repo_data["I_it"].shift(1).rolling(window=12, min_periods=1).sum()
    )
    repo_data["sum_pr_throughput_last_12_weeks"] = (
        repo_data[metric_column].shift(1).rolling(window=12, min_periods=1).sum()
    )
    latest_features = repo_data.iloc[-1][
        ["sum_I_it_last_12_weeks", "sum_pr_throughput_last_12_weeks"]
    ]

    return {
        "repo_name": repo_name,
        "sum_I_it_last_12_weeks": latest_features["sum_I_it_last_12_weeks"],
        "sum_pr_throughput_last_12_weeks": latest_features[
            "sum_pr_throughput_last_12_weeks"
        ],
    }


# 并行计算所有仓库的特征
def compute_all_repo_features_parallel(
    repo_names, productivity_df, metric_column="pr_throughput"
):
    results = Parallel(n_jobs=-1)(
        delayed(compute_repo_features_for_psm)(
            repo_name, productivity_df, metric_column
        )
        for repo_name in repo_names
    )
    return pd.DataFrame(results)


# 为单个处理仓库匹配对照仓库
def match_controls_for_treatment(
    treatment_repo, available_controls, features_df, n_neighbors=5
):
    t_feats = features_df.loc[features_df["repo_name"] == treatment_repo]
    if pd.isna(t_feats["sum_I_it_last_12_weeks"].iloc[0]) or pd.isna(
        t_feats["sum_pr_throughput_last_12_weeks"].iloc[0]
    ):
        logging.warning(f"Treatment repo {treatment_repo} has incomplete features.")
        return []

    control_features = features_df.loc[
        features_df["repo_name"].isin(available_controls)
    ]
    control_features = control_features.dropna(
        subset=["sum_I_it_last_12_weeks", "sum_pr_throughput_last_12_weeks"]
    )

    if len(control_features) < n_neighbors:
        logging.warning(
            f"Less than {n_neighbors} controls available for {treatment_repo}."
        )
        return []

    # 提取特征并标准化
    t_features = t_feats[["sigmoid_sum_combined"]].values
    control_features_values = control_features[["sigmoid_sum_combined"]].values

    scaler = StandardScaler()
    t_features_scaled = scaler.fit_transform(t_features)
    control_features_values_scaled = scaler.transform(control_features_values)

    # 使用KNN匹配
    knn = KNeighborsRegressor(n_neighbors=n_neighbors, algorithm="auto")
    knn.fit(
        control_features_values_scaled, np.zeros(len(control_features_values_scaled))
    )
    distances, indices = knn.kneighbors(t_features_scaled, n_neighbors=n_neighbors)

    control_repo_names = control_features["repo_name"].tolist()
    matched_controls = [control_repo_names[idx] for idx in indices.flatten()]
    return matched_controls

def match_all_treatments_psm(treatment_repos_with_attrition_date, all_repo_names, productivity, n_neighbors=5):
    # Initialize a set of available control repositories
    available_controls = set(all_repo_names)
    
    # Remove treatment repositories from available controls
    treatment_repos = treatment_repos_with_attrition_date['repo_name'].tolist()
    available_controls -= set(treatment_repos)
    
    
    # Create a dictionary to store treatment repo feature values at treatment week
    treatment_features = {}
    for index, row in treatment_repos_with_attrition_date.iterrows():
        t_repo = row['repo_name']
        # Find the treatment week as the attrition date with is_treated is 1
        value = productivity.loc[(productivity['repo_name'] == t_repo) & (productivity['is_treated'] == 1)]['sigmoid_sum_combined'].values
        if len(value) == 0 or pd.isna(value[0]):
            logging.warning(f"Treatment repo {t_repo} has no or invalid sigmoid_sum_combined value at treatment week.")
            continue
        treatment_features[t_repo] = value[0]
    
    # Create a Series of latest sigmoid_sum_combined for available control repositories
    # Get the latest week for each control repo
    control_latest_weeks = productivity.loc[productivity['repo_name'].isin(available_controls)].groupby('repo_name')['standardized_time_weeks'].max()
    # Get the corresponding sigmoid_sum_combined values
    control_features = productivity.loc[productivity['repo_name'].isin(available_controls)].set_index(['repo_name', 'standardized_time_weeks'])['sigmoid_sum_combined']
    # Create a dictionary for control repo feature values
    control_feature_dict = {}
    for repo, week in control_latest_weeks.items():
        try:
            control_feature_dict[repo] = control_features.loc[(repo, week)]
        except KeyError:
            logging.warning(f"Control repo {repo} has no sigmoid_sum_combined value at week {week}.")
            continue
    
    # Convert control_feature_dict to a Series
    control_features_series = pd.Series(control_feature_dict)
    
    matched_pairs = {}
    
    for t_repo, t_value in treatment_features.items():
        # Exclude treatment repo from available controls
        temp_available_controls = available_controls - {t_repo}
        
        # Get sigmoid_sum_combined values for available controls
        control_values = control_features_series[temp_available_controls]
        
        if len(control_values) < n_neighbors:
            logging.warning(f"Less than {n_neighbors} controls available for {t_repo}. Matching as many as possible.")
            n_neighbors_current = len(control_values)
        else:
            n_neighbors_current = n_neighbors
        
        if n_neighbors_current == 0:
            logging.warning(f"No controls available for {t_repo}. Skipping matching.")
            continue  # No controls available
        
        # Calculate absolute difference from treatment value
        differences = control_values - t_value
        abs_differences = differences.abs()
        
        # Get top n_neighbors controls with smallest differences
        closest_controls = abs_differences.nsmallest(n_neighbors_current).index.tolist()
        
        # Record the matched controls
        matched_pairs[t_repo] = closest_controls
        
        # Remove matched controls from available controls
        available_controls -= set(closest_controls)
    
    # Verify matching results
    for t_repo, controls in matched_pairs.items():
        if len(controls) != n_neighbors:
            logging.warning(f"Treatment repo {t_repo} has {len(controls)} controls, expected {n_neighbors}.")
    
    return matched_pairs

# 主函数
if __name__ == "__main__":
    metric_column = "pr_throughput"
    timewindow_weeks = 12
    n_neighbors = 3

    # 加载数据
    logging.info("Loading data...")
    repo_info = pd.read_csv("../data/sample_projects_quartiles.csv")
    attrition = pd.read_csv("../result/attritions.csv")
    productivity = pd.read_csv("../result/productivity_metrics_202501015.csv")
    logging.info("Data loaded successfully.")

    # # 数据完整性检查
    required_columns = ["repo_name", "datetime", "datetime", "pr_throughput"]
    for col in required_columns:
        if col not in productivity.columns:
            logging.error(f"Missing column: {col}")
            raise ValueError(f"Missing column: {col}")

    # # 数据预处理
    logging.info("Preprocessing data...")
    attrition["attrition_date"] = pd.to_datetime(attrition["attrition_date"])
    productivity["time"] = pd.to_datetime(productivity["datetime"])
    global_min_time = productivity["time"].min()
    productivity["standardized_time_weeks"] = (
        (productivity["time"] - global_min_time).dt.days // 7
    ).astype(int)
    logging.info("Data preprocessing completed.")

    # # 标记 is_treated
    attrition_dict = attrition.set_index("repo_name")["attrition_date"].to_dict()
    # def mark_is_treatment(row):
    #     """mark only the first attrition in the time unit"""
    #     attrition_date = attrition_dict.get(row["repo_name"], pd.NaT)
    #     # standardize the time unit and mark the attrition time unit
    #     if pd.isnull(attrition_date):
    #         return 0
    #     else:
    #         attrition_time_unit = (attrition_date - global_min_time).days // 7
    #         return 1 if row["standardized_time_weeks"] == attrition_time_unit else 0

    # productivity["is_treated"] = productivity.apply(mark_is_treatment, axis=1)

    # # 计算 I_it
    # logging.info("Calculating I_it...")
    # productivity = productivity.sort_values(["repo_name", "time"])
    # productivity["O_it"] = productivity[metric_column]
    # productivity["O_i_t_minus_1"] = productivity.groupby("repo_name")[
    #     metric_column
    # ].shift(1)
    # productivity["I_it"] = np.log(
    #     (productivity["O_it"] + 1) / (productivity["O_i_t_minus_1"] + 1)
    # ).fillna(0)

    # # 生成 SIGMOID 特征
    # productivity["sum_I_it_last_12_weeks"] = productivity.groupby("repo_name")[
    #     "I_it"
    # ].transform(lambda x: x.shift(1).rolling(window=12, min_periods=1).sum())
    # productivity["sum_pr_throughput_last_12_weeks"] = productivity.groupby("repo_name")[
    #     metric_column
    # ].transform(lambda x: x.shift(1).rolling(window=12, min_periods=1).sum())
    # productivity["sum_combined"] = (
    #     productivity["sum_I_it_last_12_weeks"]
    #     + productivity["sum_pr_throughput_last_12_weeks"]
    # )
    # productivity["sigmoid_sum_combined"] = sigmoid(productivity["sum_combined"])

    # 保存标准化数据
    save_data(productivity, "standardized_productivity_parallel.csv")
    
    productivity = pd.read_csv("../result/standardized_productivity/standardized_productivity_parallel.csv")
    # 提取处理组仓库
    logging.info("Extracting treatment repos...")
    attrition_counts = attrition['repo_name'].value_counts().reset_index()
    attrition_counts.columns = ['repo_name', 'attrition_count']
    repos_with_one_attrition = attrition_counts[attrition_counts['attrition_count'] == 1]
    # repos_with_one_attrition
    treatment_repos_with_attrition_date = pd.merge(
        repos_with_one_attrition, attrition, on="repo_name", how="left"
    )
    control_groups = repo_info

    all_repo_names = productivity["repo_name"].unique()
    logging.info(f"Found {len(treatment_repos_with_attrition_date)} treatment repos.")

    # 并行计算所有仓库的特征


    # match_all_treatments_psm based on the computed features, which is sigmoid_sum_combined
    logging.info("Matching controls sequentially...")
    matched_pairs = match_all_treatments_psm(
        treatment_repos_with_attrition_date, all_repo_names, productivity, n_neighbors)

    matched_pairs_df = pd.DataFrame(
        [
            {"treatment_repo": t_repo, "control_repo": c_repo}
            for t_repo, controls in matched_pairs.items()
            for c_repo in controls
        ]
    )
    save_data(matched_pairs_df, "matched_pairs_sequential.csv")

    # 面板差分分析
    logging.info("Performing panel DiD analysis with control variables...")
    combined_data = []
    for t_repo, controls in matched_pairs.items():
        t_time = attrition_dict.get(t_repo, pd.NaT)
        if pd.isna(t_time):
            continue
        these_repos = [t_repo] + controls
        sub_df = productivity[productivity["repo_name"].isin(these_repos)].copy()
        treatment_week = sub_df.loc[
            sub_df["repo_name"] == t_repo, "standardized_time_weeks"
        ].max()
        if pd.isna(treatment_week):
            continue
        sub_df["relative_time_weeks"] = (
            sub_df["standardized_time_weeks"] - treatment_week
        )
        sub_df = sub_df[
            sub_df["relative_time_weeks"].between(-timewindow_weeks, timewindow_weeks)
        ]
        sub_df["post_treatment"] = (sub_df["relative_time_weeks"] >= 0).astype(int)
        sub_df["is_treatment"] = (sub_df["repo_name"] == t_repo).astype(int)
        sub_df["is_treatment_post_treatment"] = (
            sub_df["post_treatment"] * sub_df["is_treatment"]
        )

        # 添加控制变量
        control_vars = [
            # "commits",
            # "releases",
            # "forks",
            "mainLanguage",
            # "stargazers",
            # "totalIssues",
            # "totalPullRequests",
            # "duration",
        ]
        missing_vars = [var for var in control_vars if var not in repo_info.columns]
        if missing_vars:
            logging.warning(f"Missing control variables: {missing_vars}")
        control_vars = [var for var in control_vars if var in repo_info.columns]
        for var in control_vars:
            sub_df[var] = sub_df["repo_name"].map(repo_info.set_index("repo_name")[var])

        combined_data.append(sub_df)

    if not combined_data:
        logging.warning("No matched data found for DiD.")
    else:
        final_data = pd.concat(combined_data, ignore_index=True)

        # 准备回归模型数据，包括控制变量
        control_vars = [
            "commits",
            "releases",
            "forks",
            "stargazers",
            "totalIssues",
            "totalPullRequests",
            "duration",
        ]
        if "mainLanguage" in final_data.columns:
            # 处理 mainLanguage 为分类变量，转为哑变量
            final_data = pd.get_dummies(
                final_data, columns=["mainLanguage"], drop_first=True
            )
            control_vars += [
                col for col in final_data.columns if col.startswith("mainLanguage_")
            ]

        X = sm.add_constant(
            final_data[
                ["post_treatment", "is_treatment", "is_treatment_post_treatment"]
                + control_vars
            ]
        )
        y = final_data[metric_column]

        # 运行 OLS 回归模型
        model = sm.OLS(y, X).fit()

        # 保存结果
        save_data(final_data, "final_data_with_controls.csv")
        with open(
            os.path.join(output_dir, "regression_summary_with_controls.txt"), "w"
        ) as f:
            f.write(model.summary().as_text())
        logging.info("Panel DiD analysis with control variables completed.")
