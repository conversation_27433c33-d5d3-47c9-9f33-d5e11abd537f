import logging
from pymongo import MongoClient

# Configure logging
log_file = '../logs/breaks_and_attritions.log'

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def extract_breaks_and_attritions():
    """
    Extracts breaks and attritions from the existing MongoDB collection and stores
    the processed data in a new collection, indexed by repo_name.
    """
    # Connect to MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]

    # Existing collection with developer break data
    input_collection = db["developer_breaks"]

    # New collection to store extracted breaks and attritions
    output_collection = db["repo_breaks_attritions"]
    output_collection.drop()  # Clear any existing data
    output_collection.create_index([("repo_name", 1)], unique=True)  # Create index on repo_name

    # Iterate through all repos in the input collection
    for repo_data in input_collection.aggregate([{"$group": {"_id": "$repo_name"}}]):
        repo_name = repo_data["_id"]
        logging.info(f"Processing repo: {repo_name}")

        # Fetch all developer data for the current repo
        developers = input_collection.find({"repo_name": repo_name})

        # Initialize lists to store breaks and attritions
        breaks = []
        attritions = []

        # Iterate through each developer's data
        for dev in developers:
            dev_login = dev.get("core_dev_login", "unknown")
            # Process breaks
            for brk in dev.get("Breaks", []):
                breaks.append({
                    "start_time": brk.get("start_date"),
                    "end_time": brk.get("end_date"),
                    "duration": brk.get("pause_duration"),
                    "dev_login": dev_login
                })

            # Process attrition
            attrition = dev.get("Attrition")
            if attrition:
                attritions.append({
                    "attrition_time": attrition.get("attrition_date"),
                    "dev_login": dev_login
                })

        # Sort breaks by start_time and assign unique IDs
        breaks = sorted(breaks, key=lambda x: x["start_time"])
        for idx, brk in enumerate(breaks):
            brk["id"] = idx + 1

        # Sort attritions by attrition_time and assign unique IDs
        attritions = sorted(attritions, key=lambda x: x["attrition_time"])
        for idx, attr in enumerate(attritions):
            attr["id"] = idx + 1

        # Prepare the document for this repo
        repo_document = {
            "repo_name": repo_name,
            "breaks": breaks,
            "attritions": attritions
        }

        # Insert into the new collection
        try:
            output_collection.insert_one(repo_document)
            logging.info(f"Successfully stored data for repo: {repo_name}")
        except Exception as e:
            logging.error(f"Error storing data for repo {repo_name}: {e}")

    logging.info("Processing completed. All repo data has been stored.")

def main():
    extract_breaks_and_attritions()

if __name__ == "__main__":
    main()
