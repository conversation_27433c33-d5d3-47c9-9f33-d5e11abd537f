{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Test to find possible ways to employ diff-in-diff method.\n", "\n", "## Ideas\n", "1. Define Treatment groups\n", "Treatment groups are those repos with attritions.\n", "For simplicity, we now only test on repos with only on attritions\n", "[todo] we may test the effect on muliti-attritions later.\n", "2. Match control groups for each treatment repo\n", "We need to find 5 repos from all repos, for each 1 repo in treatment groups, which should satisfy the below requirements:\n", "   1. No attrition during the timewindow(from pre-treatment to post-treatment)\n", "   2. Satisfy *parallel trends* with the treatment repo pre-treatment.\n", "1. Build a formula for diff-in-diff analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Read data and define treatment groups"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import random"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["repo_info = pd.read_csv('../data/sample_projects_quartiles.csv')\n", "attrition = pd.read_csv('../result/attritions.csv')\n", "productivity = pd.read_csv('../result/productivity_metrics_202501015.csv')\n", "\n", "attrition['attrition_date'] = pd.to_datetime(attrition['attrition_date'])\n", "productivity['time'] = pd.to_datetime(productivity['datetime'])\n", "attrition = attrition.sort_values(['repo_name', 'attrition_date'])\n", "productivity = productivity.sort_values(['repo_name', 'time'])\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Convert 'time_to_merge' to total seconds\n", "productivity['time_to_merge'] = pd.to_timedelta(productivity['time_to_merge']).dt.total_seconds()\n", "\n", "# If you prefer minutes instead of seconds, use the following line:\n", "# productivity['time_to_merge'] = pd.to_timedelta(productivity['time_to_merge']).dt.total_seconds() / 60"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>diffs_per_engineer</th>\n", "      <th>time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>401805</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-09-17</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>5567.0</td>\n", "      <td>1.0</td>\n", "      <td>2018-09-17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401806</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-09-24</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>3973.0</td>\n", "      <td>1.0</td>\n", "      <td>2018-09-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401807</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-10-01</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>254932.0</td>\n", "      <td>1.0</td>\n", "      <td>2018-10-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401808</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-10-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2018-10-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401809</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-10-15</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>336428.5</td>\n", "      <td>2.0</td>\n", "      <td>2018-10-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148923</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-11-08</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>1865596.0</td>\n", "      <td>2.0</td>\n", "      <td>2021-11-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148924</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-11-15</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>408057.0</td>\n", "      <td>2.0</td>\n", "      <td>2021-11-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148925</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-11-29</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1066.0</td>\n", "      <td>1.0</td>\n", "      <td>2021-11-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148926</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-12-13</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2136543.0</td>\n", "      <td>1.0</td>\n", "      <td>2021-12-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148927</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2022-07-04</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2575.0</td>\n", "      <td>1.0</td>\n", "      <td>2022-07-04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>447008 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                              repo_name    datetime  pr_throughput  \\\n", "401805  0xproject/0x-launch-kit-backend  2018-09-17            1.0   \n", "401806  0xproject/0x-launch-kit-backend  2018-09-24            1.0   \n", "401807  0xproject/0x-launch-kit-backend  2018-10-01            1.0   \n", "401808  0xproject/0x-launch-kit-backend  2018-10-08            0.0   \n", "401809  0xproject/0x-launch-kit-backend  2018-10-15            2.0   \n", "...                                 ...         ...            ...   \n", "148923                zupit/ritchie-cli  2021-11-08            2.0   \n", "148924                zupit/ritchie-cli  2021-11-15            2.0   \n", "148925                zupit/ritchie-cli  2021-11-29            1.0   \n", "148926                zupit/ritchie-cli  2021-12-13            1.0   \n", "148927                zupit/ritchie-cli  2022-07-04            1.0   \n", "\n", "        pull_request_success_rate  time_to_merge  diffs_per_engineer  \\\n", "401805                        1.0         5567.0                 1.0   \n", "401806                        1.0         3973.0                 1.0   \n", "401807                        1.0       254932.0                 1.0   \n", "401808                        1.0            NaN                 0.0   \n", "401809                        1.0       336428.5                 2.0   \n", "...                           ...            ...                 ...   \n", "148923                        1.0      1865596.0                 2.0   \n", "148924                        1.0       408057.0                 2.0   \n", "148925                        1.0         1066.0                 1.0   \n", "148926                        1.0      2136543.0                 1.0   \n", "148927                        1.0         2575.0                 1.0   \n", "\n", "             time  \n", "401805 2018-09-17  \n", "401806 2018-09-24  \n", "401807 2018-10-01  \n", "401808 2018-10-08  \n", "401809 2018-10-15  \n", "...           ...  \n", "148923 2021-11-08  \n", "148924 2021-11-15  \n", "148925 2021-11-29  \n", "148926 2021-12-13  \n", "148927 2022-07-04  \n", "\n", "[447008 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["productivity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Plot the distribution situation of attrition times for each repo"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1568</th>\n", "      <td>101loop/drf-user</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1569</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1570</th>\n", "      <td>Mirai-NET-Shelter/Mirai.Net</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1571</th>\n", "      <td>MirrorNetworking/Telepathy</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1572</th>\n", "      <td>OSMCha/osmcha-frontend</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2490</th>\n", "      <td>nmssh/nmssh</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2491</th>\n", "      <td>nmstate/nmstate</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2492</th>\n", "      <td>noahbuscher/macaw</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2493</th>\n", "      <td>novifinancial/opaque-ke</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2494</th>\n", "      <td>nozbe/watermelondb</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>927 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                        repo_name  attrition_count\n", "1568             101loop/drf-user                1\n", "1569            JamBrain/JamBrain                1\n", "1570  Mirai-NET-Shelter/Mirai.Net                1\n", "1571   MirrorNetworking/Telepathy                1\n", "1572       OSMCha/osmcha-frontend                1\n", "...                           ...              ...\n", "2490                  nmssh/nmssh                1\n", "2491              nmstate/nmstate                1\n", "2492            noahbuscher/macaw                1\n", "2493      novifinancial/opaque-ke                1\n", "2494           nozbe/watermelondb                1\n", "\n", "[927 rows x 2 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["attrition_counts = attrition['repo_name'].value_counts().reset_index()\n", "attrition_counts.columns = ['repo_name', 'attrition_count']\n", "\n", "# Plot the distribution of attrition counts\n", "plt.figure(figsize=(10, 6))\n", "sns.histplot(attrition_counts['attrition_count'], bins=range(1, attrition_counts['attrition_count'].max() + 2), kde=False)\n", "plt.title('Distribution of Attrition Counts per Repo')\n", "plt.xlabel('Number of Attrition Events')\n", "plt.ylabel('Frequency')\n", "plt.show()\n", "\n", "# Extract repos with only 1 attrition event\n", "repos_with_one_attrition = attrition_counts[attrition_counts['attrition_count'] == 1]\n", "repos_with_one_attrition"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# set treatment group\n", "\n", "treatment_repos_with_attrition_date = pd.merge(repos_with_one_attrition, attrition, on='repo_name', how='left')\n", "treatment_repos_with_attrition_date\n", "# all repos that satisfy the condition can be considered as control group\n", "control_groups = repo_info"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>diffs_per_engineer</th>\n", "      <th>time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>401805</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-09-17</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>5567.0</td>\n", "      <td>1.0</td>\n", "      <td>2018-09-17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401806</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-09-24</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>3973.0</td>\n", "      <td>1.0</td>\n", "      <td>2018-09-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401807</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-10-01</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>254932.0</td>\n", "      <td>1.0</td>\n", "      <td>2018-10-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401808</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-10-08</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2018-10-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>401809</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>2018-10-15</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>336428.5</td>\n", "      <td>2.0</td>\n", "      <td>2018-10-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148923</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-11-08</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>1865596.0</td>\n", "      <td>2.0</td>\n", "      <td>2021-11-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148924</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-11-15</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>408057.0</td>\n", "      <td>2.0</td>\n", "      <td>2021-11-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148925</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-11-29</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1066.0</td>\n", "      <td>1.0</td>\n", "      <td>2021-11-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148926</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2021-12-13</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2136543.0</td>\n", "      <td>1.0</td>\n", "      <td>2021-12-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148927</th>\n", "      <td>zupit/ritchie-cli</td>\n", "      <td>2022-07-04</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2575.0</td>\n", "      <td>1.0</td>\n", "      <td>2022-07-04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>447008 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                              repo_name    datetime  pr_throughput  \\\n", "401805  0xproject/0x-launch-kit-backend  2018-09-17            1.0   \n", "401806  0xproject/0x-launch-kit-backend  2018-09-24            1.0   \n", "401807  0xproject/0x-launch-kit-backend  2018-10-01            1.0   \n", "401808  0xproject/0x-launch-kit-backend  2018-10-08            0.0   \n", "401809  0xproject/0x-launch-kit-backend  2018-10-15            2.0   \n", "...                                 ...         ...            ...   \n", "148923                zupit/ritchie-cli  2021-11-08            2.0   \n", "148924                zupit/ritchie-cli  2021-11-15            2.0   \n", "148925                zupit/ritchie-cli  2021-11-29            1.0   \n", "148926                zupit/ritchie-cli  2021-12-13            1.0   \n", "148927                zupit/ritchie-cli  2022-07-04            1.0   \n", "\n", "        pull_request_success_rate  time_to_merge  diffs_per_engineer  \\\n", "401805                        1.0         5567.0                 1.0   \n", "401806                        1.0         3973.0                 1.0   \n", "401807                        1.0       254932.0                 1.0   \n", "401808                        1.0            NaN                 0.0   \n", "401809                        1.0       336428.5                 2.0   \n", "...                           ...            ...                 ...   \n", "148923                        1.0      1865596.0                 2.0   \n", "148924                        1.0       408057.0                 2.0   \n", "148925                        1.0         1066.0                 1.0   \n", "148926                        1.0      2136543.0                 1.0   \n", "148927                        1.0         2575.0                 1.0   \n", "\n", "             time  \n", "401805 2018-09-17  \n", "401806 2018-09-24  \n", "401807 2018-10-01  \n", "401808 2018-10-08  \n", "401809 2018-10-15  \n", "...           ...  \n", "148923 2021-11-08  \n", "148924 2021-11-15  \n", "148925 2021-11-29  \n", "148926 2021-12-13  \n", "148927 2022-07-04  \n", "\n", "[447008 rows x 7 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["productivity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Match control groups for each treatment repo\n", "find 5 repos from all repos, for each 1 repo in treatment groups, which should satisfy the below requirements:\n", "   1. No attrition during the timewindow(from pre-treatment to post-treatment)\n", "   2. Satisfy *parallel trends* with the treatment repo pre-treatment."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# settings to not show warnings in the notebook\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "# Function to calculate the slope of trends for a productivity metric\n", "def calculate_trend_slope(data, time_column, metric_column):\n", "    \"\"\"\n", "    Fit a linear model to calculate the slope of the trend for the metric over time.\n", "    \"\"\"\n", "    if data.empty:\n", "        print(f\"No data available for {metric_column} for trend analysis.\")\n", "        return None\n", "    # if data has 0 values, add a small random noise to avoid division by zero, don't print warning\n", "    data[metric_column] = data[metric_column].apply(lambda x: x + random.uniform(0, 0.0001) if x == 0 else x)\n", "    X = sm.add_constant(data[time_column].values.astype(float))  # Add constant for intercept\n", "    y = data[metric_column].values\n", "    model = sm.OLS(y, X).fit()\n", "    try :\n", "        model.params[1]\n", "    except:\n", "        return None\n", "    # print(model.params[1])\n", "    return model.params[1]  # Return the slope of the time variable\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["not_suitable_repos = []\n", "alread_control_repos = []\n", "# Function to test for parallel trends\n", "def test_parallel_trends(productivity, treatment_repo, control_repos, metric_column, timewindow_weeks=12):\n", "    \"\"\"\n", "    Test the parallel trend assumption by selecting 5 control repositories satisfying the parallel trend assumption.\n", "    \n", "    Parameters:\n", "        productivity (DataFrame): The productivity dataset with time and metrics.\n", "        treatment_repo (str): Name of the treatment repository.\n", "        control_repos (list of str): List of control repositories.\n", "        metric_column (str): The column representing the productivity metric.\n", "        timewindow_weeks (int): Number of weeks before and after the attrition date to check for no attrition.\n", "\n", "    Returns:\n", "        dict: Contains treatment trend and selected control repos satisfying parallel trend assumption.\n", "    \"\"\"\n", "    results = {\n", "        \"treatment_repo\": treatment_repo,\n", "        \"treatment_trend\": None,\n", "        \"selected_controls\": []\n", "    }\n", "\n", "    # Filter the data for the treatment repository pre-treatment\n", "    treatment_attrition_date = attrition.loc[attrition[\"repo_name\"] == treatment_repo, \"attrition_date\"].values[0]\n", "    treatment_data = productivity[(productivity[\"repo_name\"] == treatment_repo) & \n", "                                  (productivity[\"time\"] < treatment_attrition_date)]\n", "\n", "    # Calculate the trend slope for the treatment repository\n", "    treatment_trend = calculate_trend_slope(treatment_data, \"time\", metric_column)\n", "    if treatment_trend is None:\n", "        not_suitable_repos.append(treatment_repo)\n", "        return results\n", "    results[\"treatment_trend\"] = treatment_trend\n", "\n", "    # Shuffle the control repositories randomly\n", "    control_repos = control_repos.copy()\n", "    control_repos = [repo for repo in control_repos if repo not in alread_control_repos]\n", "    random.shuffle(control_repos)\n", "    selected_controls = []\n", "\n", "    for control_repo in control_repos:\n", "        # Ensure no attrition in the specified time window\n", "        if not attrition[(attrition[\"repo_name\"] == control_repo) & \n", "                         (attrition[\"attrition_date\"] >= (treatment_attrition_date - pd.Timedelta(weeks=timewindow_weeks))) & \n", "                         (attrition[\"attrition_date\"] <= (treatment_attrition_date + pd.Timedelta(weeks=timewindow_weeks)))].empty:\n", "            continue\n", "        # Ensure the productivity of control group span is long enough for trend analysis\n", "        if productivity[(productivity[\"repo_name\"] == control_repo) & \n", "                        (productivity[\"time\"] < treatment_attrition_date - pd.Timedelta(weeks=timewindow_weeks))].empty:\n", "            continue\n", "        # Filter the data for the control repository pre-treatment\n", "        control_data = productivity[(productivity[\"repo_name\"] == control_repo) & \n", "                                     (productivity[\"time\"] < treatment_attrition_date)]\n", "\n", "        # Calculate the trend slope for the control repository\n", "        control_trend = calculate_trend_slope(control_data, \"time\", metric_column)\n", "        if not control_trend:\n", "            continue\n", "        # Check if the control trend is similar to the treatment trend\n", "        if abs(control_trend - treatment_trend) < 0.05:  # Example threshold for similarity\n", "            selected_controls.append(control_repo)\n", "            alread_control_repos.append(control_repo)\n", "        # Stop once 5 controls have been selected\n", "        if len(selected_controls) >= 5:\n", "            break\n", "\n", "    results[\"selected_controls\"] = selected_controls\n", "\n", "    return results\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Function to perform panel Difference-in-Differences regression\n", "def perform_panel_diff_in_diff(treatment_repos_with_attrition_date, control_groups, productivity, attrition, metric_column, timewindow_weeks=12):\n", "    \"\"\"\n", "    Perform aggregated panel Difference-in-Differences (DiD) regression using all treatment repositories\n", "    and their corresponding 5 control repositories.\n", "\n", "    Parameters:\n", "        treatment_repos_with_attrition_date (DataFrame): Repositories with attrition date and count.\n", "        control_groups (DataFrame): All repositories that can be considered as control groups.\n", "        productivity (DataFrame): The productivity dataset with time and metrics.\n", "        attrition (DataFrame): The attrition dataset with repo_name and attrition_date.\n", "        metric_column (str): The column representing the productivity metric.\n", "        timewindow_weeks (int): Time window in weeks to ensure no attrition in control groups.\n", "\n", "    Returns:\n", "        Regression Results: Panel regression output summarizing the aggregated treatment effect.\n", "    \"\"\"\n", "    combined_data = []\n", "\n", "    # Loop through all treatment repositories\n", "    for treatment_repo in treatment_repos_with_attrition_date[\"repo_name\"]:\n", "        # Get the attrition date for the treatment repository\n", "        treatment_attrition_date = attrition.loc[attrition[\"repo_name\"] == treatment_repo, \"attrition_date\"].values[0]\n", "        control_repos = control_groups[control_groups['repo_name'] != treatment_repo]['repo_name'].tolist()\n", "        try:\n", "            control_repos.remove(treatment_repo)\n", "        except:\n", "            pass            \n", "        # Select 5 control repos satisfying parallel trends\n", "        parallel_trend_results = test_parallel_trends(\n", "            productivity, treatment_repo, control_repos, metric_column, timewindow_weeks\n", "        )\n", "        if parallel_trend_results[\"treatment_trend\"] is None:\n", "            print(f\"No trend available for treatment repo {treatment_repo}, should be excluded.\")\n", "            continue\n", "        selected_controls = parallel_trend_results[\"selected_controls\"]\n", "        if not selected_controls:\n", "            continue\n", "        if len(selected_controls) < 5:\n", "            print(f\"Insufficient controls for {treatment_repo}, skipping.\")\n", "            continue\n", "\n", "        # Combine treatment and control data\n", "        repos = [treatment_repo] + selected_controls\n", "        subset = productivity[productivity[\"repo_name\"].isin(repos)].copy()\n", "\n", "        # Create the standardized time variable in weeks\n", "        subset[\"standardized_time_weeks\"] = (subset[\"time\"] - treatment_attrition_date).dt.days // 7  # Convert to weeks\n", "        subset = subset[subset[\"standardized_time_weeks\"].between(-timewindow_weeks, timewindow_weeks)]\n", "\n", "        # Add DiD variables\n", "        subset[\"post_treatment\"] = subset[\"standardized_time_weeks\"] >= 0\n", "        subset[\"is_treatment\"] = subset[\"repo_name\"] == treatment_repo\n", "        subset[\"is_treatment & post_treatment\"] = subset[\"post_treatment\"] * subset[\"is_treatment\"]\n", "        combined_data.append(subset)\n", "        # save current combined to csv\n", "        \n", "\n", "    print(\"Finished selecting control repositories for all treatment repositories.\")\n", "    print(f\"Number of repositories with valid controls: {len(combined_data)}\")\n", "\n", "    # Concatenate all subsets for panel regression\n", "    final_data = pd.concat(combined_data)\n", "    print(f\"Final data shape: {final_data.shape}\")\n", "\n", "    # Run the panel DiD regression\n", "    independent_vars = [\"post_treatment\", \"is_treatment\", \"is_treatment & post_treatment\"]\n", "    return final_data, independent_vars, combined_data\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No trend available for treatment repo 101loop/drf-user, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo TheMulhima/Lumafly, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo yubico/python-yubico, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo nicolas-chaulet/torch-points3d, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo nikitacartes/easyauth, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo winauth/winauth, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo nativefier/nativefier, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo nvidiagameworks/dxvk-remix, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo metajack/libstrophe, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo opendbdiff/opendbdiff, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo meganz/megasync, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo opentracing/opentracing-csharp, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo openxc/uds-c, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo libinjection/libinjection, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo liftoff-sr/cipster, should be excluded.\n", "No trend available for treatment repo linkedin/spyglass, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo pangeo-data/xesmf, should be excluded.\n", "No trend available for treatment repo paragonie/constant_time_encoding, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo lxerxa/actionview, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo madsmtm/objc2, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo phpids/phpids, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo kbknapp/cargo-graph, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo julian<PERSON><PERSON>/muhammarajs, should be excluded.\n", "Insufficient controls for kakserpom/phpdaemon, skipping.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo jfilter/react-native-onboarding-swiper, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo jamestkhan/mundus, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo intro-skipper/intro-skipper, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo immobiliare/backstage-plugin-gitlab, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo inception-project/inception, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo rabbitmq/rabbitmq-java-client, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo ikvm-revived/ikvm, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo qb64-phoenix-edition/qb64pe, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo quamotion/madb, should be excluded.\n", "No trend available for treatment repo hibri/httpmock, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo hjiang/jsonxx, should be excluded.\n", "No trend available for treatment repo ibm-cloud/redli, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo gossi/php-code-generator, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo rpotter12/whatsapp-play, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo robertabcd/pttchrome, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo frameworkcomputer/inputmodule-rs, should be excluded.\n", "No trend available for treatment repo giscience/openpoiservice, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo google/corgi, should be excluded.\n", "No trend available for treatment repo rqlite/gorqlite, should be excluded.\n", "No trend available for treatment repo safe-global/safe-modules, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo samsung/escargot, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo rustls/tokio-rustls, should be excluded.\n", "No trend available for treatment repo ethereum/fe, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo fashionfreedom/seamly2d, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo espresense/espresense, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo etcd-cpp-apiv3/etcd-cpp-apiv3, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo signalapp/libsignal-service-java, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo simpod/grafanajsondatasource, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo expatfile/next-runtime-env, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo dspinellis/umlgraph, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo dynamorio/drmemory, should be excluded.\n", "No trend available for treatment repo embeddedt/embeddium, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo sphereserver/source, should be excluded.\n", "No trend available for treatment repo dyne/dnscrypt-proxy, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo dotnetinstaller/dotnetinstaller, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo davidgiven/ack, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo deepmedia/Transcoder, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo stevema<PERSON>ki/slam_toolbox, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo databendlabs/openraft, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo dnglab/dnglab, should be excluded.\n", "No data available for time_to_merge for trend analysis.\n", "No trend available for treatment repo crccheck/raphael-svg-import-classic, should be excluded.\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m panel_did_model \u001b[38;5;241m=\u001b[39m perform_panel_diff_in_diff(\n\u001b[1;32m      2\u001b[0m     treatment_repos_with_attrition_date, control_groups, productivity, attrition, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtime_to_merge\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      3\u001b[0m     timewindow_weeks\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m12\u001b[39m\n\u001b[1;32m      4\u001b[0m )\n", "Cell \u001b[0;32mIn[10], line 30\u001b[0m, in \u001b[0;36mperform_panel_diff_in_diff\u001b[0;34m(treatment_repos_with_attrition_date, control_groups, productivity, attrition, metric_column, timewindow_weeks)\u001b[0m\n\u001b[1;32m     28\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m            \n\u001b[1;32m     29\u001b[0m \u001b[38;5;66;03m# Select 5 control repos satisfying parallel trends\u001b[39;00m\n\u001b[0;32m---> 30\u001b[0m parallel_trend_results \u001b[38;5;241m=\u001b[39m test_parallel_trends(\n\u001b[1;32m     31\u001b[0m     productivity, treatment_repo, control_repos, metric_column, timewindow_weeks\n\u001b[1;32m     32\u001b[0m )\n\u001b[1;32m     33\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m parallel_trend_results[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtreatment_trend\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo trend available for treatment repo \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtreatment_repo\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, should be excluded.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[9], line 53\u001b[0m, in \u001b[0;36mtest_parallel_trends\u001b[0;34m(productivity, treatment_repo, control_repos, metric_column, timewindow_weeks)\u001b[0m\n\u001b[1;32m     51\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[1;32m     52\u001b[0m \u001b[38;5;66;03m# Filter the data for the control repository pre-treatment\u001b[39;00m\n\u001b[0;32m---> 53\u001b[0m control_data \u001b[38;5;241m=\u001b[39m productivity[(productivity[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrepo_name\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m control_repo) \u001b[38;5;241m&\u001b[39m \n\u001b[1;32m     54\u001b[0m                              (productivity[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtime\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m<\u001b[39m treatment_attrition_date)]\n\u001b[1;32m     56\u001b[0m \u001b[38;5;66;03m# Calculate the trend slope for the control repository\u001b[39;00m\n\u001b[1;32m     57\u001b[0m control_trend \u001b[38;5;241m=\u001b[39m calculate_trend_slope(control_data, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtime\u001b[39m\u001b[38;5;124m\"\u001b[39m, metric_column)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pandas/core/ops/common.py:76\u001b[0m, in \u001b[0;36m_unpack_zerodim_and_defer.<locals>.new_method\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m     72\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mNotImplemented\u001b[39m\n\u001b[1;32m     74\u001b[0m other \u001b[38;5;241m=\u001b[39m item_from_zerodim(other)\n\u001b[0;32m---> 76\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m method(\u001b[38;5;28mself\u001b[39m, other)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pandas/core/arraylike.py:40\u001b[0m, in \u001b[0;36mOpsMixin.__eq__\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m     38\u001b[0m \u001b[38;5;129m@unpack_zerodim_and_defer\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__eq__\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     39\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__eq__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[0;32m---> 40\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cmp_method(other, operator\u001b[38;5;241m.\u001b[39meq)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pandas/core/series.py:6119\u001b[0m, in \u001b[0;36mSeries._cmp_method\u001b[0;34m(self, other, op)\u001b[0m\n\u001b[1;32m   6116\u001b[0m lvalues \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_values\n\u001b[1;32m   6117\u001b[0m rvalues \u001b[38;5;241m=\u001b[39m extract_array(other, extract_numpy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, extract_range\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m-> 6119\u001b[0m res_values \u001b[38;5;241m=\u001b[39m ops\u001b[38;5;241m.\u001b[39mcomparison_op(lvalues, rvalues, op)\n\u001b[1;32m   6121\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_construct_result(res_values, name\u001b[38;5;241m=\u001b[39mres_name)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pandas/core/ops/array_ops.py:344\u001b[0m, in \u001b[0;36mcomparison_op\u001b[0;34m(left, right, op)\u001b[0m\n\u001b[1;32m    341\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m invalid_comparison(lvalues, rvalues, op)\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m lvalues\u001b[38;5;241m.\u001b[39mdtype \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mobject\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(rvalues, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m--> 344\u001b[0m     res_values \u001b[38;5;241m=\u001b[39m comp_method_OBJECT_ARRAY(op, lvalues, rvalues)\n\u001b[1;32m    346\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    347\u001b[0m     res_values \u001b[38;5;241m=\u001b[39m _na_arithmetic_op(lvalues, rvalues, op, is_cmp\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pandas/core/ops/array_ops.py:129\u001b[0m, in \u001b[0;36mcomp_method_OBJECT_ARRAY\u001b[0;34m(op, x, y)\u001b[0m\n\u001b[1;32m    127\u001b[0m     result \u001b[38;5;241m=\u001b[39m libops\u001b[38;5;241m.\u001b[39mvec_compare(x\u001b[38;5;241m.\u001b[39mravel(), y\u001b[38;5;241m.\u001b[39mravel(), op)\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 129\u001b[0m     result \u001b[38;5;241m=\u001b[39m libops\u001b[38;5;241m.\u001b[39mscalar_compare(x\u001b[38;5;241m.\u001b[39mravel(), y, op)\n\u001b[1;32m    130\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result\u001b[38;5;241m.\u001b[39mreshape(x\u001b[38;5;241m.\u001b[39mshape)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["panel_did_model = perform_panel_diff_in_diff(\n", "    treatment_repos_with_attrition_date, control_groups, productivity, attrition, \"time_to_merge\",\n", "    timewindow_weeks=12\n", ")"]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [], "source": ["final_data, independent_vars, combined_data = panel_did_model"]}, {"cell_type": "code", "execution_count": 213, "metadata": {}, "outputs": [], "source": ["final_data[\"post_treatment\"] = final_data[\"post_treatment\"].astype(int)\n", "final_data[\"is_treatment\"] = final_data[\"is_treatment\"].astype(int)\n", "final_data[\"is_treatment & post_treatment\"] = final_data[\"interaction\"].astype(int)"]}, {"cell_type": "code", "execution_count": 214, "metadata": {}, "outputs": [], "source": ["independent_vars = [\"post_treatment\", \"is_treatment\", \"is_treatment & post_treatment\"]"]}, {"cell_type": "code", "execution_count": 196, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>diffs_per_engineer</th>\n", "      <th>time</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>post_treatment</th>\n", "      <th>is_treatment</th>\n", "      <th>interaction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>154078</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-03-27</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:27</td>\n", "      <td>1.0</td>\n", "      <td>2023-03-27</td>\n", "      <td>-5</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154079</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-04-03</td>\n", "      <td>3.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:11</td>\n", "      <td>3.0</td>\n", "      <td>2023-04-03</td>\n", "      <td>-4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154080</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-04-10</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:11</td>\n", "      <td>1.0</td>\n", "      <td>2023-04-10</td>\n", "      <td>-3</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154081</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-04-24</td>\n", "      <td>9.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:10.111111111</td>\n", "      <td>9.0</td>\n", "      <td>2023-04-24</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154082</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-05-01</td>\n", "      <td>10.0</td>\n", "      <td>0.909091</td>\n", "      <td>0 days 00:05:17.600000</td>\n", "      <td>10.0</td>\n", "      <td>2023-05-01</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72970</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-02-27</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>29 days 05:45:02</td>\n", "      <td>1.0</td>\n", "      <td>2023-02-27</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72971</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-03-06</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>184 days 20:04:32</td>\n", "      <td>1.0</td>\n", "      <td>2023-03-06</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72972</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-03-27</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2023-03-27</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72973</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-04-03</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>9 days 21:27:16</td>\n", "      <td>1.0</td>\n", "      <td>2023-04-03</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72974</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-04-24</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2023-04-24</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>21327 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                 repo_name    datetime  pr_throughput  \\\n", "154078   JamBrain/JamBrain  2023-03-27            1.0   \n", "154079   JamBrain/JamBrain  2023-04-03            3.0   \n", "154080   JamBrain/JamBrain  2023-04-10            1.0   \n", "154081   JamBrain/JamBrain  2023-04-24            9.0   \n", "154082   JamBrain/JamBrain  2023-05-01           10.0   \n", "...                    ...         ...            ...   \n", "72970   thanos-io/objstore  2023-02-27            1.0   \n", "72971   thanos-io/objstore  2023-03-06            1.0   \n", "72972   thanos-io/objstore  2023-03-27            0.0   \n", "72973   thanos-io/objstore  2023-04-03            1.0   \n", "72974   thanos-io/objstore  2023-04-24            0.0   \n", "\n", "        pull_request_success_rate              time_to_merge  \\\n", "154078                   1.000000            0 days 00:00:27   \n", "154079                   1.000000            0 days 00:00:11   \n", "154080                   1.000000            0 days 00:00:11   \n", "154081                   1.000000  0 days 00:00:10.111111111   \n", "154082                   0.909091     0 days 00:05:17.600000   \n", "...                           ...                        ...   \n", "72970                    1.000000           29 days 05:45:02   \n", "72971                    1.000000          184 days 20:04:32   \n", "72972                    1.000000                        NaN   \n", "72973                    1.000000            9 days 21:27:16   \n", "72974                    1.000000                        NaN   \n", "\n", "        diffs_per_engineer       time  standardized_time_weeks  \\\n", "154078                 1.0 2023-03-27                       -5   \n", "154079                 3.0 2023-04-03                       -4   \n", "154080                 1.0 2023-04-10                       -3   \n", "154081                 9.0 2023-04-24                       -1   \n", "154082                10.0 2023-05-01                        0   \n", "...                    ...        ...                      ...   \n", "72970                  1.0 2023-02-27                        1   \n", "72971                  1.0 2023-03-06                        2   \n", "72972                  0.0 2023-03-27                        5   \n", "72973                  1.0 2023-04-03                        6   \n", "72974                  0.0 2023-04-24                        9   \n", "\n", "        post_treatment  is_treatment  interaction  \n", "154078               0             1            0  \n", "154079               0             1            0  \n", "154080               0             1            0  \n", "154081               0             1            0  \n", "154082               1             1            1  \n", "...                ...           ...          ...  \n", "72970                1             0            0  \n", "72971                1             0            0  \n", "72972                1             0            0  \n", "72973                1             0            0  \n", "72974                1             0            0  \n", "\n", "[21327 rows x 11 columns]"]}, "execution_count": 196, "metadata": {}, "output_type": "execute_result"}], "source": ["final_data"]}, {"cell_type": "code", "execution_count": 200, "metadata": {}, "outputs": [], "source": ["final_data = final_data.dropna(subset=independent_vars + ['pr_throughput'])"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [], "source": ["# Run the panel DiD regression\n", "panel_did_model = sm.OLS(final_data[\"pr_throughput\"], sm.add_constant(final_data[independent_vars])).fit()"]}, {"cell_type": "code", "execution_count": 247, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:          pr_throughput   R-squared:                       0.001\n", "Model:                            OLS   Adj. R-squared:                  0.001\n", "Method:                 Least Squares   F-statistic:                     7.412\n", "Date:                Wed, 18 Dec 2024   Prob (F-statistic):           5.85e-05\n", "Time:                        23:18:10   Log-Likelihood:                -53982.\n", "No. Observations:               21327   AIC:                         1.080e+05\n", "Df Residuals:                   21323   BIC:                         1.080e+05\n", "Df Model:                           3                                         \n", "Covariance Type:            nonrobust                                         \n", "=================================================================================================\n", "                                    coef    std err          t      P>|t|      [0.025      0.975]\n", "-------------------------------------------------------------------------------------------------\n", "const                             2.2283      0.033     66.611      0.000       2.163       2.294\n", "post_treatment                   -0.0990      0.047     -2.118      0.034      -0.191      -0.007\n", "is_treatment                      0.2069      0.070      2.946      0.003       0.069       0.345\n", "is_treatment & post_treatment    -0.2409      0.103     -2.329      0.020      -0.444      -0.038\n", "==============================================================================\n", "Omnibus:                    20761.902   <PERSON><PERSON><PERSON>-<PERSON>:                   1.135\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          1661238.005\n", "Skew:                           4.606   Prob(JB):                         0.00\n", "Kurtosis:                      45.244   Cond. No.                         6.71\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n"]}], "source": ["print(panel_did_model.summary())"]}, {"cell_type": "code", "execution_count": 245, "metadata": {}, "outputs": [{"data": {"text/plain": ["529"]}, "execution_count": 245, "metadata": {}, "output_type": "execute_result"}], "source": ["# show number of repos with treatment effect\n", "treatment_effect_repos = final_data[final_data[\"is_treatment\"] == 1][\"repo_name\"].nunique()\n", "treatment_effect_repos\n"]}, {"cell_type": "code", "execution_count": 246, "metadata": {}, "outputs": [{"data": {"text/plain": ["2165"]}, "execution_count": 246, "metadata": {}, "output_type": "execute_result"}], "source": ["control_effect_repos = final_data[final_data[\"is_treatment\"] == 0][\"repo_name\"].nunique()\n", "control_effect_repos"]}, {"cell_type": "code", "execution_count": 210, "metadata": {}, "outputs": [{"data": {"text/plain": ["271"]}, "execution_count": 210, "metadata": {}, "output_type": "execute_result"}], "source": ["len(not_suitable_repos)"]}, {"cell_type": "code", "execution_count": 197, "metadata": {}, "outputs": [], "source": ["final_data.to_csv('../result/did_test_data_202501015.csv')"]}, {"cell_type": "code", "execution_count": 218, "metadata": {}, "outputs": [], "source": ["# save results of panel DiD regression in a text file\n", "with open('../result/did_test_data_202501015.txt', 'w') as f:\n", "    f.write(panel_did_model.summary().as_text())"]}, {"cell_type": "code", "execution_count": 202, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot the line of treatment groupd and control group in mean of unit\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Group data into treatment and control groups\n", "treatment_data = final_data[final_data[\"is_treatment\"] == 1]\n", "control_data = final_data[final_data[\"is_treatment\"] == 0]\n", "\n", "# Function to compute mean and standard deviation by time\n", "def compute_stats(group_data):\n", "    grouped = group_data.groupby(\"standardized_time_weeks\")\n", "    mean = grouped[\"pr_throughput\"].mean()\n", "    std = grouped[\"pr_throughput\"].std()\n", "    count = grouped[\"pr_throughput\"].count()\n", "    sem = std / np.sqrt(count)  # Standard error of the mean\n", "    return mean, mean - sem, mean + sem\n", "\n", "# Calculate stats for treatment and control groups\n", "treatment_mean, treatment_lower, treatment_upper = compute_stats(treatment_data)\n", "control_mean, control_lower, control_upper = compute_stats(control_data)\n", "\n", "# Create the plots\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Plot for treatment group\n", "# plt.subplot(2, 1, 1)\n", "plt.plot(treatment_mean.index, treatment_mean, label=\"Mean pr_throughput\", color=\"blue\")\n", "plt.fill_between(treatment_mean.index, treatment_lower, treatment_upper, color=\"blue\", alpha=0.2, label=\"95% CI\")\n", "plt.axvline(0, color=\"red\", linestyle=\"--\", label=\"Treatment Start (time=0)\")\n", "plt.title(\"Treatment Group: pr_throughput Over Time\")\n", "plt.xlabel(\"Standardized Time (Weeks)\")\n", "plt.ylabel(\"pr_throughput\")\n", "plt.legend()\n", "\n", "# Plot for control group\n", "# plt.subplot(2, 1, 2)\n", "plt.plot(control_mean.index, control_mean, label=\"Mean pr_throughput\", color=\"green\")\n", "plt.fill_between(control_mean.index, control_lower, control_upper, color=\"green\", alpha=0.2, label=\"95% CI\")\n", "plt.axvline(0, color=\"red\", linestyle=\"--\", label=\"Treatment Start (time=0)\")\n", "plt.title(\"Control Group: pr_throughput Over Time\")\n", "plt.xlabel(\"Standardized Time (Weeks)\")\n", "plt.ylabel(\"pr_throughput\")\n", "plt.legend()\n", "\n", "# Adjust layout and display the plots\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 243, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>datetime</th>\n", "      <th>pr_throughput</th>\n", "      <th>pull_request_success_rate</th>\n", "      <th>time_to_merge</th>\n", "      <th>diffs_per_engineer</th>\n", "      <th>time</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>post_treatment</th>\n", "      <th>is_treatment</th>\n", "      <th>...</th>\n", "      <th>lag_4</th>\n", "      <th>lag_5</th>\n", "      <th>lag_6</th>\n", "      <th>lag_7</th>\n", "      <th>lag_8</th>\n", "      <th>lag_9</th>\n", "      <th>lag_10</th>\n", "      <th>lag_11</th>\n", "      <th>lag_12</th>\n", "      <th>is_treatment &amp; post_treatment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>154078</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-03-27</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:27</td>\n", "      <td>1.0</td>\n", "      <td>2023-03-27</td>\n", "      <td>-5</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154079</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-04-03</td>\n", "      <td>3.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:11</td>\n", "      <td>3.0</td>\n", "      <td>2023-04-03</td>\n", "      <td>-4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154080</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-04-10</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:11</td>\n", "      <td>1.0</td>\n", "      <td>2023-04-10</td>\n", "      <td>-3</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154081</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-04-24</td>\n", "      <td>9.0</td>\n", "      <td>1.000000</td>\n", "      <td>0 days 00:00:10.111111111</td>\n", "      <td>9.0</td>\n", "      <td>2023-04-24</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154082</th>\n", "      <td>JamBrain/JamBrain</td>\n", "      <td>2023-05-01</td>\n", "      <td>10.0</td>\n", "      <td>0.909091</td>\n", "      <td>0 days 00:05:17.600000</td>\n", "      <td>10.0</td>\n", "      <td>2023-05-01</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72970</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-02-27</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>29 days 05:45:02</td>\n", "      <td>1.0</td>\n", "      <td>2023-02-27</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72971</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-03-06</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>184 days 20:04:32</td>\n", "      <td>1.0</td>\n", "      <td>2023-03-06</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72972</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-03-27</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2023-03-27</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72973</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-04-03</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>9 days 21:27:16</td>\n", "      <td>1.0</td>\n", "      <td>2023-04-03</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72974</th>\n", "      <td>thanos-io/objstore</td>\n", "      <td>2023-04-24</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>2023-04-24</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>21327 rows × 37 columns</p>\n", "</div>"], "text/plain": ["                 repo_name    datetime  pr_throughput  \\\n", "154078   JamBrain/JamBrain  2023-03-27            1.0   \n", "154079   JamBrain/JamBrain  2023-04-03            3.0   \n", "154080   JamBrain/JamBrain  2023-04-10            1.0   \n", "154081   JamBrain/JamBrain  2023-04-24            9.0   \n", "154082   JamBrain/JamBrain  2023-05-01           10.0   \n", "...                    ...         ...            ...   \n", "72970   thanos-io/objstore  2023-02-27            1.0   \n", "72971   thanos-io/objstore  2023-03-06            1.0   \n", "72972   thanos-io/objstore  2023-03-27            0.0   \n", "72973   thanos-io/objstore  2023-04-03            1.0   \n", "72974   thanos-io/objstore  2023-04-24            0.0   \n", "\n", "        pull_request_success_rate              time_to_merge  \\\n", "154078                   1.000000            0 days 00:00:27   \n", "154079                   1.000000            0 days 00:00:11   \n", "154080                   1.000000            0 days 00:00:11   \n", "154081                   1.000000  0 days 00:00:10.111111111   \n", "154082                   0.909091     0 days 00:05:17.600000   \n", "...                           ...                        ...   \n", "72970                    1.000000           29 days 05:45:02   \n", "72971                    1.000000          184 days 20:04:32   \n", "72972                    1.000000                        NaN   \n", "72973                    1.000000            9 days 21:27:16   \n", "72974                    1.000000                        NaN   \n", "\n", "        diffs_per_engineer       time  standardized_time_weeks  \\\n", "154078                 1.0 2023-03-27                       -5   \n", "154079                 3.0 2023-04-03                       -4   \n", "154080                 1.0 2023-04-10                       -3   \n", "154081                 9.0 2023-04-24                       -1   \n", "154082                10.0 2023-05-01                        0   \n", "...                    ...        ...                      ...   \n", "72970                  1.0 2023-02-27                        1   \n", "72971                  1.0 2023-03-06                        2   \n", "72972                  0.0 2023-03-27                        5   \n", "72973                  1.0 2023-04-03                        6   \n", "72974                  0.0 2023-04-24                        9   \n", "\n", "        post_treatment  is_treatment  ...  lag_4  lag_5  lag_6  lag_7  lag_8  \\\n", "154078               0             1  ...      0      0      0      0      0   \n", "154079               0             1  ...      0      0      0      0      0   \n", "154080               0             1  ...      0      0      0      0      0   \n", "154081               0             1  ...      0      0      0      0      0   \n", "154082               1             1  ...      0      0      0      0      0   \n", "...                ...           ...  ...    ...    ...    ...    ...    ...   \n", "72970                1             0  ...      0      0      0      0      0   \n", "72971                1             0  ...      0      0      0      0      0   \n", "72972                1             0  ...      0      1      0      0      0   \n", "72973                1             0  ...      0      0      1      0      0   \n", "72974                1             0  ...      0      0      0      0      0   \n", "\n", "        lag_9  lag_10  lag_11  lag_12  is_treatment & post_treatment  \n", "154078      0       0       0       0                              0  \n", "154079      0       0       0       0                              0  \n", "154080      0       0       0       0                              0  \n", "154081      0       0       0       0                              0  \n", "154082      0       0       0       0                              1  \n", "...       ...     ...     ...     ...                            ...  \n", "72970       0       0       0       0                              0  \n", "72971       0       0       0       0                              0  \n", "72972       0       0       0       0                              0  \n", "72973       0       0       0       0                              0  \n", "72974       1       0       0       0                              0  \n", "\n", "[21327 rows x 37 columns]"]}, "execution_count": 243, "metadata": {}, "output_type": "execute_result"}], "source": ["final_data"]}, {"cell_type": "code", "execution_count": 233, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Lag 0: Coefficient = 0.41082261095932027, P-value = 0.12000117883983162\n", "Lag 1: Coefficient = 0.20777765000123405, P-value = 0.4737376345815266\n", "Lag 2: Coefficient = 0.30870889633446363, P-value = 0.30796833323728967\n", "Lag 3: Coefficient = 0.11965604586156385, P-value = 0.684136778688698\n", "Lag 4: Coefficient = 0.2277116210672885, P-value = 0.45070266727188224\n", "Lag 5: Coefficient = 0.13334301273032353, P-value = 0.6613495067256512\n", "Lag 6: Coefficient = -0.217823852794012, P-value = 0.45179005841078124\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAscAAAHUCAYAAADWXIWGAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACRzUlEQVR4nOzdd1hT1/8H8HeEEIYQFGQpAloXdeEGq4gW3HtWa9Wq/Vlt3V9HHYjWUm1rUVv3HlXbWmcpigsHuHdVqhY3iBMUBEJyfn8IqTEBQwhE8P16njzmnnvuvZ97SOInJ+eeKxFCCBAREREREUqYOgAiIiIiorcFk2MiIiIioixMjomIiIiIsjA5JiIiIiLKwuSYiIiIiCgLk2MiIiIioixMjomIiIiIsjA5JiIiIiLKwuSYiIiIiCgLk2PSsGrVKkgkkhwfBw4cUNd9/PgxevXqBScnJ0gkEnTq1AkAcOPGDbRt2xalS5eGRCLByJEjjR7nggULsGrVKqPvNyMjA0OGDIGrqyvMzMxQu3btHOv2798fzZo10yiTSCSYNm2aUWO6d+8epk2bhrNnzxp1v3kVHh5u9HMrSN988w22bt2ap23u3Lmjfk3b2trCx8cHCxYsKJgAjSg6OhrTpk3D06dPC3VbYyqo93RByO09OW3aNHh6euq1HyEEfvnlFzRv3hylSpWCTCZDhQoVMGzYMNy+fdu4QedTbv8vvP5/RP/+/fVug8J06NAhyGQy3Lx5U13WrFkzVK9e3YRR5d3p06fx4YcfomTJkrC3t0eXLl3w77//5mkfDx8+xIgRI+Dp6QmZTAZnZ2e0bt0ajx8/VtdZvnw5ypYti5SUFGOfwttPEL1i5cqVAoBYuXKliImJ0XokJSWp644cOVJYWFiIdevWiZiYGBEbGyuEEKJTp07CwcFBbNmyRcTExIgbN24YPc73339f+Pv7G32/YWFhAoCYP3++iI6OFufPn8+xbr9+/bRiiImJEbdv3zZqTCdOnFD/TUxp2LBhoih9ZNjY2Ih+/frpXV+pVIratWsLV1dXsXr1arFr1y4xY8YM0bt374IL0ki+++47AUDExcUV6rbGVFDv6YKQ23syODhYeHh4vHEfSqVS9OzZUwAQH330kdi6davYv3+/mDt3rihXrpywt7cXhw8fNn7wBnr9/4I2bdoIKysrnf9HXLt2TZw+fdrUIWtQqVSiTp06YtiwYRrl/v7+4v333zdRVHl3+fJlYWtrK5o0aSL+/PNPsXnzZvH+++8LNzc3kZiYqNc+7t69KypUqCAqV64sli1bJqKiosTmzZvFF198IeLj49X1FAqFqFSpkpg6dWpBnc5by9x0aTm9zapXr4569erlWufixYuoWLEi+vTpo1XeoEEDdU9yUXLx4kVYWVnhiy++MGj7Ro0aGTmivEtNTYW1tbWpwyhyYmNjcfbsWSxcuBCffPIJACAoKMjEUb19Xrx4ASsrK1OHUeTNmjULmzZtwrfffovx48ery5s1a4aePXuiYcOG6Nq1K65cuQJ7e/tCiyunz4/XP9vKlCmDEiVK6PzMs7OzK7D4DBUREYHTp0/jl19+MXUo+TJ16lTIZDLs3LlT3c5169ZFpUqV8P3332PWrFlv3MfQoUORnp6OkydPolSpUuryLl26aNQzNzfH//3f/2HGjBkYP378u/X/iqmzc3q7ZPccnzhxIsc6cXFxAoDWY//+/TrLs3ukkpKSxJgxY4Snp6eQSqXCzc1NjBgxQjx//lxj/0qlUsybN0/UqlVLWFpaCrlcLho2bCi2bdsmhBDCw8ND6xhv6ql58eKFmDBhgsaxhw4dKp48eaKuoyv23HprdfUcAxDBwcHq5ZSUFPU5y2QyUapUKVG3bl3xyy+/5BpvtpzaNPsY/fr1EzY2NuL8+fMiMDBQlCxZUjRq1EgIIUR6erqYMWOGqFKlirCwsBCOjo6if//+Wr0LGzduFIGBgcLFxUVYWlqKqlWrivHjx2v8Xfr165fr3xaAGDZsmFixYoWoXLmysLS0FHXr1hUxMTFCpVKJ2bNnC09PT2FjYyMCAgLE1atXtc41MjJSNG/eXNja2gorKyvh5+cn9uzZo1EnODhYABAXL14UvXr1EnZ2dsLJyUkMGDBAPH36VOPv8PrjTb2SsbGxAoAYOXKkXn+bnGS3xaJFi0SlSpWEhYWFqFatmtiwYYNW3QsXLogOHToIe3t7IZPJRK1atcSqVas06iiVSjFjxgx1u8rlclGjRg0RFham0Sa63o9v8qZtPTw8RNu2bcXmzZtF7dq1hUwmE+PHjxdCCBEfHy8+++wzUbZsWSGVSoWnp6eYNm2aUCgUGseYNm2aaNCggShVqpSwtbUVPj4+YtmyZUKlUqnr5Paezn4PrF+/XowbN064uLgIGxsb0a5dO5GQkCCSk5PF4MGDhYODg3BwcBD9+/cXz54904hBpVKJn3/+Wf2ZYm9vL7p27SquX7+uUS+7F/H48ePigw8+EFZWVsLLy0uEhoYKpVKpEU9O70l9eo7T09NFqVKlRLVq1TTa4VW//PKLACC+//57IYQQI0aMENbW1hq/3mXr0aOHcHJyEhkZGeqyjRs3ikaNGglra2thY2MjgoKCtHpzc/v8eJPsbXNa93obFNZnRE7at28v6tevr1WuT8/xiRMnRM+ePYWHh4ewtLQUHh4eolevXjp/FT106JBo1KiRkMlkws3NTUyePFksXbpU69eZvXv3Cn9/f1G6dGlhaWkp3N3dRZcuXURKSkqOcSgUCmFlZSX+7//+T2tdUFCQqFSpUq7nIcTL/78lEomYNm3aG+sK8fJ9LpFIxPLly/WqX1wwOSYN2cnx0aNHhUKh0HhkZmYKIYRIS0sTMTExwsfHR1SoUEHj57SYmBjh4uIiGjdurC5PS0sTKSkponbt2sLR0VHMmTNH7NmzR8ydO1fI5XLRvHlzjf8g+vbtKyQSiRg0aJDYtm2b+Ouvv8TMmTPF3LlzhRBCnD59WlSoUEH4+Pioj5HbT3gqlUq0bNlSmJubiylTpojdu3eL77//XtjY2AgfHx+RlpYmhBA6fyrU92eqbK8nx//3f/8nrK2txZw5c8T+/fvFzp07xbfffivmz5+v1/6SkpLUf5PJkyer48oeutGvXz91YhIaGir27t0rdu3aJZRKpWjVqpWwsbERISEhIjIyUixbtkyULVtWeHt7i9TUVPUxZsyYIX788Ufx559/igMHDohFixYJLy8vERAQoK5z7do10a1bNwFA4yfU7LbLTmb8/PzEH3/8IbZs2SIqV64sSpcuLUaNGiU6duwodu7cKdavXy+cnZ1FzZo1Nf7ma9euFRKJRHTq1En88ccfYseOHaJdu3bCzMxM4z+/7GSuSpUqYurUqSIyMlLMmTNHyGQyMWDAAHW9mJgYYWVlJdq0aaOO9e+//35jezdr1kyYmZmJLVu26PX30QWAcHd3F97e3mLDhg1i+/btolWrVgKA+O2339T1rly5ImxtbUXFihXFmjVrxJ9//ik++ugjAUDMmjVLXS80NFSYmZmJ4OBgsXfvXhERESHCwsLU/7ndvn1bfPnllwKA+OOPP3QOgcrJm7b18PAQrq6uokKFCmLFihVi//794vjx4yI+Pl64u7sLDw8PsXjxYrFnzx4xY8YMIZPJRP/+/TWO0b9/f7F8+XIRGRkpIiMjxYwZM4SVlZUICQlR18ntPZ2djHp4eIj+/fuLiIgIsWjRIlGyZEkREBAgAgMDxdixY8Xu3bvFrFmzhJmZmfjyyy81Yhg8eLCQSqVizJgxIiIiQvzyyy+iatWqwtnZWSQkJKjr+fv7CwcHB1GpUiWxaNEiERkZKYYOHSoAiNWrVwsh3vye1Ed0dLQAoP6iocuzZ89EiRIlRMuWLYUQQpw7d04AEEuXLtWo9+TJEyGTycTo0aPVZTNnzhQSiUR8+umnYufOneKPP/4Qvr6+wsbGRuN9kNPnhz4MSY4L4zNCl/T0dGFlZSXGjRuntU6f5Pi3334TU6dOFVu2bBFRUVFi48aNwt/fX5QpU0Y8ePBAXe/cuXPC0tJS1KxZU2zcuFFs375dtGnTRnh6emokx3FxccLS0lIEBgaKrVu3igMHDoj169eLvn37anTYvO7KlSsCgPj555+11o0dO1ZIJBLx4sWLXM9lzZo1AoBYsmSJ6NWrl7CxsREymUz4+/uL6OhondtUq1ZNdOnSJdf9FjdMjklD9oe+roeZmZlG3Zw+VLJ7m14VGhoqSpQoodUj/fvvvwsAIjw8XAghxMGDBwUAMWnSpFzjzMv4xIiICAFAzJ49W6N806ZN6g+JbLl94Ovj9eS4evXqolOnTgbvT4jcxzdm9+iuWLFCo3zDhg0CgNi8ebPOfS1YsEDnsVQqlVAoFCIqKkoAEOfOnVOvy23MMQDh4uKi0du8detWAUDUrl1b4z+57HHd2eO5U1JSROnSpUX79u019qlUKkWtWrVEgwYN1GXZyfHrf8uhQ4cKS0tLjePkdcxxbGysqFq1qqhcubKwsLAQO3fu1HvbVwEQVlZWGklXZmamqFq1qnjvvffUZb169RIymUzcunVLY/vWrVsLa2trdU94u3btRO3atXM9ZkGNOfbw8BBmZmbq6wmy/d///Z8oWbKkuHnzpkb5999/LwDk+EVEqVQKhUIhpk+fLhwcHDT+Xjm9p7OT49dfHyNHjhQAxPDhwzXKO3XqJEqXLq1ejomJEQDEDz/8oFHv9u3bWgmTv7+/ACCOHTumUdfb21udpAqR/+sANm7cKACIRYsW5VrP2dlZVKtWTb1cp04d4efnp1FnwYIFAoC4cOGCEEKIW7duCXNzc60vCM+ePRMuLi6iR48e6rKcPj/0YUhyXBifEbocO3ZMABAbN27UWmfImOPMzEzx/PlzYWNjo+60EUKI7t27CxsbG42EWalUCm9vb433WPb/e2fPns3TcY8cOSIA6PwV6ptvvhEAxL1793LdR2hoqAAg7OzsRMeOHUVERITYvHmzqFmzprC0tNT4zM/Wp08f4ezsnKdYizrOVkE6rVmzBidOnNB4HDt2zOD97dy5E9WrV0ft2rWRmZmpfrRs2VJjFoy//voLADBs2DBjnAYAYN++fQBezi7xqu7du8PGxgZ79+412rFe16BBA/z111+YMGECDhw4gBcvXhTIcbp27aqxvHPnTtjb26N9+/Ya7V27dm24uLhozDry77//onfv3nBxcYGZmRmkUin8/f0BAJcvX9Y7hoCAANjY2KiXq1WrBgBo3bo1JBKJVnn2FePR0dF4/Pgx+vXrpxGrSqVCq1atcOLECa2rpTt06KCxXLNmTaSlpSExMVHveF/1+PFjfPjhhwgMDMSFCxcQFBSErl27ql+PALBu3TpIJBLExcW9cX8tWrSAs7OzetnMzAw9e/bEtWvXcOfOHQAvX5ctWrSAu7u7xrb9+/dHamoqYmJiALx8DZ07dw5Dhw7Frl27kJycbNA5GqpmzZqoXLmyRtnOnTsREBAANzc3jb9Z69atAQBRUVHquvv27cOHH34IuVyufn1NnToVjx49ytPfq127dhrL2a+jtm3bapU/fvwYz58/V8cqkUjw8ccfa8Tq4uKCWrVqabwXAMDFxQUNGjTQaoNXZzgoLEIIjffOgAEDEB0djdjYWHXZypUrUb9+ffWMC7t27UJmZiY++eQTjfO1tLSEv7+/1vkC2p8fBaUwPyNede/ePQCAk5OTQXE/f/4c48ePx3vvvQdzc3OYm5ujZMmSSElJ0fiMjIqKQvPmzeHo6KguK1GiBHr06KGxv9q1a8PCwgKfffYZVq9eneeZJl5tq5zWKZVKrbYCoP63XLly2Lx5M1q2bIkuXbogIiICJUqUwOzZs7X26eTkhMTERGRmZuYpzqKMF+SRTtWqVXvjBXl5cf/+fVy7dg1SqVTn+ocPHwIAHjx4ADMzM7i4uBjt2I8ePYK5uTnKlCmjUS6RSODi4oJHjx4Z7VivmzdvHsqVK4dNmzZh1qxZsLS0RMuWLfHdd9+hUqVKRjmGtbW11gUw9+/fx9OnT2FhYaFzm+z2fv78OZo0aQJLS0t8/fXXqFy5MqytrXH79m106dIlT8l86dKlNZazj51TeVpamjpWAOjWrVuO+378+LHGf6oODg4a62UyGQAY/OVj+fLluH37NqZOnQoLCwts3rwZ3bp1Q+fOnbFt2za0bNkSBw4cQLVq1eDl5fXG/el6/WaXPXr0COXKlcOjR4/g6uqqVc/NzU1dDwAmTpwIGxsbrFu3DosWLYKZmRmaNm2KWbNmGfU9mhNdMd6/fx87dux44/v5+PHjCAoKQrNmzbB06VKUK1cOFhYW2Lp1K2bOnFngr6+SJUvi/v37EEJofFl5VYUKFTSWX39tAS9fX8b8Ylu+fHkAyPWLVkpKCh4+fAgfHx91WZ8+fTB27FisWrUKoaGhuHTpEk6cOKEx3WD2+6l+/fo691uihGafmK7Pj4JSmJ8Rr8r+21laWhoQNdC7d2/s3bsXU6ZMQf369WFnZweJRII2bdpovC4ePXqk83X2elnFihWxZ88ezJ49G8OGDUNKSgoqVKiA4cOHY8SIETnGkf3a1PV/1uPHjyGRSNQXb1asWFHjC11wcDCmTZum3seHH34IMzMz9XpXV1fUqlULp0+f1tq3paUlhBDq99S7gMkxFQpHR0dYWVlhxYoVOa4HXl4BrVQqkZCQoPM/ZUM4ODggMzMTDx480EiQhRBISEjI8T8RY7CxsUFISAhCQkJw//59dS9y+/btceXKFaMcQ1cvgqOjIxwcHBAREaFzG1tbWwAve/Xu3buHAwcOqHuLARTqnLfZf/v58+fnONtHTomNsVy/fh1mZmbqD34LCwv8/vvv6N69Ozp16oQffvgBa9as0Xse3oSEhBzLsv9zcnBwQHx8vFa97F6u7HYxNzfH6NGjMXr0aDx9+hR79uzBV199hZYtW+L27dsFfgV5Tq+vmjVrYubMmTq3yU7wN27cCKlUip07d2okJnmdfzo/HB0dIZFI1HPcvk5XWUGrW7cuSpUqhe3btyM0NFRnG2/fvh0qlQqBgYHqslKlSqFjx45Ys2YNvv76a6xcuRKWlpb46KOP1HWyXze///47PDw83hhLbr2Qb4v8fkZkb//qHL76SkpKws6dOxEcHIwJEyaoy9PT07X25+DgoE7kX6Xr86BJkyZo0qQJlEolTp48ifnz52PkyJFwdnZGr169dMZSsWJFWFlZ4cKFC1rrLly4gPfee0/9PtuxYwfS09PV67PfkzVr1szxXIUQWl+egJftJpPJ3pnEGGByTIWkXbt2+Oabb+Dg4JBrz1vr1q0RGhqKhQsXYvr06TnWy0tPTosWLTB79mysW7cOo0aNUpdv3rwZKSkpaNGihf4nkg/Ozs7o378/zp07h7CwML2nXDOkV7Rdu3bYuHEjlEolGjZsmGO97P8YX08QFi9enGscxpzKq3HjxrC3t8elS5cMnkJPl7y8RqpXrw6lUon169dj4MCBAP5LkDt16oRhw4bB398fvXv31mt/e/fuxf3799X/YSuVSmzatAkVK1ZEuXLlALx8XW7ZsgX37t1T/8cFvBzSZG1trTMJsLe3R7du3XD37l2MHDkSN27cgLe3d756zg19fYWHh6NixYoaU0G9TiKRwNzcXKOH6sWLF1i7dq3OOApi2FG7du3w7bff4u7du1o/bxsqv79UWFhY4H//+x+++uorfPfddxg3bpzG+sTEREycOBHOzs4YNGiQxroBAwbg119/RXh4ONatW4fOnTtrTPXWsmVLmJub4/r164U2XKKg5fczInuYxvXr1/O8rUQigRBC6zNy2bJlUCqVGmX+/v4IDw/Hw4cP1Qm5SqXCb7/9luP+zczM0LBhQ1StWhXr16/H6dOnc0yOzc3N0b59e/zxxx+YPXu2upPj1q1b2L9/v8b/bzVq1NC5j4YNG6JcuXLYvXs3lEql+r157949nDt3Tudn3L///gtvb+8cz6E4YnJMOl28eFHn+KKKFStqDU/Qx8iRI7F582Y0bdoUo0aNQs2aNaFSqXDr1i3s3r0bY8aMQcOGDdGkSRP07dsXX3/9Ne7fv4927dpBJpPhzJkzsLa2xpdffgng5Rt/48aN2LRpEypUqABLS8scPwwCAwPRsmVLjB8/HsnJyWjcuDHOnz+P4OBg+Pj4oG/fvnk+H301bNgQ7dq1Q82aNVGqVClcvnwZa9euha+vr949ftm9BevXr0e1atVQsmRJuLm5aSRUr+vVqxfWr1+PNm3aYMSIEWjQoAGkUinu3LmD/fv3o2PHjujcuTP8/PxQqlQpDBkyBMHBwZBKpVi/fj3OnTuntc/s9p01axZat24NMzMz1KxZM8ehG/oqWbIk5s+fj379+uHx48fo1q0bnJyc8ODBA5w7dw4PHjzAwoUL87zfGjVq4MCBA9ixYwdcXV1ha2uLKlWq6Kw7cOBArFy5Ep9//jkuXLiAli1bQqlUIiYmBocOHYK7uzsOHz6MX3/9Va8Ey9HREc2bN8eUKVNgY2ODBQsW4MqVK9i4caO6TnBwsHrs7tSpU1G6dGmsX78ef/75J2bPng25XA4AaN++vXre8TJlyuDmzZsICwuDh4eHemhO9t9m7ty56NevH6RSKapUqaL+z/NN7ZTXbadPn47IyEj4+flh+PDhqFKlCtLS0nDjxg2Eh4dj0aJFKFeuHNq2bYs5c+agd+/e+Oyzz/Do0SN8//33Ontr8/KezovGjRvjs88+w4ABA3Dy5Ek0bdoUNjY2iI+Px+HDh1GjRg18/vnnedqnIe/J140fPx7nzp1T/9uzZ0/I5XKcP38e3333HZ49e4adO3eqXwfZgoKCUK5cOQwdOhQJCQkYMGCAxnpPT09Mnz4dkyZNwr///otWrVqhVKlSuH//Po4fP67+Nasoye9nRLly5VChQgUcPXoUw4cP11qfnJyM33//Xau8TJky8Pf3R9OmTfHdd9/B0dERnp6eiIqKwvLly7Xmn540aRJ27NiBFi1aYNKkSbCyssKiRYvU46Gze2UXLVqEffv2oW3btihfvjzS0tLUv6p++OGHubZFSEgI6tevj3bt2mHChAlIS0vD1KlT4ejoiDFjxuS6bXYMP/74I3r06IGOHTvi888/R0pKCmbMmAELCwtMnDhRo75KpcLx48fVnQbvDFNeDUhvn9xmq8Br0wjlZbYKIYR4/vy5mDx5snre3ez5WkeNGqVxZb9SqRQ//vijqF69urqer6+v2LFjh7rOjRs3RFBQkLC1tdV7nuPx48cLDw8PIZVKhaurq/j888+1ps0x9mwVEyZMEPXq1ROlSpUSMplMVKhQQYwaNUo8fPgwT/vdsGGDqFq1qpBKpTrnOdZFoVCI77//Xj23a8mSJUXVqlXF//3f/2nMIRodHS18fX2FtbW1KFOmjBg0aJA4ffq01tX46enpYtCgQaJMmTJCIpHonOf4VdnzYX/33Xca5dmzD7w6rZkQQkRFRYm2bduK0qVLC6lUKsqWLSvatm2rUS97topXrwYX4r/X7aszLpw9e1Y0btxYWFtb6zXPcfbrs3LlykIqlQo7OzsREBAgfvnlF5GZmSk6deokzM3NtWYAeV12WyxYsEBUrFhRSKVSUbVqVbF+/XqtuhcuXBDt27cXcrlcWFhYiFq1amnNgPDDDz8IPz8/4ejoKCwsLET58uXFwIEDteZYnThxonBzcxMlSpTQe57jN22b03tZCCEePHgghg8fLry8vIRUKhWlS5cWdevWFZMmTdKYkWDFihWiSpUq6td/aGioWL58udbfK6f3dE6vl5zmZM/pNbJixQrRsGFDYWNjI6ysrETFihXFJ598Ik6ePKmuk9Nnmq7ZF3J6T+aFSqUS69evF82aNRP29vbCwsJCeHl5ic8//1xrJpBXffXVV+opA7PnX37d1q1bRUBAgLCzsxMymUx4eHiIbt26aUx7lp/PO0PnOX5VQXxG5GTKlCmiVKlS6ukns2XPUKLrkf2ZcefOHdG1a1f1XN2tWrUSFy9eFB4eHloz4hw6dEg0bNhQyGQy4eLiIv73v/+JWbNmCQDqGWhiYmJE586dhYeHh5DJZMLBwUH4+/uL7du3v/E8hBDi5MmTokWLFsLa2lrY2dmJTp06iWvXrum1bbatW7eK+vXrq+dO79Chg85ZZvbu3SsAiFOnTuVp/0WdRAghCjD3JiJ6p0gkEgwbNgw//fSTqUMhoiz37t2Dl5cX1qxZg549exbqsYOCgnDjxg38888/hXpcY+jbty/+/fdfHDlyxNShFCoOqyAiIqJizc3NDSNHjsTMmTPRvXt3nReeGcPo0aPh4+MDd3d3PH78GOvXr0dkZCSWL19eIMcrSNevX8emTZvU06G+S5gcE5mIEELrgo7XmZmZFYmryento1Kp1HOa5sTcnP8F0Ltj8uTJsLa2xt27d7XmFzcWpVKJqVOnIiEhARKJBN7e3li7di0+/vjjAjleQbp16xZ++uknfPDBB6YOpdBxWAWRiRw4cAABAQG51lm5cqXWzUuI9DFt2rQ3XngVFxcHT0/PwgmIiKiIYHJMZCLPnj3TuNOVLl5eXjpvSkD0Jvfu3VPPmZwTY8w2QkRU3DA5JiIiIiLKUjAj0omIiIiIiqAidTXGwYMH8d133+HUqVOIj4/Hli1b0KlTp1y3iYqKwujRo/H333/Dzc0N48aNw5AhQzTqbN68GVOmTMH169dRsWJFzJw5E507d9Y7LpVKhXv37sHW1pYXTxERERG9hYQQePbsGdzc3HKfscRUEywbIjw8XEyaNEls3rxZABBbtmzJtf6///4rrK2txYgRI8SlS5fE0qVLhVQqFb///ru6TnR0tDAzMxPffPONuHz5svjmm2+Eubm5OHr0qN5x3b59O9cbZ/DBBx988MEHH3zw8XY8bt++nWteV2THHEskkjf2HI8fPx7bt2/H5cuX1WVDhgzBuXPnEBMTAwDo2bMnkpOT8ddff6nrZN9uc8OGDXrFkpSUBHt7e9y+fRt2dnaGnZC+VCoo4uJw8OBBNO3dG1Idt2GlgqFQKLB7924EBQVBKpWaOpx3BtvdNNjupsF2Nw22u2kUdrsnJyfD3d0dT58+1bo1+6uK1LCKvIqJiUFQUJBGWcuWLbF8+XIoFApIpVLExMRg1KhRWnXCwsJy3G96ejrS09PVy8+ePQMAWFlZwcrKyngnoEtKCuzq1EFnAKm9e0Na0McjNXNzc1hbW8PKyoofnoWI7W4abHfTYLubBtvdNAq73RUKBQC8cQhssU6OExIS4OzsrFHm7OyMzMxMPHz4EK6urjnWSUhIyHG/oaGhOucP3b17N6ytrY0TfA7M0tLQLuv5vn37oLS0LNDjkbbIyEhTh/BOYrubBtvdNNjupsF2N43CavfU1FS96hXr5BjQ/naQPYrk1XJddXL7VjFx4kSMHj1avZzdTR8UFFTwwypSUtRPmzdvDqm9fcEej9QUCgUiIyMRGBjInoVCxHY3Dba7abDdTYPtbhqF3e7Jycl61SvWybGLi4tWD3BiYiLMzc3VN1bIqc7rvcmvkslkkOkY6yuVSgv+j/vK/gvleKSF7W4abHfTYLubBtvdNNjuplFY7a7vMYp1cuzr64sdO3ZolO3evRv16tVTN5Cvry8iIyM1xh3v3r0bfn5+hRorERFRToQQyMzMhFKpNHUoxZJCoYC5uTnS0tLYxoXI2O1uZmYGc3PzfE+rW6SS4+fPn+PatWvq5bi4OJw9exalS5dG+fLlMXHiRNy9exdr1qwB8HJmip9++gmjR4/G4MGDERMTg+XLl2vMQjFixAg0bdoUs2bNQseOHbFt2zbs2bMHhw8fLvTzIyIiel1GRgbi4+P1Hi9JeSeEgIuLC27fvs37FRSigmh3a2truLq6wsLCwuB9FKnk+OTJkwgICFAvZ4/77devH1atWoX4+HjcunVLvd7Lywvh4eEYNWoUfv75Z7i5uWHevHno2rWruo6fnx82btyIyZMnY8qUKahYsSI2bdqEhg0bFt6JERER6aBSqRAXFwczMzO4ubnBwsKCyVsBUKlUeP78OUqWLJn7zSHIqIzZ7kIIZGRk4MGDB4iLi0OlSpUM3meRSo6bNWuG3KZlXrVqlVaZv78/Tp8+net+u3Xrhm7duuU3vMJhbg7lkCG4dfMmypkXqT8fERHlUUZGBlQqFdzd3Qt8NqR3mUqlQkZGBiwtLZkcFyJjt3v2lHA3b95U79cQzK6KGpkMqnnzcD48HOV4AxAioncCEzYi/RjjvcJ3GxERERFRFibHRY0QwIMHsEhKevmciIiIiIyGwyqKmtRUSMuWRWsAig4dgHxcjUlEREREmthzTEREREYlkUhyffTv379Ajjtt2jTUrl27QPZtiAMHDkAikeDp06dvrLt48WLUqlULNjY2sLe3h4+PD2bNmqVe379/f3Tq1MlosTVr1gwjR4402v6KE/YcExERkVHFx8ern2/atAlTp05FbGysuszKykqjvkKheKfvTLd8+XKMHj0a8+bNg7+/P9LT03H+/HlcunTJ6Md619taH+w5JiIiKopSUnJ+pKXpX/fFC/3q5oGLi4v6IZfLIZFI1MtpaWmwt7fHr7/+imbNmsHS0hLr1q0DAKxcuRLVqlWDpaUlqlatigULFmjsd/z48ahcuTKsra1RoUIFTJkyBQqFAsDL6VxDQkJw7tw5dQ919hSvEokEixcvRrt27WBtbY1q1aohJiYG165dQ7NmzWBra4ugoCBcv35d43g7duxA3bp1YWlpiQoVKiAkJASZmZnq9RKJBMuWLUPnzp1hbW2NSpUqYfv27QCAGzduqO/NUKpUqVx7zHfs2IEePXpg4MCBeO+99/D+++/jo48+wowZMwC87BFfvXo1tm3bpj63AwcOvLFNsretXbs2VqxYgQoVKkAmk6Ffv36IiorC3Llz1fu7ceNGnv7GxRl7jouY1IxMWL/yXG7SaIiIyGRKlsx5XZs2wJ9//rfs5ATkdIc9f38gK9ECAHh6Ag8fatcz8kXg48ePxw8//ICVK1dCJpNh6dKlCA4Oxk8//QQfHx+cOXMGgwcPho2NDfr16wcAsLW1xapVq+Dm5oYLFy5g8ODBsLW1xbhx49CzZ09cvHgRERER2LNnDwBALv/vf8kZM2Zgzpw5mDNnDsaPH4/evXujQoUKmDhxIsqVK4cBAwbgyy+/REREBABg165d+PjjjzFv3jw0adIE169fx2effQYACA4OVu83JCQEs2fPxnfffYf58+ejT58+uHnzJtzd3bF582Z07doVsbGxsLOz0+oxz+bi4oKoqCjcvHkTHh4eWuvHjh2Ly5cvIzk5GStXrgQAlC5d+o1tku3atWv49ddfsXnzZpiZmcHDwwNXr15F9erVMX36dABAmTJlDPtDFkeC8i0pKUkAEElJSQV+rJTHT4V4+RElnt5/UODHo/9kZGSIrVu3ioyMDFOH8k5hu5sG2900Xm/3Fy9eiEuXLokXL15oV876v0Dno00bzbrW1jnX9ffXrOvoqLuegVauXCnkcrl6OS4uTgAQYWFhGvXc3d3FL7/8olE2Y8YM4evrm+O+Z8+eLerWrateDg4OFrVq1dKqB0BMnjxZvRwTEyMAiOXLlwshhFAqlWLZsmXC0tJSXadJkybim2++0djP2rVrhaura477ff78uZBIJOKvv/4SQgixf/9+AUA8efIkx3MQQoh79+6JRo0aCQCicuXKol+/fmLTpk1CqVSq6/Tr10907Ngx1/0IobtNpFKpSExM1Kjn7+8vRowY8cb9FSSlUimePHmicZ75ldt7Rt98jT3HRERERdHz5zmvMzPTXE5MzLnu6zdNKKSf1+vVq6d+/uDBA9y+fRsDBw7E4MGD1eWZmZkavb+///47wsLCcO3aNTx//hyZmZmws7PT63g1a9ZUP3d2dgYA1KhRQ13m5OSEtLQ0JCcnw87ODqdOncKJEycwc+ZMdR2lUom0tDSkpqaq71j46n5tbGxga2uLxNzaWwdXV1fExMTg4sWLiIqKQnR0NPr164dly5YhIiIi1xtb6NMmHh4e7BnOAybHRY25OX6v3gIAEMjbRxMRvbtsbExfNx9sXjmOSqUCACxduhQNGzbUqGeWlegfPXoUvXr1QkhICFq2bAm5XI6NGzfihx9+0Ot4r16EJpFIcizLjkWlUiEkJARdunTR2tertyV+/eI2iUSi3kdeVa9eHdWrV8ewYcNw+PBhNGnSBFFRUeqxy6/Tt01sCulvWlwwuypqZDKMbTsKAHCOt48mIqJiwNnZGWXLlsW///6LPn366Kxz5MgReHh4YNKkSeqymzdvatSxsLCAUqk0Skx16tRBbGws3nvvPYP3YZF1LwJDYvL29gYApGRdDKnr3PRpk9xiM1ZbFTdMjomIiMjkpk2bhuHDh8POzg6tW7dGeno6Tp48iSdPnmD06NF47733cOvWLWzcuBH169fHn3/+iS1btmjsw9PTE3FxcTh79izKlSsHW1tbyAzsSJo6dSratWsHd3d3dO/eHSVKlMD58+dx4cIFfP3113rtw8PDAxKJBDt37kSbNm1gZWWFkjoupPz888/h5uaG5s2bo1y5coiPj8fXX3+NMmXKwNfXV31uu3btQmxsLBwcHCCXy/Vqk5x4enri2LFjuHHjBkqWLInSpUvnOnzjXcJWKGqEgFVGGqwy0nj7aCr2UjMyUWnKboyIMUdqRuabNyCiImvQoEFYtmwZVq1ahRo1asDf3x+rVq2Cl5cXAKBjx44YNWoUvvjiC9SuXRvR0dGYMmWKxj66du2KVq1aISAgAGXKlMGGDRsMjqdly5bYuXMnIiMjUb9+fTRq1Ahz5szROZtETsqWLYuQkBBMmDABzs7O+OKLL3TW+/DDD3H06FF0794dlStXRteuXWFpaYm9e/fCwcEBADB48GBUqVIF9erVQ5kyZXDkyBG92iQnY8eOhZmZGby9vVGmTBncunVL7/Mq7iRCMMPKr+TkZMjlciQlJel9YYChUp8kwbq0PQAg6f4DyJ0cC/R49B+FQoHw8HC0adOGE6gXktSMTHhP3QUAODelOeQ2uqdBIuPj6900Xm/3tLQ0xMXFwcvLS2OcKxmXSqVSX4jH3tPCUxDtntt7Rt98ja8AIiIiIqIsTI6JiIiIiLIwOSYiIiIiysLkmIiIiIgoC5NjIiIiIqIsTI6JiIiIiLLwJiBFjZkZ/qzSGADwQdYtNYmIiIjIOJgcFzWWlhjWaSIA4BznvCQiIj29Om/4pektYW3BFIBIFw6rICIiIiLKwuSYiIjoHaBU/XdD3ONxjzWWSX/Tpk1D7dq1TR2GUR05cgQ1atSAVCpFp06ddJYdOHAAEokET58+1WufzZo1w8iRIwss5oLE5LioSUnBjVntcGNWOyAlxdTREBFRERBxMR4fzolSL/dfeQIfzNqHiIvxBXbM/v37qxMtfUkkEmzdurVA4jGErnjGjh2LvXv3Fsrxz5w5g+7du8PZ2RmWlpaoXLkyBg8ejH/++ceoxxk9ejRq166NuLg4rFq1SmeZn58f4uPjIZfL9drnH3/8gRkzZhg1TkNeU4ZgckxERFSMRVyMx+frTuN+crpGeUJSGj5fd7pAE2RTUSgUBbbvkiVLwsHBocD2n23nzp1o1KgR0tPTsX79ely+fBlr166FXC7HlClTjHqs69evo3nz5ihXrhzs7e11lllYWMDFxQUSiUSvfZYuXRq2trZGjbOwMDkmIiIqppQqgZAdl6BrAEV2WciOS4UyxKJZs2YYPnw4xo0bh9KlS8PFxQXTpk1Tr/f09AQAdO7cGRKJRL0MADt27EDdunVhaWmJChUqICQkBJmZmer1EokEixYtQseOHWFjY4Ovv/4aSqUSAwcOhJeXF6ysrFClShXMnTtXK64VK1agRo0acHZ2RtmyZfHFF1/kGs/rwypUKhWmT5+OcuXKQSaToXbt2oiIiFCvv3HjBiQSCf744w8EBATA2toatWrVQkxMTI5tlZqaigEDBqBNmzbYvn07PvzwQ3h5eaFhw4b4/vvvsXjxYnXdqKgoNGjQADKZDK6urpgwYYJG2wghMHv2bFSoUAFWVlaoVasWfv/9d43YHj16hE8//RQSiQSrVq3SWaZrWMWRI0fg7+8Pa2trlCpVCi1btsSTJ0/Uf+9Xh1VkZGRg3LhxKFu2LGxsbNCwYUMcOHBAvX7VqlWwt7fHrl27UK1aNZQsWRKtWrVCfHy8ut1Xr16Nbdu2QSKRQCKRaGxvTEyOiYiIiqnjcY8Rn5SW43oBID4pDcfjHhdKPKtXr4aNjQ2OHTuG2bNnY/r06YiMjAQAnDhxAgCwcuVKxMfHq5d37dqFjz/+GMOHD8elS5ewePFirFq1CjNnztTYd3BwMDp27IgLFy7g008/hUqlQrly5fDrr7/i0qVLmDp1Kr766iv8+uuv6m0WLlyIYcOGYfDgwThy5Ai2bt2K9957L9d4Xjd37lz88MMP+P7773H+/Hm0bNkSHTp0wNWrVzXqTZo0CWPHjsXZs2dRuXJlfPTRRxpJ7Kt27dqFhw8fYty4cTrXZ/fu3r17F23atEH9+vVx7tw5LFy4EMuXL8fXX3+trjt58mSsXLkSCxcuxN9//41Ro0bh448/RlRUFNzd3REfHw87OzuEhYUhPj4e3bt31yrr2bOnVgxnz55FixYt8P777yMmJgaHDx9G+/btoVQqdcY8YMAAHDlyBBs3bsT58+fRvXt3tGnTBtevX1fXSU1Nxffff4+1a9fi4MGDuHXrFsaOHQvg5XCWHj16qBPm+Ph4+Pn56TxWfnEeFyIiomIq8VnOibEh9fKrZs2aCA4OBgBUqlQJP/30E/bu3YvAwECUKVMGwMvEz8XFRb3NzJkzMWHCBPTr1w8AUKFCBcyYMQPjxo1T7wsAevfujU8//VTjeCEhIernXl5eiI6Oxq+//ooePXoAAL7++muMGTMGw4cPR3JyMuzs7NCwYUMAyDGe133//fcYP348evXqBQCYNWsW9u/fj7CwMPz888/qemPHjkXbtm3Vcb3//vu4du0aqlatqrXP7MRa17pXLViwAO7u7vjpp58gkUhQtWpV3Lt3D+PHj8fUqVPx4sULzJkzB/v27YOvr6+6/Q4fPozFixfD399fPVRCLperz9PGxkar7HWzZ89GvXr1sGDBAnXZ+++/r7Pu9evXsWHDBty5cwdubm7q9oiIiMD69evh4+MD4OVwmEWLFqFixYoAgC+++ALTp08H8HI4i5WVFdLT03P9exgDk2MiIqK3wMt5iHcDMEezDzMhl0rzvU8nW/3mw9e3Xn7VrFlTY9nV1RWJiYm5bnPq1CmcOHFCo6dYqVQiLS0NqampsLa2BgDUq1dPa9tFixZh2bJluHnzJl68eIGMjAz1kIjExETcu3cPLVq0MPh8kpOTce/ePTRu3FijvHHjxjh37pxG2avn7urqqo5BVwIshH7DXC5fvgxfX1+NccCNGzfG8+fPcefOHdy/fx9paWkIDAzU2C4jI0OdkBrq7Nmz6N69u151T58+DSEEKleurFGenp4OOzs79bK1tbU6MQb0e30UBCbHRERExVQDr9JwlVsiISlN57hjCQAXuSUaeJUulHikryX8EokEKpUq121UKhVCQkLQpUsXrXWWr9wMy8bGRmPdr7/+ilGjRuGHH36Ar68vbG1t8d133+HYsWMAACsrK0NPQ8vrF6kJIbTKXj337HU5nXt2EnnlyhV1j68uuo6TnVi/2rZ//vknypYtq1FPJpPluF995KX9VCoVzMzMcOrUKZi9cndflUql8UVA1+tD3y8KxsTkuKgxM8O+Ci+/Hdfl7aOJiCgXZiUkCG7vjc/XnYYE0EiQs1Oq4PbeMCuh3wwEBU0qlWqNWa1Tpw5iY2PVY4H1dejQIfj5+WHo0KHqslfHt9ra2sLT0xN79+6Fv7+/3vG8ys7ODm5ubjh8+DCaNm2qLo+OjkaDBg3yFO+rgoKC4OjoiNmzZ2PLli1a658+fQp7e3t4e3tj8+bNGklydHQ0bG1tUbZsWdjb20Mmk+HWrVs5nqOhatasib1792oMXcmJj48PlEolEhMT0aRJE3W5SqVCcnKy3se0sLDI9e9hLEyOixpLS3zafRoA3j6aiIjerFV1Vyz8uA6Ct/+tMZ2bi9wSwe290aq6qwmj05SdrDZu3BgymQylSpXC1KlT0a5dO7i7u6N79+4oUaIEzp8/jwsXLmhcePa69957D2vWrMGuXbvg5eWFtWvX4sSJE/Dy8lLXmTZtGoYMGYIyZcrggw8+gBACMTEx+PLLL3OM53X/+9//EBwcjIoVK6J27dpYuXIlzp49i/Xr1xvcDjY2Nli2bBm6d++ODh06YPjw4Xjvvffw8OFD/Prrr7h16xY2btyIoUOHIiwsDF9++SW++OILxMbGIjg4GKNHj0aJEiVga2uLsWPHYtSoUVCpVPjggw+QnJyM6OholCxZUj2O2xATJ05EjRo1MHToUAwZMgQWFhbYv38/unfvDkdHR426lStXRp8+ffDJJ5/ghx9+gI+PDx4+fIi9e/eiQoUK6Natm17H9PT0xK5duxAbGwsHBwfI5XKt3mZj4GwVRERExVyr6q7YM/q/nsNVA+rj8Pjmb1ViDAA//PADIiMj4e7urh4T27JlS+zcuRORkZGoX78+GjVqhDlz5sDDwyPXfQ0ZMgRdunRBz5490bBhQzx69EijFxkA+vXrh7CwMCxcuBC+vr5as0zoiud1w4cPx5gxYzBmzBjUqFEDERER2L59OypVqpSvtujYsSOio6MhlUrRu3dvVK1aFR999BGSkpLUXwrKli2L8PBwHD9+HLVq1cKQIUMwcOBATJ48Wb2fGTNmYOrUqQgNDUW1atXQsmVL7NixQ+NLgiEqV66M3bt349y5c2jQoAF8fX2xbds2mJvr7ndduXIlPvnkE4wZMwZVqlRBhw4dcOzYMa3hHrkZPHgwqlSpgnr16qFMmTI4cuRIvs4hJxJhisEcxUxycjLkcjmSkpI0BpYXhJcXbOwCAJyb0hxyG+ONmaLcKRQKhIeHo02bNgXyTZW08fVuOny9Fz5dr/e0tDTExcXBy8tLY3xtfvd/aXpLWFvwx+Ns2T/v29nZoUQJ9hsWloJo99zeM/rma3xnFDUpKbg0pysAQDH6DsBkgYiI9GBtYY4b37Y1dRhEbz0mx0WQteLlmLEkE8dBREREVNwwOSbSQ0HMP0pERERvHw6sISIiIiLKUuSS4wULFqgHWdetWxeHDh3KsW7//v0hkUi0Hq/e3nDVqlU666SlFc6tNImIiN6E184T6ccY75UilRxv2rQJI0eOxKRJk3DmzBk0adIErVu3xq1bt3TWnzt3LuLj49WP27dvo3Tp0lq3O7Szs9OoFx8fn++rgomIiPIre6aQ1NRUE0dCVDRkv1fyM8tOkRpzPGfOHAwcOBCDBg0CAISFhWHXrl1YuHAhQkNDterL5XLI5XL18tatW/HkyRMMGDBAo55EIoGLi0vBBk9ERJRHZmZmsLe3R2JiIgDA2tpa63bBlH8qlQoZGRlIS0vjVG6FyJjtLoRAamoqEhMTYW9vr3Gb6rwqMslxRkYGTp06hQkTJmiUBwUFITo6Wq99LF++HB9++KHWxOHPnz+Hh4cHlEolateujRkzZuQ42TcApKenIz39v7sMZd/6UKFQQKFQ6HtKBlEolTjqXh0A8J5SVeDHo5cUikyN52z3wsF2N53stmabF56cXu8ODg5QKpW4f/++qUIr9oQQSEtLg6WlJb98FKKCaHc7Ozs4ODjo/OzS9/OsyCTHDx8+hFKphLOzs0a5s7MzEhIS3rh9fHw8/vrrL/zyyy8a5VWrVsWqVatQo0YNJCcnY+7cuWjcuDHOnTuX491tQkNDdd5LfPfu3bC2ts7DWeVduhIY1/tbAMDsmBjIDP9iRHmQrgSy3y779u1juxcStrvpRUZGmjqEd8abXu8SiSRfvWFExZ1Sqcx1zLG+w5OKTHKc7fVvFkIIvb5trFq1Cvb29ujUqZNGeaNGjdCoUSP1cuPGjVGnTh3Mnz8f8+bN07mviRMnYvTo0erl5ORkuLu7IygoqFDukDfu+D4AQPPmzSG34djowsB2Nw22u+koFApERkYiMDCQd8grJHy9mw5f76ZR2O2e/Uv/mxSZ5NjR0RFmZmZavcSJiYlavcmvE0JgxYoV6Nu3LywsLHKtW6JECdSvX1/j3uqvk8lkkMlkWuVSqbTA/7hS8d8XAanUnG/iQsJ2Nw22u+kVxucavcTXu+nx9W4ahdXu+h6jyIw6t7CwQN26dbV+4ouMjISfn1+u20ZFReHatWsYOHDgG48jhMDZs2fh6uqar3gLTEoKTs3rjVPzegMpKaaOhoiIiKhYKTI9xwAwevRo9O3bF/Xq1YOvry+WLFmCW7duYciQIQBeDne4e/cu1qxZo7Hd8uXL0bBhQ1SvXl1rnyEhIWjUqBEqVaqE5ORkzJs3D2fPnsXPP/9cKOdkCIcXL38W4O2jiYiI8od3QKXXFankuGfPnnj06BGmT5+O+Ph4VK9eHeHh4erZJ+Lj47XmPE5KSsLmzZsxd+5cnft8+vQpPvvsMyQkJEAul8PHxwcHDx5EgwYNCvx8iIiIiOjtUqSSYwAYOnQohg4dqnPdqlWrtMrkcnmuVyf++OOP+PHHH40VHhEREREVYUVmzDERERERUUFjckxERERElIXJMRERERFRliI35vidV6IEzrm8vHOfJ+//TkRERGRUTI6LGisrdOz38gLCc1ZWJg6GiIojTm1FRO8ydj0SEREREWVhckxERERElIXDKoqa1FQcXvjpy+djLgE2HFpBREREZCxMjosaIVAuOREAkCSEiYMhIiIiKl44rIKIiIiIKAuTYyIiIiKiLEyOiYiIiIiyMDkmIiIiIsrC5JiIiIiIKAtnqyhqJBL841AeAOAskZg4GCIiIqLihclxUWNtjaBBCwAA56ytTRwMERERUfHCYRVERERERFmYHBMRERERZeGwiqImNRW7lw19+XzMad4+moiIiMiImBwXNUKg8qNbAHj7aCIiIiJjy3NyHBsbiw0bNuDQoUO4ceMGUlNTUaZMGfj4+KBly5bo2rUrZDJZQcRKRERERFSg9B5zfObMGQQGBqJWrVo4ePAg6tevj5EjR2LGjBn4+OOPIYTApEmT4ObmhlmzZiE9Pb0g4yYiIiIiMjq9e447deqE//3vf9i0aRNKly6dY72YmBj8+OOP+OGHH/DVV18ZJUgiIiIiosKgd3J89epVWFhYvLGer68vfH19kZGRka/AiIiIiIgKm97DKvRJjPNTn4iIiIjI1AyerWLv3r3Yu3cvEhMToVKpNNatWLEi34FRDiQS3LFzAgDY8vbRREREREZlUHIcEhKC6dOno169enB1dYWESVrhsbbGB5+//PLB20cTERERGZdByfGiRYuwatUq9O3b19jxEBERERGZjEG3j87IyICfn5+xYyEiIiIiMimDkuNBgwbhl19+MXYspI8XL7Bt9ShsWz0KePHC1NEQERERFSsGDatIS0vDkiVLsGfPHtSsWRNSqVRj/Zw5c4wSHOmgUqFWwlUAQNJrF0ISERERUf4YlByfP38etWvXBgBcvHhRYx0vziMiIiKiosqg5Hj//v3GjoOIiIiIyOQMGnP8qjt37uDu3bvGiIWIiIiIyKQMSo5VKhWmT58OuVwODw8PlC9fHvb29pgxY4bWDUGIiIiIiIoKg4ZVTJo0CcuXL8e3336Lxo0bQwiBI0eOYNq0aUhLS8PMmTONHScRERERUYEzKDlevXo1li1bhg4dOqjLatWqhbJly2Lo0KFMjgvYIys7APm49zcRERER6WRQfvX48WNUrVpVq7xq1ap4/PhxvoOiXNjYoO7wl3NMn7OxMXEwRERERMWLQWOOa9WqhZ9++kmr/KeffkKtWrXyHRQRERERkSkY1HM8e/ZstG3bFnv27IGvry8kEgmio6Nx+/ZthIeHGztGIiIiIqJCYVDPsb+/P/755x907twZT58+xePHj9GlSxfExsaiSZMmxo5Rw4IFC+Dl5QVLS0vUrVsXhw4dyrHugQMHIJFItB5XrlzRqLd582Z4e3tDJpPB29sbW7ZsKdBzyJcXL7DxlwnY+MsE3j6aiIiIyMgMvqbLzc2t0C+827RpE0aOHIkFCxagcePGWLx4MVq3bo1Lly6hfPnyOW4XGxsLOzs79XKZMmXUz2NiYtCzZ0/MmDEDnTt3xpYtW9CjRw8cPnwYDRs2LNDzMYhKhUa3X96VkLePJiIiIjIuvZPj8+fPo3r16ihRogTOnz+fa92aNWvmOzBd5syZg4EDB2LQoEEAgLCwMOzatQsLFy5EaGhojts5OTnB3t5e57qwsDAEBgZi4sSJAICJEyciKioKYWFh2LBhg9HPgYiIiIjeXnonx7Vr10ZCQgKcnJxQu3ZtSCQSCCG06kkkEiiVSqMGCQAZGRk4deoUJkyYoFEeFBSE6OjoXLf18fFBWloavL29MXnyZAQEBKjXxcTEYNSoURr1W7ZsibCwsBz3l56ejvT0dPVycnIyAEChUEChUOh7SgZ5df8KRWaBH49eUigyNZ6z3QsH29002O6mwXY3Dba76WS3dWG1ub7H0Ts5jouLUw9HiIuLMyyqfHj48CGUSiWcnZ01yp2dnZGQkKBzG1dXVyxZsgR169ZFeno61q5dixYtWuDAgQNo2rQpACAhISFP+wSA0NBQhISEaJXv3r0b1tbWeT21PMlMSUPXrOdRUVEwt7Es0OPRS+lKIPvtsm/fPsjMTBrOO4Ptbhpsd9Ngu5sG2930IiMjC+U4qampetXTOzn28PBQP7958yb8/Pxgbq65eWZmJqKjozXqGptEItFYFkJolWWrUqUKqlSpol729fXF7du38f3336uT47zuE3g59GL06NHq5eTkZLi7uyMoKEhjbHNBSH2SpH7u7+8PuZNDgR6PXkrNyMS44/sAAM2bN4ecX0oKBdvdNNjupsF2Nw22u+koFApERkYiMDAQUqm0wI+X/Uv/mxh0QV5AQADi4+Ph5OSkUZ6UlISAgIACGVbh6OgIMzMzrR7dxMRErZ7f3DRq1Ajr1q1TL7u4uOR5nzKZDDKZTKtcKpUW+B/31f1LpeaF8mIiQCr++7LEdi88bHfTYLubBtvdNNjuplcY+VP2cfRh0FRuOfWsPnr0CDYFdNc2CwsL1K1bV6vrPTIyEn5+fnrv58yZM3B1dVUv+/r6au1z9+7dedpnYUuVypAq1U7OiYiIiCh/8tRz3KVLFwAvhyH0799fo/dUqVTi/PnzBZpUjh49Gn379kW9evXg6+uLJUuW4NatWxgyZAiAl8Md7t69izVr1gB4OROFp6cn3n//fWRkZGDdunXYvHkzNm/erN7niBEj0LRpU8yaNQsdO3bEtm3bsGfPHhw+fLjAziNfbGzgPfpl/Lx9NBEREZFx5Sk5lsvlAF72HNva2sLKykq9zsLCAo0aNcLgwYONG+ErevbsiUePHmH69OmIj49H9erVER4erh7jHB8fj1u3bqnrZ2RkYOzYsbh79y6srKzw/vvv488//0SbNm3Udfz8/LBx40ZMnjwZU6ZMQcWKFbFp06a3c45jIiIiIipQeUqOV65cCQDw9PTE//73vwKfmUGXoUOHYujQoTrXrVq1SmN53LhxGDdu3Bv32a1bN3Tr1s0Y4RERERFREWbQBXmffPIJ7t69i0qVKmmUX716FVKpFJ6ensaIjXRJS8OK36a9fP4/P8DGKtfqRERERKQ/gy7I69+/v84bbxw7dgz9+/fPb0yUG6USzf89ieb/ngQKYFYQIiIioneZQcnxmTNn0LhxY63yRo0a4ezZs/mNiYiIiIjIJAxKjiUSCZ49e6ZVnpSUVCBzHBMRERERFQaDkuMmTZogNDRUIxFWKpUIDQ3FBx98YLTgiIiIiIgKk0EX5M2ePRtNmzZFlSpV0KRJEwDAoUOHkJycjH379hk1QCIiIiKiwmJQz7G3tzfOnz+PHj16IDExEc+ePcMnn3yCK1euoHr16saOkV6hVAnEuNfAtmpNceJ2MpQqYeqQiIiIiIoNg3qOAcDNzQ3ffPONMWOhN4i4GI/gbRdxv3foy4LfLsN1dxyC23ujVXXX3DcmIiIiojcyODkGgNTUVNy6dQsZGRka5TVr1sxXUKQt4mI8Pl93Gq/3EyckpeHzdaex8OM6TJCJiIiI8smg5PjBgwcYMGAA/vrrL53rOWOFcSlVAiE7LmklxgAgAEgAhOy4hEBvF5iVkBRydERERETFh0FjjkeOHIknT57g6NGjsLKyQkREBFavXo1KlSph+/btxo7xnXc87jHik9JyXC8AxCel4Xjc48ILioiIiKgYMqjneN++fdi2bRvq16+PEiVKwMPDA4GBgbCzs0NoaCjatm1r7DjfaYnPck6MDalHRERERLoZ1HOckpICJycnAEDp0qXx4MEDAECNGjVw+vRp40VHAAAnW0uj1iMiIiIi3QxKjqtUqYLY2FgAQO3atbF48WLcvXsXixYtgqsrLwoztgZepeEqt0ROo4klAFzllmjgVbowwyIiIiIqdgwecxwfHw8ACA4ORkREBMqXL4958+ZxercCYFZCguD23gCglSBnLwe39+bFeERERET5ZNCY4z59+qif+/j44MaNG7hy5QrKly8PR0dHowVH/2lV3RULP67zcp7jZ/9Nnecit+Q8x0RERERGkufkWKFQoEqVKti5cye8vV/2ZlpbW6NOnTpGD440taruisauVrjo0xSJJUvBZu1KBPh4sceYiIiIyEjynBxLpVKkp6dDImFCZgpmJSTwvX0BAJDkbsfEmIiIiMiIDBpz/OWXX2LWrFnIzMw0djxERERERCZj0JjjY8eOYe/evdi9ezdq1KgBGxsbjfV//PGHUYIjHaytUW3U7wCAo9bWJg6GiIiIqHgxKDm2t7dH165djR0L6UMiwQsLS/VzIiIiIjIeg5LjlStXGjsOIiIiIiKTMyg5BoDMzEwcOHAA169fR+/evWFra4t79+7Bzs4OJUuWNGaM9Kr0dHz/548vn49rDNhYmTYeIiIiomLEoOT45s2baNWqFW7duoX09HQEBgbC1tYWs2fPRlpaGhYtWmTsOClbZia6XdwLAEjiBZFERERUBKVmZMJ76m4A5mj2YSbkUqmpQ1IzaLaKESNGoF69enjy5AmsrP7ruezcuTP27t1rtOCIiIiIiAqTQT3Hhw8fxpEjR2BhYaFR7uHhgbt37xolMCIiIiKiwmZQz7FKpYJSqdQqv3PnDmxtbfMdFBERERGRKRiUHAcGBiIsLEy9LJFI8Pz5cwQHB6NNmzbGio2I3nFKlVA/P3HjicYyERFRQTBoWMWPP/6IgIAAeHt7Iy0tDb1798bVq1fh6OiIDRs2GDtGInoHRVyMR/D2v9XLg9aegav8MoLbe6NVdVcTRkZERMWZQcmxm5sbzp49i40bN+LUqVNQqVQYOHAg+vTpo3GBHhGRISIuxuPzdafxej9xQlIaPl93Ggs/rsMEmYiICoRByfHBgwfh5+eHAQMGYMCAAeryzMxMHDx4EE2bNjVagPQaa2vU+XI9AGA/bx9NxZBSJRCy45JWYgwAAoAEQMiOSwj0doFZCd4lkoiIjMugMccBAQF4/PixVnlSUhICAgLyHRTlQiLBY2s5HlvLeftoKpaOxz1GfFJajusFgPikNByP0/4MIiIiyi+DkmMhBCQ6ErNHjx7BxsYm30ER0bsr8VnOibEh9YiIiPIiT8MqunTpAuDl7BT9+/eHTCZTr1MqlTh//jz8/PyMGyFpSk/H9N0LXz7n7aOpGHKytTRqPSIiorzIU3Isl8sBvOw5trW11bj4zsLCAo0aNcLgwYONGyFpyszEJ2f+BMDbR1Px1MCrNFzllkhIStM57lgCwEVuiQZepQs7NCIiegfkKTleuXIlAMDT0xNjx47lEAoiMjqzEhIEt/fG5+tOQwJoJMjZg7mC23vzYjwiIioQBo05Dg4OZmJMRAWmVXVXLPy4DpzsZBrlLnJLTuNGREQFyqDk+P79++jbty/c3Nxgbm4OMzMzjQcRUX61qu6KPaP91cvL+vrg8PjmTIyJiKhAGTTPcf/+/XHr1i1MmTIFrq6uOmeuICLKr1eHTtT3LMWhFEREVOAMSo4PHz6MQ4cOoXbt2kYOh4iIiIjIdAwaVuHu7g4hdF1HXvAWLFgALy8vWFpaom7dujh06FCOdf/44w8EBgaiTJkysLOzg6+vL3bt2qVRZ9WqVZBIJFqPtDTOoUpERET0rjEoOQ4LC8OECRNw48YNI4eTu02bNmHkyJGYNGkSzpw5gyZNmqB169a4deuWzvoHDx5EYGAgwsPDcerUKQQEBKB9+/Y4c+aMRj07OzvEx8drPCwt39I5VK2s8MGQ5fhgyHLAinMcExERERmTQcMqevbsidTUVFSsWBHW1taQSqUa63XdWtoY5syZg4EDB2LQoEEAXibpu3btwsKFCxEaGqpVPywsTGP5m2++wbZt27Bjxw74+PioyyUSCVxcXAokZqMrUQJ35M7q50RERERkPAYlx68nnYUhIyMDp06dwoQJEzTKg4KCEB0drdc+VCoVnj17htKlNW8e8Pz5c3h4eECpVKJ27dqYMWOGRvL8uvT0dKSnp6uXk5OTAQAKhQIKhULfUzKIQpGp8bygj0cvsd1Ng+1uGmx300hL/6+dY649RLOqzrwItRDw9W4apmh3fY9hUHLcr18/QzbLl4cPH0KpVMLZ2Vmj3NnZGQkJCXrt44cffkBKSgp69OihLqtatSpWrVqFGjVqIDk5GXPnzkXjxo1x7tw5VKpUSed+QkNDERISolW+e/duWFtb5+Gs8k6RpsDE/RsAAAd8XkBqKX3DFmQM6Uog++2yb98+yDhjYaFgu5sG273wnXskweYbJZB9q5shG87D3kKgi6cKtRxMc43Pu4Kvd9MwRbunpqbqVU8i8nBlXXYP6ZvY2dnpu0u93bt3D2XLlkV0dDR8fX3V5TNnzsTatWtx5cqVXLffsGEDBg0ahG3btuHDDz/MsZ5KpUKdOnXQtGlTzJs3T2cdXT3H7u7uePjwYYGc+6tSnyRB7lwGAPDwTjzkTg4Fejx6KTUjE7Vm7AMAnJzQFHKbt3RMejHDdjcNtnvh2vX3fXy58ZzW7dKz+4zn96qFlu87v74ZGQlf76ZhinZPTk6Go6MjkpKScs3X8tRzbG9vn+ucxkIISCQSKJXKvOxWL46OjjAzM9PqJU5MTNTqTX7dpk2bMHDgQPz222+5JsYAUKJECdSvXx9Xr17NsY5MJoNMJtMql0qlWuOvje3V/Uul5gV+PHpJKv573bPdCw/b3TTY7oVHqRKY+VesVmIMvLx1ugTAzL9i0bpmWQ6xKCB8vZuGKdpd32PkKTnev3+/QcEYg4WFBerWrYvIyEh07txZXR4ZGYmOHTvmuN2GDRvw6aefYsOGDWjbtu0bjyOEwNmzZ1GjRg2jxE3Fg1L1339dJ248QUA1S/5HRUT5djzuMeKTcp46VACIT0rD8bjH8K3IXwqJCkOekmN/f/83VypAo0ePRt++fVGvXj34+vpiyZIluHXrFoYMGQIAmDhxIu7evYs1a9YAeJkYf/LJJ5g7dy4aNWqk7nW2srKCXC4HAISEhKBRo0aoVKkSkpOTMW/ePJw9exY///yzaU6S3joRF+MRvP1v9fKgtWfgKr+M4PbevJUxEeVL4jP95tTXtx4R5Z9BF+SZSs+ePfHo0SNMnz4d8fHxqF69OsLDw+Hh4QEAiI+P15jzePHixcjMzMSwYcMwbNgwdXm/fv2watUqAMDTp0/x2WefISEhAXK5HD4+Pjh48CAaNGhQqOdGb6eIi/H4fN1prZ88E5LS8Pm601j4cR0myERkMCdb/cZZ6luPiPKvSCXHADB06FAMHTpU57rshDfbgQMH3ri/H3/8ET/++KMRIqPiRqkSCNlxKdexgCE7LiHQ24VDLIjIIA28SsNVbomEpDSdnzUSAC5ySzTwKq1jLREVBN5FgigHeRkLSERkCLMSEgS39wbw3+wU2bKXg9t78ws4USHSOzk+f/48VCpVQcZC+rCyQuCnPyPw0595++gCxrGARFQYWlV3xcKP68DJTnMWJBe5JYduEZmA3smxj48PHj58CACoUKECHj16VGBBUS5KlMDVMh64WsaDt48uYBwLSESFpVV1V+wZ/d9F78v6+uDw+OZMjIlMQO/syt7eHnFxcQCAGzdusBeZir3ssYA5/ZgpAeDKsYBEZCSvDp2o71mKQymITETvC/K6du0Kf39/uLq6QiKRoF69ejAz032vv3///ddoAdJrMjIw8vD6rOcfADYcWlFQsscCfr7uNCSAxsUyHAtIRERUPOmdHC9ZsgRdunTBtWvXMHz4cAwePBi2trYFGRvpolBg5JENAIAkhe7bW5PxZI8FDN7+N+4n/3fLcBe5Jec5JiIiKobyNJVbq1atAACnTp3CiBEjmBzTO6FVdVc0fs8RNabtBvByLGBANVf2GBMRERVDBs1zvHLlSvXzO3fuQCKRoGzZskYLiuhtw7GARERE7waDpjtQqVSYPn065HI5PDw8UL58edjb22PGjBm8UI+IiIiIiiyDeo4nTZqE5cuX49tvv0Xjxo0hhMCRI0cwbdo0pKWlYebMmcaOk4iIiIiowBmUHK9evRrLli1Dhw4d1GW1atVC2bJlMXToUCbHRERERFQkGTSs4vHjx6hatapWedWqVfH4MW+lS0RERERFk0HJca1atfDTTz9plf/000+oVatWvoOiXFhaosMnc9DhkzmAJe/MRkRElB9K1X+z2J+48URjmd5NBg2rmD17Ntq2bYs9e/bA19cXEokE0dHRuH37NsLDw40dI73KzAznXSurnxMREZFhIi7GI3j73+rlQWvPwFV+mfPYv+MM6jn29/fHP//8g86dO+Pp06d4/PgxunTpgtjYWDRp0sTYMRIRUSFiTxq9CyIuxuPzdac1bvAEAAlJafh83WlEXIw3UWRkagb1HAOAm5sbL7wzhYwMfHZsc9Zz3j6aiIyLPWn0LlCqBEJ2XIKur30CgARAyI5LCPR24bz27yCDeo7JhBQKfHVgJb46sBJQKEwdDREVI+xJo3fF8bjHiE9Ky3G9ABCflIbjcZxk4F3E5JiIiN7Ykwa87EnjEAsqDhKf5ZwYG1KPihcmx0RExJ40eqc42eo325O+9ah4YXJMRETsSaN3SgOv0nCVWyKn0cQSAK5ySzTwKl2YYdFbwqDkuHnz5nj69KlWeXJyMpo3b57fmIiIqJCxJ43eJWYlJAhu7w0AWgly9nJwe29ejPeOMig5PnDgADIyMrTK09LScOjQoXwHRUREhYs9afSuaVXdFQs/rgMnO5lGuYvcEgs/rsPZWd5heZrK7fz58+rnly5dQkJCgnpZqVQiIiICZcuWNV50RERUKLJ70j5fdxoSQOPCPPakUXHVqrorGr/niBrTdgMAlvX1QUA1V77O33F5So5r164NiUQCiUSic/iElZUV5s+fb7TgSAdLS/T66BsAwGLePpqIjCi7Jy14+98a07m5yC05zzEVW68mwvU9SzExprwlx3FxcRBCoEKFCjh+/DjKlCmjXmdhYQEnJyeY8ZbGBcvMDEfL11Q/JyIyJvakEdG7Lk/JsYeHBwBApVIVSDBERGR67EkjoneZQbePDg0NhbOzMz799FON8hUrVuDBgwcYP368UYIjHRQK9D29M+t5EwC8fTQRERGRsRg0W8XixYtRtWpVrfL3338fixYtyndQlIuMDMyIXIQZkYsAHTOGEBEREZHhDEqOExIS4OqqfWFGmTJlEB8fn++giIiIiIhMwaDk2N3dHUeOHNEqP3LkCNzc3PIdFBERERGRKRg05njQoEEYOXIkFAqFekq3vXv3Yty4cRgzZoxRAyQiIiIiKiwGJcfjxo3D48ePMXToUPWd8iwtLTF+/HhMnDjRqAESERERERUWg5JjiUSCWbNmYcqUKbh8+TKsrKxQqVIlyGSyN29MRERERPSWMmjMcbaEhAQ8fvwYFStWhEwmgxDizRsREREREb2lDOo5fvToEXr06IH9+/dDIpHg6tWrqFChAgYNGgR7e3v88MMPxo6TsslkGNAtGAAQxp56IiIiIqMyqOd41KhRkEqluHXrFqytrdXlPXv2REREhNGCIx3MzbG/Yn3sr1gfMDfouw0RERER5cCg7Gr37t3YtWsXypUrp1FeqVIl3Lx50yiBEREREREVNoOS45SUFI0e42wPHz7kRXkFTaFAtwt7sp7z9tFERERExmTQsIqmTZtizZo16mWJRAKVSoXvvvsOAQEBRguOdMjIwPfhYfg+PIy3jyYiIiIyMoN6jr/77js0a9YMJ0+eREZGBsaNG4e///4bjx8/1nnnPCIiIiKiosCgnmNvb2+cP38eDRo0QGBgIFJSUtClSxecOXMGFStWNHaMGhYsWAAvLy9YWlqibt26OHToUK71o6KiULduXVhaWqJChQpYtGiRVp3NmzfD29sbMpkM3t7e2LJlS0GFT0RERERvMb2T4y5duiA5ORkAsGbNGpQqVQohISHYuXMnwsPD8fXXX8PV1bXAAgWATZs2YeTIkZg0aRLOnDmDJk2aoHXr1rh165bO+nFxcWjTpg2aNGmCM2fO4KuvvsLw4cOxefNmdZ2YmBj07NkTffv2xblz59C3b1/06NEDx44dK9BzISIiIqK3j97J8c6dO5GSkgIAGDBgAJKSkgosqJzMmTMHAwcOxKBBg1CtWjWEhYXB3d0dCxcu1Fl/0aJFKF++PMLCwlCtWjUMGjQIn376Kb7//nt1nbCwMAQGBmLixImoWrUqJk6ciBYtWiAsLKyQzoqIiIiI3hZ6jznOThwDAgIghMCvv/4KOzs7nXU/+eQTowWYLSMjA6dOncKECRM0yoOCghAdHa1zm5iYGAQFBWmUtWzZEsuXL4dCoYBUKkVMTAxGjRqlVSe35Dg9PR3p6enq5ewedYVCAYVCkZfTyrNX969QZBb48eglhSJT4znbvXCw3U2D7W4abHfTYLubhinaXd9j6J0cL1y4EGPGjMGff/4JiUSCyZMnQyKRaNWTSCQFkhw/fPgQSqUSzs7OGuXOzs5ISEjQuU1CQoLO+pmZmXj48CFcXV1zrJPTPgEgNDQUISEhWuW7d+/WOcWdMWWmpKFr1vOoqCiY21gW6PHopXQlkP122bdvH2RmJg3nncF2Nw22u2mw3U2D7W4apmj31NRUverpnRw3btwYR48eBQCUKFEC//zzD5ycnAyLLh9eT8iFEDqT9Nzqv16e131OnDgRo0ePVi8nJyfD3d0dQUFBOfamG0tqahqGdnzZez49KAhyeckCPR69lJqRiXHH9wEAmjdvDjm/lBQKtrtpsN1Ng+1uGmx30zBFu2f/0v8meifHXbp0wapVq2BnZ4eVK1fC1tbW4OAM4ejoCDMzM60e3cTERK2e32wuLi4665ubm8PBwSHXOjntEwBkMpnOm51IpVJIpVK9zsdQUisJwqt+AAAItbIs8OPRS1Lx35clqdSc7V5I2O6mwXY3Dba7abDdTcMU7a7vMQy6IO/TTz/Fs2fPDIvMQBYWFqhbty4iIyM1yiMjI+Hn56dzG19fX636u3fvRr169dQNlFOdnPZJRERERMVXkbkgDwBGjx6Nvn37ol69evD19cWSJUtw69YtDBkyBMDL4Q53795V371vyJAh+OmnnzB69GgMHjwYMTExWL58OTZs2KDe54gRI9C0aVPMmjULHTt2xLZt27Bnzx4cPny4QM4h3zIz0eZKVmyZTU0bCxEREVExo3dyvGjRIowePdpkF+QBQM+ePfHo0SNMnz4d8fHxqF69OsLDw+Hh4QEAiI+P15jz2MvLC+Hh4Rg1ahR+/vlnuLm5Yd68eejatau6jp+fHzZu3IjJkydjypQpqFixIjZt2oSGDRsWyDnkW3o6Fmz7FgCQlD4GQOEObyEiIiIqzvROjv38/N6KC/KGDh2KoUOH6ly3atUqrTJ/f3+cPn06131269YN3bp1M0Z4RERERFSEGXT76Li4OJQpU8bYsRARERERmVSekuPZs2fjxYsX8PDwgEQiwcGDBzVuhvHs2bMce3WJiIiIiN52eUqOJ06cqDFLRbt27XD37l31cmpqKhYvXmy86IiIiIiIClGekuPsG2jktExEREREVJQZNOaYiIiIiKg40nu2CnpLWFhgbJuRAIApFhamjYWIiIiomMlzcrxs2TKULFkSAJCZmYlVq1bB0dERAAr9rnnvJKkUv9f4EAAwhbe4JCIiIjKqPCXH5cuXx9KlS9XLLi4uWLt2rVYdIiIiIqKiKE/J8Y0bNwooDNJbZiYCrp/Ies7bRxMREREZE8ccFzXp6Vj5ewgAIOnnL8DbRxMREREZj96zVWzcuFHvnd6+fRtHjhwxKCAiIiIiIlPROzleuHAhqlatilmzZuHy5cta65OSkhAeHo7evXujbt26ePz4sVEDJSIiIiIqaHoPq4iKisLOnTsxf/58fPXVV7CxsYGzszMsLS3x5MkTJCQkoEyZMhgwYAAuXrwIJyengoybiIiIiMjo8jTmuF27dmjXrh0ePXqEw4cP48aNG3jx4gUcHR3h4+MDHx8flCjB+4oQERERUdFk0AV5Dg4O6Nixo7FjISIiIiIyKXbzFjHWFuY6nxMRERFR/jG7KmosLKCcOxd///03qvH20URERERGxZ7jokYqherzzxHXpg3A20cTERERGRWTYyIiIiKiLAYlx9OnT0dqaqpW+YsXLzB9+vR8B0W5UCohiYqCw4ULgFJp6miIiIiIihWDkuOQkBA8f/5cqzw1NRUhISH5DopykZYG88BAfDBlCpCWZupoiIiIiIoVg5JjIQQkEolW+blz51C6dOl8B0VEREREZAp5mq2iVKlSkEgkkEgkqFy5skaCrFQq8fz5cwwZMsToQRIRERERFYY8JcdhYWEQQuDTTz9FSEgI5HK5ep2FhQU8PT3h6+tr9CCJiIiIiApDnpLjfv36AQC8vLzg5+cHKacSIyIiIqJixKCbgPj7+0OlUuGff/5BYmIiVCqVxvqmTZsaJTgiIiIiosJkUHJ89OhR9O7dGzdv3oQQQmOdRCKBklOMEREREVERZFByPGTIENSrVw9//vknXF1ddc5cQQVEKoUyNBRXrlxBZQ5rISIiIjIqg5Ljq1ev4vfff8d7771n7HjoTSwsoBozBtfCw1HZwsLU0RAREREVKwbNc9ywYUNcu3bN2LEQEREREZmUQT3HX375JcaMGYOEhATUqFFDa9aKmjVrGiU40kGphOTkSdhfvfry9tEcWkFERERkNAYlx127dgUAfPrpp+oyiUSivnMeL8grQGlpMPfzgz8AxaBBgKWlqSMiIiIiKjYMSo7j4uKMHQcRERERvSOUqv9mOztx4wkCqlnCrMTbMcGDQcmxh4eHseMgIiIiondAxMV4BG//W708aO0ZuMovI7i9N1pVdzVhZC8ZdEEeAKxduxaNGzeGm5sbbt68CeDl7aW3bdtmtOCIiIiIqPiIuBiPz9edxv3kdI3yhKQ0fL7uNCIuxpsosv8YlBwvXLgQo0ePRps2bfD06VP1GGN7e3uEhYUZMz4iIiIiKgaUKoGQHZcgdKzLLgvZcUljyIUpGJQcz58/H0uXLsWkSZNgZmamLq9Xrx4uXLhgtOCIiIiIqHg4HvcY8UlpOa4XAOKT0nA87nHhBaWDQclxXFwcfHx8tMplMhlSUlLyHRQRERERFS+Jz3JOjA2pV1AMSo69vLxw9uxZrfK//voL3t7e+Y2JciOVQjl5Mq707Mk5jomIiKjIcLLVb/pZfesVFINmq/jf//6HYcOGIS0tDUIIHD9+HBs2bEBoaCiWLVtm7BjpVRYWUE2ditjwcFTk7aOJiIioiGjgVRquckskJKXpHHcsAeAit0QDr9KFHZoGg3qOBwwYgODgYIwbNw6pqano3bs3Fi1ahLlz56JXr17GjhEA8OTJE/Tt2xdyuRxyuRx9+/bF06dPc6yvUCgwfvx41KhRAzY2NnBzc8Mnn3yCe/fuadRr1qwZJBKJxqOgzoGIiIjoXWVWQoLg9i9HGLw+o3H2cnB7b5PPd2zwVG6DBw/GzZs3kZiYiISEBNy+fRsDBw40ZmwaevfujbNnzyIiIgIRERE4e/Ys+vbtm2P91NRUnD59GlOmTMHp06fxxx9/4J9//kGHDh10nkt8fLz6sXjx4gI7j3xTqYC//4btrVsvnxMREREVEa2qu2Lhx3XgZCfTKHeRW2Lhx3XeinmODRpW8SpHR0djxJGry5cvIyIiAkePHkXDhg0BAEuXLoWvry9iY2NRpUoVrW3kcjkiIyM1yubPn48GDRrg1q1bKF++vLrc2toaLi4uBXsSxvLiBaQ+PmgOQNG3LyCTvXETIiIiordFq+quaPyeI2pM2w0AWNbXBwHVXE3eY5xN7+S4Tp062Lt3L0qVKgUfHx9IJDmfwOnTp40SXLaYmBjI5XJ1YgwAjRo1glwuR3R0tM7kWJekpCRIJBLY29trlK9fvx7r1q2Ds7MzWrdujeDgYNja2ua4n/T0dKSn/zd5dXJyMoCXQzkUCkUezswACgWk6qcKoKCPRwAAhSJT43mB/50JANvdVNjupsF2Nw22u2molP+1e+2ytlApM6FSFuwx9f3b6p0cd+zYEbKsXspOnToZFJShEhIS4OTkpFXu5OSEhIQEvfaRlpaGCRMmoHfv3rCzs1OX9+nTB15eXnBxccHFixcxceJEnDt3TqvX+VWhoaEICQnRKt+9ezesra31isdQZmlpaJf1fN++fVBamvaKzndFuhLIfrvs27cPMrNcq5ORsN1Ng+1uGmx302C7m4Yp2j01NVWvehIhhMluQzJt2jSdSearTpw4gd27d2P16tWIjY3VWFepUiUMHDgQEyZMyHUfCoUC3bt3x61bt3DgwAGN5Ph1p06dQr169XDq1CnUqVNHZx1dPcfu7u54+PBhrvs2ipQUSEuVAgCkJiZC+lovOBWM1IxM1JqxDwBwckJTyG34paQwsN1Ng+1uGmx302C7m4Yp2j05ORmOjo5ISkrKNV8zaMzxiRMnoFKpNIY5AMCxY8dgZmaGevXq6bWfL7744o0zQ3h6euL8+fO4f/++1roHDx7A2dk51+0VCgV69OiBuLg47Nu3743Ja506dSCVSnH16tUck2OZTKbuRX+VVCqFtKDnHn5l/4VyPAIASMV/w4ikUnO2eyFhu5sG29002O6mwXY3DVO0u77HMCg5HjZsGMaNG6eVHN+9exezZs3CsWPH9NqPo6OjXhf0+fr6IikpCcePH0eDBg0AvEzEk5KS4Ofnl+N22Ynx1atXsX//fjg4OLzxWH///TcUCgVcXU1/tSQRERERFS6DpnK7dOmSzl5VHx8fXLp0Kd9Bva5atWpo1aoVBg8ejKNHj+Lo0aMYPHgw2rVrp3ExXtWqVbFlyxYAQGZmJrp164aTJ09i/fr1UCqVSEhIQEJCAjIyMgAA169fx/Tp03Hy5EncuHED4eHh6N69O3x8fNC4cWOjnwcRERERvd0MSo5lMpnOYQ7x8fEwN8/37HA6rV+/HjVq1EBQUBCCgoJQs2ZNrF27VqNObGwskpKSAAB37tzB9u3bcefOHdSuXRuurq7qR3R0NADAwsICe/fuRcuWLVGlShUMHz4cQUFB2LNnD8zM3tIR+VIplKNH42qnTrx9NBEREZGRGZTJBgYGYuLEidi2bRvkcjkA4OnTp/jqq68QGBho1ACzlS5dGuvWrcu1zqvXFnp6euJN1xq6u7sjKirKKPEVGgsLqL79FpfCw+HJ20cTERERGZVByfEPP/yApk2bwsPDAz4+PgCAs2fPwtnZWas3l4jIUNYW5rg6Iwjh4eGwtiiYX6WIiIheZdD/NmXLlsX58+exfv16nDt3DlZWVhgwYAA++ugjXuVZ0FQq4MYNWN2/z9tHExERERmZwV0xNjY2+Oyzz4wZC+njxQtIK1dGEABFjx68fTQRERGREemdHG/fvh2tW7eGVCrF9u3bc63boUOHfAdGRERERFTY9E6OO3XqpL6Nc263j5ZIJFAqC/jm2EREREREBUDv5Fj1yvhWFce6EhEREVExpPc8x6VLl8bDhw8BAJ9++imePXtWYEEREREREZmC3slxRkYGkpOTAQCrV69GWlpagQVFRERERGQKeg+r8PX1RadOnVC3bl0IITB8+HBYWVnprLtixQqjBUhEREREVFj0To7XrVuHH3/8EdevXwcAJCUlsffYFMzNoRwyBLdu3kS5ArpVNxEREdG7Su/sytnZGd9++y0AwMvLC2vXroWDg0OBBUY5kMmgmjcP58PDUY5zHBMREREZlUEX5AUEBMDCwqLAgiIiIiIiMgVekFfUCAE8eACLpKSXz4mIiIjIaHhBXlGTmgpp2bJoDUDRoQPAHnwiIiIiozHogjyJRMIL8oiIiIio2OEFeUREREREWfQec/yquLg4dWLM3mN6F1hbmOPqjCDM9c2EtQWn0CMi4+PnDNHbwaDkWKVSYcaMGShbtixKliyJf//9FwAwZcoULF++3KgBEhEREREVFoOS46+//hqrVq3C7NmzNaZ0q1GjBpYtW2a04IiIiIiICpNByfGaNWuwZMkS9OnTB2ZmZurymjVr4sqVK0YLjoiIiIioMBk0qOnu3bt47733tMpVKhUUCkW+g6JcmJtD1bcv7ty5A1fePpqIiIjIqAzqOX7//fdx6NAhrfLffvsNPj4++Q6KciGTQbl8Oc6MGAHw9tFERERERmVQ12NwcDD69u2Lu3fvQqVS4Y8//kBsbCzWrFmDnTt3GjtGIiIiIqJCYVDPcfv27bFp0yaEh4dDIpFg6tSpuHz5Mnbs2IHAwEBjx0ivEgJISYFZWhpvH01ERERkZAYPWm3ZsiVatmxpzFhIH6mpkJYqhXYAFE+e8PbRREREREaUryu6Tp06hcuXL0MikcDb25vjjYmIiIioSDMoOU5MTESvXr1w4MAB2NvbQwiBpKQkBAQEYOPGjShTpoyx4yQiIiIiKnAGjTn+8ssvkZycjL///huPHz/GkydPcPHiRSQnJ2P48OHGjpGIiIiIqFAY1HMcERGBPXv2oFq1auoyb29v/PzzzwgKCjJacEREREREhcmgnmOVSgWpVKpVLpVKoVKp8h0UEREREZEpGJQcN2/eHCNGjMC9e/fUZXfv3sWoUaPQokULowVHRERERFSYDEqOf/rpJzx79gyenp6oWLEi3nvvPXh5eeHZs2eYP3++sWOkV5mZQdWlC+76+QFmZqaOhoiIiKhYMWjMsbu7O06fPo3IyEhcuXIFQgh4e3vjww8/NHZ89DpLSyg3bsTJ8HC0sbQ0dTRERERExUq+5jkODAzkHfGIiIiIqNjI07CKffv2wdvbG8nJyVrrkpKS8P777+PQoUNGC46IiIiIqDDlKTkOCwvD4MGDYWdnp7VOLpfj//7v/zBnzhyjBUc6pKRAamGBjp06ASkppo6GiIohawtzXJ0RhLm+mbC2yNcPjERERU6ekuNz586hVatWOa4PCgrCqVOn8h0UERERUWHgl0F6XZ6S4/v37+uc3zibubk5Hjx4kO+giIiIiIhMIU/JcdmyZXHhwoUc158/fx6urq75DoqIiIiIyBTylBy3adMGU6dORVpamta6Fy9eIDg4GO3atTNacEREREREhSlPyfHkyZPx+PFjVK5cGbNnz8a2bduwfft2zJo1C1WqVMHjx48xadKkAgn0yZMn6Nu3L+RyOeRyOfr27YunT5/muk3//v0hkUg0Ho0aNdKok56eji+//BKOjo6wsbFBhw4dcOfOnQI5ByIiIiJ6u+Vp5LmzszOio6Px+eefY+LEiRBCAAAkEglatmyJBQsWwNnZuUAC7d27N+7cuYOIiAgAwGeffYa+fftix44duW7XqlUrrFy5Ur1sYWGhsX7kyJHYsWMHNm7cCAcHB4wZMwbt2rXDqVOnYMY70BERERG9U/J8WaaHhwfCw8Px5MkTXLt2DUIIVKpUCaVKlSqI+AAAly9fRkREBI4ePYqGDRsCAJYuXQpfX1/ExsaiSpUqOW4rk8ng4uKic11SUhKWL1+OtWvXqu/ut27dOri7u2PPnj1o2bKl8U8mv8zMoGrdGomJiXBg8k5ERERkVAbPWVKqVCnUr1/fmLHkKCYmBnK5XJ0YA0CjRo0gl8sRHR2da3J84MABODk5wd7eHv7+/pg5cyacnJwAAKdOnYJCoUBQUJC6vpubG6pXr47o6Ogck+P09HSkp6erl7NviqJQKKBQKPJ1rm9kZgbF77/jWGQkAs3MgII+Hqll/20L/G9MGtjupsF2Nw22u2mw3QufQpGp8bww2l7fYxSJCf0SEhLUCe2rnJyckJCQkON2rVu3Rvfu3eHh4YG4uDhMmTIFzZs3x6lTpyCTyZCQkAALCwutXm9nZ+dc9xsaGoqQkBCt8t27d8Pa2joPZ5Y/kZGRhXYs+g/b3TTY7qbBdjcNtrtpsN0LT7oSyE5D9+3bB1kh/BiempqqVz2TJsfTpk3TmWS+6sSJEwBejmt+nRBCZ3m2nj17qp9Xr14d9erVg4eHB/7880906dIlx+3etN+JEydi9OjR6uXk5GS4u7sjKChI590DjU2hUCAyMhKBgYG5zjtNxsV2Nw22u2mw3U2D7W4abPfCl5qRiXHH9wEAmjdvDrmNZYEfM/uX/jcxaXL8xRdfoFevXrnW8fT0xPnz53H//n2tdQ8ePMjTBYCurq7w8PDA1atXAQAuLi7IyMjAkydPNHqPExMT4efnl+N+ZDIZZDKZVrlUKi34N1VKCsydnNBWqYRISIC0EHuq6aVC+TuTFra7abDdTYPtbhps98IjFf91Qkql5oXS7voew6TJsaOjIxwdHd9Yz9fXF0lJSTh+/DgaNGgAADh27BiSkpJyTWJf9+jRI9y+fVt9o5K6detCKpUiMjISPXr0AADEx8fj4sWLmD17tgFnVDgkqakwB8CRUURERETGlad5jk2lWrVqaNWqFQYPHoyjR4/i6NGjGDx4MNq1a6dxMV7VqlWxZcsWAMDz588xduxYxMTE4MaNGzhw4ADat28PR0dHdO7cGQAgl8sxcOBAjBkzBnv37sWZM2fw8ccfo0aNGurZK4iIiIjo3VEkLsgDgPXr12P48OHqmSU6dOiAn376SaNObGwskpKSAABmZma4cOEC1qxZg6dPn8LV1RUBAQHYtGkTbG1t1dv8+OOPMDc3R48ePfDixQu0aNECq1at4hzHRERERO+gIpMcly5dGuvWrcu1TvZNSQDAysoKu3bteuN+LS0tMX/+fMyfPz/fMRIRERFR0VYkhlUQERERERUGJsdERERERFmKzLAKylKiBFRNm+Lxo0eQl+B3GyIiIiJjYnJc1FhZQblnD46Eh6ONlZWpoyEiIiIqVtj1SERERESUhckxEREREVEWDqsoalJSYO7piVYZGcDNm4C9vakjIiIiIio2mBwXQZKHDyEDbx9NREREZGwcVkFERERElIXJMRERERFRFibHRERERERZmBwTEREREWVhckxERERElIWzVRQ1JUpAVbcukpKSUJK3jyYiIiIyKibHRY2VFZQxMTjI20cTERERGR27HomIiIiIsjA5JiIiIiLKwmEVRU1qKsy9vRGYmgpcvQrI5aaOiIiIiKjYYHJc1AgByc2bsAagEMLU0RAREREVKxxWQURERESUhckxEREREVEWJsdERERERFmYHBMRERERZWFyTERERESUhbNVFDUSCUS1anj2/DmsJBJTR0NERERUrLDnuKixtkbmuXPYP38+YG1t6miIiIiIihUmx0REREREWZgcExERERFl4ZjjoiY1Feb16iHg+XOgWTPePpqIiIjIiJgcFzVCQHL5MuzA20cTERERGRuHVRARERERZWFyTERERESUhckxEREREVEWJsdERERERFmYHBMRERERZeFsFUWNRALh4YEXqamQ8vbRREREREbFnuOixtoamVevInLpUt4+moiIiMjImBwTEREREWVhckxERERElIVjjouaFy9g1qQJmiYlAQEBgFRq6oiIiIiIio0i03P85MkT9O3bF3K5HHK5HH379sXTp09z3UYikeh8fPfdd+o6zZo101rfq1evAj6bfFCpUOLUKZS6dg1QqUwdDREREVGxUmR6jnv37o07d+4gIiICAPDZZ5+hb9++2LFjR47bxMfHayz/9ddfGDhwILp27apRPnjwYEyfPl29bGVlZcTIiYiIiKioKBLJ8eXLlxEREYGjR4+iYcOGAIClS5fC19cXsbGxqFKlis7tXFxcNJa3bduGgIAAVKhQQaPc2tpaqy4RERERvXuKRHIcExMDuVyuTowBoFGjRpDL5YiOjs4xOX7V/fv38eeff2L16tVa69avX49169bB2dkZrVu3RnBwMGxtbXPcV3p6OtLT09XLycnJAACFQgGFQpGXU8s7hQJS9VMFUNDHI7Xsv22B/41JA9vdNNjupsF2Nw22e+FTKDI1nhdG2+t7jCKRHCckJMDJyUmr3MnJCQkJCXrtY/Xq1bC1tUWXLl00yvv06QMvLy+4uLjg4sWLmDhxIs6dO4fIyMgc9xUaGoqQkBCt8t27d8O6gOceNktLQ7us5/v27YPS0rJAj0facnttUMFhu5sG29002O6mwXYvPOlKIDsN3bdvH2RmBX/M1NRUveqZNDmeNm2aziTzVSdOnADw8uK61wkhdJbrsmLFCvTp0weWryWTgwcPVj+vXr06KlWqhHr16uH06dOoU6eOzn1NnDgRo0ePVi8nJyfD3d0dQUFBsLOz0yseg6WkqJ82b94cUnv7gj0eqSkUCkRGRiIwMBBSzhJSaNjupsF2Nw22u2mw3QtfakYmxh3fB+BlPiO3KfjOvuxf+t/EpMnxF1988caZITw9PXH+/Hncv39fa92DBw/g7Oz8xuMcOnQIsbGx2LRp0xvr1qlTB1KpFFevXs0xOZbJZJDJZFrlUqm04N9UUimEoyMyMjIK53ikhe1uGmx302C7mwbb3TTY7oVHKv7r3JRKzQul3fU9hkmTY0dHRzg6Or6xnq+vL5KSknD8+HE0aNAAAHDs2DEkJSXBz8/vjdsvX74cdevWRa1atd5Y9++//4ZCoYCrq+ubT8AUbGyQee8eIsLD0cbGxtTREBERERUrRWKe42rVqqFVq1YYPHgwjh49iqNHj2Lw4MFo166dxsV4VatWxZYtWzS2TU5Oxm+//YZBgwZp7ff69euYPn06Tp48iRs3biA8PBzdu3eHj48PGjduXODnRURERERvlyKRHAMvZ5SoUaMGgoKCEBQUhJo1a2Lt2rUadWJjY5GUlKRRtnHjRggh8NFHH2nt08LCAnv37kXLli1RpUoVDB8+HEFBQdizZw/MzAphZDgRERERvVWKxGwVAFC6dGmsW7cu1zpCCK2yzz77DJ999pnO+u7u7oiKijJKfIXmxQuYtWqFxo8e8fbRREREREZWZJJjyqJSocTBg3AEoODto4mIiKgIsrYwx9UZQQgPD4e1xduVjhaZYRVERERERAWNyTERERERURYmx0REREREWZgcExERERFlYXJMRERERJTl7bo8kPQirK2hVCpNHQYRERFRscOe46LGxgaZT5/iz02bAN4+moiIiMiomBwTEREREWVhckxERERElIVjjouatDSYdemChomJQPPmvH00ERERkRExOS5qlEqU+OsvuABQ8KI8IiIiIqPisAoiIiIioixMjomIiIiIsjA5JiIiIiLKwuSYiIiIiCgLk2MiIiIioiycrcIIhBAAgOTk5II/WEqK+qkiORnSEvx+U1gUCgVSU1ORnJwMKafQKzRsd9Ngu5sG29002O6mUdjtnp2nZedtOWFybATPnj0DALi7uxfugT08Cvd4REREREXcs2fPIJfLc1wvEW9Kn+mNVCoV7t27B1tbW0gkkgI/XnJyMtzd3XH79m3Y2dkV+PHoJba7abDdTYPtbhpsd9Ngu5tGYbe7EALPnj2Dm5sbSuTyyzt7jo2gRIkSKFeuXKEf187Ojm9iE2C7mwbb3TTY7qbBdjcNtrtpFGa759ZjnI0DVomIiIiIsjA5JiIiIiLKwuS4CJLJZAgODoZMJjN1KO8UtrtpsN1Ng+1uGmx302C7m8bb2u68II+IiIiIKAt7jomIiIiIsjA5JiIiIiLKwuSYiIiIiCgLk2MiIiIioixMjougBQsWwMvLC5aWlqhbty4OHTpk6pCKtYMHD6J9+/Zwc3ODRCLB1q1bTR3SOyE0NBT169eHra0tnJyc0KlTJ8TGxpo6rGJv4cKFqFmzpnpSfl9fX/z111+mDuudEhoaColEgpEjR5o6lGJt2rRpkEgkGg8XFxdTh/VOuHv3Lj7++GM4ODjA2toatWvXxqlTp0wdlhqT4yJm06ZNGDlyJCZNmoQzZ86gSZMmaN26NW7dumXq0IqtlJQU1KpVCz/99JOpQ3mnREVFYdiwYTh69CgiIyORmZmJoKAgpKSkmDq0Yq1cuXL49ttvcfLkSZw8eRLNmzdHx44d8ffff5s6tHfCiRMnsGTJEtSsWdPUobwT3n//fcTHx6sfFy5cMHVIxd6TJ0/QuHFjSKVS/PXXX7h06RJ++OEH2Nvbmzo0NU7lVsQ0bNgQderUwcKFC9Vl1apVQ6dOnRAaGmrCyN4NEokEW7ZsQadOnUwdyjvnwYMHcHJyQlRUFJo2bWrqcN4ppUuXxnfffYeBAweaOpRi7fnz56hTpw4WLFiAr7/+GrVr10ZYWJipwyq2pk2bhq1bt+Ls2bOmDuWdMmHCBBw5cuSt/tWbPcdFSEZGBk6dOoWgoCCN8qCgIERHR5soKqLCkZSUBOBlokaFQ6lUYuPGjUhJSYGvr6+pwyn2hg0bhrZt2+LDDz80dSjvjKtXr8LNzQ1eXl7o1asX/v33X1OHVOxt374d9erVQ/fu3eHk5AQfHx8sXbrU1GFpYHJchDx8+BBKpRLOzs4a5c7OzkhISDBRVEQFTwiB0aNH44MPPkD16tVNHU6xd+HCBZQsWRIymQxDhgzBli1b4O3tbeqwirWNGzfi9OnT/AWwEDVs2BBr1qzBrl27sHTpUiQkJMDPzw+PHj0ydWjF2r///ouFCxeiUqVK2LVrF4YMGYLhw4djzZo1pg5NzdzUAVDeSSQSjWUhhFYZUXHyxRdf4Pz58zh8+LCpQ3knVKlSBWfPnsXTp0+xefNm9OvXD1FRUUyQC8jt27cxYsQI7N69G5aWlqYO553RunVr9fMaNWrA19cXFStWxOrVqzF69GgTRla8qVQq1KtXD9988w0AwMfHB3///TcWLlyITz75xMTRvcSe4yLE0dERZmZmWr3EiYmJWr3JRMXFl19+ie3bt2P//v0oV66cqcN5J1hYWOC9995DvXr1EBoailq1amHu3LmmDqvYOnXqFBITE1G3bl2Ym5vD3NwcUVFRmDdvHszNzaFUKk0d4jvBxsYGNWrUwNWrV00dSrHm6uqq9UW7WrVqb9XEAkyOixALCwvUrVsXkZGRGuWRkZHw8/MzUVREBUMIgS+++AJ//PEH9u3bBy8vL1OH9M4SQiA9Pd3UYRRbLVq0wIULF3D27Fn1o169eujTpw/Onj0LMzMzU4f4TkhPT8fly5fh6upq6lCKtcaNG2tNy/nPP//Aw8PDRBFp47CKImb06NHo27cv6tWrB19fXyxZsgS3bt3CkCFDTB1asfX8+XNcu3ZNvRwXF4ezZ8+idOnSKF++vAkjK96GDRuGX375Bdu2bYOtra36FxO5XA4rKysTR1d8ffXVV2jdujXc3d3x7NkzbNy4EQcOHEBERISpQyu2bG1ttcbS29jYwMHBgWPsC9DYsWPRvn17lC9fHomJifj666+RnJyMfv36mTq0Ym3UqFHw8/PDN998gx49euD48eNYsmQJlixZYurQ/iOoyPn555+Fh4eHsLCwEHXq1BFRUVGmDqlY279/vwCg9ejXr5+pQyvWdLU5ALFy5UpTh1asffrpp+rPlzJlyogWLVqI3bt3mzqsd46/v78YMWKEqcMo1nr27ClcXV2FVCoVbm5uokuXLuLvv/82dVjvhB07dojq1asLmUwmqlatKpYsWWLqkDRwnmMiIiIioiwcc0xERERElIXJMRERERFRFibHRERERERZmBwTEREREWVhckxERERElIXJMRERERFRFibHRERERERZmBwTEREREWVhckxERYKnpyfCwsJMHYaG/v37o1OnTm/NfujtFBsbCxcXFzx79szUoQAA0tPTUb58eZw6dcrUoRC9lZgcE5HeFi1aBFtbW2RmZqrLnj9/DqlUiiZNmmjUPXToECQSCf7555/CDvONbty4AYlEon7I5XI0atQIO3bsKJTjnj17VqN87ty5WLVqVYEe2xgJ+LRp0zTaTdfjxo0bRon3dRKJBFu3bi2QfRuiWbNmGDlypF51J02ahGHDhsHW1hYAcODAAUgkEjx9+rTgAsyFTCbD2LFjMX78eJMcn+htx+SYiPQWEBCA58+f4+TJk+qyQ4cOwcXFBSdOnEBqaqq6/MCBA3Bzc0PlypVNEape9uzZg/j4eBw7dgwNGjRA165dcfHixUKPQy6Xw97evtCPm1djx45FfHy8+lGuXDlMnz5do8zd3V1dPyMjw4TRvh3u3LmD7du3Y8CAAaYORUOfPn1w6NAhXL582dShEL11mBwTkd6qVKkCNzc3HDhwQF124MABdOzYERUrVkR0dLRGeUBAAICXSdK4ceNQtmxZ2NjYoGHDhhr7AIDo6Gg0bdoUVlZWcHd3x/Dhw5GSkpJjLCtXroRcLkdkZCQA4Pfff0eNGjVgZWUFBwcHfPjhh7luDwAODg5wcXFB1apVMXPmTCgUCuzfv1+9/u7du+jZsydKlSoFBwcHdOzYMdee0YiICHzwwQewt7eHg4MD2rVrh+vXr6vXe3l5AQB8fHwgkUjQrFkzAJq9uosXL0bZsmWhUqk09t2hQwf069dPvbxjxw7UrVsXlpaWqFChAkJCQjR69F81bdo0rF69Gtu2bVP38Ga3/4ULF9C8eXN1u3322Wd4/vy5zv2ULFkSLi4u6oeZmRlsbW3VyxMmTEDXrl0RGhqq8cXoTe144sQJBAYGwtHREXK5HP7+/jh9+rR6vaenJwCgc+fOkEgk6uVp06ahdu3aWLFiBcqXL4+SJUvi888/h1KpxOzZs+Hi4gInJyfMnDlT4zySkpLw2WefwcnJCXZ2dmjevDnOnTun0V61a9fG2rVr4enpCblcjl69eqmHRfTv3x9RUVGYO3fuG3vMf/31V9SqVQvlypXTuV6XN7UHAFy5cgUffPABLC0t4e3tjT179mj0rmdkZOCLL76Aq6srLC0t4enpidDQUPX2Dg4O8PPzw4YNG/SOi+hdweSYiPKkWbNmGgnk/v370axZM/j7+6vLMzIyEBMTo06OBwwYgCNHjmDjxo04f/48unfvjlatWuHq1asAXiZoLVu2RJcuXXD+/Hls2rQJhw8fxhdffKEzhu+//x5jx47Frl27EBgYiPj4eHz00Uf49NNPcfnyZRw4cABdunSBEEKvc1IoFFi6dCkAQCqVAgBSU1MREBCAkiVL4uDBgzh8+DBKliyJVq1a5dgjmpKSgtGjR+PEiRPYu3cvSpQogc6dO6sT3ePHjwP4r8f6jz/+0NpH9+7d8fDhQ402fvLkCXbt2oU+ffoAAHbt2oWPP/4Yw4cPx6VLl7B48WKsWrVKKwnMNnbsWPTo0QOtWrVS9/D6+fkhNTUVrVq1QqlSpXDixAn89ttv2LNnT47tro+9e/fi8uXLiIyMxM6dO/Vqx2fPnqFfv344dOgQjh49ikqVKqFNmzbqZPTEiRMAXn4hio+PVy8DwPXr1/HXX38hIiICGzZswIoVK9C2bVvcuXMHUVFRmDVrFiZPnoyjR48CAIQQaNu2LRISEhAeHo5Tp06hTp06aNGiBR4/fqyx361bt2Lnzp3YuXMnoqKi8O233wJ4OQzG19cXgwcP1tlj/qqDBw+iXr16eWrDN7WHSqVCp06dYG1tjWPHjmHJkiWYNGmSxj7mzZuH7du349dff0VsbCzWrVun/lKRrUGDBjh06FCeYiN6JwgiojxYsmSJsLGxEQrF/7dz9zFNXW8cwL+0YKllZDqE1q3S8e6CduDGhp1pELBmmXNzbi7pELNElmXp2EvM3ObY5qLL/nCMmBCBbMk0JDIWyTQMGNNCKihDHEGlq6spKTNCRRtR3ibtsz+kN1xabLts+WU/n09CYs8995xzn/LH4+E89zaNjo5SZGQkDQ8P0+HDh2n16tVERNTe3k4A6NKlS2S32ykiIoIuX74sGic/P5/ef/99IiIqKiqikpIS0XWLxUISiYQmJiaIiCgxMZHKy8tp586dpFKpqK+vT+jb09NDAGhgYCCkZ3A4HASA5HI5KRQKkkgkBIA0Gg1du3aNiIi+/vprSk9PJ6/XK9w3NTVFcrmcWlpaiIiouLiYNm7cOO88LpeLANC5c+dE8/7666+ifnPHefbZZ+nVV18VPldVVZFSqaTp6WkiIlqzZg3t3btXNMahQ4dIpVLNu5ZAa62urqZFixbRrVu3hLbGxkaSSCQ0NDQ071g+vu9k9hwJCQk0NTUltIUSx7mmp6fpvvvuo2PHjgltAKihoUHU7+OPP6aFCxfS6Oio0GYwGEij0ZDH4xHa0tPT6fPPPyciouPHj1NsbCxNTk6KxkpOTqaqqqp5x92xYwc98cQTwme9Xk+lpaXzhUag1Wpp9+7dojaz2UwAyO12B72fyD8eTU1NFBkZSVeuXBH6tLa2imJkMplo7dq1orjPVVFRQRqNJqQ1MHYvifyfZeWMsf+kvLw8jI2Nobu7G263G2lpaYiPj4der0dRURHGxsbQ1taGZcuWISkpCfX19SAiv7PHU1NTeOCBBwAAPT09sNvtqK2tFa4TEbxeLxwOB5YvXw4A2LdvH8bGxnDmzBkkJSUJfbVaLfLz87FixQoYDAasW7cOmzdvxqJFi+76LHV1dcjIyMDFixfx1ltv4cCBA1i8eLFoTb4iKp/JyUnRUYnZLl26hI8++ginT5/GyMiIsGPsdDqRmZkZSngB3DkPWlJSgsrKSshkMtTW1uLll1+GVCoV1tbd3S3aKfZ4PJicnMT4+DgWLlwY0jxWqxVarRYKhUJo0+l08Hq9sNlsSEhICHnNPitWrMCCBQuEz6HE0eVyoaysDCdOnMDw8DA8Hg/Gx8fhdDqDzqfRaERjJyQkQCqVQiKRiNpcLpewnlu3bgm/ez4TExOi73XuuCqVShgjHBMTE4iOjg7rnmDxsNlsUKvVUCqVwj05OTmiMbZt24bCwkKkp6dj/fr1eOaZZ7Bu3TpRH7lcLqoTYIzdwckxYywsKSkpeOihh2A2m+F2u6HX6wEASqUSDz/8MDo6OmA2m7F27VoAd/4ELJVK0dPTIyR3PjExMUKf1157DW+++abffMuWLRP+vWbNGjQ2NuK7777Dzp07hXapVIrW1lZ0dnbip59+wv79+/Hhhx+iq6tLOOcbiFqtRmpqKlJTUxETE4MXXngB/f39iI+Ph9frxapVq0QJu8+SJUsCjrdhwwao1WrU1NRg6dKl8Hq9yMzMDLswbcOGDfB6vWhsbMTjjz8Oi8WCL7/8Urju9Xrx6aefYtOmTX73hpOIEREiIiICXpuvPZjZiTaAkOK4bds2XL16FV999RUSExMhk8mQm5sbUtx8x2BmrztQm+8/Kl6vFyqVyu/MOwBRUeTdxghHXFwc3G53WPcEi8fdvjef7OxsOBwONDU14eeff8ZLL72EgoICfP/990Kf69evz/u7zNi9jJNjxljY8vLy0NbWBrfbjR07dgjter0eLS0tOH36tFCdn5WVBY/HA5fL5fe6N5/s7GxcuHABKSkpd503JycHJpMJBoMBUqlUNHdERAR0Oh10Oh3KysqQmJiIhoYGvPPOOyE9k16vR2ZmJvbs2YOKigpkZ2ejrq5OKNoK5tq1a7BaraiqqhKe8+TJk6I+vh1Vj8dz17Hkcjk2bdqE2tpa2O12pKWlYdWqVcL17Oxs2Gy2oPGaO/fceR955BF8++23GBsbE5Lajo4OSCSSf+wtI6HE0WKxoLKyEk8//TQAYHBwECMjI6I+UVFRQeMW6nqGhoYQGRnpdwY3HIHiGUhWVhb6+/vDGjtYPDIyMuB0OjE8PCzs7s8+h+0TGxuLLVu2YMuWLdi8eTPWr1+P69evC38dOX/+PLKyssJaG2P3Ai7IY4yFLS8vDydPnkRvb6+wcwzcSTBramowOTkpFOOlpaXBaDRi69atOHLkCBwOB7q7u/HFF1/gxx9/BAC89957OHXqFN544w309vbi999/x9GjR2Eymfzmzs3NRVNTE3bv3o3y8nIAQFdXF/bu3YszZ87A6XTiyJEjuHr1qnAcI1TvvvsuqqqqcPnyZRiNRsTFxWHjxo2wWCxwOBxob29HaWkp/vjjD797fW9iqK6uht1ux4kTJ/wS8/j4eMjlcjQ3N2N4eBg3btyYdy1GoxGNjY345ptv8Morr4iulZWV4eDBg/jkk09w4cIFWK1W1NXVYdeuXfOOp9Fo0NfXB5vNhpGREdy+fRtGoxHR0dEoLi7G+fPnYTabYTKZUFRU9LeOVMz3HMHimJKSgkOHDsFqtaKrqwtGoxFyudxv/cePH8fQ0FDYO7GzFRQUIDc3F8899xxaWlowMDCAzs5O7Nq1S/SKwmA0Gg26urowMDAgOkIzl8FgwKlTpwIm0ufOnUNvb6/oBwgej8LCQiQnJ6O4uBh9fX3o6OgQCvJ8O8rl5eU4fPgwfvvtN1y8eBH19fVQKpWi3XGLxeJ31IIxBi7IY4yFz1dYlpGRIWofHBwkAJScnCxq//PPP6msrIw0Gg1FRUWRUqmk559/XlRU98svv1BhYSHFxMSQQqGglStX0p49e4Trc4u/2tvbSaFQUEVFBfX395PBYKAlS5aQTCajtLQ02r9/f9D1zy2M83q9lJ6eTq+//joREV25coW2bt1KcXFxJJPJKCkpibZv3043btwgIv8it9bWVlq+fDnJZDJauXIltbW1+RWS1dTUkFqtJolEQnq9PuA4RHeKsFQqlVDYOFdzczOtXr2a5HI5xcbGUk5ODlVXV8/7zC6XS4gvADKbzURE1NfXR3l5eRQdHU2LFy+m7du3082bN+cdZ7ZABXmBChSDxfHs2bP02GOPkUwmo9TUVKqvr/cb++jRo5SSkkKRkZGUmJhIRHcK57RarWiuQGuYWzw3OjpKJpOJli5dSlFRUaRWq8loNJLT6Zx33PLycmFeIiKbzUZPPvkkyeVyAkAOhyNgjKanp+nBBx+k5uZmoc1XkBfoJ9R4WK1W0ul0tGDBAsrIyKBjx44RAGGe6upqevTRR0mhUFBsbCzl5+fT2bNnhfs7Ozvp/vvvp/Hx8YDrZuxeFkEU4ruOGGOMMRa2yspK/PDDD2hpafnX5ujo6MBTTz0Fu92O5OTkoP1ffPFFZGVl4YMPPvjX1sTYfxWfOWaMMcb+RSUlJXC73bh586bfWzv+roaGBsTExCA1NRV2ux2lpaXQ6XQhJcZTU1PQarV4++23/5G1MPb/hneOGWOMsf+YgwcP4rPPPsPg4CDi4uJQUFCAffv2+b2ijjEWPk6OGWOMMcYYm8Fvq2CMMcYYY2wGJ8eMMcYYY4zN4OSYMcYYY4yxGZwcM8YYY4wxNoOTY8YYY4wxxmZwcswYY4wxxtgMTo4ZY4wxxhibwckxY4wxxhhjM/4CWPBmP6kC6MYAAAAASUVORK5CYII=", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Define lags (0 to 6)\n", "num_lags = 6\n", "\n", "# Create lag dummies for 0 to 6\n", "for i in range(0, num_lags + 1):\n", "    col_name = f\"lag_{i}\"\n", "    final_data[col_name] = (final_data[\"standardized_time_weeks\"] == i).astype(int)\n", "\n", "# Update regression formula to include interaction terms for lags 0 to 6\n", "interaction_terms = \" + \".join([f\"lag_{i} * is_treatment\" for i in range(0, num_lags + 1)])\n", "formula = f\"pr_throughput ~ post_treatment + is_treatment + is_treatment * post_treatment + {interaction_terms}\"\n", "\n", "# Fit the model\n", "model = smf.ols(formula, data=final_data).fit()\n", "\n", "# Extract coefficients and confidence intervals for `is_treatment & post_treatment` interaction terms\n", "interaction_effects = [f\"lag_{i}:is_treatment\" for i in range(0, num_lags + 1)]\n", "interaction_coef = model.params.reindex(interaction_effects)\n", "interaction_ci = model.conf_int().reindex(interaction_effects)\n", "\n", "# Extract lower and upper bounds for confidence intervals\n", "interaction_ci_lower = interaction_ci[0]\n", "interaction_ci_upper = interaction_ci[1]\n", "\n", "# Calculate y-error for plotting\n", "interaction_yerr = np.vstack((interaction_coef.values - interaction_ci_lower, \n", "                               interaction_ci_upper - interaction_coef.values))\n", "\n", "# Print coefficients and p-values for interaction terms\n", "for i, term in enumerate(interaction_effects):\n", "    coef = model.params[term]\n", "    pval = model.pvalues[term]\n", "    print(f\"Lag {i}: Coefficient = {coef}, P-value = {pval}\")\n", "\n", "# Visualize the interaction effects (`is_treatment & post_treatment`) over lags 0 to 6\n", "plt.figure(figsize=(8, 5))\n", "plt.errorbar(range(0, num_lags + 1), interaction_coef.values, \n", "             yerr=interaction_yerr, fmt='o', label='Interaction Coefficient')\n", "plt.axvline(x=0, color='red', linestyle='--', label='Treatment Start')\n", "plt.title(\"Effect of 'is_treatment & post_treatment' Over Time (Lags 0-6)\")\n", "plt.xlabel(\"Weeks Relative to Treatment (Lags)\")\n", "plt.ylabel(\"Coefficient (Effect of Interaction)\")\n", "plt.xticks(range(0, num_lags + 1))\n", "plt.legend()\n", "plt.grid()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Lagged Interaction Coefficients and P-values:\n", "Lag 0: Coefficient = 0.41082261095932027, P-value = 0.12000117883983162\n", "Lag 1: Coefficient = 0.20777765000123405, P-value = 0.4737376345815266\n", "Lag 2: Coefficient = 0.30870889633446363, P-value = 0.30796833323728967\n", "Lag 3: Coefficient = 0.11965604586156385, P-value = 0.684136778688698\n", "Lag 4: Coefficient = 0.2277116210672885, P-value = 0.45070266727188224\n", "Lag 5: Coefficient = 0.13334301273032353, P-value = 0.6613495067256512\n", "Lag 6: Coefficient = -0.217823852794012, P-value = 0.45179005841078124\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAscAAAHUCAYAAADWXIWGAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACte0lEQVR4nOzdd1xT1/sH8M8lJIEwwl4OQIsiKoIL0QJaBeponVWrpWId9Wtbdx11onVXi9WqrQtX1bY4KVr3BPcuQh0oDhAQDTOQcX5/UPIzJiCEQESf9+uVl+bck3ufe0guDyfnnsMxxhgIIYQQQgghMDJ0AIQQQgghhLwpKDkmhBBCCCHkP5QcE0IIIYQQ8h9KjgkhhBBCCPkPJceEEEIIIYT8h5JjQgghhBBC/kPJMSGEEEIIIf+h5JgQQgghhJD/UHJMCCGEEELIfyg5JmqioqLAcVypj+PHj6vqZmVloX///nBwcADHcejRowcA4P79++jatStsbGzAcRzGjBmj9zhXrlyJqKgove+3qKgII0aMgLOzM3g8Hnx8fEqtGx4ejvbt26uVcRyHWbNm6TWmJ0+eYNasWbh69ape91tRsbGxej+3qjRv3jzs3r27Qq959OiR6j1tYWEBX19frFy5smoC1KO4uDjMmjULL168qNbX6lNVfaarQlmfyVmzZsHNza1c+2GM4bfffsMHH3wAa2trCIVC1KtXD1999RUePnyo36ArqazfC6/+jggPDy93G1SnU6dOQSgU4sGDB6qy9u3bo0mTJgaMquIuX76MTp06wdzcHFZWVujVqxfu3btXoX1kZmZi9OjRcHNzg1AohKOjIzp37oysrCxVnXXr1qFWrVrIy8vT9ym8+RghL9mwYQMDwDZs2MDi4+M1HhKJRFV3zJgxTCAQsC1btrD4+HiWlJTEGGOsR48ezNbWlu3atYvFx8ez+/fv6z3Oxo0bs6CgIL3vNzIykgFgy5cvZ3Fxcez69eul1h00aJBGDPHx8ezhw4d6jenChQuqn4khffXVV6wmXTLMzMzYoEGDyl1foVAwHx8f5uzszDZu3Mj+/vtvNmfOHDZgwICqC1JPFi9ezACw5OTkan2tPlXVZ7oqlPWZnDlzJnN1dX3tPhQKBevXrx8DwD799FO2e/duduzYMbZs2TJWu3ZtZmVlxU6fPq3/4HX06u+CLl26MFNTU62/I+7cucMuX75s6JDVKJVK1rx5c/bVV1+plQcFBbHGjRsbKKqKu3XrFrOwsGABAQHsr7/+YtHR0axx48bMxcWFpaenl2sfjx8/ZvXq1WMNGjRga9euZSdOnGDR0dHs66+/Zqmpqap6MpmMeXh4sBkzZlTV6byxjA2XlpM3WZMmTdCyZcsy69y8eRP169fHwIEDNcpbt26t6kmuSW7evAlTU1N8/fXXOr2+TZs2eo6o4vLz8yESiQwdRo2TlJSEq1evYtWqVfj8888BACEhIQaO6s1TUFAAU1NTQ4dR4y1cuBA7duzAggULMGnSJFV5+/bt0a9fP/j5+aF3795ITEyElZVVtcVV2vXj1Wubvb09jIyMtF7zLC0tqyw+XR04cACXL1/Gb7/9ZuhQKmXGjBkQCoWIiYlRtXOLFi3g4eGBH374AQsXLnztPkaOHInCwkJcvHgR1tbWqvJevXqp1TM2NsaXX36JOXPmYNKkSe/W7xVDZ+fkzVLSc3zhwoVS6yQnJzMAGo9jx45pLS/pkZJIJGz8+PHMzc2N8fl85uLiwkaPHs1yc3PV9q9QKNhPP/3EmjVrxkxMTJhYLGZ+fn5sz549jDHGXF1dNY7xup6agoICNnnyZLVjjxw5kj1//lxVR1vsZfXWaus5BsBmzpypep6Xl6c6Z6FQyKytrVmLFi3Yb7/9Vma8JUpr05JjDBo0iJmZmbHr16+z4OBgZm5uztq0acMYY6ywsJDNmTOHNWzYkAkEAmZnZ8fCw8M1ehe2b9/OgoODmZOTEzMxMWGenp5s0qRJaj+XQYMGlfmzBcC++uortn79etagQQNmYmLCWrRoweLj45lSqWSLFi1ibm5uzMzMjHXo0IHdvn1b41wPHTrEPvjgA2ZhYcFMTU1Z27Zt2eHDh9XqzJw5kwFgN2/eZP3792eWlpbMwcGBDR48mL148ULt5/Dq43W9kklJSQwAGzNmTLl+NqUpaYvVq1czDw8PJhAIWKNGjdi2bds06t64cYN9/PHHzMrKigmFQtasWTMWFRWlVkehULA5c+ao2lUsFrOmTZuyyMhItTbR9nl8nde91tXVlXXt2pVFR0czHx8fJhQK2aRJkxhjjKWmprLhw4ezWrVqMT6fz9zc3NisWbOYTCZTO8asWbNY69atmbW1NbOwsGC+vr5s7dq1TKlUquqU9Zku+Qxs3bqVTZw4kTk5OTEzMzPWrVs3lpaWxrKzs9mwYcOYra0ts7W1ZeHh4SwnJ0ctBqVSyX7++WfVNcXKyor17t2b3b17V61eSS/i+fPn2fvvv89MTU2Zu7s7mz9/PlMoFGrxlPaZLE/PcWFhIbO2tmaNGjVSa4eX/fbbbwwA++GHHxhjjI0ePZqJRCK1b+9K9O3blzk4OLCioiJV2fbt21mbNm2YSCRiZmZmLCQkRKM3t6zrx+uUvLa0ba+2QXVdI0rz0UcfsVatWmmUl6fn+MKFC6xfv37M1dWVmZiYMFdXV9a/f3+t34qeOnWKtWnThgmFQubi4sKmTZvG1qxZo/HtzJEjR1hQUBCzsbFhJiYmrE6dOqxXr14sLy+v1DhkMhkzNTVlX375pca2kJAQ5uHhUeZ5MFb8+5vjODZr1qzX1mWs+HPOcRxbt25dueq/LSg5JmpKkuOzZ88ymUym9pDL5YwxxqRSKYuPj2e+vr6sXr16al+nxcfHMycnJ9auXTtVuVQqZXl5eczHx4fZ2dmxpUuXssOHD7Nly5YxsVjMPvjgA7VfEGFhYYzjODZ06FC2Z88etn//fjZ37ly2bNkyxhhjly9fZvXq1WO+vr6qY5T1FZ5SqWShoaHM2NiYTZ8+nR08eJD98MMPzMzMjPn6+jKpVMoYY1q/Kizv11QlXk2Ov/zySyYSidjSpUvZsWPHWExMDFuwYAFbvnx5ufYnkUhUP5Np06ap4ioZujFo0CBVYjJ//nx25MgR9vfffzOFQsE+/PBDZmZmxiIiItihQ4fY2rVrWa1atZiXlxfLz89XHWPOnDnsxx9/ZH/99Rc7fvw4W716NXN3d2cdOnRQ1blz5w7r06cPA6D2FWpJ25UkM23btmU7d+5ku3btYg0aNGA2NjZs7NixrHv37iwmJoZt3bqVOTo6Mm9vb7Wf+ebNmxnHcaxHjx5s586dbN++faxbt26Mx+Op/fIrSeYaNmzIZsyYwQ4dOsSWLl3KhEIhGzx4sKpefHw8MzU1ZV26dFHF+s8//7y2vdu3b894PB7btWtXuX4+2gBgderUYV5eXmzbtm1s79697MMPP2QA2B9//KGql5iYyCwsLFj9+vXZpk2b2F9//cU+/fRTBoAtXLhQVW/+/PmMx+OxmTNnsiNHjrADBw6wyMhI1S+3hw8fsm+++YYBYDt37tQ6BKo0r3utq6src3Z2ZvXq1WPr169nx44dY+fPn2epqamsTp06zNXVlf3yyy/s8OHDbM6cOUwoFLLw8HC1Y4SHh7N169axQ4cOsUOHDrE5c+YwU1NTFhERoapT1me6JBl1dXVl4eHh7MCBA2z16tXM3NycdejQgQUHB7MJEyawgwcPsoULFzIej8e++eYbtRiGDRvG+Hw+Gz9+PDtw4AD77bffmKenJ3N0dGRpaWmqekFBQczW1pZ5eHiw1atXs0OHDrGRI0cyAGzjxo2Msdd/JssjLi6OAVD9oaFNTk4OMzIyYqGhoYwxxq5du8YAsDVr1qjVe/78ORMKhWzcuHGqsrlz5zKO49gXX3zBYmJi2M6dO5m/vz8zMzNT+xyUdv0oD12S4+q4RmhTWFjITE1N2cSJEzW2lSc5/uOPP9iMGTPYrl272IkTJ9j27dtZUFAQs7e3ZxkZGap6165dYyYmJszb25tt376d7d27l3Xp0oW5ubmpJcfJycnMxMSEBQcHs927d7Pjx4+zrVu3srCwMLUOm1clJiYyAOznn3/W2DZhwgTGcRwrKCgo81w2bdrEALBff/2V9e/fn5mZmTGhUMiCgoJYXFyc1tc0atSI9erVq8z9vm0oOSZqSi762h48Hk+tbmkXlZLeppfNnz+fGRkZafRI//nnnwwAi42NZYwxdvLkSQaATZ06tcw4KzI+8cCBAwwAW7RokVr5jh07VBeJEmVd8Mvj1eS4SZMmrEePHjrvj7GyxzeW9OiuX79erXzbtm0MAIuOjta6r5UrV2o9llKpZDKZjJ04cYIBYNeuXVNtK2vMMQDm5OSk1tu8e/duBoD5+Pio/ZIrGdddMp47Ly+P2djYsI8++khtnwqFgjVr1oy1bt1aVVaSHL/6sxw5ciQzMTFRO05FxxwnJSUxT09P1qBBAyYQCFhMTEy5X/syAMzU1FQt6ZLL5czT05O99957qrL+/fszoVDIUlJS1F7fuXNnJhKJVD3h3bp1Yz4+PmUes6rGHLu6ujIej6e6n6DEl19+yczNzdmDBw/Uyn/44QcGoNQ/RBQKBZPJZGz27NnM1tZW7edV2me6JDl+9f0xZswYBoCNGjVKrbxHjx7MxsZG9Tw+Pp4BYEuWLFGr9/DhQ42EKSgoiAFg586dU6vr5eWlSlIZq/x9ANu3b2cA2OrVq8us5+joyBo1aqR63rx5c9a2bVu1OitXrmQA2I0bNxhjjKWkpDBjY2ONPxBycnKYk5MT69u3r6qstOtHeeiSHFfHNUKbc+fOMQBs+/btGtt0GXMsl8tZbm4uMzMzU3XaMMbYJ598wszMzNQSZoVCwby8vNQ+YyW/965evVqh4545c4YB0Pot1Lx58xgA9uTJkzL3MX/+fAaAWVpasu7du7MDBw6w6Oho5u3tzUxMTNSu+SUGDhzIHB0dKxRrTUezVRCtNm3ahAsXLqg9zp07p/P+YmJi0KRJE/j4+EAul6seoaGharNg7N+/HwDw1Vdf6eM0AABHjx4FUDy7xMs++eQTmJmZ4ciRI3o71qtat26N/fv3Y/LkyTh+/DgKCgqq5Di9e/dWex4TEwMrKyt89NFHau3t4+MDJycntVlH7t27hwEDBsDJyQk8Hg98Ph9BQUEAgFu3bpU7hg4dOsDMzEz1vFGjRgCAzp07g+M4jfKSO8bj4uKQlZWFQYMGqcWqVCrx4Ycf4sKFCxp3S3/88cdqz729vSGVSpGenl7ueF+WlZWFTp06ITg4GDdu3EBISAh69+6tej8CwJYtW8BxHJKTk1+7v44dO8LR0VH1nMfjoV+/frhz5w4ePXoEoPh92bFjR9SpU0ftteHh4cjPz0d8fDyA4vfQtWvXMHLkSPz999/Izs7W6Rx15e3tjQYNGqiVxcTEoEOHDnBxcVH7mXXu3BkAcOLECVXdo0ePolOnThCLxar314wZM/Ds2bMK/by6deum9rzkfdS1a1eN8qysLOTm5qpi5TgOn332mVqsTk5OaNasmdpnAQCcnJzQunVrjTZ4eYaD6sIYU/vsDB48GHFxcUhKSlKVbdiwAa1atVLNuPD3339DLpfj888/VztfExMTBAUFaZwvoHn9qCrVeY142ZMnTwAADg4OOsWdm5uLSZMm4b333oOxsTGMjY1hbm6OvLw8tWvkiRMn8MEHH8DOzk5VZmRkhL59+6rtz8fHBwKBAMOHD8fGjRsrPNPEy21V2jaFQqHRVgBU/9auXRvR0dEIDQ1Fr169cODAARgZGWHRokUa+3RwcEB6ejrkcnmF4qzJ6IY8olWjRo1ee0NeRTx9+hR37twBn8/Xuj0zMxMAkJGRAR6PBycnJ70d+9mzZzA2Noa9vb1aOcdxcHJywrNnz/R2rFf99NNPqF27Nnbs2IGFCxfCxMQEoaGhWLx4MTw8PPRyDJFIpHEDzNOnT/HixQsIBAKtrylp79zcXAQEBMDExATff/89GjRoAJFIhIcPH6JXr14VSuZtbGzUnpccu7RyqVSqihUA+vTpU+q+s7Ky1H6p2traqm0XCoUAoPMfH+vWrcPDhw8xY8YMCAQCREdHo0+fPujZsyf27NmD0NBQHD9+HI0aNYK7u/tr96ft/VtS9uzZM9SuXRvPnj2Ds7OzRj0XFxdVPQCYMmUKzMzMsGXLFqxevRo8Hg+BgYFYuHChXj+jpdEW49OnT7Fv377Xfp7Pnz+PkJAQtG/fHmvWrEHt2rUhEAiwe/duzJ07t8rfX+bm5nj69CkYY2p/rLysXr16as9ffW8Bxe8vff5hW7duXQAo8w+tvLw8ZGZmwtfXV1U2cOBATJgwAVFRUZg/fz4SEhJw4cIFtekGSz5PrVq10rpfIyP1PjFt14+qUp3XiJeV/OxMTEx0iBoYMGAAjhw5gunTp6NVq1awtLQEx3Ho0qWL2vvi2bNnWt9nr5bVr18fhw8fxqJFi/DVV18hLy8P9erVw6hRozB69OhS4yh5b2r7nZWVlQWO41Q3b9avX1/tD7qZM2di1qxZqn106tQJPB5Ptd3Z2RnNmjXD5cuXNfZtYmICxpjqM/UuoOSYVAs7OzuYmppi/fr1pW4Hiu+AVigUSEtL0/pLWRe2traQy+XIyMhQS5AZY0hLSyv1l4g+mJmZISIiAhEREXj69KmqF/mjjz5CYmKiXo6hrRfBzs4Otra2OHDggNbXWFhYACju1Xvy5AmOHz+u6i0GUK1z3pb87JcvX17qbB+lJTb6cvfuXfB4PNWFXyAQ4M8//8Qnn3yCHj16YMmSJdi0aVO55+FNS0srtazkl5OtrS1SU1M16pX0cpW0i7GxMcaNG4dx48bhxYsXOHz4ML777juEhobi4cOHVX4HeWnvL29vb8ydO1fra0oS/O3bt4PP5yMmJkYtMano/NOVYWdnB47jVHPcvkpbWVVr0aIFrK2tsXfvXsyfP19rG+/duxdKpRLBwcGqMmtra3Tv3h2bNm3C999/jw0bNsDExASffvqpqk7J++bPP/+Eq6vra2MpqxfyTVHZa0TJ61+ew7e8JBIJYmJiMHPmTEyePFlVXlhYqLE/W1tbVSL/Mm3Xg4CAAAQEBEChUODixYtYvnw5xowZA0dHR/Tv319rLPXr14epqSlu3Lihse3GjRt47733VJ+zffv2obCwULW95DPp7e1d6rkyxjT+eAKK200oFL4ziTFAyTGpJt26dcO8efNga2tbZs9b586dMX/+fKxatQqzZ88utV5FenI6duyIRYsWYcuWLRg7dqyqPDo6Gnl5eejYsWP5T6QSHB0dER4ejmvXriEyMrLcU67p0ivarVs3bN++HQqFAn5+fqXWK/nF+GqC8Msvv5QZhz6n8mrXrh2srKyQkJCg8xR62lTkPdKkSRMoFAps3boVQ4YMAfD/CXKPHj3w1VdfISgoCAMGDCjX/o4cOYKnT5+qfmErFArs2LED9evXR+3atQEUvy937dqFJ0+eqH5xAcVDmkQikdYkwMrKCn369MHjx48xZswY3L9/H15eXpXqOdf1/RUbG4v69eurTQX1Ko7jYGxsrNZDVVBQgM2bN2uNoyqGHXXr1g0LFizA48ePNb7e1lVlv6kQCAT49ttv8d1332Hx4sWYOHGi2vb09HRMmTIFjo6OGDp0qNq2wYMH4/fff0dsbCy2bNmCnj17qk31FhoaCmNjY9y9e7fahktUtcpeI0qGady9e7fCr+U4DowxjWvk2rVroVAo1MqCgoIQGxuLzMxMVUKuVCrxxx9/lLp/Ho8HPz8/eHp6YuvWrbh8+XKpybGxsTE++ugj7Ny5E4sWLVJ1cqSkpODYsWNqv9+aNm2qdR9+fn6oXbs2Dh48CIVCofpsPnnyBNeuXdN6jbt37x68vLxKPYe3ESXHRKubN29qHV9Uv359jeEJ5TFmzBhER0cjMDAQY8eOhbe3N5RKJVJSUnDw4EGMHz8efn5+CAgIQFhYGL7//ns8ffoU3bp1g1AoxJUrVyASifDNN98AKP7gb9++HTt27EC9evVgYmJS6sUgODgYoaGhmDRpErKzs9GuXTtcv34dM2fOhK+vL8LCwip8PuXl5+eHbt26wdvbG9bW1rh16xY2b94Mf3//cvf4lfQWbN26FY0aNYK5uTlcXFzUEqpX9e/fH1u3bkWXLl0wevRotG7dGnw+H48ePcKxY8fQvXt39OzZE23btoW1tTVGjBiBmTNngs/nY+vWrbh27ZrGPkvad+HChejcuTN4PB68vb1LHbpRXubm5li+fDkGDRqErKws9OnTBw4ODsjIyMC1a9eQkZGBVatWVXi/TZs2xfHjx7Fv3z44OzvDwsICDRs21Fp3yJAh2LBhA/73v//hxo0bCA0NhUKhQHx8PE6dOoU6derg9OnT+P3338uVYNnZ2eGDDz7A9OnTYWZmhpUrVyIxMRHbt29X1Zk5c6Zq7O6MGTNgY2ODrVu34q+//sKiRYsgFosBAB999JFq3nF7e3s8ePAAkZGRcHV1VQ3NKfnZLFu2DIMGDQKfz0fDhg1Vvzxf104Vfe3s2bNx6NAhtG3bFqNGjULDhg0hlUpx//59xMbGYvXq1ahduza6du2KpUuXYsCAARg+fDiePXuGH374QWtvbUU+0xXRrl07DB8+HIMHD8bFixcRGBgIMzMzpKam4vTp02jatCn+97//VWifunwmXzVp0iRcu3ZN9W+/fv0gFotx/fp1LF68GDk5OYiJiVG9D0qEhISgdu3aGDlyJNLS0jB48GC17W5ubpg9ezamTp2Ke/fu4cMPP4S1tTWePn2K8+fPq77Nqkkqe42oXbs26tWrh7Nnz2LUqFEa27Ozs/Hnn39qlNvb2yMoKAiBgYFYvHgx7Ozs4ObmhhMnTmDdunUa809PnToV+/btQ8eOHTF16lSYmppi9erVqvHQJb2yq1evxtGjR9G1a1fUrVsXUqlU9a1qp06dymyLiIgItGrVCt26dcPkyZMhlUoxY8YM2NnZYfz48WW+tiSGH3/8EX379kX37t3xv//9D3l5eZgzZw4EAgGmTJmiVl+pVOL8+fOqToN3hiHvBiRvnrJmq8Ar0whVZLYKxhjLzc1l06ZNU827WzJf69ixY9Xu7FcoFOzHH39kTZo0UdXz9/dn+/btU9W5f/8+CwkJYRYWFuWe53jSpEnM1dWV8fl85uzszP73v/9pTJuj79kqJk+ezFq2bMmsra2ZUChk9erVY2PHjmWZmZkV2u+2bduYp6cn4/P5Wuc51kYmk7EffvhBNberubk58/T0ZF9++aXaHKJxcXHM39+fiUQiZm9vz4YOHcouX76scTd+YWEhGzp0KLO3t2ccx2md5/hlJfNhL168WK28ZPaBl6c1Y4yxEydOsK5duzIbGxvG5/NZrVq1WNeuXdXqlcxW8fLd4Iz9//v25RkXrl69ytq1a8dEIlG55jkueX82aNCA8fl8ZmlpyTp06MB+++03JpfLWY8ePZixsbHGDCCvKmmLlStXsvr16zM+n888PT3Z1q1bNereuHGDffTRR0wsFjOBQMCaNWumMQPCkiVLWNu2bZmdnR0TCASsbt26bMiQIRpzrE6ZMoW5uLgwIyOjcs9z/LrXlvZZZoyxjIwMNmrUKObu7s74fD6zsbFhLVq0YFOnTlWbkWD9+vWsYcOGqvf//Pnz2bp16zR+XqV9pkt7v5Q2J3tp75H169czPz8/ZmZmxkxNTVn9+vXZ559/zi5evKiqU9o1TdvsC6V9JitCqVSyrVu3svbt2zMrKysmEAiYu7s7+9///qcxE8jLvvvuO9WUgSXzL79q9+7drEOHDszS0pIJhULm6urK+vTpozbtWWWud7rOc/yyqrhGlGb69OnM2tpaNf1kiZIZSrQ9Sq4Zjx49Yr1791bN1f3hhx+ymzdvMldXV40ZcU6dOsX8/PyYUChkTk5O7Ntvv2ULFy5kAFQz0MTHx7OePXsyV1dXJhQKma2tLQsKCmJ79+597XkwxtjFixdZx44dmUgkYpaWlqxHjx7szp075Xptid27d7NWrVqp5k7/+OOPtc4yc+TIEQaAXbp0qUL7r+k4xhirwtybEELeKRzH4auvvsKKFSsMHQoh5D9PnjyBu7s7Nm3ahH79+lXrsUNCQnD//n38+++/1XpcfQgLC8O9e/dw5swZQ4dSrWhYBSGEEELeai4uLhgzZgzmzp2LTz75ROuNZ/owbtw4+Pr6ok6dOsjKysLWrVtx6NAhrFu3rkqOV5Xu3r2LHTt2qKZDfZdQckyIgTDGNG7oeBWPx6sRd5OTN49SqVTNaVoaY2P6FUDeHdOmTYNIJMLjx4815hfXF4VCgRkzZiAtLQ0cx8HLywubN2/GZ599ViXHq0opKSlYsWIF3n//fUOHUu1oWAUhBnL8+HF06NChzDobNmzQWLyEkPKYNWvWa2+8Sk5OhpubW/UERAghNQQlx4QYSE5OjtpKV9q4u7trXZSAkNd58uSJas7k0uhjthFCCHnbUHJMCCGEEELIf6pmRDohhBBCCCE1UI26G+PkyZNYvHgxLl26hNTUVOzatQs9evQo8zUnTpzAuHHj8M8//8DFxQUTJ07EiBEj1OpER0dj+vTpuHv3LurXr4+5c+eiZ8+e5Y5LqVTiyZMnsLCwoJunCCGEEELeQIwx5OTkwMXFpewZSww1wbIuYmNj2dSpU1l0dDQDwHbt2lVm/Xv37jGRSMRGjx7NEhIS2Jo1axifz2d//vmnqk5cXBzj8Xhs3rx57NatW2zevHnM2NiYnT17ttxxPXz4sMyFM+hBD3rQgx70oAc96PFmPB4+fFhmXldjxxxzHPfanuNJkyZh7969uHXrlqpsxIgRuHbtGuLj4wEA/fr1Q3Z2Nvbv36+qU7Lc5rZt28oVi0QigZWVFR4+fAhLS0vdTqi8lErIkpNx8uRJBA4YAL6WZVhJ1ZDJZDh48CBCQkLA5/MNHc47g9rdMKjdDYPa3TCo3Q2juts9OzsbderUwYsXLzSWZn9ZjRpWUVHx8fEICQlRKwsNDcW6desgk8nA5/MRHx+PsWPHatSJjIwsdb+FhYUoLCxUPc/JyQEAmJqawtTUVH8noE1eHiybN0dPAPkDBoBf1ccjKsbGxhCJRDA1NaWLZzWidjcManfDoHY3DGp3w6judpfJZADw2iGwb3VynJaWBkdHR7UyR0dHyOVyZGZmwtnZudQ6aWlppe53/vz5WucPPXjwIEQikX6CLwVPKkW3//5/9OhRKExMqvR4RNOhQ4cMHcI7idrdMKjdDYPa3TCo3Q2juto9Pz+/XPXe6uQY0PzroGQUycvl2uqU9VfFlClTMG7cONXzkm76kJCQqh9WkZen+u8HH3wAvpVV1R6PqMhkMhw6dAjBwcHUs1CNqN0Ng9rdMKjdDYPa3TCqu92zs7PLVe+tTo6dnJw0eoDT09NhbGysWlihtDqv9ia/TCgUQqhlrC+fz6/6H+5L+6+W4xEN1O6GQe1uGNTuhkHtbhjU7oZRXe1e3mO81cmxv78/9u3bp1Z28OBBtGzZUtVA/v7+OHTokNq444MHD6Jt27bVGishhJA3C2MMcrkcCoWi2o4pk8lgbGwMqVRarcd911G7G4a+253H48HY2LjS0+rWqOQ4NzcXd+7cUT1PTk7G1atXYWNjg7p162LKlCl4/PgxNm3aBKB4ZooVK1Zg3LhxGDZsGOLj47Fu3Tq1WShGjx6NwMBALFy4EN27d8eePXtw+PBhnD59utrPjxBCyJuhqKgIqamp5R6jqC+MMTg5OeHhw4c0b341onY3jKpod5FIBGdnZwgEAp33UaOS44sXL6JDhw6q5yXjfgcNGoSoqCikpqYiJSVFtd3d3R2xsbEYO3Ysfv75Z7i4uOCnn35C7969VXXatm2L7du3Y9q0aZg+fTrq16+PHTt2wM/Pr/pOjBBCyBtDqVQiOTkZPB4PLi4uEAgE1ZYwKZVK5ObmwtzcvOxFCoheUbsbhj7bnTGGoqIiZGRkIDk5GR4eHjrvs0Ylx+3bt0dZ0zJHRUVplAUFBeHy5ctl7rdPnz7o06dPZcOrHsbGUIwYgZQHD1DbuEb9+AghpEYoKiqCUqlEnTp1qnwGolcplUoUFRXBxMSEkrRqRO1uGPpu95Ip4R48eKDary4ou6pphEIof/oJ12NjUZsWACGEkCpDSRIhNY8+Prf0ySeEEEIIIeQ/lBzXNIwBGRkQSCTF/yeEEEIIIXpDyXFNk58Pfq1a6DxoEFDNd1ETQgghZWGMYfjw4bCxsQHHcbh69arWMkLeZJQcE0IIIW+J8PBwcByHBQsWqJXv3r270jNuREVFgeM4jcfLNz0dOHAAUVFRiImJQWpqKpo0aaK1rLLCw8PRo0ePSu+HEG3ohjxCCCHkLWJiYoKFCxfiyy+/hLW1tV73bWlpiaSkJLWyl5Puu3fvwtnZWW0hLW1lhLzJqOeYEEIIeYt06tQJTk5OmD9/fpn1oqOj0bhxYwiFQri5uWHJkiWv3TfHcXByclJ7ODo6Aijuzf3mm2+QkpICjuPg5uamtQwoHn6xaNEi1KtXD6ampmjWrBn+/PNPtWP9888/6Nq1KywtLWFhYYGAgADcvXsXs2bNwsaNG7Fnzx5V7/Xx48d1aitCtKGe4xpGoVCA99L/aQV4QgipPjk5OcjNzVUrMzExgbW1NeRyOTIyMjRe4+zsDADIzMyETCZT22ZlZQVTU1Pk5eUhOzsbSqUSeXl5yMvLg4mJCWxtbSscI4/Hw7x58zBgwACMGjUKtWvX1qhz6dIl9O3bF7NmzUK/fv0QFxeHkSNHwtbWFuHh4RU+JgAsW7YM9evXx6+//ooLFy6Ax+NBIBBolAHAtGnTsHPnTqxatQoeHh44efIkPvvsM9jb2yMoKAiPHz9GYGAg2rdvj6NHj8LS0hJnzpyBXC7HhAkTcOvWLWRnZ2PDhg0AABsbG51iJkQbSo4JIYSQcrp06RJOnDihVta0aVP06tUL2dnZ+PXXXzVeM3PmTADAnj178OjRI7VtPXv2hLe3N/755x/s379fbVv9+vXx2Wef6RRnz5494ePjg5kzZ2LdunUa25cuXYqOHTti+vTpAIAGDRogISEBixcvLjM5lkgkMDc3Vytr27YtDh48CLFYDAsLC/B4PDg5Oam2v1qWl5eHpUuX4ujRo/D39wcA1KtXD6dPn8Yvv/yCoKAg/PzzzxCLxdi+fTv4fL4qxhKmpqYoLCxUOw4h+kLJMSGEEFJOLVq0QMOGDdXKSm5Is7S0xPDhw0t9bffu3bX2HANA48aNUadOHVXPsZmZmc6re5VYuHAhPvjgA4wfP15j261bt9C9e3e1snbt2iEyMrL4G0oeT+M1QHGi++qqs6amphWKKyEhAVKpFMHBwWrlRUVF8PX1BQBcvXoVAQEBqsSYkOpEyXFNY2yMJyEhyMnOhistH00IIdXKwsICFhYWWrcZGxurhlBoY2dnV+o2MzMzmJmZQalUIjs7G5aWlpVe6SswMBChoaH47rvvNHqDGWMas1ewcsydb2RkhPfee69ScSmVSgDAX3/9hVq1aqltE/638mtFE25C9Imyq5pGKMStiRNx+/ZthNPy0YQQQsqwYMEC+Pj4qA1JAAAvLy+cPn1arSwuLg4NGjQotddYX7y8vCAUCpGSkoKgoCCtdby9vbFx40bIZDKtvccCgQAKhaJK4yTvLkqOCSGEkLdU06ZNMXDgQCxfvlytfPz48WjVqhXmzJmDfv36IT4+HitWrMDKlSvL3B9jDGlpaRrlDg4O5e7ptrCwwIQJEzB27FgolUq8//77yM7ORlxcHMzNzTFo0CB8/fXXWL58Ofr3748pU6ZALBbj7NmzaN26NRo2bAg3Nzf8/fffSEpKgq2tLcRiMQ3BIHpDU7nVNIzBqKAAxoWFtHw0eespFAqcPHkSqamp1EtEiI7mzJmjMWSiefPm+P3337F9+3Y0adIEM2bMwOzZs187U0V2djacnZ01Hunp6RWOacaMGZg/fz4aNWqE0NBQ7Nu3D+7u7gAAW1tbHD16FLm5uQgKCkKLFi2wZs0aVQI8bNgwNGzYEC1btoS9vT3OnDlToeMTUhaOlWeQESlTdnY2xGIxJBIJLC0tq/RYiuxs8MRiAIA0MxMmOkzzQ3Qjk8kQGxuLLl26UA9FNVEoFDh+/HjxMKLw8ErfoETK711+v0ulUiQnJ8Pd3b3a33P6HHNMyo/a3TCqot3L+vyWN1+jdwAhhBBCCCH/oeSYEEIIIYSQ/1ByTAghhBBCyH8oOSaEEEIIIeQ/lBwTQgghhBDyH0qOCSGEEEII+Q8tAlLT8Hh4GhiI3Nxc1KriVYwIIYQQQt41lBzXNCYmuDljRvG8rzTnKyGEEEKIXtGwCkIIIYQQQv5DyTEhhBBCCCH/oeS4psnLQ8dOnTDif/8D8vIMHQ0hhJA3BMdxZT7Cw8Or5LizZs2Cj49PlexbF8ePHwfHcXjx4sVr6/7yyy9o1qwZLCws4OrqihYtWmDhwoWq7eHh4ejRo4feYmvfvj3GjBmjt/2RqkFjjgkhhJC3QGpqqur/O3bswIwZM5CUlKQqMzU1Vasvk8nA5/OrLb43zbp16zBu3Dj89NNPCAgIwLNnz3Dv3j0kJibq/VjvelvXNNRzTAghhJRXXl7pD6m0/HULCspXtwKcnJxUD7FYDI7jVM+lUimsrKzw+++/o3379jAxMcGWLVsAABs2bECjRo1gYmICT09PrFy5Um2/kyZNQoMGDSASiVCvXj1Mnz4dMpkMABAVFYWIiAhcu3ZN1UMdFRUFoLgn+5dffkG3bt0gEonQqFEjxMfH486dO2jfvj3MzMzg7++Pu3fvqh1v3759aNGiBUxMTFCvXj1ERERALpertnMch7Vr16Jnz54QiUTw8PDA3r17AQD3799Hhw4dAADW1tZl9pjv27cPffv2xZAhQ/Dee++hUaNG+PTTTzFnzhwAxT3iGzduxJ49e1Tndvz48de2SclrfXx8sH79etSrVw9CoRCDBg3CiRMnsGzZMtX+7t+/X6GfMake1HNMCCGElJe5eenbunQB/vrr/587OAD5+drrBgUB/yVaAAA3NyAzE0YArF6ux5iukWo1adIkLFmyBBs2bIBQKMSaNWswc+ZMrFixAr6+vrhy5QqGDRsGMzMzDBo0CABgYWGBqKgouLi44MaNGxg2bBgsLCwwceJE9OvXDzdv3sSBAwdw+PBhAIBYLFYdb86cOVi6dCmWLl2KSZMmYcCAAahXrx6mTJmCunXr4osvvsDXX3+N/fv3AwD+/vtvfPbZZ6re3Lt372L48OEAgJkzZ6r2GxERgUWLFmHx4sVYvnw5Bg4ciAcPHqBOnTqIjo5G7969kZSUBEtLS40e8xJOTk44ceKE6nWvmjBhAm7duoXs7Gxs2LABAGBjY/PaNilx584d/P7774iOjgaPx4Orqytu376NJk2aYPbs2QAAe3t73X6QpGoxUmkSiYQBYBKJpMqPJZdIGCu+XLKCzMwqPx75f0VFRWz37t2sqKjI0KG8M+RyOTt8+DBbtWoVKygoMHQ475R3+f1eUFDAEhIStL/n/rv+an106aJeVyQqvW5QkHpdOzvt9XS0YcMGJhaLVc+Tk5MZABYZGalWr06dOuy3335TK5szZw7z9/cvdd+LFi1iLVq0UD2fOXMma9asmUY9AGzatGmq5/Hx8QwAW7dunaps27ZtzMTERPU8ICCAzZs3T20/mzdvZs7OzqXuNzc3l3Ecx/bv388YY+zYsWMMAHv+/Hmp58AYY0+ePGFt2rRhAFiDBg3Yp59+yrZt28YUCoWqzqBBg1j37t3L3A9j2tuEz+ez9PR0tXpBQUFs9OjRr93fu0KhULDnz5+rtXlllfX5LW++Rj3HhBBCSHnl5pa+7dWFmdLTS69r9Mqoxvv3wRhDTk4OpFIp7OzsYPRqHT1o2bKl6v8ZGRl4+PAhhgwZgmHDhqnK5XK5Wu/vn3/+icjISNy5cwe5ubmQy+WwtLQs1/G8vb1V/3d0dAQANG3aVK1MKpUiOzsblpaWuHTpEi5cuIC5c+eq6igUCkilUuTn50MkEmns18zMDBYWFkgvq721cHZ2Rnx8PG7evInjx4/jxIkTGDx4MNavX48DBw6U2f7laRNXV1fqGa6hKDkmhBBCysvMrOrqMgYolcVJtpmZZgKtB2YvxaRUKgEAa9asgZ+fn1o93n+J/tmzZ9G/f39EREQgNDQUYrEY27dvx5IlS8p1vJdvQuM4rtSykliUSiUiIiLQq1cvjX2ZvLTw1as3t3Ecp9pHRTVp0gReXl747LPPcP36dQQFBeHEiROqscuvKm+bmFXk50/eKJQc1zQ8HjJbt0Zefj4cafloQgghOnJ0dEStWrVw7949DBw4UGudM2fOwNXVFVOnTlWVPXjwQK2OQCCAQqHQS0zNmzdHUlIS3nvvPZ33IRAIAECnmLy8vAAAef/dDKnt3MrTJmXFpq+2IlWHkuOaxsQE1+bNo+WjCSGEVNqsWbMwatQoWFpaonPnzigsLMTFixfx/PlzjBs3Du+99x5SUlKwfft2tGrVCn/99Rd27dqltg83NzckJyfj6tWrqF27NiwsLCAUCnWKZ8aMGejWrRvq1KmDTz75BEZGRrh+/Tpu3LiB77//vlz7cHV1BcdxiImJQZcuXWBqagpzLTdS/u9//4OLiws++OADuLi44M6dO4iMjIS9vT38/f1V5/b3338jKSkJtra2EIvF5WqT0ri5ueHcuXO4f/8+zM3NYWNjUyXDZ0jl0E+EEEIIeUcNHToUa9euRVRUFJo2bYqgoCBERUXB3d0dANC9e3eMHTsWX3/9NXx8fBAXF4fp06er7aN379748MMP0aFDB9jb22Pbtm06xxMaGoqYmBgcOnQIrVq1Qps2bbB06VK4urqWex+1atVCREQEJk+eDEdHR3z99dda63Xq1Alnz57FJ598Ak9PTwwaNAgmJiY4cuQIbG1tAQDDhg1Dw4YN0bJlS9jb2+PMmTPlapPSTJgwATweD15eXrC3t0dKSkq5z4tUH44xPc8T8w7Kzs6GWCyGRCIp900KulIoFDh+/Hhxz3F4uNoYLFK1ZDIZYmNj0aVLF5rMvZrQ+91w3uX3u1QqRXJyMtzd3av1PVcdN+QR7ZRKpeqmQGr36lMV7V7W57e8+RoNq6hp8vLQvls3BCqVUHzyCUDJAiGEEEKI3lByXAPxpFLwANCQfkIIIYQQ/aLvDggpB4VCgZMnTyI1NZXuNCaEEELeYpQcE0IIIYQQ8p8alxyvXLlSNci6RYsWOHXqVKl1w8PDwXGcxqNx48aqOlFRUVrrSKXS6jgdQgghhBDyBqlRyfGOHTswZswYTJ06FVeuXEFAQAA6d+5c6lQoy5YtQ2pqqurx8OFD2NjY4JNPPlGrZ2lpqVYvNTWV7oonhBBCCHkH1ajkeOnSpRgyZAiGDh2KRo0aITIyEnXq1MGqVau01heLxXByclI9SiY2Hzx4sFo9juPU6jk5OVXH6RBCCCGEkDdMjZmtoqioCJcuXcLkyZPVykNCQhAXF1eufaxbtw6dOnXSmEw8NzcXrq6uUCgU8PHxwZw5c+Dr61vqfgoLC1FYWKh6np2dDaB4XlCZTFbeU9KJQqGAxNsb0oICWCgU4FXx8UgxhUIBpVIJoPjnzKOlu6sFtbvhlFzLqvqa9iaSyWRgjEGpVKref7rKywMsLYv7obKzlTAzK9/rSo5PqkfJkg/U7tWrKtpdqVSCMab1d0Z5r2c1JjnOzMyEQqGAo6OjWrmjoyPS0tJe+/rU1FTs378fv/32m1q5p6enamWg7OxsLFu2DO3atcO1a9fg4eGhdV/z589HRESERvnBgwchEokqcFYVp1Qq8fR//wMAOJ45Q5OVVxOlUomnT58CAA4fPkztXk2o3Q3v0KFDhg6h2hkbG8PJyQm5ubkoKiqq1L7y8gDACkBxR0pZk92U/EIvqctxXKWOTSouJyfH0CG8k/TZ7kVFRSgoKMDJkychl8vVtuXn55drHzUmOS7x6sWCMVauC0hUVBSsrKzQo0cPtfI2bdqgTZs2quft2rVD8+bNsXz5cvz0009a9zVlyhSMGzdO9Tw7Oxt16tRBSEhItayQd+rUKdy9exedOnWisdHVhNrdMKjdDUcmk+HQoUMIDg5+J1fIe/jwIczNzfX6nrt2zRLBwUBZX4Dk5uZCKpXC0tKyypJjHo+H6Ohojd+HlREREYE9e/bg8uXLamWrV69Geno6oqOjsWfPHrx48QK7du3S23H1hTGGy5cv46OPPkJSUhIsLCwMHVK1S09PR9OmTXH58mXUqlWrWo5ZsiqkhYWF3t7vUqkUpqamCAwM1LpCXnnUmOTYzs4OPB5Po5c4PT1dozf5VYwxrF+/HmFhYRAIBGXWNTIyQqtWrXD79u1S6wiFQgiFQo1yPp9f5b9EjIyMVL1n1XE8Uoza3TCo3Q3vXWx3hUIBjuPU3n+62LkTGDXq/5937WqE2rWBZcuAXr0065d8xQxAdfyKSk9Px/Tp07F//348ffoU1tbWaNasGWbNmgV/f38Axd+kWltb6/WbmG+//RajRo1S7fPWrVuYPXs2du3ahTZt2sDa2hodO3YEY6xavwFq3749fHx8EBkZWWY9pVKJ77//HiNHjoRYLK6e4Erx/PlzjBo1Cnv37gUAfPzxx1i+fDmsrKxKfQ1jDBEREfj111/x/Plz+Pn54eeff1abnat9+/Y4ceKE2uv69euH7du3AwCcnJwQFhaGiIgIrF27Vv8npkXJUApd3+/aGBkZgeM4rdeu8l7Lasx3lAKBAC1atND4iu/QoUNo27Ztma89ceIE7ty5gyFDhrz2OIwxXL16Fc7OzpWKt8rk5SGgd28M+vbbku/rCCGEvGF27gT69AEeP1Yvf/y4uHznzqo5bu/evXHt2jVs3LgR//77L/bu3Yv27dsjKytLVcfJyUlrB09lmJubw9bWVvX87t27AIDu3burjicWi8tM8Azp0aNH2L9/P8LDw6v8WKXNsFViwIABuHr1Kg4cOIADBw7g6tWrCAsLK/M1ixYtwtKlS7FixQpcuHABTk5OCA4O1hiuMGzYMLWZuX755Re17YMHD8bWrVvx/Plz3U7ubcFqkO3btzM+n8/WrVvHEhIS2JgxY5iZmRm7f/8+Y4yxyZMns7CwMI3XffbZZ8zPz0/rPmfNmsUOHDjA7t69y65cucIGDx7MjI2N2blz58odl0QiYQCYRCLR7cQqQC6RMAYwBrCCzMwqPx4pJpfL2eHDh9mqVatYQUGBocN5Z1C7G05RURHbvXs3KyoqMnQo1a6goIAlJCTo/J6TyxmrXVt1qdZ4cBxjdeoU13uZUqlkEomEPX36lCkUigof9/nz5wwAO378eJn1ALBdu3apnp85c4Y1a9aMCYVC1qJFC7Zr1y4GgF25coUxxtixY8cYAHb48GHWokULZmpqyvz9/VliYqJqHzNnzmTNmjVT/R+A2oMxxgYNGsS6d++ueo1CoWALFixg9evXZwKBgNWpU4d9//33qu0TJ05kHh4ezNTUlLm7u7Np06apvR9Ljrlp0ybm6urKLC0tWb9+/Vh2drbqeK/GkZycrNEeSqWSzZ07l/n4+Ki1+4YNG5hYLGa7du1iHh4eTCgUsk6dOrGUlJQy21eb9PR0FhkZyXx9fZmPj0+p9RISEhgAdvbsWVVZfHw8A6DW3q/G7+TkxBYsWKAqk0qlTCwWs9WrV6vKgoKC2OjRo18bq5ubG1u3bl05zqryFAoFe/78uU7v99KU9fktb75WY3qOgeLu/8jISMyePRs+Pj44efIkYmNjVbNPpKamavxFJpFIEB0dXWqv8YsXLzB8+HA0atQIISEhePz4MU6ePInWrVtX+fkQQgh5+5w6BTx6VPp2xoCHD4vr6ZO5uTnMzc2xe/dutRmVypKTk4OPPvpINdZ0zpw5mDRpkta6U6dOxZIlS3Dx4kUYGxvjiy++0FpvwoQJ2LBhAwCoeii1mTJlChYuXIjp06cjISEBv/32m9owSQsLC0RFRSEhIQHLli3DmjVr8OOPP6rt4+7du9i9ezdiYmIQExODEydOYMGCBQCK1zrw9/dX6y2tU6eO1lji4uLQrFkzjfL8/HzMnTsXGzduxJkzZ5CdnY3+/ftr3cerioqKsGvXLvTo0QO1atXC+vXrMXDgQMTGxpb6mvj4eIjFYvj5+anK2rRpA7FYXOrMXMnJyUhLS0NISIiqTCgUIigoSOM1W7duhZ2dHRo3bowJEyZovRGudevWZS6w9i6oMWOOS4wcORIjR47Uui0qKkqjTCwWl3l34o8//qjxYSOEEEJ0VUouqHO98jI2NkZUVBSGDRuG1atXo3nz5ggKCkL//v3h7e2t9TVbt24Fx3FYs2YNTExM4OXlhcePH2PYsGEadefOnYugoCAAwOTJk9G1a1dIpVKNm57Mzc1VwydKWzcgJycHy5Ytw4oVKzBo0CAAQP369fH++++r6kybNk31fzc3N4wfPx47duzAxIkTVeVKpRJRUVGqG+jCwsJw5MgRzJ07F2KxGAKBACKR6LXrF6SkpKBJkyYa5TKZDCtWrFAlqxs3bkSjRo1w/vz5UjvRLl++jKioKPz2228QCAT49NNPcfHixVJ/Bi9LS0uDg4ODRrmDg0OpM3OVlGubzevBgweq5wMHDoS7uzucnJxw8+ZNTJkyBdeuXdMYrlqrVi1cuXLltbG+zWpcckwIIYS8ycp7y0pV3NrSu3dvdO3aFadOnUJ8fDwOHDiARYsWYe3atVrH0yYlJcHb21stwS0t6Xs5uSu5Lyc9PR1169atcJy3bt1CYWEhOnbsWGqdP//8E5GRkbhz5w5yc3Mhl8s1ZoRyc3NTm1nC2dkZ6enpFY6noKBA6zhsY2NjtGzZUvXc09MTVlZWuHXrVqnt1KtXLzx69AjTp0/HtGnTKjw/u7ZZG1g5ZuZ63WxeL//B06RJE3h4eKBly5a4fPkymjdvrtpmampa7inP3lY1algFIYQQ8qYLCABq1wZKy2U4DqhTp7heVTAxMUFwcDBmzJiBuLg4hIeHY+bMmVrraku62EuzZrzs5Tv9S16j68INpqamZW4/e/Ys+vfvj86dOyMmJgZXrlzB1KlTNeadfnX2AY7jdIrJ1tYWL1680LpNW1JaVqK6efNmhIWF4YcffkCjRo0wZ84cJCcnlysOJycn1dzuL8vIyCh1Zq6SXvGKzubVvHlz8Pl8jdm5srKyYG9vX65431aUHBNCCCF6xOMVT9cGaCbIJc8jI8ue71ifvLy8kFfK7Eaenp64fv262hjlixcvVnlMHh4eMDU1xZEjR7RuP3PmDFxdXTF16lS0bNkSHh4eakMEyksgEEBR1sor//H29sa///6rUS6Xy9XaIykpCS9evICnp2ep+woICMCGDRuQlpaGadOm4fjx4/Dw8EBgYCDWrFlTahIOAP7+/pBIJDh//ryq7Ny5c5BIJKXOzFUyVOLl4RFFRUU4ceJEmbN5/fPPP5DJZBqzc928ebPMVYLfBZQc1zRGRshu0ADprq4ArRZGCCFvpF69gD//BFxc1Mtr1y4u1zbPcWU9e/YMH3zwAbZs2YLr168jOTkZf/zxBxYtWoTu3btrfc2AAQOgVCoxfPhw3Lp1C3///Td++OEHAGX3jlaWiYkJJk2ahIkTJ2LTpk24e/cuzp49i3Xr1gEA3nvvPaSkpGD79u24e/cufvrpJ50WD3Fzc8O5c+dw//59ZGZmltqr3LFjR1y8eFEjkebz+fjmm29w7tw5XL58GYMHD0abNm3KddO+mZkZPv/8cxw5cgT37t1DSEgIFi9eXOZQkkaNGuHDDz/EsGHDcPbsWZw9exbDhg1Dt27d0LBhQ1U9T09PVXtwHIcxY8Zg3rx52LVrF27evInw8HCIRCIMGDAAQPGNi7Nnz8bFixdx//59xMbG4pNPPoGvry/atWun2m9+fj4uXbqkdnPfu4iyq5rG1BQXVq7EzsmTgdd8LUUIIbpQKBQ4efIkUlNTy9XrRrTr1QtISPj/57GxQHJy1STGQPGNcH5+fvjxxx8RGBiIJk2aYPr06Rg2bBhWrFih9TWWlpbYt28frl69Ch8fH0ydOhUzZswAgCpfkXL69OkYP348ZsyYgUaNGqFfv36q8cLdu3fH2LFj8fXXX8PHxwdxcXGYPn16hY8xYcIE8Hg8eHl5wd7evtQ5hkNCQsDn83H48GG1cpFIhEmTJmHAgAHw9/eHqampatGMiqhbty6mTZuGf//9F1u3bi2z7tatW9G0aVOEhIQgJCQE3t7e2Lx5s1qdpKQkSCQS1fOJEydizJgxGDlyJFq2bInHjx/j4MGDqvHYAoEAR44cQWhoKBo2bIhRo0YhJCQEhw8fVhsTvWfPHtStWxcBVTXmp4bgWGmDi0i5ZWdnQywWQyKRVMvy0cePH8ft27cRHh5Oy+lWE2p3w6B2N4x3vd2lUimSk5Ph7u5e6XPPywPMzYv/n5sLmJmVXpf9t5SuVCqFnZ1dta4k97KtW7di8ODBkEgkrx0b/DYoafdVq1bhyJEjOHjwIIDiGbDGjBlT5jCIt03r1q0xZswYVY9zVVMqlcjOzoalpaXe3u9lfX7Lm6/RbBWEEEJIFTEzK57X+E22adMm1KtXD7Vq1cK1a9cwadIk9O3b951IjF8WFhYGmUyGnJwctRkw3hXp6eno06cPPv30U0OHYnCUHNc0+floO3AgWstkQN++wDvWo0MIIUS/0tLSMGPGDKSlpcHZ2RmffPIJ5s6da+iwqp2xsTG+++47g/XYG5qDg4PaHNLvMkqOaxrGYPrfNC/SN707ghBCyBtv4sSJlBRpER4ernVuaPL2ezf/PCKEEEIIIUQLSo4JIYQQQgj5DyXHhBBCCCGE/IeSY0IIIYQQQv5DN+QRQgghVUShUODUqVMAipcV5lXXmtGEEJ1RclzTcBxyXV1RVFQEURUu7UkIIYQQ8i6iYRU1jUiEc+vW4fcZMwCRyNDREEIIKQNjDFKpFLm5uXjx4gXexUVpjx8/Do7j9L7SXPv27TFmzBjV8/z8fPTu3RuWlpaq47m5uSEyMlKvx9WndevWISQkxNBh1BgTJkzAqFGjqvw4lBwTQgghVSAjIwNnzpxBcnIyHjx4gBMnTuD06dPIyMiosmOGh4eD4zgsWLBArXz37t3gKvhtY3kTyytXrqBbt25wcHCAiYkJ3Nzc0K9fP2RmZgIA2rZti9TUVIjF4god/3V27tyJOXPmqJ5v3LgRp06dQlxcnOp4Fy5cwPDhw/V63NfhOA67d+9+bb3CwkLMmDED06dPr/qg9Cw6OhpeXl4QCoXw8vLCrl27XvuaGzduICgoCKampqhVqxZmz56t9sdiyR9Rrz4SExNVdSZOnIgNGzYgOTm5Ss6rBCXHhBBCiJ5lZGTg3LlzSEtLA5/Ph0gkgqmpKVJTU3Hu3LkqTZBNTEywcOFCPH/+vMqOUSI9PR2dOnWCnZ0d/v77b9y6dQvr16+Hs7Mz8vPzAQACgQBOTk4VTs5fx8bGRm2Z57t376JRo0Zo0qSJ6nj29vYQvaHfskZHR8Pc3BwBAQGV2o9UKq3S99Or4uPj0a9fP4SFheHatWsICwtD3759ce7cuVJfk52djeDgYLi4uODChQtYvnw5fvjhB/z4448adZOSkpCamqp6eHh4qLY5ODggJCQEq1evrpJzK0HJcU2Tnw+/IUPQd/Zs4L8LDyGEkDcHYwyJiYnIz8+HnZ0djI2NwXEchEIh7O3tkZ+fj8TExCobYtGpUyc4OTlh/vz5ZdaLjo5G48aNIRQK4ebmhiVLlqi2tW/fHg8ePMDYsWNVPXjaxMXFITs7G2vXroWvry/c3d3xwQcfIDIyEnXr1gWgfVjFmjVrUKdOHYhEIvTs2RNLly6FlZWVavusWbPg4+ODzZs3w83NDWKxGP3790dOTo5ajCXDKtq3b48lS5bg5MmT4DgO7du3B6DZ+/3ixQsMHz4cjo6OMDExQZMmTRATEwMAyMrKwoABA1C7dm2IRCI0bdoU27ZtUzvf9u3bY9SoUZg4cSJsbGzg5OSEWbNmqba7ubkBAHr27AmO41TPtdm+fTs+/vhjtbLw8HD06NEDERERcHBwgKWlJb788ksUFRWVup+nT5+iVq1a6NGjB3bt2lVmXX2IjIxEcHAwpkyZAk9PT0yZMgUdO3Ys81uGrVu3QiqVIioqCk2aNEGvXr3w3Xff4ccff9T4HDg4OMDJyUn1ePUm1o8//ljj56JvlBzXNIzB/MED2KSmAu/g2DVCCHnTSSQSZGZmQiwWaySVHMfB0tISmZmZkEgkVXJ8Ho+HefPmYfny5Xj06JHWOpcuXULfvn3Rv39/3LhxA7NmzcL06dMRFRUFoHjIQu3atTF79mxVD542Tk5OkMvl2LVrV7mT/TNnzmDEiBEYPXo0rl69iuDgYMydO1ej3t27d7F7927ExMQgJiYGJ06c0BguUmLnzp0YNmwY/P39kZqaip07d2rUUSqV6Ny5M+Li4rBlyxYkJCRgwYIFquSrsLAQLVq0QExMDG7evInhw4cjLCxMo0d048aNMDMzw7lz57Bo0SLMnj0bhw4dAgBcuHABALBhwwakpqaqnmtz6tQptGzZUqP8yJEjuHXrFo4dO4Zt27Zh165diIiIKHU/rq6uiI+Ph6urK7788ku4uLhg1KhRuHTpktb68+bNg7m5eZmPkhlWtImPj9cYJx0aGoq4uLgyXxMUFAShUKj2midPniAlJUWtrq+vL5ydndGxY0ccO3ZMY1+tW7fGw4cP8eDBg1KPV1kVnq0iKSkJ27Ztw6lTp3D//n3k5+fD3t4evr6+CA0NRe/evdVOnhBCCHmXFBYWQi6Xg8/na90uEAiQk5ODwsLCKouhZ8+e8PHxwcyZM7Fu3TqN7UuXLkXHjh1V410bNGiAhIQELF68GOHh4bCxsQGPx4OFhQWcnJxKPU6bNm3w3XffYcCAARgxYgRat26NDz74AJ9//jkcHR21vmb58uXo3LkzJkyYoDp2XFycqge3hFKpRFRUlGroRFhYGI4cOaI1kbaxsYFIJFIN4dDm8OHDOH/+PG7duoUGDRoAAOrVqwfGGHJycuDs7Izx48fDyKi43/Cbb77BgQMH8Mcff8DPz0+1H29vb8ycORMA4OHhgRUrVuDIkSMIDg6Gvb09AMDKyqrMdnvx4gVevHgBFxcXjW0CgQDr16+HSCRC48aNMXv2bHz77beYM2eOKrZXtWjRAi1atMCSJUuwf/9+bNq0Ce3atYOHhwcGDRqEsLAw1c9jxIgR6Nu3b6mxAUCtWrVK3ZaWlqbxs3V0dERaWlqZr3m1F71kH0+fPkXTpk3h7OyMX3/9FS1atEBhYSE2b96Mjh074vjx4wgMDNSI7f79+3B1dS3zPHRV7p7jK1euIDg4GM2aNcPJkyfRqlUrjBkzBnPmzMFnn30GxhimTp0KFxcXLFy4sEo/9IQQQsibSigUwtjYGDKZTOv2oqIiGBsbV3lH0sKFC7Fx40YkJCRobLt16xbatWunVtauXTvcvn0bCoWiQseZO3cu0tLSsHr1anh5eWH16tXw9PTEjRs3tNZPSkpC69at1cpefQ4UD1F4eUyxs7Mz0tPTKxTby65evYratWurEuNXKRQKzJs3D97e3rC1tYW5uTkOHjyo0bPp7e2t9lyXuAoKCgAUjw9/VbNmzdTGSfv7+yM3NxcPHz587X6NjY3x0Ucf4Y8//sD9+/fh7OyMb7/9Vm2IjY2NDd57770yH6ampmUe59VvRBhjrx1Tru01L5c3bNgQw4YNQ/PmzeHv74+VK1eia9eu+OGHH9ReVxJbfhUOLS13ctyjRw90794dT548wdGjR7FgwQJ88803GDp0KCZOnIhNmzYhOTkZMTExuHTpktrYJUIIIeRdIRaLYWdnB4lEojHUgDGG7Oxs2NnZ6X32hlcFBgYiNDQU3333ncY2bclMZcZA29ra4pNPPsGSJUtw69YtuLi4aCQ1FT32qz3vHMdBqVTqHOPrEr5Vq1YhMjISEydOxNGjR3H16lWEhoZqjOHVR1y2trbgOK5CN02W54ZGxhhOnjyJYcOGwdPTE7dv38aMGTMwbtw4VZ3KDqtwcnLS6CVOT08v9ZuCsl4DFI8xLk2bNm1w+/ZttbKsrCwAUPXSV4VyD6u4ffs2BALBa+v5+/vD39+/ygeEE0IIIW8ijuPg6empGnssl8vB4/FQWFiInJwciEQieHp66n32Bm0WLFgAHx8fjd5SLy8vnD59Wq0sLi4ODRo0UI3BFQgEFe5FLnld/fr1kZeXp3W7p6cnzp8/r1Z28eLFCh+nory9vfHo0SP8+++/WnuPz549i48//hifffYZgOJhHbdv30ajRo0qdBw+n//adhMIBPDy8kJCQoLG+N1r166hoKBAlcyfPXsW5ubmqF27dqn7+/fff7F582Zs2bIFmZmZ6NOnD3bv3o2goCCN91llh1X4+/vj0KFDGDt2rKrs4MGDaNu2bZmv+e6771BUVKTKJQ8ePAgXFxfVjZvaXLlyBc7OzmplN2/eBJ/PR+PGjcs8h8ood3JcnsS4MvUJIYSQt4W9vT38/PyQkJCArKwsFBYWoqCgAM7OzvD09KzSXq+XNW3aFAMHDsTy5cvVysePH49WrVphzpw56NevH+Lj47FixQqsXLlSVcfNzQ0nT55E//79IRQKYWdnp7H/mJgYbN++Hf3790eDBg3AGMO+ffsQGxuLDRs2aI3pm2++QWBgIJYuXYqPPvoIR48exf79+6v8j4WgoCAEBgaid+/eWLp0Kd577z3VHLrt2rWDu7s79u/fj7i4OFhbW2Pp0qVIS0urcHLs5uaGI0eOoF27dhAKhbC2ttZaLzQ0FKdPn1ZbyAQoHnYzZMgQTJs2DQ8ePMDMmTPx9ddflzreOCUlBY0aNUL79u0RERGB3r17w8zMrNT4bGxsYGNjU6Fzetno0aMRGBiIhQsXonv37tizZw8OHz6s9sfWihUrsGvXLhw5cgQAMGDAAERERCA8PBzfffcdbt++jXnz5mH69Omqn3tkZCTc3NzQuHFjFBUVYcuWLYiOjkZ0dLTa8U+dOoWAgIDXfhNQGTovH33kyBEcOXIE6enpGl8nrF+/vtKBkVJwHAocHSGXycCn5aMJIeSNZW9vj3bt2qGgoAByuRz+/v6wsbGplh7jl82ZMwe///67Wlnz5s3x+++/Y8aMGZgzZw6cnZ0xe/ZshIeHq+rMnj0bX375JerXr4/CwkKtQx+8vLwgEokwfvx4PHz4EEKhEB4eHli7di3CwsK0xtOuXTusXr0aERERmDZtGkJDQzF27FisWLFCr+etTXR0NCZMmIBPP/0UeXl5eO+991TjcceNG4e0tDSEhoZCJBJh+PDh6NGjR4VnFVmyZAnGjRuHNWvWoFatWrh//77WeiXjayUSidoQm44dO8LDwwOBgYEoLCxE//791aaLe5WdnR2Sk5PL7IHVp7Zt22L79u2YNm0apk+fjvr162PHjh1qNy1mZmbi7t27qudisRiHDh3CV199hZYtW8La2hrjxo3D2LFjVdPzFRUVYcKECXj8+DFMTU3RuHFj/PXXX+jSpYva8bdt21bm7B36wDEdBhlFRERg9uzZaNmyJZydnTU+6OVZKeVtkp2dDbFYDIlEAktLyyo9lkKhwPHjx3H79m2Eh4drHcxP9I/a3TCo3Q3jXW93qVSK5ORkuLu7V/rcFQqFavxmQECAxpytLyuZNUEqlcLOzq7UnsK30bBhw5CYmFjmWNeqYsh279u3L3x9fTFlyhQAxfMcv3jxolwr7L0NlEolsrOzYWlpWa52/+uvv/Dtt9/i+vXrMDbW3r9b1ue3vPmaTj3Hq1evRlRUVKl/FRJCCCGkeM7hkgUpyP/74YcfEBwcDDMzM+zfvx8bN25UG9Lxrli8eDH27t1r6DBqjLy8PGzYsKHUxFhfdNp7UVFRmQOvCSGEEEJKc/78eSxatAg5OTmoV68efvrpJwwdOtTQYVU7V1dXfPPNN4YOo8Z43Y2E+qJTcjx06FD89ttvqsnDSTUqKECrkSPRtLAQ6NcPeMe+7iSEEFLzvToGmhQrWaGQGJZOybFUKsWvv/6Kw4cPw9vbW2POv6VLl+olOKKFUgnLf/+FJQBpJeZ7JIQQQgghmnRKjq9fvw4fHx8AxfPNvay678IlhBBCqkJlFsUghBiGPj63OiXHx44dq/SBCSGEkDdRybeh+fn5VTqXKiFE/0qWlX51VENFVPp2v0ePHoHjuDJXUyGEEEJqCh6PBysrK9XytiKRqFq+FWWMoaioCDKZDFKp9J2ays2QqN0NR6lUoqioSC/tzhhDfn4+0tPTYWVlVea0ia+jU3KsVCrx/fffY8mSJcjNzQUAWFhYYPz48Zg6dSq9sQghhNRoTk5OAKBKkKsDYwyFhYWQyWSQSCQ0TLGaULsbDmNMtVS2vtrdyspK9fnVlU7J8dSpU7Fu3TosWLAA7dq1A2MMZ86cwaxZsyCVSjF37txKBUUIIYQYEsdxcHZ2hoODA2QyWbUcU6FQ4Pz583jw4AF69uwJoVBYLcd911G7G45MJsPJkycRGBhYqWEQJfh8fqV6jEvolBxv3LgRa9euxccff6wqa9asGWrVqoWRI0dSclzFisRiKBQK0N+2hBBStXg8nl5+2ZaHQqGAUqmEVCqFUCh851YmNBRqd8Ph8XiQy+UwMTHRS3KsLzolx1lZWfD09NQo9/T0RFZWVqWDImUwM8Op6OjiZV3NzAwdDSGEEELIW0WnwcHNmjXDihUrNMpXrFiBZs2aVTooQgghhBBCDEGnnuNFixaha9euOHz4MPz9/cFxHOLi4vDw4UPExsbqO0ZCCCGEEEKqhU49x0FBQfj333/Rs2dPvHjxAllZWejVqxeSkpIQEBCg7xjVrFy5Eu7u7jAxMUGLFi1w6tSpUuseP34cHMdpPBITE9XqRUdHw8vLC0KhEF5eXti1a1eVnkOlFBSg+bhx+HjpUqCgwNDREEIIIYS8VXSe59jFxaXab7zbsWMHxowZg5UrV6Jdu3b45Zdf0LlzZyQkJKBu3bqlvi4pKQmWlpaq5/b29qr/x8fHo1+/fpgzZw569uyJXbt2oW/fvjh9+jT8/Pyq9Hx0olTC+vp1WIOWjyaEEEII0bdyJ8fXr19HkyZNYGRkhOvXr5dZ19vbu9KBabN06VIMGTIEQ4cOBQBERkbi77//xqpVqzB//vxSX+fg4AArKyut2yIjIxEcHIwpU6YAAKZMmYITJ04gMjIS27Zt0/s5EEIIIYSQN1e5k2MfHx+kpaXBwcEBPj4+4DhO6/rVHMdBoVDoNUgAKCoqwqVLlzB58mS18pCQEMTFxZX5Wl9fX0ilUnh5eWHatGno0KGDalt8fDzGjh2rVj80NBSRkZGl7q+wsBCFhYWq59nZ2QCK5+ur6vkwFTIZSiYVkslk4FXT/JvvupKpfoD/2r2apnZ611G7Gwa1u2FQuxsGtbvhlORM1TWXeHmPU+7kODk5WTUcITk5WbeoKiEzMxMKhQKOjo5q5Y6OjkhLS9P6GmdnZ/z6669o0aIFCgsLsXnzZnTs2BHHjx9HYGAgACAtLa1C+wSA+fPnIyIiQqP84MGDEIlEFT21CuHy81Eyu/SxY8fAqvh4pJhSqcTTp08BAIcPH6ZVIKsJtbthULsbBrW7YVC7G96hQ4eq5Tj5+fnlqlfu5NjV1VX1/wcPHqBt27YwNlZ/uVwuR1xcnFpdfXt1eUHGWKlLDjZs2BANGzZUPff398fDhw/xww8/qJLjiu4TKB56MW7cONXz7Oxs1KlTByEhIWpjm6uC4r9eagDo0KEDTGxtq/R4pJhCocCpU6dw9+5ddOrUiSaJrybU7oZB7W4Y1O6GQe1uODKZDIcOHUJwcHC1LAKS/VIOVRadbsjr0KEDUlNT4eDgoFYukUjQoUOHKhlWYWdnBx6Pp9Gjm56ertHzW5Y2bdpgy5YtqudOTk4V3qdQKNS6vCSfz6/yH67RS/uvjuORYkZGRqreBGr36kPtbhjU7oZB7W4Y1O6GV13tXt5j6PTdQWk9q8+ePYNZFa3aJhAI0KJFC42u90OHDqFt27bl3s+VK1fg7Oyseu7v76+xz4MHD1Zon9VNYWICmUBg6DAIIYQQQt46Feo57tWrF4DiYQjh4eFqvacKhQLXr1+v0qRy3LhxCAsLQ8uWLeHv749ff/0VKSkpGDFiBIDi4Q6PHz/Gpk2bABTPROHm5obGjRujqKgIW7ZsQXR0NKKjo1X7HD16NAIDA7Fw4UJ0794de/bsweHDh3H69OkqO49KMTPD8ZgYWj6aEEIIIaQKVCg5FovFAIp7ji0sLGBqaqraJhAI0KZNGwwbNky/Eb6kX79+ePbsGWbPno3U1FQ0adIEsbGxqjHOqampSElJUdUvKirChAkT8PjxY5iamqJx48b466+/0KVLF1Wdtm3bYvv27Zg2bRqmT5+O+vXrY8eOHW/mHMeEEEIIIaRKVSg53rBhAwDAzc0N3377bZXPzKDNyJEjMXLkSK3boqKi1J5PnDgREydOfO0++/Tpgz59+ugjPEIIIYQQUoPpNOb4888/x+PHjzXKb9++jfv371c2JlIWqRTNvvsOnX/+GZBKDR0NIYQQQshbRafkODw8XOvCG+fOnUN4eHhlYyJlUShgd/48XG/eBKpgVhBCCCGEkHeZTsnxlStX0K5dO43yNm3a4OrVq5WNiRBCCCGEEIPQKTnmOA45OTka5RKJpErmOCaEEEIIIaQ66JQcBwQEYP78+WqJsEKhwPz58/H+++/rLThCCCGEEEKqk04r5C1atAiBgYFo2LAhAgICAACnTp1CdnY2jh49qtcACSGEEEIIqS469Rx7eXnh+vXr6Nu3L9LT05GTk4PPP/8ciYmJaNKkib5jJC9hjOGFlRWeOjhAkp0NxpihQyKEEEIIeWvo1HMMAC4uLpg3b54+YyGvkZGRgYSrV5HVvj3kfD64c+fg+OgRPD09YW9vb+jwCCGEEEJqPJ2TYwDIz89HSkoKioqK1Mq9vb0rFRTRlJGRgXPnziEvLw+Szp0hkUjgZWGB1NRUSCQS+Pn5UYJMCCGEEFJJOiXHGRkZGDx4MPbv3691O81YoV+MMSQmJiI/Px92dnbIy8sDx3EQCoUQiUTIyMhAYmIi7OzswHGcocMlhBBCCKmxdBpzPGbMGDx//hxnz56FqakpDhw4gI0bN8LDwwN79+7Vd4zvPIlEgszMTIjFYo3kl+M4WFpaIjMzExKJxEAREkIIIYS8HXTqOT569Cj27NmDVq1awcjICK6urggODoalpSXmz5+Prl276jvOd1phYSHkcjn4fD4gl8Ph5ElYFRUBTZoAxsYQCATIyclBYWGhoUMlhBBCCKnRdOo5zsvLg4ODAwDAxsYGGRkZAICmTZvi8uXL+ouOAACEQiGMjY0hk8kAxmD+4AFsUlMBpRIAUFRUBGNjYwiFQgNHSgghhBBSs+mUHDds2BBJSUkAAB8fH/zyyy94/PgxVq9eDWdnZ70GSACxWAw7OztIJBKNqdsYY8jOzoadnR3EYrGBIiSEEEIIeTvoNKxizJgxSE1NBQDMnDkToaGh2Lp1KwQCAaKiovQZH0HxuGJPT8/iscdZWRALhRAUFaGwsBB5ubkQiUTw9PSkm/EIIYQQQipJp+R44MCBqv/7+vri/v37SExMRN26dWFnZ6e34Mj/s7e3h5+fX/E8x6amyLGwAFdYCOdatWieY0IIIYQQPalwciyTydCwYUPExMTAy8sLACASidC8eXO9B0fU2dvbo13r1sjt2xeFAgGwaBEc3Nyox5gQQgghRE8qnBzz+XwUFhZSQmYgHMfB6sULAIDU0pJ+DoQQQggheqTTDXnffPMNFi5cCLlcru94CCGEEEIIMRidxhyfO3cOR44cwcGDB9G0aVOYmZmpbd+5c6degiNaiEQ4tm8f7t69i89EIkNHQwghhBDyVtEpObayskLv3r31HQspD46D0tQUcqEQoCEVhBBCCCF6pVNyvGHDBn3HQQghhBBCiMHpNOYYAORyOQ4fPoxffvkFOTk5AIAnT54gNzdXb8ERLQoL0WjRInTYuBGg5aIJIYQQQvRKp57jBw8e4MMPP0RKSgoKCwsRHBwMCwsLLFq0CFKpFKtXr9Z3nKSEXA6XgwcBAFK6IZIQQgghNZBCocDJkyeRmpoKhUIBPp9v6JBUdOo5Hj16NFq2bInnz5/D1NRUVd6zZ08cOXJEb8ERQgghhBBSnXTqOT59+jTOnDkDgUCgVu7q6orHjx/rJTBCCCGEEEKqm049x0qlEgqFQqP80aNHsLCwqHRQhBBCCCGEGIJOyXFwcDAiIyNVzzmOQ25uLmbOnIkuXbroKzZCyDuOMQapVAqZTAaJRALGmKFDIoQQ8pbTaVjFjz/+iA4dOsDLywtSqRQDBgzA7du3YWdnh23btuk7RkLIOygjIwMJCQl48OAB8vLycOrUKTg6OsLT0xP29vaGDo8QQshbSqfk2MXFBVevXsX27dtx6dIlKJVKDBkyBAMHDlS7QY8QQnSRkZGBc+fOIS8vD8bGxuDxeDA1NUVqaiokEgn8/PwoQSaEEFIldEqOT548ibZt22Lw4MEYPHiwqlwul+PkyZMIDAzUW4DkFSIRTv75J+7du4f+tHw0eQsxxpCYmIj8/HzY2dkhLy8PHMdBKBRCJBIhIyMDiYmJsLOzA0erRBJCCNEzncYcd+jQAVlZWRrlEokEHTp0qHRQpAwcB5mVFaQWFrR8NHkrSSQSZGZmQiwWayS/HMfB0tISmZmZkEgkBoqQEELI20yn5JgxprXH5tmzZzAzM6t0UISQd1dhYSHkcnmpE8ILBALI5XIU0gqRhBBCqkCFhlX06tULQHHvTXh4OIRCoWqbQqHA9evX0bZtW/1GSNQVFqLhTz/B8cUL4NNPARMTQ0dEiF4JhUIYGxtDJpNpTZCLiopgbGysdv0hhBBC9KVCybFYLAZQ3HNsYWGhdvOdQCBAmzZtMGzYMP1GSNTJ5ai9dy9qg5aPJm8nsVgMOzs7pKamwtbWVm0bYwzZ2dlwdnZWXY8IIYQQfapQcrxhwwYAgJubGyZMmEBDKAghesdxHDw9PVVjj+VyORhjKCwsRF5eHkQiETw9PelmPEIIIVVCpzHHM2fOpMSYEFJl7O3t4efnBycnJ8jlcigUChQUFMDZ2ZmmcSOEEFKldEqOnz59irCwMLi4uKjmIH35QQghlWVvb4927drB1dUVZmZmCAgIwPvvv0+JMSGEkCql0zzH4eHhSElJwfTp0+Hs7ExfbxJCqgTHcTAxMQGfz9c6tRshhBCibzolx6dPn8apU6fg4+Oj53AIIYQQQggxHJ2GVdSpUweMMX3HUi4rV66Eu7s7TExM0KJFC5w6darUujt37kRwcDDs7e1haWkJf39//P3332p1oqKiwHGcxkMqlVb1qRBCCCGEkDeMTslxZGQkJk+ejPv37+s5nLLt2LEDY8aMwdSpU3HlyhUEBASgc+fOSElJ0Vr/5MmTCA4ORmxsLC5duoQOHTrgo48+wpUrV9TqWVpaIjU1Ve1h8qbOH2xqijNbtmDr998DL02lRwghhBBCKk+nYRX9+vVDfn4+6tevD5FIpDFRv7alpfVh6dKlGDJkCIYOHQqgOEn/+++/sWrVKsyfP1+jfmRkpNrzefPmYc+ePdi3bx98fX1V5RzHwcnJqUpi1jsjI0idnJCTkwMY6fS3DSGEEEIIKYVOyfGrSWd1KCoqwqVLlzB58mS18pCQEMTFxZVrH0qlEjk5ObCxsVErz83NhaurKxQKBXx8fDBnzhy15PlVhYWFakvXZmdnAwBkMhlkMll5T0knCoUCSqVSdTyaHaR6ULsbBrW7YVC7G4ZcLkd+fj5kMhkyMzPh4OBAN6FWA3q/G4Yh2r28OZpOyfGgQYN0eVmlZGZmQqFQwNHRUa3c0dERaWlp5drHkiVLkJeXh759+6rKPD09ERUVhaZNmyI7OxvLli1Du3btcO3aNXh4eGjdz/z58xEREaFRfvDgQYhEogqcVcWxwkK4rVkDewBHbGzA0RK61UKpVOLp06cAgMOHD8OIeu2rBbW7YVC7Vz+pVIrnz5/jxYsXAIDo6GiYmJjAysrqzR3m95ag97thGKLd8/Pzy1WvQslxSQ/p61haWlZktxXy6l/RjLFy/WW9bds2zJo1C3v27IGDg4OqvE2bNmjTpo3qebt27dC8eXMsX74cP/30k9Z9TZkyBePGjVM9z87ORp06dRASElKl5w4AiuxsmPTrBwCov3kzTF5ZXpdUDYVCgVOnTuHu3bvo1KkT/bKqJtTuhkHtXr0yMzNx4cIF1e+z7OxsuLu7Iz8/H6ampmjVqhXs7OwMHeZbi97vhmGIdi9vHluh5NjKyqrMRLTkg61QKCqy23Kxs7MDj8fT6CVOT0/X6E1+1Y4dOzBkyBD88ccf6NSpU5l1jYyM0KpVK9y+fbvUOkKhEEItPbZ8Pl9j/LW+Gb20/+o4HilmZGSk+quW2r36ULsbBrV79WGM4c6dO5BKpbC3t0d+fj44joNIJIKFhQUyMjJw584dODk50RCLKkLvd8MwRLuX9xgVSo6PHTumUzD6IBAI0KJFCxw6dAg9e/ZUlR86dAjdu3cv9XXbtm3DF198gW3btqFr166vPQ5jDFevXkXTpk31Ejd5OzDGIJVKIZPJIJFIIBQK6RcVIaTSJBIJMjMztS5yw3EcLC0tkZmZCYlEAisrK8MEScg7pkLJcVBQUFXFUS7jxo1DWFgYWrZsCX9/f/z6669ISUnBiBEjABQPd3j8+DE2bdoEoDgx/vzzz7Fs2TK0adNG1etsamoKsVgMAIiIiECbNm3g4eGB7Oxs/PTTT7h69Sp+/vlnw5wkeeNkZGQgISEBDx48QF5eHk6dOgVHR0d4enrSUsaEkEopLCyEXC4vtUdLIBAgJydH7SZwQkjV0umGPEPp168fnj17htmzZyM1NRVNmjRBbGwsXF1dAQCpqalqcx7/8ssvkMvl+Oqrr/DVV1+pygcNGoSoqCgAwIsXLzB8+HCkpaVBLBbD19cXJ0+eROvWrav13MibKSMjA+fOnUNeXh6MjY3B4/FgamqK1NRUSCQS+Pn5UYJMCNGZUCiEsbExZDKZ1gS5qKgIxsbGWofyEUKqRo1KjgFg5MiRGDlypNZtJQlviePHj792fz/++CN+/PFHPURG3jaMMSQmJiI/Px92dnbIy8sDx3EQCoUQiUTIyMhAYmIi7OzsaIgFIUQnYrEYdnZ2SE1Nhe0rN1gzxpCdnQ1nZ2fVt52EkKpH85UQUoqKjAUkhBBdcBwHT09PiEQiZGZmQi6XgzGGwsJCZGRkQCQSwdPTk/4AJ6QalTs5vn79umqyZmJApqY4u3YtdkyfTstHV7HyjAWUy+U0FpAQUin29vbw8/ODk5MT5HI5FAoFCgoK4OzsTEO3CDGAcifHvr6+yMzMBADUq1cPz549q7KgSBmMjJDn5obnLi60fHQVe3ksoDY0FpAQoi/29vZo164dXF1dYWZmhoCAALz//vuUGBNiAOXOrqysrJCcnAwAuH//PvUik7deyVhAiUQCxpjatpKxgHZ2djQWkBCiFxzHwcTEBHw+X+twLkJI9Sj3DXm9e/dGUFAQnJ2dwXEcWrZsWeo62Pfu3dNbgOQVRUVw37gR4qwsYMAAgFbyqTIlYwFLxh6/PBYwLy+PxgISQgghb6FyJ8e//vorevXqhTt37mDUqFEYNmwYLCwsqjI2oo1MhnqbN6MeAGkpX/cT/SkZC5iQkIBnz56pjQWkeY4JIYSQt0+FpnL78MMPAQCXLl3C6NGjKTkm74SSsYC5ubm4d+8eAgIC4ODgQD3GhBBCyFtIp3mON2zYoPr/o0ePwHEcatWqpbegCHnT0FhAQggh5N2g03QHSqUSs2fPhlgshqurK+rWrQsrKyvMmTOHbtQjhBBCCCE1lk49x1OnTsW6deuwYMECtGvXDowxnDlzBrNmzYJUKsXcuXP1HSchhBBCCCFVTqfkeOPGjVi7di0+/vhjVVmzZs1Qq1YtjBw5kpJjQgghhBBSI+k0rCIrKwuenp4a5Z6ensjKyqp0UIQQQgghhBiCTslxs2bNsGLFCo3yFStWoFmzZpUOipTBxATnV6xA9KRJNMcxIYQQUkmMMUilUshkMq2LPpF3j07DKhYtWoSuXbvi8OHD8Pf3B8dxiIuLw8OHDxEbG6vvGMnLeDzkeHoig8cDSlmEhRBCCCGvl5GRgYSEBDx48AB5eXk4deoUHB0daR77d5xOPcdBQUH4999/0bNnT7x48QJZWVno1asXkpKSEBAQoO8YCSGEVCPqSSPvgoyMDJw7dw5paWkwNjYGj8eDqakpUlNTce7cOWRkZBg6RGIgOvUcA4CLiwvdeGcIRUWou2MHRJmZtHw0IUTvqCeNvAsYY0hMTER+fj7s7OyQl5cHjuMgFAohEomQkZGBxMRE2NnZ0bz27yCdeo6JAclk8FizBv67dgG0fDQhRI+oJ428KyQSCTIzM7Uu6sRxHCwtLZGZmQmJRGKgCIkhUXJMCCFEoyfN2NhY1ZNmb2+P/Px8JCYm0hAL8lYoLCyEXC4Hn8/Xul0gEEAul6OwsLCaIyNvAkqOCSGEUE8aeacIhUIYGxtDVso3sEVFRTA2NoZQKKzmyMibgJJjQggh1JNG3ilisRh2dnZabzhljCE7Oxt2dnYQi8UGipAYkk7J8QcffIAXL15olGdnZ+ODDz6obEyEEEKqGfWkkXcJx3Hw9PSESCRCZmYm5HI5GGMoLCxERkYGRCIRPD096Wa8d5ROyfHx48dRVFSkUS6VSnHq1KlKB0UIIaR6UU8aedfY29vDz88PTk5OkMvlUCgUKCgogLOzM/z8/Gh2lndYhaZyu379uur/CQkJSEtLUz1XKBQ4cOAAatWqpb/oCCGEVIuSnrSSsccv96Tl5eVRTxp5K9nb26Ndu3bIzc3FvXv3EBAQAAcHB3qfv+MqlBz7+PiA4zhwHKd1+ISpqSmWL1+ut+CIFiYmuPTDD3j8+DFCaI5jQogelfSkJSQk4NmzZ2o9aTTPMXlbcRwHExMT8Pl8rTekkndPhZLj5ORkMMZQr149nD9/Xu1CKRAI4ODgAB4taVy1eDy88PHBEzMzWj6aEKJ31JNGCHnXVSg5dnV1BQAolcoqCYYQQojhUU8aIeRdptMNefPnz8f69es1ytevX4+FCxdWOihSBpkMtffsQePjx2mFPEIIIYQQPdMpOf7ll1/g6empUd64cWOsXr260kGRMhQVoeHy5QjYsQPQMmMIIYQQQgjRnU7JcVpaGpydnTXK7e3tkZqaWumgCCGEEEIIMQSdkuM6dergzJkzGuVnzpyBi4tLpYMihBBCCCHEECp0Q16JoUOHYsyYMZDJZKop3Y4cOYKJEydi/Pjxeg2QEEIIIYSQ6qJTcjxx4kRkZWVh5MiRqpXyTExMMGnSJEyZMkWvARJCCCGEEFJddEqOOY7DwoULMX36dNy6dQumpqbw8PCAUCjUd3yEEEIIIYRUG53GHJdIS0tDVlYW6tevD6FQCMaYvuIihBBCCCGk2unUc/zs2TP07dsXx44dA8dxuH37NurVq4ehQ4fCysoKS5Ys0XecpIRQiKvff48nT57gA+qpJ4QQQgjRK516jseOHQs+n4+UlBSIRCJVeb9+/XDgwAG9BUe0MDbGszZtkNK0KWCs0982hBBCCCGkFDplVwcPHsTff/+N2rVrq5V7eHjgwYMHegmMEEIIIYSQ6qZTz3FeXp5aj3GJzMxMuimvqslkcP77bzSMj6flowkhhBBC9Eyn5DgwMBCbNm1SPec4DkqlEosXL0aHDh30FhzRoqgIXosXo8OmTbR8NCGEEEKInuk0rGLx4sVo3749Ll68iKKiIkycOBH//PMPsrKytK6cRwghhBBCSE2gU8+xl5cXrl+/jtatWyM4OBh5eXno1asXrly5gvr16+s7RjUrV66Eu7s7TExM0KJFC5w6darM+idOnECLFi1gYmKCevXqYfXq1Rp1oqOj4eXlBaFQCC8vL+zatauqwieEEEIIIW+wcifHvXr1QnZ2NgBg06ZNsLa2RkREBGJiYhAbG4vvv/8ezs7OVRYoAOzYsQNjxozB1KlTceXKFQQEBKBz585ISUnRWj85ORldunRBQEAArly5gu+++w6jRo1CdHS0qk58fDz69euHsLAwXLt2DWFhYejbty/OnTtXpedCCCGEEELePOVOjmNiYpCXlwcAGDx4MCQSSZUFVZqlS5diyJAhGDp0KBo1aoTIyEjUqVMHq1at0lp/9erVqFu3LiIjI9GoUSMMHToUX3zxBX744QdVncjISAQHB2PKlCnw9PTElClT0LFjR0RGRlbTWRFCCCGEkDdFuccclySOHTp0AGMMv//+OywtLbXW/fzzz/UWYImioiJcunQJkydPVisPCQlBXFyc1tfEx8cjJCRErSw0NBTr1q2DTCYDn89HfHw8xo4dq1GnrOS4sLAQhYWFquclPeqPHj2ChYWFqtzExARWVlaQy+XIzMzU2I+TkxOA4kVVZK/MPCEWi2Fqaoq8vDzk5OSoypU5OXB9KY6Mhw819mtvbw8ej4fnz5+rxQkA5ubmMDc3R0FBgcYfOMbGxrCzswNQvPrhq2xtbcHn8yGRSFBQUKC2zczMDBYWFigsLMTz58/VthkZGcHBwQEAkJ6eDqVSqbbd2toaQqEQOTk5qj/ASpiamkIsFkMmk+HZs2caMZW0YWZmJuRyudq2kjbMzc1Fbm6u2jahUAhra2soFApkZGRo7NfBwQFGRkbIyspCUVERFAoFsrOzIZPJIJFIwOPxtLYhn8+Hra0tAO1taGdnB2NjY7x48QJSqVRtW8nPRlsb8ng82NvbA9DehjY2NhAIBMjOzkZ+fr7atrLakOM4ODo6AgAyMjKgUCjUtltZWcHExESnNnR0dATHcVrf35aWlhCJRMjPz1d9fkqUtCFjDE+ePFG1+6NHjyAUCsv1/pZKpXjx4kWpbfj06VONFT3Len+LRCJYWlqiqKgIWVlZattefn9ra8OS97e2NqyKawQACAQC2NjYQKlUIj09XWO/r2tDU1NTFBUVqbU7QNeIl716jXiZhYUFzMzMKnyNUCgUqhgzMzM13qN0jSj28jXi6dOnGvut6DWi5Poul8shk8nA4/HoGlENeURubq7a9d3GxqbKrxGPHj3SiEmbcifHq1atwvjx4/HXX3+B4zhMmzYNHMdp1OM4rkqS48zMTCgUCtWHtISjo6PWHwBQ/IPRVr/kTebs7FxqndL2CQDz589HRESERvnmzZthYmKiem5tbQ1XV1cUFhbi1q1bGvV9fHwAAP/++6/Gxapu3bqwsbFBRkYGHj9+rCrnFxXhu//+f/ToUVy/e1djv02aNIGxsTHu3buncVFxcXGBg4MDXrx4gfv376ttMzU1RcOGDQEA165d07gwNGzYEKampkhJSdH48Ds4OMDFxQU5OTm4+0pMfD4fjRs3BgD8888/Gh/g+vXrw8LCAk+ePNH4kNrY2KBu3booKChAUlKS2jaO49CsWTMAQFJSksbFys3NDVZWVkhPT8eTJ0/UtllaWqJevXqQy+W4efMmXtW0aVPweDzcvXtX46Kyd+9eODg4ICsrS2NIj0gkQoMGDQAAV69e1dhvo0aNIBQK8eDBA40Pv6OjI5ydnZGdnY179+6pbRMIBPDy8gIA3LhxQ+Pi6uHhATMzMzx+/Fjjl5CdnR1q166N/Px8/Pvvv2rbjIyM4O3tDQBITEzUSNjd3d0hFovx9OlTpKamqm0Ti8Vwd3dHUVEREhISNM7V29sbRkZGuH37tsbFqk6dOrC1tcWzZ8/w8JU/8MzMzODh4QGlUonr16+ryrdu3Qqg+J4HgUCA5ORkjQuzs7MzHB0dIZFIkJycrLbNxMQEnp6eAIDr169rXFwbNGgAkUiER48eafwSsre3R61atZCXl4fbt2+rbePxeGjatCkAICEhQSNRqlevHiwtLZGamqrxi7wqrhFAcXJWv359KBQK3LhxQ2O/r7tG2NnZ4cGDB5BIJKp2B+ga8bKyrhG1atWCvb29TtcIW1tbGBsbIzo6WuMPPLpGFCvtGlFC12sEj8fD4cOHYWRkRNeIas4jtm7dWi3XiGvXrmmcqzYcezXycjAyMkJaWpoqi68OT548Qa1atRAXFwd/f39V+dy5c7F582YkJiZqvKZBgwYYPHgwpkyZoio7c+YM3n//faSmpsLJyQkCgQAbN27Ep59+qqqzdetWDBkyROMiUEJbz3GdOnXwzz//VHnPsaKwEBlr1uB5Vhb8FixA9isfBoB6jkvou+f4ypUrePToEfr27QsbGxvqOa6mnuOSdu/cuTP1HL+kqnuOjx07hjt37qjaHaBrxMuqquc4KSkJycnJ6NatG/UcV2PP8ZUrV/D48WMMGjQIJiYmdI2opp7jl6/v1dFzfP/+fTRu3BgSiaTU0Q8AAFZOPXv2ZBKJhDHGWFRUFMvPzy/vS/WisLCQ8Xg8tnPnTrXyUaNGscDAQK2vCQgIYKNGjVIr27lzJzM2NmZFRUWMMcbq1KnDli5dqlZn6dKlrG7duuWOTSKRMACq9qlKcrmcHT58mK1atYoVFBRU+fFIMWp3w6B2Nwxqd8OgdjcManfDMES7lzdf0+mGvC+++ELjr5CqJhAI0KJFCxw6dEit/NChQ2jbtq3W1/j7+2vUP3jwIFq2bAk+n19mndL2SQghhBBC3l415oY8ABg3bhzCwsLQsmVL+Pv749dff0VKSgpGjBgBAJgyZQoeP36sWr1vxIgRWLFiBcaNG4dhw4YhPj4e69atw7Zt21T7HD16NAIDA7Fw4UJ0794de/bsweHDh3H69OkqOYdKk8vhcOIEFKmpwGefGToaQgghhJC3SrmT49WrV2PcuHEGuyEPAPr164dnz55h9uzZSE1NRZMmTRAbGwtX1+L5G1JTU9VufnB3d0dsbCzGjh2Ln3/+GS4uLvjpp5/Qu3dvVZ22bdti+/btmDZtGqZPn4769etjx44d8PPzq5JzqLTCQjSdMwdNAUgXLADMzQ0dESGEEELIW6PcyXHbtm1x9uxZAMUDo//9999qvSGvxMiRIzFy5Eit26KiojTKgoKCcPny5TL32adPH/Tp00cf4RFCCCGEkBpMp+Wjk5OTVXfFEkIIIYQQ8raoUHK8aNEiFBQUwNXVFRzH4eTJk2pTfOTk5JTaq0sIIYQQQsibrkLJ8ZQpU9RmqejWrZvaxNL5+fn45Zdf9BcdIYQQQggh1ahCyTF7ZULsV58TQgghhBBSk+k05pgQQgghhJC3UblnqyBvCIEACd9+i6dPn8JfIDB0NIQQQgghb5UKJ8dr166F+X9z68rlckRFRanW0a7uVfPeSXw+UkNDcfv2bfj/t8ofIYQQQgjRjwolx3Xr1sWaNWtUz52cnLB582aNOoQQQgghhNREFUqO79+/X0VhkHKTy2F79iwKnzwB5HJDR0MIIYQQ8lahG/JqmsJC+Eybhi4rVwIvzTFNCCGEEEIqr9zJ8fbt28u904cPH+LMmTM6BUQIIYQQQoihlDs5XrVqFTw9PbFw4ULcunVLY7tEIkFsbCwGDBiAFi1aICsrS6+BEkIIIYQQUtXKPeb4xIkTiImJwfLly/Hdd9/BzMwMjo6OMDExwfPnz5GWlgZ7e3sMHjwYN2/ehIODQ1XGTQghhBBCiN5V6Ia8bt26oVu3bnj27BlOnz6N+/fvo6CgAHZ2dvD19YWvry+MjGgYMyGEEEIIqZl0WgTE1tYW3bt313cshBBCCCGEGBR189YwPB5P6/8JIYQQQkjl0fLRNY1AAMWyZfjnn3/QiJaPJoQQQgjRK+o5rmn4fCj/9z8kd+kC0PLRhBBCCCF6RckxIYQQQggh/9EpOZ49ezby8/M1ygsKCjB79uxKB0XKoFCAO3ECtjduAAqFoaMhhBBCCHmr6JQcR0REIDc3V6M8Pz8fERERlQ6KlEEqhXFwMN6fPh2QSg0dDSGEEELIW0Wn5JgxBo7jNMqvXbsGGxubSgdFCCGEEEKIIVRotgpra2twHAeO49CgQQO1BFmhUCA3NxcjRozQe5CEEEIIIYRUhwolx5GRkWCM4YsvvkBERATEYrFqm0AggJubG/z9/fUeJCGEEEIIIdWhQsnxoEGDAADu7u5o27Yt+DSVGCGEEEIIeYvotAhIUFAQlEol/v33X6Snp0OpVKptDwwM1EtwhBBCCCGEVCedkuOzZ89iwIABePDgARhjats4joOCphgjhBBCCCE1kE7J8YgRI9CyZUv89ddfcHZ21jpzBakifD4U8+cjMTERDWhYCyGEEEKIXumUHN++fRt//vkn3nvvPX3HQ15HIIBy/HjciY1FA4HA0NEQQgghhLxVdJrn2M/PD3fu3NF3LIQQQgghhBiUTj3H33zzDcaPH4+0tDQ0bdpUY9YKb29vvQRHtFAowF28CKvbt4uXj6ahFYQQQggheqNTcty7d28AwBdffKEq4zhOtXIe3ZBXhaRSGLdtiyAAsqFDARMTQ0dECCGEEPLW0Ck5Tk5O1ncchBBCCCHkHcEYg1QqhUwmg0QigVAofGMmeNApOXZ1ddV3HIQQQggh5B2QkZGBhIQEPHjwAHl5eTh16hQcHR3h6ekJe3t7Q4en2w15ALB582a0a9cOLi4uePDgAYDi5aX37Nmjt+AIIYQQQsjbIyMjA+fOnUNaWhqMjY3B4/FgamqK1NRUnDt3DhkZGYYOUbfkeNWqVRg3bhy6dOmCFy9eqMYYW1lZITIyUp/xEUIIIYSQtwBjDImJicjPz4ednR2MjY3BcRyEQiHs7e2Rn5+PxMREjQXmqptOyfHy5cuxZs0aTJ06FTweT1XesmVL3LhxQ2/BEUIIIYSQt4NEIkFmZibEYrHG+GKO42BpaYnMzExIJBIDRVhMp+Q4OTkZvr6+GuVCoRB5eXmVDooQQgghhLxdCgsLIZfLNaYALiEQCCCXy1FYWFjNkanTKTl2d3fH1atXNcr3798PLy+vysZEysLnQzFtGhL79aM5jgkhhBBSYwiFQhgbG0Mmk2ndXlRUBGNjYwiFwmqOTJ1Os1V8++23+OqrryCVSsEYw/nz57Ft2zbMnz8fa9eu1XeM5GUCAZQzZiApNhb1afloQgghhNQQYrEYdnZ2SE1Nha2trdo2xhiys7Ph7OwMsVhsoAiL6dRzPHjwYMycORMTJ05Efn4+BgwYgNWrV2PZsmXo37+/vmMEADx//hxhYWEQi8UQi8UICwvDixcvSq0vk8kwadIkNG3aFGZmZnBxccHnn3+OJ0+eqNVr3749OI5Te1TVORBCCCGEvKs4joOnpydEIhEyMzMhl8vBGENhYSEyMjIgEong6elp8PmOdZ7KbdiwYXjw4AHS09ORlpaGhw8fYsiQIfqMTc2AAQNw9epVHDhwAAcOHMDVq1cRFhZWav38/HxcvnwZ06dPx+XLl7Fz5078+++/+Pjjj7WeS2pqqurxyy+/VNl5VJpSCfzzDyxSUor/TwghhBBSQ9jb28PPzw9OTk6Qy+VQKBQoKCiAs7Mz/Pz83oh5jnUaVvEyOzs7fcRRplu3buHAgQM4e/Ys/Pz8AABr1qyBv78/kpKS0LBhQ43XiMViHDp0SK1s+fLlaN26NVJSUlC3bl1VuUgkgpOTU9WehL4UFIDv64sPAMjCwgADj8shhBBCCKkIe3t7tGvXDrm5ubh37x4CAgLg4OBg8B7jEuVOjps3b44jR47A2toavr6+ZZ7A5cuX9RJcifj4eIjFYlViDABt2rSBWCxGXFyc1uRYG4lEAo7jYGVlpVa+detWbNmyBY6OjujcuTNmzpwJCwuLUvdTWFiodidldnY2gOKhHKUNMtcbmQx81X9lQFUfjwAAFAoFlP/11MtkMrUpDEnVoXY3DGp3w6B2Nwxqd8NQKBQQCATg8/kQiUSQy+VVfszy5mjlTo67d++uunuwR48eOgWlq7S0NDg4OGiUOzg4IC0trVz7kEqlmDx5MgYMGABLS0tV+cCBA+Hu7g4nJyfcvHkTU6ZMwbVr1zR6nV82f/58REREaJQfPHgQIpGoXPHoiieVott//z969CgUJiZVejxSTKlU4unTpwCAw4cPw8hI5xFJpAKo3Q2D2t0wqN0Ng9rdMAzR7vn5+eWqxzEDLkMya9YsrUnmyy5cuICDBw9i48aNSEpKUtvm4eGBIUOGYPLkyWXuQyaT4ZNPPkFKSgqOHz+ulhy/6tKlS2jZsiUuXbqE5s2ba62jree4Tp06yMzMLHPfepGXB761NQAgPz0d/Fd6wUnVUCgUOHXqFO7evYuBAwfChP4oqRbU7oZB7W4Y1O6GQe1uGIZo9+zsbNjZ2UEikZSZr+k05vjChQtQKpVqwxwA4Ny5c+DxeGjZsmW59vP111+/dmYINzc3XL9+XfXXxcsyMjLg6OhY5utlMhn69u2L5ORkHD169LXJa/PmzcHn83H79u1Sk2OhUKh1Dj4+n1/qxNZ689L+q+V4BABgZGSk+quW2r36ULsbBrW7YVC7Gwa1u2EYot3LewydkuOvvvoKEydO1EiOHz9+jIULF+LcuXPl2o+dnV25bujz9/eHRCLB+fPn0bp1awDFibhEIkHbtm1LfV1JYnz79m0cO3ZMY049bf755x/IZDI4OzuX6xwIIYQQQsjbQ6cBHgkJCVp7VX19fZGQkFDpoF7VqFEjfPjhhxg2bBjOnj2Ls2fPYtiwYejWrZvazXienp7YtWsXAEAul6NPnz64ePEitm7dCoVCgbS0NKSlpaGoqAgAcPfuXcyePRsXL17E/fv3ERsbi08++QS+vr5o166d3s+DEEIIIYS82XRKjoVCodZhDqmpqTA2rvTscFpt3boVTZs2RUhICEJCQuDt7Y3Nmzer1UlKSoJEIgEAPHr0CHv37sWjR4/g4+MDZ2dn1SMuLg5A8RreR44cQWhoKBo2bIhRo0YhJCQEhw8ffnPvVuXzoRg3Drd79KDlowkhhBBC9EynTDY4OBhTpkzBnj17VEv8vXjxAt999x2Cg4P1GmAJGxsbbNmypcw6L99b6Obmhtfda1inTh2cOHFCL/FVG4EAygULkBAbCzdaPpoQQgghRK90So6XLFmCwMBAuLq6wtfXFwBw9epVODo6avTmEkKIrng8HgIDA5Gbm/vmfptDCCHkraJTclyrVi1cv34dW7duxbVr12BqaorBgwfj008/pbs8q5pSCdy/D9OnT2n5aEIIIYQQPdN5gLCZmRmGDx+uz1hIeRQUgN+gAUIAyPr2peWjCSGEEEL0qNzJ8d69e9G5c2fw+Xzs3bu3zLoff/xxpQMjhBBCCCGkupU7Oe7Ro4dqGeeylo/mOA4KhUIfsRFCCCGEEFKtyp0cK18a36qksa6EEEIIIeQtVO55jm1sbJCZmQkA+OKLL5CTk1NlQRFCCCGEEGII5U6Oi4qKkJ2dDQDYuHEjpFJplQVFCCGEEEKIIZR7WIW/vz969OiBFi1agDGGUaNGwdTUVGvd9evX6y1AQgghhBBCqku5k+MtW7bgxx9/xN27dwEAEomEeo8NwdgYihEjkPLgAWpX0VLdhBBCCCHvqnJnV46OjliwYAEAwN3dHZs3b4atrW2VBUZKIRRC+dNPuB4bi9o0xzEhhBBCiF7pdENehw4dIBAIqiwoQgghhBBCDIFuyKtpGAMyMiCQSIr/TwghhBBC9IZuyKtp8vPBr1ULnQHIPv4YoB58QgghhBC90emGPI7j6IY8QgghhBDy1qEb8gghhBBCCPmPTnOBJScnq/4vlUphYmKit4AIeRPxeDwEBgYiNzcXPB7P0OEQQt5CdJ0h5M1Q7hvyXqZUKjFnzhzUqlUL5ubmuHfvHgBg+vTpWLdunV4DJIQQQgghpLrolBx///33iIqKwqJFi9SmdGvatCnWrl2rt+AIIYQQQgipTjolx5s2bcKvv/6KgQMHqn314+3tjcTERL0FRwghhBBCSHXSaczx48eP8d5772mUK5VKyGSySgdFymBsDGVYGB49egRnWj6aEEIIIUSvdOo5bty4MU6dOqVR/scff8DX17fSQZEyCIVQrFuHK6NHA7R8NCGEEEKIXunU9Thz5kyEhYXh8ePHUCqV2LlzJ5KSkrBp0ybExMToO0ZCCCGEEEKqhU49xx999BF27NiB2NhYcByHGTNm4NatW9i3bx+Cg4P1HSN5GWNAXh54UiktH00IIYQQomc6D1oNDQ1FaGioPmMh5ZGfD761NboBkD1/TstHE0IIIYToUaXu6Lp06RJu3boFjuPg5eVF440JIYQQQkiNplNynJ6ejv79++P48eOwsrICYwwSiQQdOnTA9u3bYW9vr+84CSGEEEIIqXI6jTn+5ptvkJ2djX/++QdZWVl4/vw5bt68iezsbIwaNUrfMRJCCCGEEFItdOo5PnDgAA4fPoxGjRqpyry8vPDzzz8jJCREb8ERQgghhBBSnXTqOVYqleDz+RrlfD4fSqWy0kH9X3v3HhZVnf8B/D0MMNxHhbglglfUVVEgFUgRlIumabReiki3FrYer5n7K7M1dTO6bGuurSY8XbxtmG2mpoKgQCiCiBFeiMTV9RKIF+SqOM58f384nHUEBAo4DrxfzzPPw/mec77fz3wmez7zne85h4iIiIhIDr+qOA4ODsb8+fPxyy+/SG2XLl3CK6+8grFjx7ZacERERERE7elXFccff/wxKisr4eHhgd69e6NPnz7o2bMnKisrsWbNmtaOke6lVEIXEYFL/v6AUil3NEREREQdyq9ac+zm5oZjx44hOTkZP/30E4QQGDhwIMaNG9fa8dH9LCygTUjA0T17MMHCQu5oiIiIiDqU33Sf45CQED4Rj4iIiIg6jBYtqzhw4AAGDhyIioqKevvKy8vxu9/9DhkZGa0WHBERERFRe2pRcfzRRx8hOjoadnZ29fap1Wr86U9/wt///vdWC44aUF0NM3NzTJ4yBaiuljsaIuqAlEolRo8eDRcXFyh5bQMRdTItKo5//PFHhIeHN7o/NDQUubm5vzkoIiIiovbAL4N0vxYVx5cvX27w/sZ1TE1NceXKld8cFBERERGRHFpUHD/66KM4fvx4o/vz8/Ph4uLym4MiIiIiIpJDi4rjCRMmYOnSpbh161a9fTdv3sRbb72FiRMntlpwRERERETtqUXF8Ztvvonr16+jX79+eP/997Fjxw7s3LkT7733Hjw9PXH9+nUsWbKkTQItKytDVFQU1Go11Go1oqKicOPGjQeeM2vWLCgUCoPXyJEjDY6pra3F3Llz4eDgAGtrazz55JO4ePFim7wHIiIiInq4teg+x05OTsjMzMTLL7+MxYsXQwgBAFAoFAgLC8PatWvh5OTUJoE+++yzuHjxIhITEwEAMTExiIqKwq5dux54Xnh4OD7//HNp29zc3GD/ggULsGvXLiQkJMDe3h6vvvoqJk6ciNzcXC7MJyIiIupkWvwQEHd3d+zZswdlZWUoKiqCEAJ9+/ZF165d2yI+AEBBQQESExORlZWFESNGAADi4+Ph5+eHwsJCeHp6NnquSqWCs7Nzg/vKy8vx6aefYtOmTdLT/TZv3gw3NzekpKQgLCys9d/Mb6VUQjd+PEpLS2HP4p2IiIioVf3qJ+R17doVjz32WGvG0qjDhw9DrVZLhTEAjBw5Emq1GpmZmQ8sjtPS0uDo6IguXbogMDAQK1euhKOjIwAgNzcXGo0GoaGh0vGurq4YNGgQMjMzGy2Oa2trUVtbK23XPRRFo9FAo9H8pvfaJKUSmq+/RnZyMkKUSqCtxyNJ3Wfb5p8xGWDe5cG8y4N5lwfz3v60Wi10Oh2Au3lvj1/rm/v5/qbHR7eXkpISqaC9l6OjI0pKSho9b/z48Zg6dSrc3d1x9uxZ/OUvf0FwcDByc3OhUqlQUlICc3PzerPeTk5OD+w3NjYWy5cvr9e+b98+WFlZteCd/TbJycntNhb9D/MuD+ZdHsy7PJh3eTDv7Uen0+Hy5csAgJSUFJiYtOgyuF+lpqamWcfJWhwvW7aswSLzXjk5OQDurmu+nxCiwfY606dPl/4eNGgQfH194e7ujt27dyMiIqLR85rqd/HixVi4cKG0XVFRATc3N4SGhjb49MDWptFokJycjJCQkAfed5paF/MuD+ZdHsy7PJh3eTDv7U+r1SIjIwNnzpzBuHHjYGFh0eZj1v3S3xRZi+M5c+ZgxowZDzzGw8MD+fn50reLe125cqVFFwC6uLjA3d0dp0+fBgA4Ozvj9u3bKCsrM5g9Li0thb+/f6P9qFQqqFSqeu1mZmZt/4+quhqmjo54QquFKCmBWTvOVNNd7fI5Uz3MuzyYd3kw7/Jg3tuPiYmJNFvcXnlv7hiyFscODg5wcHBo8jg/Pz+Ul5fjyJEjGD58OAAgOzsb5eXlDyxi73ft2jVcuHBBelCJj48PzMzMkJycjGnTpgEAiouLceLECbz//vu/4h21D0VNDUwBcGUUERERUetq+wUerWDAgAEIDw9HdHQ0srKykJWVhejoaEycONHgYrz+/ftj+/btAICqqiosWrQIhw8fxrlz55CWloZJkybBwcEBTz31FABArVbjxRdfxKuvvor9+/fjhx9+wHPPPYfBgwdLd68gIiIios7DKC7IA4AtW7Zg3rx50p0lnnzySXz88ccGxxQWFqK8vBwAoFQqcfz4cWzcuBE3btyAi4sLgoKCsHXrVtja2krnrFq1Cqamppg2bRpu3ryJsWPH4osvvuA9jomIiIg6IaMpjrt164bNmzc/8Ji6h5IAgKWlJZKSkprs18LCAmvWrMGaNWt+c4xEREREZNyMYlkFEREREVF7YHFMRERERKRnNMsqSM/EBLrRo3H92jWo2+GG2URERESdCYtjY2NpCW1KCg7t2YMJlpZyR0NERETUoXDqkYiIiIhIj8UxEREREZEel1UYm+pqmHp4IPz2beC//wW6dJE7IiIiIqIOg8WxEVJcvQoV+PhoIiIiotbGZRVERERERHosjomIiIiI9FgcExERERHpsTgmIiIiItJjcUxEREREpMe7VRgbExPofHxQXl4OGz4+moiIiKhVsTg2NpaW0B4+jO/5+GgiIiKiVsepRyIiIiIiPRbHRERERER6XFZhbGpqYDpwIEJqaoDTpwG1Wu6IiIiIiDoMFsfGRggo/vtfWAHQCCF3NEREREQdCpdVEBERERHpsTgmIiIiItJjcUxEREREpMfimIiIiIhIj8UxEREREZEe71ZhbBQKiAEDUFlVBUuFQu5oiIiIiDoUzhwbGysr3PnxR6SuWQNYWckdDREREVGHwuKYiIiIiEiPxTERERERkR7XHBubmhqY+voiqKoKGDOGj48mIiIiakUsjo2NEFAUFMAOfHw0ERERUWvjsgoiIiIiIj0Wx0REREREeiyOiYiIiIj0WBwTEREREemxOCYiIiIi0uPdKoyNQgHh7o6bNTUw4+OjiYiIiFoVZ46NjZUV7pw+jeT4eD4+moiIiKiVsTgmIiIiItJjcUxEREREpMc1x8bm5k0oR43C6PJyICgIMDOTOyIiIiKiDsNoZo7LysoQFRUFtVoNtVqNqKgo3Lhx44HnKBSKBl8ffPCBdMyYMWPq7Z8xY0Ybv5vfQKeDSW4uuhYVATqd3NEQERERdShGM3P87LPP4uLFi0hMTAQAxMTEICoqCrt27Wr0nOLiYoPtvXv34sUXX8TTTz9t0B4dHY0VK1ZI25aWlq0YOREREREZC6MojgsKCpCYmIisrCyMGDECABAfHw8/Pz8UFhbC09OzwfOcnZ0Ntnfs2IGgoCD06tXLoN3KyqresURERETU+RhFcXz48GGo1WqpMAaAkSNHQq1WIzMzs9Hi+F6XL1/G7t27sWHDhnr7tmzZgs2bN8PJyQnjx4/HW2+9BVtb20b7qq2tRW1trbRdUVEBANBoNNBoNC15ay2n0cBM+lMDtPV4JKn7bNv8MyYDzLs8mHd5MO/yYN7bn1arhU6/PFSj0UCpVLb5mM39fI2iOC4pKYGjo2O9dkdHR5SUlDSrjw0bNsDW1hYREREG7ZGRkejZsyecnZ1x4sQJLF68GD/++COSk5Mb7Ss2NhbLly+v175v3z5YtfG9h5W3bmGi/u8DBw5Aa2HRpuNRfQ/6b4PaDvMuD+ZdHsy7PJj39qPT6XD58mUAQEpKCkxM2v4yuJqammYdJ2txvGzZsgaLzHvl5OQAuHtx3f2EEA22N+Szzz5DZGQkLO4rJqOjo6W/Bw0ahL59+8LX1xfHjh2Dt7d3g30tXrwYCxculLYrKirg5uaG0NBQ2NnZNSueX626WvozODgYZl26tO14JNFoNEhOTkZISAjMeJeQdsO8y4N5lwfzLg/mvf1ptVpkZGTgzJkzGDduXL36rC3U/dLfFFmL4zlz5jR5ZwgPDw/k5+dL3y7udeXKFTg5OTU5TkZGBgoLC7F169Ymj/X29oaZmRlOnz7daHGsUqmgUqnqtZuZmbX9PyozMwgHB9y+fbt9xqN6mHd5MO/yYN7lwbzLg3lvPyYmJtJscXvlvbljyFocOzg4wMHBocnj/Pz8UF5ejiNHjmD48OEAgOzsbJSXl8Pf37/J8z/99FP4+PjAy8uryWNPnjwJjUYDFxeXpt+AHKytceeXX5C4Zw8mWFvLHQ0RERFRh2IU9zkeMGAAwsPDER0djaysLGRlZSE6OhoTJ040uBivf//+2L59u8G5FRUV2LZtG/74xz/W6/fMmTNYsWIFjh49inPnzmHPnj2YOnUqhg0bhoCAgDZ/X0RERET0cDGK4hi4e0eJwYMHIzQ0FKGhoRgyZAg2bdpkcExhYSHKy8sN2hISEiCEwDPPPFOvT3Nzc+zfvx9hYWHw9PTEvHnzEBoaipSUlHa5apKIiIiIHi5GcbcKAOjWrRs2b978wGOEEPXaYmJiEBMT0+Dxbm5uSE9Pb5X42s3Nm1CGhyPg2jU+PpqIiIiolRlNcUx6Oh1Mvv8eDgA0fHw0ERERGSGlUonRo0ejqqrqofu13miWVRARERERtTUWx0REREREeiyOiYiIiIj0WBwTEREREemxOCYiIiIi0uPdKoyQsLKCVquVOwwiIiKiDoczx8bG2hp3btzA7q1bAT4+moiIiKhVsTgmIiIiItJjcUxEREREpMc1x8bm1i0oIyIworQUCA7m46OJiIiIWhGLY2Oj1cJk7144A9DwojwiIiKiVsVlFUREREREeiyOiYiIiIj0WBwTEREREemxOCYiIiIi0mNxTERERESkx7tVtAIhBACgoqKi7Qerrpb+1FRUwMyE32/ai0ajQU1NDSoqKmDGW+i1G+ZdHsy7PJh3eTDv8mjvvNfVaXV1W2NYHLeCyspKAICbm1v7Duzu3r7jERERERm5yspKqNXqRvcrRFPlMzVJp9Phl19+ga2tLRQKRZuPV1FRATc3N1y4cAF2dnZtPh7dxbzLg3mXB/MuD+ZdHsy7PNo770IIVFZWwtXVFSYP+OWdM8etwMTEBN27d2/3ce3s7PiPWAbMuzyYd3kw7/Jg3uXBvMujPfP+oBnjOlywSkRERESkx+KYiIiIiEiPxbERUqlUeOutt6BSqeQOpVNh3uXBvMuDeZcH8y4P5l0eD2veeUEeEREREZEeZ46JiIiIiPRYHBMRERER6bE4JiIiIiLSY3FMRERERKTH4tgIrV27Fj179oSFhQV8fHyQkZEhd0gd2vfff49JkybB1dUVCoUC3377rdwhdQqxsbF47LHHYGtrC0dHR0yZMgWFhYVyh9XhrVu3DkOGDJFuyu/n54e9e/fKHVanEhsbC4VCgQULFsgdSoe2bNkyKBQKg5ezs7PcYXUKly5dwnPPPQd7e3tYWVlh6NChyM3NlTssCYtjI7N161YsWLAAS5YswQ8//IBRo0Zh/PjxOH/+vNyhdVjV1dXw8vLCxx9/LHconUp6ejpmz56NrKwsJCcn486dOwgNDUV1dbXcoXVo3bt3x7vvvoujR4/i6NGjCA4OxuTJk3Hy5Em5Q+sUcnJyEBcXhyFDhsgdSqfwu9/9DsXFxdLr+PHjcofU4ZWVlSEgIABmZmbYu3cvTp06hQ8//BBdunSROzQJb+VmZEaMGAFvb2+sW7dOahswYACmTJmC2NhYGSPrHBQKBbZv344pU6bIHUqnc+XKFTg6OiI9PR2jR4+WO5xOpVu3bvjggw/w4osvyh1Kh1ZVVQVvb2+sXbsWb7/9NoYOHYqPPvpI7rA6rGXLluHbb79FXl6e3KF0Kq+//joOHTr0UP/qzZljI3L79m3k5uYiNDTUoD00NBSZmZkyRUXUPsrLywHcLdSofWi1WiQkJKC6uhp+fn5yh9PhzZ49G0888QTGjRsndyidxunTp+Hq6oqePXtixowZ+M9//iN3SB3ezp074evri6lTp8LR0RHDhg1DfHy83GEZYHFsRK5evQqtVgsnJyeDdicnJ5SUlMgUFVHbE0Jg4cKFePzxxzFo0CC5w+nwjh8/DhsbG6hUKrz00kvYvn07Bg4cKHdYHVpCQgKOHTvGXwDb0YgRI7Bx40YkJSUhPj4eJSUl8Pf3x7Vr1+QOrUP7z3/+g3Xr1qFv375ISkrCSy+9hHnz5mHjxo1yhyYxlTsAajmFQmGwLYSo10bUkcyZMwf5+fk4ePCg3KF0Cp6ensjLy8ONGzfw73//GzNnzkR6ejoL5DZy4cIFzJ8/H/v27YOFhYXc4XQa48ePl/4ePHgw/Pz80Lt3b2zYsAELFy6UMbKOTafTwdfXF++88w4AYNiwYTh58iTWrVuH559/Xubo7uLMsRFxcHCAUqmsN0tcWlpabzaZqKOYO3cudu7cidTUVHTv3l3ucDoFc3Nz9OnTB76+voiNjYWXlxdWr14td1gdVm5uLkpLS+Hj4wNTU1OYmpoiPT0d//jHP2BqagqtVit3iJ2CtbU1Bg8ejNOnT8sdSofm4uJS74v2gAEDHqobC7A4NiLm5ubw8fFBcnKyQXtycjL8/f1lioqobQghMGfOHHzzzTc4cOAAevbsKXdInZYQArW1tXKH0WGNHTsWx48fR15envTy9fVFZGQk8vLyoFQq5Q6xU6itrUVBQQFcXFzkDqVDCwgIqHdbzp9//hnu7u4yRVQfl1UYmYULFyIqKgq+vr7w8/NDXFwczp8/j5deeknu0DqsqqoqFBUVSdtnz55FXl4eunXrhh49esgYWcc2e/Zs/Otf/8KOHTtga2sr/WKiVqthaWkpc3Qd1xtvvIHx48fDzc0NlZWVSEhIQFpaGhITE+UOrcOytbWtt5be2toa9vb2XGPfhhYtWoRJkyahR48eKC0txdtvv42KigrMnDlT7tA6tFdeeQX+/v545513MG3aNBw5cgRxcXGIi4uTO7T/EWR0/vnPfwp3d3dhbm4uvL29RXp6utwhdWipqakCQL3XzJkz5Q6tQ2so5wDE559/LndoHdoLL7wg/f/lkUceEWPHjhX79u2TO6xOJzAwUMyfP1/uMDq06dOnCxcXF2FmZiZcXV1FRESEOHnypNxhdQq7du0SgwYNEiqVSvTv31/ExcXJHZIB3ueYiIiIiEiPa46JiIiIiPRYHBMRERER6bE4JiIiIiLSY3FMRERERKTH4piIiIiISI/FMRERERGRHotjIiIiIiI9FsdERERERHosjonIKHh4eOCjjz6SOwwDs2bNwpQpUx6afujhVFhYCGdnZ1RWVsodCgCgtrYWPXr0QG5urtyhED2UWBwTUbN98sknsLW1xZ07d6S2qqoqmJmZYdSoUQbHZmRkQKFQ4Oeff27vMJt07tw5KBQK6aVWqzFy5Ejs2rWrXcbNy8szaF+9ejW++OKLNh27NQrwZcuWGeStode5c+daJd77KRQKfPvtt23S968xZswYLFiwoFnHLlmyBLNnz4atrS0AIC0tDQqFAjdu3Gi7AB9ApVJh0aJFeO2112QZn+hhx+KYiJotKCgIVVVVOHr0qNSWkZEBZ2dn5OTkoKamRmpPS0uDq6sr+vXrJ0eozZKSkoLi4mJkZ2dj+PDhePrpp3HixIl2j0OtVqNLly7tPm5LLVq0CMXFxdKre/fuWLFihUGbm5ubdPzt27dljPbhcPHiRezcuRN/+MMf5A7FQGRkJDIyMlBQUCB3KEQPHRbHRNRsnp6ecHV1RVpamtSWlpaGyZMno3fv3sjMzDRoDwoKAnC3SPq///s/PProo7C2tsaIESMM+gCAzMxMjB49GpaWlnBzc8O8efNQXV3daCyff/451Go1kpOTAQBff/01Bg8eDEtLS9jb22PcuHEPPB8A7O3t4ezsjP79+2PlypXQaDRITU2V9l+6dAnTp09H165dYW9vj8mTJz9wZjQxMRGPP/44unTpAnt7e0ycOBFnzpyR9vfs2RMAMGzYMCgUCowZMwaA4azu+vXr8eijj0Kn0xn0/eSTT2LmzJnS9q5du+Dj4wMLCwv06tULy5cvN5jRv9eyZcuwYcMG7NixQ5rhrcv/8ePHERwcLOUtJiYGVVVVDfZjY2MDZ2dn6aVUKmFrayttv/7663j66acRGxtr8MWoqTzm5OQgJCQEDg4OUKvVCAwMxLFjx6T9Hh4eAICnnnoKCoVC2l62bBmGDh2Kzz77DD169ICNjQ1efvllaLVavP/++3B2doajoyNWrlxp8D7Ky8sRExMDR0dH2NnZITg4GD/++KNBvoYOHYpNmzbBw8MDarUaM2bMkJZFzJo1C+np6Vi9enWTM+ZfffUVvLy80L179wb3N6SpfADATz/9hMcffxwWFhYYOHAgUlJSDGbXb9++jTlz5sDFxQUWFhbw8PBAbGysdL69vT38/f3x5ZdfNjsuos6CxTERtciYMWMMCsjU1FSMGTMGgYGBUvvt27dx+PBhqTj+wx/+gEOHDiEhIQH5+fmYOnUqwsPDcfr0aQB3C7SwsDBEREQgPz8fW7duxcGDBzFnzpwGY/jb3/6GRYsWISkpCSEhISguLsYzzzyDF154AQUFBUhLS0NERASEEM16TxqNBvHx8QAAMzMzAEBNTQ2CgoJgY2OD77//HgcPHoSNjQ3Cw8MbnRGtrq7GwoULkZOTg/3798PExARPPfWUVOgeOXIEwP9mrL/55pt6fUydOhVXr141yHFZWRmSkpIQGRkJAEhKSsJzzz2HefPm4dSpU1i/fj2++OKLekVgnUWLFmHatGkIDw+XZnj9/f1RU1OD8PBwdO3aFTk5Odi2bRtSUlIazXtz7N+/HwUFBUhOTsZ3333XrDxWVlZi5syZyMjIQFZWFvr27YsJEyZIxWhOTg6Au1+IiouLpW0AOHPmDPbu3YvExER8+eWX+Oyzz/DEE0/g4sWLSE9Px3vvvYc333wTWVlZAAAhBJ544gmUlJRgz549yM3Nhbe3N8aOHYvr168b9Pvtt9/iu+++w3fffYf09HS8++67AO4ug/Hz80N0dHSDM+b3+v777+Hr69uiHDaVD51OhylTpsDKygrZ2dmIi4vDkiVLDPr4xz/+gZ07d+Krr75CYWEhNm/eLH2pqDN8+HBkZGS0KDaiTkEQEbVAXFycsLa2FhqNRlRUVAhTU1Nx+fJlkZCQIPz9/YUQQqSnpwsA4syZM6KoqEgoFApx6dIlg37Gjh0rFi9eLIQQIioqSsTExBjsz8jIECYmJuLmzZtCCCHc3d3FqlWrxOuvvy5cXFxEfn6+dGxubq4AIM6dO9es93D27FkBQFhaWgpra2thYmIiAAgPDw9x7do1IYQQn376qfD09BQ6nU46r7a2VlhaWoqkpCQhhBAzZ84UkydPbnSc0tJSAUAcP37cYNwffvjB4Lj7+3nyySfFCy+8IG2vX79eODs7izt37gghhBg1apR45513DPrYtGmTcHFxaTSWhmKNi4sTXbt2FVVVVVLb7t27hYmJiSgpKWm0rzp1n8m9Yzg5OYna2lqprTl5vN+dO3eEra2t2LVrl9QGQGzfvt3guLfeektYWVmJiooKqS0sLEx4eHgIrVYrtXl6eorY2FghhBD79+8XdnZ24tatWwZ99e7dW6xfv77Rfv/85z+LESNGSNuBgYFi/vz5jaVG4uXlJVasWGHQlpqaKgCIsrKyJs8Xon4+9u7dK0xNTUVxcbF0THJyskGO5s6dK4KDgw3yfr/Vq1cLDw+PZsVA1JmYylaVE5FRCgoKQnV1NXJyclBWVoZ+/frB0dERgYGBiIqKQnV1NdLS0tCjRw/06tUL27ZtgxCi3trj2tpa2NvbAwByc3NRVFSELVu2SPuFENDpdDh79iwGDBgAAPjwww9RXV2No0ePolevXtKxXl5eGDt2LAYPHoywsDCEhobi97//Pbp27frA97J161b0798fP//8MxYsWIBPPvkE3bp1M4ip7iKqOrdu3TJYKnGvM2fO4C9/+QuysrJw9epVacb4/PnzGDRoUHPSC+DuetCYmBisXbsWKpUKW7ZswYwZM6BUKqXYcnJyDGaKtVotbt26hZqaGlhZWTVrnIKCAnh5ecHa2lpqCwgIgE6nQ2FhIZycnJodc53BgwfD3Nxc2m5OHktLS7F06VIcOHAAly9fhlarRU1NDc6fP9/keB4eHgZ9Ozk5QalUwsTExKCttLRUiqeqqkr6b6/OzZs3DT7X+/t1cXGR+miJmzdvwsLCokXnNJWPwsJCuLm5wdnZWTpn+PDhBn3MmjULISEh8PT0RHh4OCZOnIjQ0FCDYywtLQ2uEyCiu1gcE1GL9OnTB927d0dqairKysoQGBgIAHB2dkbPnj1x6NAhpKamIjg4GMDdn4CVSiVyc3Ol4q6OjY2NdMyf/vQnzJs3r954PXr0kP4eNWoUdu/eja+++gqvv/661K5UKpGcnIzMzEzs27cPa9aswZIlS5CdnS2t822Im5sb+vbti759+8LGxgZPP/00Tp06BUdHR+h0Ovj4+BgU7HUeeeSRBvubNGkS3NzcEB8fD1dXV+h0OgwaNKjFF6ZNmjQJOp0Ou3fvxmOPPYaMjAz8/e9/l/brdDosX74cERER9c5tSSEmhIBCoWhwX2PtTbm30AbQrDzOmjULV65cwUcffQR3d3eoVCr4+fk1K291y2DujbuhtrovKjqdDi4uLvXWvAMwuCjyQX20hIODA8rKylp0TlP5eNDnVsfb2xtnz57F3r17kZKSgmnTpmHcuHH4+uuvpWOuX7/e6H/LRJ0Zi2MiarGgoCCkpaWhrKwMf/7zn6X2wMBAJCUlISsrS7o6f9iwYdBqtSgtLa13u7c63t7eOHnyJPr06fPAcYcPH465c+ciLCwMSqXSYGyFQoGAgAAEBARg6dKlcHd3x/bt27Fw4cJmvafAwEAMGjQIK1euxOrVq+Ht7Y2tW7dKF2015dq1aygoKMD69eul93nw4EGDY+pmVLVa7QP7srS0REREBLZs2YKioiL069cPPj4+0n5vb28UFhY2ma/7x75/3IEDB2LDhg2orq6WitpDhw7BxMSk1e4y0pw8ZmRkYO3atZgwYQIA4MKFC7h69arBMWZmZk3mrbnxlJSUwNTUtN4a3JZoKJ8NGTZsGE6dOtWivpvKR//+/XH+/HlcvnxZmt2/dx12HTs7O0yfPh3Tp0/H73//e4SHh+P69evSryMnTpzAsGHDWhQbUWfAC/KIqMWCgoJw8OBB5OXlSTPHwN0CMz4+Hrdu3ZIuxuvXrx8iIyPx/PPP45tvvsHZs2eRk5OD9957D3v27AEAvPbaazh8+DBmz56NvLw8nD59Gjt37sTcuXPrje3n54e9e/dixYoVWLVqFQAgOzsb77zzDo4ePYrz58/jm2++wZUrV6TlGM316quvYv369bh06RIiIyPh4OCAyZMnIyMjA2fPnkV6ejrmz5+Pixcv1ju37k4McXFxKCoqwoEDB+oV5o6OjrC0tERiYiIuX76M8vLyRmOJjIzE7t278dlnn+G5554z2Ld06VJs3LgRy5Ytw8mTJ1FQUICtW7fizTffbLQ/Dw8P5Ofno7CwEFevXoVGo0FkZCQsLCwwc+ZMnDhxAqmpqZg7dy6ioqJ+1ZKKxt5HU3ns06cPNm3ahIKCAmRnZyMyMhKWlpb14t+/fz9KSkpaPBN7r3HjxsHPzw9TpkxBUlISzp07h8zMTLz55psGtyhsioeHB7Kzs3Hu3DmDJTT3CwsLw+HDhxsspI8fP468vDyDF9B0PkJCQtC7d2/MnDkT+fn5OHTokHRBXt2M8qpVq5CQkICffvoJP//8M7Zt2wZnZ2eD2fGMjIx6Sy2ICLwgj4haru7Csv79+xu0X7hwQQAQvXv3Nmi/ffu2WLp0qfDw8BBmZmbC2dlZPPXUUwYX1R05ckSEhIQIGxsbYW1tLYYMGSJWrlwp7b//4q/09HRhbW0tVq9eLU6dOiXCwsLEI488IlQqlejXr59Ys2ZNk/Hff2GcTqcTnp6e4uWXXxZCCFFcXCyef/554eDgIFQqlejVq5eIjo4W5eXlQoj6F7klJyeLAQMGCJVKJYYMGSLS0tLqXUgWHx8v3NzchImJiQgMDGywHyHuXoTl4uIiXdh4v8TEROHv7y8sLS2FnZ2dGD58uIiLi2v0PZeWlkr5BSBSU1OFEELk5+eLoKAgYWFhIbp16yaio6NFZWVlo/3cq6EL8hq6QLGpPB47dkz4+voKlUol+vbtK7Zt21av7507d4o+ffoIU1NT4e7uLoS4e+Gcl5eXwVgNxXD/xXMVFRVi7ty5wtXVVZiZmQk3NzcRGRkpzp8/32i/q1atksYVQojCwkIxcuRIYWlpKQCIs2fPNpijO3fuiEcffVQkJiZKbXUX5DX0am4+CgoKREBAgDA3Nxf9+/cXu3btEgCkceLi4sTQoUOFtbW1sLOzE2PHjhXHjh2Tzs/MzBRdunQRNTU1DcZN1JkphGjmvY6IiIioxdauXYsdO3YgKSmpzcY4dOgQHn/8cRQVFaF3795NHj916lQMGzYMb7zxRpvFRGSsuOaYiIioDcXExKCsrAyVlZX17trxa23fvh02Njbo27cvioqKMH/+fAQEBDSrMK6trYWXlxdeeeWVVomFqKPhzDEREZGR2bhxI/7617/iwoULcHBwwLhx4/Dhhx/Wu0UdEbUci2MiIiIiIj3erYKIiIiISI/FMRERERGRHotjIiIiIiI9FsdERERERHosjomIiIiI9FgcExERERHpsTgmIiIiItJjcUxEREREpPf/S5AzDsdnE3AAAAAASUVORK5CYII=", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Define the number of lags (e.g., 0 to 6 weeks)\n", "num_lags = 6\n", "\n", "# Create lag dummy variables for each time lag (0 to num_lags)\n", "for i in range(0, num_lags + 1):\n", "    lag_col_name = f\"lag_{i}\"\n", "    final_data[lag_col_name] = (final_data[\"standardized_time_weeks\"] == i).astype(int)\n", "\n", "# Build the regression formula\n", "# Add lagged interaction terms dynamically\n", "interaction_terms = \" + \".join([f\"lag_{i} * is_treatment\" for i in range(0, num_lags + 1)])\n", "formula = f\"pr_throughput ~ post_treatment + is_treatment + is_treatment * post_treatment + {interaction_terms}\"\n", "\n", "# Fit the regression model using statsmodels' formula API\n", "model = smf.ols(formula, data=final_data).fit()\n", "\n", "# Extract coefficients and confidence intervals for lagged interaction terms\n", "interaction_effects = [f\"lag_{i}:is_treatment\" for i in range(0, num_lags + 1)]\n", "interaction_coef = model.params.reindex(interaction_effects)\n", "interaction_ci = model.conf_int().reindex(interaction_effects)\n", "\n", "# Extract lower and upper bounds for confidence intervals\n", "interaction_ci_lower = interaction_ci[0]\n", "interaction_ci_upper = interaction_ci[1]\n", "\n", "# Calculate y-error for plotting\n", "interaction_yerr = np.vstack((interaction_coef.values - interaction_ci_lower, \n", "                               interaction_ci_upper - interaction_coef.values))\n", "\n", "# Print coefficients and p-values for lagged interaction terms\n", "print(\"Lagged Interaction Coefficients and P-values:\")\n", "for i, term in enumerate(interaction_effects):\n", "    coef = model.params[term]\n", "    pval = model.pvalues[term]\n", "    print(f\"Lag {i}: Coefficient = {coef}, P-value = {pval}\")\n", "\n", "# Plot the lagged interaction coefficients with confidence intervals\n", "# Plot the lagged interaction coefficients with confidence intervals\n", "plt.figure(figsize=(8, 5))\n", "\n", "# Highlight significant points (p < 0.05)\n", "significant = model.pvalues.reindex(interaction_effects) < 0.05\n", "non_significant = ~significant\n", "\n", "# Plot significant points in a distinct style\n", "plt.errorbar(\n", "    np.arange(0, num_lags + 1)[significant], \n", "    interaction_coef.values[significant], \n", "    yerr=interaction_yerr[:, significant],\n", "    fmt='o', color='blue', label='Significant (p < 0.05)'\n", ")\n", "\n", "# Plot non-significant points in a lighter style\n", "plt.errorbar(\n", "    np.arange(0, num_lags + 1)[non_significant], \n", "    interaction_coef.values[non_significant], \n", "    yerr=interaction_yerr[:, non_significant],\n", "    fmt='o', color='gray', alpha=0.5, label='Not Significant (p >= 0.05)'\n", ")\n", "\n", "# Add a horizontal line for zero effect\n", "plt.axhline(y=0, color='gray', linestyle='--', linewidth=1, label='No Effect')\n", "\n", "# Add a vertical line for treatment start\n", "plt.axvline(x=0, color='red', linestyle='--', label='Treatment Start')\n", "\n", "# Set plot title and labels\n", "plt.title(\"Effect of 'is_treatment & post_treatment' Over Time (Lags 0-6)\")\n", "plt.xlabel(\"Weeks Relative to Treatment (Lags)\")\n", "plt.ylabel(\"Coefficient (Effect of Interaction)\")\n", "\n", "# Set x-ticks and legend\n", "plt.xticks(range(0, num_lags + 1))\n", "plt.legend()\n", "plt.grid()\n", "\n", "# Show the plot\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 237, "metadata": {}, "outputs": [], "source": ["# save results of panel DiD regression with leads and lags in a text file\n", "with open('../result/did_regression_results_leads_lags_20241218.txt', 'w') as f:\n", "    f.write(model.summary().as_text())"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:          pr_throughput   R-squared:                       0.002\n", "Model:                            OLS   Adj. R-squared:                  0.001\n", "Method:                 Least Squares   F-statistic:                     1.962\n", "Date:                Wed, 18 Dec 2024   Prob (F-statistic):             0.0102\n", "Time:                        22:58:55   Log-Likelihood:                -53977.\n", "No. Observations:               21327   AIC:                         1.080e+05\n", "Df Residuals:                   21309   BIC:                         1.081e+05\n", "Df Model:                          17                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "Intercept                       2.2283      0.033     66.606      0.000       2.163       2.294\n", "post_treatment                 -0.0737      0.059     -1.256      0.209      -0.189       0.041\n", "is_treatment                    0.2069      0.070      2.946      0.003       0.069       0.345\n", "is_treatment:post_treatment    -0.3438      0.135     -2.547      0.011      -0.608      -0.079\n", "lag_0                          -0.0524      0.128     -0.409      0.683      -0.304       0.199\n", "lag_0:is_treatment              0.4108      0.264      1.555      0.120      -0.107       0.929\n", "lag_1                           0.0996      0.128      0.778      0.437      -0.151       0.351\n", "lag_1:is_treatment              0.2078      0.290      0.716      0.474      -0.361       0.776\n", "lag_2                          -0.0765      0.128     -0.595      0.552      -0.328       0.175\n", "lag_2:is_treatment              0.3087      0.303      1.020      0.308      -0.285       0.902\n", "lag_3                          -0.0535      0.126     -0.425      0.671      -0.300       0.193\n", "lag_3:is_treatment              0.1197      0.294      0.407      0.684      -0.457       0.696\n", "lag_4                          -0.1899      0.126     -1.504      0.133      -0.437       0.058\n", "lag_4:is_treatment              0.2277      0.302      0.754      0.451      -0.364       0.819\n", "lag_5                           0.0531      0.129      0.413      0.680      -0.199       0.305\n", "lag_5:is_treatment              0.1333      0.304      0.438      0.661      -0.463       0.730\n", "lag_6                          -0.0981      0.124     -0.790      0.429      -0.341       0.145\n", "lag_6:is_treatment             -0.2178      0.289     -0.752      0.452      -0.785       0.350\n", "==============================================================================\n", "Omnibus:                    20754.109   <PERSON><PERSON><PERSON>-<PERSON>:                   1.135\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):          1657584.219\n", "Skew:                           4.604   Prob(JB):                         0.00\n", "Kurtosis:                      45.197   Cond. No.                         24.0\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n"]}], "source": ["print(model.summary())"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                 NUM_PR   R-squared:                       0.004\n", "Model:                            OLS   Adj. R-squared:                  0.004\n", "Method:                 Least Squares   F-statistic:                     27.71\n", "Date:                Wed, 18 Dec 2024   Prob (F-statistic):           7.17e-18\n", "Time:                        21:06:06   Log-Likelihood:                -76591.\n", "No. Observations:               19268   AIC:                         1.532e+05\n", "Df Residuals:                   19264   BIC:                         1.532e+05\n", "Df Model:                           3                                         \n", "Covariance Type:            nonrobust                                         \n", "==================================================================================\n", "                     coef    std err          t      P>|t|      [0.025      0.975]\n", "----------------------------------------------------------------------------------\n", "const              5.7134      0.147     38.761      0.000       5.424       6.002\n", "post_treatment     0.0695      0.205      0.340      0.734      -0.331       0.470\n", "is_treatment       3.0129      0.353      8.528      0.000       2.320       3.705\n", "interaction       -2.0547      0.489     -4.199      0.000      -3.014      -1.096\n", "==============================================================================\n", "Omnibus:                    26640.033   <PERSON><PERSON><PERSON>-Watson:                   0.601\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):         15416828.567\n", "Skew:                           7.767   Prob(JB):                         0.00\n", "Kurtosis:                     140.702   Cond. No.                         7.28\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n"]}], "source": ["print(panel_did_model.summary())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scipy.stats import combine_pvalues\n", "\n", "def aggregate_p_values(all_did_results):\n", "    \"\"\"\n", "    Combine p-values from individual regression models using <PERSON>'s method.\n", "\n", "    Parameters:\n", "        all_did_results (list): List of regression results for each treatment repository.\n", "\n", "    Returns:\n", "        float: Combined p-value.\n", "    \"\"\"\n", "    p_values = [result[\"regression_summary\"].tables[0].data[3][4]  # Extract p-value for interaction term\n", "                for result in all_did_results if \"interaction\" in result[\"regression_summary\"].params.index]\n", "\n", "    if p_values:\n", "        _, combined_p_value = combine_pvalues(p_values, method='fisher')\n", "        return combined_p_value\n", "    return None\n", "\n", "# Compute the combined p-value\n", "combined_p_value = aggregate_p_values(stacked_did_results[\"all_did_results\"])\n", "\n", "# Display the combined p-value\n", "print(\"Combined P-value for Aggregated DiD:\", combined_p_value)\n"]}], "metadata": {"kernelspec": {"display_name": "disengagement", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}