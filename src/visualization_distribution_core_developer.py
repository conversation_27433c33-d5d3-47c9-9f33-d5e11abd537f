import matplotlib.pyplot as plt
import pandas as pd

data = pd.read_csv('../data/core_developer_list_4000_repo.csv')
# Calculate the percentage of core developers for each repository
data['core_dev_percentage'] = (data['num_core_developers'] / data['num_total_developers']) * 100

# Define bins for analysis
core_dev_bins = [0, 1, 2, 5, 10, 20, data['num_core_developers'].max()]
total_dev_bins = [0, 5, 10, 20, 50, 100, data['num_total_developers'].max()]
percentage_bins = [0, 10, 20, 50, 75, 100]

# Categorize data into bins
data['core_dev_bin'] = pd.cut(data['num_core_developers'], bins=core_dev_bins)
data['total_dev_bin'] = pd.cut(data['num_total_developers'], bins=total_dev_bins)
data['percentage_bin'] = pd.cut(data['core_dev_percentage'], bins=percentage_bins)

# Count occurrences in each bin
core_dev_dist = data['core_dev_bin'].value_counts(normalize=True).sort_index() * 100
total_dev_dist = data['total_dev_bin'].value_counts(normalize=True).sort_index() * 100
percentage_dist = data['percentage_bin'].value_counts(normalize=True).sort_index() * 100

# Visualize core developers distribution
plt.figure(figsize=(10, 6))
core_dev_dist.plot(kind='bar', color='skyblue', edgecolor='black')
plt.title('Distribution of Number of Core Developers')
plt.xlabel('Number of Core Developers')
plt.ylabel('Percentage of Repositories (%)')
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)
for idx, val in enumerate(core_dev_dist):
    plt.text(idx, val + 0.5, f'{val:.1f}%', ha='center', fontsize=10)
plt.tight_layout()
plt.savefig('../result/figures/core_developers_distribution.pdf')
plt.show()

# Visualize total developers distribution
plt.figure(figsize=(10, 6))
total_dev_dist.plot(kind='bar', color='lightgreen', edgecolor='black')
plt.title('Distribution of Total Number of Developers')
plt.xlabel('Number of Developers')
plt.ylabel('Percentage of Repositories (%)')
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)
for idx, val in enumerate(total_dev_dist):
    plt.text(idx, val + 0.5, f'{val:.1f}%', ha='center', fontsize=10)
plt.tight_layout()
plt.savefig('../result/figures/total_developers_distribution.pdf')
plt.show()

# Visualize percentage distribution
plt.figure(figsize=(10, 6))
percentage_dist.plot(kind='bar', color='lightcoral', edgecolor='black')
plt.title('Percentage Distribution of Core Developers')
plt.xlabel('Percentage of Core Developers / Total Developers')
plt.ylabel('Percentage of Repositories (%)')
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)
for idx, val in enumerate(percentage_dist):
    plt.text(idx, val + 0.5, f'{val:.1f}%', ha='center', fontsize=10)
plt.tight_layout()
plt.savefig('../result/figures/core_developers_percentage_distribution.pdf')
plt.show()
