{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-01-16 02:15:15,244 - INFO - Connecting to MongoDB...\n", "2025-01-16 02:15:15,251 - INFO - Accessing database and collection...\n", "2025-01-16 02:15:15,259 - INFO - Creating index on 'merged_at' field...\n", "2025-01-16 02:25:28,720 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:29,870 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:31,298 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:32,399 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:33,580 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:35,118 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:35,896 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:37,273 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:38,747 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:40,074 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:41,653 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:42,979 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:43,837 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:44,461 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:46,091 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:47,335 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:49,105 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:50,841 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:52,851 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:53,969 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:55,808 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:57,474 - INFO - Processed 5000 documents.\n", "2025-01-16 02:25:58,951 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:00,564 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:02,466 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:04,041 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:05,432 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:06,755 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:08,302 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:09,856 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:11,818 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:14,015 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:17,972 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:19,661 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:21,222 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:22,761 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:24,200 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:25,993 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:27,670 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:29,321 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:30,561 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:32,412 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:33,953 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:34,797 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:36,914 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:40,053 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:41,622 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:43,443 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:44,361 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:45,853 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:46,470 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:48,328 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:49,595 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:50,763 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:53,339 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:54,909 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:56,115 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:57,466 - INFO - Processed 5000 documents.\n", "2025-01-16 02:26:59,055 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:00,976 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:02,373 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:03,382 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:04,700 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:06,302 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:07,141 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:09,154 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:10,764 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:12,538 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:14,141 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:15,731 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:17,709 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:19,393 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:21,433 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:23,432 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:24,627 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:26,560 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:27,903 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:29,559 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:30,796 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:32,279 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:33,473 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:34,786 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:36,014 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:37,420 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:38,071 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:39,070 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:39,898 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:40,720 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:41,132 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:42,263 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:42,827 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:43,703 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:44,198 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:45,057 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:45,622 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:46,756 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:48,360 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:49,928 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:50,889 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:51,610 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:52,825 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:54,318 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:55,669 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:56,682 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:58,389 - INFO - Processed 5000 documents.\n", "2025-01-16 02:27:59,615 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:00,596 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:01,451 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:02,254 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:02,837 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:03,255 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:04,204 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:04,729 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:06,051 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:07,375 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:07,857 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:08,817 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:11,699 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:13,025 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:14,338 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:15,153 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:15,593 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:16,706 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:17,124 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:17,859 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:18,639 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:18,922 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:19,889 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:20,515 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:21,649 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:22,514 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:23,656 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:24,817 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:25,191 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:25,999 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:26,556 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:27,575 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:27,984 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:28,679 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:29,592 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:30,336 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:30,941 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:31,197 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:32,007 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:32,435 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:33,582 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:33,886 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:34,201 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:34,990 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:35,413 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:35,802 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:36,255 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:36,745 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:37,094 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:37,905 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:38,481 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:38,823 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:39,417 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:39,929 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:40,341 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:40,860 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:41,290 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:42,534 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:43,092 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:44,263 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:45,383 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:45,935 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:46,756 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:47,247 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:48,805 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:49,482 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:50,705 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:51,608 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:52,512 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:53,107 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:54,479 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:55,912 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:56,323 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:57,678 - INFO - Processed 5000 documents.\n", "2025-01-16 02:28:58,550 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:00,684 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:02,355 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:03,515 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:04,331 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:05,862 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:07,159 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:08,647 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:09,532 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:11,203 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:12,592 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:13,296 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:14,905 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:16,249 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:17,660 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:19,177 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:19,748 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:21,659 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:22,572 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:23,748 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:25,350 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:26,577 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:27,675 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:28,623 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:30,106 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:30,735 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:32,212 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:33,735 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:34,355 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:35,685 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:37,080 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:37,609 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:38,826 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:40,260 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:40,938 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:44,062 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:46,902 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:47,846 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:49,444 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:50,871 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:51,531 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:53,303 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:54,618 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:55,367 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:56,771 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:58,428 - INFO - Processed 5000 documents.\n", "2025-01-16 02:29:59,735 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:00,549 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:02,023 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:04,529 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:06,244 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:07,179 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:08,333 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:09,204 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:09,884 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:10,989 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:12,580 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:13,170 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:14,808 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:15,773 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:17,261 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:18,316 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:19,805 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:20,574 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:22,050 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:23,326 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:24,655 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:25,650 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:26,918 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:28,567 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:30,145 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:31,542 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:33,272 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:35,123 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:36,937 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:38,301 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:39,071 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:40,898 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:43,044 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:44,692 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:46,578 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:47,977 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:49,400 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:50,868 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:53,074 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:54,350 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:55,667 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:56,765 - INFO - Processed 5000 documents.\n", "2025-01-16 02:30:58,156 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:01,230 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:02,841 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:04,378 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:05,371 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:07,905 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:09,743 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:10,893 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:12,871 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:14,679 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:16,464 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:17,804 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:19,154 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:20,180 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:21,366 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:22,737 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:23,770 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:25,293 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:26,198 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:27,581 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:29,123 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:30,523 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:31,849 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:33,189 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:34,107 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:35,391 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:36,493 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:37,750 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:39,128 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:39,973 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:41,312 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:42,768 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:44,463 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:45,752 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:47,348 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:48,694 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:49,958 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:50,739 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:52,226 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:53,619 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:55,259 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:56,411 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:57,724 - INFO - Processed 5000 documents.\n", "2025-01-16 02:31:59,566 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:00,991 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:02,399 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:03,572 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:04,886 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:06,910 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:07,543 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:08,644 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:10,859 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:12,685 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:13,797 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:16,866 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:18,556 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:19,104 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:20,303 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:22,267 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:23,264 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:24,548 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:25,563 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:26,221 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:27,332 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:27,772 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:29,137 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:29,630 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:30,902 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:32,158 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:33,606 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:34,239 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:35,215 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:36,617 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:37,449 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:38,816 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:40,007 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:40,677 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:42,408 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:43,390 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:44,007 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:45,418 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:46,333 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:47,617 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:48,732 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:49,462 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:50,777 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:52,443 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:53,993 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:55,257 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:56,180 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:57,397 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:57,934 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:59,213 - INFO - Processed 5000 documents.\n", "2025-01-16 02:32:59,854 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:01,375 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:02,530 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:03,066 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:04,422 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:05,517 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:06,208 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:07,507 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:08,795 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:09,405 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:10,454 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:11,801 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:12,400 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:13,791 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:15,803 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:17,165 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:17,917 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:18,841 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:19,393 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:21,310 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:22,239 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:24,121 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:25,271 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:26,421 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:27,075 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:28,306 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:29,858 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:30,677 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:32,105 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:33,379 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:34,131 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:35,215 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:35,727 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:37,615 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:39,987 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:41,318 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:42,803 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:43,724 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:44,962 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:46,395 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:46,932 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:48,590 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:49,852 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:51,362 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:51,936 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:53,393 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:54,809 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:55,511 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:57,349 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:59,053 - INFO - Processed 5000 documents.\n", "2025-01-16 02:33:59,613 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:00,889 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:02,523 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:03,391 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:04,476 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:05,429 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:06,982 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:08,561 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:09,744 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:10,216 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:11,782 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:13,264 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:14,309 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:15,591 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:17,576 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:19,376 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:20,996 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:22,139 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:23,383 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:25,036 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:26,676 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:28,040 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:29,330 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:29,978 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:31,171 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:32,659 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:33,858 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:34,413 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:35,971 - INFO - Processed 5000 documents.\n", "2025-01-16 02:34:36,382 - INFO - Processed 3293 documents.\n", "2025-01-16 02:34:36,387 - INFO - No more documents to fetch.\n", "2025-01-16 02:34:36,388 - INFO - Creating DataFrame...\n", "2025-01-16 02:34:39,038 - INFO - Converting date fields to datetime...\n", "2025-01-16 02:34:44,701 - INFO - Exporting data to CSV...\n", "2025-01-16 02:35:24,088 - INFO - CSV file generated successfully.\n"]}], "source": ["import pymongo\n", "import pandas as pd\n", "import logging\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s',\n", "    handlers=[\n", "        logging.StreamHandler()  # Log to console\n", "    ]\n", ")\n", "\n", "# Establish a connection to MongoDB\n", "logging.info('Connecting to MongoDB...')\n", "client = pymongo.MongoClient('localhost', 27017)\n", "\n", "# Access the database and collection\n", "logging.info('Accessing database and collection...')\n", "db = client['disengagement']\n", "collection = db['pull_requests_202501']\n", "\n", "# Ensure 'merged_at' is indexed\n", "if 'merged_at' not in [index['key'][0] for index in collection.index_information().values()]:\n", "    logging.info(\"Creating index on 'merged_at' field...\")\n", "    collection.create_index('merged_at')\n", "\n", "# Define the projection to include only necessary fields, including '_id'\n", "projection = {\n", "    '_id': 1,\n", "    'number': 1,\n", "    'user.login': 1,\n", "    'created_at': 1,\n", "    'updated_at': 1,\n", "    'closed_at': 1,\n", "    'merged_at': 1,\n", "    'repo_name': 1\n", "}\n", "\n", "# Initialize variables for pagination\n", "last_id = None\n", "batch_size = 5000  # Number of documents to fetch per batch\n", "data = []\n", "\n", "while True:\n", "    # Build the query based on last_id\n", "    if last_id:\n", "        query = {'$and': [{'merged_at': {'$ne': None}}, {'_id': {'$gt': last_id}}]}\n", "    else:\n", "        query = {'merged_at': {'$ne': None}}\n", "    \n", "    # Fetch documents sorted by '_id'\n", "    pull_requests = collection.find(\n", "        query,\n", "        projection=projection,\n", "        limit=batch_size,\n", "        sort=[('_id', pymongo.ASCENDING)]\n", "    )\n", "    \n", "    batch = list(pull_requests)\n", "    if not batch:\n", "        logging.info('No more documents to fetch.')\n", "        break\n", "    \n", "    # Extract the required fields\n", "    for pr in batch:\n", "        user_login = pr.get('user', {}).get('login') if pr.get('user') else None\n", "        data.append({\n", "            'number': pr.get('number'),\n", "            'user_login': user_login,\n", "            'created_at': pr.get('created_at'),\n", "            'updated_at': pr.get('updated_at'),\n", "            'closed_at': pr.get('closed_at'),\n", "            'merged_at': pr.get('merged_at'),\n", "            'repo_name': pr.get('repo_name')\n", "        })\n", "    \n", "    # Update last_id to the '_id' of the last document in the batch\n", "    last_id = batch[-1]['_id']\n", "    logging.info(f'Processed {len(batch)} documents.')\n", "\n", "# Create a DataFrame\n", "logging.info('Creating DataFrame...')\n", "df_pr = pd.DataFrame(data)\n", "\n", "# Convert date fields to datetime\n", "logging.info('Converting date fields to datetime...')\n", "date_cols = ['created_at', 'updated_at', 'closed_at', 'merged_at']\n", "df_pr[date_cols] = df_pr[date_cols].apply(pd.to_datetime, errors='coerce')\n", "\n", "# Generate the CSV file\n", "logging.info('Exporting data to CSV...')\n", "df_pr.to_csv('../data/2025_0116_pull_requests.csv', index=False)\n", "logging.info('CSV file generated successfully.')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    datetime            repo_name  pr_throughput\n", "0 2010-09-06  doctrine/migrations              2\n", "1 2010-09-06    psychopy/psychopy              1\n", "2 2010-09-07   socketio/socket.io              1\n", "3 2010-09-08    psychopy/psychopy              1\n", "4 2010-09-09  davidfstr/rdiscount              1\n"]}], "source": ["import pandas as pd\n", "\n", "# Load the CSV file\n", "df_pr = pd.read_csv('../data/2025_0116_pull_requests.csv')\n", "\n", "# Convert 'merged_at' to datetime\n", "df_pr['merged_at'] = pd.to_datetime(df_pr['merged_at'], errors='coerce')\n", "\n", "# Filter out PRs without a 'merged_at' date\n", "df_merged = df_pr.dropna(subset=['merged_at'])\n", "\n", "# Extract the date part from 'merged_at'\n", "df_merged['datetime'] = df_merged['merged_at'].dt.date\n", "\n", "# Group by 'datetime' and 'repo_name' and count the number of PRs\n", "pr_throughput = df_merged.groupby(['datetime', 'repo_name']).size().reset_index()\n", "\n", "# Rename the count column\n", "pr_throughput.rename(columns={0: 'pr_throughput'}, inplace=True)\n", "\n", "# Ensure 'datetime' is in datetime format\n", "pr_throughput['datetime'] = pd.to_datetime(pr_throughput['datetime'])\n", "\n", "# Display the result\n", "print(pr_throughput.head())\n", "pr_throughput.to_csv('../data/2025_0116_pr_throughput_productivity.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3055</th>\n", "      <td>ros/rosdistro</td>\n", "      <td>3765</td>\n", "    </tr>\n", "    <tr>\n", "      <th>681</th>\n", "      <td>cockroachdb/cockroach</td>\n", "      <td>3489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>952</th>\n", "      <td>dotnet/roslyn</td>\n", "      <td>3342</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007</th>\n", "      <td>libretro/retroarch</td>\n", "      <td>3068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1006</th>\n", "      <td>easybuilders/easybuild-easyconfigs</td>\n", "      <td>3047</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2228</th>\n", "      <td>microsoft/typescript</td>\n", "      <td>2919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1050</th>\n", "      <td>electron/electron</td>\n", "      <td>2882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3063</th>\n", "      <td>rpcs3/rpcs3</td>\n", "      <td>2832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1123</th>\n", "      <td>etcd-io/etcd</td>\n", "      <td>2786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>338</th>\n", "      <td>automattic/jetpack</td>\n", "      <td>2775</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               repo_name  count\n", "3055                       ros/rosdistro   3765\n", "681                cockroachdb/cockroach   3489\n", "952                        dotnet/roslyn   3342\n", "2007                  libretro/retroarch   3068\n", "1006  easybuilders/easybuild-easyconfigs   3047\n", "2228                microsoft/typescript   2919\n", "1050                   electron/electron   2882\n", "3063                         rpcs3/rpcs3   2832\n", "1123                        etcd-io/etcd   2786\n", "338                   automattic/jetpack   2775"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["pr_throughput.groupby('repo_name').size().sort_values(ascending=False)\n", "df_grouped = pr_throughput.groupby('repo_name').size().reset_index(name='count')\n", "df_grouped.sort_values(by='count', ascending=False).head(10)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3971.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>210.216822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>338.655063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>36.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>87.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>224.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3765.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             count\n", "count  3971.000000\n", "mean    210.216822\n", "std     338.655063\n", "min       1.000000\n", "25%      36.000000\n", "50%      87.000000\n", "75%     224.500000\n", "max    3765.000000"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df_grouped.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# plot the data\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    834771.000000\n", "mean          2.633408\n", "std           3.811951\n", "min           1.000000\n", "25%           1.000000\n", "50%           1.000000\n", "75%           3.000000\n", "max         274.000000\n", "Name: pr_throughput, dtype: float64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# grouped by repo_name, total mean, median, max, min, quartile(0.25, 0.5, 0.75), std, var\n", "pr_throughput.groupby('repo_name')['pr_throughput'].describe()\n", "\n", "# no, no,no, i need the distribution of pr_throughput for whole dataset, but the unit is summed by repo_name"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["repo_name\n", "0xproject/0x-launch-kit-backend     1.413793\n", "0xproject/0x-launch-kit-frontend    3.000000\n", "101loop/drf-user                    2.418605\n", "10gic/vanitygen-plusplus            1.000000\n", "10up/autoshare-for-twitter          1.309859\n", "                                      ...   \n", "zotero/zotero-standalone-build      1.230769\n", "zoujingli/thinkadmin                1.333333\n", "zowe/vscode-extension-for-zowe      2.065502\n", "zulip/zulip-desktop                 1.336232\n", "zupit/ritchie-cli                   2.636771\n", "Name: pr_throughput, Length: 3971, dtype: float64 repo_name\n", "0xproject/0x-launch-kit-backend     1.0\n", "0xproject/0x-launch-kit-frontend    2.0\n", "101loop/drf-user                    1.0\n", "10gic/vanitygen-plusplus            1.0\n", "10up/autoshare-for-twitter          1.0\n", "                                   ... \n", "zotero/zotero-standalone-build      1.0\n", "zoujingli/thinkadmin                1.0\n", "zowe/vscode-extension-for-zowe      2.0\n", "zulip/zulip-desktop                 1.0\n", "zupit/ritchie-cli                   2.0\n", "Name: pr_throughput, Length: 3971, dtype: float64 repo_name\n", "0xproject/0x-launch-kit-backend     0.824502\n", "0xproject/0x-launch-kit-frontend    2.385856\n", "101loop/drf-user                    2.647843\n", "10gic/vanitygen-plusplus            0.000000\n", "10up/autoshare-for-twitter          0.716405\n", "                                      ...   \n", "zotero/zotero-standalone-build      0.710363\n", "zoujingli/thinkadmin                0.730297\n", "zowe/vscode-extension-for-zowe      1.388233\n", "zulip/zulip-desktop                 0.737290\n", "zupit/ritchie-cli                   1.785298\n", "Name: pr_throughput, Length: 3971, dtype: float64 repo_name\n", "0xproject/0x-launch-kit-backend      4\n", "0xproject/0x-launch-kit-frontend    13\n", "101loop/drf-user                    14\n", "10gic/vanitygen-plusplus             1\n", "10up/autoshare-for-twitter           5\n", "                                    ..\n", "zotero/zotero-standalone-build       4\n", "zoujingli/thinkadmin                 4\n", "zowe/vscode-extension-for-zowe       9\n", "zulip/zulip-desktop                  6\n", "zupit/ritchie-cli                    9\n", "Name: pr_throughput, Length: 3971, dtype: int64 repo_name                             \n", "0xproject/0x-launch-kit-backend   0.25    1.0\n", "                                  0.50    1.0\n", "                                  0.75    2.0\n", "0xproject/0x-launch-kit-frontend  0.25    1.0\n", "                                  0.50    2.0\n", "                                         ... \n", "zulip/zulip-desktop               0.50    1.0\n", "                                  0.75    1.0\n", "zupit/ritchie-cli                 0.25    1.0\n", "                                  0.50    2.0\n", "                                  0.75    3.0\n", "Name: pr_throughput, Length: 11913, dtype: float64\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "disengagement", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}