import pandas as pd
import numpy as np
import statsmodels.api as sm
from sklearn.linear_model import LogisticRegression
from sklearn.neighbors import NearestNeighbors
import matplotlib.pyplot as plt
import os
import logging

# 全局缓存字典，用于存储 (repo_name, cut_time, metric_column) 的特征
features_cache = {}

# 配置日志
log_dir = "../logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "did_analysis_20250108_v2.log")),
        logging.StreamHandler(),
    ],
)

# 设置输出目录
output_dir = "../result/did_result_parallel/"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 保存数据到文件
def save_data(data, filename):
    filepath = os.path.join(output_dir, filename)
    data.to_csv(filepath, index=False)
    logging.info(f"Saved {filename} to {filepath}.")

# 保存回归结果到文本
def save_model_summary(model, filename):
    filepath = os.path.join(output_dir, filename)
    with open(filepath, "w") as f:
        f.write(model.summary().as_text())
    logging.info(f"Saved regression summary to {filepath}.")

# 可视化处理组和对照组趋势
def visualize_trends(final_data, metric_column="pr_throughput"):
    # Group data into treatment and control groups
    treatment_data = final_data[final_data["is_treatment"] == 1]
    control_data = final_data[final_data["is_treatment"] == 0]

    # Function to compute mean and standard deviation by time
    def compute_stats(group_data):
        grouped = group_data.groupby("standardized_time_weeks")
        mean = grouped[metric_column].mean()
        std = grouped[metric_column].std()
        count = grouped[metric_column].count()
        sem = std / np.sqrt(count)  # Standard error of the mean
        return mean, mean - sem, mean + sem

    # Calculate stats for treatment and control groups
    treatment_mean, treatment_lower, treatment_upper = compute_stats(treatment_data)
    control_mean, control_lower, control_upper = compute_stats(control_data)

    # Create the plots
    plt.figure(figsize=(12, 6))

    # Plot for treatment group
    plt.plot(treatment_mean.index, treatment_mean, label="Treatment Mean", color="blue")
    plt.fill_between(
        treatment_mean.index,
        treatment_lower,
        treatment_upper,
        color="blue",
        alpha=0.2,
        label="Treatment 95% CI",
    )

    # Plot for control group
    plt.plot(control_mean.index, control_mean, label="Control Mean", color="green")
    plt.fill_between(
        control_mean.index,
        control_lower,
        control_upper,
        color="green",
        alpha=0.2,
        label="Control 95% CI",
    )

    # Add vertical line for treatment start
    plt.axvline(0, color="red", linestyle="--", label="Treatment Start (time=0)")

    # Add titles and labels
    plt.title(f"{metric_column.capitalize()} Over Time (Treatment vs. Control)")
    plt.xlabel("Standardized Time (Weeks)")
    plt.ylabel(metric_column.capitalize())
    plt.legend()

    # Save and show the plot
    plot_path = os.path.join(output_dir, f"{metric_column}_trend_plot.png")
    plt.savefig(plot_path)
    logging.info(f"Saved plot to {plot_path}.")
    plt.show()

# 计算单个仓库在给定处理时间前的特征（支持缓存）
def compute_repo_features_for_time(
    repo_name, cut_time, productivity_df, metric_column="pr_throughput"
):
    # 缓存 key
    cache_key = (repo_name, cut_time, metric_column)
    if cache_key in features_cache:
        return features_cache[cache_key]

    # 筛选
    repo_data = productivity_df[
        (productivity_df["repo_name"] == repo_name)
        & (productivity_df["time"] < cut_time)
    ]
    if repo_data.empty:
        result = {
            "repo_name": repo_name,
            "avg_metric": np.nan,
            "slope_metric": np.nan,
        }
        features_cache[cache_key] = result
        return result

    # 此处使用预先添加的 time_numeric 列
    avg_metric = repo_data[metric_column].mean()
    time_numeric = repo_data["time_numeric"].values
    metric_vals = repo_data[metric_column].values

    # 计算斜率
    if len(metric_vals) < 2 or np.var(time_numeric, ddof=0) == 0:
        slope_metric = 0.0
    else:
        slope_metric = (
            np.cov(time_numeric, metric_vals, ddof=0)[0, 1] /
            np.var(time_numeric, ddof=0)
        )

    result = {
        "repo_name": repo_name,
        "avg_metric": avg_metric,
        "slope_metric": slope_metric,
    }
    # 放入缓存
    features_cache[cache_key] = result
    return result

# 针对单个处理仓库的匹配
def match_controls_for_treatment(
    treatment_repo,
    treatment_time,
    available_controls,
    productivity_df,
    n_neighbors=5,
    metric_column="pr_throughput",
):
    # 1) 先获取 treatment 仓库的特征
    t_feats = compute_repo_features_for_time(
        treatment_repo, treatment_time, productivity_df, metric_column
    )
    if pd.isna(t_feats["avg_metric"]):
        return []

    # 2) 批量获取所有可用控制仓库的特征
    c_feats_list = []
    for c_repo in available_controls:
        feats = compute_repo_features_for_time(
            c_repo, treatment_time, productivity_df, metric_column
        )
        # 如果平均值或斜率是 NaN 就过滤
        if (not pd.isna(feats["avg_metric"])) and (not pd.isna(feats["slope_metric"])):
            c_feats_list.append(feats)
    if not c_feats_list:
        return []

    # 3) 构造一个列表来合并处理/控制
    local_data = []
    # 处理仓库
    local_data.append({
        "repo_name": t_feats["repo_name"],
        "avg_metric": t_feats["avg_metric"],
        "slope_metric": t_feats["slope_metric"],
        "is_treatment": 1
    })
    # 控制仓库
    for feats in c_feats_list:
        local_data.append({
            "repo_name": feats["repo_name"],
            "avg_metric": feats["avg_metric"],
            "slope_metric": feats["slope_metric"],
            "is_treatment": 0
        })

    # 4) 转为 DataFrame 并进行 Logistic 回归
    local_df = pd.DataFrame(local_data)
    X = local_df[["avg_metric", "slope_metric"]].values
    y = local_df["is_treatment"].values

    # 使用并行加速（若 solver 支持）
    model = LogisticRegression(n_jobs=-1).fit(X, y)
    local_df["propensity_score"] = model.predict_proba(X)[:, 1]

    # 5) 找到 treatment 的得分
    t_score = local_df.loc[local_df["is_treatment"] == 1, "propensity_score"].iloc[0]
    # 控制组的 df
    control_df = local_df[local_df["is_treatment"] == 0]

    # 6) 最近邻匹配（并行）
    if len(control_df) < n_neighbors:
        logging.warning(
            f"Less than {n_neighbors} controls available for {treatment_repo}."
        )
        return []
    nbrs = NearestNeighbors(n_neighbors=n_neighbors, n_jobs=-1).fit(
        control_df[["propensity_score"]]
    )
    distances, indices = nbrs.kneighbors([[t_score]])
    return control_df.iloc[indices[0]]["repo_name"].tolist()

# 针对所有处理仓库的匹配
def match_all_treatments_psm(
    treatment_repos_with_attrition_date,
    all_repo_names,
    productivity_df,
    n_neighbors=5,
    metric_column="pr_throughput",
):
    matched_pairs = {}
    matched_controls = set()  # 用来追踪已经匹配到的控制仓库
    
    for idx, row in treatment_repos_with_attrition_date.iterrows():
        t_repo = row["repo_name"]
        t_time = row["attrition_date"]
        # 排除已经被匹配过的控制仓库
        available_controls = [r for r in all_repo_names if r != t_repo and r not in matched_controls]

        matched_list = match_controls_for_treatment(
            t_repo,
            t_time,
            available_controls,
            productivity_df,
            n_neighbors,
            metric_column
        )
        matched_pairs[t_repo] = matched_list
        matched_controls.update(matched_list)
        logging.info(f"Matched {len(matched_list)} controls for {t_repo}.")

    return matched_pairs

# 面板 DiD
def perform_panel_diff_in_diff_with_psm(
    treatment_repos_with_attrition_date,
    matched_pairs,
    productivity,
    metric_column="pr_throughput",
    timewindow_weeks=12,
):
    combined_data = []
    for idx, row in treatment_repos_with_attrition_date.iterrows():
        t_repo = row["repo_name"]
        t_time = row["attrition_date"]
        c_repos = matched_pairs.get(t_repo, [])
        if not c_repos:
            continue

        these_repos = [t_repo] + c_repos
        sub_df = productivity[productivity["repo_name"].isin(these_repos)].copy()

        # 计算 standardized_time_weeks
        sub_df["standardized_time_weeks"] = (sub_df["time"] - t_time).dt.days // 7
        sub_df = sub_df[
            sub_df["standardized_time_weeks"].between(-timewindow_weeks, timewindow_weeks)
        ]

        sub_df["post_treatment"] = (sub_df["standardized_time_weeks"] >= 0).astype(int)
        sub_df["is_treatment"] = (sub_df["repo_name"] == t_repo).astype(int)
        sub_df["is_treatment_post_treatment"] = (
            sub_df["post_treatment"] * sub_df["is_treatment"]
        )
        combined_data.append(sub_df)

    if not combined_data:
        logging.warning(
            "No matched data found for DiD. Check matching step or data coverage."
        )
        return None, None

    final_data = pd.concat(combined_data)

    X = sm.add_constant(
        final_data[["post_treatment", "is_treatment", "is_treatment_post_treatment"]]
    )
    y = final_data[metric_column]

    model = sm.OLS(y, X).fit()
    logging.info("Panel DiD analysis with per-repo PSM completed.")

    return model, final_data

# 主函数
if __name__ == "__main__":
    metric_column = "pr_throughput"
    timewindow_weeks = 12
    n_neighbors = 5

    # 加载数据
    logging.info("Loading data...")
    repo_info = pd.read_csv('../data/sample_projects_quartiles.csv')
    attrition = pd.read_csv('../result/attritions.csv')
    productivity = pd.read_csv('../result/productivity_metrics_202501015.csv')
    logging.info("Data loaded successfully.")

    # 数据预处理
    logging.info("Preprocessing data...")
    attrition["attrition_date"] = pd.to_datetime(attrition["attrition_date"])
    productivity["time"] = pd.to_datetime(productivity["datetime"])
    # 预先添加一列 time_numeric (秒数)，减少重复转换
    productivity["time_numeric"] = (productivity["time"] - pd.Timestamp("1970-01-01")).dt.total_seconds()
    logging.info("Data preprocessing completed.")

    # 提取处理组仓库
    logging.info("Extracting treatment repos...")
    attrition_counts = attrition["repo_name"].value_counts().reset_index()
    attrition_counts.columns = ["repo_name", "attrition_count"]
    repos_with_one_attrition = attrition_counts[attrition_counts["attrition_count"] == 1]

    # sample 100 repos
    repos_with_one_attrition = repos_with_one_attrition.sample(100, random_state=42)
    treatment_repos_with_attrition_date = pd.merge(
        repos_with_one_attrition, attrition, on="repo_name", how="left"
    )

    all_repo_names = list(
        set(repo_info["repo_name"].unique()) | set(attrition["repo_name"].unique())
    )
    logging.info(f"Found {len(treatment_repos_with_attrition_date)} treatment repos.")

    # 匹配控制组
    logging.info("Matching controls for all treatment repos...")
    matched_pairs = match_all_treatments_psm(
        treatment_repos_with_attrition_date,
        all_repo_names,
        productivity,
        n_neighbors,
        metric_column,
    )

    matched_pairs_df = pd.DataFrame(
        [
            {"treatment_repo": k, "control_repo": v}
            for k, controls in matched_pairs.items()
            for v in controls
        ]
    )
    save_data(matched_pairs_df, f"{metric_column}_matched_pairs.csv")

    # 面板差分分析
    logging.info("Performing panel DiD analysis...")
    model, final_data = perform_panel_diff_in_diff_with_psm(
        treatment_repos_with_attrition_date,
        matched_pairs,
        productivity,
        metric_column,
        timewindow_weeks,
    )
    if model is not None:
        save_model_summary(model, f"{metric_column}_regression_summary.txt")
        save_data(final_data, f"{metric_column}_final_data.csv")
        visualize_trends(final_data, metric_column)
    else:
        logging.warning("No regression result due to empty matched data.")
