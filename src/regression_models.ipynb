{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import logging\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import matplotlib.pyplot as plt\n", "from itertools import product\n", "\n", "# Set up logging\n", "log_dir = \"../logs\"\n", "os.makedirs(log_dir, exist_ok=True)\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s\",\n", "    handlers=[\n", "        logging.FileHandler(os.path.join(log_dir, \"psm_2025_0201.log\")),\n", "        logging.StreamHandler(),\n", "    ],\n", ")\n", "\n", "output_dir = \"../result/standardized_productivity_20250202/\"\n", "os.makedirs(output_dir, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Data loads and preprocess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["compiled_data_test['i']"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["compiled_data_test = pd.read_csv(output_dir + \"compiled_data_test.csv\")\n", "compiled_data_test['log_pr_throughput'] = np.log(compiled_data_test['pr_throughput'] + 1)\n", "compiled_data_test['log_project_commits'] = np.log(compiled_data_test['project_commits'] + 1)\n", "compiled_data_test['log_project_contributors'] = np.log(compiled_data_test['project_contributors'] + 1)\n", "compiled_data_test['log_project_age'] = np.log(compiled_data_test['project_age'] + 1)\n", "\n", "# Create time-cohort and repo-cohort effects\n", "compiled_data_test['time_cohort_effect'] = compiled_data_test['is_post_treatment'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)\n", "compiled_data_test['repo_cohort_effect'] = compiled_data_test['is_treated'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>relativized_time</th>\n", "      <th>is_treated</th>\n", "      <th>post_treatment</th>\n", "      <th>cohort_id</th>\n", "      <th>...</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>log_pr_throughput</th>\n", "      <th>log_project_commits</th>\n", "      <th>log_project_contributors</th>\n", "      <th>log_project_age</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>427</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>-12</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>132</td>\n", "      <td>3</td>\n", "      <td>58</td>\n", "      <td>TypeScript</td>\n", "      <td>1.609438</td>\n", "      <td>4.890349</td>\n", "      <td>1.386294</td>\n", "      <td>4.077537</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>428</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>-11</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>135</td>\n", "      <td>3</td>\n", "      <td>65</td>\n", "      <td>TypeScript</td>\n", "      <td>0.693147</td>\n", "      <td>4.912655</td>\n", "      <td>1.386294</td>\n", "      <td>4.189655</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>429</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>-10</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>136</td>\n", "      <td>3</td>\n", "      <td>72</td>\n", "      <td>TypeScript</td>\n", "      <td>0.693147</td>\n", "      <td>4.919981</td>\n", "      <td>1.386294</td>\n", "      <td>4.290459</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>430</td>\n", "      <td>2.0</td>\n", "      <td>75.0</td>\n", "      <td>0.486486</td>\n", "      <td>108.0</td>\n", "      <td>-9</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>4</td>\n", "      <td>79</td>\n", "      <td>TypeScript</td>\n", "      <td>1.098612</td>\n", "      <td>4.955827</td>\n", "      <td>1.609438</td>\n", "      <td>4.382027</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>431</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>-8</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>4</td>\n", "      <td>86</td>\n", "      <td>TypeScript</td>\n", "      <td>0.000000</td>\n", "      <td>4.955827</td>\n", "      <td>1.609438</td>\n", "      <td>4.465908</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40444</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>470</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>101</td>\n", "      <td>5</td>\n", "      <td>230</td>\n", "      <td>PHP</td>\n", "      <td>1.098612</td>\n", "      <td>4.624973</td>\n", "      <td>1.791759</td>\n", "      <td>5.442418</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40445</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>471</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>115</td>\n", "      <td>5</td>\n", "      <td>237</td>\n", "      <td>PHP</td>\n", "      <td>0.000000</td>\n", "      <td>4.753590</td>\n", "      <td>1.791759</td>\n", "      <td>5.472271</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40446</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>472</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>148</td>\n", "      <td>5</td>\n", "      <td>244</td>\n", "      <td>PHP</td>\n", "      <td>1.386294</td>\n", "      <td>5.003946</td>\n", "      <td>1.791759</td>\n", "      <td>5.501258</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40447</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>473</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>148</td>\n", "      <td>5</td>\n", "      <td>251</td>\n", "      <td>PHP</td>\n", "      <td>0.000000</td>\n", "      <td>5.003946</td>\n", "      <td>1.791759</td>\n", "      <td>5.529429</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40448</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>474</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>162</td>\n", "      <td>5</td>\n", "      <td>258</td>\n", "      <td>PHP</td>\n", "      <td>0.693147</td>\n", "      <td>5.093750</td>\n", "      <td>1.791759</td>\n", "      <td>5.556828</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>40449 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                             repo_name  standardized_time_weeks  \\\n", "0      0xproject/0x-launch-kit-backend                      427   \n", "1      0xproject/0x-launch-kit-backend                      428   \n", "2      0xproject/0x-launch-kit-backend                      429   \n", "3      0xproject/0x-launch-kit-backend                      430   \n", "4      0xproject/0x-launch-kit-backend                      431   \n", "...                                ...                      ...   \n", "40444       10up/autoshare-for-twitter                      470   \n", "40445       10up/autoshare-for-twitter                      471   \n", "40446       10up/autoshare-for-twitter                      472   \n", "40447       10up/autoshare-for-twitter                      473   \n", "40448       10up/autoshare-for-twitter                      474   \n", "\n", "       pr_throughput  tenure  commit_percent  commits  relativized_time  \\\n", "0                4.0     0.0        0.000000      0.0               -12   \n", "1                1.0     0.0        0.000000      0.0               -11   \n", "2                1.0     0.0        0.000000      0.0               -10   \n", "3                2.0    75.0        0.486486    108.0                -9   \n", "4                0.0     0.0        0.000000      0.0                -8   \n", "...              ...     ...             ...      ...               ...   \n", "40444            2.0     0.0        0.000000      0.0                 8   \n", "40445            0.0     0.0        0.000000      0.0                 9   \n", "40446            3.0     0.0        0.000000      0.0                10   \n", "40447            0.0     0.0        0.000000      0.0                11   \n", "40448            1.0     0.0        0.000000      0.0                12   \n", "\n", "       is_treated  post_treatment  cohort_id  ...  project_commits  \\\n", "0               1           False          0  ...              132   \n", "1               1           False          0  ...              135   \n", "2               1           False          0  ...              136   \n", "3               1           False          0  ...              141   \n", "4               1           False          0  ...              141   \n", "...           ...             ...        ...  ...              ...   \n", "40444           0            True        817  ...              101   \n", "40445           0            True        817  ...              115   \n", "40446           0            True        817  ...              148   \n", "40447           0            True        817  ...              148   \n", "40448           0            True        817  ...              162   \n", "\n", "       project_contributors  project_age  mainLanguage  log_pr_throughput  \\\n", "0                         3           58    TypeScript           1.609438   \n", "1                         3           65    TypeScript           0.693147   \n", "2                         3           72    TypeScript           0.693147   \n", "3                         4           79    TypeScript           1.098612   \n", "4                         4           86    TypeScript           0.000000   \n", "...                     ...          ...           ...                ...   \n", "40444                     5          230           PHP           1.098612   \n", "40445                     5          237           PHP           0.000000   \n", "40446                     5          244           PHP           1.386294   \n", "40447                     5          251           PHP           0.000000   \n", "40448                     5          258           PHP           0.693147   \n", "\n", "      log_project_commits  log_project_contributors  log_project_age  \\\n", "0                4.890349                  1.386294         4.077537   \n", "1                4.912655                  1.386294         4.189655   \n", "2                4.919981                  1.386294         4.290459   \n", "3                4.955827                  1.609438         4.382027   \n", "4                4.955827                  1.609438         4.465908   \n", "...                   ...                       ...              ...   \n", "40444            4.624973                  1.791759         5.442418   \n", "40445            4.753590                  1.791759         5.472271   \n", "40446            5.003946                  1.791759         5.501258   \n", "40447            5.003946                  1.791759         5.529429   \n", "40448            5.093750                  1.791759         5.556828   \n", "\n", "       time_cohort_effect  repo_cohort_effect  \n", "0                     0_0                 1_0  \n", "1                     0_0                 1_0  \n", "2                     0_0                 1_0  \n", "3                     0_0                 1_0  \n", "4                     0_0                 1_0  \n", "...                   ...                 ...  \n", "40444               1_817               0_817  \n", "40445               1_817               0_817  \n", "40446               1_817               0_817  \n", "40447               1_817               0_817  \n", "40448               1_817               0_817  \n", "\n", "[40449 rows x 22 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data_test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Time Cohorts: {compiled_data_test['time_cohort_effect'].nunique()}\")\n", "print(f\"Repo Cohorts: {compiled_data_test['repo_cohort_effect'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- remove outliers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# remove samples with missing values\n", "compiled_data_test['outlier'] = 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Regression Model 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "overall_model = smf.ols(\n", "    formula=\"log_pr_throughput ~ is_treated * is_post_treatment + C(time_cohort_effect) + C(repo_cohort_effect) \",\n", "    data=compiled_data_test\n", ").fit()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# save the overall model results\n", "with open(output_dir + \"original_model_with_different_fixed_effect_20250203.txt\", \"w\") as f:\n", "    f.write(overall_model.summary().as_text())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Regression Model 2\n", "add disengagement characteristics\n", "- core_dev characterisitcs:\n", "  - dev_tenure\n", "  - commits_count\n", "  - commits_percentage\n", "- repo characteristics:\n", "  - project_age\n", "  - project_commit_count\n", "  - project_contributor_count\n", "  - mainLanguage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- add core_dev characterstics to data grouped by cohort_id, add core_dev features each group data\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["compiled_data_test"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>relativized_time</th>\n", "      <th>is_treated</th>\n", "      <th>post_treatment</th>\n", "      <th>cohort_id</th>\n", "      <th>...</th>\n", "      <th>project_commits</th>\n", "      <th>project_contributors</th>\n", "      <th>project_age</th>\n", "      <th>mainLanguage</th>\n", "      <th>log_pr_throughput</th>\n", "      <th>log_project_commits</th>\n", "      <th>log_project_contributors</th>\n", "      <th>log_project_age</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>427</td>\n", "      <td>4.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-12</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>132</td>\n", "      <td>3</td>\n", "      <td>58</td>\n", "      <td>TypeScript</td>\n", "      <td>1.609438</td>\n", "      <td>4.890349</td>\n", "      <td>1.386294</td>\n", "      <td>4.077537</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>428</td>\n", "      <td>1.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-11</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>135</td>\n", "      <td>3</td>\n", "      <td>65</td>\n", "      <td>TypeScript</td>\n", "      <td>0.693147</td>\n", "      <td>4.912655</td>\n", "      <td>1.386294</td>\n", "      <td>4.189655</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>429</td>\n", "      <td>1.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-10</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>136</td>\n", "      <td>3</td>\n", "      <td>72</td>\n", "      <td>TypeScript</td>\n", "      <td>0.693147</td>\n", "      <td>4.919981</td>\n", "      <td>1.386294</td>\n", "      <td>4.290459</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>430</td>\n", "      <td>2.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-9</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>4</td>\n", "      <td>79</td>\n", "      <td>TypeScript</td>\n", "      <td>1.098612</td>\n", "      <td>4.955827</td>\n", "      <td>1.609438</td>\n", "      <td>4.382027</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>431</td>\n", "      <td>0.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-8</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>4</td>\n", "      <td>86</td>\n", "      <td>TypeScript</td>\n", "      <td>0.000000</td>\n", "      <td>4.955827</td>\n", "      <td>1.609438</td>\n", "      <td>4.465908</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40444</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>470</td>\n", "      <td>2.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>101</td>\n", "      <td>5</td>\n", "      <td>230</td>\n", "      <td>PHP</td>\n", "      <td>1.098612</td>\n", "      <td>4.624973</td>\n", "      <td>1.791759</td>\n", "      <td>5.442418</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40445</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>471</td>\n", "      <td>0.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>115</td>\n", "      <td>5</td>\n", "      <td>237</td>\n", "      <td>PHP</td>\n", "      <td>0.000000</td>\n", "      <td>4.753590</td>\n", "      <td>1.791759</td>\n", "      <td>5.472271</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40446</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>472</td>\n", "      <td>3.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>148</td>\n", "      <td>5</td>\n", "      <td>244</td>\n", "      <td>PHP</td>\n", "      <td>1.386294</td>\n", "      <td>5.003946</td>\n", "      <td>1.791759</td>\n", "      <td>5.501258</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40447</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>473</td>\n", "      <td>0.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>148</td>\n", "      <td>5</td>\n", "      <td>251</td>\n", "      <td>PHP</td>\n", "      <td>0.000000</td>\n", "      <td>5.003946</td>\n", "      <td>1.791759</td>\n", "      <td>5.529429</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40448</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>474</td>\n", "      <td>1.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>162</td>\n", "      <td>5</td>\n", "      <td>258</td>\n", "      <td>PHP</td>\n", "      <td>0.693147</td>\n", "      <td>5.093750</td>\n", "      <td>1.791759</td>\n", "      <td>5.556828</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>40449 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                             repo_name  standardized_time_weeks  \\\n", "0      0xproject/0x-launch-kit-backend                      427   \n", "1      0xproject/0x-launch-kit-backend                      428   \n", "2      0xproject/0x-launch-kit-backend                      429   \n", "3      0xproject/0x-launch-kit-backend                      430   \n", "4      0xproject/0x-launch-kit-backend                      431   \n", "...                                ...                      ...   \n", "40444       10up/autoshare-for-twitter                      470   \n", "40445       10up/autoshare-for-twitter                      471   \n", "40446       10up/autoshare-for-twitter                      472   \n", "40447       10up/autoshare-for-twitter                      473   \n", "40448       10up/autoshare-for-twitter                      474   \n", "\n", "       pr_throughput  tenure  commit_percent  commits  relativized_time  \\\n", "0                4.0    88.0         0.13964     31.0               -12   \n", "1                1.0    88.0         0.13964     31.0               -11   \n", "2                1.0    88.0         0.13964     31.0               -10   \n", "3                2.0    88.0         0.13964     31.0                -9   \n", "4                0.0    88.0         0.13964     31.0                -8   \n", "...              ...     ...             ...      ...               ...   \n", "40444            2.0  2480.0         0.12800     16.0                 8   \n", "40445            0.0  2480.0         0.12800     16.0                 9   \n", "40446            3.0  2480.0         0.12800     16.0                10   \n", "40447            0.0  2480.0         0.12800     16.0                11   \n", "40448            1.0  2480.0         0.12800     16.0                12   \n", "\n", "       is_treated  post_treatment  cohort_id  ...  project_commits  \\\n", "0               1           False          0  ...              132   \n", "1               1           False          0  ...              135   \n", "2               1           False          0  ...              136   \n", "3               1           False          0  ...              141   \n", "4               1           False          0  ...              141   \n", "...           ...             ...        ...  ...              ...   \n", "40444           0            True        817  ...              101   \n", "40445           0            True        817  ...              115   \n", "40446           0            True        817  ...              148   \n", "40447           0            True        817  ...              148   \n", "40448           0            True        817  ...              162   \n", "\n", "       project_contributors  project_age  mainLanguage  log_pr_throughput  \\\n", "0                         3           58    TypeScript           1.609438   \n", "1                         3           65    TypeScript           0.693147   \n", "2                         3           72    TypeScript           0.693147   \n", "3                         4           79    TypeScript           1.098612   \n", "4                         4           86    TypeScript           0.000000   \n", "...                     ...          ...           ...                ...   \n", "40444                     5          230           PHP           1.098612   \n", "40445                     5          237           PHP           0.000000   \n", "40446                     5          244           PHP           1.386294   \n", "40447                     5          251           PHP           0.000000   \n", "40448                     5          258           PHP           0.693147   \n", "\n", "      log_project_commits  log_project_contributors  log_project_age  \\\n", "0                4.890349                  1.386294         4.077537   \n", "1                4.912655                  1.386294         4.189655   \n", "2                4.919981                  1.386294         4.290459   \n", "3                4.955827                  1.609438         4.382027   \n", "4                4.955827                  1.609438         4.465908   \n", "...                   ...                       ...              ...   \n", "40444            4.624973                  1.791759         5.442418   \n", "40445            4.753590                  1.791759         5.472271   \n", "40446            5.003946                  1.791759         5.501258   \n", "40447            5.003946                  1.791759         5.529429   \n", "40448            5.093750                  1.791759         5.556828   \n", "\n", "       time_cohort_effect  repo_cohort_effect  \n", "0                     0_0                 1_0  \n", "1                     0_0                 1_0  \n", "2                     0_0                 1_0  \n", "3                     0_0                 1_0  \n", "4                     0_0                 1_0  \n", "...                   ...                 ...  \n", "40444               1_817               0_817  \n", "40445               1_817               0_817  \n", "40446               1_817               0_817  \n", "40447               1_817               0_817  \n", "40448               1_817               0_817  \n", "\n", "[40449 rows x 22 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# group by cohort_id, copy the value of ['tenure', 'commit_percent', 'commits'] from the row that is_treated == 1 and relativized_time = 0\n", "\n", "for cohort_id, group in compiled_data_test.groupby('cohort_id'):\n", "    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]\n", "    if treated_group.empty:\n", "        continue\n", "    treated_row = treated_group.iloc[0][['tenure', 'commit_percent', 'commits']]\n", "    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['tenure', 'commit_percent', 'commits']] = treated_row.values    \n", "compiled_data_test"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["\n", "compiled_data_test['log_tenure'] = np.log(compiled_data_test['tenure'] + 1)\n", "compiled_data_test['log_commit_percent'] = np.log(compiled_data_test['commit_percent'] + 1)\n", "compiled_data_test['log_commits'] = np.log(compiled_data_test['commits'] + 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["formula_with_features_left_core_dev = \"log_pr_throughput ~ is_treated * is_post_treatment * (log_tenure + log_commit_percent + log_commits) + C(time_cohort_effect) + C(repo_cohort_effect) \"\n", "model_with_features_left_core_dev = smf.ols(\n", "    formula=formula_with_features_left_core_dev,\n", "    data=compiled_data_test\n", ").fit()\n", "model_with_features_left_core_dev.summary()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# save the model results\n", "with open(output_dir + \"model_with_features_left_core_dev_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_features_left_core_dev.summary().as_text())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### model 3: add project characteristics before disengagement"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1369468/3274868643.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '5.159055299214529' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "/tmp/ipykernel_1369468/3274868643.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '1.9459101490553132' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "/tmp/ipykernel_1369468/3274868643.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '4.962844630259907' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>relativized_time</th>\n", "      <th>is_treated</th>\n", "      <th>post_treatment</th>\n", "      <th>cohort_id</th>\n", "      <th>...</th>\n", "      <th>log_project_age</th>\n", "      <th>time_cohort_effect</th>\n", "      <th>repo_cohort_effect</th>\n", "      <th>log_tenure</th>\n", "      <th>log_commit_percent</th>\n", "      <th>log_commits</th>\n", "      <th>log_project_commits_before_treatment</th>\n", "      <th>log_project_contributors_before_treatment</th>\n", "      <th>log_project_age_before_treatment</th>\n", "      <th>project_main_language</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>427</td>\n", "      <td>4.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-12</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>4.077537</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>4.488636</td>\n", "      <td>0.130712</td>\n", "      <td>3.465736</td>\n", "      <td>5.159055</td>\n", "      <td>1.945910</td>\n", "      <td>4.962845</td>\n", "      <td>TypeScript</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>428</td>\n", "      <td>1.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-11</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>4.189655</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>4.488636</td>\n", "      <td>0.130712</td>\n", "      <td>3.465736</td>\n", "      <td>5.159055</td>\n", "      <td>1.945910</td>\n", "      <td>4.962845</td>\n", "      <td>TypeScript</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>429</td>\n", "      <td>1.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-10</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>4.290459</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>4.488636</td>\n", "      <td>0.130712</td>\n", "      <td>3.465736</td>\n", "      <td>5.159055</td>\n", "      <td>1.945910</td>\n", "      <td>4.962845</td>\n", "      <td>TypeScript</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>430</td>\n", "      <td>2.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-9</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>4.382027</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>4.488636</td>\n", "      <td>0.130712</td>\n", "      <td>3.465736</td>\n", "      <td>5.159055</td>\n", "      <td>1.945910</td>\n", "      <td>4.962845</td>\n", "      <td>TypeScript</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0xproject/0x-launch-kit-backend</td>\n", "      <td>431</td>\n", "      <td>0.0</td>\n", "      <td>88.0</td>\n", "      <td>0.13964</td>\n", "      <td>31.0</td>\n", "      <td>-8</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>4.465908</td>\n", "      <td>0_0</td>\n", "      <td>1_0</td>\n", "      <td>4.488636</td>\n", "      <td>0.130712</td>\n", "      <td>3.465736</td>\n", "      <td>5.159055</td>\n", "      <td>1.945910</td>\n", "      <td>4.962845</td>\n", "      <td>TypeScript</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40444</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>470</td>\n", "      <td>2.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>5.442418</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "      <td>7.816417</td>\n", "      <td>0.120446</td>\n", "      <td>2.833213</td>\n", "      <td>4.787492</td>\n", "      <td>3.178054</td>\n", "      <td>7.995980</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40445</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>471</td>\n", "      <td>0.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>5.472271</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "      <td>7.816417</td>\n", "      <td>0.120446</td>\n", "      <td>2.833213</td>\n", "      <td>4.787492</td>\n", "      <td>3.178054</td>\n", "      <td>7.995980</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40446</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>472</td>\n", "      <td>3.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>5.501258</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "      <td>7.816417</td>\n", "      <td>0.120446</td>\n", "      <td>2.833213</td>\n", "      <td>4.787492</td>\n", "      <td>3.178054</td>\n", "      <td>7.995980</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40447</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>473</td>\n", "      <td>0.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>5.529429</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "      <td>7.816417</td>\n", "      <td>0.120446</td>\n", "      <td>2.833213</td>\n", "      <td>4.787492</td>\n", "      <td>3.178054</td>\n", "      <td>7.995980</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40448</th>\n", "      <td>10up/autoshare-for-twitter</td>\n", "      <td>474</td>\n", "      <td>1.0</td>\n", "      <td>2480.0</td>\n", "      <td>0.12800</td>\n", "      <td>16.0</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>817</td>\n", "      <td>...</td>\n", "      <td>5.556828</td>\n", "      <td>1_817</td>\n", "      <td>0_817</td>\n", "      <td>7.816417</td>\n", "      <td>0.120446</td>\n", "      <td>2.833213</td>\n", "      <td>4.787492</td>\n", "      <td>3.178054</td>\n", "      <td>7.995980</td>\n", "      <td>C</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>40449 rows × 29 columns</p>\n", "</div>"], "text/plain": ["                             repo_name  standardized_time_weeks  \\\n", "0      0xproject/0x-launch-kit-backend                      427   \n", "1      0xproject/0x-launch-kit-backend                      428   \n", "2      0xproject/0x-launch-kit-backend                      429   \n", "3      0xproject/0x-launch-kit-backend                      430   \n", "4      0xproject/0x-launch-kit-backend                      431   \n", "...                                ...                      ...   \n", "40444       10up/autoshare-for-twitter                      470   \n", "40445       10up/autoshare-for-twitter                      471   \n", "40446       10up/autoshare-for-twitter                      472   \n", "40447       10up/autoshare-for-twitter                      473   \n", "40448       10up/autoshare-for-twitter                      474   \n", "\n", "       pr_throughput  tenure  commit_percent  commits  relativized_time  \\\n", "0                4.0    88.0         0.13964     31.0               -12   \n", "1                1.0    88.0         0.13964     31.0               -11   \n", "2                1.0    88.0         0.13964     31.0               -10   \n", "3                2.0    88.0         0.13964     31.0                -9   \n", "4                0.0    88.0         0.13964     31.0                -8   \n", "...              ...     ...             ...      ...               ...   \n", "40444            2.0  2480.0         0.12800     16.0                 8   \n", "40445            0.0  2480.0         0.12800     16.0                 9   \n", "40446            3.0  2480.0         0.12800     16.0                10   \n", "40447            0.0  2480.0         0.12800     16.0                11   \n", "40448            1.0  2480.0         0.12800     16.0                12   \n", "\n", "       is_treated  post_treatment  cohort_id  ...  log_project_age  \\\n", "0               1           False          0  ...         4.077537   \n", "1               1           False          0  ...         4.189655   \n", "2               1           False          0  ...         4.290459   \n", "3               1           False          0  ...         4.382027   \n", "4               1           False          0  ...         4.465908   \n", "...           ...             ...        ...  ...              ...   \n", "40444           0            True        817  ...         5.442418   \n", "40445           0            True        817  ...         5.472271   \n", "40446           0            True        817  ...         5.501258   \n", "40447           0            True        817  ...         5.529429   \n", "40448           0            True        817  ...         5.556828   \n", "\n", "       time_cohort_effect  repo_cohort_effect  log_tenure  log_commit_percent  \\\n", "0                     0_0                 1_0    4.488636            0.130712   \n", "1                     0_0                 1_0    4.488636            0.130712   \n", "2                     0_0                 1_0    4.488636            0.130712   \n", "3                     0_0                 1_0    4.488636            0.130712   \n", "4                     0_0                 1_0    4.488636            0.130712   \n", "...                   ...                 ...         ...                 ...   \n", "40444               1_817               0_817    7.816417            0.120446   \n", "40445               1_817               0_817    7.816417            0.120446   \n", "40446               1_817               0_817    7.816417            0.120446   \n", "40447               1_817               0_817    7.816417            0.120446   \n", "40448               1_817               0_817    7.816417            0.120446   \n", "\n", "      log_commits  log_project_commits_before_treatment  \\\n", "0        3.465736                              5.159055   \n", "1        3.465736                              5.159055   \n", "2        3.465736                              5.159055   \n", "3        3.465736                              5.159055   \n", "4        3.465736                              5.159055   \n", "...           ...                                   ...   \n", "40444    2.833213                              4.787492   \n", "40445    2.833213                              4.787492   \n", "40446    2.833213                              4.787492   \n", "40447    2.833213                              4.787492   \n", "40448    2.833213                              4.787492   \n", "\n", "       log_project_contributors_before_treatment  \\\n", "0                                       1.945910   \n", "1                                       1.945910   \n", "2                                       1.945910   \n", "3                                       1.945910   \n", "4                                       1.945910   \n", "...                                          ...   \n", "40444                                   3.178054   \n", "40445                                   3.178054   \n", "40446                                   3.178054   \n", "40447                                   3.178054   \n", "40448                                   3.178054   \n", "\n", "       log_project_age_before_treatment  project_main_language  \n", "0                              4.962845             TypeScript  \n", "1                              4.962845             TypeScript  \n", "2                              4.962845             TypeScript  \n", "3                              4.962845             TypeScript  \n", "4                              4.962845             TypeScript  \n", "...                                 ...                    ...  \n", "40444                          7.995980                      C  \n", "40445                          7.995980                      C  \n", "40446                          7.995980                      C  \n", "40447                          7.995980                      C  \n", "40448                          7.995980                      C  \n", "\n", "[40449 rows x 29 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# generate the model with each cohort group of the project characteristics just before the treatment\n", "compiled_data_test['log_project_commits_before_treatment'] = 0\n", "compiled_data_test['log_project_contributors_before_treatment'] = 0\n", "compiled_data_test['log_project_age_before_treatment'] = 0\n", "compiled_data_test['project_main_language'] = ''\n", "for cohort_id, group in compiled_data_test.groupby('cohort_id'):\n", "    treated_group = group[(group['is_treated'] == 1) & (group['relativized_time'] == 0)]\n", "    if treated_group.empty:\n", "        continue\n", "    treated_row = treated_group.iloc[0][['log_project_commits', 'log_project_contributors', 'log_project_age', 'mainLanguage']]\n", "    # rename ['log_project_commits', 'log_project_contributors', 'log_project_age'] into [log_project_commits_before_treatment, log_project_contributors_before_treatment, log_project_age_before_treatment]\n", "    compiled_data_test.loc[compiled_data_test['cohort_id'] == cohort_id, ['log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_language']] = treated_row.values\n", "compiled_data_test"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["\n", "formula_with_features_project_characteristics = \"log_pr_throughput ~ is_treated * is_post_treatment * (log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language) + C(time_cohort_effect) + C(repo_cohort_effect)\""]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["model_with_features_project_characteristics = smf.ols(\n", "    formula=formula_with_features_project_characteristics,\n", "    data=compiled_data_test\n", ").fit()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# save the model results\n", "with open(output_dir + \"model_with_features_project_characteristics_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_features_project_characteristics.summary().as_text())"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["formual_with_both_features = \"log_pr_throughput ~ is_treated * is_post_treatment * (log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language + log_tenure + log_commit_percent + log_commits) + C(time_cohort_effect) + C(repo_cohort_effect)\"\n", "model_with_both_features = smf.ols(\n", "    formula=formual_with_both_features,\n", "    data=compiled_data_test\n", ").fit()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# save the model results\n", "with open(output_dir + \"model_with_both_features_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_both_features.summary().as_text())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Regression Model added other variables"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["formula_original_with_other_variables = \"log_pr_throughput ~ is_treated * is_post_treatment + C(time_cohort_effect) + C(repo_cohort_effect) + log_project_commits + log_project_contributors + log_project_age + mainLanguage\"\n", "model_original_with_other_variables = smf.ols(\n", "    formula=formula_original_with_other_variables,\n", "    data=compiled_data_test\n", ").fit()\n", "\n", "# save the model results\n", "\n", "with open(output_dir + \"model_original_with_other_variables_20250203.txt\", \"w\") as f:\n", "    f.write(model_original_with_other_variables.summary().as_text())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["formula_with_features_left_core_dev_and_other_variables = \"log_pr_throughput ~ is_treated * is_post_treatment * (log_tenure + log_commit_percent + log_commits) + C(time_cohort_effect) + C(repo_cohort_effect) + log_project_commits + log_project_contributors + log_project_age + mainLanguage\"\n", "model_with_features_left_core_dev_and_other_variables = smf.ols(\n", "    formula=formula_with_features_left_core_dev_and_other_variables,\n", "    data=compiled_data_test\n", ").fit()\n", "\n", "# save the model results\n", "with open(output_dir + \"model_with_features_left_core_dev_and_other_variables_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_features_left_core_dev_and_other_variables.summary().as_text())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["formula_with_features_project_characteristics_and_other_variables = \"log_pr_throughput ~ is_treated * is_post_treatment * (log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language) + C(time_cohort_effect) + C(repo_cohort_effect) + log_project_commits + log_project_contributors + log_project_age + mainLanguage\"\n", "model_with_features_project_characteristics_and_other_variables = smf.ols(\n", "    formula=formula_with_features_project_characteristics_and_other_variables,\n", "    data=compiled_data_test\n", ").fit()\n", "\n", "# save the model results\n", "with open(output_dir + \"model_with_features_project_characteristics_and_other_variables_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_features_project_characteristics_and_other_variables.summary().as_text())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["formual_with_both_features_and_other_variables = \"log_pr_throughput ~ is_treated * is_post_treatment * (log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language + log_tenure + log_commit_percent + log_commits) + C(time_cohort_effect) + C(repo_cohort_effect) + log_project_commits + log_project_contributors + log_project_age + mainLanguage\"\n", "\n", "model_with_both_features_and_other_variables = smf.ols(\n", "    formula=formual_with_both_features_and_other_variables,\n", "    data=compiled_data_test\n", ").fit()\n", "\n", "# save the model results\n", "with open(output_dir + \"model_with_both_features_and_other_variables_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_both_features_and_other_variables.summary().as_text())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### model 4: add both core_dev and project characteristics before disengagement\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["regression model 5: add relativized_time to examing the effect of time heterogeneity on disengagement effect\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["0        0       -12\\n1       -11\\n2       -10\\n3      ...\n", "1        0       -12\\n1       -11\\n2       -10\\n3      ...\n", "2        0       -12\\n1       -11\\n2       -10\\n3      ...\n", "3        0       -12\\n1       -11\\n2       -10\\n3      ...\n", "4        0       -12\\n1       -11\\n2       -10\\n3      ...\n", "                               ...                        \n", "40444    0       -12\\n1       -11\\n2       -10\\n3      ...\n", "40445    0       -12\\n1       -11\\n2       -10\\n3      ...\n", "40446    0       -12\\n1       -11\\n2       -10\\n3      ...\n", "40447    0       -12\\n1       -11\\n2       -10\\n3      ...\n", "40448    0       -12\\n1       -11\\n2       -10\\n3      ...\n", "Name: relativized_time, Length: 40449, dtype: object"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_data_test['relativized_time']"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# if relativized_time > 0, change relativized_time to 'post_treatement' + relativized_time\n", "# change relativized_time to 'pre_treatment' + relativized_time\n", "compiled_data_test['relativized_time_post'] = compiled_data_test['relativized_time'].apply(lambda x: f\"post_treatment_{x}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# make the relativized_time as a categorical variable\n", "compiled_data_test['relativized_time'] = compiled_data_test['relativized_time'].astype(str)\n", "formula_with_features_relativized_time = \"log_pr_throughput ~ is_treated * is_post_treatment * relativized_time_post + C(time_cohort_effect) + C(repo_cohort_effect) + log_project_commits + log_project_contributors + log_project_age + mainLanguage\"\n", "\n", "model_with_features_relativized_time = smf.ols(\n", "    formula=formula_with_features_relativized_time,\n", "    data=compiled_data_test\n", ").fit()\n", "\n", "# save the model results\n", "with open(output_dir + \"model_with_features_relativized_time_20250203.txt\", \"w\") as f:\n", "    f.write(model_with_features_relativized_time.summary().as_text())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Parallel Trend Examine"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1996164/2232750385.py:25: UserWarning: linestyle is redundantly defined by the 'linestyle' keyword argument and the fmt string \"-o\" (-> linestyle='-'). The keyword argument will take precedence.\n", "  plt.errorbar(treatment_group['relativized_time'],\n", "/tmp/ipykernel_1996164/2232750385.py:38: UserWarning: linestyle is redundantly defined by the 'linestyle' keyword argument and the fmt string \"-o\" (-> linestyle='-'). The keyword argument will take precedence.\n", "  plt.errorbar(control_group['relativized_time'],\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = compiled_data_test\n", "\n", "# Data Cleaning\n", "# Convert 'pr_throughput' and 'relativized_time' columns to numeric, handling any errors by converting them to NaN\n", "data['pr_throughput'] = pd.to_numeric(data['pr_throughput'], errors='coerce')\n", "data['relativized_time'] = pd.to_numeric(data['relativized_time'], errors='coerce')\n", "\n", "# Remove any rows with NaN values that might have been created during the coercion\n", "data_cleaned = data.dropna(subset=['pr_throughput', 'relativized_time'])\n", "\n", "# Group by 'is_treated' and 'relativized_time' and calculate the mean, standard deviation, and count\n", "grouped = data_cleaned.groupby(['is_treated', 'relativized_time'])['pr_throughput'].agg(['mean', 'std', 'count']).reset_index()\n", "\n", "# Calculate 95% Confidence Interval for each group\n", "grouped['ci95'] = 1.96 * grouped['std'] / np.sqrt(grouped['count'])\n", "\n", "# Separate the treatment and control groups\n", "treatment_group = grouped[grouped['is_treated'] == 1]\n", "control_group = grouped[grouped['is_treated'] == 0]\n", "\n", "# Plot the Treatment and Control groups with 95% CI\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Plot Treatment group with error bars for 95% confidence intervals\n", "plt.errorbar(treatment_group['relativized_time'], \n", "             treatment_group['mean'], \n", "             yerr=treatment_group['ci95'], \n", "             label='Treatment Group', \n", "             fmt='-o', \n", "             color='royalblue', \n", "             capsize=6, \n", "             markersize=10, \n", "             elinewidth=2, \n", "             linestyle='-', \n", "             zorder=5)\n", "\n", "# Plot Control group with error bars for 95% confidence intervals\n", "plt.errorbar(control_group['relativized_time'], \n", "             control_group['mean'], \n", "             yerr=control_group['ci95'], \n", "             label='Control Group', \n", "             fmt='-o', \n", "             color='tomato', \n", "             capsize=6, \n", "             markersize=10, \n", "             elinewidth=2, \n", "             linestyle='-', \n", "             zorder=4)\n", "\n", "# Add labels, title, and legend with increased font sizes for better readability\n", "plt.xlabel('Relativized Time', fontsize=14)\n", "plt.ylabel('Average PR Throughput', fontsize=14)\n", "plt.title('Average PR Throughput with 95% Confidence Intervals (Treatment vs Control)', fontsize=16)\n", "plt.legend(title='Group', fontsize=12)\n", "\n", "# Customize grid for better visibility\n", "plt.grid(True, linestyle='--', alpha=0.6)\n", "\n", "# Tighten layout to ensure all elements are visible and nothing is cut off\n", "plt.tight_layout()\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data['pr_throughput'] = pd.to_numeric(data['pr_throughput'], errors='coerce')\n", "data['relativized_time'] = pd.to_numeric(data['relativized_time'], errors='coerce')\n", "\n", "# Remove any rows with NaN values that might have been created during the coercion\n", "data_cleaned = data.dropna(subset=['pr_throughput', 'relativized_time'])\n", "\n", "# Group by 'is_treated' and 'relativized_time' and calculate the mean\n", "grouped = data_cleaned.groupby(['is_treated', 'relativized_time'])['pr_throughput'].agg(['mean']).reset_index()\n", "\n", "# Separate the treatment and control groups\n", "treatment_group = grouped[grouped['is_treated'] == 1]\n", "control_group = grouped[grouped['is_treated'] == 0]\n", "\n", "# Plot the Treatment and Control groups without error bars\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Plot Treatment group\n", "plt.plot(treatment_group['relativized_time'], \n", "         treatment_group['mean'], \n", "         label='Treatment Group', \n", "         marker='o', \n", "         color='royalblue', \n", "         markersize=10, \n", "         linestyle='-', \n", "         zorder=5)\n", "\n", "# Plot Control group\n", "plt.plot(control_group['relativized_time'], \n", "         control_group['mean'], \n", "         label='Control Group', \n", "         marker='o', \n", "         color='tomato', \n", "         markersize=10, \n", "         linestyle='-', \n", "         zorder=4)\n", "\n", "# Add labels, title, and legend with increased font sizes for better readability\n", "plt.xlabel('Relativized Time', fontsize=14)\n", "plt.ylabel('Average PR Throughput', fontsize=14)\n", "plt.title('Average PR Throughput (Treatment vs Control)', fontsize=16)\n", "plt.legend(title='Group', fontsize=12)\n", "\n", "# Customize grid for better visibility\n", "plt.grid(True, linestyle='--', alpha=0.6)\n", "\n", "# Tighten layout to ensure all elements are visible and nothing is cut off\n", "plt.tight_layout()\n", "\n", "# Display the plot\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}