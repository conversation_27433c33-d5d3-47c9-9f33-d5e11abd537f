#!/usr/bin/env python3
"""
测试内存修复后的PSM匹配函数
专注于内存使用监控
"""
import pandas as pd
import numpy as np
import logging
import time
import gc
import psutil
from datetime import datetime
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_memory_usage():
    """获取当前内存使用量(MB)"""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024

def monitor_memory_during_psm():
    """监控PSM过程中的内存使用"""
    print("="*60)
    print("TESTING MEMORY-FIXED PSM ALGORITHM")
    print("="*60)
    
    # 监控内存使用
    initial_memory = get_memory_usage()
    print(f"Initial memory usage: {initial_memory:.1f}MB")
    
    start_time = time.time()
    
    # 读取测试数据
    test_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365_test_sample.csv'
    print(f"Loading test data from: {test_file}")
    
    if not os.path.exists(test_file):
        print(f"Error: Test file not found: {test_file}")
        return
    
    p_test = pd.read_csv(test_file)
    print(f"Test data shape: {p_test.shape}")
    
    after_load_memory = get_memory_usage()
    print(f"Memory after loading data: {after_load_memory:.1f}MB (+{after_load_memory-initial_memory:.1f}MB)")
    
    # 数据预处理
    p_test_attrition = p_test[p_test['someone_left'] == 1].copy()
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'].notnull()]
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'] != 0.5]
    
    print(f"Attrition data shape after filtering: {p_test_attrition.shape}")
    
    p_test = p_test.fillna(0)
    p_test_attrition = p_test_attrition.fillna(0)
    
    if 'burst' in p_test_attrition.columns:
        p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)
    
    after_preprocess_memory = get_memory_usage()
    print(f"Memory after preprocessing: {after_preprocess_memory:.1f}MB (+{after_preprocess_memory-after_load_memory:.1f}MB)")
    
    # 导入修复后的函数
    print("Importing memory-fixed PSM function...")
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "psm_module", 
            "src_new_new/20250629_04_PSM_matching_multi_limits_improved.py"
        )
        psm_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(psm_module)
        
        print("Successfully imported PSM module")
        
        # 调用修复后的PSM函数进行匹配
        print("Starting PSM matching with memory monitoring...")
        before_psm_memory = get_memory_usage()
        print(f"Memory before PSM: {before_psm_memory:.1f}MB")
        
        # 创建一个自定义logger来捕获内存信息
        memory_logger = logging.getLogger('memory_monitor')
        memory_logger.setLevel(logging.INFO)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter("%(asctime)s [MEMORY] %(message)s")
        console_handler.setFormatter(formatter)
        memory_logger.addHandler(console_handler)
        
        matched_pairs, treatment_features_df, control_features_df = psm_module.compile_control_group_psm_knn(
            p_test_attrition,
            p_test['repo_name'].tolist(),
            p_test,
            n_neighbors=5,
            timewindow_weeks=12,
            feature_columns=['feature_sigmod_add'],
            extra_candidates=10,
            batch_size=1000,
            logger=memory_logger
        )
        
        after_psm_memory = get_memory_usage()
        print(f"Memory after PSM: {after_psm_memory:.1f}MB (+{after_psm_memory-before_psm_memory:.1f}MB)")
        
    except Exception as e:
        print(f"Error during PSM matching: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # 强制垃圾回收
    gc.collect()
    final_memory = get_memory_usage()
    
    print(f"\n=== MEMORY-FIXED PSM RESULTS ===")
    print(f"Processing time: {processing_time:.2f} seconds")
    print(f"Total matched pairs: {len(matched_pairs)}")
    print(f"Treatment features shape: {treatment_features_df.shape}")
    print(f"Control features shape: {control_features_df.shape}")
    print(f"Peak memory usage: {after_psm_memory:.1f}MB")
    print(f"Final memory usage: {final_memory:.1f}MB")
    print(f"Memory increase during PSM: {after_psm_memory-before_psm_memory:.1f}MB")
    print(f"Memory cleaned up: {after_psm_memory-final_memory:.1f}MB")
    
    # 显示一些详细信息
    if matched_pairs:
        first_key = list(matched_pairs.keys())[0]
        first_match = matched_pairs[first_key]
        print(f"\nSample match (burst {first_key}):")
        print(f"  Repo: {first_match['repo_name']}")
        print(f"  Treatment time: {first_match['treatment_time']}")
        print(f"  Number of controls: {len(first_match['controls'])}")
        print(f"  Control repos: {[c['repo_name'] for c in first_match['controls']]}")
    
    return matched_pairs, processing_time, after_psm_memory

def simulate_large_dataset():
    """模拟处理大数据集的情况"""
    print("\n" + "="*60)
    print("SIMULATING LARGE DATASET PROCESSING")
    print("="*60)
    
    # 创建一个较大的模拟数据集
    print("Creating simulated large dataset...")
    
    # 模拟更多的treatment events
    np.random.seed(42)
    n_treatments = 1000  # 模拟1000个treatment events
    n_controls = 5000    # 模拟5000个control events
    
    # 创建模拟数据
    treatment_data = pd.DataFrame({
        'repo_name': [f'treatment_repo_{i}' for i in range(n_treatments)],
        'burst': np.random.randint(1, 10000, n_treatments),
        'someone_left': 1,
        'standardized_time_weeks': np.random.randint(1, 500, n_treatments),
        'feature_sigmod_add': np.random.uniform(0.1, 0.9, n_treatments)
    })
    
    control_data = pd.DataFrame({
        'repo_name': [f'control_repo_{i}' for i in range(n_controls)],
        'burst': 0,
        'someone_left': np.random.choice([0, 1], n_controls, p=[0.9, 0.1]),
        'standardized_time_weeks': np.random.randint(1, 500, n_controls),
        'feature_sigmod_add': np.random.uniform(0.1, 0.9, n_controls)
    })
    
    # 合并数据
    full_data = pd.concat([treatment_data, control_data], ignore_index=True)
    
    print(f"Simulated dataset shape: {full_data.shape}")
    print(f"Treatment events: {len(treatment_data)}")
    print(f"Control events: {len(control_data)}")
    
    initial_memory = get_memory_usage()
    print(f"Memory before simulation: {initial_memory:.1f}MB")
    
    # 测试内存修复版本
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "psm_module", 
            "src_new_new/20250629_04_PSM_matching_multi_limits_improved.py"
        )
        psm_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(psm_module)
        
        memory_logger = logging.getLogger('simulation_memory')
        memory_logger.setLevel(logging.INFO)
        
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter("%(asctime)s [SIM] %(message)s")
        console_handler.setFormatter(formatter)
        memory_logger.addHandler(console_handler)
        
        print("Starting simulated PSM matching...")
        start_time = time.time()
        
        matched_pairs, _, _ = psm_module.compile_control_group_psm_knn(
            treatment_data,
            full_data['repo_name'].tolist(),
            full_data,
            n_neighbors=5,
            timewindow_weeks=12,
            feature_columns=['feature_sigmod_add'],
            extra_candidates=10,
            batch_size=1000,
            logger=memory_logger
        )
        
        end_time = time.time()
        final_memory = get_memory_usage()
        
        print(f"\n=== SIMULATION RESULTS ===")
        print(f"Processing time: {end_time - start_time:.2f} seconds")
        print(f"Total matched pairs: {len(matched_pairs)}")
        print(f"Final memory usage: {final_memory:.1f}MB")
        print(f"Memory increase: {final_memory - initial_memory:.1f}MB")
        
    except Exception as e:
        print(f"Simulation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Available memory: {psutil.virtual_memory().available / 1024 / 1024 / 1024:.1f}GB")
    
    try:
        # 测试真实数据
        result = monitor_memory_during_psm()
        if result:
            print("✅ Memory-fixed PSM test completed successfully!")
        else:
            print("❌ Memory-fixed PSM test failed!")
            
        # 测试模拟大数据集
        simulate_large_dataset()
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
