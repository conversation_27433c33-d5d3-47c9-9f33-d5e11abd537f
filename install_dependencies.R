#!/usr/bin/env Rscript

# =============================================================================
# Dependency Installation Script for DID Analysis
# Purpose: Install all required packages for performance optimization
# =============================================================================

cat("🚀 Installing dependencies for DID Analysis Performance Optimization\n")
cat("=", paste(rep("=", 60), collapse=""), "\n")

# Function to install packages with error handling
install_package_safe <- function(package_name, repo = "https://cran.rstudio.com/") {
  cat("📦 Installing", package_name, "...\n")
  
  if (!requireNamespace(package_name, quietly = TRUE)) {
    tryCatch({
      install.packages(package_name, repos = repo, dependencies = TRUE)
      cat("✅", package_name, "installed successfully\n")
    }, error = function(e) {
      cat("❌ Failed to install", package_name, ":", e$message, "\n")
      return(FALSE)
    })
  } else {
    cat("✅", package_name, "already installed\n")
  }
  return(TRUE)
}

# Core statistical packages (required)
core_packages <- c(
  "stats",
  "lme4",
  "lmerTest",
  "broom",
  "broom.mixed",
  "MuMIn"
)

# Parallel processing packages (for performance)
parallel_packages <- c(
  "parallel",
  "doParallel", 
  "foreach",
  "future",
  "future.apply",
  "future.callr"
)

# High-performance data manipulation packages
data_packages <- c(
  "data.table",
  "dplyr",
  "readr",
  "vroom"
)

# Memory and performance optimization packages
optimization_packages <- c(
  "memoise",
  "microbenchmark",
  "profvis"
)

# Visualization packages (optional)
viz_packages <- c(
  "ggplot2",
  "knitr"
)

# Install packages by category
cat("\n📊 Installing Core Statistical Packages:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
for (pkg in core_packages) {
  install_package_safe(pkg)
}

cat("\n🚀 Installing Parallel Processing Packages:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
for (pkg in parallel_packages) {
  install_package_safe(pkg)
}

cat("\n⚡ Installing High-Performance Data Packages:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
for (pkg in data_packages) {
  install_package_safe(pkg)
}

cat("\n🔧 Installing Optimization Packages:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
for (pkg in optimization_packages) {
  install_package_safe(pkg)
}

cat("\n📈 Installing Visualization Packages:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
for (pkg in viz_packages) {
  install_package_safe(pkg)
}

# Check for optional GPU support
cat("\n🎮 Checking for GPU Support:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
if (requireNamespace("gpuR", quietly = TRUE)) {
  cat("✅ GPU support available (gpuR package)\n")
} else {
  cat("ℹ️  GPU support not available (optional)\n")
  cat("   To enable GPU acceleration, install: install.packages('gpuR')\n")
}

# Verify installations
cat("\n🔍 Verifying Package Installations:\n")
cat(paste(rep("-", 40), collapse=""), "\n")

all_packages <- c(core_packages, parallel_packages, data_packages, optimization_packages, viz_packages)
installed_packages <- c()
missing_packages <- c()

for (pkg in all_packages) {
  if (requireNamespace(pkg, quietly = TRUE)) {
    installed_packages <- c(installed_packages, pkg)
    cat("✅", pkg, "\n")
  } else {
    missing_packages <- c(missing_packages, pkg)
    cat("❌", pkg, "\n")
  }
}

# Summary
cat("\n", paste(rep("=", 60), collapse=""), "\n")
cat("📋 INSTALLATION SUMMARY\n")
cat(paste(rep("=", 60), collapse=""), "\n")
cat("✅ Successfully installed:", length(installed_packages), "packages\n")
if (length(missing_packages) > 0) {
  cat("❌ Failed to install:", length(missing_packages), "packages\n")
  cat("   Missing packages:", paste(missing_packages, collapse = ", "), "\n")
  cat("\n💡 To manually install missing packages:\n")
  for (pkg in missing_packages) {
    cat("   install.packages('", pkg, "')\n", sep = "")
  }
} else {
  cat("🎉 All packages installed successfully!\n")
}

# System information
cat("\n💻 System Information:\n")
cat(paste(rep("-", 40), collapse=""), "\n")
cat("R version:", R.version.string, "\n")
cat("Platform:", R.version$platform, "\n")
cat("CPU cores:", parallel::detectCores(), "\n")

# Memory information (Linux only)
if (Sys.info()["sysname"] == "Linux") {
  tryCatch({
    memory_info <- system("free -h | grep '^Mem:'", intern = TRUE)
    cat("Memory:", memory_info, "\n")
  }, error = function(e) {
    cat("Memory: Unable to determine\n")
  })
}

cat("\n🚀 Ready to run performance-optimized DID analysis!\n")
cat("📖 See README_PERFORMANCE_OPTIMIZATION.md for usage instructions\n") 