{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:05:56.292271Z", "start_time": "2024-11-25T07:05:55.962619Z"}, "collapsed": true}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "f4f8a47878185074", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:08:15.943646Z", "start_time": "2024-11-25T07:08:15.834331Z"}}, "outputs": [], "source": ["# project_sampled = pd.read_csv('../data/sample_projects_quartiles.csv')\n", "# project_names = project_sampled['name'].tolist()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "51421e57", "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "\n", "# 连接到 MongoDB 实例\n", "client = MongoClient('mongodb://localhost:27017/')  # 替换为你的 MongoDB 实例的 URI\n", "\n", "# 选择目标数据库和集合\n", "db = client['disengagement']  # 选择 disengagement 数据库\n", "cache_collection = db[\"progress_cache\"]\n", "finished_projects = cache_collection.find({\n", "    \"commits_finished\": 1,\n", "    \"pr_finished\": 1,\n", "    \"pr_review_finished\": 1\n", "}, {\"repo_name\": 1})\n", "repos = [project[\"repo_name\"] for project in finished_projects]\n", "project_names = repos"]}, {"cell_type": "code", "execution_count": 3, "id": "6f417b3ed7851dc7", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:56:07.515947Z", "start_time": "2024-11-25T07:56:07.495954Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "sha", "rawType": "object", "type": "string"}, {"name": "author_name", "rawType": "object", "type": "string"}, {"name": "author_email", "rawType": "object", "type": "string"}, {"name": "author_login", "rawType": "object", "type": "unknown"}, {"name": "committer_name", "rawType": "object", "type": "string"}, {"name": "committer_email", "rawType": "object", "type": "string"}, {"name": "committer_login", "rawType": "object", "type": "unknown"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "message", "rawType": "object", "type": "string"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "parent_count", "rawType": "int64", "type": "integer"}, {"name": "parent_shas", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "36ea84e9-a2ad-409a-90e0-8d9f6de24776", "rows": [["0", "25102dced99c444243f0cc7c3328116b892bb5d8", "<PERSON><PERSON><PERSON>", "<EMAIL>", "teigen", "<PERSON><PERSON><PERSON>", "<EMAIL>", "teigen", "2010-03-28T22:26:45Z", "Support for 2.8.0.Beta1 (breaks 2.7.x)", "cucumber-attic/cuke4duke", "1", "['243102535244bf7c208efe704ce91bf348295399']"], ["1", "fd5a0489ad1e067e54f62a81743721988b74e614", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-26T18:26:16Z", "Clean up Javascript DSL a little", "cucumber-attic/cuke4duke", "1", "['243102535244bf7c208efe704ce91bf348295399']"], ["2", "243102535244bf7c208efe704ce91bf348295399", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-26T13:14:11Z", "Moving Java annotations into one file, so that it's easier to regenerate files.", "cucumber-attic/cuke4duke", "1", "['dfb3ce875c186c3ea879dc16ad12693b32c1ae13']"], ["3", "dfb3ce875c186c3ea879dc16ad12693b32c1ae13", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-26T11:30:20Z", "Update with latest Gherkin i18n", "cucumber-attic/cuke4duke", "1", "['cb223fdc881c4d6f5df8f58b7b52fba47f497233']"], ["4", "cb223fdc881c4d6f5df8f58b7b52fba47f497233", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-24T15:27:36Z", "Indentation", "cucumber-attic/cuke4duke", "1", "['6916b8d991416c6526c3fe489c4cd0104ac49515']"], ["5", "ba6abf3a7a3a8c2c98621f5816430aa9d4964b2e", "unknown", "<PERSON>@.(none)", null, "unknown", "<PERSON>@.(none)", null, "2010-03-12T20:37:32Z", "added the plugin for source generation to the pom root pom file", "cucumber-attic/cuke4duke", "1", "['98152bb3b35413c96bfdc4d3867d5090fb62b98b']"], ["6", "6916b8d991416c6526c3fe489c4cd0104ac49515", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-10T19:52:16Z", "Upgrade gems and fix encoding", "cucumber-attic/cuke4duke", "1", "['98152bb3b35413c96bfdc4d3867d5090fb62b98b']"], ["7", "98152bb3b35413c96bfdc4d3867d5090fb62b98b", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-02T22:25:55Z", "Release 0.2.4", "cucumber-attic/cuke4duke", "1", "['85354f4cf2f7647bf6115155fc3d7d08c52b9a77']"], ["8", "85354f4cf2f7647bf6115155fc3d7d08c52b9a77", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-02T22:18:41Z", "Update i18n", "cucumber-attic/cuke4duke", "1", "['c1f29acba715939afee2b1ab1b30d0f58b936d48']"], ["9", "c1f29acba715939afee2b1ab1b30d0f58b936d48", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-01T14:22:54Z", "Upgrade to Clojure 1.1.0", "cucumber-attic/cuke4duke", "1", "['750274b10bdd769ee904a5082c43f9c8377117dc']"], ["10", "750274b10bdd769ee904a5082c43f9c8377117dc", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-03-01T10:09:38Z", "Upgrade to JUnit 4.8.1, <PERSON><PERSON> 1.8.0, <PERSON><PERSON>C<PERSON><PERSON> 2.10.2, <PERSON><PERSON><PERSON> 1.8.2, <PERSON><PERSON><PERSON> 1.7.1", "cucumber-attic/cuke4duke", "1", "['4eb8e65252a198199b507b00bedcd8cf0c4c7c13']"], ["11", "4eb8e65252a198199b507b00bedcd8cf0c4c7c13", "<PERSON><PERSON>", "<EMAIL>", "ckoenig", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-25T08:41:58Z", "Reorder classpath, s.t. test-classes are upfront.\n\nChanged classpath creation, to ensure that the test-classes come\nfirst. This way it is possible to override configuration resources\nfor testing.", "cucumber-attic/cuke4duke", "1", "['1ee80eb8958629932c2d4983f13d3d4e84ccd7cd']"], ["12", "1ee80eb8958629932c2d4983f13d3d4e84ccd7cd", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T15:23:01Z", "Updated i18n support for Scala", "cucumber-attic/cuke4duke", "1", "['3447637ee7f093b6ba1867723fc47adb07bd49fd']"], ["13", "3447637ee7f093b6ba1867723fc47adb07bd49fd", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T15:10:01Z", "ScalaDsl and Languages have moved to cuke4duke.scala.Dsl and cuke4duke.scala.I18n", "cucumber-attic/cuke4duke", "1", "['5ee5094a5a9d0b2e17a7a60718194f6a84584a18']"], ["14", "5ee5094a5a9d0b2e17a7a60718194f6a84584a18", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T14:53:33Z", "Scala step definitions must explicitly specify i18n language. Ex: extends ScalaDsl with EN", "cucumber-attic/cuke4duke", "1", "['4028d7923d65e10734bd251da3b36a940b3cc0c5']"], ["15", "4028d7923d65e10734bd251da3b36a940b3cc0c5", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T11:54:49Z", "Comply with small change in Gherkin", "cucumber-attic/cuke4duke", "1", "['0ce587a5279d239c74411a64b065f12e646f21de']"], ["16", "0ce587a5279d239c74411a64b065f12e646f21de", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T11:51:41Z", "Added a chinese example, just to verify that i18n step definitions work (they do).", "cucumber-attic/cuke4duke", "1", "['56dfde35b7f52b0cd555ba3c310191a72f0fa72b']"], ["17", "56dfde35b7f52b0cd555ba3c310191a72f0fa72b", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T11:40:23Z", "Moved remaining annotations to cuke4duke.annotation package.", "cucumber-attic/cuke4duke", "1", "['69a12f8358f17883a2bfa05d0f7d8714c0ee03f8']"], ["18", "69a12f8358f17883a2bfa05d0f7d8714c0ee03f8", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-22T11:16:55Z", "I18n support for all of Cucumber's 40 i18n languages. (Java annotations only).", "cucumber-attic/cuke4duke", "1", "['b46c029bf6939bc7f0371da31af0ba2011699b78']"], ["19", "b46c029bf6939bc7f0371da31af0ba2011699b78", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-21T22:27:19Z", "Added future Chinese stepdef annotation class - just making sure we can compile.", "cucumber-attic/cuke4duke", "1", "['5a639bbe8ae58b0b189129a53e74bd3f98f7e202']"], ["20", "5a639bbe8ae58b0b189129a53e74bd3f98f7e202", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-21T15:42:36Z", "Make annotations inner classes of cuke4duke.annotation.English", "cucumber-attic/cuke4duke", "1", "['1eb791d8dce4336524a091157494913b1ea95388']"], ["21", "1eb791d8dce4336524a091157494913b1ea95388", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-21T14:04:24Z", "Annotating annotations so we can more easily support i18n annotations.", "cucumber-attic/cuke4duke", "1", "['4b6129ea94445424ad0b7ad51657ff8c9a8cb332']"], ["22", "4b6129ea94445424ad0b7ad51657ff8c9a8cb332", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-20T20:02:21Z", "Added ask, embed and announce", "cucumber-attic/cuke4duke", "1", "['39b98f42815b457abe2e64ffa144868cb47eca25']"], ["23", "39b98f42815b457abe2e64ffa144868cb47eca25", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-08T18:59:33Z", "fix compile error", "cucumber-attic/cuke4duke", "1", "['a3c93526fb5fd798f0dcf2a8cd086d5395124295']"], ["24", "a3c93526fb5fd798f0dcf2a8cd086d5395124295", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-08T18:05:58Z", "Attribution. Closes #60.", "cucumber-attic/cuke4duke", "1", "['090440eb2d1713b0fdb041f725de13d817dbc306']"], ["25", "019a5a7c5b1bcbcf00434d417a91fadb649b91a7", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-08T18:03:57Z", "Align new table API more with native Ruby Cucumber", "cucumber-attic/cuke4duke", "1", "['019b6fe71709d45e2d9cfeb5ee4982b2509c3f8a']"], ["26", "336ca65b945a10119e4823ae84d1276c92e6b86c", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-02-08T14:32:27Z", "Attribution. Closes #61", "cucumber-attic/cuke4duke", "1", "['8be9be4e4ea15f0ab9e8c196716e5cb83e63d3ca']"], ["27", "aabb53a686bce74293fc8db9c7faa9178ea027de", "<PERSON><PERSON><PERSON>", "<EMAIL>", "teigen", "<PERSON><PERSON><PERSON>", "<EMAIL>", "teigen", "2010-02-07T11:44:38Z", "issue #61 - support for tables and multiline strings\n* added more examples\n* refactored the execution/recording handle code in the scalaDsl\n* implicit conversion from <PERSON>le to Fun of Function0 allowing to call step from a stepdefinition with call-by-name\n* changed scala version from 2.7.6 to 2.7.7", "cucumber-attic/cuke4duke", "1", "['4f55db352673d4fa53e30ed877c3944ae842e22b']"], ["28", "820dd84822e9f9b4322f2a3d96d80823c75a7ac9", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-31T15:48:43Z", "Added a failing feature for #61.", "cucumber-attic/cuke4duke", "1", "['2c141c9aa67c673f4dbc38d27ff41b22b468f96b']"], ["29", "2c141c9aa67c673f4dbc38d27ff41b22b468f96b", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-31T15:42:49Z", "Make it clearer that we ignore errors. <PERSON><PERSON> up.", "cucumber-attic/cuke4duke", "1", "['4f55db352673d4fa53e30ed877c3944ae842e22b']"], ["30", "019b6fe71709d45e2d9cfeb5ee4982b2509c3f8a", "<PERSON>", "<EMAIL>", "senny", "<PERSON>", "<EMAIL>", "senny", "2010-01-26T10:00:24Z", "support Cucumber::Ast::Table#map_columns! and map_headers!", "cucumber-attic/cuke4duke", "1", "['4f55db352673d4fa53e30ed877c3944ae842e22b']"], ["31", "4f55db352673d4fa53e30ed877c3944ae842e22b", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-18T00:39:16Z", "Release 0.2.3", "cucumber-attic/cuke4duke", "1", "['bcd537b6995eef378a2c608e3be643acde68784b']"], ["32", "bcd537b6995eef378a2c608e3be643acde68784b", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-18T00:37:59Z", "Release 0.2.3", "cucumber-attic/cuke4duke", "1", "['e79ce300332347ba9d69f720703f4096454232cc']"], ["33", "e79ce300332347ba9d69f720703f4096454232cc", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-14T15:36:05Z", "Lazy instantiation of WebDriver - prevents browser from starting unless you're running a scenario that uses it.", "cucumber-attic/cuke4duke", "1", "['d6a98e71b5959c88f643b383bb10473749319e6c']"], ["34", "d6a98e71b5959c88f643b383bb10473749319e6c", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-14T00:17:41Z", "Added debugging example", "cucumber-attic/cuke4duke", "1", "['7df189c17f5acc2035ab5511f5a0ebe401e380d9']"], ["35", "7df189c17f5acc2035ab5511f5a0ebe401e380d9", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-11T11:35:51Z", "Formatting", "cucumber-attic/cuke4duke", "1", "['bde872b5ff62b00e73f8b799898739cf2256ffb7']"], ["36", "bde872b5ff62b00e73f8b799898739cf2256ffb7", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-11T10:36:47Z", "Added back registering of stepdefs for each scenario in Java. Added EJB3 example to build.", "cucumber-attic/cuke4duke", "1", "['3f08878e7cb504d801f36a38dac14a302942626b']"], ["37", "b3fd6dbef52758a97533106c368deaa1ad99c8d7", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-11T09:37:07Z", "For Java, only register stepdefs once.", "cucumber-attic/cuke4duke", "1", "['3575bf40cde229e75d00594604db1f0159e47a79']"], ["38", "3575bf40cde229e75d00594604db1f0159e47a79", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-10T23:35:40Z", "All tests passing again.", "cucumber-attic/cuke4duke", "1", "['114d26ff144bb8260cc9f2d1fdfb98b65e04d23a']"], ["39", "1d8700d7722874e35280ba5fb38ba526c96831a3", "jbandi", "<EMAIL>", null, "jbandi", "<EMAIL>", null, "2010-01-10T00:48:04Z", "Initial commit", "cucumber-attic/cuke4duke", "1", "['831ad02e403247e42cb564fe563b045da8d69e96']"], ["40", "114d26ff144bb8260cc9f2d1fdfb98b65e04d23a", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-06T16:18:17Z", "Refactor transforms so that default transforms don't need an object factory. Better lifecycle for stepdefinition objects.", "cucumber-attic/cuke4duke", "1", "['831ad02e403247e42cb564fe563b045da8d69e96']"], ["41", "831ad02e403247e42cb564fe563b045da8d69e96", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-03T14:56:48Z", "Update deploy script", "cucumber-attic/cuke4duke", "1", "['af9108d8ac15918ec2cfe4b324cd0e0a57a68c2f']"], ["42", "af9108d8ac15918ec2cfe4b324cd0e0a57a68c2f", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-03T14:50:55Z", "Releasing", "cucumber-attic/cuke4duke", "1", "['992673c0f57c32605ef3180ed9fdb5b4b5d79d88']"], ["43", "992673c0f57c32605ef3180ed9fdb5b4b5d79d88", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-03T14:46:03Z", "Bump dependency", "cucumber-attic/cuke4duke", "1", "['b6894108e1674a09f02393ff5fa0448434ca3dba']"], ["44", "b6894108e1674a09f02393ff5fa0448434ca3dba", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2010-01-03T14:04:23Z", "Release 0.2.2", "cucumber-attic/cuke4duke", "1", "['aa508fec23e9a40701af5fb64f9ae746da82770c']"], ["45", "aa508fec23e9a40701af5fb64f9ae746da82770c", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2009-12-31T03:54:32Z", "Updates for cucumber master", "cucumber-attic/cuke4duke", "1", "['ec11b9f250176796b484f201e8f0d19c5246517a']"], ["46", "ec11b9f250176796b484f201e8f0d19c5246517a", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2009-12-28T14:53:53Z", "Attribution", "cucumber-attic/cuke4duke", "1", "['4cf2382390e99b19e0f527f848bc55035d4b563a']"], ["47", "3ff8ba82574c993f3e2d6621a976b5c8cae1af51", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2009-12-28T14:36:31Z", "Upgrade to Ioke P-0.4.0", "cucumber-attic/cuke4duke", "1", "['b93a9bea04a3737b89641e978acf9dd099e2922a']"], ["48", "b93a9bea04a3737b89641e978acf9dd099e2922a", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2009-12-28T14:20:40Z", "Upgrade to Groovy 1.7.0", "cucumber-attic/cuke4duke", "1", "['c813a9c3f34ea2d15e9c326c37813b53a4c3261f']"], ["49", "ad07f57410031991d2a67a4711dbd717d55dbd0b", "<PERSON><PERSON><PERSON>", "<EMAIL>", "ovstetun", "<PERSON><PERSON><PERSON>", "<EMAIL>", "ovstetun", "2009-12-26T15:37:07Z", "Update spring example to 3.0.0.RELEASE. Closes #52", "cucumber-attic/cuke4duke", "1", "['42e5e8eb72c51fea3eca6969b1a27f30c3cd16df']"]], "shape": {"columns": 12, "rows": 539}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sha</th>\n", "      <th>author_name</th>\n", "      <th>author_email</th>\n", "      <th>author_login</th>\n", "      <th>committer_name</th>\n", "      <th>committer_email</th>\n", "      <th>committer_login</th>\n", "      <th>date</th>\n", "      <th>message</th>\n", "      <th>repo_name</th>\n", "      <th>parent_count</th>\n", "      <th>parent_shas</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>25102dced99c444243f0cc7c3328116b892bb5d8</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>teigen</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>teigen</td>\n", "      <td>2010-03-28T22:26:45Z</td>\n", "      <td>Support for 2.8.0.Beta1 (breaks 2.7.x)</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['243102535244bf7c208efe704ce91bf348295399']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>fd5a0489ad1e067e54f62a81743721988b74e614</td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2010-03-26T18:26:16Z</td>\n", "      <td>Clean up Javascript DSL a little</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['243102535244bf7c208efe704ce91bf348295399']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>243102535244bf7c208efe704ce91bf348295399</td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2010-03-26T13:14:11Z</td>\n", "      <td>Moving Java annotations into one file, so that...</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['dfb3ce875c186c3ea879dc16ad12693b32c1ae13']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>dfb3ce875c186c3ea879dc16ad12693b32c1ae13</td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2010-03-26T11:30:20Z</td>\n", "      <td>Update with latest Gherkin i18n</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['cb223fdc881c4d6f5df8f58b7b52fba47f497233']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>cb223fdc881c4d6f5df8f58b7b52fba47f497233</td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>as<PERSON>.<EMAIL></td>\n", "      <td>as<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2010-03-24T15:27:36Z</td>\n", "      <td>Indentation</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['6916b8d991416c6526c3fe489c4cd0104ac49515']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>534</th>\n", "      <td>b76858db09fd52d76edc50c60e04f7aac1f0d07f</td>\n", "      <td>oc</td>\n", "      <td><EMAIL></td>\n", "      <td>oc</td>\n", "      <td>unknown</td>\n", "      <td>Ut<PERSON><PERSON>@.(none)</td>\n", "      <td>NaN</td>\n", "      <td>2009-05-06T16:46:47Z</td>\n", "      <td>JRuby facet (IDEA)</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['98ba871a87862f78b94a064a50f1d31f77b54746']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>535</th>\n", "      <td>98ba871a87862f78b94a064a50f1d31f77b54746</td>\n", "      <td>unknown</td>\n", "      <td>Ut<PERSON><PERSON>@.(none)</td>\n", "      <td>NaN</td>\n", "      <td>unknown</td>\n", "      <td>Ut<PERSON><PERSON>@.(none)</td>\n", "      <td>NaN</td>\n", "      <td>2009-05-06T16:44:26Z</td>\n", "      <td>Formatting...</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['265029e7a54b3f4bcc239e3ea2ab7ee6b67d3529']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>536</th>\n", "      <td>265029e7a54b3f4bcc239e3ea2ab7ee6b67d3529</td>\n", "      <td>U-PC-E4801DB21500\\Utvikling</td>\n", "      <td>Utvik<PERSON>@pc-e4801db21500.(none)</td>\n", "      <td>NaN</td>\n", "      <td>U-PC-E4801DB21500\\Utvikling</td>\n", "      <td>Utvik<PERSON>@pc-e4801db21500.(none)</td>\n", "      <td>NaN</td>\n", "      <td>2009-05-06T16:25:27Z</td>\n", "      <td>Added test compile path to AbstractJRubyMojo (...</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['05d408c198335a7cdd485a9b7b98fa1dec51d04c']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>537</th>\n", "      <td>c9fa125d50a7384b6583ffecf9a7af8ae847bd42</td>\n", "      <td>T<PERSON>d <PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>ovstetun</td>\n", "      <td>T<PERSON>d <PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>ovstetun</td>\n", "      <td>2009-05-06T11:50:06Z</td>\n", "      <td>Added an example showing how to use spring aut...</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['fc9892a3df41c085fb985f151edae86125e8765f']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>538</th>\n", "      <td>fc9892a3df41c085fb985f151edae86125e8765f</td>\n", "      <td>T<PERSON>d <PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>ovstetun</td>\n", "      <td>T<PERSON>d <PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>ovstetun</td>\n", "      <td>2009-05-06T11:49:08Z</td>\n", "      <td>Added support for spring autowiring into Step ...</td>\n", "      <td>cucumber-attic/cuke4duke</td>\n", "      <td>1</td>\n", "      <td>['f47e0dbd50e158d5408939850293e59bfabbe292']</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>539 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                                          sha                  author_name  \\\n", "0    25102dced99c444243f0cc7c3328116b892bb5d8            <PERSON><PERSON><PERSON>   \n", "1    fd5a0489ad1e067e54f62a81743721988b74e614                <PERSON><PERSON><PERSON><PERSON><PERSON>   \n", "2    243102535244bf7c208efe704ce91bf348295399                <PERSON><PERSON><PERSON><PERSON><PERSON>   \n", "3    dfb3ce875c186c3ea879dc16ad12693b32c1ae13                as<PERSON><PERSON><PERSON><PERSON>   \n", "4    cb223fdc881c4d6f5df8f58b7b52fba47f497233                <PERSON><PERSON><PERSON><PERSON><PERSON>   \n", "..                                        ...                          ...   \n", "534  b76858db09fd52d76edc50c60e04f7aac1f0d07f                           oc   \n", "535  98ba871a87862f78b94a064a50f1d31f77b54746                      unknown   \n", "536  265029e7a54b3f4bcc239e3ea2ab7ee6b67d3529  U-PC-E4801DB21500\\Utvikling   \n", "537  c9fa125d50a7384b6583ffecf9a7af8ae847bd42        Trond <PERSON>   \n", "538  fc9892a3df41c085fb985f151edae86125e8765f        Trond <PERSON>   \n", "\n", "                         author_email   author_login  \\\n", "0                    <EMAIL>         teigen   \n", "1            <EMAIL>  aslakhellesoy   \n", "2            <EMAIL>  aslakhellesoy   \n", "3            <EMAIL>  aslakhellesoy   \n", "4            <EMAIL>  aslakhellesoy   \n", "..                                ...            ...   \n", "534                     <EMAIL>             oc   \n", "535                 <PERSON><PERSON><PERSON><PERSON>@.(none)            NaN   \n", "536  Ut<PERSON>ling@pc-e4801db21500.(none)            NaN   \n", "537                 <EMAIL>       ovstetun   \n", "538                 <EMAIL>       ovstetun   \n", "\n", "                  committer_name                   committer_email  \\\n", "0              <PERSON><PERSON><PERSON>                  <EMAIL>   \n", "1                  aslakhellesoy          <EMAIL>   \n", "2                  aslakhellesoy          <EMAIL>   \n", "3                  aslakhellesoy          <EMAIL>   \n", "4                  aslakhellesoy          <EMAIL>   \n", "..                           ...                               ...   \n", "534                      unknown                 <PERSON><PERSON><PERSON><PERSON>@.(none)   \n", "535                      unknown                 <PERSON><PERSON><PERSON><PERSON>@.(none)   \n", "536  U-PC-E4801DB21500\\Utvikling  Utvikling@pc-e4801db21500.(none)   \n", "537        Trond <PERSON>                 <EMAIL>   \n", "538        Trond <PERSON>                 <EMAIL>   \n", "\n", "    committer_login                  date  \\\n", "0            teigen  2010-03-28T22:26:45Z   \n", "1     aslakhellesoy  2010-03-26T18:26:16Z   \n", "2     aslakhellesoy  2010-03-26T13:14:11Z   \n", "3     aslakhellesoy  2010-03-26T11:30:20Z   \n", "4     aslakhellesoy  2010-03-24T15:27:36Z   \n", "..              ...                   ...   \n", "534             NaN  2009-05-06T16:46:47Z   \n", "535             NaN  2009-05-06T16:44:26Z   \n", "536             NaN  2009-05-06T16:25:27Z   \n", "537        ovstetun  2009-05-06T11:50:06Z   \n", "538        ovstetun  2009-05-06T11:49:08Z   \n", "\n", "                                               message  \\\n", "0               Support for 2.8.0.Beta1 (breaks 2.7.x)   \n", "1                     Clean up Javascript DSL a little   \n", "2    Moving Java annotations into one file, so that...   \n", "3                      Update with latest Gherkin i18n   \n", "4                                          Indentation   \n", "..                                                 ...   \n", "534                                 JRuby facet (IDEA)   \n", "535                                      Formatting...   \n", "536  Added test compile path to AbstractJRubyMojo (...   \n", "537  Added an example showing how to use spring aut...   \n", "538  Added support for spring autowiring into Step ...   \n", "\n", "                    repo_name  parent_count  \\\n", "0    cucumber-attic/cuke4duke             1   \n", "1    cucumber-attic/cuke4duke             1   \n", "2    cucumber-attic/cuke4duke             1   \n", "3    cucumber-attic/cuke4duke             1   \n", "4    cucumber-attic/cuke4duke             1   \n", "..                        ...           ...   \n", "534  cucumber-attic/cuke4duke             1   \n", "535  cucumber-attic/cuke4duke             1   \n", "536  cucumber-attic/cuke4duke             1   \n", "537  cucumber-attic/cuke4duke             1   \n", "538  cucumber-attic/cuke4duke             1   \n", "\n", "                                      parent_shas  \n", "0    ['243102535244bf7c208efe704ce91bf348295399']  \n", "1    ['243102535244bf7c208efe704ce91bf348295399']  \n", "2    ['dfb3ce875c186c3ea879dc16ad12693b32c1ae13']  \n", "3    ['cb223fdc881c4d6f5df8f58b7b52fba47f497233']  \n", "4    ['6916b8d991416c6526c3fe489c4cd0104ac49515']  \n", "..                                            ...  \n", "534  ['98ba871a87862f78b94a064a50f1d31f77b54746']  \n", "535  ['265029e7a54b3f4bcc239e3ea2ab7ee6b67d3529']  \n", "536  ['05d408c198335a7cdd485a9b7b98fa1dec51d04c']  \n", "537  ['fc9892a3df41c085fb985f151edae86125e8765f']  \n", "538  ['f47e0dbd50e158d5408939850293e59bfabbe292']  \n", "\n", "[539 rows x 12 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_commit_file_repo_name(repo_name):\n", "    file_path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "    repo_commit = pd.read_csv(file_path)\n", "    # Exclude merge commits with those who have two values in the columns named 'parent_shas'\n", "    repo_commit = repo_commit[repo_commit['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)\n", "    # Exclude bot accounts\n", "    # bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')\n", "    # bot_developers = bot_developers['bot_name'].tolist()\n", "    # repo_commit = repo_commit[~repo_commit['author_login'].isin(bot_developers)].reset_index(drop=True)\n", "    # repo_commit = repo_commit[~repo_commit['author_name'].isin(bot_developers)].reset_index(drop=True)\n", "    return repo_commit\n", "\n", "commits_sample = get_commit_file_repo_name(project_names[11])\n", "commits_sample"]}, {"cell_type": "markdown", "id": "cef37e74", "metadata": {}, "source": ["## Merge alias of commits with different login or username but actually the same person\n"]}, {"cell_type": "code", "execution_count": null, "id": "794c680b", "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "# from difflib import SequenceMatcher\n", "# import numpy as np\n", "\n", "# def merge_alias_commit_from_tuple(repo_name):\n", "#     \"\"\"\n", "#     合并来自同一作者但使用不同身份信息的提交记录\n", "    \n", "#     参数:\n", "#         repo_name: str, 仓库名称\n", "        \n", "#     返回:\n", "#         pandas.DataFrame: 包含合并后提交记录的数据框\n", "#     \"\"\"\n", "#     # 读取提交数据\n", "#     file_path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "#     commits_df = pd.read_csv(file_path)\n", "#     commits_df = commits_df[commits_df['parent_shas'].apply(lambda x: len(eval(x)) < 2    )].reset_index(drop=True)\n", "#     # 第一轮: 基于元组匹配合并\n", "#     # 生成作者身份元组\n", "#     commits_df['author_tuple'] = list(zip(\n", "#         commits_df['author_name'].fillna(''),\n", "#         commits_df['author_login'].fillna(''),\n", "#         commits_df['author_email'].fillna('')\n", "#     ))\n", "    \n", "#     # 创建别名字典,将相似的作者身份分组\n", "#     alias_dict = {}\n", "#     processed_tuples = set()\n", "    \n", "#     for idx, row in commits_df.iterrows():\n", "#         current_tuple = row['author_tuple']\n", "#         if current_tuple in processed_tuples:\n", "#             continue\n", "            \n", "#         name, login, email = current_tuple\n", "#         group = []\n", "        \n", "#         for idx2, row2 in commits_df.iterrows():\n", "#             other_tuple = row2['author_tuple']\n", "#             other_name, other_login, other_email = other_tuple\n", "            \n", "            \n", "#             # 如果有2个或以上字段匹配\n", "#             matches = sum([\n", "#                 name != '' and name == other_name,\n", "#                 login != '' and login == other_login, \n", "#                 email != '' and email == other_email\n", "#             ])\n", "            \n", "#             if matches >= 1:\n", "#                 group.append(other_tuple)\n", "#                 processed_tuples.add(other_tuple)\n", "                \n", "#         if group:\n", "#             alias_id = len(alias_dict)\n", "#             alias_dict[alias_id] = group\n", "    \n", "#     # 分配别名ID\n", "#     commits_df['alias'] = commits_df['author_tuple'].map(\n", "#         {tuple_: alias_id \n", "#          for alias_id, tuples in alias_dict.items() \n", "#          for tuple_ in tuples}\n", "#     )\n", "    \n", "#     # 对于没有分配到别名的记录,使用原始作者信息作为别名\n", "#     commits_df['alias'] = commits_df['alias'].fillna(\n", "#         pd.Series(range(len(commits_df.index))) + len(alias_dict)\n", "#     )\n", "    \n", "#     # 第二轮: 基于字符串相似度合并\n", "#     def compute_similarity(str1, str2):\n", "#         if pd.isna(str1) or pd.isna(str2):\n", "#             return 0\n", "#         return SequenceMatcher(None, str1, str2).ratio()\n", "    \n", "#     # 计算作者名称+邮箱的组合字符串\n", "#     commits_df['author_combined'] = commits_df['author_name'].fillna('') + \\\n", "#                                   commits_df['author_email'].fillna('')\n", "    \n", "#     # 合并相似度高的记录\n", "#     similarity_threshold = 0.9\n", "#     alias_groups = {}\n", "    \n", "#     for alias_id in commits_df['alias'].unique():\n", "#         group = commits_df[commits_df['alias'] == alias_id]\n", "        \n", "#         for idx1, row1 in group.iterrows():\n", "#             for idx2, row2 in group.iterrows():\n", "#                 if idx1 >= idx2:\n", "#                     continue\n", "                    \n", "#                 similarity = compute_similarity(\n", "#                     row1['author_combined'],\n", "#                     row2['author_combined']\n", "#                 )\n", "                \n", "#                 if similarity >= similarity_threshold:\n", "#                     min_alias = min(row1['alias'], row2['alias'])\n", "#                     max_alias = max(row1['alias'], row2['alias'])\n", "#                     if min_alias != max_alias:\n", "#                         commits_df.loc[commits_df['alias'] == max_alias, 'alias'] = min_alias\n", "    \n", "#     # 填充缺失的login\n", "#     for alias_id in commits_df['alias'].unique():\n", "#         mask = commits_df['alias'] == alias_id\n", "#         group = commits_df[mask]\n", "#         valid_logins = group['author_login'].dropna().unique()\n", "        \n", "#         if len(valid_logins) > 0:\n", "#             commits_df.loc[mask & commits_df['author_login'].isna(), 'author_login'] = valid_logins[0]\n", "          \n", "#     # 清理临时列\n", "#     commits_df = commits_df.drop(['author_tuple', 'author_combined'], axis=1)\n", "    \n", "#     return commits_df\n", "import pandas as pd\n", "from difflib import SequenceMatcher\n", "import numpy as np\n", "def merge_alias_commit_from_tuple(repo_name):\n", "    \"\"\"\n", "    合并来自同一作者但使用不同身份信息的提交记录\n", "    优先基于 login 进行分组，已归类的记录不再重复处理\n", "    \"\"\"\n", "    # 读取提交数据\n", "    file_path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "    commits_df = pd.read_csv(file_path)\n", "    commits_df = commits_df[commits_df['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)\n", "    \n", "    # 初始化alias列\n", "    commits_df['alias'] = -1\n", "    \n", "    # 第一轮：基于 login 分组\n", "    alias_counter = 0\n", "    login_groups = commits_df[commits_df['author_login'] != ''].groupby('author_login')\n", "    \n", "    # 处理有login的记录\n", "    for login, group in login_groups:\n", "        group_indices = group.index\n", "        commits_df.loc[group_indices, 'alias'] = alias_counter\n", "        alias_counter += 1\n", "    \n", "    # 第二轮：处理未分组的记录（无login或login为空）\n", "    unprocessed_mask = commits_df['alias'] == -1\n", "    unprocessed_df = commits_df[unprocessed_mask].copy()\n", "    \n", "    for idx, row in unprocessed_df.iterrows():\n", "        if commits_df.loc[idx, 'alias'] != -1:  # 如果已经被其他组处理，跳过\n", "            continue\n", "            \n", "        current_name = row['author_name']\n", "        current_email = row['author_email']\n", "        found_match = False\n", "        \n", "        # 检查是否可以匹配到现有组\n", "        for alias_id in commits_df['alias'].unique():\n", "            if alias_id == -1:\n", "                continue\n", "                \n", "            group = commits_df[commits_df['alias'] == alias_id]\n", "            group_names = set(group['author_name'].dropna())\n", "            group_emails = set(group['author_email'].dropna())\n", "            \n", "            # 检查名称或邮箱是否匹配\n", "            if ((current_name and current_name in group_names) or \n", "                (current_email and current_email in group_emails)):\n", "                commits_df.loc[idx, 'alias'] = alias_id\n", "                found_match = True\n", "                break\n", "        \n", "        # 如果没有找到匹配，创建新组\n", "        if not found_match:\n", "            commits_df.loc[idx, 'alias'] = alias_counter\n", "            alias_counter += 1\n", "    \n", "    # 第三轮：基于字符串相似度合并未匹配的组\n", "    def compute_similarity(str1, str2):\n", "        if pd.isna(str1) or pd.isna(str2):\n", "            return 0\n", "        return SequenceMatcher(None, str1, str2).ratio()\n", "    \n", "    commits_df['author_combined'] = commits_df['author_name'].fillna('') + \\\n", "                                  commits_df['author_email'].fillna('')\n", "    \n", "    similarity_threshold = 0.9\n", "    processed_aliases = set()\n", "    \n", "    for alias_id in sorted(commits_df['alias'].unique()):\n", "        if alias_id in processed_aliases:\n", "            continue\n", "            \n", "        group = commits_df[commits_df['alias'] == alias_id]\n", "        \n", "        for other_alias in sorted(commits_df['alias'].unique()):\n", "            if other_alias <= alias_id or other_alias in processed_aliases:\n", "                continue\n", "                \n", "            other_group = commits_df[commits_df['alias'] == other_alias]\n", "            \n", "            # 计算组间的相似度\n", "            for _, row1 in group.iterrows():\n", "                for _, row2 in other_group.iterrows():\n", "                    similarity = compute_similarity(\n", "                        row1['author_combined'],\n", "                        row2['author_combined']\n", "                    )\n", "                    \n", "                    if similarity >= similarity_threshold:\n", "                        # 将较大alias的组合并到较小alias的组\n", "                        commits_df.loc[commits_df['alias'] == other_alias, 'alias'] = alias_id\n", "                        processed_aliases.add(other_alias)\n", "                        break\n", "                if other_alias in processed_aliases:\n", "                    break\n", "    \n", "    # 填充缺失的login\n", "    for alias_id in commits_df['alias'].unique():\n", "        mask = commits_df['alias'] == alias_id\n", "        group = commits_df[mask]\n", "        # fillna with the first valid login\n", "        valid_logins = group['author_login'].dropna().unique()\n", "        if len(valid_logins) > 0:\n", "            commits_df.loc[mask & commits_df['author_login'].isna(), 'author_login'] = valid_logins[0]\n", "    \n", "    # 清理临时列\n", "    commits_df = commits_df.drop('author_combined', axis=1)\n", "    \n", "    return commits_df"]}, {"cell_type": "code", "execution_count": 7, "id": "e7bd8f43", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from difflib import SequenceMatcher\n", "import numpy as np\n", "\n", "def merge_alias_commit_from_tuple(commits_df):\n", "    \"\"\"\n", "    高性能加速版本：合并来自同一作者但使用不同身份信息的提交记录\n", "    优先基于 login 分组，已归类记录不再重复处理\n", "    \"\"\"\n", "    # 初始化 alias 列\n", "    commits_df['alias'] = -1\n", "\n", "    # 第一轮：基于非空 author_login 进行分组\n", "    alias_counter = 0\n", "    mask = commits_df['author_login'] != ''\n", "    for _, group in commits_df[mask].groupby('author_login'):\n", "        commits_df.loc[group.index, 'alias'] = alias_counter\n", "        alias_counter += 1\n", "\n", "    # 第二轮：对 alias 仍为 -1 的记录，利用 author_name 和 author_email 进行匹配\n", "    # 建立从已分组记录到 alias 的映射（取首次出现的值）\n", "    processed = commits_df[commits_df['alias'] != -1]\n", "    name_to_alias = processed.groupby('author_name')['alias'].first().to_dict()\n", "    email_to_alias = processed.groupby('author_email')['alias'].first().to_dict()\n", "\n", "    # 对未分组记录（alias == -1），尝试通过映射来分配 alias\n", "    unprocessed_mask = commits_df['alias'] == -1\n", "    temp_alias = commits_df.loc[unprocessed_mask].apply(\n", "        lambda row: name_to_alias.get(row['author_name'],\n", "                                      email_to_alias.get(row['author_email'], np.nan)),\n", "        axis=1\n", "    )\n", "    assigned = temp_alias.notna()\n", "    commits_df.loc[unprocessed_mask & assigned, 'alias'] = temp_alias[assigned]\n", "\n", "    # 对仍未匹配到的记录，分配新 alias（连续递增即可）\n", "    remaining = commits_df['alias'] == -1\n", "    num_remaining = remaining.sum()\n", "    commits_df.loc[remaining, 'alias'] = np.arange(alias_counter, alias_counter + num_remaining)\n", "\n", "    # 填充缺失的 author_login：对每个 alias，用首个非空 login 填充组中的 NaN\n", "    commits_df = commits_df.groupby('alias', group_keys=False).apply(\n", "        lambda group: group.assign(author_login=group['author_login'].fillna(\n", "            group['author_login'].dropna().iloc[0] if not group['author_login'].dropna().empty else np.nan))\n", "    )\n", "\n", "    return commits_df\n"]}, {"cell_type": "code", "execution_count": 8, "id": "939b1165", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_460218/2055376691.py:43: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  lambda group: group.assign(author_login=group['author_login'].fillna(\n", "/tmp/ipykernel_460218/2055376691.py:42: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  commits_df = commits_df.groupby('alias', group_keys=False).apply(\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "sha", "rawType": "object", "type": "string"}, {"name": "author_name", "rawType": "object", "type": "string"}, {"name": "author_email", "rawType": "object", "type": "string"}, {"name": "author_login", "rawType": "object", "type": "unknown"}, {"name": "committer_name", "rawType": "object", "type": "string"}, {"name": "committer_email", "rawType": "object", "type": "string"}, {"name": "committer_login", "rawType": "object", "type": "unknown"}, {"name": "date", "rawType": "object", "type": "string"}, {"name": "message", "rawType": "object", "type": "string"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "parent_count", "rawType": "int64", "type": "integer"}, {"name": "parent_shas", "rawType": "object", "type": "string"}, {"name": "alias", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "c1c3cf82-7b0e-42ef-a66e-aca59116b651", "rows": [["0", "64b091146b2557c288c802823adcb3485eba581d", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-05-15T02:20:41Z", "Update CHANGELOG to reflect 2.4.8 release", "yui/yuicompressor", "1", "['4018929bb868ad68443a20fbd71c2ba75d8b8ad8']", "33"], ["1", "ff0bde022c4527b9a1a695ce30c7383e1733a971", "<PERSON>", "<EMAIL>", "danbeam", "<PERSON>", "<EMAIL>", "danbeam", "2012-01-11T11:18:34Z", "And porting 0 length unary terms optimization to JS port", "yui/yuicompressor", "1", "['a5377b6ca2fc0e817c547b0812645f4c13034476']", "11"], ["2", "6d5892c6bd765646583681e03b826d947fe9a11b", "<PERSON>", "<EMAIL>", null, "<PERSON>", "<EMAIL>", null, "2013-05-03T16:25:08Z", "Fixes issue 73 - RGB colors above 255 would generate invalid css.", "yui/yuicompressor", "1", "['65c8e40c41c5204d23e73be447ae67d970b6ea33']", "37"], ["3", "a5377b6ca2fc0e817c547b0812645f4c13034476", "<PERSON>", "<EMAIL>", "danbeam", "<PERSON>", "<EMAIL>", "danbeam", "2012-01-11T08:58:27Z", "Adding case for zero length unary term starting on new line as well", "yui/yuicompressor", "1", "['dbc0ae4c9893b8f7c0e28409b79e5a84ac67d29f']", "11"], ["4", "dbc0ae4c9893b8f7c0e28409b79e5a84ac67d29f", "<PERSON>", "<EMAIL>", "danbeam", "<PERSON>", "<EMAIL>", "danbeam", "2012-01-07T05:30:08Z", "Adding minfication for more unary 0 length terms, like:\n\n- angles (deg, rad, grad)\n- times (s, ms)\n- frequencies (hz, khz)\n- floating point 0 unary lengths (.0, 0.0)\n\nwith tests and small regex performance optimizations (doesn't store matches).\n\nAlso, small whitespace change in CssCompressor.java for those without tabstop=4\nand bumping copyright.", "yui/yuicompressor", "1", "['6e2bc23f1056ea07fc78846b6938b7ce85abb276']", "11"], ["5", "556ba2507e51549f510588f14e55522bec8663e2", "<PERSON>", "<EMAIL>", "sbertrang", "<PERSON>", "<EMAIL>", "sbertrang", "2011-10-04T15:47:52Z", "fix bug (found while testing the perl port)", "yui/yuicompressor", "1", "['6e2bc23f1056ea07fc78846b6938b7ce85abb276']", "28"], ["6", "5a988bfa5ae6692e02df4bf01bde799d5097d398", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-28T07:01:46Z", "Fix option parsing when piping data in via stdin", "yui/yuicompressor", "1", "['3930b3230bd9c1a5d294c0ccfc87a38c77cc8994']", "33"], ["7", "9d65596f5709fa1556d41dcb66047a7b1b174bde", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-28T23:23:12Z", "Removed dates from CHANGELOG. Just adds an uneccesary step to release packaging", "yui/yuicompressor", "1", "['223fbcc4e3b1907ff3bdd35e2a486572c2bb8c63']", "29"], ["8", "223fbcc4e3b1907ff3bdd35e2a486572c2bb8c63", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-27T22:17:22Z", "2010 - 2011 in license", "yui/yuicompressor", "1", "['ed40b1e158deeb87aecf303bbae04d52353c70af']", "29"], ["9", "ed40b1e158deeb87aecf303bbae04d52353c70af", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-27T16:50:38Z", "Added test with #AARRGGBB in filters", "yui/yuicompressor", "1", "['91c5ea5ba37d8f969c3939e3b33a1296c561b872']", "29"], ["10", "0116b431055a6597836264b546139761dc911bcc", "<PERSON>", "<EMAIL>", null, "<PERSON>", "<EMAIL>", null, "2013-04-22T23:47:06Z", "Removing empty rules with a media query like \"(-o-max-device-pixel-ratio:  5/4)\" would stop at the \"/\".\nForward slash is replace with a token before the empty rule check. Replaced after empty rule check.", "yui/yuicompressor", "1", "['0617be69657527c6f54ec04e39fda3a111a23acf']", "38"], ["11", "3b1f37228d6edede24df7279e44964ae6195f041", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-27T07:13:11Z", "Build last 2.4.7 merge", "yui/yuicompressor", "1", "['d908eec9fb9c7865e5fe70e38a7de7e27c0fc521']", "29"], ["12", "94c028a8c33a6c859d9ddd584a74ed40014395bf", "<PERSON>", "<EMAIL>", null, "<PERSON>", "<EMAIL>", null, "2013-04-22T23:47:06Z", "Reming empty rules with a media query like \"(-o-max-device-pixel-ratio:  5/4)\" would stop at the \"/\".\nForward slash is replace with a token before the empty rule check. Replaced after empty rule check.", "yui/yuicompressor", "1", "['0617be69657527c6f54ec04e39fda3a111a23acf']", "39"], ["13", "1ee0cc4b1e211dc3c43ae3254417b511544f8cde", "<PERSON><PERSON>", "<EMAIL>", "davglass", "<PERSON><PERSON>", "<EMAIL>", "davglass", "2013-04-22T21:35:09Z", "Removed emails from the travis file", "yui/yuicompressor", "1", "['0617be69657527c6f54ec04e39fda3a111a23acf']", "13"], ["14", "91c5ea5ba37d8f969c3939e3b33a1296c561b872", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-27T06:57:51Z", "Fixed border-color:#ABCDEF #AABBCC #ABCDEF #AABBCC use case, which was broken after the more rebost ID fix was added", "yui/yuicompressor", "1", "['cb68d7761c5a4ec03619418e06206b16a1189f89']", "29"], ["15", "2880182673a349e656d8f358637384f51e17b307", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-03-17T23:26:08Z", "Accept PR #10 without the additional whitespace", "yui/yuicompressor", "1", "['f1666bbe3264a1f69c10d0fe9853cdd361c56b80']", "33"], ["16", "f1666bbe3264a1f69c10d0fe9853cdd361c56b80", "<PERSON>", "<EMAIL>", null, "<PERSON>", "<EMAIL>", "tml", "2011-06-22T21:04:13Z", "output pattern support for single files [FIX #2528091]", "yui/yuicompressor", "1", "['0617be69657527c6f54ec04e39fda3a111a23acf']", "40"], ["17", "cb68d7761c5a4ec03619418e06206b16a1189f89", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-23T20:03:12Z", "Removed redundant  A-F check in regex. We're already using the i flag. Reminded me to add case variance to the test file", "yui/yuicompressor", "1", "['f023e8cf1aaf16574ac4a91926a24facddc97fee']", "29"], ["18", "f023e8cf1aaf16574ac4a91926a24facddc97fee", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-23T17:36:53Z", "Might as well have the debugger page try and support IE", "yui/yuicompressor", "1", "['8e4a8cc2fd7e4f090a5729c3908264a813fba45b']", "29"], ["19", "52a90e1942b205488cc8d74395dac62511f71814", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-18T04:05:12Z", "Test no longer fails", "yui/yuicompressor", "1", "['4ec3a47d19dea6bfcd0eeff8acd0b0e99b652253']", "33"], ["20", "8e4a8cc2fd7e4f090a5729c3908264a813fba45b", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-23T17:07:32Z", "use textNode instead of innerHTML", "yui/yuicompressor", "1", "['d1d0402a4cf16367ba22d44c810028195482da9d']", "29"], ["21", "bb3d8783086500397f3ea9e56adb83939d1f1b8e", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-18T04:02:42Z", "Remove JAR file from build", "yui/yuicompressor", "1", "['293291a11b9cb3c5c7da1a1e6f2f2e4a06b20b02']", "33"], ["22", "b72f46a1247fac53c9e576783c6555e945dc28ca", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-23T07:28:03Z", "Jar build for ID selector fix", "yui/yuicompressor", "1", "['45382cb2cedd1a556e7b01a7e38e530e2c6a6d5c']", "29"], ["23", "d1d0402a4cf16367ba22d44c810028195482da9d", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-23T07:16:29Z", "Jar Build for revamped ID selector fix", "yui/yuicompressor", "1", "['87b4ee83155c1c6201b70aaf2671807a82d32f33']", "29"], ["24", "87b4ee83155c1c6201b70aaf2671807a82d32f33", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-23T07:11:38Z", "Last fix for #2528102 was not robust enough (didn't fix the test cases\nadded. e.g. .foo, #AABBCC {...}).\n\nThis commit attempts to fix the ID selector breakage by only compressing\nhex colors inside {...}.\n\nAdded test cases which were still breaking in the original fix, and some\nmore variants for hex color compression and IDs together in the same file.\n\nAlso added a tools/cssmin-debugger.html test page, which can be used to\nstep through the cssmin JS port in a (HTML 5 File API compatible) browser.", "yui/yuicompressor", "1", "['71b9d2cf23b885b9a078c32d6788cbcee842e3fb']", "29"], ["25", "0b3643886453dcd8b3b1b402bf5e33a6f86cf2dc", "<PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<EMAIL>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "2011-09-21T15:42:21Z", "Added filename output before warnings and errors", "yui/yuicompressor", "1", "['3f137761edc89b373b5f8d748ba2440a42aa62e5']", "12"], ["26", "45844f57460cc2d7a820880f09a33169c7009fe3", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-15T05:26:20Z", "Mark j<PERSON>y test as an expected failure", "yui/yuicompressor", "1", "['64694d6aff241a21ea673c9a3df16a9e02ef74da']", "33"], ["27", "3aa2bc209d816ab85e3e76f739e3789ad603e599", "<PERSON>", "<EMAIL>", "apm", "<PERSON>", "<EMAIL>", "apm", "2011-09-20T21:34:05Z", "Added a jquery test, which fails in 2.4.8pre in the trunk, but passes in\n2.4.6.", "yui/yuicompressor", "1", "['3f137761edc89b373b5f8d748ba2440a42aa62e5']", "4"], ["28", "71b9d2cf23b885b9a078c32d6788cbcee842e3fb", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-08T21:31:34Z", "Rebuilt with cherry-picked ID selector commit (a14ebcd1a)", "yui/yuicompressor", "1", "['a14ebcd1a45583b6541dffae76283c73130b7eb4']", "29"], ["29", "293291a11b9cb3c5c7da1a1e6f2f2e4a06b20b02", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-15T04:46:24Z", "Preserve ! in \"important comments\" across compression runs", "yui/yuicompressor", "1", "['9b2c0134a2a43182ba5b645db5dfd39032f6dac2']", "33"], ["30", "a14ebcd1a45583b6541dffae76283c73130b7eb4", "<PERSON>", "<EMAIL>", "reid", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-05-26T19:19:29Z", "Don't transform CSS ID selectors. Fix #2528102.", "yui/yuicompressor", "1", "['e1fa3c0790b7e28815bbc0133b2ef79908e3e1f4']", "27"], ["31", "e1fa3c0790b7e28815bbc0133b2ef79908e3e1f4", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-07T19:29:51Z", "Added new meta-data requirements for yicf files", "yui/yuicompressor", "1", "['41338722797d5aaac48c875438ecde07923e5a4b']", "29"], ["32", "41338722797d5aaac48c875438ecde07923e5a4b", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-06T21:05:42Z", "Fixed doc typo", "yui/yuicompressor", "1", "['776ace98b6c167aee6611d546adcba3272ea6931']", "29"], ["33", "5115644bbaa3aa30237329eacef4ba9a27b28383", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-13T23:49:01Z", "Remove JAR from repo, add it to .gitignore", "yui/yuicompressor", "1", "['64694d6aff241a21ea673c9a3df16a9e02ef74da']", "33"], ["34", "776ace98b6c167aee6611d546adcba3272ea6931", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-06T21:00:18Z", "Updated docs with dataurl changes, and general note on the fact that CssCompressor requires Java >=1.5", "yui/yuicompressor", "1", "['e21258bdfda3145b969266f13c2f92e745ddc03e']", "29"], ["35", "75bb9d14926f8d45a029ca376988ca0001631644", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-06T20:38:48Z", "Bump up to 2.4.8pre", "yui/yuicompressor", "1", "['cf0c42729a511b06c8c270bd7d6557d798bc7295']", "29"], ["36", "e21258bdfda3145b969266f13c2f92e745ddc03e", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T23:47:09Z", "Removed 2.4.6 jar", "yui/yuicompressor", "1", "['0078a1f410c70886ff3b1b84e190d07f9974e543']", "29"], ["37", "0078a1f410c70886ff3b1b84e190d07f9974e543", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T20:24:53Z", "Adding 2.4.7 jar", "yui/yuicompressor", "1", "['1c5bb45c44b39053b8bfb9bb4d6cc94dc0e5304b']", "29"], ["38", "1c5bb45c44b39053b8bfb9bb4d6cc94dc0e5304b", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T20:24:18Z", "Adding 2.4.7 jar", "yui/yuicompressor", "1", "['93117d568af2e073c96b3e553f43ad15336bab36']", "29"], ["39", "93117d568af2e073c96b3e553f43ad15336bab36", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T18:37:08Z", "Cleaned up inadvertant global and other minor lint issues", "yui/yuicompressor", "1", "['2fd0f4a2922c3a0bc629bd168868fafbc389a3d4']", "29"], ["40", "86195c9d194a1fcead16c1d18ef5993363603788", "bandesz2", "<EMAIL>", "bandesz", "bandesz2", "<EMAIL>", "bandesz", "2011-09-02T12:20:53Z", "Parameter parsing fix, when compressing multiple files, nomunge, preserve-semi and disable-optimizations options will be valid only for the first file", "yui/yuicompressor", "1", "['4c54e62890bc61d72122be044fd8c1e6fa83912b']", "6"], ["41", "2fd0f4a2922c3a0bc629bd168868fafbc389a3d4", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T02:34:05Z", "Real world test css with data url from a yui app", "yui/yuicompressor", "1", "['ea7aa2b94e64ddf4765bbbb571fabc1b046337ac']", "29"], ["42", "1925918d32b34192014c9dfe8104a0078a1804bf", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-04-05T14:24:56Z", "Add tests for this PR", "yui/yuicompressor", "1", "['e50368598026efc3b54bc958c1f506f5e9db8347']", "33"], ["43", "ea7aa2b94e64ddf4765bbbb571fabc1b046337ac", "<PERSON>", "<EMAIL>", "apm", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-04-25T19:30:43Z", "remove build step", "yui/yuicompressor", "1", "['b715e66b66dc6f666f19139103e51f69e6875e53']", "4"], ["44", "b715e66b66dc6f666f19139103e51f69e6875e53", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T02:24:27Z", "Updated version number to 2.4.7", "yui/yuicompressor", "1", "['056780010b23f4df1065500cbdb3225f1f8cdf72']", "29"], ["45", "056780010b23f4df1065500cbdb3225f1f8cdf72", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T02:16:54Z", "Some more dataurl tests", "yui/yuicompressor", "1", "['333c2883089d16057d8b6c7bb3c0334c81cbc955']", "29"], ["46", "333c2883089d16057d8b6c7bb3c0334c81cbc955", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-02T01:38:52Z", "Updated jsport, small changes in java version to match", "yui/yuicompressor", "1", "['b006b35a6eb47dd53e6d5a9af408aab93c13fa2f']", "29"], ["47", "d19c48300647cf8d0a72b38fdd6d9a595b5e0cf2", "<PERSON>", "<EMAIL>", "tml", "<PERSON>", "<EMAIL>", "tml", "2013-03-18T00:13:52Z", "Adding myself to Travis notifications", "yui/yuicompressor", "1", "['65ae061039fc1bb9c064e92113ef46ae0d8473e3']", "33"], ["48", "b006b35a6eb47dd53e6d5a9af408aab93c13fa2f", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "<PERSON><PERSON><PERSON>", "<EMAIL>", "sdesai", "2011-09-01T06:20:05Z", "More tests", "yui/yuicompressor", "1", "['a07d18b70ea053914fb9cb78fbf587d635a323b2']", "29"], ["49", "65ae061039fc1bb9c064e92113ef46ae0d8473e3", "<PERSON><PERSON><PERSON>", "fyz<PERSON>@gmail.com", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<EMAIL>", "tml", "2013-03-14T11:18:41Z", "Forgot to include Maroon", "yui/yuicompressor", "1", "['bc8cbae60e810d4cb9e55ce700e62895ae5f9b12']", "15"]], "shape": {"columns": 13, "rows": 423}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sha</th>\n", "      <th>author_name</th>\n", "      <th>author_email</th>\n", "      <th>author_login</th>\n", "      <th>committer_name</th>\n", "      <th>committer_email</th>\n", "      <th>committer_login</th>\n", "      <th>date</th>\n", "      <th>message</th>\n", "      <th>repo_name</th>\n", "      <th>parent_count</th>\n", "      <th>parent_shas</th>\n", "      <th>alias</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>64b091146b2557c288c802823adcb3485eba581d</td>\n", "      <td><PERSON></td>\n", "      <td>joe<PERSON><PERSON>@gmail.com</td>\n", "      <td>tml</td>\n", "      <td><PERSON></td>\n", "      <td>joe<PERSON><PERSON>@gmail.com</td>\n", "      <td>tml</td>\n", "      <td>2013-05-15T02:20:41Z</td>\n", "      <td>Update CHANGELOG to reflect 2.4.8 release</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['4018929bb868ad68443a20fbd71c2ba75d8b8ad8']</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ff0bde022c4527b9a1a695ce30c7383e1733a971</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>danbeam</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>danbeam</td>\n", "      <td>2012-01-11T11:18:34Z</td>\n", "      <td>And porting 0 length unary terms optimization ...</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['a5377b6ca2fc0e817c547b0812645f4c13034476']</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6d5892c6bd765646583681e03b826d947fe9a11b</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td>2013-05-03T16:25:08Z</td>\n", "      <td>Fixes issue 73 - RGB colors above 255 would ge...</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['65c8e40c41c5204d23e73be447ae67d970b6ea33']</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>a5377b6ca2fc0e817c547b0812645f4c13034476</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>danbeam</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>danbeam</td>\n", "      <td>2012-01-11T08:58:27Z</td>\n", "      <td>Adding case for zero length unary term startin...</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['dbc0ae4c9893b8f7c0e28409b79e5a84ac67d29f']</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>dbc0ae4c9893b8f7c0e28409b79e5a84ac67d29f</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>danbeam</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>danbeam</td>\n", "      <td>2012-01-07T05:30:08Z</td>\n", "      <td>Adding minfication for more unary 0 length ter...</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['6e2bc23f1056ea07fc78846b6938b7ce85abb276']</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>418</th>\n", "      <td>daef888d7c3a263a35a3d80779caab5d7a4857b9</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>jlecomte</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td>2007-08-10T15:47:18Z</td>\n", "      <td>Misc improvements to the build process</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['28bd0040a50369d5f713b9cf3e4a0bbdf5397d47']</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>419</th>\n", "      <td>28bd0040a50369d5f713b9cf3e4a0bbdf5397d47</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>jlecomte</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td>2007-08-09T23:49:57Z</td>\n", "      <td>Added build target build.package which generat...</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['07f968990aee91f9e5e8db3b40bbee60b828bc2a']</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>420</th>\n", "      <td>07f968990aee91f9e5e8db3b40bbee60b828bc2a</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>jlecomte</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td>2007-08-09T23:23:48Z</td>\n", "      <td>Fixed LONG_DESC</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['35f126d68288f89589b8ad70dcdd670928aa6cc5']</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421</th>\n", "      <td>35f126d68288f89589b8ad70dcdd670928aa6cc5</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>jlecomte</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td>2007-08-09T23:14:38Z</td>\n", "      <td>YUI Compressor - yinst package</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>1</td>\n", "      <td>['4acfc31323f73c252c3996d5a271449771eb4efe']</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>422</th>\n", "      <td>4acfc31323f73c252c3996d5a271449771eb4efe</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>jlecomte</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>NaN</td>\n", "      <td>2007-08-09T23:06:24Z</td>\n", "      <td>YUI Compressor</td>\n", "      <td>yui/yuicompressor</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>19</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>423 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                          sha     author_name  \\\n", "0    64b091146b2557c288c802823adcb3485eba581d      <PERSON>   \n", "1    ff0bde022c4527b9a1a695ce30c7383e1733a971        <PERSON>   \n", "2    6d5892c6bd765646583681e03b826d947fe9a11b     <PERSON>   \n", "3    a5377b6ca2fc0e817c547b0812645f4c13034476        Dan <PERSON>   \n", "4    dbc0ae4c9893b8f7c0e28409b79e5a84ac67d29f        <PERSON>   \n", "..                                        ...             ...   \n", "418  daef888d7c3a263a35a3d80779caab5d7a4857b9  <PERSON>   \n", "419  28bd0040a50369d5f713b9cf3e4a0bbdf5397d47  <PERSON>   \n", "420  07f968990aee91f9e5e8db3b40bbee60b828bc2a  <PERSON>   \n", "421  35f126d68288f89589b8ad70dcdd670928aa6cc5  <PERSON>   \n", "422  4acfc31323f73c252c3996d5a271449771eb4ef<PERSON>  <PERSON>   \n", "\n", "               author_email author_login  committer_name  \\\n", "0       <EMAIL>          tml      <PERSON>   \n", "1           <EMAIL>      danbeam        Dan Beam   \n", "2    <EMAIL>          NaN     <PERSON>   \n", "3           <EMAIL>      danbeam        Dan Beam   \n", "4           <EMAIL>      danbeam        Dan Beam   \n", "..                      ...          ...             ...   \n", "418  <EMAIL>     jlecomte  Julien <PERSON>   \n", "419  <EMAIL>     jlecomte  Julien <PERSON>   \n", "420  <EMAIL>     jlecomte  <PERSON>   \n", "421  <EMAIL>     jlecomte  Julien <PERSON>   \n", "422  <EMAIL>     jlecomte  Julien <PERSON>   \n", "\n", "            committer_email committer_login                  date  \\\n", "0       <EMAIL>             tml  2013-05-15T02:20:41Z   \n", "1           <EMAIL>         danbeam  2012-01-11T11:18:34Z   \n", "2    <EMAIL>             NaN  2013-05-03T16:25:08Z   \n", "3           <EMAIL>         danbeam  2012-01-11T08:58:27Z   \n", "4           <EMAIL>         danbeam  2012-01-07T05:30:08Z   \n", "..                      ...             ...                   ...   \n", "418  <EMAIL>             NaN  2007-08-10T15:47:18Z   \n", "419  <EMAIL>             NaN  2007-08-09T23:49:57Z   \n", "420  <EMAIL>             NaN  2007-08-09T23:23:48Z   \n", "421  <EMAIL>             NaN  2007-08-09T23:14:38Z   \n", "422  <EMAIL>             NaN  2007-08-09T23:06:24Z   \n", "\n", "                                               message          repo_name  \\\n", "0            Update CHANGELOG to reflect 2.4.8 release  yui/yuicompressor   \n", "1    And porting 0 length unary terms optimization ...  yui/yuicompressor   \n", "2    Fixes issue 73 - RGB colors above 255 would ge...  yui/yuicompressor   \n", "3    Adding case for zero length unary term startin...  yui/yuicompressor   \n", "4    Adding minfication for more unary 0 length ter...  yui/yuicompressor   \n", "..                                                 ...                ...   \n", "418             Misc improvements to the build process  yui/yuicompressor   \n", "419  Added build target build.package which generat...  yui/yuicompressor   \n", "420                                    Fixed LONG_DESC  yui/yuicompressor   \n", "421                     YUI Compressor - yinst package  yui/yuicompressor   \n", "422                                     YUI Compressor  yui/yuicompressor   \n", "\n", "     parent_count                                   parent_shas  alias  \n", "0               1  ['4018929bb868ad68443a20fbd71c2ba75d8b8ad8']     33  \n", "1               1  ['a5377b6ca2fc0e817c547b0812645f4c13034476']     11  \n", "2               1  ['65c8e40c41c5204d23e73be447ae67d970b6ea33']     37  \n", "3               1  ['dbc0ae4c9893b8f7c0e28409b79e5a84ac67d29f']     11  \n", "4               1  ['6e2bc23f1056ea07fc78846b6938b7ce85abb276']     11  \n", "..            ...                                           ...    ...  \n", "418             1  ['28bd0040a50369d5f713b9cf3e4a0bbdf5397d47']     19  \n", "419             1  ['07f968990aee91f9e5e8db3b40bbee60b828bc2a']     19  \n", "420             1  ['35f126d68288f89589b8ad70dcdd670928aa6cc5']     19  \n", "421             1  ['4acfc31323f73c252c3996d5a271449771eb4efe']     19  \n", "422             0                                            []     19  \n", "\n", "[423 rows x 13 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# test\n", "commits_sample = merge_alias_commit_from_tuple(get_commit_file_repo_name('yui/yuicompressor'))\n", "commits_sample"]}, {"cell_type": "markdown", "id": "cda6d1b48488bcd3", "metadata": {}, "source": ["## Identify Core Developers (count based)"]}, {"cell_type": "code", "execution_count": 9, "id": "c1c6e22f898814fb", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T11:00:05.490778Z", "start_time": "2024-11-25T11:00:05.413223Z"}}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def identify_core_developer_commit(commits):\n", "    \"\"\"\n", "    Identify the core developers based on commit counts from individual commits,\n", "    then filter out developers with fewer than 10 commits.\n", "\n", "    Args:\n", "        commits (pd.DataFrame): A DataFrame where each row represents a commit,\n", "                                with at least one column: 'author_login'.\n", "\n", "    Returns:\n", "        list: A list of core developers who collectively made over 80% of the total commits\n", "              and have at least 10 commits.\n", "    \"\"\"\n", "    # fill commits with empty author_login with author_name\n", "    commits['author_login'] = commits['author_login'].fillna(commits['author_name'])\n", "    # Count the number of commits per author\n", "    commit_counts = commits[\"author_login\"].value_counts().reset_index()\n", "    commit_counts.columns = [\"developer\", \"commits\"]\n", "    # Sort by commits in descending order\n", "    commit_counts = commit_counts.sort_values(by=\"commits\", ascending=False).reset_index(drop=True)\n", "\n", "    # Calculate total commits and 80% threshold\n", "    total_commits = commit_counts[\"commits\"].sum()\n", "    threshold = total_commits * 0.8\n", "\n", "    # Identify core developers\n", "    core_developers = []\n", "    cumulative_commits = 0\n", "\n", "    for _, row in commit_counts.iterrows():\n", "        core_developers.append(row[\"developer\"])\n", "        cumulative_commits += row[\"commits\"]\n", "\n", "        if cumulative_commits >= threshold:\n", "            break\n", "\n", "    # Filter out developers with fewer than 10 commits from the core developers\n", "    core_developers_df = commit_counts[commit_counts[\"developer\"].isin(core_developers)]\n", "    core_developers_filtered = core_developers_df[core_developers_df[\"commits\"] >= 10]\n", "\n", "    return core_developers_filtered[\"developer\"].tolist()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4d2a3c3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'yui/yuicompressor'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_sample = project_names[4]\n", "repo_sample"]}, {"cell_type": "code", "execution_count": null, "id": "0b79c7a0", "metadata": {}, "outputs": [{"data": {"text/plain": ["['<PERSON>',\n", " 'tml',\n", " 's<PERSON><PERSON>',\n", " 'isaacs',\n", " 'davglass',\n", " 'stoyan',\n", " 'danbeam',\n", " '<PERSON>',\n", " '<PERSON><PERSON><PERSON>']"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_sample = project_names[4]\n", "repo_commit = get_commit_file_repo_name(repo_sample)\n", "optimized_commits_df = generate_login_from_name_and_email(optimize_and_unify_logins(repo_commit))\n", "core_developers = identify_core_developer_commit(repo_commit)\n", "core_developers"]}, {"cell_type": "code", "execution_count": 10, "id": "a00c12ef6d6ed418", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:08:56.924553Z", "start_time": "2024-11-25T07:08:56.911206Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_460218/2055376691.py:43: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  lambda group: group.assign(author_login=group['author_login'].fillna(\n", "/tmp/ipykernel_460218/2055376691.py:42: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  commits_df = commits_df.groupby('alias', group_keys=False).apply(\n"]}, {"data": {"text/plain": ["['jlecomte', 'tml', 'sdesai', 'stoyan', 'isaacs', 'davglass', 'danbeam']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_sample = project_names[4]\n", "repo_commit = get_commit_file_repo_name(repo_sample)\n", "repo_commit = merge_alias_commit_from_tuple(repo_commit)\n", "core_developers = identify_core_developer_commit(repo_commit)\n", "core_developers"]}, {"cell_type": "code", "execution_count": 11, "id": "35471d8bcaeeeaa3", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:27:01.174278Z", "start_time": "2024-11-25T07:27:01.159670Z"}}, "outputs": [], "source": ["import os\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "from tqdm import tqdm\n", "\n", "def process_repo(repo_name):\n", "    file_path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "    if not os.path.exists(file_path):\n", "        return None\n", "    repo_commit = get_commit_file_repo_name(repo_name)\n", "    # repo_commit = merge_alias_commit_from_tuple(repo_commit)\n", "    core_developers = identify_core_developer_commit(repo_commit)\n", "    return {\n", "        'repo_name': repo_name,\n", "        'core_developers': core_developers,\n", "        'num_core_developers': len(core_developers),\n", "        'num_total_developers': len(repo_commit['author_login'].unique())\n", "    }\n", "\n", "def get_all_core_developers(project_names):\n", "    core_developers_list = []\n", "    with ThreadPoolExecutor(max_workers=40) as executor:\n", "        futures = {executor.submit(process_repo, repo): repo for repo in project_names}\n", "        for future in tqdm(as_completed(futures), total=len(futures), desc=\"Processing repos\"):\n", "            result = future.result()\n", "            if result is not None:\n", "                core_developers_list.append(result)\n", "    return core_developers_list"]}, {"cell_type": "code", "execution_count": 12, "id": "6e19659753073157", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T11:02:30.007043Z", "start_time": "2024-11-25T11:00:10.827593Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing repos:  14%|█▍        | 3989/28235 [01:14<05:31, 73.22it/s] /tmp/ipykernel_460218/1140640678.py:3: DtypeWarning: Columns (3,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  35%|███▌      | 9975/28235 [02:51<05:42, 53.26it/s]/tmp/ipykernel_460218/1140640678.py:3: DtypeWarning: Columns (2,3,5,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  36%|███▌      | 10169/28235 [02:55<04:04, 73.80it/s]/tmp/ipykernel_460218/1140640678.py:3: DtypeWarning: Columns (3,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  38%|███▊      | 10817/28235 [03:05<04:38, 62.51it/s] /tmp/ipykernel_460218/1140640678.py:3: DtypeWarning: Columns (2,3,5,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos: 100%|██████████| 28235/28235 [06:56<00:00, 67.76it/s]  \n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "core_developers", "rawType": "object", "type": "unknown"}, {"name": "num_core_developers", "rawType": "int64", "type": "integer"}, {"name": "num_total_developers", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "f5f866b4-a7e6-46e7-a99e-1c695289bfda", "rows": [["0", "simpligility/android-maven-plugin", "['mosabua', 'hug<PERSON><PERSON><PERSON><PERSON>', 'william-ferguson-au', 'pa314159', '<PERSON><PERSON><PERSON>', 'step<PERSON><PERSON><PERSON><PERSON>', 'johan', '<PERSON><PERSON>', 'c<PERSON><PERSON><PERSON><PERSON>', 'o<PERSON><PERSON>', 'sisbell', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', 'm<PERSON><PERSON><PERSON><PERSON>', 'mtt<PERSON><PERSON>']", "16", "174"], ["1", "twitter/elephant-bird", "['<PERSON><PERSON><PERSON>', 'sagemintblue', 'dvryaboy', 'rubanm', 'isnotin<PERSON>in', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'aniket486']", "10", "73"], ["2", "eleybourn/book-catalogue", "['<PERSON><PERSON><PERSON><PERSON>', 'eleybourn']", "2", "29"], ["3", "jdbi/jdbi", "['hgschmie', 'brian<PERSON>', 's<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'qualidafial']", "7", "176"], ["4", "basho/riak-java-client", "['alex<PERSON><PERSON>', '<PERSON>', 'russelldb', 'invalid-email-address', 'mgodave', 'srgg']", "6", "70"], ["5", "sitemesh/sitemesh2", "['mboga<PERSON>', 'joe<PERSON><PERSON>', 'farkas', 'codeconsole', 'hani']", "5", "30"], ["6", "liato/android-bankdroid", "['liato', 'goober', 'fredrike']", "3", "50"], ["7", "dropwizard/metrics", "['codahale', 'arteam', 'joschi', 'ryantenney', 'dependabot-preview[bot]', 'chids', 'waywardmonkeys', 'dpursehouse']", "8", "262"], ["8", "bpellin/keepassdroid", "['bpellin']", "1", "54"], ["9", "sirthias/pegdown", "['sirthias', 'vsch', 'unknown']", "3", "28"], ["10", "tcurdt/jdeb", "['tcurdt', 'e<PERSON>', 'tmortagne', '<PERSON><PERSON><PERSON>', 'roedll']", "5", "77"], ["11", "cwensel/cascading", "['cwensel']", "1", "31"], ["12", "qos-ch/slf4j", "['ceki']", "1", "99"], ["13", "sbt/junit-interface", "['szeiger', 'eed3si9n', 'unkarjedy']", "3", "22"], ["14", "graphstream/gs-core", "['gsavin', 'Ant01n3', 'pigne', 's<PERSON>ev']", "4", "34"], ["15", "yui/yuicompressor", "['<PERSON>', 'tml', 's<PERSON><PERSON>', 'isaacs', 'davglass', 'stoyan', 'danbeam', '<PERSON>', '<PERSON><PERSON><PERSON>']", "9", "51"], ["16", "resty-gwt/resty-gwt", "['mk<PERSON>ian', 'chirino', 'abalke', 'Armageddon-', 'freddyboucher', '<PERSON>', 'vasvir', 'r<PERSON><PERSON>', 'seamusmac']", "9", "76"], ["17", "jai-imageio/jai-imageio-core", "['stain', '<EMAIL>']", "2", "16"], ["18", "haraldk/twelvemonkeys", "['harald<PERSON>']", "1", "40"], ["19", "pocmo/yaaic", "['pocmo']", "1", "25"], ["20", "twilio/twilio-java", "['jingming', 'twilio-ci', 'twilio-dx', 'skimbrel', '<PERSON>', 'codejudas', 'jwitz10', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', 'childish-sambino', 'jmctwi<PERSON>', 'sullis', '<PERSON>', 'Ragi<PERSON>', 'eshanholtz', 'thinkingserious', 'ChristerF', 'sban<PERSON>']", "19", "141"], ["21", "unclebob/fitnesse", "['amolenaar', 'f<PERSON>ben', 'uncle<PERSON>b', 'hans<PERSON><PERSON><PERSON>', 'jediw<PERSON>', 'six42', 'i-m-fit-r-u', 'mikes<PERSON><PERSON>', 'robertmartin', 'raboof', '<PERSON>', 'nash<PERSON>in', '<PERSON>', 'micah']", "14", "185"], ["22", "j<PERSON><PERSON><PERSON>/ognl", "['l<PERSON><PERSON><PERSON><PERSON>', 'j<PERSON><PERSON><PERSON>']", "2", "29"], ["23", "bndtools/bndtools", "['nj<PERSON><PERSON><PERSON>', 'f<PERSON><PERSON><PERSON>', 'bj<PERSON><PERSON>', 'p<PERSON><PERSON><PERSON>']", "4", "41"], ["24", "voldemort/voldemort", "['a<PERSON><PERSON><PERSON>', 'rsumbaly', 'bban<PERSON>', 'jay<PERSON><PERSON><PERSON>', 'j<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'ijuma', 'a<PERSON><PERSON><PERSON><PERSON><PERSON>', 'kirktrue', 'v<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>G<PERSON>', 'jay-w-opus', '<PERSON><PERSON>', 'abh1nay']", "15", "103"], ["25", "sparklemotion/nokogiri", "['flavorjones', 'tenderlove', 'yokolet', 'knu', 'stevecheckoway', 'jvshahid']", "6", "267"], ["26", "antlr/antlr4", "['parrt', 's<PERSON>well', 'er<PERSON><PERSON><PERSON><PERSON>', 'mike-lischke', '<PERSON><PERSON><PERSON><PERSON>', 'pboyer', 'ewan<PERSON><PERSON>', 'carocad', 'jimidle', 'jcking']", "10", "405"], ["27", "todotxt/todo.txt-android", "['gina<PERSON>pani', 'chuckbjones', 'mpcjan<PERSON>', 'tormodh', 'intrications', 'mathias', 'eisbehr']", "7", "41"], ["28", "tootallnate/java-websocket", "['marci4', '<PERSON><PERSON><PERSON><PERSON>', 'TooTallNate', 'dota17', '<PERSON><PERSON><PERSON>', 'detro', 'harun<PERSON><PERSON><PERSON>']", "7", "97"], ["29", "todoroo/astrid", "['s<PERSON><PERSON>', '<PERSON>']", "2", "25"], ["30", "torquebox/jruby-maven-plugins", "['mkristian']", "1", "26"], ["31", "maven-nar/nar-maven-plugin", "['duns', 'ctr<PERSON>en', 'dscho', 'ericker', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'b<PERSON><PERSON><PERSON>', 'for<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>']", "11", "97"], ["32", "rnewson/couchdb-lucene", "['<PERSON>']", "1", "30"], ["33", "martint/jmxutils", "['<PERSON>', 'martint', 'electrum', 'dain']", "4", "19"], ["34", "xetorthio/jedis", "['sazzad16', '<PERSON>', 'HeartSaVioR', 'marc<PERSON><PERSON><PERSON>', 'g<PERSON><PERSON>', 'xetorthio', 'yaourt', 'dengliming', 'nykolas<PERSON>a', 'chayim', 'andy-stark-redis', 'mardambey', 'zeekling', 'ivowiblo', 'yangbodong22011', 'gerzse', 'Avital-Fine', 'mindwind', 'ewhauser', 'phufool', '<PERSON>yo<PERSON>, <PERSON>', 'sam<PERSON><PERSON>', 'devFozgul', 'uglide']", "24", "253"], ["35", "jblas-project/jblas", "['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>']", "3", "30"], ["36", "apollo-rsps/apollo", "['Major-', 'gary<PERSON><PERSON><PERSON>', 'rmcmk']", "3", "35"], ["37", "magro/kryo-serializers", "['<PERSON>']", "1", "37"], ["38", "connectbot/connectbot", "['kruton', 'dependabot-preview[bot]', 'jklein24', 'rhan<PERSON>', 'alescdb']", "5", "71"], ["39", "caelum/vraptor", "['lucas<PERSON>', 'guil<PERSON><PERSON><PERSON><PERSON><PERSON>', 'peas', 'garcia-jj', 'fabiokung', '<PERSON><PERSON><PERSON><PERSON>', 'rafael<PERSON><PERSON>', 'sergiolopes']", "8", "94"], ["40", "davidb/scala-maven-plugin", "['david<PERSON>', 's<PERSON><PERSON>', 'jsuereth', 'dependabot-preview[bot]', 'pvlugter']", "5", "74"], ["41", "abarisain/dmix", "['abarisain', 'avuton', 'jcnoir', 'hurzl']", "4", "52"], ["42", "awaitility/awaitility", "['johan<PERSON><PERSON>']", "1", "52"], ["43", "apache/shiro", "['pharaohh', '<PERSON>', 'lprimak', 'bdemers', 'mentiro', 'kaosko', 'fpapon']", "7", "90"], ["44", "cbeust/jcommander", "['c<PERSON>ust', 'mkarg', 'j<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'dozmus', 'simon04', 'selliera', 'tgallagher2017']", "8", "101"], ["45", "linkedin/sensei", "['java<PERSON><PERSON>', 'wonlay', '<PERSON><PERSON><PERSON><PERSON>', 's<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>']", "7", "50"], ["46", "braintree/braintree_java", "['braintree<PERSON>', 'sa<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', 'se<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>']", "11", "222"], ["47", "vrapper/vrapper", "['kefor<PERSON>', 'albertdev', 'wawe']", "3", "41"], ["48", "qos-ch/logback", "['ceki', '<PERSON><PERSON><PERSON>']", "2", "151"], ["49", "sanger-pathogens/artemis", "['tjc', 'tcarver']", "2", "25"]], "shape": {"columns": 4, "rows": 26532}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>core_developers</th>\n", "      <th>num_core_developers</th>\n", "      <th>num_total_developers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>simpligility/android-maven-plugin</td>\n", "      <td>[m<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, will<PERSON>-fer<PERSON><PERSON>-<PERSON>, p...</td>\n", "      <td>16</td>\n", "      <td>174</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>twitter/elephant-bird</td>\n", "      <td>[<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, dv<PERSON><PERSON>, rub<PERSON><PERSON>,...</td>\n", "      <td>10</td>\n", "      <td>73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>eleybourn/book-catalogue</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>]</td>\n", "      <td>2</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>jdbi/jdbi</td>\n", "      <td>[h<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>.</td>\n", "      <td>7</td>\n", "      <td>176</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>basho/riak-java-client</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON>, <PERSON>, r<PERSON><PERSON><PERSON><PERSON>, invalid-em...</td>\n", "      <td>6</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26527</th>\n", "      <td>knowagelabs/knowage-server</td>\n", "      <td>[<PERSON><PERSON><PERSON>, <PERSON>jan<PERSON>EngIT, kerny3d, n3ils, fra...</td>\n", "      <td>22</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26528</th>\n", "      <td>hashgraph/hedera-services</td>\n", "      <td>[tinker-<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>...</td>\n", "      <td>26</td>\n", "      <td>129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26529</th>\n", "      <td>kframework/k</td>\n", "      <td>[<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...</td>\n", "      <td>16</td>\n", "      <td>130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26530</th>\n", "      <td>iterate-ch/cyberduck</td>\n", "      <td>[dkocher]</td>\n", "      <td>1</td>\n", "      <td>47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26531</th>\n", "      <td>odpi/egeria</td>\n", "      <td>[mandy-<PERSON><PERSON>, planetf1, da<PERSON><PERSON><PERSON>, cm<PERSON><PERSON>,...</td>\n", "      <td>11</td>\n", "      <td>81</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>26532 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                               repo_name  \\\n", "0      simpligility/android-maven-plugin   \n", "1                  twitter/elephant-bird   \n", "2               eleybourn/book-catalogue   \n", "3                              jdbi/jdbi   \n", "4                 basho/riak-java-client   \n", "...                                  ...   \n", "26527         knowagelabs/knowage-server   \n", "26528          hashgraph/hedera-services   \n", "26529                       kframework/k   \n", "26530               iterate-ch/cyberduck   \n", "26531                        odpi/egeria   \n", "\n", "                                         core_developers  num_core_developers  \\\n", "0      [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>, p...                   16   \n", "1      [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,...                   10   \n", "2                                  [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>]                    2   \n", "3      [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>.                    7   \n", "4      [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, invalid-em...                    6   \n", "...                                                  ...                  ...   \n", "26527  [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, kern<PERSON>3d, n3<PERSON>, fra...                   22   \n", "26528  [tinker-<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>...                   26   \n", "26529  [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...                   16   \n", "26530                                          [d<PERSON><PERSON>]                    1   \n", "26531  [man<PERSON>-<PERSON><PERSON>, planetf<PERSON>, <PERSON><PERSON><PERSON><PERSON>, cm<PERSON><PERSON>,...                   11   \n", "\n", "       num_total_developers  \n", "0                       174  \n", "1                        73  \n", "2                        29  \n", "3                       176  \n", "4                        70  \n", "...                     ...  \n", "26527                    75  \n", "26528                   129  \n", "26529                   130  \n", "26530                    47  \n", "26531                    81  \n", "\n", "[26532 rows x 4 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["core_developers_list = get_all_core_developers(project_names)\n", "# make it into a DataFrame\n", "core_developers_df = pd.DataFrame(core_developers_list)\n", "core_developers_df"]}, {"cell_type": "code", "execution_count": 13, "id": "7509d1669b34cd39", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T11:02:30.101478Z", "start_time": "2024-11-25T11:02:30.067149Z"}}, "outputs": [], "source": ["core_developers_df.to_csv('../data/core_developer_list_total_repo_without_merging_alias.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "2264e2d0374a5d5b", "metadata": {}, "outputs": [], "source": ["# merge all the bot account in a list, and store the list in a csv file with only bot_name and repo_nam"]}, {"cell_type": "markdown", "id": "830b6f23244c778c", "metadata": {}, "source": ["## Identify Bot Developers (Simple method)"]}, {"cell_type": "code", "execution_count": 6, "id": "c80fdc742a399bd0", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:54:36.267162Z", "start_time": "2024-11-25T07:52:25.326626Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing repos:   7%|▋         | 3534/50812 [01:02<15:51, 49.71repo/s]  /tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (3,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  19%|█▉        | 9716/50812 [02:17<32:28, 21.09repo/s]  /tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (2,3,5,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  20%|█▉        | 9944/50812 [02:23<10:31, 64.69repo/s]/tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (3,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  21%|██        | 10492/50812 [02:29<05:09, 130.22repo/s]/tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (2,3,5,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  54%|█████▎    | 27234/50812 [05:52<36:35, 10.74repo/s]  /tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (2,3,5,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  56%|█████▌    | 28562/50812 [06:41<15:07, 24.51repo/s]  "]}, {"name": "stdout", "output_type": "stream", "text": ["Error processing jesse-ai/jesse: 'author_login'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing repos:  73%|███████▎  | 37165/50812 [08:17<03:41, 61.73repo/s]  "]}, {"name": "stdout", "output_type": "stream", "text": ["Error processing nv-legate/legate.core: 'author_login'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing repos:  77%|███████▋  | 39069/50812 [09:04<02:59, 65.56repo/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Error processing zelonewolf/openstreetmap-americana: 'author_login'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing repos:  87%|████████▋ | 44230/50812 [10:01<01:17, 85.20repo/s] /tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (3,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos:  98%|█████████▊| 49687/50812 [11:17<00:08, 125.93repo/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Error processing cmu-db/optd: 'author_login'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing repos:  98%|█████████▊| 49848/50812 [11:20<00:09, 102.38repo/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Error processing masslight/ottehr: 'author_login'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing repos:  99%|█████████▉| 50457/50812 [11:41<00:05, 65.09repo/s] /tmp/ipykernel_2474427/********.py:3: DtypeWarning: Columns (3,6) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  repo_commit = pd.read_csv(file_path)\n", "Processing repos: 100%|██████████| 50812/50812 [11:59<00:00, 70.61repo/s]\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "bot_developers", "rawType": "object", "type": "unknown"}, {"name": "num_bot_developers", "rawType": "int64", "type": "integer"}, {"name": "num_total_developers", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "ff5ea3e7-6db1-4a65-8565-b1d237f7a056", "rows": [["0", "sparklemotion/nokogiri", "['dependabot[bot]']", "1", "236"], ["1", "davidb/scala-maven-plugin", "['dependabot-preview[bot]', 'dependabot[bot]']", "2", "61"], ["2", "tcurdt/jdeb", "['renovate[bot]', 'snyk-bot', 'dependabot[bot]', 'renovate-bot']", "4", "67"], ["3", "junit-team/junit4", "[]", "0", "146"], ["4", "yui/yuicompressor", "[]", "0", "38"], ["5", "unclebob/fitnesse", "['dependabot[bot]']", "1", "114"], ["6", "connectbot/connectbot", "['dependabot-preview[bot]', 'dependabot[bot]']", "2", "60"], ["7", "bpellin/keepassdroid", "[]", "0", "41"], ["8", "rnewson/couchdb-lucene", "[]", "0", "23"], ["9", "nodebox/nodebox", "[]", "0", "14"], ["10", "cwensel/cascading", "[]", "0", "28"], ["11", "cucumber-attic/cuke4duke", "[]", "0", "21"], ["12", "bndtools/bndtools", "[]", "0", "29"], ["13", "twitter4j/twitter4j", "[]", "0", "127"], ["14", "magro/memcached-session-manager", "[]", "0", "19"], ["15", "caelum/vraptor", "[]", "0", "60"], ["16", "maxcom/lorsource", "['dependabot[bot]']", "1", "78"], ["17", "rzwitserloot/lombok", "[]", "0", "131"], ["18", "voldemort/voldemort", "[]", "0", "55"], ["19", "jdbi/jdbi", "[]", "0", "133"], ["20", "simpligility/android-maven-plugin", "[]", "0", "122"], ["21", "jblas-project/jblas", "['dependabot[bot]']", "1", "23"], ["22", "pocmo/yaaic", "[]", "0", "20"], ["23", "ccw-ide/ccw", "[]", "0", "21"], ["24", "novoda/android-demos", "[]", "0", "17"], ["25", "qos-ch/logback", "[]", "0", "117"], ["26", "qos-ch/slf4j", "[]", "0", "78"], ["27", "apache/shiro", "['step-security-bot', 'dependabot[bot]']", "2", "67"], ["28", "haraldk/twelvemonkeys", "['snyk-bot', 'dependabot[bot]']", "2", "33"], ["29", "fusesource/jansi", "['dependabot[bot]']", "1", "36"], ["30", "yaxim-org/yaxim", "[]", "0", "17"], ["31", "cucumber-attic/gherkin2", "[]", "0", "80"], ["32", "talklittle/reddit-is-fun", "[]", "0", "24"], ["33", "twilio/twilio-java", "['snyk-bot', 'dependabot[bot]']", "2", "106"], ["34", "webmetrics/browsermob-proxy", "[]", "0", "16"], ["35", "sitemesh/sitemesh2", "[]", "0", "16"], ["36", "sirthias/parboiled", "['scala-steward-bot', 'dependabot[bot]', 'github-actions[bot]']", "3", "26"], ["37", "resty-gwt/resty-gwt", "[]", "0", "49"], ["38", "maven-nar/nar-maven-plugin", "['dependabot[bot]']", "1", "70"], ["39", "martint/jmxutils", "[]", "0", "15"], ["40", "jruby/joni", "['dependabot[bot]']", "1", "24"], ["41", "sanger-pathogens/artemis", "[]", "0", "11"], ["42", "notnoop/java-apns", "[]", "0", "24"], ["43", "trifork/erjang", "[]", "0", "10"], ["44", "torquebox/jruby-maven-plugins", "['dependabot[bot]']", "1", "22"], ["45", "jhy/jsoup", "['dependabot[bot]']", "1", "101"], ["46", "jberkel/sms-backup-plus", "[]", "0", "49"], ["47", "ervandew/eclim", "[]", "0", "35"], ["48", "sbt/junit-interface", "[]", "0", "17"], ["49", "j<PERSON><PERSON><PERSON>/ognl", "['renovate[bot]', 'dependabot[bot]']", "2", "29"]], "shape": {"columns": 4, "rows": 50804}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>bot_developers</th>\n", "      <th>num_bot_developers</th>\n", "      <th>num_total_developers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>sparklemotion/nokogiri</td>\n", "      <td>[dependabot[bot]]</td>\n", "      <td>1</td>\n", "      <td>236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>davidb/scala-maven-plugin</td>\n", "      <td>[dependabot-preview[bot], dependabot[bot]]</td>\n", "      <td>2</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>tcurdt/jdeb</td>\n", "      <td>[renovate[bot], snyk-bot, dependabot[bot], ren...</td>\n", "      <td>4</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>junit-team/junit4</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>146</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>yui/yuicompressor</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50799</th>\n", "      <td>wso2/product-micro-integrator</td>\n", "      <td>[wso2-jenkins-bot, wso2-product-performance-bo...</td>\n", "      <td>3</td>\n", "      <td>90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50800</th>\n", "      <td>mir-evaluation/mir_eval</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50801</th>\n", "      <td>graphql-hive/console</td>\n", "      <td>[renovate[bot], mend-bolt-for-github[bot], git...</td>\n", "      <td>4</td>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50802</th>\n", "      <td>pixi-viewport/pixi-viewport</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50803</th>\n", "      <td>motiondivision/motion</td>\n", "      <td>[dependabot[bot]]</td>\n", "      <td>1</td>\n", "      <td>58</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>50804 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                           repo_name  \\\n", "0             sparklemotion/nokogiri   \n", "1          davidb/scala-maven-plugin   \n", "2                        tcurdt/jdeb   \n", "3                  junit-team/junit4   \n", "4                  yui/yuicompressor   \n", "...                              ...   \n", "50799  wso2/product-micro-integrator   \n", "50800        mir-evaluation/mir_eval   \n", "50801           graphql-hive/console   \n", "50802    pixi-viewport/pixi-viewport   \n", "50803          motiondivision/motion   \n", "\n", "                                          bot_developers  num_bot_developers  \\\n", "0                                      [dependabot[bot]]                   1   \n", "1             [dependabot-preview[bot], dependabot[bot]]                   2   \n", "2      [renovate[bot], snyk-bot, dependabot[bot], ren...                   4   \n", "3                                                     []                   0   \n", "4                                                     []                   0   \n", "...                                                  ...                 ...   \n", "50799  [wso2-jen<PERSON>-bot, wso2-product-performance-bo...                   3   \n", "50800                                                 []                   0   \n", "50801  [renovate[bot], mend-bolt-for-github[bot], git...                   4   \n", "50802                                                 []                   0   \n", "50803                                  [dependabot[bot]]                   1   \n", "\n", "       num_total_developers  \n", "0                       236  \n", "1                        61  \n", "2                        67  \n", "3                       146  \n", "4                        38  \n", "...                     ...  \n", "50799                    90  \n", "50800                    10  \n", "50801                    54  \n", "50802                    29  \n", "50803                    58  \n", "\n", "[50804 rows x 4 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import pandas as pd\n", "from tqdm import tqdm\n", "\n", "def get_bot_developers(project_names):\n", "    bot_developers_list = []\n", "    for repo_name in tqdm(project_names, desc=\"Processing repos\", unit=\"repo\"):\n", "        # first check if the file exists, if not, skip\n", "        if not os.path.exists(f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"):\n", "            continue\n", "        repo_commit = get_commit_file_repo_name(repo_name)\n", "        try:\n", "            # Fill NaN values in 'author_login' with an empty string\n", "            repo_commit['author_login'] = repo_commit['author_login'].fillna('')\n", "            repo_commit['author_name'] = repo_commit['author_name'].fillna('')\n", "            # select those with '[bot]' or '-bot' or '-robot' in the 'author_login' column\n", "            bot_developers = repo_commit[repo_commit['author_login'].str.contains(r'\\[bot\\]|\\-bot|\\-robot', case=False)]['author_login'].unique().tolist()\n", "            bot_developers_name = repo_commit[repo_commit['author_name'].str.contains(r'\\[bot\\]|\\-bot|\\-robot', case=False)]['author_name'].unique().tolist()\n", "            # merge the two lists\n", "            bot_developers = list(set(bot_developers + bot_developers_name))\n", "            bot_developers_list.append({\n", "                'repo_name': repo_name,\n", "                'bot_developers': bot_developers,\n", "                'num_bot_developers': len(bot_developers),\n", "                'num_total_developers': len(repo_commit['author_login'].unique())\n", "            })\n", "        except Exception as e:\n", "            print(f\"Error processing {repo_name}: {e}\")\n", "    return bot_developers_list\n", "\n", "bot_developers_list = get_bot_developers(project_names)\n", "# make it into a DataFrame\n", "bot_developers_df = pd.DataFrame(bot_developers_list)\n", "bot_developers_df.to_csv('../data/bot_developer_list_total_repo.csv', index=False)\n", "bot_developers_df"]}, {"cell_type": "code", "execution_count": 7, "id": "8000eb81", "metadata": {}, "outputs": [], "source": ["bot_developers_df = pd.DataFrame(bot_developers_list)"]}, {"cell_type": "code", "execution_count": 8, "id": "d6beb115", "metadata": {}, "outputs": [{"data": {"text/plain": ["0                                        [dependabot[bot]]\n", "1               [dependabot-preview[bot], dependabot[bot]]\n", "2        [renovate[bot], snyk-bot, dependabot[bot], ren...\n", "3                                                       []\n", "4                                                       []\n", "                               ...                        \n", "50799    [wso2-jenkins-bot, wso2-product-performance-bo...\n", "50800                                                   []\n", "50801    [renovate[bot], mend-bolt-for-github[bot], git...\n", "50802                                                   []\n", "50803                                    [dependabot[bot]]\n", "Name: bot_developers, Length: 50804, dtype: object"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["bot_developers_df['bot_developers']"]}, {"cell_type": "code", "execution_count": null, "id": "da6e7212", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>bot_developers</th>\n", "      <th>num_bot_developers</th>\n", "      <th>num_total_developers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>24909</th>\n", "      <td>reactjs/zh-hans.reactjs.org</td>\n", "      <td>[fine-bot, docschina-bot]</td>\n", "      <td>2</td>\n", "      <td>2232</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         repo_name             bot_developers  \\\n", "24909  reactjs/zh-hans.reactjs.org  [fine-bot, docschina-bot]   \n", "\n", "       num_bot_developers  num_total_developers  \n", "24909                   2                  2232  "]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# select with bot_developers contains ''docschina-bot'\n", "bot_developers_df[bot_developers_df['bot_developers'].apply(lambda x: 'docschina-bot' in x)]\n"]}, {"cell_type": "code", "execution_count": 9, "id": "3b50bff1ee5518df", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:54:52.617682Z", "start_time": "2024-11-25T07:54:52.533511Z"}}, "outputs": [], "source": ["# merge bot developers list from single repos to a single list\n", "def merge_bot_developers(bot_developers_df):\n", "    bot_developers = []\n", "    for _, row in bot_developers_df.iterrows():\n", "        bot_developers.extend(row['bot_developers'])\n", "    # deduplicate the list\n", "    bot_developers = list(set(bot_developers))\n", "    return bot_developers\n", "bot_developers = merge_bot_developers(bot_developers_df)"]}, {"cell_type": "code", "execution_count": 10, "id": "492e7cb0e829d815", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:54:54.603366Z", "start_time": "2024-11-25T07:54:54.595752Z"}}, "outputs": [{"data": {"text/plain": ["['essos-bot',\n", " 'reunion-maestro[bot]',\n", " 'kie-tools-bot',\n", " 'malem-robotiq',\n", " 'npm-to-cdn-bot (by <PERSON>)',\n", " 'codesee-maps[bot]',\n", " 'codeflash-ai[bot]',\n", " 'scott-robotics',\n", " 'facebook-github-bot-2',\n", " 'HELICS-bot',\n", " 'mindspore-ci-bot',\n", " 'grafana-i18n-bot',\n", " 'dxos-bot',\n", " 'ibis-squawk-bot[bot]',\n", " 'NocWorx-BOT',\n", " 'x6-bot',\n", " 'tas-operability-bot',\n", " 'simple-icons[bot]',\n", " 'Jdaviz-Triage-Bot',\n", " 'consul-version-updater[bot]',\n", " 'TCGdex [Bot]',\n", " 'release-please[bot]',\n", " 'chrome-release-bot',\n", " 'ci.automation[bot]',\n", " 'lego-10-01-06[bot]',\n", " 'dependabot-circleci[bot]',\n", " 'Liss-Bo<PERSON>',\n", " 'serokell-bot',\n", " 'redocly-bot',\n", " 'openshift-merge-bot',\n", " 'm<PERSON><PERSON><PERSON><PERSON>-robot',\n", " 'openpracticelibrary-bot',\n", " 'posthog-bot',\n", " 'servicetalk-bot',\n", " 'gradle-update-robot',\n", " 'orbitprofiler-bot',\n", " 'jsdoc-bot',\n", " '@r2wc/react-to-web-component[bot]@workflow',\n", " 'tas-runtime-bot',\n", " 'kubeflow-pipeline-bot',\n", " 'dae-bot[bot]',\n", " 'googleforcreators-bot',\n", " 'prettier-toc-me[bot]',\n", " 'godaddy-wordpress-bot',\n", " 'lbry-bot',\n", " 'minio-bot',\n", " 'facebook-github-bot-9',\n", " 'eks-distro-pr-bot',\n", " 'aws-crypto-tools-ci-bot',\n", " 'suzuki-shunsuke-app[bot]',\n", " 'protobuf-github-bot',\n", " 'rh-trusted-application-pipeline[bot]',\n", " 'ch-integrations-robot',\n", " 'openapi-sdkautomation[bot]',\n", " 'daniele-bottelli',\n", " 'jenkins-x-labs-bot',\n", " 'pangeran-bottor',\n", " 'shopify[bot]',\n", " 'InstantSearch [bot]',\n", " 'obspy-bot',\n", " 'c2c-bot-gis-ci',\n", " 'openshift-ci-robot',\n", " 'budimanjojo-bot',\n", " 'moderneapp[bot]',\n", " 'emscripten-bot',\n", " 'woodpecker-bot',\n", " 'flowtyped-bot',\n", " 'oss-release-bot',\n", " 'hopr-version-bot',\n", " 'PolicyEngine[bot]',\n", " 'omnibus-software-auto-bump[bot]',\n", " 'grailbio-bot',\n", " 'robotology-bot (Tag Repository Action)',\n", " 'kubevirt-bot',\n", " 'callstack-bot',\n", " 'tophat-opensource-bot',\n", " 'valora-bot-crowdin',\n", " 'OTA-Release-Bot',\n", " 'p42-ai[bot]',\n", " 'atlas-dst-bot',\n", " 'r<PERSON><PERSON><PERSON>[bot]',\n", " 'apollo-bot[bot]',\n", " 'sourcegraph-release-bot',\n", " 'celo-ci-bot-user',\n", " 'access-token-generator[bot]',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-bot',\n", " 'kubeflow-bot',\n", " 'Repo-Sync-Bot',\n", " 'kcl-release-automation-bot',\n", " 'si-bors-ng[bot]',\n", " 'jetstack-bot',\n", " 'authentik-automation[bot]',\n", " 'hurl-bot',\n", " 'ergebnis-bot',\n", " 'bassamtantawi-botpress',\n", " 'stack-file[bot]',\n", " 'public-renovate-gha[bot]',\n", " 'greenkeeperio-bot',\n", " 'oxide-renovate[bot]',\n", " 'kiali-bot',\n", " 'trailheadapps-bot',\n", " 'anchore-actions-token-generator[bot]',\n", " 'stackable-bot',\n", " 'zksync-admin-bot2',\n", " 'Favware-bot',\n", " 'Universe-7-bot',\n", " 'iris-actions[bot]',\n", " 'natfrp-bot',\n", " 'prowler-bot',\n", " 'eclipse-jdt-bot',\n", " 'detsys-pr-bot',\n", " 'eltriny-bot',\n", " 'microsoft-github-policy-service[bot]',\n", " 'moul-bot',\n", " 'sagemaker-bot',\n", " 'pancakeswap-github-bot',\n", " 'Dotnet-GitSync-Bot',\n", " 'eclipse-milo-bot',\n", " 'mo-bot',\n", " 'zwo-bot',\n", " 'sustainable-computing-bot',\n", " 'themeisle[bot]',\n", " 'atlassian-compass[bot]',\n", " 'kudo-sync-bot',\n", " 'eclipse-ecal-bot',\n", " 'CBL-<PERSON>r-<PERSON><PERSON>',\n", " 'xerial-bot',\n", " 'grit-app[bot]',\n", " 'high5-bot',\n", " 'shuoli-robotics',\n", " 'nextest-bot',\n", " 'size-plugin[bot]',\n", " 'SORMAS-Robot',\n", " 'gr8rj-bot',\n", " 'svc-idee-bot',\n", " 'npmcdn-to-unpkg-bot',\n", " 'infra-db-release-bot',\n", " 'weaviate-git-bot',\n", " 'p-robot',\n", " 'gitbook-bot',\n", " 'Kaiser-bot',\n", " 'Jena-bot',\n", " 'trust-bot',\n", " 'redwood-bot',\n", " 'openhab-bot',\n", " 'autofix-ci[bot]',\n", " 'owt-bot',\n", " 'anycli-bot',\n", " 'bob-bot',\n", " 'mdn-bot',\n", " 'nextcloud-pr-bot',\n", " 'mojarra-bot',\n", " 'builderio-bot',\n", " 'Bidaya0-bot',\n", " 'ipa-robotino',\n", " 'typedb-bot',\n", " 'craig[bot]',\n", " 'web-scrobbler-bot',\n", " 'todd-the-bot',\n", " 'pfc-bot',\n", " 'mosasauroidea-bot',\n", " 'hasura-bot',\n", " 'toggl-button-bot',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'liurunliang-bot',\n", " 'renovate[bot]',\n", " 'check-bot',\n", " 'slimevr-bot',\n", " 'team-moderne[bot]',\n", " 'bw-ghapp[bot]',\n", " 'ma-robot.com',\n", " 'dd-pub-platform[bot]',\n", " 'ntp-bot',\n", " 'thi-bot',\n", " 'envoy-java-control-plane-bot',\n", " 'eventstore-bot',\n", " 'frappe-bot',\n", " 'silverhand-bot',\n", " 'pmem-bot',\n", " 'oca-git-bot',\n", " 'victoriametrics-bot',\n", " 'r2c-argo[bot]',\n", " 'brutusthe<PERSON>[bot]',\n", " 'sumo-backporter[bot]',\n", " 'gitjob-bot',\n", " 'sanson-robotics',\n", " 'sourcegraph-release-guild-bot',\n", " 'porter-deployment-app[bot]',\n", " 'oktapp-bacon-worker-okta[bot]',\n", " 'yc-ui-bot',\n", " 'kleinanzeigen-bot-tu[bot]',\n", " 'apk-github-bot',\n", " 'flojoy-bot[bot]',\n", " 'fenics-bot',\n", " 'syntax-transcript-bot[bot]',\n", " 'dionisio-bot[bot]',\n", " 'icinga-probot[bot]',\n", " 'k8s-on-aws-manager-app[bot]',\n", " 'terraform-exec [bot]',\n", " 'jf-botto',\n", " 'eclipse-cdi-bot',\n", " 'forest-bot',\n", " 'BLee-bot',\n", " 'chrome-bot',\n", " 'terraform-docs-bot',\n", " 'ferretdb-bot',\n", " 'npg-bot',\n", " 'fakerjs-bot',\n", " 'rancher-security-bot',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>',\n", " 'zwave-js-bot',\n", " 'cf-allstar[bot]',\n", " 'neil-botelho',\n", " 'spotless[bot]',\n", " 'redhattools-bot',\n", " '<PERSON><PERSON><PERSON>-bot[bot]',\n", " 'apollogith<PERSON><PERSON>bot[bot]',\n", " 'PlaykitJs-Bo<PERSON>',\n", " 'chime-sdk-js-bot',\n", " 'eclipse-californium-bot',\n", " 'gae-java-bot',\n", " 'resin-io-modules-versionbot[bot]',\n", " 'kr8s-bot',\n", " 'bitnami-bot',\n", " 'pta-robot',\n", " 'dotnet-sb-bot',\n", " 'pyansys-ci-bot',\n", " 'aw-ci-bot',\n", " 'dataops-ci-bot',\n", " 'etc-contribunator[bot]',\n", " 'bucketeer-bot',\n", " 'icon-bot',\n", " 'google-cloud-policy-bot[bot]',\n", " 'blitzjs-bot',\n", " 'stable-bot for <PERSON>',\n", " 'unified-ci-app[bot]',\n", " 'moz-wptsync-bot',\n", " 'o3de-issues-bot',\n", " 'tf-models-copybara-bot',\n", " 'ioredis-robot',\n", " 'rouault-bot',\n", " 'github-actions-dotnet-formatter[bot]',\n", " 'zakodium-bot',\n", " 'wso2-product-performance-bot',\n", " 'autosync[bot] 🔃',\n", " 'B<PERSON><PERSON><PERSON>ard-bot',\n", " 'k8slens-bot',\n", " 'fal-bot',\n", " 'coredns[bot]',\n", " 'kubeclipper-bot',\n", " 'starkandwayne-bot',\n", " 'im-robot',\n", " 'checkly[bot]',\n", " 'nvidia-merlin-bot',\n", " 'turtlesocks-bot',\n", " 'metrix-ci-bot',\n", " 'p3x-robot',\n", " 'rhacs-bot',\n", " 'cloudinary-bot',\n", " 'primus-bot[bot]',\n", " 'tina-cloud-app[bot]',\n", " 'github-bot',\n", " 'ospo-bot',\n", " 'zblogcn-bot',\n", " 'eclipse-releng-bot',\n", " 'kodiakhq[bot]',\n", " 'amplication[bot]',\n", " 'condo-bot',\n", " 'anton-bot',\n", " 'google-oss-robot',\n", " 'xflow-bot[bot]',\n", " 'ergomake[bot]',\n", " 'openapi-getsentry-bot',\n", " 'syd-botz',\n", " 'openeuler-ci-bot',\n", " 'ChainPatrol-bot',\n", " 'geovista-ci[bot]',\n", " 'yandex-cloud-bot',\n", " 'git-machete-bot',\n", " 'import-bot',\n", " 'mergify-bot',\n", " 'rustin-bot',\n", " 'OpenRCT2-git-bot',\n", " 'aws-amplify-bot',\n", " 'actiontech-bot',\n", " '<EMAIL>',\n", " 'Discord-NET-Robot',\n", " 'sonar-dotnet-bot',\n", " 'ilastik-bot',\n", " 'WPIRoboticsProjects-Bot',\n", " 'geovista-actions[bot]',\n", " 'cthulhu-bot',\n", " 'rtbyte-bot',\n", " 'bit-bot-bit',\n", " 'pyup-vuln-bot',\n", " 'railway-bot',\n", " 'pyvista-bot',\n", " 'sourcegraph-bot',\n", " 'webdjoe-bot',\n", " 'edx-cache-uploader-bot',\n", " 'gardener-robot-ci-3',\n", " 'semgrep-dev-pr-bot[bot]',\n", " 'nuxt-studio-dev[bot]',\n", " 'opensearch-trigger-bot[bot]',\n", " 'electron-appveyor-updater[bot]',\n", " 'Grim-bot',\n", " 'lottiefiles-bot',\n", " 'TizenAPI-Bot',\n", " 'kubermatic-bot',\n", " 'mongodb-devtools-bot[bot]',\n", " 'eclipse-eclipselink-bot',\n", " 'l5d-bot',\n", " 'github-service-catalog[bot]',\n", " 'osc-bot',\n", " 'zeek-bot',\n", " 'n-botthof',\n", " 'calcom-bot',\n", " 'clueless-bot',\n", " 'corretto-github-robot',\n", " 'preact-bot',\n", " 'remix-cla-bot[bot]',\n", " 'Yew-<PERSON><PERSON>-Bo<PERSON>',\n", " 'coredns-auto-go-fmt[bot]',\n", " 'eggroll-bot',\n", " 'joyloy-bot',\n", " 'openshift-merge-robot',\n", " 'math<PERSON><PERSON><PERSON><PERSON>-bot',\n", " 'kiwicom-gitlab-bot',\n", " 'uccser-bot',\n", " 'skrub-bot',\n", " 'k8s-merge-robot',\n", " 'Nitish-bot',\n", " 'sentry-autofix-experimental[bot]',\n", " 'nr-opensource-bot',\n", " 'openclarity[bot]',\n", " 'pyroscope-development-app[bot]',\n", " 'max-bot1',\n", " 'charcoal-bot[bot]',\n", " 'trigger-github-actions-release[bot]',\n", " 'TG-BOTSNETWORK',\n", " 'Changelog-bot',\n", " 'chuwy-bot',\n", " 'mongodb-atlas-app-services[bot]',\n", " 'update-envoy[bot]',\n", " 'synthetik-bot',\n", " 'grafana-pr-automation[bot]',\n", " 'pix-bot',\n", " 'release-train[bot]',\n", " 'elastic-vault-github-plugin-prod[bot]',\n", " 'devcontainers-bot',\n", " 'gcf-merge-on-green[bot]',\n", " 'nur-bot',\n", " 'blacksmith-sh[bot]',\n", " 'codetriage-readme-bot',\n", " 'rtthread-bot',\n", " 'pyup-bot',\n", " 'croct-bot',\n", " 'pipedrive-renovate[bot]',\n", " 'b-bot',\n", " 'conan-center-bot',\n", " 'devlooped-bot',\n", " 'lit-robot',\n", " 'internalautomation[bot]',\n", " 'kg-bot',\n", " 'ninech-bot',\n", " 'coredns-auto-go-mod-tidy[bot]',\n", " 'pull[bot]',\n", " 'crossplane-renovate[bot]',\n", " 'flarum-bot',\n", " 'scaleway-bot',\n", " 'spyder-bot',\n", " 'hikari-bot',\n", " 'flyte-bot',\n", " 'warrenbailey-cb-bot',\n", " 'logistic-bot',\n", " 'spoon-bot',\n", " 'keycloak-bot',\n", " 'mlcommons-bot',\n", " 'yourls-bot[bot]',\n", " 'jx-app-bot',\n", " 'telegraf-tiger[bot]',\n", " 'pr-automation-bot-public[bot]',\n", " 'positron-bot[bot]',\n", " 'plural-bot',\n", " '<PERSON><PERSON>-<PERSON><PERSON>',\n", " 'techy-robot',\n", " 'semgrep-ci[bot]',\n", " 'pmd-bot',\n", " 'pandas-docs-bot',\n", " 'alek-kam-robotec-ai',\n", " 'tyrus-bot',\n", " 'ghosterey-adblocker-bot[bot]',\n", " '2i2c-token-generator-bot[bot]',\n", " 'philips-software-forest-releaser[bot]',\n", " 'ghosted-bot',\n", " 'the-code-robot',\n", " 'rhdh-bot service account',\n", " 'minikube-bot',\n", " 'circleci-bot',\n", " 'angular-robot',\n", " 'gcf-owl-bot[bot]',\n", " 'sui-merge-bot[bot]',\n", " 'rancher-sy-bot',\n", " 'R<PERSON>-<PERSON><PERSON>',\n", " 'sbb-angular-renovate[bot]',\n", " 'hhvm-bot',\n", " 'martin-robot',\n", " 'eclipse-edc-bot',\n", " 'ilo-nanpa[bot]',\n", " 'airbnb-bot',\n", " 'vidavidorra[bot]',\n", " 'djlint-bot',\n", " '1Panel-bot',\n", " 'Anonymous-Github-Robot',\n", " 'miguel-botelho',\n", " 'robotology-bot',\n", " 'MapServer-backport-bot',\n", " 'paketo-bot',\n", " 'xls-github-bot',\n", " 'DeSerFix-bot',\n", " 'gitlab-bot',\n", " 'client-engineering-bot',\n", " 'webdetails-build-bot',\n", " 'brutus[bot]',\n", " 'cf-rabbit-bot',\n", " 'scverse-bot',\n", " 'smallrye-ci-releases[bot]',\n", " '@lottiefiles-bot',\n", " 'release-plz-ipvm-wg[bot]',\n", " 'reactjs-translation-bot',\n", " 'carbon-bot',\n", " 'surc-bot',\n", " 'jersey-bot',\n", " 'service-bot-app[bot]',\n", " 'microsoft-github-operations[bot]',\n", " 'openshift-cherrypick-robot',\n", " 'openiddict-bot',\n", " 'defichain-bot',\n", " 'tidb-cloud-data-service[bot]',\n", " 'Sunny-bot1',\n", " 'openshift-merge-bot[bot]',\n", " 'dotnet-bot',\n", " 'che-bot',\n", " 'joomla-translation-bot',\n", " 'ipfs-mgmt-read-write[bot]',\n", " 'Flobotics-robotics.com',\n", " 'luau-types-generator[bot]',\n", " 'Qv2ray-Bo<PERSON>',\n", " 'bunq-bot',\n", " 'voxel51-bot',\n", " 'ohif-bot',\n", " 'SlimeVR-bot',\n", " 'CNCF-Bot2',\n", " 'eclipse-leshan-bot',\n", " 'net-jetbot[bot]',\n", " 'tf-release-bot',\n", " 'tidb-dashboard-bot',\n", " 'intel-k8s-bot',\n", " 'promote-release[bot]',\n", " 'damien-<PERSON><PERSON>',\n", " 'pull-robot',\n", " 'polly-updater-bot[bot]',\n", " 'token-generator-app[bot]',\n", " 'facebook-flipper-bot',\n", " 'platform-engineering-bot',\n", " 'companion-module-bot',\n", " 'firebase-workflow-trigger[bot]',\n", " 'brim-bot',\n", " 'hig-bot',\n", " 'violinist-bot',\n", " 'dev-portal-coveo-open-source-org[bot]',\n", " 'testplatform-bot',\n", " 'snip-bot',\n", " 'enigma2-translation-bot',\n", " 'data-intelligence-robot',\n", " '<PERSON>-<PERSON><PERSON>',\n", " 'espressif-bot',\n", " 'cb-robot',\n", " 'reactor-production[bot]',\n", " 'hawkeye-bot',\n", " 'pranav-bot-code',\n", " 'cnes-datalabs-bot',\n", " 'ellipsis-dev[bot]',\n", " 'boxy-robot',\n", " 'cocos-robot',\n", " 'salte-bot',\n", " 'scikit-learn-bot',\n", " 'scdf-build-bot',\n", " 'dyu-bot',\n", " 'npm-cli-bot',\n", " 'aks-node-sig-release-assistant[bot]',\n", " 'triple-frontend[bot]',\n", " 'roadie-bot',\n", " 'hco-bot',\n", " 'qkeras-robot',\n", " 'capa-bot',\n", " 'tauri-bot',\n", " 'carvel-bot',\n", " 'aws-tensorflow-bot',\n", " 'vuejs-jp-bot',\n", " 'mlx-bot',\n", " 'jayly-bot',\n", " 'i-robot',\n", " 'tenzir-bot',\n", " 'frankframework-bot',\n", " 'password-manager-resources-bot',\n", " 'sre-ci-robot',\n", " 'shopify-github-actions-access[bot]',\n", " 'janus-idp[bot]',\n", " 'kubeapps-bot',\n", " 'up-up-and-away[bot]',\n", " 'rhdh-bot',\n", " 'xnnpack-bot',\n", " 'github-ci-robot',\n", " 'tensorflow-graphics-github-robot',\n", " 'bcsnyk-bot',\n", " 'j143-bot',\n", " 'cc-creativecommons-github-io-bot',\n", " 'taiga-family-bot',\n", " 'greenkeeper[bot]',\n", " 'alinea-cloud[bot]',\n", " 'Skeptic-Robot',\n", " 'google-labs-jules[bot]',\n", " 'frakt-bot',\n", " 'augustoproiete-bot',\n", " 'team-k8s-bot',\n", " 'DotNet-Bot',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " 'sgrep-live-pr-bot[bot]',\n", " 'upgradvisor-bot',\n", " 'syself-bot[bot]',\n", " 'toship-botsync',\n", " 'lace-bot',\n", " 'tooljet-bot',\n", " 'token-list-automerger[bot]',\n", " 'scikit-build-app-bot[bot]',\n", " 'ci-bot',\n", " 'newrelic-ruby-agent-bot',\n", " 'istio-bot',\n", " 'searx-bot',\n", " 'mapbox-github-ci-writer-public-1[bot]',\n", " 'vuln-bot',\n", " 'modrinth-bot',\n", " 'vkui-bot',\n", " 'trap-renovate[bot]',\n", " 'drone-bot',\n", " 'kosmos-robot',\n", " 'not-a-robot69',\n", " 'redhat-renovate-bot',\n", " 'snyk-io[bot]',\n", " 'nf-core-bot',\n", " 'janeway-bot',\n", " 'bsig-gh-bot',\n", " 'fern-api[bot]',\n", " 'changelog-bot',\n", " 'dotnet-build-bot',\n", " 'aws-dlinfra-bot',\n", " 'kubeshop-bot',\n", " 'microcovid-automation[bot]',\n", " 'phillip-ground[bot]',\n", " 'ci-robot',\n", " 'exercism-bot',\n", " 'condo-design-bot',\n", " 'code-foundations[bot]',\n", " 'whitesource-bolt-for-github[bot]',\n", " 'plasmapy-requirements-bot[bot]',\n", " 'Jo-bot86',\n", " 'hippo4jbot[bot]',\n", " 'Continuous Integration [bot]',\n", " 'pypi-combine-prs[bot]',\n", " 'gdpr-tracker-bot',\n", " 'confs-tech-bot',\n", " 'format-robot',\n", " 'Hyperion-Bo<PERSON>',\n", " 'storybook-safe-bot',\n", " 'Pvl-bot',\n", " 'likec4-ci[bot]',\n", " 'flashlight-bot',\n", " 'live-github-bot[bot]',\n", " 'ComunidadAM-Bot',\n", " 'casbin-bot',\n", " 'eclipse-jakartaee-tck-bot',\n", " 'learn-build-service-prod[bot]',\n", " 'docusaurus-bot',\n", " 'shroud-robot',\n", " 'terraform-config[bot]',\n", " 'refine-bot',\n", " 'deepsource-autofix-local[bot]',\n", " 'element-bot',\n", " 'owlbot-bootstrapper[bot]',\n", " 'hongyun-robot',\n", " 'rosey-bot',\n", " 'prettierci[bot]',\n", " 'ci-robbot [bot]',\n", " 'packagrio-bot',\n", " 'gitbook-com[bot]',\n", " 'r<PERSON><PERSON>g[bot]',\n", " 'casperlabs-bors-ng[bot]',\n", " 'specifyapp[bot]',\n", " 'dotnet-maestro[bot]',\n", " 'neoforged-renovate[bot]',\n", " 'fauna-bot',\n", " 'atlauncher-bot',\n", " 'brian-bot',\n", " 'axmol-bot',\n", " 'pirate-bot',\n", " 'jpegxl-bot',\n", " 'time-to-leave[bot]',\n", " 'signalstickers-bot',\n", " 'mypy-build-bot',\n", " 'k8s-publish-robot',\n", " 'njzjz-bot',\n", " 'apecloud-bot',\n", " 'grouparoo-bot',\n", " 'pulumi-bot',\n", " 'dependencytrack-bot',\n", " 'paritytech-substrate-connect-pr[bot]',\n", " 'jwlash-read-bot',\n", " 'mergify[bot]',\n", " 'aspnet-contrib-service-account[bot]',\n", " 'gitauto-ai[bot]',\n", " 'TosbackCgusBridge-<PERSON><PERSON>',\n", " 'hono-bot',\n", " 'whitesource-for-github-com[bot]',\n", " 'azure-powershell-bot',\n", " 'twa-bot',\n", " 'teto-bot',\n", " 'bors[bot]',\n", " 'ob-operator-bot',\n", " 'pascal-botpress',\n", " 'cadlab-io[bot]',\n", " 'helmwave-bot[bot]',\n", " 'ld-repository-standards[bot]',\n", " 'sentry-autofix[bot]',\n", " 'luau-language-server-helper[bot]',\n", " 'sustainabile-computing-bot',\n", " 'idinero-danger-bot',\n", " 'borisfx-robot',\n", " 'k8s-ci-robot',\n", " 'open-collective-bot[bot]',\n", " 'debricked[bot]',\n", " 'JabRef refresh journal lists [bot]',\n", " 'nathan-bottomley',\n", " 'contrib-readme-bot',\n", " 'facebook-github-bot',\n", " 'ActionTech-bot',\n", " 'yasson-bot',\n", " 'gh-automation-app[bot]',\n", " 'pymedphys-bot',\n", " 'bjerk-bot',\n", " 'erda-bot',\n", " 'lingohub[bot]',\n", " 'qgis-bot',\n", " 'Nextcloud-PR-Bo<PERSON>',\n", " 'wge-build-bot',\n", " 'ui5-webcomponents-bot',\n", " 'k8s-infra-cherrypick-robot',\n", " 'circleci-docs-license-generator-bot',\n", " 'hackclub-bot',\n", " 'Amplication-Bo<PERSON>',\n", " 'wdio-bot',\n", " 'gitstart-app[bot]',\n", " 'react-to-webcomponent[bot]@workflow',\n", " 'Github Actions[bot]',\n", " 'imagebuilder-bot',\n", " 'rp-bot',\n", " 'it-is-a-robot',\n", " 'Anushka-bot',\n", " 'deepin-bot[bot]',\n", " '<PERSON><PERSON><PERSON><PERSON>[bot]',\n", " 'aptos-bot',\n", " 'gluon-bot',\n", " 'Dev Infrastructure [Bot]',\n", " 'ipfs-gui-bot',\n", " 'explosion-bot',\n", " 'fleet-bot',\n", " 'gatk-sv-bot',\n", " 'acemod-bot',\n", " 're-team-bot',\n", " 'antrea-bot',\n", " 'grafana-backstage-integration[bot]',\n", " 'jss-release-bot',\n", " 'sider[bot]',\n", " 'abc-bot',\n", " 'trendyol-bot',\n", " 'FlexGet-Bot',\n", " 'ronething-bot',\n", " 'mindspore-bot',\n", " 'renovate-bot',\n", " 'highsoft-bot',\n", " 'fog-workflows[bot]',\n", " 'azure-pipelines[bot]',\n", " 'Picnic-Bo<PERSON>',\n", " 'lokalise-bot',\n", " 'stickler-ci[bot]',\n", " 'pyuk-bot',\n", " 'stream-ci-bot',\n", " 'deploy-bot-alex-<PERSON><PERSON><PERSON><PERSON>',\n", " 'moditect-release-bot',\n", " 'updatecli-bot',\n", " 'phpmyadmin-bot',\n", " 'qodana-cloud[bot]',\n", " 'hikari-bot[bot]',\n", " 'whatif-bot',\n", " 'localstack[bot]',\n", " 'syntax-bot',\n", " 'pymc-bot',\n", " 'cgeo-ci-bot',\n", " 'worldcoin[bot]',\n", " 'stdlib-bot',\n", " 'action-assistant[bot]',\n", " 'Forty-Bot',\n", " 'itavero-s-helping-cat[bot]',\n", " 'ct-changesets[bot]',\n", " 'optic-release-automation[bot]',\n", " 'semantic-release [bot]',\n", " 'theguild-bot',\n", " 'uhh2-bot',\n", " 'docs-bot',\n", " 'release-controller[bot]',\n", " 'restyled-io[bot]',\n", " 'browser-automation-bot',\n", " 'vaadin-bot',\n", " 'iree-github-actions-bot',\n", " 'react-translation-bot',\n", " 'codebot-robot',\n", " 'build-o-bot',\n", " 'netlify-bot',\n", " 'young-robot',\n", " 'tanzu-bump-robot',\n", " 'github-actions [bot]',\n", " 'mayhem-bot',\n", " 'cloud-spanner-emulator-bot',\n", " 'hyperledger-bot',\n", " 'datadog-agent-integrations-bot[bot]',\n", " 'slackapi[bot]',\n", " 'swoole-bundle-bot',\n", " 'typescript-eslint[bot]',\n", " 'aserto-bot',\n", " 'sk1project-build-bot',\n", " 'ContinualAI-bot',\n", " 'autoware-sync-bot[bot]',\n", " 'rerun-bot',\n", " 'certus-bot',\n", " 'fixmie[bot]',\n", " 'edx-pipeline-bot',\n", " '<EMAIL>',\n", " 'stripe-openapi[bot]',\n", " 'sunset-bot',\n", " 'meili-bot',\n", " 'psap-ci-robot',\n", " 'k3d-io-bot',\n", " 'edx-transifex-bot',\n", " 'cog-bot',\n", " 'syrupy-bot',\n", " 'jade-bot',\n", " 'packagr-io-beta[bot]',\n", " 'hopr-bot',\n", " 'facebook-github-bot-6',\n", " 'sui-bot',\n", " 'I-am-<PERSON><PERSON>',\n", " 'lingvo-bot',\n", " 'GitHub Actions[bot]',\n", " 'swc-bot',\n", " 'openfasoc-bot',\n", " 'slicer-app[bot]',\n", " 'stoplight-bot',\n", " 'cf-ci-bot',\n", " 'gitlab-terraform-provider-bot',\n", " 'meoww-bot',\n", " 'openshift-edge-bot',\n", " 'osv-robot',\n", " 'vercel-release-bot',\n", " 'app-toolkit-opensource-innersource[bot]',\n", " 'marmelab-bot',\n", " 'pipedrive-bot',\n", " 'semgrep-live-pr-bot[bot]',\n", " 'wwebjs-bot',\n", " 'shopinvader-git-bot',\n", " 'testcafe-build-bot',\n", " 'otto-the-bot',\n", " 'multiformats-mgmt-read-write[bot]',\n", " 'bot-me[bot]',\n", " 'chef-expeditor[bot]',\n", " 'quant-ranger[bot]',\n", " 'ai-roboter-1',\n", " 'tin-robot',\n", " 'npmbuildbot-nextcloud[bot]',\n", " 'polywrap-build-bot',\n", " 'mrhappyma-bot',\n", " 'highlander-ci-bot',\n", " 'changelog-pr-bot',\n", " 'teleport-post-release-automation[bot]',\n", " 'nhatthm-bot',\n", " 'core-repository-dispatch-app[bot]',\n", " 'wks-ci-test-bot',\n", " 'cibuildwheel-bot[bot]',\n", " 'mljs-bot',\n", " 'oxc-bot',\n", " 'friendly-test-bot',\n", " 'zerozero-robotic',\n", " 'csatf-bot',\n", " 'sedy-bot',\n", " 'sealos-ci-robot',\n", " 'Reg [bot]',\n", " 'tigrisdata-argocd-bot',\n", " 'ec2-bot',\n", " 'konstruct-bot',\n", " 'speakeasy-bot',\n", " 'red-hat-trusted-app-pipeline[bot]',\n", " 'PitchBlack-BOT',\n", " 'jazzband-bot',\n", " 'kissaten-bot',\n", " 'rmp-bot',\n", " 'c-bot',\n", " 'jenkins-ci-bot',\n", " 'codeautopilot[bot]',\n", " 'jellyfishsdk-bot',\n", " 'mfma<PERSON>ror-bot',\n", " 'msftbot[bot]',\n", " 'snow-lance[bot]',\n", " 'renovate-coveooss[bot]',\n", " 'civility-bot',\n", " 'keystonejs-release-bot',\n", " 'semi-bot',\n", " 'djangoupdater-bot',\n", " 'salesforce-nucleus[bot]',\n", " 'packit-public-repos-bot',\n", " 'openapi-ts-bot',\n", " 'itpp-bot',\n", " 'i18n-bot',\n", " 'bitwarden-devops-bot',\n", " 'checrs-bot',\n", " 'CNCF-bot',\n", " 'forest-releaser[bot]',\n", " 'Swa<PERSON><PERSON><PERSON>-<PERSON><PERSON>',\n", " 'weex-issue-bot',\n", " 'koishi-bot',\n", " 'krismy-botkin',\n", " 'budimanjojo-bot[bot]',\n", " 'dd-devflow[bot]',\n", " 'square-build-bot',\n", " 'gardener-robot-ci-1',\n", " 'Auto-GPT-Bot',\n", " 'pre-commit-ci[bot]',\n", " 'instantsearch-bot',\n", " 'process-analytics-bot',\n", " 'PROJ-BOT',\n", " 'epi2melabs-bot',\n", " 'knative-prow-updater-robot',\n", " 'mistah-robot',\n", " 'Anzz-bot',\n", " 'chatgpt-bot',\n", " 'roller-bot',\n", " 'buildo-release-bot[bot]',\n", " 'opsmill-bot',\n", " 'swagger-bot',\n", " 'red-hat-konflux[bot]',\n", " 'clickhouse-robot-curie',\n", " 'timja-bot',\n", " 'harana-bot',\n", " 'iree-copybara-bot',\n", " 'inclusive-coding-bot',\n", " 'daft-bot',\n", " 'trax-robot',\n", " 'cOS-ci [bot]',\n", " 'browser-specs-bot',\n", " 'Xinyu-bot',\n", " 'digidem-bot',\n", " 'netlify-tokens-generator-app[bot]',\n", " 'vlsida-bot',\n", " 'danez-bot',\n", " 'neos-bot',\n", " 'fw-bot-adhoc',\n", " 'prints-charming-bot',\n", " 'translation-platform[bot]',\n", " 'vassal-bot',\n", " 'gardener-botmanager',\n", " 'rbe-toolchains-copybara-robot',\n", " 'salesforce-ux-bot',\n", " 'fat-bot',\n", " 'submariner-bot',\n", " 'sourcery-ai[bot]',\n", " 'nb-bot',\n", " 'github-openapi-bot',\n", " 'automatic-robot',\n", " 'semaphore-agent-production[bot]',\n", " 'semantic-release-bot',\n", " 'eth-jenkins-bot',\n", " 'dolt-release-bot',\n", " 'marvin-robot',\n", " 'ansible-translation-bot',\n", " 'osmo-bot',\n", " 'tonyke-bot',\n", " 'mini-bot',\n", " 'stompy-bot',\n", " 'ermek-botpress',\n", " 'dotnet-docker-bot',\n", " 'nextcloud-bot',\n", " 'k8s-publishing-bot',\n", " 'cpeditor-bot',\n", " 'vivcat[bot]',\n", " 'vb-bot',\n", " 'electron-roller[bot]',\n", " 'step-security-bot',\n", " 'flat-bot',\n", " 'asyncer-io-bot',\n", " 'sonatype-lift[bot]',\n", " 'adblocker-bot',\n", " 'pulumi-renovate[bot]',\n", " 'biggie-jenkins-bot',\n", " '<PERSON>[bot]',\n", " 'heroku-linguist[bot]',\n", " 'trop[bot]',\n", " 'dependencies-bot',\n", " 'poggit-bot',\n", " 'fine-bot',\n", " 'arcgis-maps-sdk-bot',\n", " 'galoybot-app[bot]',\n", " 'proj-terraform-exec-bot',\n", " 'gmx-dev-bot',\n", " 'resin-io-versionbot[bot]',\n", " 'vue-bot',\n", " 'gcp-cherry-pick-bot[bot]',\n", " 'fw-bot',\n", " 'i-tier-bot',\n", " 'svc-docs-eng-opensource-bot',\n", " 'publish-envoy[bot]',\n", " 'b-it-bots',\n", " 'nvml-bot',\n", " 'packagr-io[bot]',\n", " 'xendit-devx-bot',\n", " 'publicuibot[bot]',\n", " 'kantai-robot',\n", " 'korifi-bot',\n", " 'nebula-bots',\n", " 'cd-jenkins-bot',\n", " 'brefphp-bot',\n", " 'opencollective-robot',\n", " 'milionowa-firma-bot',\n", " 'soleng-terraform[bot]',\n", " 'doppins-bot',\n", " 'pubnub-release-bot',\n", " 'prometheus-rpm-bot',\n", " 'jrnl-bot',\n", " 'google-pr-creation-bot',\n", " 'kroxylicious-robot',\n", " 'bitrise-devs-bot',\n", " 'pathwar-bot',\n", " 'cypress-viewport-updater-bot[bot]',\n", " 'svc-cli-bot-jsforce',\n", " 'mimblewimble-robot',\n", " 'rward-bot',\n", " 'tinkoff-bot',\n", " 'gravity-ui-bot',\n", " 'airlift-bot',\n", " 'ferdium-bot',\n", " 'phasetwo-bot',\n", " 'build-bot',\n", " 'wfcd-bot-boi',\n", " 'kendo-bot',\n", " 'hcloud-bot',\n", " 'teclib-bot',\n", " 'immich-tofu[bot]',\n", " 'github-merge-queue[bot]',\n", " 'udata-bot',\n", " '@r2wc/core[bot]@workflow',\n", " 'cfpa-bot[bot]',\n", " 'reactiveops-bot',\n", " 'neverendingqs-bot[bot]',\n", " 'acl-pwc-bot',\n", " 'jenkins-x-bot-test',\n", " 'loggie-robot',\n", " 'northstar-automation-bot',\n", " 'danez[bot]',\n", " 'patchback[bot]',\n", " 'weaveworks-admin-bot',\n", " 'kubeinit-bot',\n", " 'making-bot',\n", " 'openkraken-bot',\n", " 'java-team-github-bot',\n", " 'alluxio-bot',\n", " 'terraform-aws-provider[bot]',\n", " 'fetch-metadata-action-automation[bot]',\n", " 'jxl-bot',\n", " '<PERSON>[bot]',\n", " 'deepin-admin-bot',\n", " 'typescript-bot',\n", " 'nginx-bot',\n", " 'bpg-autobot[bot]',\n", " 'rikaikun-bot',\n", " 'tracy-client-sys-auto-update[bot]',\n", " 'semgrep-live-local-dev[bot]',\n", " 'lingui-bot',\n", " 'texify[bot]',\n", " 'forge-bot',\n", " 'microcks-bot',\n", " 'NetBox-Bot',\n", " 'iasql-bot',\n", " 'ibmdotcom-bot',\n", " ...]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["bot_developers"]}, {"cell_type": "code", "execution_count": 11, "id": "31aa171c280ee55f", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:54:58.102765Z", "start_time": "2024-11-25T07:54:58.096898Z"}}, "outputs": [], "source": ["# store the bot developers list in a csv file\n", "bot_developers_df = pd.DataFrame(bot_developers, columns=['bot_name'])\n", "bot_developers_df.to_csv('../data/bot_developer_list_original.csv', index=False)"]}, {"cell_type": "code", "execution_count": 12, "id": "ae9f6d172e6e4714", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:55:05.978067Z", "start_time": "2024-11-25T07:55:05.970582Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "bot_name", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "3040d712-bf15-4d32-b06d-f8307f8e1fbe", "rows": [["0", "essos-bot"], ["1", "reunion-maestro[bot]"], ["2", "kie-tools-bot"], ["3", "malem-robot<PERSON>"], ["4", "npm-to-cdn-bot (by <PERSON> Lindesay)"], ["5", "codesee-maps[bot]"], ["6", "codeflash-ai[bot]"], ["7", "scott-robotics"], ["8", "facebook-github-bot-2"], ["9", "HELICS-bot"], ["10", "mindspore-ci-bot"], ["11", "grafana-i18n-bot"], ["12", "dxos-bot"], ["13", "ibis-squawk-bot[bot]"], ["14", "NocWorx-BOT"], ["15", "x6-bot"], ["16", "tas-operability-bot"], ["17", "simple-icons[bot]"], ["18", "Jdaviz-Triage-Bot"], ["19", "consul-version-updater[bot]"], ["20", "TCGdex [Bot]"], ["21", "release-please[bot]"], ["22", "chrome-release-bot"], ["23", "ci.automation[bot]"], ["24", "lego-10-01-06[bot]"], ["25", "dependabot-circleci[bot]"], ["26", "Liss-Bot"], ["27", "serokell-bot"], ["28", "redocly-bot"], ["29", "openshift-merge-bot"], ["30", "mrs<PERSON><PERSON><PERSON>-robot"], ["31", "openpracticelibrary-bot"], ["32", "posthog-bot"], ["33", "servicetalk-bot"], ["34", "gradle-update-robot"], ["35", "orbitprofiler-bot"], ["36", "jsdoc-bot"], ["37", "@r2wc/react-to-web-component[bot]@workflow"], ["38", "tas-runtime-bot"], ["39", "kubeflow-pipeline-bot"], ["40", "dae-bot[bot]"], ["41", "googleforcreators-bot"], ["42", "prettier-toc-me[bot]"], ["43", "godaddy-wordpress-bot"], ["44", "lbry-bot"], ["45", "minio-bot"], ["46", "facebook-github-bot-9"], ["47", "eks-distro-pr-bot"], ["48", "aws-crypto-tools-ci-bot"], ["49", "suzuki-shunsuke-app[bot]"]], "shape": {"columns": 1, "rows": 1777}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bot_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>essos-bot</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>reunion-maestro[bot]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>kie-tools-bot</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>malem-robotiq</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>npm-to-cdn-bot (by <PERSON>)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1772</th>\n", "      <td>soloio-bulldozer[bot]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1773</th>\n", "      <td>bloomberg-oss-ci[bot]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1774</th>\n", "      <td>pyodide-pr-bot</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1775</th>\n", "      <td>alfred-openmined-bot</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1776</th>\n", "      <td>Abhi-bot-dot</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1777 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                                 bot_name\n", "0                               essos-bot\n", "1                    reunion-maestro[bot]\n", "2                           kie-tools-bot\n", "3                           malem-robotiq\n", "4     npm-to-cdn-bot (by <PERSON> Lin<PERSON>)\n", "...                                   ...\n", "1772                soloio-bulldozer[bot]\n", "1773                bloomberg-oss-ci[bot]\n", "1774                       pyodide-pr-bot\n", "1775                 alfred-openmined-bot\n", "1776                         Abhi-bot-dot\n", "\n", "[1777 rows x 1 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["bot_developers_df"]}, {"cell_type": "code", "execution_count": null, "id": "e91deae805d17b70", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:29:33.689205Z", "start_time": "2024-11-25T07:29:33.669074Z"}}, "outputs": [], "source": ["core_developers_df.to_csv('../data/core_developer_list_4000_repo.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "cba20be81b007009", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:28:18.684466Z", "start_time": "2024-11-25T07:28:18.676860Z"}}, "outputs": [{"data": {"text/plain": ["num_core_developers\n", "1      895\n", "2      798\n", "3      591\n", "4      432\n", "5      273\n", "      ... \n", "127      1\n", "109      1\n", "50       1\n", "102      1\n", "150      1\n", "Name: count, Length: 77, dtype: int64"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["core_developers_df['num_core_developers'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "ed583a2727f7d714", "metadata": {}, "outputs": [], "source": ["core_developers_df.sort_values(by='num_core_developers',ascending=False)\n", "# plot the distribution of core developers, no need for x-axis, just show the distribution"]}, {"cell_type": "code", "execution_count": null, "id": "ca5cc91c79ed5594", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T07:16:21.122042Z", "start_time": "2024-11-25T07:16:20.904684Z"}}, "outputs": [{"ename": "ValueError", "evalue": "object __array__ method not producing an array", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/IPython/core/formatters.py:343\u001b[0m, in \u001b[0;36mBaseFormatter.__call__\u001b[0;34m(self, obj)\u001b[0m\n\u001b[1;32m    341\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m    342\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 343\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m printer(obj)\n\u001b[1;32m    344\u001b[0m \u001b[38;5;66;03m# Finally look for special method names\u001b[39;00m\n\u001b[1;32m    345\u001b[0m method \u001b[38;5;241m=\u001b[39m get_real_method(obj, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprint_method)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/IPython/core/pylabtools.py:170\u001b[0m, in \u001b[0;36mprint_figure\u001b[0;34m(fig, fmt, bbox_inches, base64, **kwargs)\u001b[0m\n\u001b[1;32m    167\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend_bases\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FigureCanvasBase\n\u001b[1;32m    168\u001b[0m     FigureCanvasBase(fig)\n\u001b[0;32m--> 170\u001b[0m fig\u001b[38;5;241m.\u001b[39mcanvas\u001b[38;5;241m.\u001b[39mprint_figure(bytes_io, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw)\n\u001b[1;32m    171\u001b[0m data \u001b[38;5;241m=\u001b[39m bytes_io\u001b[38;5;241m.\u001b[39mgetvalue()\n\u001b[1;32m    172\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m fmt \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msvg\u001b[39m\u001b[38;5;124m'\u001b[39m:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/backend_bases.py:2204\u001b[0m, in \u001b[0;36mFigureCanvasBase.print_figure\u001b[0;34m(self, filename, dpi, facecolor, edgecolor, orientation, format, bbox_inches, pad_inches, bbox_extra_artists, backend, **kwargs)\u001b[0m\n\u001b[1;32m   2200\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   2201\u001b[0m     \u001b[38;5;66;03m# _get_renderer may change the figure dpi (as vector formats\u001b[39;00m\n\u001b[1;32m   2202\u001b[0m     \u001b[38;5;66;03m# force the figure dpi to 72), so we need to set it again here.\u001b[39;00m\n\u001b[1;32m   2203\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m cbook\u001b[38;5;241m.\u001b[39m_setattr_cm(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfigure, dpi\u001b[38;5;241m=\u001b[39mdpi):\n\u001b[0;32m-> 2204\u001b[0m         result \u001b[38;5;241m=\u001b[39m print_method(\n\u001b[1;32m   2205\u001b[0m             filename,\n\u001b[1;32m   2206\u001b[0m             facecolor\u001b[38;5;241m=\u001b[39mfacecolor,\n\u001b[1;32m   2207\u001b[0m             edgecolor\u001b[38;5;241m=\u001b[39medgecolor,\n\u001b[1;32m   2208\u001b[0m             orientation\u001b[38;5;241m=\u001b[39morientation,\n\u001b[1;32m   2209\u001b[0m             bbox_inches_restore\u001b[38;5;241m=\u001b[39m_bbox_inches_restore,\n\u001b[1;32m   2210\u001b[0m             \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m   2211\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m   2212\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m bbox_inches \u001b[38;5;129;01mand\u001b[39;00m restore_bbox:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/backend_bases.py:2054\u001b[0m, in \u001b[0;36mFigureCanvasBase._switch_canvas_and_return_print_method.<locals>.<lambda>\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m   2050\u001b[0m     optional_kws \u001b[38;5;241m=\u001b[39m {  \u001b[38;5;66;03m# Passed by print_figure for other renderers.\u001b[39;00m\n\u001b[1;32m   2051\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdpi\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfacecolor\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124medgecolor\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124morientation\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   2052\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbbox_inches_restore\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[1;32m   2053\u001b[0m     skip \u001b[38;5;241m=\u001b[39m optional_kws \u001b[38;5;241m-\u001b[39m {\u001b[38;5;241m*\u001b[39minspect\u001b[38;5;241m.\u001b[39msignature(meth)\u001b[38;5;241m.\u001b[39mparameters}\n\u001b[0;32m-> 2054\u001b[0m     print_method \u001b[38;5;241m=\u001b[39m functools\u001b[38;5;241m.\u001b[39mwraps(meth)(\u001b[38;5;28;01mlambda\u001b[39;00m \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: meth(\n\u001b[1;32m   2055\u001b[0m         \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m{k: v \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m kwargs\u001b[38;5;241m.\u001b[39mitems() \u001b[38;5;28;01mif\u001b[39;00m k \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m skip}))\n\u001b[1;32m   2056\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:  \u001b[38;5;66;03m# Let third-parties do as they see fit.\u001b[39;00m\n\u001b[1;32m   2057\u001b[0m     print_method \u001b[38;5;241m=\u001b[39m meth\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/backends/backend_agg.py:496\u001b[0m, in \u001b[0;36mFigureCanvasAgg.print_png\u001b[0;34m(self, filename_or_obj, metadata, pil_kwargs)\u001b[0m\n\u001b[1;32m    449\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mprint_png\u001b[39m(\u001b[38;5;28mself\u001b[39m, filename_or_obj, \u001b[38;5;241m*\u001b[39m, metadata\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, pil_kwargs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    450\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    451\u001b[0m \u001b[38;5;124;03m    Write the figure to a PNG file.\u001b[39;00m\n\u001b[1;32m    452\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    494\u001b[0m \u001b[38;5;124;03m        *metadata*, including the default 'Software' key.\u001b[39;00m\n\u001b[1;32m    495\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 496\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_print_pil(filename_or_obj, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpng\u001b[39m\u001b[38;5;124m\"\u001b[39m, pil_kwargs, metadata)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/backends/backend_agg.py:444\u001b[0m, in \u001b[0;36mFigureCanvasAgg._print_pil\u001b[0;34m(self, filename_or_obj, fmt, pil_kwargs, metadata)\u001b[0m\n\u001b[1;32m    439\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_print_pil\u001b[39m(\u001b[38;5;28mself\u001b[39m, filename_or_obj, fmt, pil_kwargs, metadata\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    440\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    441\u001b[0m \u001b[38;5;124;03m    Draw the canvas, then save it using `.image.imsave` (to which\u001b[39;00m\n\u001b[1;32m    442\u001b[0m \u001b[38;5;124;03m    *pil_kwargs* and *metadata* are forwarded).\u001b[39;00m\n\u001b[1;32m    443\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 444\u001b[0m     FigureCanvasAgg\u001b[38;5;241m.\u001b[39mdraw(\u001b[38;5;28mself\u001b[39m)\n\u001b[1;32m    445\u001b[0m     mpl\u001b[38;5;241m.\u001b[39mimage\u001b[38;5;241m.\u001b[39mimsave(\n\u001b[1;32m    446\u001b[0m         filename_or_obj, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuffer_rgba(), \u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m=\u001b[39mfmt, origin\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mupper\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    447\u001b[0m         dpi\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfigure\u001b[38;5;241m.\u001b[39mdpi, metadata\u001b[38;5;241m=\u001b[39mmetadata, pil_kwargs\u001b[38;5;241m=\u001b[39mpil_kwargs)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/backends/backend_agg.py:387\u001b[0m, in \u001b[0;36mFigureCanvasAgg.draw\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    384\u001b[0m \u001b[38;5;66;03m# Acquire a lock on the shared font cache.\u001b[39;00m\n\u001b[1;32m    385\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtoolbar\u001b[38;5;241m.\u001b[39m_wait_cursor_for_draw_cm() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtoolbar\n\u001b[1;32m    386\u001b[0m       \u001b[38;5;28;01melse\u001b[39;00m nullcontext()):\n\u001b[0;32m--> 387\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfigure\u001b[38;5;241m.\u001b[39mdraw(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrenderer)\n\u001b[1;32m    388\u001b[0m     \u001b[38;5;66;03m# A GUI class may be need to update a window using this draw, so\u001b[39;00m\n\u001b[1;32m    389\u001b[0m     \u001b[38;5;66;03m# don't forget to call the superclass.\u001b[39;00m\n\u001b[1;32m    390\u001b[0m     \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mdraw()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/artist.py:95\u001b[0m, in \u001b[0;36m_finalize_rasterization.<locals>.draw_wrapper\u001b[0;34m(artist, renderer, *args, **kwargs)\u001b[0m\n\u001b[1;32m     93\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(draw)\n\u001b[1;32m     94\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdraw_wrapper\u001b[39m(artist, renderer, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m---> 95\u001b[0m     result \u001b[38;5;241m=\u001b[39m draw(artist, renderer, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m renderer\u001b[38;5;241m.\u001b[39m_rasterizing:\n\u001b[1;32m     97\u001b[0m         renderer\u001b[38;5;241m.\u001b[39mstop_rasterizing()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/artist.py:72\u001b[0m, in \u001b[0;36mallow_rasterization.<locals>.draw_wrapper\u001b[0;34m(artist, renderer)\u001b[0m\n\u001b[1;32m     69\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m artist\u001b[38;5;241m.\u001b[39mget_agg_filter() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m     70\u001b[0m         renderer\u001b[38;5;241m.\u001b[39mstart_filter()\n\u001b[0;32m---> 72\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m draw(artist, renderer)\n\u001b[1;32m     73\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m     74\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m artist\u001b[38;5;241m.\u001b[39mget_agg_filter() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/figure.py:3161\u001b[0m, in \u001b[0;36mFigure.draw\u001b[0;34m(self, renderer)\u001b[0m\n\u001b[1;32m   3158\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m   3159\u001b[0m         \u001b[38;5;66;03m# ValueError can occur when resizing a window.\u001b[39;00m\n\u001b[0;32m-> 3161\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpatch\u001b[38;5;241m.\u001b[39mdraw(renderer)\n\u001b[1;32m   3162\u001b[0m mimage\u001b[38;5;241m.\u001b[39m_draw_list_compositing_images(\n\u001b[1;32m   3163\u001b[0m     renderer, \u001b[38;5;28mself\u001b[39m, artists, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msuppressComposite)\n\u001b[1;32m   3165\u001b[0m renderer\u001b[38;5;241m.\u001b[39mclose_group(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfigure\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/artist.py:72\u001b[0m, in \u001b[0;36mallow_rasterization.<locals>.draw_wrapper\u001b[0;34m(artist, renderer)\u001b[0m\n\u001b[1;32m     69\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m artist\u001b[38;5;241m.\u001b[39mget_agg_filter() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m     70\u001b[0m         renderer\u001b[38;5;241m.\u001b[39mstart_filter()\n\u001b[0;32m---> 72\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m draw(artist, renderer)\n\u001b[1;32m     73\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m     74\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m artist\u001b[38;5;241m.\u001b[39mget_agg_filter() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/patches.py:632\u001b[0m, in \u001b[0;36mPatch.draw\u001b[0;34m(self, renderer)\u001b[0m\n\u001b[1;32m    630\u001b[0m tpath \u001b[38;5;241m=\u001b[39m transform\u001b[38;5;241m.\u001b[39mtransform_path_non_affine(path)\n\u001b[1;32m    631\u001b[0m affine \u001b[38;5;241m=\u001b[39m transform\u001b[38;5;241m.\u001b[39mget_affine()\n\u001b[0;32m--> 632\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_draw_paths_with_artist_properties(\n\u001b[1;32m    633\u001b[0m     renderer,\n\u001b[1;32m    634\u001b[0m     [(tpath, affine,\n\u001b[1;32m    635\u001b[0m       \u001b[38;5;66;03m# Work around a bug in the PDF and SVG renderers, which\u001b[39;00m\n\u001b[1;32m    636\u001b[0m       \u001b[38;5;66;03m# do not draw the hatches if the facecolor is fully\u001b[39;00m\n\u001b[1;32m    637\u001b[0m       \u001b[38;5;66;03m# transparent, but do if it is None.\u001b[39;00m\n\u001b[1;32m    638\u001b[0m       \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_facecolor \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_facecolor[\u001b[38;5;241m3\u001b[39m] \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m)])\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/patches.py:617\u001b[0m, in \u001b[0;36mPatch._draw_paths_with_artist_properties\u001b[0;34m(self, renderer, draw_path_args_list)\u001b[0m\n\u001b[1;32m    614\u001b[0m     renderer \u001b[38;5;241m=\u001b[39m PathEffectRenderer(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_path_effects(), renderer)\n\u001b[1;32m    616\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m draw_path_args \u001b[38;5;129;01min\u001b[39;00m draw_path_args_list:\n\u001b[0;32m--> 617\u001b[0m     renderer\u001b[38;5;241m.\u001b[39mdraw_path(gc, \u001b[38;5;241m*\u001b[39mdraw_path_args)\n\u001b[1;32m    619\u001b[0m gc\u001b[38;5;241m.\u001b[39mrestore()\n\u001b[1;32m    620\u001b[0m renderer\u001b[38;5;241m.\u001b[39mclose_group(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpatch\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/matplotlib/backends/backend_agg.py:131\u001b[0m, in \u001b[0;36mRendererAgg.draw_path\u001b[0;34m(self, gc, path, transform, rgbFace)\u001b[0m\n\u001b[1;32m    129\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    130\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 131\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_renderer\u001b[38;5;241m.\u001b[39mdraw_path(gc, path, transform, rgbFace)\n\u001b[1;32m    132\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOverflowError\u001b[39;00m:\n\u001b[1;32m    133\u001b[0m         cant_chunk \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "\u001b[0;31mValueError\u001b[0m: object __array__ method not producing an array"]}, {"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Ensure 'num_core_developers' is a valid Pandas Series\n", "num_core_developers = core_developers_df['num_core_developers']\n", "\n", "# Convert to a NumPy array if necessary\n", "num_core_developers_array = num_core_developers.to_numpy()\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.histplot(num_core_developers_array, bins=20, kde=True)\n", "plt.title(\"Distribution of Core Developers\")\n", "plt.xlabel(\"Number of Core Developers\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8578b4275864144a", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T09:57:03.685577Z", "start_time": "2024-11-25T09:57:03.664823Z"}}, "outputs": [{"data": {"text/plain": ["['bignerdranch/expandable-recycler-view']"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fadb534356f49c6d", "metadata": {}, "outputs": [], "source": ["exclude_repo = \n", "[\n", "    'reactjs/zh-hans.reactjs.org',\n", "    \n", "]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}