{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# Sampling Projects from Github\n", "**Criteria**:\n", "- created before 2023-11-01 ( for at least a year of development)\n", "- at least 100 stars\n", "- not fork projects\n", "- has license\n", "- has open issues\n", "- has pull requests\n", "- has over 10 contributors\n", "- over 100 commits\n", "\n", "*Stratified Sampling based on programming languages*\n", "\n", "**Programming Languages**:\n", "- Python\n", "- JavaScript\n", "- Java\n", "- TypeScript\n", "- C++\n", "- Go\n", "- C#\n", "- C\n", "- PHP\n", "- Rust\n", "\n", "**Exclusion** ( after sampling, only maintain code projects) :\n", "- exclude projects with keywords like 'homework', 'assignments', 'course'\n", "- remove projects starting with 'awesome-\""], "id": "22a7f46a8e3f709e"}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:57:32.623123Z", "start_time": "2025-01-13T07:57:30.885972Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "from scipy.stats import norm\n", "\n", "programming_languages = ['Python','JavaScript', 'Java', 'TypeScript', 'C++', 'Go', 'C#', 'C', 'PHP','Rust']\n", "sample_projects = pd.read_csv('../data/raw_data_sampling/results.csv')"], "id": "f9d3ce06af37142d", "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:57:34.757699Z", "start_time": "2025-01-13T07:57:34.651488Z"}}, "cell_type": "code", "source": "sample_projects.describe()", "id": "cb904f964b933997", "outputs": [{"data": {"text/plain": ["                 id        commits      branches      releases         forks  \\\n", "count  6.448200e+04   64482.000000  64482.000000  64482.000000  64482.000000   \n", "mean   2.950285e+07    2413.387395     29.568174     37.754908    387.335458   \n", "std    3.477351e+07   11832.281297    148.441639    101.621644   1486.921666   \n", "min    0.000000e+00     100.000000      1.000000      0.000000      0.000000   \n", "25%    3.947180e+06     301.000000      3.000000      0.000000     57.000000   \n", "50%    6.932825e+06     675.000000      8.000000     12.000000    116.000000   \n", "75%    6.902443e+07    1748.000000     21.000000     37.000000    282.750000   \n", "max    9.522477e+07  987758.000000  21504.000000  10066.000000  78879.000000   \n", "\n", "           watchers     stargazers  contributors          size    totalIssues  \\\n", "count  64482.000000   64482.000000  64482.000000  6.448200e+04   64482.000000   \n", "mean      64.414286    2170.624190     51.113644  6.789857e+04     476.453026   \n", "std      170.671054    6699.620949     78.187305  3.396216e+05    1758.398467   \n", "min        0.000000     100.000000     10.000000  4.400000e+01       1.000000   \n", "25%       14.000000     232.000000     16.000000  1.383000e+03      66.000000   \n", "50%       27.000000     552.000000     26.000000  6.675500e+03     149.000000   \n", "75%       57.000000    1640.000000     52.000000  3.372575e+04     374.000000   \n", "max     8552.000000  405316.000000   6777.000000  1.588222e+07  185909.000000   \n", "\n", "         openIssues  totalPullRequests  openPullRequests    blankLines  \\\n", "count  64482.000000       64482.000000      64482.000000  6.446200e+04   \n", "mean      83.371421         635.119615         14.089793  1.795844e+04   \n", "std      291.649527        2811.504387         62.729060  1.057696e+05   \n", "min        1.000000           1.000000          1.000000  0.000000e+00   \n", "25%       12.000000          68.000000          2.000000  8.542500e+02   \n", "50%       30.000000         164.000000          6.000000  2.731000e+03   \n", "75%       74.000000         476.000000         13.000000  9.543500e+03   \n", "max    30586.000000      310325.000000       6333.000000  1.122867e+07   \n", "\n", "          codeLines  commentLines  \n", "count  6.446200e+04  6.446200e+04  \n", "mean   1.619035e+05  2.553725e+04  \n", "std    1.092778e+06  2.102468e+05  \n", "min    1.000000e+00  0.000000e+00  \n", "25%    5.881000e+03  4.250000e+02  \n", "50%    2.034950e+04  1.884500e+03  \n", "75%    7.469275e+04  8.817750e+03  \n", "max    1.420817e+08  1.279447e+07  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>watchers</th>\n", "      <th>stargazers</th>\n", "      <th>contributors</th>\n", "      <th>size</th>\n", "      <th>totalIssues</th>\n", "      <th>openIssues</th>\n", "      <th>totalPullRequests</th>\n", "      <th>openPullRequests</th>\n", "      <th>blankLines</th>\n", "      <th>codeLines</th>\n", "      <th>commentLines</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>6.448200e+04</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>6.448200e+04</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>64482.000000</td>\n", "      <td>6.446200e+04</td>\n", "      <td>6.446200e+04</td>\n", "      <td>6.446200e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.950285e+07</td>\n", "      <td>2413.387395</td>\n", "      <td>29.568174</td>\n", "      <td>37.754908</td>\n", "      <td>387.335458</td>\n", "      <td>64.414286</td>\n", "      <td>2170.624190</td>\n", "      <td>51.113644</td>\n", "      <td>6.789857e+04</td>\n", "      <td>476.453026</td>\n", "      <td>83.371421</td>\n", "      <td>635.119615</td>\n", "      <td>14.089793</td>\n", "      <td>1.795844e+04</td>\n", "      <td>1.619035e+05</td>\n", "      <td>2.553725e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.477351e+07</td>\n", "      <td>11832.281297</td>\n", "      <td>148.441639</td>\n", "      <td>101.621644</td>\n", "      <td>1486.921666</td>\n", "      <td>170.671054</td>\n", "      <td>6699.620949</td>\n", "      <td>78.187305</td>\n", "      <td>3.396216e+05</td>\n", "      <td>1758.398467</td>\n", "      <td>291.649527</td>\n", "      <td>2811.504387</td>\n", "      <td>62.729060</td>\n", "      <td>1.057696e+05</td>\n", "      <td>1.092778e+06</td>\n", "      <td>2.102468e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000e+00</td>\n", "      <td>100.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>100.000000</td>\n", "      <td>10.000000</td>\n", "      <td>4.400000e+01</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3.947180e+06</td>\n", "      <td>301.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>57.000000</td>\n", "      <td>14.000000</td>\n", "      <td>232.000000</td>\n", "      <td>16.000000</td>\n", "      <td>1.383000e+03</td>\n", "      <td>66.000000</td>\n", "      <td>12.000000</td>\n", "      <td>68.000000</td>\n", "      <td>2.000000</td>\n", "      <td>8.542500e+02</td>\n", "      <td>5.881000e+03</td>\n", "      <td>4.250000e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.932825e+06</td>\n", "      <td>675.000000</td>\n", "      <td>8.000000</td>\n", "      <td>12.000000</td>\n", "      <td>116.000000</td>\n", "      <td>27.000000</td>\n", "      <td>552.000000</td>\n", "      <td>26.000000</td>\n", "      <td>6.675500e+03</td>\n", "      <td>149.000000</td>\n", "      <td>30.000000</td>\n", "      <td>164.000000</td>\n", "      <td>6.000000</td>\n", "      <td>2.731000e+03</td>\n", "      <td>2.034950e+04</td>\n", "      <td>1.884500e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>6.902443e+07</td>\n", "      <td>1748.000000</td>\n", "      <td>21.000000</td>\n", "      <td>37.000000</td>\n", "      <td>282.750000</td>\n", "      <td>57.000000</td>\n", "      <td>1640.000000</td>\n", "      <td>52.000000</td>\n", "      <td>3.372575e+04</td>\n", "      <td>374.000000</td>\n", "      <td>74.000000</td>\n", "      <td>476.000000</td>\n", "      <td>13.000000</td>\n", "      <td>9.543500e+03</td>\n", "      <td>7.469275e+04</td>\n", "      <td>8.817750e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.522477e+07</td>\n", "      <td>987758.000000</td>\n", "      <td>21504.000000</td>\n", "      <td>10066.000000</td>\n", "      <td>78879.000000</td>\n", "      <td>8552.000000</td>\n", "      <td>405316.000000</td>\n", "      <td>6777.000000</td>\n", "      <td>1.588222e+07</td>\n", "      <td>185909.000000</td>\n", "      <td>30586.000000</td>\n", "      <td>310325.000000</td>\n", "      <td>6333.000000</td>\n", "      <td>1.122867e+07</td>\n", "      <td>1.420817e+08</td>\n", "      <td>1.279447e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:57:46.201143Z", "start_time": "2025-01-13T07:57:46.131138Z"}}, "cell_type": "code", "source": ["# only maintain projects with mainLanguage in programming_languages\n", "sample_projects = sample_projects[sample_projects['mainLanguage'].isin(programming_languages)]\n", "sample_projects.describe()"], "id": "d69f2b11634e21a5", "outputs": [{"data": {"text/plain": ["                 id        commits      branches      releases         forks  \\\n", "count  5.253700e+04   52537.000000  52537.000000  52537.000000  52537.000000   \n", "mean   2.342031e+07    2518.094295     30.636980     40.177018    402.027010   \n", "std    3.089339e+07   11990.234847    128.202466    106.096876   1567.876453   \n", "min    0.000000e+00     100.000000      1.000000      0.000000      0.000000   \n", "25%    3.926450e+06     309.000000      4.000000      1.000000     58.000000   \n", "50%    6.308873e+06     703.000000      8.000000     14.000000    118.000000   \n", "75%    2.501094e+07    1829.000000     22.000000     40.000000    291.000000   \n", "max    9.522477e+07  987758.000000   9106.000000  10066.000000  78879.000000   \n", "\n", "           watchers     stargazers  contributors          size    totalIssues  \\\n", "count  52537.000000   52537.000000  52537.000000  5.253700e+04   52537.000000   \n", "mean      66.139159    2276.315511     52.011649  6.780197e+04     502.228068   \n", "std      178.155023    7041.433228     81.294591  3.223758e+05    1821.798434   \n", "min        0.000000     100.000000     10.000000  5.100000e+01       1.000000   \n", "25%       14.000000     234.000000     16.000000  1.572000e+03      69.000000   \n", "50%       27.000000     564.000000     26.000000  7.348000e+03     156.000000   \n", "75%       59.000000    1705.000000     52.000000  3.534100e+04     394.000000   \n", "max     8552.000000  405316.000000   6777.000000  1.588222e+07  185909.000000   \n", "\n", "         openIssues  totalPullRequests  openPullRequests    blankLines  \\\n", "count  52537.000000       52537.000000      52537.000000  5.251900e+04   \n", "mean      87.434913         657.591374         14.783848  2.015020e+04   \n", "std      300.366052        2366.326498         60.606219  1.153671e+05   \n", "min        1.000000           1.000000          1.000000  0.000000e+00   \n", "25%       13.000000          71.000000          2.000000  9.600000e+02   \n", "50%       31.000000         173.000000          6.000000  3.156000e+03   \n", "75%       78.000000         510.000000         14.000000  1.103450e+04   \n", "max    30586.000000      167323.000000       4556.000000  1.122867e+07   \n", "\n", "          codeLines  commentLines  \n", "count  5.251900e+04  5.251900e+04  \n", "mean   1.782675e+05  2.832153e+04  \n", "std    1.185411e+06  2.290918e+05  \n", "min    1.000000e+00  0.000000e+00  \n", "25%    7.003000e+03  4.860000e+02  \n", "50%    2.392700e+04  2.195000e+03  \n", "75%    8.567850e+04  1.016100e+04  \n", "max    1.420817e+08  1.279447e+07  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>watchers</th>\n", "      <th>stargazers</th>\n", "      <th>contributors</th>\n", "      <th>size</th>\n", "      <th>totalIssues</th>\n", "      <th>openIssues</th>\n", "      <th>totalPullRequests</th>\n", "      <th>openPullRequests</th>\n", "      <th>blankLines</th>\n", "      <th>codeLines</th>\n", "      <th>commentLines</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>5.253700e+04</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>5.253700e+04</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>52537.000000</td>\n", "      <td>5.251900e+04</td>\n", "      <td>5.251900e+04</td>\n", "      <td>5.251900e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.342031e+07</td>\n", "      <td>2518.094295</td>\n", "      <td>30.636980</td>\n", "      <td>40.177018</td>\n", "      <td>402.027010</td>\n", "      <td>66.139159</td>\n", "      <td>2276.315511</td>\n", "      <td>52.011649</td>\n", "      <td>6.780197e+04</td>\n", "      <td>502.228068</td>\n", "      <td>87.434913</td>\n", "      <td>657.591374</td>\n", "      <td>14.783848</td>\n", "      <td>2.015020e+04</td>\n", "      <td>1.782675e+05</td>\n", "      <td>2.832153e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.089339e+07</td>\n", "      <td>11990.234847</td>\n", "      <td>128.202466</td>\n", "      <td>106.096876</td>\n", "      <td>1567.876453</td>\n", "      <td>178.155023</td>\n", "      <td>7041.433228</td>\n", "      <td>81.294591</td>\n", "      <td>3.223758e+05</td>\n", "      <td>1821.798434</td>\n", "      <td>300.366052</td>\n", "      <td>2366.326498</td>\n", "      <td>60.606219</td>\n", "      <td>1.153671e+05</td>\n", "      <td>1.185411e+06</td>\n", "      <td>2.290918e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000e+00</td>\n", "      <td>100.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>100.000000</td>\n", "      <td>10.000000</td>\n", "      <td>5.100000e+01</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3.926450e+06</td>\n", "      <td>309.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>58.000000</td>\n", "      <td>14.000000</td>\n", "      <td>234.000000</td>\n", "      <td>16.000000</td>\n", "      <td>1.572000e+03</td>\n", "      <td>69.000000</td>\n", "      <td>13.000000</td>\n", "      <td>71.000000</td>\n", "      <td>2.000000</td>\n", "      <td>9.600000e+02</td>\n", "      <td>7.003000e+03</td>\n", "      <td>4.860000e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.308873e+06</td>\n", "      <td>703.000000</td>\n", "      <td>8.000000</td>\n", "      <td>14.000000</td>\n", "      <td>118.000000</td>\n", "      <td>27.000000</td>\n", "      <td>564.000000</td>\n", "      <td>26.000000</td>\n", "      <td>7.348000e+03</td>\n", "      <td>156.000000</td>\n", "      <td>31.000000</td>\n", "      <td>173.000000</td>\n", "      <td>6.000000</td>\n", "      <td>3.156000e+03</td>\n", "      <td>2.392700e+04</td>\n", "      <td>2.195000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.501094e+07</td>\n", "      <td>1829.000000</td>\n", "      <td>22.000000</td>\n", "      <td>40.000000</td>\n", "      <td>291.000000</td>\n", "      <td>59.000000</td>\n", "      <td>1705.000000</td>\n", "      <td>52.000000</td>\n", "      <td>3.534100e+04</td>\n", "      <td>394.000000</td>\n", "      <td>78.000000</td>\n", "      <td>510.000000</td>\n", "      <td>14.000000</td>\n", "      <td>1.103450e+04</td>\n", "      <td>8.567850e+04</td>\n", "      <td>1.016100e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.522477e+07</td>\n", "      <td>987758.000000</td>\n", "      <td>9106.000000</td>\n", "      <td>10066.000000</td>\n", "      <td>78879.000000</td>\n", "      <td>8552.000000</td>\n", "      <td>405316.000000</td>\n", "      <td>6777.000000</td>\n", "      <td>1.588222e+07</td>\n", "      <td>185909.000000</td>\n", "      <td>30586.000000</td>\n", "      <td>167323.000000</td>\n", "      <td>4556.000000</td>\n", "      <td>1.122867e+07</td>\n", "      <td>1.420817e+08</td>\n", "      <td>1.279447e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:57:54.017835Z", "start_time": "2025-01-13T07:57:54.008939Z"}}, "cell_type": "code", "source": "sample_projects.value_counts('mainLanguage')", "id": "207881ab89e4e84b", "outputs": [{"data": {"text/plain": ["mainLanguage\n", "Python        10961\n", "JavaScript     9857\n", "TypeScript     7090\n", "Go             5100\n", "Java           4358\n", "C++            4132\n", "Rust           3056\n", "C              2895\n", "PHP            2687\n", "C#             2401\n", "Name: count, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:58:05.345434Z", "start_time": "2025-01-13T07:58:05.246629Z"}}, "cell_type": "code", "source": ["# exclude projects with keywords like 'homework', 'assignments', 'course'\n", "# remove projects starting with 'awesome-\" after '/'\n", "\n", "# Filter out rows where the second part of the 'name' column contains specific keywords\n", "keywords = 'homework|assignments|course|tutorial|questions|leetcode|guide|beginner|algorithms|lecture|template|example|awesome'\n", "sample_projects = sample_projects[~sample_projects['name'].apply(lambda x: x.split('/')[1].lower()).str.contains(keywords)]\n", "\n", "\n", "# exclude project that 'last_commits' - 'created_at' < 365 days\n", "sample_projects['createdAt'] = pd.to_datetime(sample_projects['createdAt'])\n", "sample_projects['lastCommit'] = pd.to_datetime(sample_projects['lastCommit'])\n", "sample_projects['duration'] = sample_projects['lastCommit'] - sample_projects['createdAt']\n", "sample_projects = sample_projects[sample_projects['duration'] > pd.Timedelta(days=365)]\n", "sample_projects.value_counts('mainLanguage')\n", "\n", "# possible examination exclusion after filtering\n", "# 1. samples\n", "# 2. books"], "id": "f3ab811629cb5789", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_133843/1808643252.py:10: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sample_projects['createdAt'] = pd.to_datetime(sample_projects['createdAt'])\n", "/tmp/ipykernel_133843/1808643252.py:11: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sample_projects['lastCommit'] = pd.to_datetime(sample_projects['lastCommit'])\n", "/tmp/ipykernel_133843/1808643252.py:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  sample_projects['duration'] = sample_projects['lastCommit'] - sample_projects['createdAt']\n"]}, {"data": {"text/plain": ["mainLanguage\n", "Python        10575\n", "JavaScript     9569\n", "TypeScript     6839\n", "Go             5013\n", "Java           4257\n", "C++            4032\n", "Rust           2984\n", "C              2860\n", "PHP            2667\n", "C#             2362\n", "Name: count, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:58:10.826407Z", "start_time": "2025-01-13T07:58:10.768103Z"}}, "cell_type": "code", "source": "sample_projects.describe()", "id": "50e0b9e0b519cbea", "outputs": [{"data": {"text/plain": ["                 id        commits      branches      releases         forks  \\\n", "count  5.115800e+04   51158.000000  51158.000000  51158.000000  51158.000000   \n", "mean   2.294147e+07    2536.234411     30.911255     40.886978    396.448610   \n", "min    0.000000e+00     100.000000      1.000000      0.000000      0.000000   \n", "25%    3.925284e+06     314.000000      4.000000      1.000000     58.000000   \n", "50%    6.265502e+06     715.000000      9.000000     14.000000    119.000000   \n", "75%    2.120388e+07    1860.000000     22.000000     40.000000    290.000000   \n", "max    9.522477e+07  987758.000000   9106.000000  10066.000000  78879.000000   \n", "std    3.051503e+07   11947.490797    129.491587    107.148203   1520.201889   \n", "\n", "           watchers     stargazers  contributors          size  \\\n", "count  51158.000000   51158.000000  51158.000000  5.115800e+04   \n", "mean      65.880566    2259.145490     52.118378  6.817073e+04   \n", "min        0.000000     100.000000     10.000000  5.100000e+01   \n", "25%       14.000000     235.000000     16.000000  1.571000e+03   \n", "50%       28.000000     566.000000     26.500000  7.360000e+03   \n", "75%       59.000000    1703.000000     53.000000  3.554150e+04   \n", "max     8552.000000  405316.000000   6777.000000  1.588222e+07   \n", "std      175.295839    6910.460267     79.322715  3.204384e+05   \n", "\n", "                           createdAt    totalIssues    openIssues  \\\n", "count                          51158   51158.000000  51158.000000   \n", "mean   2017-02-21 01:28:10.230364928     511.697310     88.752727   \n", "min              2008-01-12 04:46:52       1.000000      1.000000   \n", "25%              2014-09-23 08:05:45      71.000000     13.000000   \n", "50%       2017-03-06 04:34:57.500000     159.000000     32.000000   \n", "75%    2019-09-14 12:41:35.750000128     403.000000     79.000000   \n", "max              2023-10-29 08:49:50  185909.000000  30586.000000   \n", "std                              NaN    1844.592457    304.079997   \n", "\n", "       totalPullRequests  openPullRequests    blankLines     codeLines  \\\n", "count       51158.000000      51158.000000  5.114000e+04  5.114000e+04   \n", "mean          668.216584         14.804879  2.029165e+04  1.793839e+05   \n", "min             1.000000          1.000000  0.000000e+00  1.000000e+00   \n", "25%            72.000000          2.000000  9.700000e+02  7.035750e+03   \n", "50%           175.000000          6.000000  3.194000e+03  2.408150e+04   \n", "75%           519.000000         14.000000  1.117725e+04  8.660075e+04   \n", "max        167323.000000       4556.000000  1.122867e+07  1.420817e+08   \n", "std          2394.258699         61.065556  1.160959e+05  1.194303e+06   \n", "\n", "       commentLines                     lastCommit  \\\n", "count  5.114000e+04                          51158   \n", "mean   2.862357e+04  2023-07-22 12:49:37.786563072   \n", "min    0.000000e+00            2011-03-20 03:16:15   \n", "25%    4.960000e+02  2022-12-16 05:05:25.249999872   \n", "50%    2.238000e+03            2024-08-04 09:10:57   \n", "75%    1.035800e+04  2024-11-04 01:04:23.750000128   \n", "max    1.279447e+07            2024-11-13 12:59:51   \n", "std    2.315255e+05                            NaN   \n", "\n", "                           duration  \n", "count                         51158  \n", "mean   2342 days 11:21:27.556198464  \n", "min               365 days 03:26:37  \n", "25%       1436 days 22:02:01.750000  \n", "50%              2210 days 22:25:01  \n", "75%       3158 days 23:12:38.750000  \n", "max              6072 days 20:40:24  \n", "std    1157 days 01:53:22.314503104  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>watchers</th>\n", "      <th>stargazers</th>\n", "      <th>contributors</th>\n", "      <th>size</th>\n", "      <th>createdAt</th>\n", "      <th>totalIssues</th>\n", "      <th>openIssues</th>\n", "      <th>totalPullRequests</th>\n", "      <th>openPullRequests</th>\n", "      <th>blankLines</th>\n", "      <th>codeLines</th>\n", "      <th>commentLines</th>\n", "      <th>lastCommit</th>\n", "      <th>duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>5.115800e+04</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>5.115800e+04</td>\n", "      <td>51158</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>51158.000000</td>\n", "      <td>5.114000e+04</td>\n", "      <td>5.114000e+04</td>\n", "      <td>5.114000e+04</td>\n", "      <td>51158</td>\n", "      <td>51158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.294147e+07</td>\n", "      <td>2536.234411</td>\n", "      <td>30.911255</td>\n", "      <td>40.886978</td>\n", "      <td>396.448610</td>\n", "      <td>65.880566</td>\n", "      <td>2259.145490</td>\n", "      <td>52.118378</td>\n", "      <td>6.817073e+04</td>\n", "      <td>2017-02-21 01:28:10.230364928</td>\n", "      <td>511.697310</td>\n", "      <td>88.752727</td>\n", "      <td>668.216584</td>\n", "      <td>14.804879</td>\n", "      <td>2.029165e+04</td>\n", "      <td>1.793839e+05</td>\n", "      <td>2.862357e+04</td>\n", "      <td>2023-07-22 12:49:37.786563072</td>\n", "      <td>2342 days 11:21:27.556198464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000e+00</td>\n", "      <td>100.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>100.000000</td>\n", "      <td>10.000000</td>\n", "      <td>5.100000e+01</td>\n", "      <td>2008-01-12 04:46:52</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>2011-03-20 03:16:15</td>\n", "      <td>365 days 03:26:37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3.925284e+06</td>\n", "      <td>314.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>58.000000</td>\n", "      <td>14.000000</td>\n", "      <td>235.000000</td>\n", "      <td>16.000000</td>\n", "      <td>1.571000e+03</td>\n", "      <td>2014-09-23 08:05:45</td>\n", "      <td>71.000000</td>\n", "      <td>13.000000</td>\n", "      <td>72.000000</td>\n", "      <td>2.000000</td>\n", "      <td>9.700000e+02</td>\n", "      <td>7.035750e+03</td>\n", "      <td>4.960000e+02</td>\n", "      <td>2022-12-16 05:05:25.249999872</td>\n", "      <td>1436 days 22:02:01.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.265502e+06</td>\n", "      <td>715.000000</td>\n", "      <td>9.000000</td>\n", "      <td>14.000000</td>\n", "      <td>119.000000</td>\n", "      <td>28.000000</td>\n", "      <td>566.000000</td>\n", "      <td>26.500000</td>\n", "      <td>7.360000e+03</td>\n", "      <td>2017-03-06 04:34:57.500000</td>\n", "      <td>159.000000</td>\n", "      <td>32.000000</td>\n", "      <td>175.000000</td>\n", "      <td>6.000000</td>\n", "      <td>3.194000e+03</td>\n", "      <td>2.408150e+04</td>\n", "      <td>2.238000e+03</td>\n", "      <td>2024-08-04 09:10:57</td>\n", "      <td>2210 days 22:25:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.120388e+07</td>\n", "      <td>1860.000000</td>\n", "      <td>22.000000</td>\n", "      <td>40.000000</td>\n", "      <td>290.000000</td>\n", "      <td>59.000000</td>\n", "      <td>1703.000000</td>\n", "      <td>53.000000</td>\n", "      <td>3.554150e+04</td>\n", "      <td>2019-09-14 12:41:35.750000128</td>\n", "      <td>403.000000</td>\n", "      <td>79.000000</td>\n", "      <td>519.000000</td>\n", "      <td>14.000000</td>\n", "      <td>1.117725e+04</td>\n", "      <td>8.660075e+04</td>\n", "      <td>1.035800e+04</td>\n", "      <td>2024-11-04 01:04:23.750000128</td>\n", "      <td>3158 days 23:12:38.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.522477e+07</td>\n", "      <td>987758.000000</td>\n", "      <td>9106.000000</td>\n", "      <td>10066.000000</td>\n", "      <td>78879.000000</td>\n", "      <td>8552.000000</td>\n", "      <td>405316.000000</td>\n", "      <td>6777.000000</td>\n", "      <td>1.588222e+07</td>\n", "      <td>2023-10-29 08:49:50</td>\n", "      <td>185909.000000</td>\n", "      <td>30586.000000</td>\n", "      <td>167323.000000</td>\n", "      <td>4556.000000</td>\n", "      <td>1.122867e+07</td>\n", "      <td>1.420817e+08</td>\n", "      <td>1.279447e+07</td>\n", "      <td>2024-11-13 12:59:51</td>\n", "      <td>6072 days 20:40:24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.051503e+07</td>\n", "      <td>11947.490797</td>\n", "      <td>129.491587</td>\n", "      <td>107.148203</td>\n", "      <td>1520.201889</td>\n", "      <td>175.295839</td>\n", "      <td>6910.460267</td>\n", "      <td>79.322715</td>\n", "      <td>3.204384e+05</td>\n", "      <td>NaN</td>\n", "      <td>1844.592457</td>\n", "      <td>304.079997</td>\n", "      <td>2394.258699</td>\n", "      <td>61.065556</td>\n", "      <td>1.160959e+05</td>\n", "      <td>1.194303e+06</td>\n", "      <td>2.315255e+05</td>\n", "      <td>NaN</td>\n", "      <td>1157 days 01:53:22.314503104</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-01-13T07:58:37.272333Z", "start_time": "2025-01-13T07:58:35.323658Z"}}, "cell_type": "code", "source": ["# save the filtered projects\n", "sample_projects.to_csv('../data/sample_projects_total.csv',index=False)"], "id": "4f66c95b72a09437", "outputs": [], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-23T07:19:51.968118Z", "start_time": "2024-11-23T07:19:51.961096Z"}}, "cell_type": "code", "source": ["# sampled_projeimport pandas as pd\n", "import numpy as np\n", "\n", "def sample_projects_by_quartiles(df, sample_size_per_quartile=100):\n", "    \"\"\"\n", "    Groups and samples projects by mainLanguage and stargazer quartiles.\n", "\n", "    Parameters:\n", "    - df: Original DataFrame\n", "    - sample_size_per_quartile: Number of samples to randomly draw from each quartile\n", "\n", "    Returns:\n", "    - Sam<PERSON> DataFrame\n", "    \"\"\"\n", "    sampled_df_list = []\n", "\n", "    # Group by mainLanguage\n", "    for language, group in df.groupby('mainLanguage'):\n", "        # Calculate quartiles for stargazers\n", "        group['quartile'] = pd.qcut(group['stargazers'], 4, labels=False)\n", "\n", "        # Sample from each quartile\n", "        for quartile in range(4):\n", "            quartile_group = group[group['quartile'] == quartile]\n", "            n = len(quartile_group)\n", "            final_sample_size = min(sample_size_per_quartile, n)  # Ensure sample size does not exceed group size\n", "            print(f\"Sampling {final_sample_size} projects for {language} in quartile {quartile} from {n} projects\")\n", "            sampled_group = quartile_group.sample(n=final_sample_size, random_state=42)\n", "            sampled_df_list.append(sampled_group)\n", "\n", "    # Concatenate all sampled groups into a single DataFrame\n", "    sampled_df = pd.concat(sampled_df_list, ignore_index=True)\n", "    return sampled_df\n", "\n", "\n", "# Example usage\n", "# sample_projects = pd.read_csv('../data/raw_data_sampling/results.csv')\n", "# sampled_projects = sample_projects_by_quartiles(sample_projects)\n", "# sampled_projects.to_csv('../data/sample_projects_quartiles.csv', index=False)"], "id": "4321036cb6b7000a", "outputs": [], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-23T07:19:53.592461Z", "start_time": "2024-11-23T07:19:53.420509Z"}}, "cell_type": "code", "source": ["sample_projects_100 = sample_projects_by_quartiles(sample_projects,100)\n", "sample_projects_100"], "id": "3182a95969a5d9f9", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sampling 100 projects for C in quartile 0 from 715 projects\n", "Sampling 100 projects for C in quartile 1 from 715 projects\n", "Sampling 100 projects for C in quartile 2 from 715 projects\n", "Sampling 100 projects for C in quartile 3 from 715 projects\n", "Sampling 100 projects for C# in quartile 0 from 596 projects\n", "Sampling 100 projects for C# in quartile 1 from 585 projects\n", "Sampling 100 projects for C# in quartile 2 from 590 projects\n", "Sampling 100 projects for C# in quartile 3 from 591 projects\n", "Sampling 100 projects for C++ in quartile 0 from 1011 projects\n", "Sampling 100 projects for C++ in quartile 1 from 1005 projects\n", "Sampling 100 projects for C++ in quartile 2 from 1008 projects\n", "Sampling 100 projects for C++ in quartile 3 from 1008 projects\n", "Sampling 100 projects for Go in quartile 0 from 1256 projects\n", "Sampling 100 projects for Go in quartile 1 from 1251 projects\n", "Sampling 100 projects for Go in quartile 2 from 1253 projects\n", "Sampling 100 projects for Go in quartile 3 from 1253 projects\n", "Sampling 100 projects for Java in quartile 0 from 1068 projects\n", "Sampling 100 projects for Java in quartile 1 from 1064 projects\n", "Sampling 100 projects for Java in quartile 2 from 1061 projects\n", "Sampling 100 projects for Java in quartile 3 from 1064 projects\n", "Sampling 100 projects for JavaScript in quartile 0 from 2397 projects\n", "Sampling 100 projects for JavaScript in quartile 1 from 2388 projects\n", "Sampling 100 projects for JavaScript in quartile 2 from 2394 projects\n", "Sampling 100 projects for JavaScript in quartile 3 from 2390 projects\n", "Sampling 100 projects for PHP in quartile 0 from 668 projects\n", "Sampling 100 projects for PHP in quartile 1 from 668 projects\n", "Sampling 100 projects for PHP in quartile 2 from 664 projects\n", "Sampling 100 projects for PHP in quartile 3 from 667 projects\n", "Sampling 100 projects for Python in quartile 0 from 2647 projects\n", "Sampling 100 projects for Python in quartile 1 from 2644 projects\n", "Sampling 100 projects for Python in quartile 2 from 2641 projects\n", "Sampling 100 projects for Python in quartile 3 from 2643 projects\n", "Sampling 100 projects for Rust in quartile 0 from 750 projects\n", "Sampling 100 projects for Rust in quartile 1 from 742 projects\n", "Sampling 100 projects for Rust in quartile 2 from 746 projects\n", "Sampling 100 projects for Rust in quartile 3 from 746 projects\n", "Sampling 100 projects for TypeScript in quartile 0 from 1720 projects\n", "Sampling 100 projects for TypeScript in quartile 1 from 1701 projects\n", "Sampling 100 projects for TypeScript in quartile 2 from 1708 projects\n", "Sampling 100 projects for TypeScript in quartile 3 from 1710 projects\n"]}, {"data": {"text/plain": ["            id                          name  isFork  commits  branches  \\\n", "0      3390411                 mixer/ftl-sdk   False      430        21   \n", "1      3954958    pauldmccarthy/indexed_gzip   False     1034         2   \n", "2      3375539    superpermutators/superperm   False      347         2   \n", "3      3486003                   likle/cwalk   False      143         2   \n", "4     70510212          lima-vm/socket_vmnet   False      142         1   \n", "...        ...                           ...     ...      ...       ...   \n", "3995   4054975  valor-software/ngx-bootstrap   False     2596        47   \n", "3996   4097392                   alibaba/ice   False     3226        62   \n", "3997   4097407  vuestorefront/vue-storefront   False    17160       591   \n", "3998   4019794     jamiemason/imageoptim-cli   False      393         3   \n", "3999  10788023                       unjs/h3   False      893        15   \n", "\n", "      releases  forks mainLanguage defaultBranch  \\\n", "0           41     46            C        master   \n", "1           61     26            C          main   \n", "2            0     25            C        master   \n", "3           12     40            C        master   \n", "4           11     17            C        master   \n", "...        ...    ...          ...           ...   \n", "3995       138   1687   TypeScript   development   \n", "3996       129   2096   TypeScript        master   \n", "3997       156   2083   TypeScript          main   \n", "3998        20    126   TypeScript          main   \n", "3999        34    215   TypeScript          main   \n", "\n", "                                     license  ...  \\\n", "0                                MIT License  ...   \n", "1                                      Other  ...   \n", "2     GNU Affero General Public License v3.0  ...   \n", "3                                MIT License  ...   \n", "4                         Apache License 2.0  ...   \n", "...                                      ...  ...   \n", "3995                             MIT License  ...   \n", "3996                             MIT License  ...   \n", "3997                             MIT License  ...   \n", "3998                             MIT License  ...   \n", "3999                             MIT License  ...   \n", "\n", "                                 lastCommitSHA  hasWiki  isArchived  \\\n", "0     d0c8469f66806b5ea738d607f7d2b000af8b1129     True       False   \n", "1     8fee6723105b4e26ab5fea0e73a9fe36854a1c17    False       False   \n", "2     68008299c04098c442022b2e8cf7cfedbd8f8341     True       False   \n", "3     e98d23f68807208952c179b49e4fd1813f31298d    False       False   \n", "4     f486d475d4842bbddfe8f66ba09f7d1cb10cfbed    False       False   \n", "...                                        ...      ...         ...   \n", "3995  f09e4b15464b7e745c6262a76db343b3023b15e4     True       False   \n", "3996  710b2e48a0c15547e9de84ceae43aeaa095071d5    False       False   \n", "3997  01524a1bdc49f2c4168f2d67b8b93dce4b2ac4b0    False       False   \n", "3998  509d2ef8707b21be622d56ca25b653a0d9a593a2    False       False   \n", "3999  77510f3c27e6884489189b300c52b5355751aefc    False       False   \n", "\n", "      isDisabled  isLocked                                          languages  \\\n", "0          False     False                                                NaN   \n", "1          False     False  {\"C\":138041,\"Cython\":92832,\"Python\":72303,\"She...   \n", "2          False     False                                                NaN   \n", "3          False     False            {\"C\":135773,\"CMake\":13079,\"Meson\":1170}   \n", "4          False     False           {\"C\":29054,\"Make<PERSON><PERSON>\":4924,\"Shell\":1963}   \n", "...          ...       ...                                                ...   \n", "3995       False     False  {\"TypeScript\":3366680,\"HTML\":212787,\"SCSS\":594...   \n", "3996       False     False  {\"TypeScript\":1308181,\"JavaScript\":91886,\"CSS\"...   \n", "3997       False     False  {\"TypeScript\":807813,\"JavaScript\":32564,\"HTML\"...   \n", "3998       False     False  {\"TypeScript\":20747,\"AppleScript\":4647,\"JavaSc...   \n", "3999       False     False             {\"TypeScript\":391596,\"JavaScript\":748}   \n", "\n", "                                                 labels  \\\n", "0                                                   NaN   \n", "1     bug;dependencies;duplicate;enhancement;help wa...   \n", "2                                                   NaN   \n", "3     bug;duplicate;enhancement;good first issue;hel...   \n", "4     allstar;bug;documentation;duplicate;enhancemen...   \n", "...                                                 ...   \n", "3995  angular issue;aria;as designed;blocked;breakin...   \n", "3996  bug;dependencies;discussion;docs;duplicate;enh...   \n", "3997  10: very complex;15: too complex;1: easy;3: me...   \n", "3998  dependencies;priority: critical;priority: high...   \n", "3999  breaking change;bug;chore;discussion;documenta...   \n", "\n", "                                                 topics           duration  \\\n", "0                                                   NaN 1527 days 04:03:25   \n", "1                                                   NaN 3163 days 15:08:19   \n", "2                                                   NaN  560 days 01:56:45   \n", "3     c;c-plus-plus;c-plus-plus-11;cpp;cross-platfor... 2261 days 17:17:10   \n", "4                                                   NaN  912 days 19:40:19   \n", "...                                                 ...                ...   \n", "3995  angular;bootstrap;carousel;components;datepick... 3369 days 00:42:57   \n", "3996  framework;icejs;microfrontends;mpa;nodejs;reac... 2929 days 23:42:16   \n", "3997  commercetools;e-commerce;ecommerce;ecommerce-p... 2646 days 00:19:20   \n", "3998  advpng;compress;gifsicle;image-optimisation;im... 4228 days 02:01:52   \n", "3999                                                NaN 1451 days 08:33:07   \n", "\n", "      quartile  \n", "0            0  \n", "1            0  \n", "2            0  \n", "3            0  \n", "4            0  \n", "...        ...  \n", "3995         3  \n", "3996         3  \n", "3997         3  \n", "3998         3  \n", "3999         3  \n", "\n", "[4000 rows x 37 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>isFork</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>mainLanguage</th>\n", "      <th>defaultBranch</th>\n", "      <th>license</th>\n", "      <th>...</th>\n", "      <th>lastCommitSHA</th>\n", "      <th>hasWiki</th>\n", "      <th>isArchived</th>\n", "      <th>isDisabled</th>\n", "      <th>isLocked</th>\n", "      <th>languages</th>\n", "      <th>labels</th>\n", "      <th>topics</th>\n", "      <th>duration</th>\n", "      <th>quartile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3390411</td>\n", "      <td>mixer/ftl-sdk</td>\n", "      <td>False</td>\n", "      <td>430</td>\n", "      <td>21</td>\n", "      <td>41</td>\n", "      <td>46</td>\n", "      <td>C</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>d0c8469f66806b5ea738d607f7d2b000af8b1129</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1527 days 04:03:25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3954958</td>\n", "      <td>pauldmccarthy/indexed_gzip</td>\n", "      <td>False</td>\n", "      <td>1034</td>\n", "      <td>2</td>\n", "      <td>61</td>\n", "      <td>26</td>\n", "      <td>C</td>\n", "      <td>main</td>\n", "      <td>Other</td>\n", "      <td>...</td>\n", "      <td>8fee6723105b4e26ab5fea0e73a9fe36854a1c17</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":138041,\"Cython\":92832,\"Python\":72303,\"She...</td>\n", "      <td>bug;dependencies;duplicate;enhancement;help wa...</td>\n", "      <td>NaN</td>\n", "      <td>3163 days 15:08:19</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3375539</td>\n", "      <td>superpermutators/superperm</td>\n", "      <td>False</td>\n", "      <td>347</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>C</td>\n", "      <td>master</td>\n", "      <td>GNU Affero General Public License v3.0</td>\n", "      <td>...</td>\n", "      <td>68008299c04098c442022b2e8cf7cfedbd8f8341</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>560 days 01:56:45</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3486003</td>\n", "      <td>likle/cwalk</td>\n", "      <td>False</td>\n", "      <td>143</td>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>40</td>\n", "      <td>C</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>e98d23f68807208952c179b49e4fd1813f31298d</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":135773,\"CMake\":13079,\"Meson\":1170}</td>\n", "      <td>bug;duplicate;enhancement;good first issue;hel...</td>\n", "      <td>c;c-plus-plus;c-plus-plus-11;cpp;cross-platfor...</td>\n", "      <td>2261 days 17:17:10</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>70510212</td>\n", "      <td>lima-vm/socket_vmnet</td>\n", "      <td>False</td>\n", "      <td>142</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>17</td>\n", "      <td>C</td>\n", "      <td>master</td>\n", "      <td>Apache License 2.0</td>\n", "      <td>...</td>\n", "      <td>f486d475d4842bbddfe8f66ba09f7d1cb10cfbed</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":29054,\"<PERSON>fi<PERSON>\":4924,\"Shell\":1963}</td>\n", "      <td>allstar;bug;documentation;duplicate;enhancemen...</td>\n", "      <td>NaN</td>\n", "      <td>912 days 19:40:19</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3995</th>\n", "      <td>4054975</td>\n", "      <td>valor-software/ngx-bootstrap</td>\n", "      <td>False</td>\n", "      <td>2596</td>\n", "      <td>47</td>\n", "      <td>138</td>\n", "      <td>1687</td>\n", "      <td>TypeScript</td>\n", "      <td>development</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>f09e4b15464b7e745c6262a76db343b3023b15e4</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":3366680,\"HTML\":212787,\"SCSS\":594...</td>\n", "      <td>angular issue;aria;as designed;blocked;breakin...</td>\n", "      <td>angular;bootstrap;carousel;components;datepick...</td>\n", "      <td>3369 days 00:42:57</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3996</th>\n", "      <td>4097392</td>\n", "      <td>alibaba/ice</td>\n", "      <td>False</td>\n", "      <td>3226</td>\n", "      <td>62</td>\n", "      <td>129</td>\n", "      <td>2096</td>\n", "      <td>TypeScript</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>710b2e48a0c15547e9de84ceae43aeaa095071d5</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":1308181,\"JavaScript\":91886,\"CSS\"...</td>\n", "      <td>bug;dependencies;discussion;docs;duplicate;enh...</td>\n", "      <td>framework;icejs;microfrontends;mpa;nodejs;reac...</td>\n", "      <td>2929 days 23:42:16</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3997</th>\n", "      <td>4097407</td>\n", "      <td>vuestorefront/vue-storefront</td>\n", "      <td>False</td>\n", "      <td>17160</td>\n", "      <td>591</td>\n", "      <td>156</td>\n", "      <td>2083</td>\n", "      <td>TypeScript</td>\n", "      <td>main</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>01524a1bdc49f2c4168f2d67b8b93dce4b2ac4b0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":807813,\"JavaScript\":32564,\"HTML\"...</td>\n", "      <td>10: very complex;15: too complex;1: easy;3: me...</td>\n", "      <td>commercetools;e-commerce;ecommerce;ecommerce-p...</td>\n", "      <td>2646 days 00:19:20</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3998</th>\n", "      <td>4019794</td>\n", "      <td>jamiemason/imageoptim-cli</td>\n", "      <td>False</td>\n", "      <td>393</td>\n", "      <td>3</td>\n", "      <td>20</td>\n", "      <td>126</td>\n", "      <td>TypeScript</td>\n", "      <td>main</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>509d2ef8707b21be622d56ca25b653a0d9a593a2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":20747,\"AppleScript\":4647,\"JavaSc...</td>\n", "      <td>dependencies;priority: critical;priority: high...</td>\n", "      <td>advpng;compress;gifsicle;image-optimisation;im...</td>\n", "      <td>4228 days 02:01:52</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3999</th>\n", "      <td>10788023</td>\n", "      <td>unjs/h3</td>\n", "      <td>False</td>\n", "      <td>893</td>\n", "      <td>15</td>\n", "      <td>34</td>\n", "      <td>215</td>\n", "      <td>TypeScript</td>\n", "      <td>main</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>77510f3c27e6884489189b300c52b5355751aefc</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":391596,\"JavaScript\":748}</td>\n", "      <td>breaking change;bug;chore;discussion;documenta...</td>\n", "      <td>NaN</td>\n", "      <td>1451 days 08:33:07</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4000 rows × 37 columns</p>\n", "</div>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "execution_count": 12}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "sample_projects_100.sort", "id": "12b42cea6d00bf8b"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-23T07:22:13.231465Z", "start_time": "2024-11-23T07:22:13.084848Z"}}, "cell_type": "code", "source": "sample_projects_100.to_csv('../data/sample_projects_quartiles.csv',index=False)", "id": "ca9cd7093f996a32", "outputs": [], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T13:05:55.115035Z", "start_time": "2024-11-13T13:05:55.105297Z"}}, "cell_type": "code", "source": ["# def calculate_sample_size(population_size, confidence_level=0.95, margin_of_error=0.05, p=0.5):\n", "#     \"\"\"\n", "#     Dynamically calculates the sample size.\n", "#\n", "#     Parameters:\n", "#     - population_size: Total population size\n", "#     - confidence_level: Confidence level (default 0.95)\n", "#     - margin_of_error: Margin of error (default 0.05)\n", "#     - p: Probability of success, default is 0.5\n", "#\n", "#     Returns:\n", "#     - Calculated sample size\n", "#     \"\"\"\n", "#     z = norm.ppf(1 - (1 - confidence_level) / 2)  # Z-score for the given confidence level\n", "#     # Initial sample size (not adjusted for population size)\n", "#     initial_sample_size = (z**2 * p * (1 - p)) / (margin_of_error**2)\n", "#     # Adjust the sample size using the finite population correction formula\n", "#     adjusted_sample_size = initial_sample_size / (1 + (initial_sample_size - 1) / population_size)\n", "#     return int(np.ceil(adjusted_sample_size))\n", "#\n", "# def sample_projects_by_language(df, top_n=2000, desired_margin_of_error=0.05):\n", "#     \"\"\"\n", "#     Groups and samples projects by mainLanguage.\n", "#\n", "#     Parameters:\n", "#     - df: Original DataFrame\n", "#     - top_n: Maximum number of projects to select per mainLanguage group\n", "#     - desired_margin_of_error: Margin of error for controlling the sample size calculation\n", "#\n", "#     Returns:\n", "#     - Sam<PERSON><PERSON><PERSON>\n", "#     \"\"\"\n", "#     # Step 1: For each mainLanguage group, sort by 'stargazers' and select the top_n projects\n", "#     top_projects = df.groupby('mainLanguage', group_keys=False, as_index=False).apply(\n", "#         lambda x: x.nlargest(top_n, 'stargazers')\n", "#     )\n", "#\n", "#     # Step 2: Dynamically calculate sample size and sample each mainLanguage group\n", "#     sampled_df_list = []\n", "#     for language, group in top_projects.groupby('mainLanguage'):\n", "#         n = len(group)\n", "#         sample_size = calculate_sample_size(n, margin_of_error=desired_margin_of_error)  # Dynamically calculate sample size\n", "#         print(f\"Sampling {sample_size} projects for {language} from {n} projects\")\n", "#         sampled_group = group.sample(n=sample_size, random_state=42)\n", "#         sampled_df_list.append(sampled_group)\n", "#\n", "#     # Concatenate all sampled groups into a single DataFrame\n", "#     sampled_df = pd.concat(sampled_df_list, ignore_index=True)\n", "#\n", "#     return sampled_df\n", "#\n", "# import pandas as pd\n", "#\n", "# def sample_projects_fixed(df, top_n=2000, sample_size=100):\n", "#     \"\"\"\n", "#     Groups and randomly samples a fixed number of projects by mainLanguage.\n", "#\n", "#     Parameters:\n", "#     - df: Original DataFrame\n", "#     - top_n: Maximum number of projects to select per mainLanguage group\n", "#     - sample_size: Number of samples to randomly draw from each mainLanguage group\n", "#\n", "#     Returns:\n", "#     - Sam<PERSON><PERSON><PERSON>\n", "#     \"\"\"\n", "#     # Step 1: For each mainLanguage group, sort by 'stargazers' and select the top_n projects\n", "#     top_projects = df.groupby('mainLanguage', group_keys=False, as_index=False).apply(\n", "#         lambda x: x.nlargest(top_n, 'stargazers')\n", "#     )\n", "#\n", "#     # Step 2: Sample a fixed number of projects from each mainLanguage group\n", "#     sampled_df_list = []\n", "#     for language, group in top_projects.groupby('mainLanguage'):\n", "#         n = len(group)\n", "#         final_sample_size = min(sample_size, n)  # Ensure sample size does not exceed group size\n", "#         print(f\"Sampling {final_sample_size} projects for {language} from {n} projects\")\n", "#         sampled_group = group.sample(n=final_sample_size, random_state=42)\n", "#         sampled_df_list.append(sampled_group)\n", "#\n", "#     # Concatenate all sampled groups into a single DataFrame\n", "#     sampled_df = pd.concat(sampled_df_list, ignore_index=True)\n", "#\n", "#     return sampled_df\n"], "id": "8bcf5fa0d7b5a9b6", "outputs": [], "execution_count": 22}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T13:05:58.336589Z", "start_time": "2024-11-13T13:05:58.175511Z"}}, "cell_type": "code", "source": ["# sample_results = sample_projects_by_language(sample_projects,top_n=2000)\n", "# sample_results"], "id": "d67ce10b37635fce", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sampling 323 projects for C from 2000 projects\n", "Sampling 323 projects for C# from 2000 projects\n", "Sampling 323 projects for C++ from 2000 projects\n", "Sampling 323 projects for Go from 2000 projects\n", "Sampling 323 projects for Java from 2000 projects\n", "Sampling 323 projects for JavaScript from 2000 projects\n", "Sampling 323 projects for PHP from 2000 projects\n", "Sampling 323 projects for Python from 2000 projects\n", "Sampling 323 projects for Rust from 2000 projects\n", "Sampling 323 projects for TypeScript from 2000 projects\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_128570/833405440.py:34: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  top_projects = df.groupby('mainLanguage', group_keys=False, as_index=False).apply(\n"]}, {"data": {"text/plain": ["            id                              name  isFork  commits  branches  \\\n", "0      3489656        bao-project/bao-hypervisor   False      557        13   \n", "1      3495316                   neomutt/neomutt   False    17196        23   \n", "2      3485591                      krakjoe/pcov   False      198         4   \n", "3      3495407       bitcraze/crazyflie-firmware   False     3571        62   \n", "4     48025101                nixos/mobile-nixos   False     2898         2   \n", "...        ...                               ...     ...      ...       ...   \n", "3225   4073634     twilio/twilio-video-app-react   False      372       113   \n", "3226   4049335  formulahendry/vscode-code-runner   False      206         7   \n", "3227   7565341                 appwrite/appwrite   False    22710       285   \n", "3228   4051261                   oguimbal/pg-mem   False      583         7   \n", "3229   7925934                  conwnet/github1s   False      405         9   \n", "\n", "      releases  forks mainLanguage defaultBranch  \\\n", "0            2    127            C          main   \n", "1           88    309            C          main   \n", "2            0     29            C       develop   \n", "3           37   1057            C        master   \n", "4            0    167            C   development   \n", "...        ...    ...          ...           ...   \n", "3225        20    728   TypeScript        master   \n", "3226        83    288   TypeScript        master   \n", "3227       102   4010   TypeScript          main   \n", "3228         3     97   TypeScript        master   \n", "3229        49    871   TypeScript        master   \n", "\n", "                                  license  ...          lastCommit  \\\n", "0                      Apache License 2.0  ... 2024-11-05 09:55:12   \n", "1         GNU General Public License v2.0  ... 2024-11-11 10:48:43   \n", "2                                   Other  ... 2024-10-18 07:41:01   \n", "3         GNU General Public License v3.0  ... 2024-11-06 07:19:07   \n", "4                             MIT License  ... 2024-05-13 07:08:59   \n", "...                                   ...  ...                 ...   \n", "3225                   Apache License 2.0  ... 2024-10-18 04:46:20   \n", "3226                          MIT License  ... 2024-04-05 09:24:14   \n", "3227  BSD 3-Clause New or Revised License  ... 2024-10-07 08:51:47   \n", "3228                          MIT License  ... 2024-10-11 10:25:29   \n", "3229                          MIT License  ... 2024-09-25 05:23:08   \n", "\n", "                                 lastCommitSHA  hasWiki  isArchived  \\\n", "0     8f219e980cf9201a5ab8137adee3bbf5151d61d2     True       False   \n", "1     b5a589ceb1dd3c5ff138e1d79151e69fb9832dee    False       False   \n", "2     a511421382eca9e1a52f7fa488fbe0fdc30039fc     True       False   \n", "3     b046745801fc2ad6888aa4359bcb36e3292b268c    False       False   \n", "4     655c8830d5fe2eae79c8fc0bab8033b34c8456eb    False       False   \n", "...                                        ...      ...         ...   \n", "3225  91be1c413fc3a3459210d92a958ad50ea5764d5c     True       False   \n", "3226  97af1080d09046e0129acce592f0033ff2df9f26     True       False   \n", "3227  a49c3a33f0fd831423afa7a0b53df2c5d709fc2b    False       False   \n", "3228  8f84e85fb77dc1a52c2cdc93b7a218078a0907f8     True       False   \n", "3229  8e9bb058b184f7a338e84f1a8b162bda849f443b     True       False   \n", "\n", "      isDisabled isLocked                                          languages  \\\n", "0          False    False     {\"C\":673615,\"Assembly\":50317,\"Makefile\":26322}   \n", "1          False    False  {\"C\":8513909,\"Tcl\":157526,\"Shell\":93753,\"Perl\"...   \n", "2          False    False  {\"C\":142322,\"PHP\":2995,\"M4\":1150,\"JavaScript\":...   \n", "3          False    False  {\"C\":12516369,\"HTML\":269858,\"Python\":143178,\"C...   \n", "4          False    False  {\"C\":476736,\"Nix\":386353,\"<PERSON>\":228929,\"HTML\":...   \n", "...          ...      ...                                                ...   \n", "3225       False    False  {\"TypeScript\":624509,\"JavaScript\":32818,\"Shell...   \n", "3226       False    False                               {\"TypeScript\":26029}   \n", "3227       False    False  {\"JavaScript\":527650,\"TypeScript\":490130,\"Less...   \n", "3228       False    False  {\"TypeScript\":1587943,\"JavaScript\":18721,\"CSS\"...   \n", "3229       False    False  {\"TypeScript\":468581,\"HTML\":40417,\"JavaScript\"...   \n", "\n", "                                                 labels  \\\n", "0     bug;documentation;duplicate;feature-request;go...   \n", "1     bug:confirmed;bug:crash;bug:leak;bug:regressio...   \n", "2     bug;duplicate;enhancement;good first issue;hel...   \n", "3     bug;discussion;documentation;duplicate;enhance...   \n", "4     * good first issue;* help wanted;0. priority: ...   \n", "...                                                 ...   \n", "3225  bug;dependencies;documentation;duplicate;featu...   \n", "3226  announcement;as designed;bug;dependencies;doc;...   \n", "3227  api / graphql;api / realtime;backlog;bug;close...   \n", "3228  bug;dependencies;documentation;duplicate;enhan...   \n", "3229  admin's-discussion;bug;dependencies;discusson;...   \n", "\n", "                                                 topics           duration  \n", "0     arm;armv8;cortex-a;cortex-r;embedded;hyperviso... 1761 days 04:13:01  \n", "1     c;cli;email;imap;mail;maildir;mbox;mime;mua;mu... 3248 days 08:35:49  \n", "2                                                   NaN 2102 days 05:04:33  \n", "3                                                   NaN 3911 days 04:57:02  \n", "4                                                   NaN 2164 days 06:08:01  \n", "...                                                 ...                ...  \n", "3225                          react;twilio;video;webrtc 1826 days 23:43:33  \n", "3226  executor;runner;typescript;visual-studio-code;... 2822 days 05:32:04  \n", "3227  android;appwrite;backend;backend-as-a-service;... 2009 days 04:15:22  \n", "3228  deno;hacktoberfest;mocha;node;node-postgres;no... 1547 days 22:58:21  \n", "3229                               hacktoberfest;vscode 1927 days 19:27:43  \n", "\n", "[3230 rows x 36 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>isFork</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>mainLanguage</th>\n", "      <th>defaultBranch</th>\n", "      <th>license</th>\n", "      <th>...</th>\n", "      <th>lastCommit</th>\n", "      <th>lastCommitSHA</th>\n", "      <th>hasWiki</th>\n", "      <th>isArchived</th>\n", "      <th>isDisabled</th>\n", "      <th>isLocked</th>\n", "      <th>languages</th>\n", "      <th>labels</th>\n", "      <th>topics</th>\n", "      <th>duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3489656</td>\n", "      <td>bao-project/bao-hypervisor</td>\n", "      <td>False</td>\n", "      <td>557</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>127</td>\n", "      <td>C</td>\n", "      <td>main</td>\n", "      <td>Apache License 2.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-05 09:55:12</td>\n", "      <td>8f219e980cf9201a5ab8137adee3bbf5151d61d2</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":673615,\"Assembly\":50317,\"Makefile\":26322}</td>\n", "      <td>bug;documentation;duplicate;feature-request;go...</td>\n", "      <td>arm;armv8;cortex-a;cortex-r;embedded;hyperviso...</td>\n", "      <td>1761 days 04:13:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3495316</td>\n", "      <td>neomutt/neomutt</td>\n", "      <td>False</td>\n", "      <td>17196</td>\n", "      <td>23</td>\n", "      <td>88</td>\n", "      <td>309</td>\n", "      <td>C</td>\n", "      <td>main</td>\n", "      <td>GNU General Public License v2.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-11 10:48:43</td>\n", "      <td>b5a589ceb1dd3c5ff138e1d79151e69fb9832dee</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":8513909,\"Tcl\":157526,\"Shell\":93753,\"Perl\"...</td>\n", "      <td>bug:confirmed;bug:crash;bug:leak;bug:regressio...</td>\n", "      <td>c;cli;email;imap;mail;maildir;mbox;mime;mua;mu...</td>\n", "      <td>3248 days 08:35:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3485591</td>\n", "      <td>krakjoe/pcov</td>\n", "      <td>False</td>\n", "      <td>198</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>29</td>\n", "      <td>C</td>\n", "      <td>develop</td>\n", "      <td>Other</td>\n", "      <td>...</td>\n", "      <td>2024-10-18 07:41:01</td>\n", "      <td>a511421382eca9e1a52f7fa488fbe0fdc30039fc</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":142322,\"PHP\":2995,\"M4\":1150,\"JavaScript\":...</td>\n", "      <td>bug;duplicate;enhancement;good first issue;hel...</td>\n", "      <td>NaN</td>\n", "      <td>2102 days 05:04:33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3495407</td>\n", "      <td>bitcraze/crazyflie-firmware</td>\n", "      <td>False</td>\n", "      <td>3571</td>\n", "      <td>62</td>\n", "      <td>37</td>\n", "      <td>1057</td>\n", "      <td>C</td>\n", "      <td>master</td>\n", "      <td>GNU General Public License v3.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-06 07:19:07</td>\n", "      <td>b046745801fc2ad6888aa4359bcb36e3292b268c</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":12516369,\"HTML\":269858,\"Python\":143178,\"C...</td>\n", "      <td>bug;discussion;documentation;duplicate;enhance...</td>\n", "      <td>NaN</td>\n", "      <td>3911 days 04:57:02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48025101</td>\n", "      <td>nixos/mobile-nixos</td>\n", "      <td>False</td>\n", "      <td>2898</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>167</td>\n", "      <td>C</td>\n", "      <td>development</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-05-13 07:08:59</td>\n", "      <td>655c8830d5fe2eae79c8fc0bab8033b34c8456eb</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":476736,\"Nix\":386353,\"<PERSON>\":228929,\"HTML\":...</td>\n", "      <td>* good first issue;* help wanted;0. priority: ...</td>\n", "      <td>NaN</td>\n", "      <td>2164 days 06:08:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3225</th>\n", "      <td>4073634</td>\n", "      <td>twilio/twilio-video-app-react</td>\n", "      <td>False</td>\n", "      <td>372</td>\n", "      <td>113</td>\n", "      <td>20</td>\n", "      <td>728</td>\n", "      <td>TypeScript</td>\n", "      <td>master</td>\n", "      <td>Apache License 2.0</td>\n", "      <td>...</td>\n", "      <td>2024-10-18 04:46:20</td>\n", "      <td>91be1c413fc3a3459210d92a958ad50ea5764d5c</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":624509,\"JavaScript\":32818,\"Shell...</td>\n", "      <td>bug;dependencies;documentation;duplicate;featu...</td>\n", "      <td>react;twilio;video;webrtc</td>\n", "      <td>1826 days 23:43:33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3226</th>\n", "      <td>4049335</td>\n", "      <td>formulahendry/vscode-code-runner</td>\n", "      <td>False</td>\n", "      <td>206</td>\n", "      <td>7</td>\n", "      <td>83</td>\n", "      <td>288</td>\n", "      <td>TypeScript</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-04-05 09:24:14</td>\n", "      <td>97af1080d09046e0129acce592f0033ff2df9f26</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":26029}</td>\n", "      <td>announcement;as designed;bug;dependencies;doc;...</td>\n", "      <td>executor;runner;typescript;visual-studio-code;...</td>\n", "      <td>2822 days 05:32:04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3227</th>\n", "      <td>7565341</td>\n", "      <td>appwrite/appwrite</td>\n", "      <td>False</td>\n", "      <td>22710</td>\n", "      <td>285</td>\n", "      <td>102</td>\n", "      <td>4010</td>\n", "      <td>TypeScript</td>\n", "      <td>main</td>\n", "      <td>BSD 3-Clause New or Revised License</td>\n", "      <td>...</td>\n", "      <td>2024-10-07 08:51:47</td>\n", "      <td>a49c3a33f0fd831423afa7a0b53df2c5d709fc2b</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"JavaScript\":527650,\"TypeScript\":490130,\"Less...</td>\n", "      <td>api / graphql;api / realtime;backlog;bug;close...</td>\n", "      <td>android;appwrite;backend;backend-as-a-service;...</td>\n", "      <td>2009 days 04:15:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3228</th>\n", "      <td>4051261</td>\n", "      <td>oguimbal/pg-mem</td>\n", "      <td>False</td>\n", "      <td>583</td>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>97</td>\n", "      <td>TypeScript</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-10-11 10:25:29</td>\n", "      <td>8f84e85fb77dc1a52c2cdc93b7a218078a0907f8</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":1587943,\"JavaScript\":18721,\"CSS\"...</td>\n", "      <td>bug;dependencies;documentation;duplicate;enhan...</td>\n", "      <td>deno;hacktoberfest;mocha;node;node-postgres;no...</td>\n", "      <td>1547 days 22:58:21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3229</th>\n", "      <td>7925934</td>\n", "      <td>conwnet/github1s</td>\n", "      <td>False</td>\n", "      <td>405</td>\n", "      <td>9</td>\n", "      <td>49</td>\n", "      <td>871</td>\n", "      <td>TypeScript</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-09-25 05:23:08</td>\n", "      <td>8e9bb058b184f7a338e84f1a8b162bda849f443b</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"TypeScript\":468581,\"HTML\":40417,\"JavaScript\"...</td>\n", "      <td>admin's-discussion;bug;dependencies;discusson;...</td>\n", "      <td>hacktoberfest;vscode</td>\n", "      <td>1927 days 19:27:43</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3230 rows × 36 columns</p>\n", "</div>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "execution_count": 23}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T13:06:07.329751Z", "start_time": "2024-11-13T13:06:07.290293Z"}}, "cell_type": "code", "source": "# sample_results.describe()", "id": "4e41bf2eb9e3f62c", "outputs": [{"data": {"text/plain": ["                 id        commits     branches     releases         forks  \\\n", "count  3.230000e+03    3230.000000  3230.000000  3230.000000   3230.000000   \n", "mean   2.458715e+07    3251.648916    33.566254    51.539009    718.997523   \n", "min    0.000000e+00     100.000000     1.000000     0.000000      0.000000   \n", "25%    3.874919e+06     439.000000     4.000000     3.000000    138.000000   \n", "50%    6.756678e+06    1089.000000     9.000000    20.000000    302.000000   \n", "75%    3.827584e+07    2806.750000    23.000000    51.000000    704.000000   \n", "max    9.522097e+07  129205.000000  5025.000000  1000.000000  27568.000000   \n", "std    3.142058e+07    8213.523542   134.960930   110.696039   1362.607896   \n", "\n", "          watchers    stargazers  contributors          size  \\\n", "count  3230.000000   3230.000000   3230.000000  3.230000e+03   \n", "mean    114.313622   4693.330650     77.489783  1.005157e+05   \n", "min       2.000000    192.000000     10.000000  7.200000e+01   \n", "25%      29.000000    885.250000     21.000000  2.600500e+03   \n", "50%      59.000000   2318.000000     42.000000  1.099150e+04   \n", "75%     128.000000   4995.250000     91.000000  4.966950e+04   \n", "max    3789.000000  64988.000000    478.000000  1.552462e+07   \n", "std     179.142868   7037.938053     90.352750  5.098675e+05   \n", "\n", "                           createdAt   totalIssues    openIssues  \\\n", "count                           3230   3230.000000   3230.000000   \n", "mean   2016-11-12 19:59:03.751393024    875.317957    151.240867   \n", "min              2008-03-21 04:25:42      1.000000      1.000000   \n", "25%    2014-04-22 03:42:17.750000128    130.000000     20.000000   \n", "50%       2016-09-16 18:55:17.500000    313.000000     55.000000   \n", "75%              2019-06-08 18:18:08    811.750000    140.000000   \n", "max              2023-10-22 02:58:35  67075.000000  30586.000000   \n", "std                              NaN   2300.943680    627.371759   \n", "\n", "       totalPullRequests  openPullRequests    blankLines     codeLines  \\\n", "count        3230.000000       3230.000000  3.230000e+03  3.230000e+03   \n", "mean         1020.093189         21.494427  3.164413e+04  2.209856e+05   \n", "min             4.000000          1.000000  4.000000e+00  9.000000e+00   \n", "25%            96.000000          3.000000  1.520250e+03  1.035000e+04   \n", "50%           252.000000          8.000000  5.166000e+03  3.555750e+04   \n", "75%           826.000000         20.000000  1.768325e+04  1.287068e+05   \n", "max         67535.000000       4556.000000  1.122867e+07  1.409087e+07   \n", "std          3078.472238         95.771124  2.300696e+05  8.220036e+05   \n", "\n", "       commentLines                     lastCommit  \\\n", "count  3.230000e+03                           3230   \n", "mean   4.123385e+04  2023-11-07 21:15:13.582352640   \n", "min    0.000000e+00            2013-02-03 09:58:05   \n", "25%    9.760000e+02     2023-09-05 05:15:30.500000   \n", "50%    3.883000e+03            2024-09-26 01:18:50   \n", "75%    1.629250e+04  2024-11-07 10:10:43.750000128   \n", "max    8.124748e+06            2024-11-13 12:43:35   \n", "std    2.534319e+05                            NaN   \n", "\n", "                           duration  \n", "count                          3230  \n", "mean   2551 days 01:16:09.830959744  \n", "min               368 days 00:58:49  \n", "25%       1621 days 12:12:46.750000  \n", "50%              2482 days 18:09:50  \n", "75%       3432 days 20:46:57.500000  \n", "max              5942 days 03:20:38  \n", "std    1201 days 23:55:45.277414976  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>watchers</th>\n", "      <th>stargazers</th>\n", "      <th>contributors</th>\n", "      <th>size</th>\n", "      <th>createdAt</th>\n", "      <th>totalIssues</th>\n", "      <th>openIssues</th>\n", "      <th>totalPullRequests</th>\n", "      <th>openPullRequests</th>\n", "      <th>blankLines</th>\n", "      <th>codeLines</th>\n", "      <th>commentLines</th>\n", "      <th>lastCommit</th>\n", "      <th>duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3.230000e+03</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3.230000e+03</td>\n", "      <td>3230</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3230.000000</td>\n", "      <td>3.230000e+03</td>\n", "      <td>3.230000e+03</td>\n", "      <td>3.230000e+03</td>\n", "      <td>3230</td>\n", "      <td>3230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.458715e+07</td>\n", "      <td>3251.648916</td>\n", "      <td>33.566254</td>\n", "      <td>51.539009</td>\n", "      <td>718.997523</td>\n", "      <td>114.313622</td>\n", "      <td>4693.330650</td>\n", "      <td>77.489783</td>\n", "      <td>1.005157e+05</td>\n", "      <td>2016-11-12 19:59:03.751393024</td>\n", "      <td>875.317957</td>\n", "      <td>151.240867</td>\n", "      <td>1020.093189</td>\n", "      <td>21.494427</td>\n", "      <td>3.164413e+04</td>\n", "      <td>2.209856e+05</td>\n", "      <td>4.123385e+04</td>\n", "      <td>2023-11-07 21:15:13.582352640</td>\n", "      <td>2551 days 01:16:09.830959744</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000e+00</td>\n", "      <td>100.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>192.000000</td>\n", "      <td>10.000000</td>\n", "      <td>7.200000e+01</td>\n", "      <td>2008-03-21 04:25:42</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000e+00</td>\n", "      <td>9.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>2013-02-03 09:58:05</td>\n", "      <td>368 days 00:58:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>3.874919e+06</td>\n", "      <td>439.000000</td>\n", "      <td>4.000000</td>\n", "      <td>3.000000</td>\n", "      <td>138.000000</td>\n", "      <td>29.000000</td>\n", "      <td>885.250000</td>\n", "      <td>21.000000</td>\n", "      <td>2.600500e+03</td>\n", "      <td>2014-04-22 03:42:17.750000128</td>\n", "      <td>130.000000</td>\n", "      <td>20.000000</td>\n", "      <td>96.000000</td>\n", "      <td>3.000000</td>\n", "      <td>1.520250e+03</td>\n", "      <td>1.035000e+04</td>\n", "      <td>9.760000e+02</td>\n", "      <td>2023-09-05 05:15:30.500000</td>\n", "      <td>1621 days 12:12:46.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.756678e+06</td>\n", "      <td>1089.000000</td>\n", "      <td>9.000000</td>\n", "      <td>20.000000</td>\n", "      <td>302.000000</td>\n", "      <td>59.000000</td>\n", "      <td>2318.000000</td>\n", "      <td>42.000000</td>\n", "      <td>1.099150e+04</td>\n", "      <td>2016-09-16 18:55:17.500000</td>\n", "      <td>313.000000</td>\n", "      <td>55.000000</td>\n", "      <td>252.000000</td>\n", "      <td>8.000000</td>\n", "      <td>5.166000e+03</td>\n", "      <td>3.555750e+04</td>\n", "      <td>3.883000e+03</td>\n", "      <td>2024-09-26 01:18:50</td>\n", "      <td>2482 days 18:09:50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.827584e+07</td>\n", "      <td>2806.750000</td>\n", "      <td>23.000000</td>\n", "      <td>51.000000</td>\n", "      <td>704.000000</td>\n", "      <td>128.000000</td>\n", "      <td>4995.250000</td>\n", "      <td>91.000000</td>\n", "      <td>4.966950e+04</td>\n", "      <td>2019-06-08 18:18:08</td>\n", "      <td>811.750000</td>\n", "      <td>140.000000</td>\n", "      <td>826.000000</td>\n", "      <td>20.000000</td>\n", "      <td>1.768325e+04</td>\n", "      <td>1.287068e+05</td>\n", "      <td>1.629250e+04</td>\n", "      <td>2024-11-07 10:10:43.750000128</td>\n", "      <td>3432 days 20:46:57.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.522097e+07</td>\n", "      <td>129205.000000</td>\n", "      <td>5025.000000</td>\n", "      <td>1000.000000</td>\n", "      <td>27568.000000</td>\n", "      <td>3789.000000</td>\n", "      <td>64988.000000</td>\n", "      <td>478.000000</td>\n", "      <td>1.552462e+07</td>\n", "      <td>2023-10-22 02:58:35</td>\n", "      <td>67075.000000</td>\n", "      <td>30586.000000</td>\n", "      <td>67535.000000</td>\n", "      <td>4556.000000</td>\n", "      <td>1.122867e+07</td>\n", "      <td>1.409087e+07</td>\n", "      <td>8.124748e+06</td>\n", "      <td>2024-11-13 12:43:35</td>\n", "      <td>5942 days 03:20:38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.142058e+07</td>\n", "      <td>8213.523542</td>\n", "      <td>134.960930</td>\n", "      <td>110.696039</td>\n", "      <td>1362.607896</td>\n", "      <td>179.142868</td>\n", "      <td>7037.938053</td>\n", "      <td>90.352750</td>\n", "      <td>5.098675e+05</td>\n", "      <td>NaN</td>\n", "      <td>2300.943680</td>\n", "      <td>627.371759</td>\n", "      <td>3078.472238</td>\n", "      <td>95.771124</td>\n", "      <td>2.300696e+05</td>\n", "      <td>8.220036e+05</td>\n", "      <td>2.534319e+05</td>\n", "      <td>NaN</td>\n", "      <td>1201 days 23:55:45.277414976</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "execution_count": 24}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T13:23:06.419864Z", "start_time": "2024-11-13T13:23:06.414316Z"}}, "cell_type": "code", "source": "# sample_projects['stargazers'].sum()", "id": "2184f5bbe22fea43", "outputs": [{"data": {"text/plain": ["np.int64(115573365)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "execution_count": 28}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T13:06:09.270189Z", "start_time": "2024-11-13T13:06:09.262195Z"}}, "cell_type": "code", "source": "# sample_results.value_counts('isArchived')", "id": "f67ca6c5f9c76401", "outputs": [{"data": {"text/plain": ["isArchived\n", "False    3143\n", "True       87\n", "Name: count, dtype: int64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "execution_count": 25}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T10:22:06.849655Z", "start_time": "2024-11-13T10:22:06.571469Z"}}, "cell_type": "code", "source": "# sample_results.to_csv('../data/sample_results_323.csv')", "id": "a00b948b71d80c89", "outputs": [], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": "save a simplified sampling version", "id": "70ac086f68fd1580"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "# get the total commits, pull rquests", "id": "f8736329e189bbf0"}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T10:22:07.194872Z", "start_time": "2024-11-13T10:22:06.986550Z"}}, "cell_type": "code", "source": ["sample_20 = sample_projects_fixed(sample_projects,2000,20)\n", "sample_20"], "id": "66b409ab4b1ca269", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sampling 20 projects for C from 2000 projects\n", "Sampling 20 projects for C# from 2000 projects\n", "Sampling 20 projects for C++ from 2000 projects\n", "Sampling 20 projects for Clojure from 225 projects\n", "Sampling 20 projects for CoffeeScript from 149 projects\n", "Sampling 20 projects for Common Lisp from 49 projects\n", "Sampling 20 projects for Dart from 522 projects\n", "Sampling 20 projects for Dockerfile from 137 projects\n", "Sampling 20 projects for Elixir from 342 projects\n", "Sampling 20 projects for Elm from 40 projects\n", "Sampling 20 projects for Erlang from 133 projects\n", "Sampling 20 projects for F# from 98 projects\n", "Sampling 20 projects for Fortran from 53 projects\n", "Sampling 20 projects for Go from 2000 projects\n", "Sampling 20 projects for Groovy from 96 projects\n", "Sampling 20 projects for Haskell from 316 projects\n", "Sampling 20 projects for Java from 2000 projects\n", "Sampling 20 projects for JavaScript from 2000 projects\n", "Sampling 20 projects for Julia from 472 projects\n", "Sampling 20 projects for Jupyter Notebook from 675 projects\n", "Sampling 20 projects for Kotlin from 844 projects\n", "Sampling 20 projects for Lua from 380 projects\n", "Sampling 20 projects for MATLAB from 38 projects\n", "Sampling 20 projects for Makefile from 95 projects\n", "Sampling 20 projects for Nix from 102 projects\n", "Sampling 20 projects for OCaml from 177 projects\n", "Sampling 20 projects for Objective-C from 508 projects\n", "Sampling 20 projects for PHP from 2000 projects\n", "Sampling 20 projects for <PERSON> from 30 projects\n", "Sampling 20 projects for Perl from 135 projects\n", "Sampling 20 projects for Python from 2000 projects\n", "Sampling 20 projects for R from 271 projects\n", "Sampling 14 projects for Racket from 14 projects\n", "Sampling 20 projects for Ruby from 2000 projects\n", "Sampling 20 projects for Rust from 2000 projects\n", "Sampling 20 projects for Scala from 658 projects\n", "Sampling 20 projects for Shell from 1270 projects\n", "Sampling 9 projects for Smalltalk from 9 projects\n", "Sampling 20 projects for Swift from 917 projects\n", "Sampling 20 projects for TeX from 86 projects\n", "Sampling 20 projects for TypeScript from 2000 projects\n", "Sampling 20 projects for Verilog from 27 projects\n", "Sampling 20 projects for Vue from 344 projects\n", "Sampling 20 projects for <PERSON>ig from 24 projects\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_128570/833405440.py:67: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  top_projects = df.groupby('mainLanguage', group_keys=False, as_index=False).apply(\n"]}, {"data": {"text/plain": ["           id                         name  isFork  commits  branches  \\\n", "0     3489656   bao-project/bao-hypervisor   False      557        13   \n", "1     3495316              neomutt/neomutt   False    17196        23   \n", "2     3485591                 krakjoe/pcov   False      198         4   \n", "3     3495407  bitcraze/crazyflie-firmware   False     3571        62   \n", "4    48025101           nixos/mobile-nixos   False     2898         2   \n", "..        ...                          ...     ...      ...       ...   \n", "858  94996732          <PERSON><PERSON><PERSON>/zig-protobuf   False      182         5   \n", "859   3272647              pixelguys/cubyz   False      802         6   \n", "860  70033494          natecraddock/ziglua   False      328        10   \n", "861  94996767                  Syndica/sig   False     1442        62   \n", "862  94701317      zig-gamedev/zig-gamedev   False     4812         4   \n", "\n", "     releases  forks mainLanguage defaultBranch  \\\n", "0           2    127            C          main   \n", "1          88    309            C          main   \n", "2           0     29            C       develop   \n", "3          37   1057            C        master   \n", "4           0    167            C   development   \n", "..        ...    ...          ...           ...   \n", "858         7     27          Zig        master   \n", "859         0     56          Zig        master   \n", "860         3     36          Zig          main   \n", "861         1     34          Zig          main   \n", "862         0    173          Zig          main   \n", "\n", "                             license  ...          lastCommit  \\\n", "0                 Apache License 2.0  ... 2024-11-05 09:55:12   \n", "1    GNU General Public License v2.0  ... 2024-11-11 10:48:43   \n", "2                              Other  ... 2024-10-18 07:41:01   \n", "3    GNU General Public License v3.0  ... 2024-11-06 07:19:07   \n", "4                        MIT License  ... 2024-05-13 07:08:59   \n", "..                               ...  ...                 ...   \n", "858                      MIT License  ... 2024-10-29 12:24:54   \n", "859  GNU General Public License v3.0  ... 2024-11-11 03:13:29   \n", "860                      MIT License  ... 2024-11-09 04:58:42   \n", "861               Apache License 2.0  ... 2024-11-10 03:55:58   \n", "862                      MIT License  ... 2024-11-07 08:26:01   \n", "\n", "                                lastCommitSHA  hasWiki  isArchived  \\\n", "0    8f219e980cf9201a5ab8137adee3bbf5151d61d2     True       False   \n", "1    b5a589ceb1dd3c5ff138e1d79151e69fb9832dee    False       False   \n", "2    a511421382eca9e1a52f7fa488fbe0fdc30039fc     True       False   \n", "3    b046745801fc2ad6888aa4359bcb36e3292b268c    False       False   \n", "4    655c8830d5fe2eae79c8fc0bab8033b34c8456eb    False       False   \n", "..                                        ...      ...         ...   \n", "858  eed464eaf55fefa913d33b9a6207f17e5e9d5e0f     True       False   \n", "859  e6db1ed81b439ce25eadf2a68ffe5b678b570bba     True       False   \n", "860  ef9400c57000f9772d77737fb19747eab3f57fd4    False       False   \n", "861  a9d016c59db782c85d20da1122ab0f9aad973983     True       False   \n", "862  ce7b5af913905c401305cefc37ec132f0ebd2d0e     True       False   \n", "\n", "     isDisabled isLocked                                          languages  \\\n", "0         False    False     {\"C\":673615,\"Assembly\":50317,\"Makefile\":26322}   \n", "1         False    False  {\"C\":8513909,\"Tcl\":157526,\"Shell\":93753,\"Perl\"...   \n", "2         False    False  {\"C\":142322,\"PHP\":2995,\"M4\":1150,\"JavaScript\":...   \n", "3         False    False  {\"C\":12516369,\"HTML\":269858,\"Python\":143178,\"C...   \n", "4         False    False  {\"C\":476736,\"Nix\":386353,\"<PERSON>\":228929,\"HTML\":...   \n", "..          ...      ...                                                ...   \n", "858       False    False       {\"Zig\":390415,\"Shell\":1643,\"Dockerfile\":161}   \n", "859       False    False  {\"C\":2297387,\"Zig\":1329568,\"Java\":999362,\"Obje...   \n", "860       False    False  {\"Zig\":310114,\"C\":443,\"Makefile\":420,\"C++\":401...   \n", "861       False    False        {\"Zig\":3523195,\"Python\":18001,\"Shell\":3512}   \n", "862       False    False  {\"C\":33988856,\"C++\":19603286,\"Objective-C\":706...   \n", "\n", "                                                labels  \\\n", "0    bug;documentation;duplicate;feature-request;go...   \n", "1    bug:confirmed;bug:crash;bug:leak;bug:regressio...   \n", "2    bug;duplicate;enhancement;good first issue;hel...   \n", "3    bug;discussion;documentation;duplicate;enhance...   \n", "4    * good first issue;* help wanted;0. priority: ...   \n", "..                                                 ...   \n", "858  bug;documentation;duplicate;enhancement;good f...   \n", "859  bug;content;contributor friendly;design princi...   \n", "860  bug;cleanup;documentation;duplicate;feature;go...   \n", "861  bug;documentation;duplicate;enhancement;featur...   \n", "862  breaking;bug;demo;documentation;duplicate;enha...   \n", "\n", "                                                topics           duration  \n", "0    arm;armv8;cortex-a;cortex-r;embedded;hyperviso... 1761 days 04:13:01  \n", "1    c;cli;email;imap;mail;maildir;mbox;mime;mua;mu... 3248 days 08:35:49  \n", "2                                                  NaN 2102 days 05:04:33  \n", "3                                                  NaN 3911 days 04:57:02  \n", "4                                                  NaN 2164 days 06:08:01  \n", "..                                                 ...                ...  \n", "858                           protobuf;zig;zig-package 1278 days 08:13:51  \n", "859  3d-game;cubyz;game;procedural-generation;sandb... 2106 days 16:56:52  \n", "860  binding;library;lua;lua-bindings;package;zig;z...  891 days 00:21:25  \n", "861                                                NaN  539 days 23:01:10  \n", "862  cross-platform;d3d12;demos;directx12;game-deve... 1210 days 06:11:09  \n", "\n", "[863 rows x 36 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>isFork</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>mainLanguage</th>\n", "      <th>defaultBranch</th>\n", "      <th>license</th>\n", "      <th>...</th>\n", "      <th>lastCommit</th>\n", "      <th>lastCommitSHA</th>\n", "      <th>hasWiki</th>\n", "      <th>isArchived</th>\n", "      <th>isDisabled</th>\n", "      <th>isLocked</th>\n", "      <th>languages</th>\n", "      <th>labels</th>\n", "      <th>topics</th>\n", "      <th>duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3489656</td>\n", "      <td>bao-project/bao-hypervisor</td>\n", "      <td>False</td>\n", "      <td>557</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>127</td>\n", "      <td>C</td>\n", "      <td>main</td>\n", "      <td>Apache License 2.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-05 09:55:12</td>\n", "      <td>8f219e980cf9201a5ab8137adee3bbf5151d61d2</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":673615,\"Assembly\":50317,\"Makefile\":26322}</td>\n", "      <td>bug;documentation;duplicate;feature-request;go...</td>\n", "      <td>arm;armv8;cortex-a;cortex-r;embedded;hyperviso...</td>\n", "      <td>1761 days 04:13:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3495316</td>\n", "      <td>neomutt/neomutt</td>\n", "      <td>False</td>\n", "      <td>17196</td>\n", "      <td>23</td>\n", "      <td>88</td>\n", "      <td>309</td>\n", "      <td>C</td>\n", "      <td>main</td>\n", "      <td>GNU General Public License v2.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-11 10:48:43</td>\n", "      <td>b5a589ceb1dd3c5ff138e1d79151e69fb9832dee</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":8513909,\"Tcl\":157526,\"Shell\":93753,\"Perl\"...</td>\n", "      <td>bug:confirmed;bug:crash;bug:leak;bug:regressio...</td>\n", "      <td>c;cli;email;imap;mail;maildir;mbox;mime;mua;mu...</td>\n", "      <td>3248 days 08:35:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3485591</td>\n", "      <td>krakjoe/pcov</td>\n", "      <td>False</td>\n", "      <td>198</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>29</td>\n", "      <td>C</td>\n", "      <td>develop</td>\n", "      <td>Other</td>\n", "      <td>...</td>\n", "      <td>2024-10-18 07:41:01</td>\n", "      <td>a511421382eca9e1a52f7fa488fbe0fdc30039fc</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":142322,\"PHP\":2995,\"M4\":1150,\"JavaScript\":...</td>\n", "      <td>bug;duplicate;enhancement;good first issue;hel...</td>\n", "      <td>NaN</td>\n", "      <td>2102 days 05:04:33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3495407</td>\n", "      <td>bitcraze/crazyflie-firmware</td>\n", "      <td>False</td>\n", "      <td>3571</td>\n", "      <td>62</td>\n", "      <td>37</td>\n", "      <td>1057</td>\n", "      <td>C</td>\n", "      <td>master</td>\n", "      <td>GNU General Public License v3.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-06 07:19:07</td>\n", "      <td>b046745801fc2ad6888aa4359bcb36e3292b268c</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":12516369,\"HTML\":269858,\"Python\":143178,\"C...</td>\n", "      <td>bug;discussion;documentation;duplicate;enhance...</td>\n", "      <td>NaN</td>\n", "      <td>3911 days 04:57:02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48025101</td>\n", "      <td>nixos/mobile-nixos</td>\n", "      <td>False</td>\n", "      <td>2898</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>167</td>\n", "      <td>C</td>\n", "      <td>development</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-05-13 07:08:59</td>\n", "      <td>655c8830d5fe2eae79c8fc0bab8033b34c8456eb</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":476736,\"Nix\":386353,\"<PERSON>\":228929,\"HTML\":...</td>\n", "      <td>* good first issue;* help wanted;0. priority: ...</td>\n", "      <td>NaN</td>\n", "      <td>2164 days 06:08:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>858</th>\n", "      <td>94996732</td>\n", "      <td>Arwalk/zig-protobuf</td>\n", "      <td>False</td>\n", "      <td>182</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>27</td>\n", "      <td>Zig</td>\n", "      <td>master</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-10-29 12:24:54</td>\n", "      <td>eed464eaf55fefa913d33b9a6207f17e5e9d5e0f</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"Zig\":390415,\"Shell\":1643,\"Dockerfile\":161}</td>\n", "      <td>bug;documentation;duplicate;enhancement;good f...</td>\n", "      <td>protobuf;zig;zig-package</td>\n", "      <td>1278 days 08:13:51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>3272647</td>\n", "      <td>pixelguys/cubyz</td>\n", "      <td>False</td>\n", "      <td>802</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>56</td>\n", "      <td>Zig</td>\n", "      <td>master</td>\n", "      <td>GNU General Public License v3.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-11 03:13:29</td>\n", "      <td>e6db1ed81b439ce25eadf2a68ffe5b678b570bba</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":2297387,\"Zig\":1329568,\"Java\":999362,\"Obje...</td>\n", "      <td>bug;content;contributor friendly;design princi...</td>\n", "      <td>3d-game;cubyz;game;procedural-generation;sandb...</td>\n", "      <td>2106 days 16:56:52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>860</th>\n", "      <td>70033494</td>\n", "      <td>natecraddock/ziglua</td>\n", "      <td>False</td>\n", "      <td>328</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>36</td>\n", "      <td>Zig</td>\n", "      <td>main</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-11-09 04:58:42</td>\n", "      <td>ef9400c57000f9772d77737fb19747eab3f57fd4</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"Zig\":310114,\"C\":443,\"<PERSON><PERSON><PERSON>\":420,\"C++\":401...</td>\n", "      <td>bug;cleanup;documentation;duplicate;feature;go...</td>\n", "      <td>binding;library;lua;lua-bindings;package;zig;z...</td>\n", "      <td>891 days 00:21:25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>861</th>\n", "      <td>94996767</td>\n", "      <td>Syndica/sig</td>\n", "      <td>False</td>\n", "      <td>1442</td>\n", "      <td>62</td>\n", "      <td>1</td>\n", "      <td>34</td>\n", "      <td>Zig</td>\n", "      <td>main</td>\n", "      <td>Apache License 2.0</td>\n", "      <td>...</td>\n", "      <td>2024-11-10 03:55:58</td>\n", "      <td>a9d016c59db782c85d20da1122ab0f9aad973983</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"Zig\":3523195,\"Python\":18001,\"Shell\":3512}</td>\n", "      <td>bug;documentation;duplicate;enhancement;featur...</td>\n", "      <td>NaN</td>\n", "      <td>539 days 23:01:10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>862</th>\n", "      <td>94701317</td>\n", "      <td>zig-gamedev/zig-gamedev</td>\n", "      <td>False</td>\n", "      <td>4812</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>173</td>\n", "      <td>Zig</td>\n", "      <td>main</td>\n", "      <td>MIT License</td>\n", "      <td>...</td>\n", "      <td>2024-11-07 08:26:01</td>\n", "      <td>ce7b5af913905c401305cefc37ec132f0ebd2d0e</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>{\"C\":33988856,\"C++\":19603286,\"Objective-C\":706...</td>\n", "      <td>breaking;bug;demo;documentation;duplicate;enha...</td>\n", "      <td>cross-platform;d3d12;demos;directx12;game-deve...</td>\n", "      <td>1210 days 06:11:09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>863 rows × 36 columns</p>\n", "</div>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "execution_count": 12}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T10:22:07.351910Z", "start_time": "2024-11-13T10:22:07.315364Z"}}, "cell_type": "code", "source": "sample_20.describe()", "id": "ef1d6f19e438496b", "outputs": [{"data": {"text/plain": ["                 id        commits     branches     releases         forks  \\\n", "count  8.630000e+02     863.000000   863.000000   863.000000    863.000000   \n", "mean   6.521782e+07    3390.156431    31.706837    31.867903    377.468134   \n", "min    9.528000e+03     100.000000     1.000000     0.000000      6.000000   \n", "25%    1.514980e+07     335.000000     3.000000     0.000000     54.000000   \n", "50%    9.476934e+07     781.000000     8.000000     8.000000    119.000000   \n", "75%    9.499518e+07    2077.500000    20.000000    29.000000    308.500000   \n", "max    9.522212e+07  703459.000000  3317.000000  1000.000000  14069.000000   \n", "std    3.825846e+07   24962.051094   160.383541    85.879015    931.310922   \n", "\n", "          watchers    stargazers  contributors          size  \\\n", "count   863.000000    863.000000    863.000000  8.630000e+02   \n", "mean     70.033604   2446.272306     52.398610  1.063265e+05   \n", "min       2.000000    100.000000     10.000000  7.300000e+01   \n", "25%      15.000000    268.500000     15.000000  1.335000e+03   \n", "50%      31.000000    632.000000     27.000000  8.077000e+03   \n", "75%      64.500000   1952.500000     57.000000  4.071100e+04   \n", "max    2151.000000  74184.000000    464.000000  1.498032e+07   \n", "std     128.193200   5885.358665     69.211962  7.088408e+05   \n", "\n", "                           createdAt   totalIssues   openIssues  \\\n", "count                            863    863.000000   863.000000   \n", "mean   2016-09-19 12:35:46.903823872    520.614137   105.674392   \n", "min              2008-06-25 05:56:39      2.000000     1.000000   \n", "25%              2014-05-16 06:40:34     68.000000    14.000000   \n", "50%              2016-07-28 03:04:13    167.000000    34.000000   \n", "75%       2019-05-08 18:40:49.500000    443.000000    90.500000   \n", "max              2023-09-02 07:07:14  42015.000000  9167.000000   \n", "std                              NaN   1796.837492   390.701676   \n", "\n", "       totalPullRequests  openPullRequests    blankLines     codeLines  \\\n", "count         863.000000        863.000000  8.630000e+02  8.630000e+02   \n", "mean          888.882966         19.301275  1.731139e+04  1.379903e+05   \n", "min             5.000000          1.000000  3.100000e+01  1.100000e+02   \n", "25%            66.000000          2.000000  8.585000e+02  5.142500e+03   \n", "50%           146.000000          5.000000  2.821000e+03  1.883000e+04   \n", "75%           449.000000         12.000000  9.905000e+03  7.451800e+04   \n", "max        310325.000000       6333.000000  1.465699e+06  6.931994e+06   \n", "std         10632.851174        216.799542  6.619654e+04  4.664278e+05   \n", "\n", "        commentLines                     lastCommit  \\\n", "count     863.000000                            863   \n", "mean    20454.025492  2023-08-14 17:48:31.763615232   \n", "min         0.000000            2013-02-17 01:50:09   \n", "25%       482.500000     2023-06-19 04:03:29.500000   \n", "50%      1913.000000            2024-09-12 09:01:33   \n", "75%     10190.500000            2024-11-05 02:37:55   \n", "max    922084.000000            2024-11-12 12:37:16   \n", "std     66527.672887                            NaN   \n", "\n", "                           duration  \n", "count                           863  \n", "mean   2520 days 05:12:44.859791392  \n", "min               389 days 23:33:31  \n", "25%              1539 days 21:27:05  \n", "50%              2388 days 01:16:11  \n", "75%              3411 days 10:11:30  \n", "max              5837 days 05:45:49  \n", "std    1197 days 03:48:53.334529776  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>commits</th>\n", "      <th>branches</th>\n", "      <th>releases</th>\n", "      <th>forks</th>\n", "      <th>watchers</th>\n", "      <th>stargazers</th>\n", "      <th>contributors</th>\n", "      <th>size</th>\n", "      <th>createdAt</th>\n", "      <th>totalIssues</th>\n", "      <th>openIssues</th>\n", "      <th>totalPullRequests</th>\n", "      <th>openPullRequests</th>\n", "      <th>blankLines</th>\n", "      <th>codeLines</th>\n", "      <th>commentLines</th>\n", "      <th>lastCommit</th>\n", "      <th>duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>8.630000e+02</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>8.630000e+02</td>\n", "      <td>863</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>863.000000</td>\n", "      <td>8.630000e+02</td>\n", "      <td>8.630000e+02</td>\n", "      <td>863.000000</td>\n", "      <td>863</td>\n", "      <td>863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>6.521782e+07</td>\n", "      <td>3390.156431</td>\n", "      <td>31.706837</td>\n", "      <td>31.867903</td>\n", "      <td>377.468134</td>\n", "      <td>70.033604</td>\n", "      <td>2446.272306</td>\n", "      <td>52.398610</td>\n", "      <td>1.063265e+05</td>\n", "      <td>2016-09-19 12:35:46.903823872</td>\n", "      <td>520.614137</td>\n", "      <td>105.674392</td>\n", "      <td>888.882966</td>\n", "      <td>19.301275</td>\n", "      <td>1.731139e+04</td>\n", "      <td>1.379903e+05</td>\n", "      <td>20454.025492</td>\n", "      <td>2023-08-14 17:48:31.763615232</td>\n", "      <td>2520 days 05:12:44.859791392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>9.528000e+03</td>\n", "      <td>100.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>6.000000</td>\n", "      <td>2.000000</td>\n", "      <td>100.000000</td>\n", "      <td>10.000000</td>\n", "      <td>7.300000e+01</td>\n", "      <td>2008-06-25 05:56:39</td>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>5.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3.100000e+01</td>\n", "      <td>1.100000e+02</td>\n", "      <td>0.000000</td>\n", "      <td>2013-02-17 01:50:09</td>\n", "      <td>389 days 23:33:31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.514980e+07</td>\n", "      <td>335.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>54.000000</td>\n", "      <td>15.000000</td>\n", "      <td>268.500000</td>\n", "      <td>15.000000</td>\n", "      <td>1.335000e+03</td>\n", "      <td>2014-05-16 06:40:34</td>\n", "      <td>68.000000</td>\n", "      <td>14.000000</td>\n", "      <td>66.000000</td>\n", "      <td>2.000000</td>\n", "      <td>8.585000e+02</td>\n", "      <td>5.142500e+03</td>\n", "      <td>482.500000</td>\n", "      <td>2023-06-19 04:03:29.500000</td>\n", "      <td>1539 days 21:27:05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>9.476934e+07</td>\n", "      <td>781.000000</td>\n", "      <td>8.000000</td>\n", "      <td>8.000000</td>\n", "      <td>119.000000</td>\n", "      <td>31.000000</td>\n", "      <td>632.000000</td>\n", "      <td>27.000000</td>\n", "      <td>8.077000e+03</td>\n", "      <td>2016-07-28 03:04:13</td>\n", "      <td>167.000000</td>\n", "      <td>34.000000</td>\n", "      <td>146.000000</td>\n", "      <td>5.000000</td>\n", "      <td>2.821000e+03</td>\n", "      <td>1.883000e+04</td>\n", "      <td>1913.000000</td>\n", "      <td>2024-09-12 09:01:33</td>\n", "      <td>2388 days 01:16:11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.499518e+07</td>\n", "      <td>2077.500000</td>\n", "      <td>20.000000</td>\n", "      <td>29.000000</td>\n", "      <td>308.500000</td>\n", "      <td>64.500000</td>\n", "      <td>1952.500000</td>\n", "      <td>57.000000</td>\n", "      <td>4.071100e+04</td>\n", "      <td>2019-05-08 18:40:49.500000</td>\n", "      <td>443.000000</td>\n", "      <td>90.500000</td>\n", "      <td>449.000000</td>\n", "      <td>12.000000</td>\n", "      <td>9.905000e+03</td>\n", "      <td>7.451800e+04</td>\n", "      <td>10190.500000</td>\n", "      <td>2024-11-05 02:37:55</td>\n", "      <td>3411 days 10:11:30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.522212e+07</td>\n", "      <td>703459.000000</td>\n", "      <td>3317.000000</td>\n", "      <td>1000.000000</td>\n", "      <td>14069.000000</td>\n", "      <td>2151.000000</td>\n", "      <td>74184.000000</td>\n", "      <td>464.000000</td>\n", "      <td>1.498032e+07</td>\n", "      <td>2023-09-02 07:07:14</td>\n", "      <td>42015.000000</td>\n", "      <td>9167.000000</td>\n", "      <td>310325.000000</td>\n", "      <td>6333.000000</td>\n", "      <td>1.465699e+06</td>\n", "      <td>6.931994e+06</td>\n", "      <td>922084.000000</td>\n", "      <td>2024-11-12 12:37:16</td>\n", "      <td>5837 days 05:45:49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.825846e+07</td>\n", "      <td>24962.051094</td>\n", "      <td>160.383541</td>\n", "      <td>85.879015</td>\n", "      <td>931.310922</td>\n", "      <td>128.193200</td>\n", "      <td>5885.358665</td>\n", "      <td>69.211962</td>\n", "      <td>7.088408e+05</td>\n", "      <td>NaN</td>\n", "      <td>1796.837492</td>\n", "      <td>390.701676</td>\n", "      <td>10632.851174</td>\n", "      <td>216.799542</td>\n", "      <td>6.619654e+04</td>\n", "      <td>4.664278e+05</td>\n", "      <td>66527.672887</td>\n", "      <td>NaN</td>\n", "      <td>1197 days 03:48:53.334529776</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T10:22:07.482141Z", "start_time": "2024-11-13T10:22:07.445569Z"}}, "cell_type": "code", "source": "sample_20.to_csv('../data/sample_projects_20.csv')", "id": "3c641a63a6a4f815", "outputs": [], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2024-11-13T10:22:07.693379Z", "start_time": "2024-11-13T10:22:07.686886Z"}}, "cell_type": "code", "source": "sample_20.value_counts('isArchived')", "id": "8e2d4140a13c6223", "outputs": [{"data": {"text/plain": ["isArchived\n", "False    832\n", "True      31\n", "Name: count, dtype: int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "execution_count": 15}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "3f846ed040fb76c8"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}