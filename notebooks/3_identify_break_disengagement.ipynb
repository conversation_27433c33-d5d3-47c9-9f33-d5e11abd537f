{"cells": [{"cell_type": "code", "execution_count": 2, "id": "af3e1285be60e473", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:09:26.908843Z", "start_time": "2024-11-25T13:09:26.778252Z"}}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "d570f42a1582c74a", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:09:40.063749Z", "start_time": "2024-11-25T13:09:40.058673Z"}}, "outputs": [], "source": ["def get_processed_commit_file_repo_name(repo_name):\n", "    output_path = f\"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv\"\n", "    repo_commit = pd.read_csv(output_path)\n", "    if repo_commit.empty:\n", "        raise ValueError(\"The processed commit file is empty.\")\n", "        return None\n", "    return repo_commit\n", "\n", "def get_commit_file_repo_name(repo_name):\n", "    file_path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "    repo_commit = pd.read_csv(file_path)\n", "    # Exclude merge commits with those who have two values in the columns named 'parent_shas'\n", "    repo_commit = repo_commit[repo_commit['parent_shas'].apply(lambda x: len(eval(x)) < 2)].reset_index(drop=True)\n", "    # Exclude bot accounts\n", "    bot_developers = pd.read_csv('../data/bot_developer_list_original.csv')\n", "    bot_developers = bot_developers['bot_name'].tolist()\n", "    repo_commit = repo_commit[~repo_commit['author_login'].isin(bot_developers)].reset_index(drop=True)\n", "    repo_commit = repo_commit[~repo_commit['author_name'].isin(bot_developers)].reset_index(drop=True)\n", "    return repo_commit"]}, {"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-11-27T07:12:32.321729Z", "start_time": "2024-11-27T07:12:31.589018Z"}, "collapsed": true}, "outputs": [], "source": ["# import logging\n", "# from pymongo import MongoClient\n", "# import pandas as pd\n", "\n", "# # Configure logging\n", "# logging.basicConfig(\n", "#     level=logging.INFO,\n", "#     format=\"%(asctime)s - %(levelname)s - %(message)s\"\n", "# )\n", "\n", "# def process_repo_data_and_store(project_names, core_devs_data):\n", "#     \"\"\"\n", "#     Process data for each repository based on its commits and core developers,\n", "#     then store the results in MongoDB incrementally.\n", "\n", "#     Args:\n", "#         project_names (pd.DataFrame): Names for all repositories.\n", "#         core_devs_data (pd.DataFrame): Core developers for each repository.\n", "#     \"\"\"\n", "#     def process_repo_data(project_names, core_devs_data, collection):\n", "#         # Ensure 'core_developers' column is correctly processed\n", "#         logging.info(\"Processing core developers data.\")\n", "#         core_devs_data['core_developers'] = core_devs_data['core_developers'].apply(\n", "#             lambda x: x if isinstance(x, list) else eval(x)\n", "#         )\n", "\n", "#         # Process each repository with a sequential project_id\n", "#         project_counter = 0\n", "#         for repo_name in project_names:\n", "#             logging.info(f\"Processing repository: {repo_name} (Project ID: {project_counter})\")\n", "\n", "#             # Fetch commit data for the current repository\n", "#             try:\n", "#                 repo_commits = get_commit_file_repo_name(repo_name)\n", "#             except Exception as e:\n", "#                 logging.error(f\"Error fetching commit data for repository '{repo_name}': {e}\")\n", "#                 continue\n", "\n", "#             # Get core developers for the repository\n", "#             try:\n", "#                 core_developers = core_devs_data[core_devs_data['repo_name'] == repo_name]['core_developers'].iloc[0]\n", "#                 logging.debug(f\"Core developers for # {project_counter} '{repo_name}': {core_developers}\")\n", "#             except Exception as e:\n", "#                 logging.error(f\"Error fetching core developers for repository '{repo_name}': {e}\")\n", "#                 continue\n", "\n", "#             # Process each core developer for the repository\n", "#             core_dev_counter = 0\n", "#             for core_dev in core_developers:\n", "#                 logging.debug(f\"# {project_counter} '{repo_name}' -- Processing core developer: {core_dev}\")\n", "\n", "#                 # Filter commits by the core developer\n", "#                 dev_commits = repo_commits[repo_commits['author_login'] == core_dev]\n", "#                 dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')\n", "#                 dev_commits = dev_commits.sort_values(by='date')\n", "\n", "#                 # Calculate inactivity periods using sliding window (5.1.2)\n", "#                 commit_dates = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ').reset_index(drop=True)\n", "#                 def get_pauses_with_commit(dev_commits):\n", "#                     pauses = {\n", "#                     }\n", "#                     pause_id = 0\n", "#                     for i in range(1, len(dev_commits)):\n", "#                         days_diff = (pd.to_datetime(dev_commits.iloc[i]['date']) - pd.to_datetime(dev_commits.iloc[i - 1]['date'])).days\n", "#                         # if days > 0, then it is a pause\n", "#                         if days_diff > 0:\n", "#                             pauses[pause_id] = {\n", "#                                 'start_date': dev_commits.iloc[i - 1]['date'],\n", "#                                 'end_date': dev_commits.iloc[i]['date'],\n", "#                                 'duration_days': days_diff\n", "#                             }\n", "#                             pause_id += 1\n", "#                     logging.debug(f\"# {project_counter} '{repo_name}' -- Pauses: {pauses}\")\n", "#                     return pauses\n", "#                 # pauses = commit_dates.diff().dt.days.dropna()\n", "#                 # pauses = pauses[pauses > 0].reset_index(drop=True)\n", "#                 pauses = get_pauses_with_commit(dev_commits)\n", "#                 # Identify long pauses\n", "#                 dev_output = {\n", "#                     \"repo_id\": project_counter,\n", "#                     \"repo_name\": repo_name,\n", "#                     \"core_dev_id\": core_dev_counter,\n", "#                     \"core_dev_login\": core_dev,\n", "#                     \"Attrition\": None,\n", "#                     \"Breaks\": []\n", "#                 }\n", "#                 core_dev_counter += 1\n", "\n", "#                 if len(pauses) > 0:\n", "#                     pauses = pd.<PERSON><PERSON>rame(pauses).T\n", "#                     pauses_days = pauses['duration_days']\n", "#                     logging.debug(f\"# {project_counter} '{repo_name}' -- Pauses: {pauses}\")\n", "#                     if not pauses_days.empty:\n", "#                         Q1 = pauses_days.quantile(0.25)\n", "#                         Q3 = pauses_days.quantile(0.75)\n", "#                         IQR = Q3 - Q1\n", "#                         Tfov = Q3 + 3 * IQR\n", "#                         logging.debug(f\"# {project_counter} '{repo_name}' -- Q1: {Q1}, Q3: {Q3}, IQR: {IQR}, Tfov: {Tfov}\")\n", "#                         long_pauses = pauses[pauses['duration_days'] > Tfov].reset_index(drop=True)\n", "#                         logging.debug(f\"# {project_counter} '{repo_name}' -- Long pauses: {long_pauses}\")\n", "\n", "#                         break_counter = 0\n", "#                         for pause_index in long_pauses.index:\n", "#                             try:\n", "#                                 start_date = long_pauses.loc[pause_index, 'start_date']\n", "#                                 end_date = long_pauses.loc[pause_index, 'end_date']\n", "#                                 duration_days = long_pauses.loc[pause_index, 'duration_days']\n", "#                                 dev_output[\"Breaks\"].append({\n", "#                                     \"break_id\": break_counter,\n", "#                                     \"start_date\": str(start_date.date()),\n", "#                                     \"end_date\": str(end_date.date()),\n", "#                                     \"duration_days\": duration_days\n", "#                                 })\n", "#                                 break_counter += 1\n", "#                                 logging.info(f\"# {project_counter} '{repo_name}' -- Break recorded for {core_dev}: {start_date.date()} to {end_date.date()} ({duration_days} days)\")\n", "#                             except IndexError as e:\n", "#                                 logging.error(f\"Error processing pause for {core_dev}: {e}\")\n", "#                                 continue\n", "#                         last_commit_date = commit_dates.max()\n", "#                         project_last_commit_date = pd.to_datetime(repo_commits['date']).max()\n", "\n", "#                         # Ensure both dates are timezone-naive\n", "#                         last_commit_date = last_commit_date.tz_localize(None)\n", "#                         project_last_commit_date = project_last_commit_date.tz_localize(None)\n", "\n", "#                         if (project_last_commit_date - last_commit_date).days > 365:\n", "#                             dev_output[\"Attrition\"] = {\n", "#                                 \"attrition_date\": str(last_commit_date.date())\n", "#                             }\n", "#                             logging.info(f\"# {project_counter} '{repo_name}' -- Developer {core_dev} marked as disengaged since {last_commit_date.date()}.\")\n", "#                 try:\n", "#                     collection.insert_one(dev_output)\n", "#                     logging.info(f\"# {project_counter} '{repo_name}' -- Data for core developer {core_dev} stored in MongoDB.\")\n", "#                 except Exception as e:\n", "#                     logging.error(f\"Error storing data for core developer {core_dev} in MongoDB: {e}\")\n", "#             project_counter += 1\n", "\n", "#     # MongoDB Connection\n", "#     logging.info(\"Connecting to MongoDB.\")\n", "#     WINDOWS_IP = \"localhost\"\n", "#     PORT = 27017\n", "#     client = MongoClient(f\"mongodb://{WINDOWS_IP}:{PORT}/\")\n", "#     db = client[\"disengagement\"]\n", "#     # collection = db[\"project_analysis\"]\n", "#     # create collection 'project_analysis'\n", "#     collection = db['project_analysis']\n", "\n", "#     collection.create_collection('project_analysis', ignore_existing=True)\n", "#     # Set unique index for collection of repo_name and core_dev_id\n", "#     collection.create_index([(\"repo_name\", 1), (\"core_dev_id\", 1)], unique=True)\n", "#     # Process the data and store incrementally\n", "#     for project_name in project_names:\n", "#         core_devs = core_devs_data[core_devs_data['project_name'] == project_name]\n", "#         process_repo_data(project_name, core_devs, collection)\n", "#     logging.info(\"Data processing completed.\")\n", "\n", "#     logging.info(\"Data successfully stored in MongoDB.\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cc3e9760a45ae481", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:10:30.084113Z", "start_time": "2024-11-25T13:10:29.986009Z"}}, "outputs": [], "source": ["# project_sampled = pd.read_csv('../data/sample_projects_quartiles.csv')\n", "# project_names = project_sampled['name'].tolist()\n", "core_developers_df = pd.read_csv('../data/core_developer_list_total_repo.csv')\n", "# sample 10 from the project_names\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ae19a863b85dc229", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:10:50.241312Z", "start_time": "2024-11-25T13:10:50.229986Z"}}, "outputs": [], "source": ["import logging\n", "from pymongo import MongoClient\n", "import pandas as pd\n", "from datetime import timedelta\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\"\n", ")\n", "\n", "def generate_activity_table_and_export(repo_name):\n", "    \"\"\"\n", "    Reads data from MongoDB, processes it into a CSV with the specified format.\n", "\n", "    Args:\n", "        repo_name (str): Name of the repository to filter data.\n", "    Returns:\n", "        pd.DataFrame: Pivot table with activity statuses.\n", "    \"\"\"\n", "    # MongoDB Connection\n", "    logging.info(\"Connecting to MongoDB.\")\n", "    WINDOWS_IP = \"localhost\"\n", "    PORT = 27017\n", "    client = MongoClient(f\"mongodb://{WINDOWS_IP}:{PORT}/\")\n", "    db = client[\"disengagement\"]\n", "    collection = db[\"project_analysis\"]\n", "\n", "    # Fetch data from MongoDB\n", "    logging.info(f\"Fetching data for repo: {repo_name}\")\n", "    data = list(collection.find({\"repo_name\": repo_name}))\n", "\n", "    # If no data is found, log and return an empty DataFrame\n", "    if not data:\n", "        logging.warning(f\"No data found for repo: {repo_name}\")\n", "        return pd.DataFrame()\n", "\n", "    # Fetch commit data and determine the date range\n", "    commits = get_commit_file_repo_name(repo_name)\n", "     # df['date'] = df['date'].apply(lambda x: x.tz_localize(None) if x.tzinfo is not None else x)\n", "    # Ensure all dates in the DataFrame are timezone-naive\n", "    def parse_date(date_str):\n", "        \"\"\"Attempt to parse a date string into yyyy-mm-dd format.\"\"\"\n", "        try:\n", "            # First attempt: ISO8601\n", "            return pd.to_datetime(date_str, format='%Y-%m-%dT%H:%M:%SZ').strftime('%Y-%m-%d')\n", "        except ValueError:\n", "            logging.warning(f\"Failed to parse date as ISO8601: {date_str}\")\n", "            try:\n", "                # Second attempt: Flexible parsing (mixed formats)\n", "                return pd.to_datetime(date_str, format='mixed').strftime('%Y-%m-%d')\n", "            except Exception as e:\n", "                logging.error(f\"Failed to parse date entirely: {date_str}, Error: {e}\")\n", "                return None\n", "    commits['date'] = commits['date'].apply(parse_date)\n", "    start_date = commits['date'].min()\n", "    end_date = commits['date'].max()\n", "    date_range = pd.date_range(start=start_date, end=end_date)\n", "\n", "    all_rows = []\n", "\n", "    for record in data:\n", "        repo_id = record[\"repo_id\"]\n", "        core_dev_id = record[\"core_dev_id\"]\n", "        core_dev_login = record[\"core_dev_login\"]\n", "\n", "        # Fetch developer-specific commit data\n", "        core_dev_commit = commits[commits['author_login'] == core_dev_login]\n", "        core_dev_commit_start_date = pd.to_datetime(core_dev_commit['date']).min()\n", "        core_dev_commit_end_date = pd.to_datetime(core_dev_commit['date']).max()\n", "        core_dev_commit_dates = pd.date_range(start=core_dev_commit_start_date, end=core_dev_commit_end_date)\n", "\n", "        # Initialize activity map with 0\n", "        activity_map = {date: 0 for date in date_range}\n", "\n", "        # Mark active commits as 1\n", "        for commit_date in core_dev_commit_dates:\n", "            activity_map[commit_date] = 1\n", "\n", "        # <PERSON> breaks as 2\n", "        for break_record in record.get(\"Breaks\", []):\n", "            break_start = pd.to_datetime(break_record[\"start_date\"])\n", "            break_end = pd.to_datetime(break_record[\"end_date\"])\n", "            for date in pd.date_range(start=break_start, end=break_end):\n", "                activity_map[date] = 2\n", "\n", "        # Mark attrition as -1\n", "        if record.get(\"Attrition\"):\n", "            attrition_date = pd.to_datetime(record[\"Attrition\"][\"attrition_date\"]).tz_localize(None)\n", "            for date in date_range:\n", "                if date >= attrition_date:\n", "                    activity_map[date] = -1\n", "\n", "        # Append rows for each date\n", "        for date, status in activity_map.items():\n", "            all_rows.append({\n", "                \"repo_id\": repo_id,\n", "                \"repo_name\": repo_name,\n", "                \"core_dev_id\": core_dev_id,\n", "                \"core_dev_login\": core_dev_login,\n", "                \"date\": date.strftime('%y-%m-%d'),  # Format date as 'yy-mm-dd'\n", "                \"status\": status\n", "            })\n", "\n", "    # Create a DataFrame\n", "    df = pd.DataFrame(all_rows)\n", "    # df['date'] = df['date'].apply(lambda x: x.tz_localize(None) if x.tzinfo is not None else x)\n", "    logging.info(\"Activity table created.\")\n", "\n", "    # Pivot table\n", "    pivot_table = df.pivot_table(\n", "        index=[\"repo_id\", \"repo_name\", \"core_dev_id\", \"core_dev_login\"],\n", "        columns=\"date\",\n", "        values=\"status\",\n", "        fill_value=0\n", "    )\n", "    logging.info(\"Pivot table created.\")\n", "\n", "    return pivot_table\n"]}, {"cell_type": "code", "execution_count": null, "id": "953ace3993c4d8df", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b0cafb77bf7526ff", "metadata": {}, "outputs": [], "source": ["\n", "# activity_table = generate_activity_table_and_export(repo_name)"]}, {"cell_type": "code", "execution_count": null, "id": "4c88c54bee0343db", "metadata": {}, "outputs": [], "source": ["# activity_table"]}, {"cell_type": "code", "execution_count": null, "id": "3a52cf867a5c851c", "metadata": {}, "outputs": [], "source": ["# activity_table.to_csv('../data/activity_table_temp.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "d84df64f813f396e", "metadata": {}, "outputs": [], "source": ["# print(activity_table.columns)"]}, {"cell_type": "code", "execution_count": null, "id": "abfca1acc28bc60d", "metadata": {}, "outputs": [], "source": ["# activity_table = pd.read_csv('../data/activity_table_temp.csv')\n", "# activity_table"]}, {"cell_type": "code", "execution_count": 4, "id": "def5c2a4dc8f0ba9", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:11:02.317828Z", "start_time": "2024-11-25T13:11:02.305346Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "def identify_intervals(activity_table):\n", "    \"\"\"\n", "    Identify contiguous intervals of the same status for each developer.\n", "\n", "    Args:\n", "        activity_table (pd.DataFrame): Activity table with metadata and date columns.\n", "\n", "    Returns:\n", "        pd.DataFrame: Summary table with start_date, end_date, and status for each interval.\n", "    \"\"\"\n", "    # Inspect the column names\n", "    # print(\"Columns in the DataFrame:\", activity_table.columns)\n", "\n", "    # Update metadata_cols with actual column names from your DataFrame\n", "    metadata_cols = ['repo_id', 'repo_name', 'core_dev_id', 'core_dev_login']  # Replace these with actual names\n", "    date_cols = [col for col in activity_table.columns if col not in metadata_cols]\n", "\n", "    # Melt the activity table\n", "    melted_table = activity_table.melt(id_vars=metadata_cols, value_vars=date_cols,\n", "                                       var_name='date', value_name='status')\n", "    melted_table['date'] = pd.to_datetime(melted_table['date'], format='%y-%m-%d', errors='coerce')\n", "\n", "    # Drop invalid rows\n", "    melted_table.dropna(subset=['date'], inplace=True)\n", "\n", "    # Sort by developer and date\n", "    melted_table.sort_values(by=['repo_name', 'core_dev_login', 'date'], inplace=True)\n", "\n", "    # Identify intervals\n", "    intervals = []\n", "    for (repo_name, core_dev_login), group in melted_table.groupby(['repo_name', 'core_dev_login']):\n", "        group = group.reset_index(drop=True)\n", "        start_date, status = group.loc[0, ['date', 'status']]\n", "\n", "        for i in range(1, len(group)):\n", "            current_date, current_status = group.loc[i, ['date', 'status']]\n", "            if current_status != status:\n", "                # Save the interval\n", "                intervals.append({\n", "                    'repo_name': repo_name,\n", "                    'core_dev_login': core_dev_login,\n", "                    'start_date': start_date,\n", "                    'end_date': group.loc[i - 1, 'date'],\n", "                    'status': status\n", "                })\n", "                # Start a new interval\n", "                start_date, status = current_date, current_status\n", "\n", "        # Save the last interval\n", "        intervals.append({\n", "            'repo_name': repo_name,\n", "            'core_dev_login': core_dev_login,\n", "            'start_date': start_date,\n", "            'end_date': group.loc[len(group) - 1, 'date'],\n", "            'status': status\n", "        })\n", "\n", "    return pd.DataFrame(intervals)\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "def plot_intervals(intervals_df):\n", "    \"\"\"\n", "    Plot intervals as a Gantt-like chart for each developer.\n", "\n", "    Args:\n", "        intervals_df (pd.DataFrame): Summary table with intervals.\n", "    \"\"\"\n", "    for repo_name in intervals_df['repo_name'].unique():\n", "        repo_intervals = intervals_df[intervals_df['repo_name'] == repo_name]\n", "\n", "        plt.figure(figsize=(12, 6))\n", "        seen = set()  # To track added labels for the legend\n", "        for i, (dev, group) in enumerate(repo_intervals.groupby('core_dev_login')):\n", "            for _, row in group.iterrows():\n", "                color = {0: 'gray', 1: 'green', 2: 'orange', -1: 'red'}.get(row['status'], 'white')\n", "                label = None\n", "                if row['status'] not in seen:\n", "                    label = {0: 'Before 1st commit', 1: 'Active', 2: 'Break', -1: 'Attrition'}[row['status']]\n", "                    seen.add(row['status'])\n", "\n", "                plt.barh(i, (row['end_date'] - row['start_date']).days + 1,\n", "                         left=row['start_date'], color=color, edgecolor='black', label=label)\n", "\n", "        plt.title(f\"Developer Activity Intervals for {repo_name}\", fontsize=16)\n", "        plt.xlabel(\"Date\", fontsize=12)\n", "        plt.ylabel(\"Core Developer Login\", fontsize=12)\n", "        plt.yticks(range(len(repo_intervals['core_dev_login'].unique())),\n", "                   repo_intervals['core_dev_login'].unique())\n", "        plt.legend(title=\"Status\")\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "\n", "# # Identify intervals and plot\n", "# intervals_df = identify_intervals(activity_table)\n", "# plot_intervals(intervals_df)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "7e66bbd7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-26 21:43:27,966 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Starting topology monitoring\"}\n", "2025-02-26 21:43:27,966 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"previousDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Unknown, servers: []>\", \"newDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>\", \"message\": \"Topology description changed\"}\n", "2025-02-26 21:43:27,966 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"message\": \"Starting server monitoring\"}\n", "2025-02-26 21:43:27,967 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection pool created\", \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,968 - DEBUG - {\"message\": \"Server selection started\", \"selector\": \"Primary()\", \"operation\": \"find\", \"topologyDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>\", \"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}}\n", "2025-02-26 21:43:27,968 - DEBUG - {\"message\": \"Waiting for suitable server to become available\", \"selector\": \"Primary()\", \"operation\": \"find\", \"topologyDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>\", \"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"remainingTimeMS\": 29}\n", "2025-02-26 21:43:27,968 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": false, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:43:27,969 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": false, \"durationMS\": 0.38108089938759804, \"reply\": \"{\\\"helloOk\\\": true, \\\"ismaster\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:43:27.969Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:43:27,969 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection pool ready\", \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,970 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"previousDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>\", \"newDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Single, servers: [<ServerDescription ('localhost', 27017) server_type: Standalone, rtt: 0.00038108089938759804>]>\", \"message\": \"Topology description changed\"}\n", "2025-02-26 21:43:27,970 - DEBUG - {\"message\": \"Server selection succeeded\", \"selector\": \"Primary()\", \"operation\": \"find\", \"topologyDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Single, servers: [<ServerDescription ('localhost', 27017) server_type: Standalone, rtt: 0.00038108089938759804>]>\", \"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,970 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:43:27,970 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection checkout started\", \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,971 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection created\", \"serverHost\": \"localhost\", \"serverPort\": 27017, \"driverConnectionId\": 1}\n", "2025-02-26 21:43:27,971 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection ready\", \"serverHost\": \"localhost\", \"serverPort\": 27017, \"driverConnectionId\": 1, \"durationMS\": 0.00023794383741915226}\n", "2025-02-26 21:43:27,972 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection checked out\", \"serverHost\": \"localhost\", \"serverPort\": 27017, \"driverConnectionId\": 1, \"durationMS\": 0.0014649140648543835}\n", "2025-02-26 21:43:27,972 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Command started\", \"command\": \"{\\\"find\\\": \\\"progress_cache\\\", \\\"filter\\\": {\\\"commits_finished\\\": 1, \\\"pr_finished\\\": 1, \\\"pr_review_finished\\\": 1}, \\\"projection\\\": {\\\"repo_name\\\": 1}, \\\"lsid\\\": {\\\"id\\\": {\\\"$binary\\\": {\\\"base64\\\": \\\"gZX62+13TdGbfiMAIRn5cQ==\\\", \\\"subType\\\": \\\"04\\\"}}}, \\\"$db\\\": \\\"disengagement\\\"}\", \"commandName\": \"find\", \"databaseName\": \"disengagement\", \"requestId\": 2044897763, \"operationId\": 2044897763, \"driverConnectionId\": 1, \"serverConnectionId\": 170387, \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,973 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Command succeeded\", \"durationMS\": 1.149, \"reply\": \"{\\\"cursor\\\": {\\\"firstBatch\\\": [{\\\"_id\\\": {\\\"$oid\\\": \\\"679b3cdc47094c4a7ae58793\\\"}, \\\"repo_name\\\": \\\"sparklemotion/nokogiri\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3d4f47094c4a7ae58794\\\"}, \\\"repo_name\\\": \\\"davidb/scala-maven-plugin\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3d6347094c4a7ae58795\\\"}, \\\"repo_name\\\": \\\"tcurdt/jdeb\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3d7747094c4a7ae58796\\\"}, \\\"repo_name\\\": \\\"junit-team/junit4\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3dab47094c4a7ae58797\\\"}, \\\"repo_name\\\": \\\"yui/yuicompressor\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3dbc47094c4a7ae58798\\\"}, \\\"repo_name\\\": \\\"unclebob/fitnesse\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3dda47094c4a7ae58799\\\"}, \\\"repo_name\\\": \\\"connectbot/connectbot\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3df247094c4a7ae5879a\\\"}, \\\"repo_name\\\": \\\"bpellin/keepassdroid\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3e0247094c4a7ae5879b\\\"}, \\\"repo_name\\\": \\\"rnewson/couchdb-lucene\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3e1547094c4a7ae5879c\\\"}, \\\"repo_name\\\": \\\"nodebox/nodebox\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3e2547094c4a7ae5879d\\\"}, \\\"repo_name\\\": \\\"cwensel/cascading\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b3e3747094c4a7ae5879e\\\"}, \\\"repo_name\\\": \\\"cucum...\", \"commandName\": \"find\", \"databaseName\": \"disengagement\", \"requestId\": 2044897763, \"operationId\": 2044897763, \"driverConnectionId\": 1, \"serverConnectionId\": 170387, \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,974 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection checked in\", \"serverHost\": \"localhost\", \"serverPort\": 27017, \"driverConnectionId\": 1}\n", "2025-02-26 21:43:27,974 - DEBUG - {\"message\": \"Server selection started\", \"selector\": \"<function any_server_selector at 0x7900c8b59120>\", \"operation\": \"getMore\", \"topologyDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Single, servers: [<ServerDescription ('localhost', 27017) server_type: Standalone, rtt: 0.00038108089938759804>]>\", \"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}}\n", "2025-02-26 21:43:27,974 - DEBUG - {\"message\": \"Server selection succeeded\", \"selector\": \"<function any_server_selector at 0x7900c8b59120>\", \"operation\": \"getMore\", \"topologyDescription\": \"<TopologyDescription id: 67bf1a7f699e865d09860bd1, topology_type: Single, servers: [<ServerDescription ('localhost', 27017) server_type: Standalone, rtt: 0.00038108089938759804>]>\", \"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,974 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection checkout started\", \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:27,974 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection checked out\", \"serverHost\": \"localhost\", \"serverPort\": 27017, \"driverConnectionId\": 1, \"durationMS\": 0.00013695494271814823}\n", "2025-02-26 21:43:27,974 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Command started\", \"command\": \"{\\\"getMore\\\": 5417224933927340751, \\\"collection\\\": \\\"progress_cache\\\", \\\"lsid\\\": {\\\"id\\\": {\\\"$binary\\\": {\\\"base64\\\": \\\"gZX62+13TdGbfiMAIRn5cQ==\\\", \\\"subType\\\": \\\"04\\\"}}}, \\\"$db\\\": \\\"disengagement\\\"}\", \"commandName\": \"getMore\", \"databaseName\": \"disengagement\", \"requestId\": 1967513926, \"operationId\": 1967513926, \"driverConnectionId\": 1, \"serverConnectionId\": 170387, \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:28,074 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Command succeeded\", \"durationMS\": 99.72399999999999, \"reply\": \"{\\\"cursor\\\": {\\\"nextBatch\\\": [{\\\"_id\\\": {\\\"$oid\\\": \\\"679b489047094c4a7ae587f8\\\"}, \\\"repo_name\\\": \\\"opentsdb/asynchbase\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b48a247094c4a7ae587f9\\\"}, \\\"repo_name\\\": \\\"alexruiz/fest-assert-2.x\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b48b247094c4a7ae587fa\\\"}, \\\"repo_name\\\": \\\"fasterxml/aalto-xml\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b48c247094c4a7ae587fb\\\"}, \\\"repo_name\\\": \\\"blacklabs/play-morphia\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b48d047094c4a7ae587fc\\\"}, \\\"repo_name\\\": \\\"arduino/arduino\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b48e747094c4a7ae587fd\\\"}, \\\"repo_name\\\": \\\"rabbitmq/rabbitmq-java-client\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b48fe47094c4a7ae587fe\\\"}, \\\"repo_name\\\": \\\"dcm4che/dcm4che\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b491247094c4a7ae587ff\\\"}, \\\"repo_name\\\": \\\"errai/errai\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b492847094c4a7ae58800\\\"}, \\\"repo_name\\\": \\\"coremedia/jangaroo-tools\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b493a47094c4a7ae58801\\\"}, \\\"repo_name\\\": \\\"enginehub/worldedit\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b495047094c4a7ae58802\\\"}, \\\"repo_name\\\": \\\"jmock-developers/jmock-library\\\"}, {\\\"_id\\\": {\\\"$oid\\\": \\\"679b496547094c4a7ae58803\\\"...\", \"commandName\": \"getMore\", \"databaseName\": \"disengagement\", \"requestId\": 1967513926, \"operationId\": 1967513926, \"driverConnectionId\": 1, \"serverConnectionId\": 170387, \"serverHost\": \"localhost\", \"serverPort\": 27017}\n", "2025-02-26 21:43:28,075 - DEBUG - {\"clientId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"message\": \"Connection checked in\", \"serverHost\": \"localhost\", \"serverPort\": 27017, \"driverConnectionId\": 1}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-26 21:43:37,974 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"durationMS\": 10003.685309085995, \"reply\": \"{\\\"isWritablePrimary\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:43:37.974Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:43:37,975 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:43:47,985 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"durationMS\": 10009.77988098748, \"reply\": \"{\\\"isWritablePrimary\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:43:47.985Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:43:47,986 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:43:57,995 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"durationMS\": 10009.03479498811, \"reply\": \"{\\\"isWritablePrimary\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:43:57.995Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:43:57,996 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:44:08,006 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"durationMS\": 10009.334567002952, \"reply\": \"{\\\"isWritablePrimary\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:44:08.005Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:44:08,006 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:44:18,015 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"durationMS\": 10008.182683959603, \"reply\": \"{\\\"isWritablePrimary\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:44:18.014Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:44:18,015 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n", "2025-02-26 21:44:28,025 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"durationMS\": 10009.768886025995, \"reply\": \"{\\\"isWritablePrimary\\\": true, \\\"topologyVersion\\\": {\\\"processId\\\": {\\\"$oid\\\": \\\"67aea17c2bc2f82e742c567b\\\"}}, \\\"maxBsonObjectSize\\\": 16777216, \\\"maxMessageSizeBytes\\\": 48000000, \\\"maxWriteBatchSize\\\": 100000, \\\"localTime\\\": {\\\"$date\\\": \\\"2025-02-26T13:44:28.025Z\\\"}, \\\"logicalSessionTimeoutMinutes\\\": 30, \\\"connectionId\\\": 170385, \\\"maxWireVersion\\\": 25, \\\"ok\\\": 1.0}\", \"message\": \"Server heartbeat succeeded\"}\n", "2025-02-26 21:44:28,026 - DEBUG - {\"topologyId\": {\"$oid\": \"67bf1a7f699e865d09860bd1\"}, \"driverConnectionId\": 1, \"serverConnectionId\": 170385, \"serverHost\": \"localhost\", \"serverPort\": 27017, \"awaited\": true, \"message\": \"Server heartbeat started\"}\n"]}], "source": ["client = MongoClient('mongodb://localhost:27017/')  # 替换为你的 MongoDB 实例的 URI\n", "\n", "db = client['disengagement']  # 选择 disengagement 数据库\n", "cache_collection = db[\"progress_cache\"]\n", "finished_projects = cache_collection.find({\n", "    \"commits_finished\": 1,\n", "    \"pr_finished\": 1,\n", "    \"pr_review_finished\": 1\n", "}, {\"repo_name\": 1})\n", "repos = [project[\"repo_name\"] for project in finished_projects]\n", "project_names = repos"]}, {"cell_type": "code", "execution_count": 7, "id": "51375af9ac422fc5", "metadata": {}, "outputs": [], "source": ["import os\n", "failed_repo_list = []\n", "i = 0\n", "for repo_name in project_names:\n", "    file_path = f\"../data/commits/{repo_name.replace('/', '_')}_commits.csv\"\n", "    if not os.path.exists(file_path):\n", "        continue\n", "    if os.path.exists(f\"../data/disengagement_tables/{repo_name.replace('/', '_')}_activity_table.csv\"):\n", "        continue\n", "    try:\n", "        activity_table = generate_activity_table_and_export(repo_name)\n", "    except:\n", "        failed_repo_list.append(repo_name)\n", "        continue\n", "    activity_table.to_csv(f\"../data/disengagement_tables/{repo_name.replace('/', '_')}_activity_table.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "e337b6aa5d526f71", "metadata": {}, "outputs": [], "source": ["# get repo_name list from mongodb\n"]}, {"cell_type": "code", "execution_count": 8, "id": "65905775c3a18612", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:42:05.049311Z", "start_time": "2024-11-25T13:42:05.040345Z"}}, "outputs": [{"data": {"text/plain": ["['nationalsecurityagency/datawave',\n", " 'hashgraph/hedera-mirror-node',\n", " 'pojavlauncherteam/pojavlauncher',\n", " 'bilde2910/hauk',\n", " 'ionic-team/capacitor',\n", " 'confluentinc/bottledwater-pg',\n", " 'swift-nav/piksi_firmware',\n", " 'sustrik/libmill',\n", " 'parrot/parrot',\n", " 'emcraftsystems/linux-emcraft',\n", " 'libtrading/libtrading',\n", " 'endrazine/wcc',\n", " 'kvmtool/kvmtool',\n", " 'clibs/cmocka',\n", " 'pinkavaj/rtl-sdr',\n", " 'andrewrk/libgroove',\n", " 'gerhobbelt/pthread-win32',\n", " 'nezticle/raspberrypi-buildroot',\n", " 'mikachu/openbox',\n", " 'cacalabs/libcaca',\n", " 'charybdis-ircd/charybdis',\n", " 'tats/w3m',\n", " 'keirf/greaseweazle',\n", " 'openprinting/cups',\n", " 'riscv/riscv-tests',\n", " 'private-octopus/picoquic',\n", " 'xebd/accel-ppp',\n", " 'wargus/wargus',\n", " 'yubico/yubico-piv-tool',\n", " 'rstudio/httpuv',\n", " 'freeradius/freeradius-server',\n", " 'osmc/osmc',\n", " 'cc65/cc65',\n", " 'nixos/patchelf',\n", " 'pgbouncer/pgbouncer',\n", " 'libtom/libtomcrypt',\n", " 'unoplatform/uno',\n", " 'quantconnect/lean',\n", " 'eventstore/eventstore',\n", " 'windows-toolkit/windowscommunitytoolkit',\n", " 'files-community/files',\n", " 'thangchung/clean-code-dotnet',\n", " 'masstransit/masstransit',\n", " 'cake-build/cake',\n", " 'pythonnet/pythonnet',\n", " 'azure/azure-powershell',\n", " 'djl/vcprompt',\n", " 'scholrly/neo4django',\n", " 'cloudant/bigcouch',\n", " 'django-nonrel/djangoappengine',\n", " 'pyvideo/richard',\n", " 'erikrose/peep',\n", " 'yelp/python-gearman',\n", " 'joestump/django-ajax',\n", " 'erich<PERSON>cher/django-test-utils',\n", " 'cdr-stats/cdr-stats',\n", " 'wrobstory/vincent',\n", " 'charlesthomas/magpie',\n", " 'tow/sunburnt',\n", " 'bslatkin/dpxdt',\n", " 'pyside/pyside',\n", " 'thunder-project/thunder',\n", " 'bookieio/bookie',\n", " 'braiden/python-ant-downloader',\n", " 'clusterhq/flocker',\n", " 'soumith/convnet-benchmarks',\n", " 'pgq/skytools-legacy',\n", " 'community-libs/vaurien',\n", " 'mythmon/wok',\n", " 'mrkipling/maraschino',\n", " 'joeferraro/mavensmate-sublimetext',\n", " 'pyapi-gitlab/pyapi-gitlab',\n", " 'jamesmeneghello/pynab',\n", " 'esri/arcrest',\n", " 'yadutaf/ctop',\n", " 'mila-iqia/platoon',\n", " 'mknx/smarthome',\n", " 'richq/folders2flickr',\n", " 'osm2vectortiles/osm2vectortiles',\n", " 'sam<PERSON><PERSON>/django-form-designer',\n", " 'mgaitan/sublime-rst-completion',\n", " 'topazproject/topaz',\n", " 'openyou/libfitbit',\n", " 'amplab/spark-ec2',\n", " 'minrk/ipython_extensions',\n", " 'django-nonrel/djangotoolbox',\n", " 'niwinz/djorm-pgarray',\n", " 'graycatlabs/pybbio',\n", " 'disqus/nexus',\n", " 'billymoon/stylus',\n", " 'catehstn/technically-speaking',\n", " 'abakan-zz/ablog',\n", " 'sananth12/imagescraper',\n", " 'mank319/elementaryplus',\n", " 'wagtail/wagtaildemo',\n", " 'astro-pi/python-sense-hat',\n", " 'biocore/qiime',\n", " 'ga4gh/ga4gh-schemas',\n", " 'pycqa/flake8-docstrings',\n", " 'rasbt/pyprind',\n", " 'kubespray/kubespray-cli',\n", " 'openxenmanager/openxenmanager',\n", " 'fvisin/dataset_loaders',\n", " 'dmlc/minpy',\n", " 'pegler/pytzwhere',\n", " 'ridersdiscountcom/hypchat',\n", " 'ceph/calamari',\n", " 'dbgx/lldb.nvim',\n", " 'rosedu/wouso',\n", " 'mardiros/pyshop',\n", " 'lispython/human_curl',\n", " 'rocketmap/rocketmap',\n", " 'imgur/imgurpython',\n", " 'chartit/django-chartit',\n", " '<PERSON><PERSON><PERSON><PERSON>/django-ios-notifications',\n", " 'hovel/pybbm',\n", " 'devstructure/blueprint',\n", " 'appliedsec/pygeoip',\n", " 'mattupstate/flask-social',\n", " 'django-inplaceedit/django-inplaceedit',\n", " 'cornell-brg/pydgin',\n", " 'facebookarchive/augmented-traffic-control',\n", " 'rstacruz/sparkup',\n", " 'floydwch/kaggle-cli',\n", " 'grnet/synnefo',\n", " 'pybrain/pybrain',\n", " 'adamhajari/spyre',\n", " 'condemil/gist',\n", " 'danie<PERSON><PERSON>/hearthbreaker',\n", " 'juli<PERSON><PERSON>ius/sublimepythonide',\n", " 'spadgos/sublime-jsdocs',\n", " 'zapier/django-knowledge',\n", " 'coursera/dataduct',\n", " 'jere<PERSON><PERSON><PERSON>/tablesnap',\n", " 'styxit/htpc-manager',\n", " 'allenai/deep_qa',\n", " 'benoitc/restkit',\n", " 'lmjohns3/theanets',\n", " 'timothycrosley/deprecated.frosted',\n", " 'googlecloudplatform/appengine-mapreduce',\n", " 'autopilotpattern/mysql',\n", " 'allianceauth/allianceauth',\n", " 'tresystechnology/refpolicy',\n", " 'mozilla/sugardough',\n", " 'everware/everware',\n", " 'khan/tinyquery',\n", " 'cbfinn/gps',\n", " 'tensorflowkorea/tensorflow-kr',\n", " 'sebdah/dynamic-dynamodb',\n", " 'sebdah/scrapy-mongodb',\n", " 'projectatomic/container-best-practices',\n", " 'python-rope/ropemacs',\n", " 'jorgebastida/glue',\n", " 'raelgc/scudcloud',\n", " 'benoitc/couchdbkit',\n", " 'ansibleplaybookbundle/ansible-playbook-bundle',\n", " '6si/shipwright',\n", " 'jeff<PERSON><PERSON><PERSON>/sandman',\n", " 'guillermooo/vintageous',\n", " 'roxma/nvim-completion-manager',\n", " 'tildaslash/ratticweb',\n", " 'google/fplutil',\n", " 'floobits/floobits-emacs',\n", " 'labpy/lantz',\n", " 'zeevg/python-forecast.io',\n", " 'nuxeo/funkload',\n", " 'openhatch/oh-mainline',\n", " 'rdegges/django-sslify',\n", " 'vmware/liota',\n", " 'pettazz/pygooglevoice',\n", " 'kelproject/pykube',\n", " 'botbotme/botbot-web',\n", " 'ethereum/eth-hash',\n", " 'benoitc/gaffer',\n", " 'openagriculturefoundation/openag_brain',\n", " 'bmuller/twistar',\n", " 'pacificbiosciences/falcon',\n", " 'globocom/tornado-es',\n", " 'juanpotato/legofy',\n", " 'rll/rllab',\n", " 'lmco/laikaboss',\n", " 'myusuf3/octogit',\n", " 'crypto-toolbox/btfxwss',\n", " 'rackerlabs/lambda-uploader',\n", " 'jasonmillward/autorippr',\n", " 'googlecloudplatform/datastore-ndb-python',\n", " 'jamezq/palaver',\n", " 'rougeth/bottery',\n", " 'antergos/web-greeter',\n", " 'uber/doubles',\n", " 'python-tls/tls',\n", " 'timvideos/streaming-system',\n", " 'gak/pycallgraph',\n", " 'stephenmcd/django-socketio',\n", " 'erikng/cacher',\n", " 'zetaops/ulakbus',\n", " 'danmcinerney/lans.py',\n", " 'geier/pycarddav',\n", " 'daniellawrence/graphitesend',\n", " 'erikrose/nose-progressive',\n", " 'thriftpy/thriftpy',\n", " 'kong/unirest-python',\n", " 'shymonk/django-datatable',\n", " 'philadams-zz/habitica',\n", " 'lmacken/liveusb-creator',\n", " 'chrippa/livestreamer',\n", " 'pogodevorg/pgoapi',\n", " 'watttime/pyiso',\n", " 'espeed/bulbs',\n", " 'djangonauts/django-hstore',\n", " 'kvasirsecurity/kvasir',\n", " 'disqus/nydus',\n", " 'madjar/nox',\n", " 'mgear-dev/mgear',\n", " 'barosl/homu',\n", " 'm0mchil/poclbm',\n", " 'maxcutler/python-wordpress-xmlrpc',\n", " 'jazzband/django-mongonaut',\n", " 'django-cumulus/django-cumulus',\n", " 'ustream/openduty',\n", " 'ctxis/django-admin-view-permission',\n", " 'olgabot/prettyplotlib',\n", " 'yelp/pyleus',\n", " 'quarnster/sublimegdb',\n", " 'okfn/bibserver',\n", " 'jborg/attic',\n", " 'viniciuschiele/flask-apscheduler',\n", " 'jessemiller/hamlpy',\n", " 'mantl/terraform.py',\n", " 'googlearchive/simian',\n", " 'uclnlp/jack',\n", " 'e-dard/flask-s3',\n", " 'swisscom/cleanerversion',\n", " 'mila-iqia/blocks',\n", " 'ly0/baidupcsapi',\n", " 'dialogflow/dialogflow-python-client',\n", " 'datawire/forge',\n", " 'brookslabucsc/flair',\n", " 'laike9m/pdir2',\n", " 'nick-thompson/blueprint',\n", " 'mupen64plus-ae/mupen64plus-ae',\n", " 'friendupcloud/friendup',\n", " 'instantos/instantwm',\n", " 'px4/eigen',\n", " 'sielobrowser/sielo-legacy',\n", " 'velocidex/c-aff4',\n", " 'openjabnab/openjabnab',\n", " 'cocos2d/cocos2d-js',\n", " 'grishka/libtgvoip',\n", " 'xyzz/acquisition',\n", " 'imvu-open/istatd',\n", " 'thelaui/m.a.r.s.',\n", " 'ethereum/evmjit',\n", " 'mbroadst/qamqp',\n", " 'machinekit/qtquickvcp',\n", " 'shadowsocks/shadowsocks-qt5',\n", " 'dena/handlersocket-plugin-for-mysql',\n", " 'catchorg/clara',\n", " 'rbei-etas/busmaster',\n", " 'reverbrain/elliptics',\n", " 'headmyshoulder/odeint-v2',\n", " 'leggedrobotics/free_gait',\n", " 'animallogic/al_usdmaya',\n", " 'foundationdb/fdb-document-layer',\n", " 'oregoncore/oregoncore',\n", " 'eu07/maszyna',\n", " 'uber-archive/pyflame',\n", " 'crosswalk-project/crosswalk',\n", " 'parro-it/libui-node',\n", " 'blizzard/s2client-api',\n", " 'polysync/oscc',\n", " 'mafintosh/fuse-bindings',\n", " 'googlefonts/fontview',\n", " 'rumpkernel/rumprun-packages',\n", " 'primitiv/primitiv',\n", " 'leapmotion/autowiring',\n", " 'ulordchain/ulordchain',\n", " 'facebookarchive/xcbuild',\n", " 'qihoo360/qconf',\n", " 'nitroshare/nitroshare-desktop',\n", " 'victorprad/infinitam',\n", " 'ct-open-source/basecamp',\n", " 'teamwisp/wisprenderer',\n", " 'cybozu/yrmcds',\n", " 'finitespace/bme280',\n", " 'echronos/echronos',\n", " 'genome/pindel',\n", " 'kvasir-io/mpl',\n", " 'p12tic/libsimdpp',\n", " 'behdad/glyphy',\n", " 'dave<PERSON>ill/commandcenter',\n", " 'roy-ht/editdistance',\n", " 'wujian16/cornell-moe',\n", " 'snas/openbmp',\n", " 'scylladb/dpdk',\n", " 'intel/libyami',\n", " 'eglaysher/rlvm',\n", " 'amazon-archives/amazon-dsstne',\n", " 'tekezo/karabiner',\n", " 'facebookresearch/tensorcomprehensions',\n", " 'libxmljs/libxmljs',\n", " 'scanner-research/scanner',\n", " 'pimoroni/unicorn-hat-hd',\n", " 'adacompnus/summit',\n", " 'fluorohydride/ygopro',\n", " 'microsoft/checkedc-clang',\n", " 'alibaba/cicadaplayer',\n", " 'kendryte/nncase',\n", " 'inet-framework/inet',\n", " 'cloudendpoints/esp',\n", " 'zeroc-ice/ice',\n", " 'alicevision/alicevision',\n", " 'stellar-group/hpx',\n", " 'solvespace/solvespace',\n", " 'randombit/botan',\n", " 'notepadqq/notepadqq',\n", " 'toggl-open-source/toggldesktop',\n", " 'openxray/xray-16',\n", " 'leelachesszero/lc0',\n", " 'opentx/opentx',\n", " 'blazingdb/blazingsql',\n", " 'google/souper',\n", " 'khronosgroup/vulkan-samples',\n", " 'fisco-bcos/fisco-bcos',\n", " 'microsoft/projectreunion',\n", " 'winmerge/winmerge',\n", " 'pmem/pmdk',\n", " 'embox/embox',\n", " 'vroom-project/vroom',\n", " 'adventuregamestudio/ags',\n", " 'christo<PERSON><PERSON>/hise',\n", " 'lunarg/vulkantools',\n", " 'paddlepaddle/serving',\n", " 'faasm/faasm',\n", " 'rokups/rbfx',\n", " 'cosmosoftware/obs-studio-webrtc',\n", " 'khronosgroup/vulkan-validationlayers',\n", " 'pmem/pmemkv',\n", " 'ptillet/triton',\n", " 'ros2/rclcpp',\n", " 'microsoft/qsharp-runtime',\n", " 'khale<PERSON><PERSON>ny/ots',\n", " 'stremio/stremio-shell',\n", " 'biojppm/rapidyaml',\n", " 'samsung/one',\n", " 'onnx/onnx-mlir',\n", " 'mne-tools/mne-cpp',\n", " 'tbarbette/fastclick',\n", " 'azure/sonic-swss',\n", " 'wohlstand/libadlmidi',\n", " 'pytorch/tensorpipe',\n", " 'crowcpp/crow',\n", " 'mdolab/pyoptsparse',\n", " 'eclipse/xacc',\n", " 'tensorflow/tflite-support',\n", " 'bluebrain/coreneuron',\n", " 'belledonnecommunications/flexisip',\n", " 'alpine-dav/ascent',\n", " 'samsung/escargot',\n", " 'ncar/vapor',\n", " 'ryan-rsm-mckenzie/commonlibsse',\n", " 'commaai/cereal',\n", " 'enigma-game/enigma',\n", " 'openpmd/openpmd-api',\n", " 'arbor-sim/arbor',\n", " 'llnl/axom',\n", " 'academysoftwarefoundation/imath',\n", " 'sstsimulator/sst-core',\n", " 'torquegameengines/torque3d',\n", " 'wisdem/wisdem',\n", " 'cisco/mlspp',\n", " 'indilib/indi-3rdparty',\n", " 'lethe-cfd/lethe',\n", " 'cagnulein/qdomyos-zwift',\n", " 'ros-planning/moveit_calibration',\n", " 'acts-project/acts',\n", " 'cilium/proxy',\n", " 'scipp/scipp',\n", " 'llnl/serac',\n", " 'nacos-group/nacos-sdk-cpp',\n", " 'uowuo/abaddon',\n", " 'luxonis/depthai-core',\n", " 'ocornut/imgui',\n", " 'dmlc/xgboost',\n", " 'google/flatbuffers',\n", " 'sqlitebrowser/sqlitebrowser',\n", " 'valvesoftware/proton',\n", " 'microsoft/airsim',\n", " 'thealgorithms/c-plus-plus',\n", " 'diasurgical/devilution',\n", " 'capnproto/capnproto',\n", " 'symless/synergy-core',\n", " 'google/or-tools',\n", " 'draios/sysdig',\n", " 'facebook/hermes',\n", " 'open-source-parsers/jsoncpp',\n", " 'baldurk/renderdoc',\n", " 'kdab/gammaray',\n", " 'second-state/ssvm',\n", " 'shader-slang/slang',\n", " 'intel/compute-runtime',\n", " 'nrel/energyplus',\n", " 'imageengine/cortex',\n", " 'google/verible',\n", " 'ngageoint/hootenanny',\n", " 'indilib/indi',\n", " 'llnl/raja',\n", " 'mantidproject/mantid',\n", " 'djcb/mu',\n", " 'dotnet/diagnostics',\n", " 'objectcomputing/opendds',\n", " 'rigsofrods/rigs-of-rods',\n", " 'verilog-to-routing/vtr-verilog-to-routing',\n", " 'rui314/mold',\n", " 'oneapi-src/onedal',\n", " 'lutraconsulting/input',\n", " 'gpuopen-drivers/llpc',\n", " 'dotnet/templating',\n", " 'tinycsvparser/tinycsvparser',\n", " 'dotnet/aspnetcore.docs',\n", " 'jas<PERSON><PERSON>/notepads',\n", " 'dotnet/winforms',\n", " 'zksnacks/walletwasabi',\n", " 'npgsql/npgsql',\n", " 'azure/azure-sdk-for-net',\n", " 'vis2k/mirror',\n", " 'crest-ocean/crest',\n", " 'mono/cppsharp',\n", " 'linq2db/linq2db',\n", " 'xamarin/xamarincomponents',\n", " 'chillicream/hotchocolate',\n", " 'tyrrrz/discordchatexporter',\n", " 'lidarr/lidarr',\n", " 'btcpayserver/btcpayserver',\n", " 'grandnode/grandnode',\n", " 'tmodloader/tmodloader',\n", " 'dotnet/samples',\n", " 'brightercommand/brighter',\n", " 'dafny-lang/dafny',\n", " 'dotnet/iot',\n", " 'snoopwpf/snoopwpf',\n", " 'ksp-ckan/ckan',\n", " 'piranhacms/piranha.core',\n", " 'emgucv/emgucv',\n", " 'tasvideos/bizhawk',\n", " 'nuke-build/nuke',\n", " 'subnauticanitrox/nitrox',\n", " 'revolutionary-games/thrive',\n", " 'gerardog/gsudo',\n", " 'livesplit/livesplit',\n", " 'system-io-abstractions/system.io.abstractions',\n", " 'shinyorg/shiny',\n", " 'erikej/efcorepowertools',\n", " 'dotnet/project-system',\n", " 'stnkl/everythingtoolbar',\n", " 'crosire/scripthookvdotnet',\n", " 'microsoft/buildxl',\n", " 'identitymodel/identitymodel',\n", " 'ppy/osu-framework',\n", " 'swharden/scottplot',\n", " 'arkypita/lasergrbl',\n", " 'readarr/readarr',\n", " 'dahall/vanara',\n", " 'aliyun/aliyun-openapi-net-sdk',\n", " 'sonarsource/sonar-dotnet',\n", " 'ultz/silk.net',\n", " 'adamramberg/unity-atoms',\n", " 'adam<PERSON>ph/bullseye',\n", " 'dotnet/arcade',\n", " '2881099/freeredis',\n", " 'blazored/modal',\n", " 'adamralph/xbehave.net',\n", " 'microsoft/windowsprotocoltestsuites',\n", " 'adam<PERSON>ph/minver',\n", " 'microsoft/playwright-sharp',\n", " 'edcd/eddi',\n", " 'dotnet/crank',\n", " 'mono/linker',\n", " 'tencentcloud/tencentcloud-sdk-dotnet',\n", " 'unity-technologies/usd-unity-sdk',\n", " 'jkorf/binance.net',\n", " 'jetbrains/rd',\n", " 'mcneel/rhino3dm',\n", " 'flow-launcher/flow.launcher',\n", " 'dotnet-outdated/dotnet-outdated',\n", " 'copytext/textcopy',\n", " 'robincornelius/libedssharp',\n", " 'goatcorp/dalamud',\n", " 'rungwiroon/blazorgooglemaps',\n", " 'shootme/fallguysstats',\n", " 'xeeynamo/openkh',\n", " 'openmod/openmod',\n", " 'pnp/pnpcore',\n", " 'imchillin/anamnesis',\n", " 'thesupersonic16/hedgemodmanager',\n", " 'gircore/gir.core',\n", " 'nihlus/remora.discord',\n", " 'turbolinks/turbolinks-android',\n", " 'jetbrains/jdk8u_jdk',\n", " 'streamthoughts/kafka-connect-file-pulse',\n", " 'kernitus/bukkitoldcombatmechanics',\n", " 'tuguangquan/mybatis',\n", " 'dotnet/aspnetcore',\n", " 'dotnet/roslyn',\n", " 'samuel/python-munin',\n", " 'kangasbros/django-bitcoin',\n", " 'mcdevs/mark2',\n", " 'mariano/snakefire',\n", " 'mozilla/elasticutils',\n", " 'rlisagor/freshen',\n", " 'opentreemap/otm-legacy',\n", " 'docker-archive/docker-registry',\n", " 'twilio/stashboard',\n", " 'echonest/pyechonest',\n", " 'enigmacurry/blogofile',\n", " 'edas/webperf-book',\n", " 'treeio/treeio',\n", " 'spencergibb/battleschool',\n", " 'etsy/skyline',\n", " 'topikachu/python-ev3',\n", " 'azavea/open-data-catalog',\n", " 'diybookscanner/spreads',\n", " 'mrjoes/flask-admin',\n", " 'spockbotmc/spockbot',\n", " 'devassistant/devassistant',\n", " 'i2y/mochi',\n", " 'j<PERSON><PERSON>/django-treemenus',\n", " 'egrcc/zhihu-python',\n", " 'bitly/asyncmongo',\n", " 'jbalogh/jingo',\n", " 'brml/climin',\n", " 'pytube/pytube',\n", " 'thomasvs/morituri',\n", " 'disqus/gargoyle',\n", " 'vimeo/graphite-influxdb',\n", " 'wimleers/fileconveyor',\n", " 'deldotdr/txredis',\n", " 'machinalis/iepy',\n", " 'flashingpumpkin/django-socialregistration',\n", " 'linkedin/naarad',\n", " 'turi-code/how-to',\n", " 'abunsen/paython',\n", " 'sublimetext/coldfusion',\n", " 'syrusakbary/flask-superadmin',\n", " 'idsia/brainstorm',\n", " 'agoragames/haigha',\n", " 'stormpath/stormpath-flask',\n", " 'stuartherbert/sublime-phpunit',\n", " 's<PERSON>yoshi/django-dbsettings',\n", " 'nate-parrott/flashlight',\n", " 'plivo/plivoframework',\n", " 'spragunr/deep_q_rl',\n", " 'blinktrade/bitex',\n", " 'pyga/parsley',\n", " 'goodcloud/django-zebra',\n", " 'kakaroto/swproxy',\n", " 'hydrachain/hydrachain',\n", " 'pingo-io/pingo-py',\n", " 'krmaxwell/maltrieve',\n", " 'chromium/web-page-replay',\n", " 'saltstack/raet',\n", " 'friendcode/gittle',\n", " 'owncloud/client',\n", " 'bloomberg/bde',\n", " 'dfhack/dfhack',\n", " 'otland/forgottenserver',\n", " 'coelckers/gzdoom',\n", " 'tencent/puerts',\n", " 'zilliqa/zilliqa',\n", " 'ultrafunkamsterdam/undetected-chromedriver',\n", " 'python-semver/python-semver',\n", " 'untitaker/python-atomicwrites',\n", " 'puddletag/puddletag',\n", " 'tivix/django-common',\n", " 'materialsproject/fireworks',\n", " 'myint/docformatter',\n", " 'pytest-dev/pytest-rerunfailures',\n", " 'roflcoopter/viseron',\n", " 'iamkroot/trakt-scrobbler',\n", " 'matplotlib/cmocean',\n", " 'pe-st/garmin-connect-export',\n", " 'anitab-org/vms',\n", " 'redhat-cop/casl-ansible',\n", " 'vpelletier/python-libusb1',\n", " 'holgern/pyedflib',\n", " 'pythonistaguild/wavelink',\n", " 'dlr-rm/rafcon',\n", " 'thorrak/fermentrack',\n", " 'byro/byro',\n", " 'nico<PERSON><PERSON><PERSON>i/linkedin-easy-apply-bot',\n", " 'aboudykreidieh/h-baselines',\n", " '3liz/quickosm',\n", " 'sunpy/ablog',\n", " 'kmkfw/kmk_firmware',\n", " 'mdomke/schwifty',\n", " 'brandon-rhodes/python-jplephem',\n", " 'alex<PERSON><PERSON><PERSON>/executing',\n", " 'nedbat/scriv',\n", " 'na<PERSON>cott/scrython',\n", " 'operatorai/modelstore',\n", " 'donalffons/opencascade.js',\n", " 'amanoteam/eduurobot',\n", " 'archspec/archspec',\n", " 'custom-components/grocy',\n", " 'allenai/ir_datasets',\n", " 'onlyoffice/build_tools',\n", " 'donnemartin/system-design-primer',\n", " 'cool-rr/pysnooper',\n", " 'ddbourgin/numpy-ml',\n", " 'lukemelas/efficientnet-pytorch',\n", " 'hips/autograd',\n", " 'docker/docker-py',\n", " 'pytransitions/transitions',\n", " 'carlton<PERSON>bson/django-filter',\n", " 'thunlp/opennre',\n", " 'residentmario/missingno',\n", " 'veil-framework/veil',\n", " 'cryptosignal/crypto-signal',\n", " 'flyingrub/scdl',\n", " 'ethan-funny/explore-python',\n", " 'open-mmlab/openpcdet',\n", " 'althonos/instalooter',\n", " 'databrewery/cubes',\n", " 'prompt-toolkit/pymux',\n", " 'gabrielfalcao/lettuce',\n", " 'anymail/django-anymail',\n", " 'pyjs/pyjs',\n", " 'kiwiz/gkeepapi',\n", " 'fwupd/fwupd',\n", " 'wolfssl/wolfssl',\n", " 'op-tee/optee_os',\n", " 'swi-prolog/swipl-devel',\n", " 'nanovms/nanos',\n", " 'osgeo/proj',\n", " 'ros-planning/navigation2',\n", " 'r-lib/vroom',\n", " 'pinterest/rocksplicator',\n", " 'microsoft/nnfusion',\n", " 'tensorflow/io',\n", " 'troldal/openxlsx',\n", " 'openblack/openblack',\n", " 'networkit/networkit',\n", " 'rism-ch/verovio',\n", " 'openspace/openspace',\n", " 'robotology/yarp',\n", " 'yacreader/yacreader',\n", " 'ericsson/codecompass',\n", " 'irods/irods',\n", " 'actboy168/ydwe',\n", " 'grammatech/ddisasm',\n", " 'da<PERSON><PERSON><PERSON><PERSON>/djv',\n", " 'opengisch/qfield',\n", " 'agherzan/meta-raspberrypi',\n", " 'dgtal-team/dgtal',\n", " 'akshitagit/cpp',\n", " 'justdan96/tsmuxer',\n", " 'cosmoscout/cosmoscout-vr',\n", " 'axiodl/urde',\n", " 'memkind/memkind',\n", " 'intel/yarpgen',\n", " 'scp-fs2open/fs2open.github.com',\n", " 'xilinx/xrt',\n", " 'lifting-bits/rellic',\n", " 'superelastix/elastix',\n", " 'proddy/ems-esp',\n", " 'loco-3d/crocoddyl',\n", " 'eclipse-iceoryx/iceoryx',\n", " 'smistad/fast',\n", " 'bkaradzic/bimg',\n", " 'lattice/quda',\n", " 'spoutn1k/mcmap',\n", " 'pulp-platform/pulpissimo',\n", " 'ethz-asl/aslam_cv2',\n", " 'gismo/gismo',\n", " 'rocmsoftwareplatform/rocblas',\n", " 'google/amber',\n", " 'r-lib/cpp11',\n", " 'simgrid/simgrid',\n", " 'singular/singular',\n", " 'couchbaselabs/fleece',\n", " 'call-for-code/clusterduck-protocol',\n", " 'rnpgp/rnp',\n", " 'johnsully/keydb',\n", " 'zcash/zcash',\n", " 'interpretml/interpret',\n", " 'falcosecurity/falco',\n", " 'nvidia/dali',\n", " 'microsoft/verona',\n", " 'prusa3d/prusaslicer',\n", " 'mozilla/rhino',\n", " 'juce-framework/juce',\n", " 'deepmind/open_spiel',\n", " 'canonical/multipass',\n", " 'cwida/duckdb',\n", " 'tigervnc/tigervnc',\n", " 'google/oboe',\n", " 'pytorch/glow',\n", " 'openthread/openthread',\n", " 'cgal/cgal',\n", " 'osgeo/gdal',\n", " 'jolibrain/deepdetect',\n", " 'openscenegraph/openscenegraph',\n", " 'facontidavide/plotjuggler',\n", " 'facebook/mysql-5.6',\n", " 'cnr-isti-vclab/meshlab',\n", " 'triton-inference-server/server',\n", " 'giuspen/cherrytree',\n", " 'khronosgroup/glslang',\n", " 'microsoft/directxshadercompiler',\n", " 'rathena/rathena',\n", " 'google/orbit',\n", " 'haivision/srt',\n", " 'newsboat/newsboat',\n", " 'cryfs/cryfs',\n", " 'valhalla/valhalla',\n", " 'academysoftwarefoundation/openvdb',\n", " 'apache/trafficserver',\n", " 'cyberbotics/webots',\n", " 'dresden-elektronik/deconz-rest-plugin',\n", " 'sminghub/sming',\n", " 'aws/aws-sdk-cpp',\n", " 'ibm/fhe-toolkit-linux',\n", " 'facebookresearch/habitat-sim',\n", " 'tiledb-inc/tiledb',\n", " 'synfig/synfig',\n", " 'meganz/sdk',\n", " 'project-chip/connectedhomeip',\n", " 'bitcoin-abc/bitcoin-abc',\n", " 'arisotura/melonds',\n", " 'microsoft/adaptivecards',\n", " 'realm/realm-core',\n", " 'borglab/gtsam',\n", " 'eclipse/omr',\n", " 'airensoft/ovenmediaengine',\n", " 'pdal/pdal',\n", " 'trustwallet/wallet-core',\n", " 'khronosgroup/spirv-tools',\n", " 'neoml-lib/neoml',\n", " 'manticoresoftware/manticoresearch',\n", " 'google/xls',\n", " 'zerotier/libzt',\n", " 'shigu<PERSON>/momo',\n", " 'vsg-dev/vulkanscenegraph',\n", " 'fastmachinelearning/hls4ml',\n", " 'microsoft/ccf',\n", " 'sofa-framework/sofa',\n", " 'google/perfetto',\n", " 'danielogorchock/st_anything',\n", " 'canaltp/navitia',\n", " 'castxml/castxml',\n", " 'exult/exult',\n", " 'google/highway',\n", " 'precice/precice',\n", " 'gaijinentertainment/dascript',\n", " 'electro-smith/daisysp',\n", " 'googleapis/google-cloud-cpp',\n", " 'tenzir/vast',\n", " 'opae/opae-sdk',\n", " 'quasarapp/cqtdeployer',\n", " 'qiskit/qiskit-aer',\n", " 'efroemling/ballistica',\n", " 'fenics/dolfinx',\n", " 'boostorg/math',\n", " 'ngsolve/ngsolve',\n", " 'kpet/clvk',\n", " 'valeevgroup/tiledarray',\n", " 'microsoft/tensorflow-directml',\n", " 'qmcpack/qmcpack',\n", " 'espressomd/espresso',\n", " 'opennmt/ctranslate2',\n", " 'ornladios/adios2',\n", " 'openturns/openturns',\n", " 'elastic/ml-cpp',\n", " 'inet-framework/simulte',\n", " 'rncbc/qjackctl',\n", " 'xrootd/xrootd',\n", " 'alibaba/heterogeneity-aware-lowering-and-optimization',\n", " 'khronosgroup/opencl-cts',\n", " 'lk8000/lk8000',\n", " 'uiuc-ppl/charm',\n", " 'open-telemetry/opentelemetry-cpp',\n", " 'quotient-im/libquotient',\n", " 'opm/resinsight',\n", " 'quinoacomputing/quinoa',\n", " 'opensuse/libzypp',\n", " 'ecp-warpx/warpx',\n", " 'electron/electron',\n", " 'bitcoin/bitcoin',\n", " 'tesseract-ocr/tesseract',\n", " 'n<PERSON><PERSON>/json',\n", " 'google/googletest',\n", " 'apolloauto/apollo',\n", " 'telegramdesktop/tdesktop',\n", " 'ossrs/srs',\n", " 'ethereum/solidity',\n", " 'polybar/polybar',\n", " 'flameshot-org/flameshot',\n", " 'scylladb/scylla',\n", " 'zerotier/zerotierone',\n", " 'rr-debugger/rr',\n", " 'mapsme/omim',\n", " 'plaidml/plaidml',\n", " 'microsoft/onnxruntime',\n", " 'intel-isl/open3d',\n", " 'rapidsai/cudf',\n", " 'supercollider/supercollider',\n", " 'arvidn/libtorrent',\n", " 'esphome/esphome',\n", " 'oneapi-src/onednn',\n", " 'oneflow-inc/oneflow',\n", " 'mixxxdj/mixxx',\n", " 'subsurface/subsurface',\n", " 'rizsotto/bear',\n", " 'yosyshq/yosys',\n", " 'root-project/root',\n", " 'grame-cncm/faust',\n", " 'introlab/rtabmap',\n", " 'taocpp/pegtl',\n", " 'pytorch/xla',\n", " 'oracle/graaljs',\n", " 'cms-sw/cmssw',\n", " 'wayfirewm/wayfire',\n", " 'sentdex/nnfsix',\n", " 'steinbergmedia/vstgui',\n", " 'finalburnneo/fbneo',\n", " 'dltcollab/sse2neon',\n", " 'widelands/widelands',\n", " 'qalculate/qalculate-gtk',\n", " 'bearware/teamtalk5',\n", " 'openmined/tenseal',\n", " 'bitcoin-core/gui',\n", " 'sxs-collaboration/spectre',\n", " 'ignitionrobotics/ign-gazebo',\n", " 'batocera-linux/batocera-emulationstation',\n", " 'drowe67/freedv-gui',\n", " 'azure/azure-sdk-for-cpp',\n", " 'mozilla-mobile/mozilla-vpn-client',\n", " 'h2zero/esp-nimble-cpp',\n", " 'ravendb/ravendb',\n", " 'svg-net/svg',\n", " 'bililive/bililiverecorder',\n", " 'scottplot/scottplot',\n", " 'wowup/wowup',\n", " 'dotnet/roslyn-sdk',\n", " 'aottg-2/aottg-2',\n", " 'dotnet/wpf',\n", " 'dotnetcore/freesql',\n", " 'dotnet/extensions',\n", " 'jetbrains/resharper-unity',\n", " 'dotnet/interactive',\n", " 'microsoft/fhir-server',\n", " 'stryker-mutator/stryker-net',\n", " 'datadog/dd-trace-dotnet',\n", " 'altinn/altinn-studio',\n", " 'smartstore/smartstore',\n", " 'microsoft/powerapps-language-tooling',\n", " 'mopidy/mopidy',\n", " 'jiaaro/pydub',\n", " 'activitywatch/activitywatch',\n", " 'microsoft/qlib',\n", " 'pytorch/ignite',\n", " 'nabla-c0d3/sslyze',\n", " 'cea-sec/miasm',\n", " 'home-assistant-libs/pychromecast',\n", " 'aiortc/aiortc',\n", " 'confluentinc/confluent-kafka-python',\n", " 'axnsan12/drf-yasg',\n", " 'napalm-automation/napalm',\n", " 'archerysec/archerysec',\n", " 'ethereum/eth2.0-specs',\n", " 'mi<PERSON><PERSON><PERSON><PERSON><PERSON>/evo',\n", " 'projectmesa/mesa',\n", " 'deepset-ai/haystack',\n", " 'systemrage/py-kms',\n", " 'abhin<PERSON><PERSON><PERSON>/proxy.py',\n", " 'soco/soco',\n", " 'pyfilesystem/pyfilesystem2',\n", " 'cuthbertlab/music21',\n", " 'd<PERSON><PERSON>/uer-py',\n", " 'paddlepaddle/paddlex',\n", " 'analogj/lexicon',\n", " 'harisekhon/nagios-plugins',\n", " 'pupil-labs/pupil',\n", " 'fhamborg/news-please',\n", " 'alexxit/sonofflan',\n", " 'petl-developers/petl',\n", " 'ewels/multiqc',\n", " 'aliyun/aliyun-oss-python-sdk',\n", " 'thuml/transfer-learning-library',\n", " 'paddlepaddle/paddleslim',\n", " 'deepcharles/ruptures',\n", " 'centerforopenscience/osf.io',\n", " 'hhursev/recipe-scrapers',\n", " 'zentralopensource/zentral',\n", " 'quark-engine/quark-engine',\n", " 'pypsa/pypsa',\n", " 'stm32-rs/stm32-rs',\n", " 'zerodha/pykiteconnect',\n", " 'koaning/scikit-lego',\n", " 'holoviz/hvplot',\n", " 'paddlepaddle/x2paddle',\n", " 'mittagessen/kraken',\n", " 'harry24k/adversarial-attacks-pytorch',\n", " 'fabioz/pydev.debugger',\n", " 'yaqwsx/kikit',\n", " 'okfde/froide',\n", " 'hungpham2511/toppra',\n", " 'dask/dask-kubernetes',\n", " 'algolia/docsearch-scraper',\n", " 'allegroai/clearml-server',\n", " 'kiwigrid/k8s-sidecar',\n", " 'oemof/oemof-solph',\n", " 'jborean93/pypsrp',\n", " 'awslabs/aws-serverless-data-lake-framework',\n", " 'tenpy/tenpy',\n", " 'calliope-project/calliope',\n", " 'agenium-scale/nsimd',\n", " 'qiskit/qiskit-ignis',\n", " 'oxford-quantum-group/discopy',\n", " 'rucio/rucio',\n", " 'labgrid-project/labgrid',\n", " 'swansonk14/typed-argument-parser',\n", " 'autogoal/autogoal',\n", " 'nf-core/tools',\n", " 'ovirt/vdsm',\n", " 'marketsquare/robotframework-browser',\n", " 'hazelcast/hazelcast-python-client',\n", " 'thedirtyfew/dash-extensions',\n", " 'dome9/cloud-bots',\n", " 'mechmotum/cyipopt',\n", " 'spinnakermanchester/spynnaker',\n", " 'pippyn/home-assistant-sensor-afval<PERSON><PERSON>r',\n", " 'teahouse-studios/mcwzh-meme-resourcepack',\n", " '42school/norminette',\n", " 'storaged-project/blivet',\n", " 'trioptima/iommi',\n", " 'openprinting/system-config-printer',\n", " 'ha0y/xiaomi_miot_raw',\n", " 'custom-components/integration_blueprint',\n", " 'allegro/bigflow',\n", " 'mflowgen/mflowgen',\n", " 'metoppv/improver',\n", " 'superwerker/superwerker',\n", " 'equinor/xtgeo',\n", " 'larq/zoo',\n", " 'google/ml_collections',\n", " 'kernelci/kernelci-core',\n", " 'openai/gym',\n", " 'kivy/kivy',\n", " 'beetbox/beets',\n", " 'the-paperless-project/paperless',\n", " 'crazyguitar/pysheeet',\n", " 'readthedocs/readthedocs.org',\n", " 'facebookresearch/pytext',\n", " 'google/clusterfuzz',\n", " 'geerlingguy/ansible-for-devops',\n", " 'kinto/kinto',\n", " 'googleapis/google-cloud-python',\n", " 'ctfd/ctfd',\n", " 'mikf/gallery-dl',\n", " 'catalyst-team/catalyst',\n", " 'fishtown-analytics/dbt',\n", " 'boltgolt/howdy',\n", " 'pyload/pyload',\n", " 'jupyter/nbdime',\n", " 'rhinosecuritylabs/pacu',\n", " 'crossbario/crossbar',\n", " 'pantsbuild/pex',\n", " 'holoviz/holoviews',\n", " 'intelowlproject/intelowl',\n", " 'libratbag/piper',\n", " 'kaggle/docker-python',\n", " 'ibis-project/ibis',\n", " 'google/timesketch',\n", " 'google/tensornetwork',\n", " 'rochacbruno/dynaconf',\n", " 'yaml/pyyaml',\n", " 'jazzband/django-simple-history',\n", " 'ansible-community/ara',\n", " 'google/glazier',\n", " 'tensorflow/model-analysis',\n", " 'pyscaffold/pyscaffold',\n", " 'python-streamz/streamz',\n", " 'django-json-api/django-rest-framework-json-api',\n", " 'canonical/cloud-init',\n", " 'rpm-software-management/dnf',\n", " 'onnx/onnx-tensorflow',\n", " 'thoughtworksarts/emopy',\n", " 'databiosphere/toil',\n", " 'unit8co/darts',\n", " 'crytic/slither',\n", " 'gafferhq/gaffer',\n", " 'pkkid/python-plexapi',\n", " 'fairlearn/fairlearn',\n", " 'svinota/pyroute2',\n", " 'smarthomehub/smartir',\n", " 'indicodatasolutions/finetune',\n", " 'facebookresearch/fvcore',\n", " 'certtools/intelmq',\n", " 'gnome-terminator/terminator',\n", " ...]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["failed_repo_list"]}, {"cell_type": "code", "execution_count": 7, "id": "88467be4b6a9e1ee", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:10:55.798553Z", "start_time": "2024-11-25T13:10:55.749389Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-18 11:04:11,895 - INFO - Connecting to MongoDB.\n", "2025-02-18 11:04:11,897 - INFO - Fetching data for repo: junit-team/junit4\n"]}, {"ename": "ValueError", "evalue": "The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m repo_name \u001b[38;5;241m=\u001b[39m project_names[\u001b[38;5;241m3\u001b[39m]\n\u001b[0;32m----> 2\u001b[0m activity_table \u001b[38;5;241m=\u001b[39m \u001b[43mgenerate_activity_table_and_export\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrepo_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m activity_table\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m../data/disengagement_tables/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrepo_name\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m/\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;250m \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_activity_table.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      5\u001b[0m activity_table \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m../data/disengagement_tables/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrepo_name\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m/\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;250m \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_activity_table.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[5], line 91\u001b[0m, in \u001b[0;36mgenerate_activity_table_and_export\u001b[0;34m(repo_name)\u001b[0m\n\u001b[1;32m     89\u001b[0m     attrition_date \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mto_datetime(record[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAttrition\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mattrition_date\u001b[39m\u001b[38;5;124m\"\u001b[39m])\u001b[38;5;241m.\u001b[39mtz_localize(\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m     90\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m date \u001b[38;5;129;01min\u001b[39;00m date_range:\n\u001b[0;32m---> 91\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m date \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m attrition_date:\n\u001b[1;32m     92\u001b[0m             activity_map[date] \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m\n\u001b[1;32m     94\u001b[0m \u001b[38;5;66;03m# Append rows for each date\u001b[39;00m\n", "\u001b[0;31mValueError\u001b[0m: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()"]}], "source": ["repo_name = project_names[3]\n", "activity_table = generate_activity_table_and_export(repo_name)\n", "\n", "activity_table.to_csv(f\"../data/disengagement_tables/{repo_name.replace('/', '_')}_activity_table.csv\")\n", "activity_table = pd.read_csv(f\"../data/disengagement_tables/{repo_name.replace('/', '_')}_activity_table.csv\")\n", "intervals_df = identify_intervals(activity_table)\n", "plot_intervals(intervals_df)\n", "# plot the commit number for the whole repo by date\n", "repo_commit = get_commit_file_repo_name(repo_name)\n", "repo_commit['date'] = pd.to_datetime(repo_commit['date'], format='%Y-%m-%dT%H:%M:%SZ')\n", "repo_commit['date'] = repo_commit['date'].apply(lambda x: x.date())\n", "repo_commit['date'] = pd.to_datetime(repo_commit['date'])\n", "plt.figure(figsize=(12, 6))\n", "repo_commit['date'].value_counts().sort_index().plot()\n", "plt.title(f\"Commit number for {repo_name}\")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Number of Commits\")\n", "plt.show()\n", "# also plot the commit number for the whole repo, as well as plot the commit number for each core developer"]}, {"cell_type": "code", "execution_count": null, "id": "31bfb78a511ea9a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "498ece0634a97d04", "metadata": {}, "source": ["## different unit of time"]}, {"cell_type": "code", "execution_count": 6, "id": "8e3d42179cb46350", "metadata": {}, "outputs": [], "source": ["import logging\n", "from pymongo import MongoClient\n", "import pandas as pd\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.DEBUG,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\"\n", ")\n", "\n", "def process_repo_data_and_store(project_names, core_devs_data, window_size=1, attrition_limit=365):\n", "    \"\"\"\n", "    Process data for each repository based on its commits and core developers,\n", "    then store the results in MongoDB incrementally.\n", "\n", "    Args:\n", "        project_names (pd.DataFrame): Names for all repositories.\n", "        core_devs_data (pd.DataFrame): Core developers for each repository.\n", "        window_size (int): The size of the window in days for calculating pauses.\n", "        attrition_limit (int): The threshold in days for determining developer attrition.\n", "    \"\"\"\n", "    def process_repo_data(project_names, core_devs_data, collection, window_size, attrition_limit):\n", "        # Ensure 'core_developers' column is correctly processed\n", "        logging.info(\"Processing core developers data.\")\n", "        core_devs_data['core_developers'] = core_devs_data['core_developers'].apply(\n", "            lambda x: x if isinstance(x, list) else eval(x)\n", "        )\n", "\n", "        # Process each repository with a sequential project_id\n", "        project_counter = 0\n", "        for repo_name in project_names:\n", "            logging.info(f\"Processing repository: {repo_name} (Project ID: {project_counter})\")\n", "\n", "            # Fetch commit data for the current repository\n", "            try:\n", "                repo_commits = get_processed_commit_file_repo_name(repo_name)\n", "                # unify timezone for repo_commits\n", "                repo_commits['date'] = pd.to_datetime(repo_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')\n", "                repo_commits['date'] = repo_commits['date'].dt.tz_localize('UTC').dt.tz_convert(None)\n", "            except Exception as e:\n", "                logging.error(f\"Error fetching commit data for repository '{repo_name}': {e}\")\n", "                continue\n", "\n", "            # Get core developers for the repository\n", "            try:\n", "                core_developers = core_devs_data[core_devs_data['repo_name'] == repo_name]['core_developers'].iloc[0]\n", "                logging.debug(f\"Core developers for # {project_counter} '{repo_name}': {core_developers}\")\n", "            except Exception as e:\n", "                logging.error(f\"Error fetching core developers for repository '{repo_name}': {e}\")\n", "                continue\n", "\n", "            # Process each core developer for the repository\n", "            core_dev_counter = 0\n", "            for core_dev in core_developers:\n", "                logging.debug(f\"# {project_counter} '{repo_name}' -- Processing core developer: {core_dev}\")\n", "\n", "                # Filter commits by the core developer\n", "                dev_commits = repo_commits[repo_commits['author_login'] == core_dev]\n", "                dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')\n", "                dev_commits = dev_commits.sort_values(by='date')\n", "\n", "                # Calculate inactivity periods using sliding window\n", "                def get_pauses_with_commit(dev_commits, window_size):\n", "                    pauses = {}\n", "                    pause_id = 0\n", "                    for i in range(1, len(dev_commits)):\n", "                        days_diff = (dev_commits.iloc[i]['date'] - dev_commits.iloc[i - 1]['date']).days\n", "                        units_diff = days_diff / window_size\n", "                        # if units_diff > 0, then it is a pause\n", "                        if units_diff > 0:\n", "                            pauses[pause_id] = {\n", "                                'start_date': dev_commits.iloc[i - 1]['date'],\n", "                                'end_date': dev_commits.iloc[i]['date'],\n", "                                'duration_units': units_diff\n", "                            }\n", "                            pause_id += 1\n", "                    logging.debug(f\"# {project_counter} '{repo_name}' -- Pauses: {pauses}\")\n", "                    return pauses\n", "\n", "                pauses = get_pauses_with_commit(dev_commits, window_size)\n", "\n", "                # Identify long pauses\n", "                dev_output = {\n", "                    \"repo_id\": project_counter,\n", "                    \"repo_name\": repo_name,\n", "                    \"core_dev_id\": core_dev_counter,\n", "                    \"core_dev_login\": core_dev,\n", "                    \"Attrition\": None,\n", "                    \"Breaks\": []\n", "                }\n", "                core_dev_counter += 1\n", "\n", "                if len(pauses) > 0:\n", "                    pauses = pd.<PERSON><PERSON>rame(pauses).T\n", "                    pauses_units = pauses['duration_units']\n", "                    logging.debug(f\"# {project_counter} '{repo_name}' -- Pauses: {pauses}\")\n", "                    if not pauses_units.empty:\n", "                        Q1 = pauses_units.quantile(0.25)\n", "                        Q3 = pauses_units.quantile(0.75)\n", "                        IQR = Q3 - Q1\n", "                        Tfov = Q3 + 3 * IQR\n", "                        logging.debug(f\"# {project_counter} '{repo_name}' -- Q1: {Q1}, Q3: {Q3}, IQR: {IQR}, Tfov: {Tfov}\")\n", "                        long_pauses = pauses[pauses['duration_units'] > Tfov].reset_index(drop=True)\n", "                        logging.debug(f\"# {project_counter} '{repo_name}' -- Long pauses: {long_pauses}\")\n", "\n", "                        break_counter = 0\n", "                        for pause_index in long_pauses.index:\n", "                            try:\n", "                                start_date = long_pauses.loc[pause_index, 'start_date']\n", "                                end_date = long_pauses.loc[pause_index, 'end_date']\n", "                                duration_units = long_pauses.loc[pause_index, 'duration_units']\n", "                                dev_output[\"Breaks\"].append({\n", "                                    \"break_id\": break_counter,\n", "                                    \"start_date\": str(start_date.date()),\n", "                                    \"end_date\": str(end_date.date()),\n", "                                    \"duration_units\": duration_units\n", "                                })\n", "                                break_counter += 1\n", "                                logging.info(f\"# {project_counter} '{repo_name}' -- Break recorded for {core_dev}: {start_date.date()} to {end_date.date()} ({duration_units} units)\")\n", "                            except IndexError as e:\n", "                                logging.error(f\"Error processing pause for {core_dev}: {e}\")\n", "                                continue\n", "                        last_commit_date = dev_commits['date'].max()\n", "                        project_last_commit_date = repo_commits['date'].max()\n", "\n", "                        # Ensure both dates are timezone-naive\n", "                        last_commit_date = last_commit_date.tz_localize(None)\n", "                        project_last_commit_date = project_last_commit_date.tz_localize(None)\n", "                        \n", "                        if (project_last_commit_date - last_commit_date).days > attrition_limit:\n", "                            dev_output[\"Attrition\"] = {\n", "                                \"attrition_date\": [str(last_commit_date.date())]\n", "                            }\n", "                            logging.info(f\"# {project_counter} '{repo_name}' -- Developer {core_dev} marked as disengaged since {last_commit_date.date()}.\")\n", "                        # also add breaks with duration_units >= attrition_limits into attrition\n", "                        for break_event in dev_output[\"Breaks\"]:\n", "                            if break_event[\"duration_units\"] >= attrition_limit:\n", "                                ## add the break into attrition\n", "                                if dev_output[\"Attrition\"] is None:\n", "                                    dev_output[\"Attrition\"] = {\n", "                                        \"attrition_date\": [str(break_event[\"start_date\"])]\n", "                                    }\n", "                                else:\n", "                                    dev_output[\"Attrition\"][\"attrition_date\"].append(str(break_event[\"start_date\"]))\n", "                                \n", "                                logging.info(f\"# {project_counter} '{repo_name}' -- Developer {core_dev} marked as disengaged since {break_event['start_date']}.\")\n", "                try:\n", "                    collection.insert_one(dev_output)\n", "                    logging.info(f\"# {project_counter} '{repo_name}' -- Data for core developer {core_dev} stored in MongoDB.\")\n", "                except Exception as e:\n", "                    logging.error(f\"Error storing data for core developer {core_dev} in MongoDB: {e}\")\n", "            project_counter += 1\n", "\n", "    # MongoDB Connection\n", "    logging.info(\"Connecting to MongoDB.\")\n", "    WINDOWS_IP = \"localhost\"\n", "    PORT = 27017\n", "    client = MongoClient(f\"mongodb://{WINDOWS_IP}:{PORT}/\")\n", "    db = client[\"disengagement\"]\n", "    collection = db[\"project_analysis\"]\n", "    collection.drop()\n", "    # set unique index for collection of repo_name and core_dev_id\n", "    collection.create_index([(\"repo_name\", 1), (\"core_dev_id\", 1)], unique=True)\n", "    # Process the data and store incrementally\n", "    logging.info(\"Starting data processing for all repositories.\")\n", "    process_repo_data(project_names, core_devs_data, collection, window_size, attrition_limit)\n", "    logging.info(\"Data processing completed.\")\n", "\n", "    logging.info(\"Data successfully stored in MongoDB.\")\n"]}, {"cell_type": "markdown", "id": "3d0ca18da3ab9742", "metadata": {}, "source": ["# TEST"]}, {"cell_type": "code", "execution_count": null, "id": "fbc730c0", "metadata": {}, "outputs": [], "source": ["standardized_productivity_20250202"]}, {"cell_type": "code", "execution_count": 7, "id": "36a82c37", "metadata": {}, "outputs": [{"data": {"text/plain": ["['sparklemotion/nokogiri',\n", " 'davidb/scala-maven-plugin',\n", " 'tcurdt/jdeb',\n", " 'junit-team/junit4',\n", " 'yui/yuicompressor',\n", " 'unclebob/fitnesse',\n", " 'connectbot/connectbot',\n", " 'bpellin/keepassdroid',\n", " 'rnewson/couchdb-lucene',\n", " 'nodebox/nodebox',\n", " 'cwensel/cascading',\n", " 'cucumber-attic/cuke4duke',\n", " 'bndtools/bndtools',\n", " 'twitter4j/twitter4j',\n", " 'magro/memcached-session-manager',\n", " 'caelum/vraptor',\n", " 'maxcom/lorsource',\n", " 'rzwitserloot/lombok',\n", " 'voldemort/voldemort',\n", " 'jdbi/jdbi',\n", " 'simpligility/android-maven-plugin',\n", " 'jblas-project/jblas',\n", " 'pocmo/yaaic',\n", " 'ccw-ide/ccw',\n", " 'novoda/android-demos',\n", " 'qos-ch/logback',\n", " 'qos-ch/slf4j',\n", " 'apache/shiro',\n", " 'haraldk/twelvemonkeys',\n", " 'fusesource/jansi',\n", " 'yaxim-org/yaxim',\n", " 'cucumber-attic/gherkin2',\n", " 'talklittle/reddit-is-fun',\n", " 'twilio/twilio-java',\n", " 'webmetrics/browsermob-proxy',\n", " 'sitemesh/sitemesh2',\n", " 'sirthias/parboiled',\n", " 'resty-gwt/resty-gwt',\n", " 'maven-nar/nar-maven-plugin',\n", " 'martint/jmxutils',\n", " 'j<PERSON>by/joni',\n", " 'sanger-pathogens/artemis',\n", " 'notnoop/java-apns',\n", " 'trifork/erjang',\n", " 'torquebox/jruby-maven-plugins',\n", " 'jhy/jsoup',\n", " 'jberkel/sms-backup-plus',\n", " 'ervandew/eclim',\n", " 'sbt/junit-interface',\n", " 'j<PERSON><PERSON><PERSON>/ognl',\n", " 'elastic/elasticsearch',\n", " 'antlr/antlr4',\n", " 'tootallnate/java-websocket',\n", " 'awaitility/awaitility',\n", " 'hector-client/hector',\n", " 'citrusframework/citrus',\n", " 'ge0rg/memorizingtrustmanager',\n", " 'tvrenamer/tvrenamer',\n", " 'dropwizard/metrics',\n", " 'hierynomus/sshj',\n", " 'zeromq/jzmq',\n", " 'linkedin/sensei',\n", " 'braintree/braintree_java',\n", " 'cowtowncoder/java-uuid-generator',\n", " 'twitter/elephant-bird',\n", " 'magro/kryo-serializers',\n", " 'brian<PERSON>nkcooper/ycsb',\n", " 'kilim/kilim',\n", " 'flyingsaucerproject/flyingsaucer',\n", " 'github-api/github-api',\n", " 'bndtools/bnd',\n", " 'eleybourn/book-catalogue',\n", " 'basho/riak-java-client',\n", " 'liato/android-bankdroid',\n", " 'sirthias/pegdown',\n", " 'graphstream/gs-core',\n", " 'cdk/cdk',\n", " 'jai-imageio/jai-imageio-core',\n", " 'graylog2/graylog2-server',\n", " 'bigbluebutton/bigbluebutton',\n", " 'apache/jmeter',\n", " 'todotxt/todo.txt-android',\n", " 'todoroo/astrid',\n", " 'apache/mina-sshd',\n", " 'xetorthio/jedis',\n", " 'apollo-rsps/apollo',\n", " 'cbeust/testng',\n", " 'abarisain/dmix',\n", " 'cbeust/jcommander',\n", " 'vrapper/vrapper',\n", " 'rhuss/jolokia',\n", " 'shrinkwrap/shrinkwrap',\n", " 'mixpanel/mixpanel-android',\n", " 'wildfly/jandex',\n", " 'openstreetmap/osm-binary',\n", " 'nysenate/openlegislation',\n", " 'cyclestreets/android',\n", " 'square/retrofit',\n", " 'scribe<PERSON><PERSON>/scribejava',\n", " 'robolectric/robolectric',\n", " 'go-lang-plugin-org/go-lang-idea-plugin',\n", " 'opentsdb/asynchbase',\n", " 'alexruiz/fest-assert-2.x',\n", " 'fasterxml/aalto-xml',\n", " 'blacklabs/play-morphia',\n", " 'arduino/arduino',\n", " 'rabbitmq/rabbitmq-java-client',\n", " 'dcm4che/dcm4che',\n", " 'errai/errai',\n", " 'coremedia/jangaroo-tools',\n", " 'enginehub/worldedit',\n", " 'jmock-developers/jmock-library',\n", " 'craigwblake/redline',\n", " 'mpatric/mp3agic',\n", " 'pholser/junit-quickcheck',\n", " 'jmeter-maven-plugin/jmeter-maven-plugin',\n", " 'rest-assured/rest-assured',\n", " 'modelmapper/modelmapper',\n", " 'bartdag/py4j',\n", " 'samskivert/jmustache',\n", " 'j<PERSON>erman/expiringmap',\n", " 'jhalterman/typetools',\n", " 'enginehub/craftbook',\n", " 'netty/netty',\n", " 'plantuml/plantuml',\n", " 'infinitest/infinitest',\n", " 'jpos/jpos',\n", " 'spring-projects/spring-data-jpa',\n", " 'spring-projects/spring-batch',\n", " 'spring-projects/spring-data-commons',\n", " 'enginehub/worldguard',\n", " 'dozingcat/vector-pinball',\n", " 'alecgorge/jsonapi',\n", " 'vvakame/jsonpullparser',\n", " 'kordamp/json-lib',\n", " 'spring-projects/spring-framework',\n", " 'rstudio/rstudio',\n", " 'validator/validator',\n", " 'hidendra/lwc',\n", " 'spockframework/spock',\n", " 'ocpsoft/prettytime',\n", " 'springtestdbunit/spring-test-dbunit',\n", " 'jen<PERSON><PERSON>/jira-plugin',\n", " 'jenkinsci/scm-sync-configuration-plugin',\n", " 'radargun/radargun',\n", " 'jenkinsci/selenium-plugin',\n", " 'jen<PERSON><PERSON>/cobertura-plugin',\n", " 'webbukkit/dynmap',\n", " 'asterisk-java/asterisk-java',\n", " 'smooks/smooks',\n", " 'aws/aws-toolkit-eclipse',\n", " 'forcedotcom/wsc',\n", " 'p6spy/p6spy',\n", " 'eclipse-color-theme/eclipse-color-theme',\n", " 'fasterxml/jackson-dataformat-xml',\n", " 'osmandapp/osmand',\n", " 'webbit/webbit',\n", " 'matyb/java-koans',\n", " 'dropwizard/dropwizard',\n", " 'json-path/jsonpath',\n", " 'codebutler/farebot',\n", " 'plantuml/plantuml-server',\n", " 'gresrun/jesque',\n", " 'thucydides-webtests/thucydides',\n", " 'lukaszlenart/launch4j-maven-plugin',\n", " 't0xa/gelfj',\n", " 'k9mail/k-9',\n", " 'ushahidi/smssync',\n", " 'jnr/jnr-ffi',\n", " 'structr/structr',\n", " 'liveramp/hank',\n", " 'sandsmark/quasseldroid',\n", " 'timmoore/mojo-executor',\n", " 'git-commit-id/git-commit-id-maven-plugin',\n", " 'tomighty/tomighty',\n", " 'taweili/ardublock',\n", " 'operasoftware/operaprestodriver',\n", " 'android-async-http/android-async-http',\n", " 'guardianproject/chatsecureandroid',\n", " 'slapperwan/gh4a',\n", " 'helun/ektorp',\n", " 'xebialabs/overthere',\n", " 'hudson/hudson-2.x',\n", " 'enginehub/commandhelper',\n", " 'mono/sharpen',\n", " 'jfrog/build-info',\n", " 'movingblocks/terasology',\n", " 'spring-projects/spring-social',\n", " 'tagtime/tagtime',\n", " 'enginehub/commandbook',\n", " 'jakewharton/actionbarsherlock',\n", " 'asynchttpclient/async-http-client',\n", " 'sqlcipher/android-database-sqlcipher',\n", " 'addthis/stream-lib',\n", " 'twitter-archive/cloudhopper-smpp',\n", " 'tbsalling/aismessages',\n", " 'jorensix/tarsosdsp',\n", " 'onelogin/java-saml',\n", " 'glowstonemc/glowstone-legacy',\n", " 'six2six/fixture-factory',\n", " 'nathanmarz/dfs-datastores',\n", " 'stapler/stapler',\n", " 'asakusafw/asakusafw',\n", " 'multiverse/multiverse-portals',\n", " 'moocar/logback-gelf',\n", " 'apache/bookkeeper',\n", " 'jline/jline2',\n", " 'arquillian/arquillian-core',\n", " 'jopt-simple/jopt-simple',\n", " 'guardianproject/lildebi',\n", " 'stathis<PERSON>ris/ditaa',\n", " 'nyholku/purejavacomm',\n", " 'dhanji/sitebricks',\n", " 'bekvon/residence',\n", " 'koush/urlimageviewhelper',\n", " 'pexplugins/permissionsex',\n", " 'rest-driver/rest-driver',\n", " 'jpos/jpos-ee',\n", " 'knowm/sundial',\n", " 'simpleserver/simpleserver',\n", " 'java-native-access/jna',\n", " 'mtedone/podam',\n", " 'mcmics/jenkins-control-plugin',\n", " 'perwendel/spark',\n", " 'impetus/kundera',\n", " 'stoicflame/enunciate',\n", " 'geosolutions-it/geoserver-manager',\n", " 'jodaorg/joda-beans',\n", " 'jodaorg/joda-time',\n", " 'mvel/mvel',\n", " 'cometd/cometd',\n", " 'eclipse-ee4j/cdi',\n", " 'resthub/resthub-spring-stack',\n", " 'aaberg/sql2o',\n", " 'spring-projects/spring-social-facebook',\n", " 'qmx/jitescript',\n", " 'splunk/splunk-sdk-java',\n", " 'spring-projects/spring-social-twitter',\n", " 'wizcorp/phonegap-facebook-plugin',\n", " 'gwt-maven-plugin/gwt-maven-plugin',\n", " 'garbagemule/mobarena',\n", " 'dius/java-faker',\n", " 'jiaqi/jmxterm',\n", " 'nutzam/nutz',\n", " 'onebusaway/onebusaway-android',\n", " 'eclipse-vertx/vert.x',\n", " 'chariotsolutions/phonegap-nfc',\n", " 'kohsuke/args4j',\n", " 'jsqlparser/jsqlparser',\n", " 'google/truth',\n", " 'gitblit/gitblit',\n", " 'jeffheaton/encog-java-core',\n", " 'wvengen/proguard-maven-plugin',\n", " 'milkbowl/vault',\n", " 'ocpsoft/rewrite',\n", " 'cucumber/cucumber-jvm',\n", " 'angryip/ipscan',\n", " 'java-json-tools/json-schema-validator',\n", " 'm<PERSON><PERSON><PERSON>i/ttorrent',\n", " 'msgpack/msgpack-java',\n", " 'tony19/logback-android',\n", " 'spring-projects/spring-data-redis',\n", " 'sachin-handiekar/jinstagram',\n", " 'swagger-api/swagger-core',\n", " 'jjoe64/graphview',\n", " 'liquibase/liquibase',\n", " 'rajawali/rajawali',\n", " 'liquibase/liquibase-hibernate',\n", " 'cgeo/cgeo',\n", " 'rcongiu/hive-json-serde',\n", " 'eclipse/jetty.project',\n", " 'netflix/curator',\n", " 'netflix/astyanax',\n", " 'eclipse-cdt/cdt',\n", " 'opentripplanner/opentripplanner',\n", " 'freezy/android-xbmcremote',\n", " 'netflix/priam',\n", " 'dain/leveldb',\n", " 'spring-projects/spring-integration',\n", " 'fluentlenium/fluentlenium',\n", " 'mctcp/terraincontrol',\n", " 'mebigfatguy/fb-contrib',\n", " 'shrinkwrap/resolver',\n", " 'github/maven-plugins',\n", " 'hunterhacker/jdom',\n", " 'linqs/psl',\n", " 'querydsl/querydsl',\n", " 'j<PERSON><PERSON>an/tomcat-redis-session-manager',\n", " 'jetbrains/grammar-kit',\n", " 'jsevellec/cassandra-unit',\n", " 'stripe/stripe-java',\n", " 'spring-projects/spring-data-cassandra',\n", " 'spring-social/spring-social-google',\n", " 'chennaione/sugar',\n", " 'farin/jcloisterzone',\n", " 'seleniumhq/fluent-selenium',\n", " 'mbax/vanishnopacket',\n", " 'threerings/playn',\n", " 'jenkinsci/ansicolor-plugin',\n", " 'groovy/gmavenplus',\n", " 'jankotek/jdbm3',\n", " 'edumips64/edumips64',\n", " 'pockethub/pockethub',\n", " 'labexp/osmtracker-android',\n", " 'restfb/restfb',\n", " 'sonian/elasticsearch-jetty',\n", " 'alex<PERSON><PERSON><PERSON><PERSON>/jasper-report-maven-plugin',\n", " 'bwaldvogel/mongo-java-server',\n", " 't-oster/visicut',\n", " 'crossbario/autobahn-java',\n", " 'cloudfoundry/cf-java-client',\n", " 'sanity/quickml',\n", " 'ifixit/ifixitandroid',\n", " 'usc-isi-i2/web-karma',\n", " 'lishid/orebfuscator',\n", " 'dynjs/dynjs',\n", " 'spring-projects/spring-data-neo4j',\n", " 'mkarneim/pojobuilder',\n", " 'globalbioticinteractions/globalbioticinteractions',\n", " 'nifty-gui/nifty-gui',\n", " 'yamcs/yamcs',\n", " 'ome/bioformats',\n", " 'ripe-ncc/hadoop-pcap',\n", " 'neo4j-contrib/cypher-dsl',\n", " 'apache/accumulo',\n", " 'tomakehurst/wiremock',\n", " 'fluent/fluent-logger-java',\n", " 'zxing/zxing',\n", " 'spring-projects/spring-data-mongodb',\n", " 'gephi/gephi',\n", " 'chewiebug/gcviewer',\n", " 'kevinsawicki/http-request',\n", " 'internetarchive/heritrix3',\n", " 'javaparser/javaparser',\n", " 'mitreid-connect/openid-connect-java-spring-server',\n", " 'openxc/openxc-android',\n", " 'wiringproject/wiring',\n", " 'alibaba/fastjson',\n", " 'alibaba/druid',\n", " 'chocoteam/choco-solver',\n", " 'nathanmarz/storm-contrib',\n", " 'mapfish/mapfish-print',\n", " 'floodlight/floodlight',\n", " 'sqldroid/sqldroid',\n", " 'nicolas<PERSON><PERSON>/andengine',\n", " 'zeroturnaround/zt-zip',\n", " 'ps3mediaserver/ps3mediaserver',\n", " 'aspose-words/aspose.words-for-java',\n", " 'aspose-cells/aspose.cells-for-java',\n", " 'nostra13/android-universal-image-loader',\n", " 'rythmengine/rythmengine',\n", " 'axonframework/axonframework',\n", " 'nightwhistler/pageturner',\n", " 'jmxtrans/jmxtrans',\n", " 'azure/azure-sdk-for-java',\n", " 'vx/connectbot',\n", " 's3-wagon-private/s3-wagon-private',\n", " 'medcl/elasticsearch-analysis-ik',\n", " 'fusesource/mqtt-client',\n", " 'restlet/restlet-framework-java',\n", " 'imglib/imglib2',\n", " 'kaitoy/pcap4j',\n", " 'hstaudacher/osgi-jax-rs-connector',\n", " 'fasterxml/jackson-core',\n", " 'marytts/marytts',\n", " 'togglz/togglz',\n", " 'bguerout/jongo',\n", " 'tasssadar/multirommgr',\n", " 'spring-projects/spring-webflow',\n", " 'yasserg/crawler4j',\n", " 'jooq/joor',\n", " 'murgo/irssinotifier',\n", " 'jakewharton/diskl<PERSON>che',\n", " 'mcmmo-dev/mcmmo',\n", " 'apavlo/h-store',\n", " 'lkuza2/java-speech-api',\n", " 'spring-projects/spring-security',\n", " 'mrn<PERSON>/netty-socketio',\n", " 'wstrange/googleauth',\n", " 'boncey/flickr4java',\n", " 'soabase/exhibitor',\n", " 'heroku/webapp-runner',\n", " 'mbeddr/mbeddr.core',\n", " 'mjiderhamn/classloader-leak-prevention',\n", " 'cutr-at-usf/opentripplanner-for-android',\n", " 'minecraftforge/minecraftforge',\n", " 'lukas-krecan/jsonunit',\n", " 'gentlecat/counter',\n", " 'knowm/xchange',\n", " 'skyscreamer/jsonassert',\n", " 'selenide/selenide',\n", " 'ajanata/pretendyourexyzzy',\n", " 'pgjdbc/pgjdbc',\n", " 'ppareit/swiftp',\n", " 'splunk/splunk-library-javalogging',\n", " 'cloudfoundry/uaa',\n", " 'dadoonet/spring-elasticsearch',\n", " 'woorea/openstack-java-sdk',\n", " 'jfxtras/jfxtras',\n", " 'onebusaway/onebusaway-application-modules',\n", " 'jfxtras/jfxtras-labs',\n", " 'onebusaway/onebusaway-gtfs-modules',\n", " 'spring-projects/spring-shell',\n", " 'citizensdev/citizens2',\n", " 'graphhopper/graphhopper',\n", " 'maescool/catacomb-snatch',\n", " 'nroduit/weasis',\n", " 'springside/springside4',\n", " '<PERSON><PERSON><PERSON><PERSON>/titan',\n", " 'iiordanov/remote-desktop-clients',\n", " 'scala-android/sbt-android',\n", " 'marginallyclever/makelangelo-software',\n", " 'bkiers/liqp',\n", " 'wurstscript/wurstscript',\n", " 'eclipse/org.aspectj',\n", " 'denizenscript/denizen',\n", " 'prototik/holoeverywhere',\n", " 'urbanairship/datacube',\n", " 'open-keychain/open-keychain',\n", " 'spring-projects/spring-data-rest',\n", " 'kohsuke/file-leak-detector',\n", " 'emilsjolander/stickylistheaders',\n", " 'qos-ch/logback-extensions',\n", " 'http-kit/http-kit',\n", " 'intive-fdv/dynamicjasper',\n", " 'dotcms/core',\n", " 'dspace/dspace',\n", " 'ibotpeaches/apktool',\n", " 'pentaho/mondrian',\n", " 'hazelcast/hazelcast',\n", " 'qiniu/java-sdk',\n", " 'spring-projects/spring-data-envers',\n", " 'scijava/native-lib-loader',\n", " 'buchen/portfolio',\n", " 'st-js/st-js',\n", " 'branflake2267/gwt-maps-v3-api',\n", " 'openconext/mujina',\n", " 'minecraftforge/fml',\n", " 'epfldata/squall',\n", " 'hamcrest/javahamcrest',\n", " 'tvpt/voxelsniper',\n", " 'jetbrains/intellij-samples',\n", " 'wala/wala',\n", " 'tillnagel/unfolding',\n", " 'iipc/openwayback',\n", " 'dita-ot/dita-ot',\n", " 'traccar/traccar',\n", " 'buildcraft/buildcraft',\n", " 'neuland/jade4j',\n", " 'anysoftkeyboard/anysoftkeyboard',\n", " 'ttddyy/datasource-proxy',\n", " 'realm/realm-java',\n", " 'sqisher/java-object-diff',\n", " 'deadrik/tfcraft',\n", " 'pahimar/equivalent-exchange-3',\n", " 'd<PERSON><PERSON><PERSON>/umlgraph',\n", " 'freshplanet/ane-in-app-purchase',\n", " 'enonic/xp',\n", " 'romraider/romraider',\n", " 'magefree/mage',\n", " 'mitre/http-proxy-servlet',\n", " 'familysearch/gedcom',\n", " 'aragozin/jvm-tools',\n", " 'scobal/seyren',\n", " 'ph-7/simple-java-calculator',\n", " 'springfox/springfox',\n", " 'tordanik/osm2world',\n", " 'spring-projects/spring-hateoas',\n", " 'netflix/archaius',\n", " 'jenkinsci/embeddable-build-status-plugin',\n", " 'cloudera/cm_api',\n", " 'bivas/protobuf-java-format',\n", " 'geosolutions-it/imageio-ext',\n", " 'lordofthejars/nosql-unit',\n", " 'jidesoft/jide-oss',\n", " 'find-sec-bugs/find-sec-bugs',\n", " 'jersey/jersey',\n", " 'opentelecoms-org/lumicall',\n", " 'thymeleaf/thymeleaf',\n", " 'codinguser/gnucash-android',\n", " 'samaxes/minify-maven-plugin',\n", " 'searchbox-io/jest',\n", " 'thymeleaf/thymeleaf-spring',\n", " 'ebourg/jsign',\n", " 'vekexasia/android-edittext-validator',\n", " 'sarxos/webcam-capture',\n", " 'i2p/i2p.i2p',\n", " 'jknack/handlebars.java',\n", " 'mapstruct/mapstruct',\n", " 'cleverage/play2-elasticsearch',\n", " 'sufficientlysecure/donations',\n", " 'jprante/elasticsearch-jdbc',\n", " 'oauth-apis/apis',\n", " 'wolpi/prim-ftpd',\n", " 'msteiger/jxmapviewer2',\n", " 'ryantenney/metrics-spring',\n", " 'ninjaframework/ninja',\n", " 'universalmediaserver/universalmediaserver',\n", " 'ne<PERSON>kov/android-backup-extractor',\n", " 'dadoonet/fscrawler',\n", " 'sirixdb/sirix',\n", " 'pentaho/pentaho-platform',\n", " 'jfree/jfreechart-fse',\n", " 'intel-bigdata/hibench',\n", " 'objectify/objectify',\n", " 'azagniotov/stubby4j',\n", " 'geonetwork/core-geonetwork',\n", " 'andlyticsproject/andlytics',\n", " 'airlift/airlift',\n", " 'opacapp/opacclient',\n", " 'apache/dubbo',\n", " 'naver/ngrinder',\n", " 'joscha/play-authenticate',\n", " 'sable/soot',\n", " 'digital-preservation/droid',\n", " 'mit-cml/appinventor-sources',\n", " 'winder/universal-g-code-sender',\n", " 'spring-projects/eclipse-integration-gradle',\n", " 'square/otto',\n", " 'jfeinstein10/slidingmenu',\n", " 'daneren2005/subsonic',\n", " 'intermine/intermine',\n", " 'damianszczepanik/cucumber-reporting',\n", " 'netflix/simianarmy',\n", " 'jacoco/jacoco',\n", " 'netflix/governator',\n", " 'pmd/pmd',\n", " 'scout24/yum-repo-server',\n", " 'takari/polyglot-maven',\n", " 'filestack/filestack-android',\n", " 'tomahawk-player/tomahawk-android',\n", " 'aboutsip/pkts',\n", " 'greenrobot/eventbus',\n", " 'vmi/selenese-runner-java',\n", " 'chrisbanes/photoview',\n", " 'lz4/lz4-java',\n", " 'facebookarchive/jcommon',\n", " 'dj3500/hightail',\n", " 'usethesource/rascal',\n", " 'airlift/airline',\n", " 'ignatov/intellij-erlang',\n", " 'airlift/aircompressor',\n", " 'jfrog/artifactory-client-java',\n", " 'netflix/eureka',\n", " 'la-team/light-admin',\n", " 'gradle/native-platform',\n", " 'antennapod/antennapod',\n", " 'zeromq/jeromq',\n", " 'openwdl/wdl',\n", " 'square/dagger',\n", " 'keyboardsurfer/crouton',\n", " 'fordfrog/apgdiff',\n", " 'fabi<PERSON><PERSON><PERSON>etti/jsondoc',\n", " 'qiniu/android-sdk',\n", " 'dcache/nfs4j',\n", " 'arcbees/gwtchosen',\n", " 'prestodb/presto',\n", " 'libgdx/libgdx',\n", " 'nanohttpd/nanohttpd',\n", " 'roboguice/roboguice',\n", " 'j-easy/easy-batch',\n", " 'mekanism/mekanism',\n", " 'jankotek/mapdb',\n", " 'restcomm/restcomm-connect',\n", " 'restcomm/sip-servlets',\n", " 'restcomm/media-core',\n", " 'restcomm/jss7',\n", " 'restcomm/jdiameter',\n", " 'restcomm/smscgateway',\n", " 'naver/yobi',\n", " 'flansmods/flansmod',\n", " 'yegor256/s3auth',\n", " 'koush/androidasync',\n", " 'zen0wu/topcoder-greed',\n", " 'uwescience/myria',\n", " 'jeremylong/dependencycheck',\n", " 'nlpchina/ansj_seg',\n", " 'apache/drill',\n", " 'jenkinsci/ghprb-plugin',\n", " 'arcbees/gwtp',\n", " 'junrar/junrar',\n", " 'oehf/ipf',\n", " 'xerial/snappy-java',\n", " 'oblac/jodd',\n", " 'ragunath<PERSON><PERSON><PERSON>/android-saripaar',\n", " 'fossasia/phimpme-android',\n", " 'square/tape',\n", " 'hdrhistogram/hdrhistogram',\n", " 'wyvernlang/wyvern',\n", " 'activiti/activiti',\n", " 'ebean-orm/ebean',\n", " 'kermitt2/grobid',\n", " 'freeplane/freeplane',\n", " 'aadnk/protocollib',\n", " 'jzy3d/jzy3d-api',\n", " 'wiglenet/wigle-wifi-wardriving',\n", " 'stephanenicolas/robospice',\n", " 'linkedin/parseq',\n", " 'oltpbenchmark/oltpbench',\n", " 'comcast/cmb',\n", " 'lmax-exchange/disruptor',\n", " 'vanilla-music/vanilla',\n", " 'pvorb/npm-stat.com',\n", " 'automattic/simplenote-android',\n", " 'yandex-qatools/htmlelements',\n", " 'jscep/jscep',\n", " 'progether/jadventure',\n", " 'eclipse/pdt',\n", " 'mikera/vectorz',\n", " 'jdmonin/jsettlers2',\n", " 'uwe<PERSON><PERSON><PERSON>/tmdb-java',\n", " 'jcodec/jcodec',\n", " 'micdoodle8/galacticraft',\n", " 'tntim96/jscover',\n", " 'inl/blacklab',\n", " 'spigotmc/bungeecord',\n", " 'teiid/teiid',\n", " 'fasterxml/jackson-jaxrs-providers',\n", " 'ceon/cermine',\n", " 'mkotsur/restito',\n", " 'pf4j/pf4j',\n", " 'cloudinary/cloudinary_java',\n", " 'mockito/mockito',\n", " 'openrefine/openrefine',\n", " 'azkaban/azkaban',\n", " 'dreamhead/moco',\n", " 'spring-projects/spring-boot',\n", " 'vaadin/framework',\n", " 'killbill/killbill',\n", " 'junkdog/artemis-odb',\n", " 'membrane/service-proxy',\n", " 'apache/druid',\n", " 'ben<PERSON><PERSON>/mbassador',\n", " 'adjust/android_sdk',\n", " 'ta<PERSON><PERSON><PERSON><PERSON>/nv-i18n',\n", " 'lawrancej/logisim',\n", " 'forgeessentials/forgeessentials',\n", " 'ovirt/ovirt-engine',\n", " 'ukanth/afwall',\n", " 'lunatrius/schematica',\n", " 'vivo-project/vivo',\n", " 'javamoney/jsr354-api',\n", " 'cucumber/cucumber-eclipse',\n", " 'comcast/jrugged',\n", " 'modesty/pdf2json',\n", " 'bod/android-contentprovider-generator',\n", " 'pac4j/play-pac4j',\n", " 'odnoklassniki/one-nio',\n", " 'sable/heros',\n", " 'jenkinsci/java-client-api',\n", " 'threeten/threeten-extra',\n", " 'doanduyhai/achilles',\n", " 'dropwizard-jobs/dropwizard-jobs',\n", " 'jberet/jsr352',\n", " 'oldmanpushcart/greys-anatomy',\n", " 'robospock/robospock',\n", " 'maven-download-plugin/maven-download-plugin',\n", " 'facebook/nailgun',\n", " 'libreplan/libreplan',\n", " 'apache/cordova-android',\n", " 'jona<PERSON>eland/runnerup',\n", " 'mutabilitydetector/mutabilitydetector',\n", " 'journeyapps/zxing-android-embedded',\n", " 'haiwen/seadroid',\n", " 'linkedin/rest.li',\n", " 'hubspot/dropwizard-guice',\n", " 'olap4j/olap4j',\n", " 'deegree/deegree3',\n", " 'hawtio/hawtio',\n", " 'apps4av/avare',\n", " 'dianping/cat',\n", " 'baqend/orestes-bloomfilter',\n", " 'calimero-project/calimero-core',\n", " 'spring-projects/spring-loaded',\n", " 'netflix/turbine',\n", " 'jprante/elasticsearch-knapsack',\n", " 'guardianproject/pixelknot',\n", " 'orientechnologies/orientdb',\n", " 'grahamar/cron-parser',\n", " 'flyway/flyway',\n", " 'v<PERSON><PERSON><PERSON><PERSON>/la4j',\n", " 'yubico/ykneo-openpgp',\n", " 'alluxio/alluxio',\n", " 'lviggiano/owner',\n", " 'dbfit/dbfit',\n", " 'lwjgl/lwjgl3',\n", " 'pebbletemplates/pebble',\n", " 'larswerkman/holocolorpicker',\n", " 'dieselpoint/norm',\n", " 'addstarmc/prism-bukkit',\n", " 'camunda/camunda-bpm-platform',\n", " 'apache/jena',\n", " 'selendroid/selendroid',\n", " 'zhuowei/mcpelauncher',\n", " 'ktgw0316/lightzone',\n", " 'oracle/opengrok',\n", " 'reactivex/rxjava',\n", " 'pentaho/pentaho-reporting',\n", " 'spring-projects/spring-petclinic',\n", " 'passy/android-directorychooser',\n", " 'jetbrains/jediterm',\n", " 'paulfitz/daff',\n", " 'esri/spatial-framework-for-hadoop',\n", " 'azkaban/azkaban-plugins',\n", " 'tada/pljava',\n", " 'alibaba/canal',\n", " 'seleniumhq/selenium',\n", " 'netflix/ribbon',\n", " 'esri/geometry-api-java',\n", " 'square/assertj-android',\n", " 'forcedotcom/phoenix',\n", " 'eido<PERSON><PERSON>hi/rpicheck',\n", " 'thekrakken/java-grok',\n", " 'ratpack/ratpack',\n", " 'slimeknights/tinkersconstruct',\n", " 'processing/processing',\n", " 'netflix/denominator',\n", " 'openpnp/openpnp',\n", " 'eoecn/android-app',\n", " 'koral--/android-gif-drawable',\n", " 'redsolution/xabber-android',\n", " 'spring-projects/spring-ws',\n", " 'hakandilek/play2-crud',\n", " 'square/javapoet',\n", " 'jparsec/jparsec',\n", " 'prometheus/client_java',\n", " 'fcrepo4/fcrepo4',\n", " 'molgenis/molgenis',\n", " 'ios-driver/ios-driver',\n", " 'kairosdb/kairosdb',\n", " 'netflix/karyon',\n", " 'bastillion-io/bastillion',\n", " 'netflix/evcache',\n", " 'mybatis/jpetstore-6',\n", " 'diereicheerethons/brewery',\n", " 'jenkinsci/cucumber-reports-plugin',\n", " 'jd-alexander/google-directions-android',\n", " 'ikarus23/mifareclassictool',\n", " 'dandelion/dandelion-datatables',\n", " 'jenkinsci/stashnotifier-plugin',\n", " 'mybatis/mybatis-3',\n", " 'libgdx/ashley',\n", " 'bazaarvoice/jolt',\n", " 'chrislacy/tweetlanes',\n", " 'fixteam/fixflow',\n", " 'movingblocks/terasologylauncher',\n", " 'mybatis/spring',\n", " 'mybatis/guice',\n", " 'yegor256/rultor',\n", " 'droidplanner/tower',\n", " 'pubnub/java',\n", " 'tumblr/jumblr',\n", " 'ralfstx/minimal-json',\n", " 'ktuukkan/marine-api',\n", " 'norconex/collector-http',\n", " 'eclipse/golo-lang',\n", " 'gkopff/gson-jodatime-serialisers',\n", " 'mock-server/mockserver',\n", " 'orcid/orcid-source',\n", " 'jbake-org/jbake',\n", " 'ccavanaugh/jgnash',\n", " 'a-schild/jave2',\n", " 'logstash/logstash-logback-encoder',\n", " 'jdeferred/jdeferred',\n", " 'jnrouvignac/autorefactor',\n", " 'jakewharton/butterknife',\n", " 'pushkar/abagail',\n", " 'tanaguru/tanaguru',\n", " 'onepf/openiab',\n", " 'roomorama/caldroid',\n", " 'j256/ormlite-core',\n", " 'authme/authmereloaded',\n", " 'j-easy/easy-rules',\n", " 'elastic/elasticsearch-hadoop',\n", " 'impossibl/pgjdbc-ng',\n", " 'mongojack/mongojack',\n", " 'netflix/zuul',\n", " 'joel-costig<PERSON><PERSON>/assertj-core',\n", " 'julian<PERSON>de/sqlline',\n", " 'kongchen/swagger-maven-plugin',\n", " 'finmath/finmath-lib',\n", " 'skylot/jadx',\n", " 'asciidoctor/asciidoctorj',\n", " 'athou/commafeed',\n", " 'netflix/suro',\n", " 'yahoo/storm-yarn',\n", " 'intel-cloud/cosbench',\n", " 'techempower/frameworkbenchmarks',\n", " 'forge/roaster',\n", " 'psdev/licensesdialog',\n", " 'puniverse/quasar',\n", " 'graphhopper/jsprit',\n", " 'clulab/processors',\n", " 'crbednarz/amidst',\n", " 'hekailiang/squirrel',\n", " 'abstractj/kalium',\n", " 'atduskgreg/opencv-processing',\n", " 'citiususc/hipster',\n", " 'maxmind/geoip2-java',\n", " 'airlift/slice',\n", " 'killbill/killbill-commons',\n", " 'uservoice/uservoice-android-sdk',\n", " 'aksw/rdfunit',\n", " 'biojava/biojava',\n", " 'mixi-inc/androidtraining',\n", " 'quantumbadger/redreader',\n", " 'cloudbees/zendesk-java-client',\n", " 'openzipkin/brave',\n", " 'haehnchen/idea-php-symfony2-plugin',\n", " 'jreddit/jreddit',\n", " 'crate/crate',\n", " 'aschot-myan/rashr',\n", " 'kobakei/android-ratethisapp',\n", " 'pusher/pusher-websocket-java',\n", " 'spring-guides/gs-rest-service',\n", " 'digitalpebble/storm-crawler',\n", " 'mohamicorp/stash-jenkins-postreceive-webhook',\n", " 'ronmamo/reflections',\n", " 'dayatang/dddlib',\n", " 'facebook/buck',\n", " 'stratosphere/stratosphere',\n", " 'spring-guides/gs-scheduling-tasks',\n", " 'lookfirst/sardine',\n", " 'krasa/eclipsecodeformatter',\n", " 'spring-guides/gs-maven',\n", " 'spring-guides/gs-gradle',\n", " 'teamed/qulice',\n", " 'socketio/engine.io-client-java',\n", " 'jmxtrans/jmxtrans-agent',\n", " 'code4craft/webmagic',\n", " 'jitsi/jitsi',\n", " 'jitsi/libjitsi',\n", " 'morphiaorg/morphia',\n", " 'leapcode/bitmask_android',\n", " 'socketio/socket.io-client-java',\n", " 'spring-projects/spring-data-elasticsearch',\n", " 'apache/cloudstack',\n", " 'arx-deidentifier/arx',\n", " 'spring-guides/gs-messaging-rabbitmq',\n", " 'octopus-platform/joern',\n", " 'caelum/vraptor4',\n", " 'restx/restx',\n", " 'erogenousbeef-zz/bigreactors',\n", " 'lindenb/jvarkit',\n", " '3pillarlabs/socialauth',\n", " 'dice-group/agdistis',\n", " 'ripe-ncc/whois',\n", " 'google/gwtmockito',\n", " 'opensourcebim/bimserver',\n", " 'bastillion-io/bastillion-ec2',\n", " 'playgameservices/android-basic-samples',\n", " 'rackerlabs/blueflood',\n", " 'googlemaps/android-maps-utils',\n", " 'cobbzilla/s3s3mirror',\n", " 'j-easy/easy-random',\n", " 'timols/java-gitlab-api',\n", " 'timols/*********************request-builder-plugin',\n", " 'google/auto',\n", " 'koush/ion',\n", " 'angelozerr/tern.java',\n", " 'jboss-javassist/javassist',\n", " 'jeff<PERSON>on/aifh',\n", " 'trautonen/coveralls-maven-plugin',\n", " 'avast/android-styled-dialogs',\n", " 'nextcloud/news-android',\n", " 'rcarz/jira-client',\n", " 'schildbach/public-transport-enabler',\n", " 'javalite/javalite',\n", " 'maxmind/maxmind-db-reader-java',\n", " 'umano/androidslidinguppanel',\n", " 'spring-projects/spring-data-couchbase',\n", " 'bcgit/bc-java',\n", " 'openhft/chronicle-queue',\n", " 'markus<PERSON>sch/shadereditor',\n", " 'uwolfer/gerrit-intellij-plugin',\n", " 'ahmetaa/zemberek-nlp',\n", " 'sksamuel/scrimage',\n", " 'devicehive/devicehive-java-server',\n", " 'joshdholtz/sentry-android',\n", " 'spring-io/initializr',\n", " 'dmfs/opentasks',\n", " 'mgarin/weblaf',\n", " 'aerogear/aerogear-unifiedpush-server',\n", " 'joanzapata/android-iconify',\n", " 'apache/logging-log4j2',\n", " 'mycelium-com/wallet-android',\n", " 'codefollower/cassandra-research',\n", " 'apache/fluo',\n", " 'david<PERSON>n/geo',\n", " 'apache/cordova-plugin-inappbrowser',\n", " 'joe<PERSON><PERSON><PERSON><PERSON>/jsonschema2pojo',\n", " 'twitter/hraven',\n", " 'openfeign/feign',\n", " 'dropbox/dropbox-sdk-java',\n", " 'stanfordnlp/corenlp',\n", " 'electrical-age/electricalage',\n", " 'erickok/transdroid',\n", " 'raml-org/raml-java-parser',\n", " 'sonar-intellij-plugin/sonar-intellij-plugin',\n", " 'keycloak/keycloak',\n", " 'spring-projects/spring-ldap',\n", " 'estatio/estatio',\n", " 'sufficientlysecure/document-viewer',\n", " 'saros-project/saros',\n", " 'jan-molak/jenkins-build-monitor-plugin',\n", " 'jeevatkm/digitalocean-api-java',\n", " 'immutables/immutables',\n", " 'bumptech/glide',\n", " 'deathmarine/luyten',\n", " 'molgenis/systemsgenetics',\n", " 'j<PERSON><PERSON>/oandbackup',\n", " 'timgifford/maven-buildtime-extension',\n", " 'testfx/testfx',\n", " 'dgarijo/widoco',\n", " 'dependencytrack/dependency-track',\n", " 'easymock/objenesis',\n", " 'opendatalab-de/geojson-jackson',\n", " 'netflix/nicobar',\n", " 'joshjdevl/libsodium-jni',\n", " 'synyx/urlaubsverwaltung',\n", " 'flaxsearch/luwak',\n", " 'luontola/retrolambda',\n", " 'openmods/openblocks',\n", " 'ata4/disunity',\n", " 'ontop/ontop',\n", " 'asciidoctor/asciidoctor-intellij-plugin',\n", " 'verbalexpressions/javaverbalexpressions',\n", " 'vidstige/jadb',\n", " 'rollerderby/scoreboard',\n", " 'exist-db/exist',\n", " 'puniverse/comsat',\n", " 'kframework/k-legacy',\n", " 'sromku/android-simple-facebook',\n", " 'pietro<PERSON><PERSON>i/updatechecker',\n", " 'netmackan/atimetracker',\n", " 'yegor256/xembly',\n", " 'strongbox/strongbox',\n", " 'stagemonitor/stagemonitor',\n", " 'jchambers/pushy',\n", " 'huaban/jieba-analysis',\n", " 'brettwooldridge/nuprocess',\n", " 'bobbylight/rsyntaxtextarea',\n", " 'languagetool-org/languagetool',\n", " 'alibaba/otter',\n", " 'protegeproject/webprotege',\n", " 'bilibili/danmakuflamemaster',\n", " 'sleepytrousers/enderio',\n", " 'spring-projects/spring-xd-samples',\n", " 'opherv/gitflow4idea',\n", " 'protegeproject/protege',\n", " 'komoot/photon',\n", " 'duckduckgo/android-search-and-stories',\n", " 'allegro/grunt-maven-plugin',\n", " 'wayoftime/bloodmagic',\n", " 'peergos/peergos',\n", " 'apiman/apiman',\n", " 'sendgrid/sendgrid-java',\n", " 'opencb/opencga',\n", " 'davemorrissey/subsampling-scale-image-view',\n", " 'google/compile-testing',\n", " 'javaee-samples/javaee7-samples',\n", " 'greendelta/olca-app',\n", " 'eirslett/frontend-maven-plugin',\n", " 'checkstyle/checkstyle',\n", " 'michel-kraemer/gradle-download-task',\n", " 'threerings/getdown',\n", " 'atlauncher/atlauncher',\n", " 'alibaba/jstorm',\n", " 'real-logic/simple-binary-encoding',\n", " 'ical4j/ical4j',\n", " 'jcabi/jcabi-aspects',\n", " 'artur<PERSON><PERSON><PERSON><PERSON><PERSON>/iban4j',\n", " 'michaellavelle/spring-data-dynamodb',\n", " 'jcabi/jcabi-jdbc',\n", " 'rwth-i5-idsg/steve',\n", " 'paul-hammant/ngwebdriver',\n", " 'fasterxml/jackson-jr',\n", " 'cjlin1/libsvm',\n", " 'anjlab/android-inapp-billing-v3',\n", " 'wordpress-mobile/passcodelock-android',\n", " 'square/flow',\n", " 'mathew-kurian/textjustify-android',\n", " 'sannies/mp4parser',\n", " 'galenframework/galen',\n", " 'sialcasa/mvvmfx',\n", " 'percyliang/sempre',\n", " 'corfudb/corfudb',\n", " 'ripple-unmaintained/ripple-lib-java',\n", " 'openhab/openhab1-addons',\n", " 'openhft/java-thread-affinity',\n", " 'pires/obd-java-api',\n", " 'konsoletyper/teavm',\n", " 'rometools/rome',\n", " 'openhft/java-runtime-compiler',\n", " 'torakiki/pdfsam',\n", " 'indeedeng/proctor',\n", " 'mineshopper/carpentersblocks',\n", " 'zdevelopers/imageonmap',\n", " 'jmacg<PERSON>an/burlap',\n", " 'siom79/japicmp',\n", " ...]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["project_names"]}, {"cell_type": "code", "execution_count": 5, "id": "017563e6", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'project_names' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m core_developers_df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m../data/core_developer_list_total_repo.csv\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m----> 2\u001b[0m process_repo_data_and_store(\u001b[43mproject_names\u001b[49m, core_developers_df, window_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m, attrition_limit\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m365\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'project_names' is not defined"]}], "source": ["\n", "core_developers_df = pd.read_csv('../data/core_developer_list_total_repo.csv')\n", "process_repo_data_and_store(project_names, core_developers_df, window_size=1, attrition_limit=365)"]}, {"cell_type": "code", "execution_count": null, "id": "ac4b75e19bf8ac7a", "metadata": {"ExecuteTime": {"end_time": "2024-11-25T13:54:06.425805Z", "start_time": "2024-11-25T13:53:36.272907Z"}}, "outputs": [{"ename": "ServerSelectionTimeoutError", "evalue": "localhost :27017: [Errno -2] Name or service not known (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 674481601395f9bec1cf5543, topology_type: Unknown, servers: [<ServerDescription ('localhost ', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost :27017: [Errno -2] Name or service not known (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mServerSelectionTimeoutError\u001b[0m               Traceback (most recent call last)", "Cell \u001b[0;32mIn[21], line 11\u001b[0m\n\u001b[1;32m      9\u001b[0m db \u001b[38;5;241m=\u001b[39m client[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdisengagement\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m     10\u001b[0m collection \u001b[38;5;241m=\u001b[39m db[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mproject_analysis\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m---> 11\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(collection\u001b[38;5;241m.\u001b[39mfind({\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrepo_name\u001b[39m\u001b[38;5;124m\"\u001b[39m:repo_name}))\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data:\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnot found\u001b[39m\u001b[38;5;124m'\u001b[39m, repo_name)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/cursor.py:1281\u001b[0m, in \u001b[0;36mCursor.__next__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1280\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__next__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _DocumentType:\n\u001b[0;32m-> 1281\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnext()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/cursor.py:1257\u001b[0m, in \u001b[0;36mCursor.next\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1255\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_empty:\n\u001b[1;32m   1256\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m\n\u001b[0;32m-> 1257\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_refresh():\n\u001b[1;32m   1258\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data\u001b[38;5;241m.\u001b[39mpopleft()\n\u001b[1;32m   1259\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/cursor.py:1205\u001b[0m, in \u001b[0;36mCursor._refresh\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1183\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\n\u001b[1;32m   1184\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPassing a \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhint\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is required when using the min/max query\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1185\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m option to ensure the query utilizes the correct index\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1186\u001b[0m         )\n\u001b[1;32m   1187\u001b[0m     q \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_class(\n\u001b[1;32m   1188\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_query_flags,\n\u001b[1;32m   1189\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_collection\u001b[38;5;241m.\u001b[39mdatabase\u001b[38;5;241m.\u001b[39mname,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1203\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exhaust,\n\u001b[1;32m   1204\u001b[0m     )\n\u001b[0;32m-> 1205\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_message(q)\n\u001b[1;32m   1206\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_id:  \u001b[38;5;66;03m# Get More\u001b[39;00m\n\u001b[1;32m   1207\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_limit:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/cursor.py:1100\u001b[0m, in \u001b[0;36mCursor._send_message\u001b[0;34m(self, operation)\u001b[0m\n\u001b[1;32m   1097\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m InvalidOperation(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mexhaust cursors do not support auto encryption\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   1099\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1100\u001b[0m     response \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39m_run_operation(\n\u001b[1;32m   1101\u001b[0m         operation, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_unpack_response, address\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_address\n\u001b[1;32m   1102\u001b[0m     )\n\u001b[1;32m   1103\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m OperationFailure \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m   1104\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mcode \u001b[38;5;129;01min\u001b[39;00m _CURSOR_CLOSED_ERRORS \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exhaust:\n\u001b[1;32m   1105\u001b[0m         \u001b[38;5;66;03m# Don't send killCursors because the cursor is already closed.\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/_csot.py:119\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    117\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[1;32m    118\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m--> 119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:1754\u001b[0m, in \u001b[0;36mMongoClient._run_operation\u001b[0;34m(self, operation, unpack_res, address)\u001b[0m\n\u001b[1;32m   1744\u001b[0m     operation\u001b[38;5;241m.\u001b[39mreset()  \u001b[38;5;66;03m# Reset op in case of retry.\u001b[39;00m\n\u001b[1;32m   1745\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m server\u001b[38;5;241m.\u001b[39mrun_operation(\n\u001b[1;32m   1746\u001b[0m         conn,\n\u001b[1;32m   1747\u001b[0m         operation,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1751\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1752\u001b[0m     )\n\u001b[0;32m-> 1754\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retryable_read(\n\u001b[1;32m   1755\u001b[0m     _cmd,\n\u001b[1;32m   1756\u001b[0m     operation\u001b[38;5;241m.\u001b[39mread_preference,\n\u001b[1;32m   1757\u001b[0m     operation\u001b[38;5;241m.\u001b[39msession,  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[1;32m   1758\u001b[0m     address\u001b[38;5;241m=\u001b[39maddress,\n\u001b[1;32m   1759\u001b[0m     retryable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28misinstance\u001b[39m(operation, _Query),\n\u001b[1;32m   1760\u001b[0m     operation\u001b[38;5;241m=\u001b[39moperation\u001b[38;5;241m.\u001b[39mname,\n\u001b[1;32m   1761\u001b[0m )\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:1863\u001b[0m, in \u001b[0;36mMongoClient._retryable_read\u001b[0;34m(self, func, read_pref, session, operation, address, retryable, operation_id)\u001b[0m\n\u001b[1;32m   1858\u001b[0m \u001b[38;5;66;03m# Ensure that the client supports retrying on reads and there is no session in\u001b[39;00m\n\u001b[1;32m   1859\u001b[0m \u001b[38;5;66;03m# transaction, otherwise, we will not support retry behavior for this call.\u001b[39;00m\n\u001b[1;32m   1860\u001b[0m retryable \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mbool\u001b[39m(\n\u001b[1;32m   1861\u001b[0m     retryable \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mretry_reads \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (session \u001b[38;5;129;01mand\u001b[39;00m session\u001b[38;5;241m.\u001b[39min_transaction)\n\u001b[1;32m   1862\u001b[0m )\n\u001b[0;32m-> 1863\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retry_internal(\n\u001b[1;32m   1864\u001b[0m     func,\n\u001b[1;32m   1865\u001b[0m     session,\n\u001b[1;32m   1866\u001b[0m     \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1867\u001b[0m     operation,\n\u001b[1;32m   1868\u001b[0m     is_read\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m   1869\u001b[0m     address\u001b[38;5;241m=\u001b[39maddress,\n\u001b[1;32m   1870\u001b[0m     read_pref\u001b[38;5;241m=\u001b[39mread_pref,\n\u001b[1;32m   1871\u001b[0m     retryable\u001b[38;5;241m=\u001b[39mretryable,\n\u001b[1;32m   1872\u001b[0m     operation_id\u001b[38;5;241m=\u001b[39moperation_id,\n\u001b[1;32m   1873\u001b[0m )\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/_csot.py:119\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    117\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[1;32m    118\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m--> 119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:1830\u001b[0m, in \u001b[0;36mMongoClient._retry_internal\u001b[0;34m(self, func, session, bulk, operation, is_read, address, read_pref, retryable, operation_id)\u001b[0m\n\u001b[1;32m   1793\u001b[0m \u001b[38;5;129m@_csot\u001b[39m\u001b[38;5;241m.\u001b[39mapply\n\u001b[1;32m   1794\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_retry_internal\u001b[39m(\n\u001b[1;32m   1795\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1804\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1805\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[1;32m   1806\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Internal retryable helper for all client transactions.\u001b[39;00m\n\u001b[1;32m   1807\u001b[0m \n\u001b[1;32m   1808\u001b[0m \u001b[38;5;124;03m    :param func: Callback function we want to retry\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1817\u001b[0m \u001b[38;5;124;03m    :return: Output of the calling func()\u001b[39;00m\n\u001b[1;32m   1818\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m   1819\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _ClientConnectionRetryable(\n\u001b[1;32m   1820\u001b[0m         mongo_client\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1821\u001b[0m         func\u001b[38;5;241m=\u001b[39mfunc,\n\u001b[1;32m   1822\u001b[0m         bulk\u001b[38;5;241m=\u001b[39mbulk,\n\u001b[1;32m   1823\u001b[0m         operation\u001b[38;5;241m=\u001b[39moperation,\n\u001b[1;32m   1824\u001b[0m         is_read\u001b[38;5;241m=\u001b[39mis_read,\n\u001b[1;32m   1825\u001b[0m         session\u001b[38;5;241m=\u001b[39msession,\n\u001b[1;32m   1826\u001b[0m         read_pref\u001b[38;5;241m=\u001b[39mread_pref,\n\u001b[1;32m   1827\u001b[0m         address\u001b[38;5;241m=\u001b[39maddress,\n\u001b[1;32m   1828\u001b[0m         retryable\u001b[38;5;241m=\u001b[39mretryable,\n\u001b[1;32m   1829\u001b[0m         operation_id\u001b[38;5;241m=\u001b[39moperation_id,\n\u001b[0;32m-> 1830\u001b[0m     )\u001b[38;5;241m.\u001b[39mrun()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:2554\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable.run\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2552\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error(check_csot\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m   2553\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 2554\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_read \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_write()\n\u001b[1;32m   2555\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ServerSelectionTimeoutError:\n\u001b[1;32m   2556\u001b[0m     \u001b[38;5;66;03m# The application may think the write was never attempted\u001b[39;00m\n\u001b[1;32m   2557\u001b[0m     \u001b[38;5;66;03m# if we raise ServerSelectionTimeoutError on the retry\u001b[39;00m\n\u001b[1;32m   2558\u001b[0m     \u001b[38;5;66;03m# attempt. Raise the original exception instead.\u001b[39;00m\n\u001b[1;32m   2559\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error()\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:2689\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._read\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2684\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_read\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[1;32m   2685\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Wrapper method for read-type retryable client executions\u001b[39;00m\n\u001b[1;32m   2686\u001b[0m \n\u001b[1;32m   2687\u001b[0m \u001b[38;5;124;03m    :return: Output for func()'s call\u001b[39;00m\n\u001b[1;32m   2688\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 2689\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_server()\n\u001b[1;32m   2690\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_pref \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRead Preference required on read calls\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2691\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39m_conn_from_server(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_pref, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_session) \u001b[38;5;28;01mas\u001b[39;00m (\n\u001b[1;32m   2692\u001b[0m         conn,\n\u001b[1;32m   2693\u001b[0m         read_pref,\n\u001b[1;32m   2694\u001b[0m     ):\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:2645\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._get_server\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2640\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_get_server\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[1;32m   2641\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Retrieves a server object based on provided object context\u001b[39;00m\n\u001b[1;32m   2642\u001b[0m \n\u001b[1;32m   2643\u001b[0m \u001b[38;5;124;03m    :return: Abstraction to connect to server\u001b[39;00m\n\u001b[1;32m   2644\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 2645\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39m_select_server(\n\u001b[1;32m   2646\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_server_selector,\n\u001b[1;32m   2647\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_session,\n\u001b[1;32m   2648\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_operation,\n\u001b[1;32m   2649\u001b[0m         address\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_address,\n\u001b[1;32m   2650\u001b[0m         deprioritized_servers\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_deprioritized_servers,\n\u001b[1;32m   2651\u001b[0m         operation_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_operation_id,\n\u001b[1;32m   2652\u001b[0m     )\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/mongo_client.py:1649\u001b[0m, in \u001b[0;36mMongoClient._select_server\u001b[0;34m(self, server_selector, session, operation, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[1;32m   1647\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m AutoReconnect(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mserver \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m no longer available\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m address)  \u001b[38;5;66;03m# noqa: UP031\u001b[39;00m\n\u001b[1;32m   1648\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1649\u001b[0m         server \u001b[38;5;241m=\u001b[39m topology\u001b[38;5;241m.\u001b[39mselect_server(\n\u001b[1;32m   1650\u001b[0m             server_selector,\n\u001b[1;32m   1651\u001b[0m             operation,\n\u001b[1;32m   1652\u001b[0m             deprioritized_servers\u001b[38;5;241m=\u001b[39mdeprioritized_servers,\n\u001b[1;32m   1653\u001b[0m             operation_id\u001b[38;5;241m=\u001b[39moperation_id,\n\u001b[1;32m   1654\u001b[0m         )\n\u001b[1;32m   1655\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m server\n\u001b[1;32m   1656\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m PyMongoError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m   1657\u001b[0m     \u001b[38;5;66;03m# Server selection errors in a transaction are transient.\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/topology.py:398\u001b[0m, in \u001b[0;36mTopology.select_server\u001b[0;34m(self, selector, operation, server_selection_timeout, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[1;32m    388\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mselect_server\u001b[39m(\n\u001b[1;32m    389\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    390\u001b[0m     selector: Callable[[Selection], Selection],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    395\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    396\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[1;32m    397\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Like select_servers, but choose a random server if several match.\"\"\"\u001b[39;00m\n\u001b[0;32m--> 398\u001b[0m     server \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_select_server(\n\u001b[1;32m    399\u001b[0m         selector,\n\u001b[1;32m    400\u001b[0m         operation,\n\u001b[1;32m    401\u001b[0m         server_selection_timeout,\n\u001b[1;32m    402\u001b[0m         address,\n\u001b[1;32m    403\u001b[0m         deprioritized_servers,\n\u001b[1;32m    404\u001b[0m         operation_id\u001b[38;5;241m=\u001b[39moperation_id,\n\u001b[1;32m    405\u001b[0m     )\n\u001b[1;32m    406\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _csot\u001b[38;5;241m.\u001b[39mget_timeout():\n\u001b[1;32m    407\u001b[0m         _csot\u001b[38;5;241m.\u001b[39mset_rtt(server\u001b[38;5;241m.\u001b[39mdescription\u001b[38;5;241m.\u001b[39mmin_round_trip_time)\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/topology.py:376\u001b[0m, in \u001b[0;36mTopology._select_server\u001b[0;34m(self, selector, operation, server_selection_timeout, address, deprioritized_servers, operation_id)\u001b[0m\n\u001b[1;32m    367\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_select_server\u001b[39m(\n\u001b[1;32m    368\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    369\u001b[0m     selector: Callable[[Selection], Selection],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    374\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    375\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Server:\n\u001b[0;32m--> 376\u001b[0m     servers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mselect_servers(\n\u001b[1;32m    377\u001b[0m         selector, operation, server_selection_timeout, address, operation_id\n\u001b[1;32m    378\u001b[0m     )\n\u001b[1;32m    379\u001b[0m     servers \u001b[38;5;241m=\u001b[39m _filter_servers(servers, deprioritized_servers)\n\u001b[1;32m    380\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(servers) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/topology.py:283\u001b[0m, in \u001b[0;36mTopology.select_servers\u001b[0;34m(self, selector, operation, server_selection_timeout, address, operation_id)\u001b[0m\n\u001b[1;32m    280\u001b[0m     server_timeout \u001b[38;5;241m=\u001b[39m server_selection_timeout\n\u001b[1;32m    282\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock:\n\u001b[0;32m--> 283\u001b[0m     server_descriptions \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_select_servers_loop(\n\u001b[1;32m    284\u001b[0m         selector, server_timeout, operation, operation_id, address\n\u001b[1;32m    285\u001b[0m     )\n\u001b[1;32m    287\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [\n\u001b[1;32m    288\u001b[0m         cast(Server, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_server_by_address(sd\u001b[38;5;241m.\u001b[39maddress)) \u001b[38;5;28;01mfor\u001b[39;00m sd \u001b[38;5;129;01min\u001b[39;00m server_descriptions\n\u001b[1;32m    289\u001b[0m     ]\n", "File \u001b[0;32m~/anaconda3/envs/disengagement/lib/python3.11/site-packages/pymongo/synchronous/topology.py:333\u001b[0m, in \u001b[0;36mTopology._select_servers_loop\u001b[0;34m(self, selector, timeout, operation, operation_id, address)\u001b[0m\n\u001b[1;32m    322\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _SERVER_SELECTION_LOGGER\u001b[38;5;241m.\u001b[39misEnabledFor(logging\u001b[38;5;241m.\u001b[39mDEBUG):\n\u001b[1;32m    323\u001b[0m         _debug_log(\n\u001b[1;32m    324\u001b[0m             _SERVER_SELECTION_LOGGER,\n\u001b[1;32m    325\u001b[0m             message\u001b[38;5;241m=\u001b[39m_ServerSelectionStatusMessage\u001b[38;5;241m.\u001b[39mFAILED,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    331\u001b[0m             failure\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_error_message(selector),\n\u001b[1;32m    332\u001b[0m         )\n\u001b[0;32m--> 333\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ServerSelectionTimeoutError(\n\u001b[1;32m    334\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_error_message(selector)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Timeout: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtimeout\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124ms, Topology Description: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdescription\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    335\u001b[0m     )\n\u001b[1;32m    337\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m logged_waiting:\n\u001b[1;32m    338\u001b[0m     _debug_log(\n\u001b[1;32m    339\u001b[0m         _SERVER_SELECTION_LOGGER,\n\u001b[1;32m    340\u001b[0m         message\u001b[38;5;241m=\u001b[39m_ServerSelectionStatusMessage\u001b[38;5;241m.\u001b[39mWAITING,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    346\u001b[0m         remainingTimeMS\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mint\u001b[39m(end_time \u001b[38;5;241m-\u001b[39m time\u001b[38;5;241m.\u001b[39mmonotonic()),\n\u001b[1;32m    347\u001b[0m     )\n", "\u001b[0;31mServerSelectionTimeoutError\u001b[0m: localhost :27017: [Errno -2] Name or service not known (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 674481601395f9bec1cf5543, topology_type: Unknown, servers: [<ServerDescription ('localhost ', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost :27017: [Errno -2] Name or service not known (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>"]}], "source": ["\n", "# GET repo_name from collection\n", "# Fetch distinct repo_name from the collection\n", "\n", "\n", "for repo_name in project_names:\n", "    WINDOWS_IP = \"localhost \"\n", "    PORT = 27017\n", "    client = MongoClient(f\"mongodb://{WINDOWS_IP}:{PORT}/\")\n", "    db = client[\"disengagement\"]\n", "    collection = db[\"project_analysis\"]\n", "    data = list(collection.find({\"repo_name\":repo_name}))\n", "    if not data:\n", "        print('not found', repo_name)\n", "        failed_repo_list.append(repo_name)\n", "\n", "print(failed_repo_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d63f2ae2ab551fb8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}