#!/bin/bash

echo "Installing optimization dependencies..."

# 安装基础优化包
pip install numba
pip install joblib

# 安装GPU加速包（如果可用）
if command -v nvidia-smi &> /dev/null; then
    echo "NVIDIA GPU detected, installing CUDA packages..."
    pip install cupy-cuda12x  # 根据CUDA版本调整
else
    echo "No NVIDIA GPU detected, skipping CUDA packages"
fi

# 安装其他优化包
pip install scikit-learn
pip install pandas
pip install numpy

echo "Dependencies installation completed!" 