#!/usr/bin/env Rscript

# =============================================================================
# Performance Comparison Script
# Purpose: Compare execution times of different DID analysis versions
# =============================================================================

# Load required libraries
suppressPackageStartupMessages({
  library(microbenchmark)
  library(ggplot2)
  library(dplyr)
  library(knitr)
})

# Function to run performance test
run_performance_test <- function(script_name, description) {
  cat("🔍 Testing:", description, "\n")
  
  start_time <- Sys.time()
  
  # Run the script
  result <- tryCatch({
    source(script_name)
    "SUCCESS"
  }, error = function(e) {
    paste("ERROR:", e$message)
  })
  
  end_time <- Sys.time()
  execution_time <- difftime(end_time, start_time, units = "mins")
  
  cat("   Result:", result, "\n")
  cat("   Time:", round(execution_time, 2), "minutes\n")
  cat("   ", paste(rep("-", 40), collapse=""), "\n")
  
  return(list(
    script = script_name,
    description = description,
    result = result,
    execution_time = execution_time,
    timestamp = Sys.time()
  ))
}

# Main performance comparison
main_performance_comparison <- function() {
  cat("🚀 DID Analysis Performance Comparison\n")
  cat("=", paste(rep("=", 50), collapse=""), "\n")
  
  # Define test scripts
  test_scripts <- list(
    list(
      name = "automated_did_analysis.R",
      description = "Original Sequential Version"
    ),
    list(
      name = "automated_did_analysis_parallel.R", 
      description = "Parallel Version (Basic)"
    ),
    list(
      name = "automated_did_analysis_ultra_optimized.R",
      description = "Ultra-Optimized Version"
    )
  )
  
  # Run performance tests
  results <- list()
  
  for (script in test_scripts) {
    if (file.exists(script$name)) {
      result <- run_performance_test(script$name, script$description)
      results[[length(results) + 1]] <- result
    } else {
      cat("⚠️  Script not found:", script$name, "\n")
    }
  }
  
  # Create performance summary
  if (length(results) > 0) {
    cat("\n", paste(rep("=", 60), collapse=""), "\n")
    cat("📊 PERFORMANCE SUMMARY\n")
    cat(paste(rep("=", 60), collapse=""), "\n")
    
    # Create summary table
    summary_data <- do.call(rbind, lapply(results, function(r) {
      data.frame(
        Version = r$description,
        Execution_Time_Minutes = round(r$execution_time, 2),
        Status = r$result,
        Timestamp = r$timestamp
      )
    }))
    
    # Print summary table
    print(knitr::kable(summary_data, format = "simple"))
    
    # Calculate speedup
    if (nrow(summary_data) > 1) {
      cat("\n🚀 SPEEDUP ANALYSIS:\n")
      baseline_time <- summary_data$Execution_Time_Minutes[1]
      
      for (i in 2:nrow(summary_data)) {
        speedup <- baseline_time / summary_data$Execution_Time_Minutes[i]
        improvement <- ((baseline_time - summary_data$Execution_Time_Minutes[i]) / baseline_time) * 100
        cat("  ", summary_data$Version[i], ":\n")
        cat("    Speedup:", round(speedup, 2), "x\n")
        cat("    Improvement:", round(improvement, 1), "%\n")
      }
    }
    
    # Save results
    results_file <- "performance_comparison_results.csv"
    write.csv(summary_data, results_file, row.names = FALSE)
    cat("\n💾 Results saved to:", results_file, "\n")
    
    # Create visualization if ggplot2 is available
    if (requireNamespace("ggplot2", quietly = TRUE)) {
      tryCatch({
        p <- ggplot(summary_data, aes(x = Version, y = Execution_Time_Minutes, fill = Version)) +
          geom_bar(stat = "identity") +
          geom_text(aes(label = paste0(Execution_Time_Minutes, " min")), 
                   vjust = -0.5, size = 3) +
          labs(title = "DID Analysis Performance Comparison",
               x = "Version", y = "Execution Time (minutes)") +
          theme_minimal() +
          theme(axis.text.x = element_text(angle = 45, hjust = 1),
                legend.position = "none")
        
        plot_file <- "performance_comparison_plot.png"
        ggsave(plot_file, p, width = 10, height = 6)
        cat("📊 Performance plot saved to:", plot_file, "\n")
      }, error = function(e) {
        cat("⚠️  Could not create performance plot:", e$message, "\n")
      })
    }
  }
  
  cat("\n🎉 Performance comparison completed!\n")
}

# Run the performance comparison
if (!interactive()) {
  main_performance_comparison()
} 