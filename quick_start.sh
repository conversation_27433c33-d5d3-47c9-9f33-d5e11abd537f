#!/bin/bash

# =============================================================================
# Quick Start Script for DID Analysis Performance Optimization
# Purpose: One-click setup and execution
# =============================================================================

echo "🚀 DID Analysis Performance Optimization - Quick Start"
echo "======================================================"

# Check if R is installed
if ! command -v R &> /dev/null; then
    echo "❌ R is not installed. Please install R first."
    echo "   Visit: https://cran.r-project.org/"
    exit 1
fi

# Check if Rscript is available
if ! command -v Rscript &> /dev/null; then
    echo "❌ Rscript is not available. Please check your R installation."
    exit 1
fi

echo "✅ R installation found: $(R --version | head -n 1)"

# Check system resources
echo ""
echo "💻 System Resources:"
echo "   CPU cores: $(nproc)"
if command -v free &> /dev/null; then
    echo "   Memory: $(free -h | grep '^Mem:' | awk '{print $2}')"
else
    echo "   Memory: Unable to determine"
fi

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
Rscript install_dependencies.R

# Check if data files exist
echo ""
echo "📁 Checking data files..."
data_dir="/home/<USER>/repo/disengagement/result/20250629_did_result"
if [ -d "$data_dir" ]; then
    echo "✅ Data directory found: $data_dir"
    
    # Check for specific data files
    for limit in 180 270 450; do
        file="$data_dir/compiled_data_test_limit${limit}_processed.csv"
        if [ -f "$file" ]; then
            echo "✅ Data file found: compiled_data_test_limit${limit}_processed.csv"
        else
            echo "⚠️  Data file missing: compiled_data_test_limit${limit}_processed.csv"
        fi
    done
else
    echo "⚠️  Data directory not found: $data_dir"
    echo "   Please ensure your data files are in the correct location."
fi

# Ask user which version to run
echo ""
echo "🎯 Choose which version to run:"
echo "1. Original Sequential Version (for testing/debugging)"
echo "2. Parallel Version (recommended for production)"
echo "3. Ultra-Optimized Version (for maximum performance)"
echo "4. Performance Comparison (run all versions and compare)"
echo "5. Exit"

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🚀 Running Original Sequential Version..."
        Rscript automated_did_analysis.R
        ;;
    2)
        echo ""
        echo "🚀 Running Parallel Version (recommended)..."
        Rscript automated_did_analysis_parallel.R
        ;;
    3)
        echo ""
        echo "🚀 Running Ultra-Optimized Version..."
        Rscript automated_did_analysis_ultra_optimized.R
        ;;
    4)
        echo ""
        echo "🚀 Running Performance Comparison..."
        Rscript run_performance_comparison.R
        ;;
    5)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Analysis completed!"
echo ""
echo "📊 Results are saved in: /home/<USER>/repo/disengagement/result/20250629_did_result/"
echo "📖 For detailed information, see: README_PERFORMANCE_OPTIMIZATION.md" 