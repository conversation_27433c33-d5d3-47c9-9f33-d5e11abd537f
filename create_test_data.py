import pandas as pd
import numpy as np
import os

def create_test_data():
    """从365.csv中采样10000个数据用于测试"""
    
    # 读取原始数据
    input_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365.csv'
    output_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365_test_sample.csv'
    
    print(f"Reading data from: {input_file}")
    df = pd.read_csv(input_file)
    print(f"Original data shape: {df.shape}")
    
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    
    # 采样10000个数据点
    if len(df) > 10000:
        sample_df = df.sample(n=10000, random_state=42)
        print(f"Sampled {len(sample_df)} rows from {len(df)} total rows")
    else:
        sample_df = df.copy()
        print(f"Data has only {len(df)} rows, using all data")
    
    # 保存采样数据
    sample_df.to_csv(output_file, index=False)
    print(f"Test data saved to: {output_file}")
    
    # 显示一些基本统计信息
    print("\n=== Test Data Statistics ===")
    print(f"Shape: {sample_df.shape}")
    print(f"Columns: {list(sample_df.columns)}")
    
    if 'someone_left' in sample_df.columns:
        left_count = sample_df['someone_left'].sum()
        print(f"someone_left=1 count: {left_count}")
    
    if 'feature_sigmod_add' in sample_df.columns:
        feature_stats = sample_df['feature_sigmod_add'].describe()
        print(f"feature_sigmod_add statistics:\n{feature_stats}")
        
        # 检查过滤条件
        not_null_count = sample_df['feature_sigmod_add'].notnull().sum()
        not_equal_05_count = (sample_df['feature_sigmod_add'] != 0.5).sum()
        print(f"feature_sigmod_add not null: {not_null_count}")
        print(f"feature_sigmod_add != 0.5: {not_equal_05_count}")
    
    if 'repo_name' in sample_df.columns:
        unique_repos = sample_df['repo_name'].nunique()
        print(f"Unique repositories: {unique_repos}")
    
    return output_file

if __name__ == "__main__":
    create_test_data()
