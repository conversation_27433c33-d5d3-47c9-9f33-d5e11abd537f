#!/bin/bash

# PSM匹配脚本启动器
# 使用nohup确保在用户断开连接后继续运行

echo "=========================================="
echo "PSM Matching Script Launcher"
echo "Started at: $(date)"
echo "=========================================="

# 设置工作目录
cd /home/<USER>/repo/disengagement

# 创建日志目录
mkdir -p logs

# 使用nohup运行Python脚本
# 将输出重定向到日志文件
nohup python src_new/20250629_PSM_matching_multi_limits.py > logs/psm_matching_$(date +%Y%m%d_%H%M%S).log 2>&1 &

# 获取进程ID
PID=$!
echo "PSM matching script started with PID: $PID"
echo "Log file: logs/psm_matching_$(date +%Y%m%d_%H%M%S).log"
echo "You can safely disconnect now. The script will continue running."
echo "To check progress, use: tail -f logs/psm_matching_*.log"
echo "To check if process is still running: ps aux | grep 20250629_PSM_matching_multi_limits.py"
echo "==========================================" 