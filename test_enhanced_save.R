#!/usr/bin/env Rscript

# Test script for enhanced DID analysis saving functionality
suppressPackageStartupMessages({
  library(stats)
  library(lme4)
  library(readr)
  library(ggplot2)
  library(stargazer)
  library(lmtest)
  library(MuMIn)
  library(lmerTest)
  library(survival)
  library(ggpubr)
  library(survminer)
  library(car)
  library(coxme)
  library(dplyr)
  library(broom)
  library(broom.mixed)
})

# Test configuration
limit <- 180
base_result_dir <- "result/20250629_did_result"
output_dir <- file.path(base_result_dir, paste0("test_enhanced_attrition_", limit))

# Create output directory
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  cat("Created directory:", output_dir, "\n")
}

# Load test data
data_file <- "/home/<USER>/repo/disengagement/result/20250629_did_result/compiled_data_test_limit180_processed.csv"
cat("Loading data from:", data_file, "\n")

if (!file.exists(data_file)) {
  cat("❌ Data file not found:", data_file, "\n")
  quit(status = 1)
}

compiled_data_test <- read.csv(data_file)
cat("Data loaded. Initial observations:", nrow(compiled_data_test), "\n")

# Simple preprocessing
compiled_data_test <- compiled_data_test %>%
  mutate(
    log_tenure_c = scale(log_tenure),
    log_commit_percent_c = scale(log_commit_percent),
    log_commits_c = scale(log_commits),
    log_project_commits = scale(log_project_commits),
    log_project_contributors = scale(log_project_contributors),
    log_project_age = scale(log_project_age)
  )

# Filter growth phases
compiled_data_test <- compiled_data_test[!is.na(compiled_data_test$growth_phase) & compiled_data_test$growth_phase != '', ]
compiled_data_test <- compiled_data_test[compiled_data_test$growth_phase %in% c('accelerating', 'decelerating', 'first 3 months', 'saturation', 'steady'), ]

cat("After preprocessing. Observations:", nrow(compiled_data_test), "\n")

# Set up control parameters
ctrl <- lmerControl(
  optimizer = "nloptwrap",
  optCtrl = list(maxeval = 1e5, xtol_abs = 1e-8, ftol_abs = 1e-8),
  calc.derivs = FALSE
)

# Function to check if model already exists
model_exists <- function(model_name, limit, output_dir) {
  model_file <- file.path(output_dir, paste0(model_name, ".rds"))
  return(file.exists(model_file))
}

# Function to load existing model
load_model <- function(model_name, limit, output_dir) {
  model_file <- file.path(output_dir, paste0(model_name, ".rds"))
  if (file.exists(model_file)) {
    cat("📂 Loading existing model:", model_name, "\n")
    return(readRDS(model_file))
  }
  return(NULL)
}

# Function to save model object
save_model_object <- function(model, model_name, limit, output_dir) {
  model_file <- file.path(output_dir, paste0(model_name, ".rds"))
  tryCatch({
    saveRDS(model, model_file)
    cat("💾 Model object saved:", model_file, "\n")
  }, error = function(e) {
    cat("❌ Error saving model object:", e$message, "\n")
  })
}

# Enhanced function to save model results using multiple methods
save_model_results_enhanced <- function(model, model_name, limit, output_dir) {
  cat("📝 Saving comprehensive results for:", model_name, "\n")
  
  # 1. Save model object
  save_model_object(model, model_name, limit, output_dir)
  
  # 2. Save detailed text results
  text_file <- file.path(output_dir, paste0(model_name, "_detailed.txt"))
  
  tryCatch({
    # Capture all output
    output_text <- capture.output({
      cat("=== DID Model Results:", model_name, "=== (Limit:", limit, ")\n\n")
      cat("Analysis Date:", Sys.time(), "\n\n")
      
      # Model formula
      cat("Model Formula:\n")
      cat(as.character(formula(model)), "\n\n")
      
      # Model convergence info
      cat("Model Convergence:\n")
      if (is.null(model@optinfo$conv$lme4$messages)) {
        cat("✓ Model converged successfully\n")
      } else {
        cat("⚠️  Model convergence warnings:\n")
        cat(model@optinfo$conv$lme4$messages, "\n")
      }
      cat("\n")
      
      # Model summary using capture.output
      cat("Model Summary:\n")
      print(summary(model))
      cat("\n")
      
      # VIF values
      cat("Variance Inflation Factors (VIF):\n")
      tryCatch({
        vif_values <- vif(model)
        print(vif_values)
      }, error = function(e) {
        cat("VIF calculation failed:", e$message, "\n")
      })
      cat("\n")
      
      # R-squared values
      cat("R-squared Values:\n")
      tryCatch({
        r2_values <- r.squaredGLMM(model)
        print(r2_values)
      }, error = function(e) {
        cat("R-squared calculation failed:", e$message, "\n")
      })
      cat("\n")
      
      # Model diagnostics
      cat("Model Diagnostics:\n")
      cat("AIC:", AIC(model), "\n")
      cat("BIC:", BIC(model), "\n")
      cat("Log-likelihood:", logLik(model), "\n")
      cat("\n")
      
      # Random effects summary
      cat("Random Effects Summary:\n")
      print(VarCorr(model))
      cat("\n")
      
      cat("=== End of Detailed Results ===\n")
    })
    
    # Write to file using sink
    sink(text_file)
    cat(paste(output_text, collapse = "\n"))
    sink()
    
    cat("✓ Detailed results saved:", text_file, "(Size:", file.size(text_file), "bytes)\n")
    
  }, error = function(e) {
    sink()  # Close sink in case of error
    cat("❌ Error saving detailed results:", e$message, "\n")
  })
  
  # 3. Save tidy results using broom
  tidy_file <- file.path(output_dir, paste0(model_name, "_tidy.csv"))
  tryCatch({
    # Fixed effects
    fixed_effects <- tidy(model, conf.int = TRUE, conf.level = 0.95)
    write.csv(fixed_effects, tidy_file, row.names = FALSE)
    cat("✓ Tidy fixed effects saved:", tidy_file, "\n")
    
    # Random effects
    random_effects <- tidy(model, effects = "ran_pars")
    random_file <- file.path(output_dir, paste0(model_name, "_random_effects.csv"))
    write.csv(random_effects, random_file, row.names = FALSE)
    cat("✓ Random effects saved:", random_file, "\n")
    
    # Model performance metrics
    performance <- glance(model)
    performance_file <- file.path(output_dir, paste0(model_name, "_performance.csv"))
    write.csv(performance, performance_file, row.names = FALSE)
    cat("✓ Performance metrics saved:", performance_file, "\n")
    
  }, error = function(e) {
    cat("❌ Error saving tidy results:", e$message, "\n")
  })
  
  # 4. Save stargazer table (with better error handling for mixed models)
  stargazer_file <- file.path(output_dir, paste0(model_name, "_stargazer.txt"))
  tryCatch({
    sink(stargazer_file)
    # For mixed models, we need to extract fixed effects for stargazer
    fixed_effects <- fixef(model)
    stargazer(as.data.frame(t(fixed_effects)), type = "text", 
              title = paste("DID Model Results:", model_name, "(Limit:", limit, ")"),
              column.labels = c("Fixed Effects"),
              dep.var.labels = "Dependent Variable",
              notes = paste("Analysis date:", Sys.time(), "\nNote: Only fixed effects shown"))
    sink()
    cat("✓ Stargazer table saved:", stargazer_file, "\n")
  }, error = function(e) {
    sink()  # Close sink in case of error
    cat("⚠️  Stargazer table not saved (mixed model limitation):", e$message, "\n")
  })
}

# Test the enhanced saving functionality
cat("\n--- Testing Enhanced Saving Functionality ---\n")

model_name <- "test_enhanced_did_main_pr_throughput"

# Check if model already exists
if (model_exists(model_name, limit, output_dir)) {
  cat("📂 Model already exists, loading...\n")
  model <- load_model(model_name, limit, output_dir)
  if (!is.null(model)) {
    cat("✓ Model loaded successfully\n")
    save_model_results_enhanced(model, model_name, limit, output_dir)
  }
} else {
  cat("🔧 Fitting new model...\n")
  tryCatch({
    model <- lmer(
      log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +
        log_project_commits + log_project_contributors + log_project_age + 
        (1 | time_cohort_effect) + (1 | repo_cohort_effect),
      REML = FALSE, data = compiled_data_test, control = ctrl
    )
    cat("✓ Model fitted successfully\n")
    save_model_results_enhanced(model, model_name, limit, output_dir)
  }, error = function(e) {
    cat("❌ Error fitting model:", e$message, "\n")
  })
}

# List all created files
cat("\n--- Created Files Summary ---\n")
if (dir.exists(output_dir)) {
  files <- list.files(output_dir, pattern = "\\.(txt|csv|rds)$")
  cat("Total files created:", length(files), "\n")
  for (file in files) {
    file_size <- file.size(file.path(output_dir, file))
    cat("  📄", file, "(", file_size, "bytes)\n")
  }
}

cat("\n✅ Enhanced saving test completed!\n") 