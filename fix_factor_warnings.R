# Fix for "contrasts dropped from factor due to missing levels" warnings
# 
# The issue occurs because you're creating factors and setting contrasts 
# after filtering the data, but R tries to create contrast matrices for 
# all possible levels, including the ones that were filtered out.

# Instead of this (problematic code):
# compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language)
# compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase)
# compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = "JavaScript")
# compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = "steady")
# contrasts(compiled_data_test$project_main_language) <- "contr.sum"
# contrasts(compiled_data_test$growth_phase) <- "contr.sum"

# Use this (fixed code):
# 重新创建因子，确保只包含实际存在的水平
compiled_data_test$project_main_language <- factor(compiled_data_test$project_main_language, 
                                                  levels = unique(compiled_data_test$project_main_language))
compiled_data_test$growth_phase <- factor(compiled_data_test$growth_phase, 
                                         levels = unique(compiled_data_test$growth_phase))

# 设置参考水平
compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = "JavaScript")
compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = "steady")

# 设置对比方式
contrasts(compiled_data_test$project_main_language) <- "contr.sum"
contrasts(compiled_data_test$growth_phase) <- "contr.sum"

# Alternative approach using droplevels():
# compiled_data_test$project_main_language <- droplevels(factor(compiled_data_test$project_main_language))
# compiled_data_test$growth_phase <- droplevels(factor(compiled_data_test$growth_phase))
# compiled_data_test$project_main_language <- relevel(compiled_data_test$project_main_language, ref = "JavaScript")
# compiled_data_test$growth_phase <- relevel(compiled_data_test$growth_phase, ref = "steady")
# contrasts(compiled_data_test$project_main_language) <- "contr.sum"
# contrasts(compiled_data_test$growth_phase) <- "contr.sum" 