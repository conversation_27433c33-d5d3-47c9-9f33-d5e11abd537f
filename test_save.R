#!/usr/bin/env Rscript

# Test script to verify file saving functionality
cat("Testing file saving functionality...\n")

# Test 1: Simple file writing
test_file <- "test_output.txt"
cat("Writing test content to:", test_file, "\n")

sink(test_file)
cat("=== Test Output ===\n")
cat("This is a test file.\n")
cat("Current time:", Sys.time(), "\n")
cat("=== End Test ===\n")
sink()

cat("✓ Test file written. Size:", file.size(test_file), "bytes\n")

# Test 2: Check if file exists and has content
if (file.exists(test_file)) {
  cat("✓ File exists\n")
  content <- readLines(test_file)
  cat("✓ File content:\n")
  cat(paste(content, collapse = "\n"), "\n")
} else {
  cat("❌ File does not exist\n")
}

# Test 3: Test with absolute path
abs_test_file <- "/home/<USER>/repo/disengagement/test_abs_output.txt"
cat("Writing to absolute path:", abs_test_file, "\n")

sink(abs_test_file)
cat("=== Absolute Path Test ===\n")
cat("This is a test with absolute path.\n")
cat("Current directory:", getwd(), "\n")
cat("=== End Test ===\n")
sink()

cat("✓ Absolute path test completed. Size:", file.size(abs_test_file), "bytes\n")

cat("All tests completed!\n") 