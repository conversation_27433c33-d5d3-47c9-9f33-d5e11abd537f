#!/usr/bin/env python3
"""
测试内存修复效果的脚本
"""

import sys
import os
import psutil
import gc
import time
from datetime import datetime

def monitor_memory():
    """监控内存使用情况"""
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    return memory_mb

def test_memory_fix():
    """测试内存修复效果"""
    print(f"开始内存修复测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"可用内存: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
    print(f"可用CPU核心: {psutil.cpu_count()}")
    print("-" * 60)
    
    # 记录初始内存
    initial_memory = monitor_memory()
    print(f"初始内存使用: {initial_memory:.1f}MB")
    
    try:
        # 直接从文件中导入函数
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "psm_module", 
            "src_new_new/20250629_04_PSM_matching_multi_limits_improved.py"
        )
        psm_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(psm_module)
        
        print("成功导入修复后的模块")
        
        # 测试单个limit处理
        print("开始测试单个limit处理...")
        start_time = time.time()
        
        # 监控内存使用
        memory_before = monitor_memory()
        print(f"处理前内存: {memory_before:.1f}MB")
        
        # 运行处理
        result = psm_module.process_single_limit(365)
        
        # 监控处理后内存
        memory_after = monitor_memory()
        processing_time = time.time() - start_time
        
        print(f"处理后内存: {memory_after:.1f}MB")
        print(f"内存增长: {memory_after - memory_before:.1f}MB")
        print(f"处理时间: {processing_time:.1f}秒")
        
        if result:
            limit, total_time, peak_memory = result
            print(f"处理结果: limit={limit}, 时间={total_time:.1f}s, 峰值内存={peak_memory:.1f}MB")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 最终内存清理
        gc.collect()
        final_memory = monitor_memory()
        print(f"最终内存使用: {final_memory:.1f}MB")
        print(f"总内存变化: {final_memory - initial_memory:.1f}MB")
        print(f"测试完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_memory_fix()
