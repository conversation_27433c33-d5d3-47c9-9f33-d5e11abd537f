#!/usr/bin/env python3
"""
性能对比测试脚本
比较原始版本和优化版本的性能差异
"""

import time
import psutil
import os
import sys
import subprocess
import logging

def monitor_performance():
    """监控系统性能"""
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    return {
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'memory_available_gb': memory.available / (1024**3)
    }

def run_performance_test():
    """运行性能测试"""
    print("="*80)
    print("PSM MATCHING PERFORMANCE COMPARISON")
    print("="*80)
    
    # 检查文件是否存在
    original_script = "src_new/20250629_PSM_matching_multi_limits.py"
    optimized_script = "src_new/20250629_PSM_matching_multi_limits_optimized.py"
    
    if not os.path.exists(original_script):
        print(f"Error: Original script not found: {original_script}")
        return
    
    if not os.path.exists(optimized_script):
        print(f"Error: Optimized script not found: {optimized_script}")
        return
    
    # 测试单个limit的性能
    test_limit = 180
    
    print(f"\nTesting with limit: {test_limit}")
    print("-" * 50)
    
    # 测试原始版本
    print("Testing original version...")
    start_time = time.time()
    start_perf = monitor_performance()
    
    try:
        # 运行原始脚本（只处理一个limit）
        result = subprocess.run([
            sys.executable, original_script
        ], capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        original_time = time.time() - start_time
        end_perf = monitor_performance()
        
        print(f"Original version completed in {original_time:.2f} seconds")
        print(f"CPU usage: {start_perf['cpu_percent']:.1f}% -> {end_perf['cpu_percent']:.1f}%")
        print(f"Memory usage: {start_perf['memory_percent']:.1f}% -> {end_perf['memory_percent']:.1f}%")
        
        if result.returncode != 0:
            print(f"Original version failed: {result.stderr}")
            original_time = float('inf')
            
    except subprocess.TimeoutExpired:
        print("Original version timed out after 1 hour")
        original_time = float('inf')
    except Exception as e:
        print(f"Error running original version: {e}")
        original_time = float('inf')
    
    # 测试优化版本
    print("\nTesting optimized version...")
    start_time = time.time()
    start_perf = monitor_performance()
    
    try:
        # 运行优化脚本
        result = subprocess.run([
            sys.executable, optimized_script
        ], capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        optimized_time = time.time() - start_time
        end_perf = monitor_performance()
        
        print(f"Optimized version completed in {optimized_time:.2f} seconds")
        print(f"CPU usage: {start_perf['cpu_percent']:.1f}% -> {end_perf['cpu_percent']:.1f}%")
        print(f"Memory usage: {start_perf['memory_percent']:.1f}% -> {end_perf['memory_percent']:.1f}%")
        
        if result.returncode != 0:
            print(f"Optimized version failed: {result.stderr}")
            optimized_time = float('inf')
            
    except subprocess.TimeoutExpired:
        print("Optimized version timed out after 1 hour")
        optimized_time = float('inf')
    except Exception as e:
        print(f"Error running optimized version: {e}")
        optimized_time = float('inf')
    
    # 性能对比
    print("\n" + "="*50)
    print("PERFORMANCE COMPARISON")
    print("="*50)
    
    if original_time != float('inf') and optimized_time != float('inf'):
        speedup = original_time / optimized_time
        print(f"Original time: {original_time:.2f} seconds")
        print(f"Optimized time: {optimized_time:.2f} seconds")
        print(f"Speedup: {speedup:.2f}x")
        
        if speedup > 1:
            print(f"✅ Optimization successful! {speedup:.2f}x faster")
        else:
            print(f"❌ No improvement or slower by {(1/speedup):.2f}x")
    else:
        print("❌ One or both versions failed to complete")
    
    print("\n" + "="*80)

if __name__ == "__main__":
    run_performance_test() 