import pandas as pd
from pymongo import MongoClient
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def check_differences_breaks():
    """检查有差异的Attrition记录是否都没有Breaks"""
    logging.info("开始检查有差异的Attrition记录的Breaks情况...")
    
    # 连接数据库
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    
    # 获取所有有Attrition或Attrition_365_new字段的文档
    cursor = collection.find({
        "$or": [
            {"Attrition": {"$exists": True, "$ne": None}},
            {"Attrition_365_new": {"$exists": True, "$ne": None}}
        ]
    })
    
    differences_with_breaks = []
    differences_without_breaks = []
    
    for doc in cursor:
        repo_name = doc.get("repo_name", "")
        core_dev_login = doc.get("core_dev_login", "")
        
        # 提取Attrition数据
        attrition_dates = []
        if "Attrition" in doc and doc["Attrition"]:
            attrition = doc["Attrition"]
            if "attrition_date" in attrition:
                attrition_dates = attrition["attrition_date"]
        
        # 提取Attrition_365_new数据
        attrition_365_new_dates = []
        if "Attrition_365_new" in doc and doc["Attrition_365_new"]:
            attrition_365_new = doc["Attrition_365_new"]
            if "attrition_date" in attrition_365_new:
                attrition_365_new_dates = attrition_365_new["attrition_date"]
        
        # 提取Breaks数据
        breaks_data = []
        if "Breaks" in doc and doc["Breaks"]:
            breaks_data = doc["Breaks"]
        
        # 计算差异
        attrition_set = set(attrition_dates)
        attrition_365_new_set = set(attrition_365_new_dates)
        
        only_attrition_dates = attrition_set - attrition_365_new_set
        only_attrition_365_new_dates = attrition_365_new_set - attrition_set
        
        # 只记录有差异的数据
        if len(only_attrition_dates) > 0 or len(only_attrition_365_new_dates) > 0:
            if len(breaks_data) > 0:
                differences_with_breaks.append({
                    "repo_name": repo_name,
                    "core_dev_login": core_dev_login,
                    "attrition_dates": attrition_dates,
                    "attrition_365_new_dates": attrition_365_new_dates,
                    "only_attrition_dates": list(only_attrition_dates),
                    "only_attrition_365_new_dates": list(only_attrition_365_new_dates),
                    "breaks_count": len(breaks_data),
                    "breaks_data": breaks_data
                })
            else:
                differences_without_breaks.append({
                    "repo_name": repo_name,
                    "core_dev_login": core_dev_login,
                    "attrition_dates": attrition_dates,
                    "attrition_365_new_dates": attrition_365_new_dates,
                    "only_attrition_dates": list(only_attrition_dates),
                    "only_attrition_365_new_dates": list(only_attrition_365_new_dates),
                    "breaks_count": 0
                })
    
    logging.info(f"有差异且有Breaks的记录数: {len(differences_with_breaks)}")
    logging.info(f"有差异但无Breaks的记录数: {len(differences_without_breaks)}")
    
    # 打印前10个有Breaks的差异记录
    if differences_with_breaks:
        print(f"\n{'='*80}")
        print("前10个有Breaks的差异记录:")
        print(f"{'='*80}")
        for i, record in enumerate(differences_with_breaks[:10]):
            print(f"\n{i+1}. {record['repo_name']} - {record['core_dev_login']}")
            print(f"   Attrition日期: {record['attrition_dates']}")
            print(f"   Attrition_365_new日期: {record['attrition_365_new_dates']}")
            print(f"   仅Attrition日期: {record['only_attrition_dates']}")
            print(f"   仅Attrition_365_new日期: {record['only_attrition_365_new_dates']}")
            print(f"   Breaks数量: {record['breaks_count']}")
            print(f"   Breaks详情:")
            for j, break_info in enumerate(record['breaks_data']):
                print(f"     {j+1}. {break_info.get('start_date', '')} 到 {break_info.get('end_date', '')} "
                      f"(持续{break_info.get('duration_units', 0):.1f}单位)")
            print("-" * 60)
    
    # 打印前10个无Breaks的差异记录
    if differences_without_breaks:
        print(f"\n{'='*80}")
        print("前10个无Breaks的差异记录:")
        print(f"{'='*80}")
        for i, record in enumerate(differences_without_breaks[:10]):
            print(f"\n{i+1}. {record['repo_name']} - {record['core_dev_login']}")
            print(f"   Attrition日期: {record['attrition_dates']}")
            print(f"   Attrition_365_new日期: {record['attrition_365_new_dates']}")
            print(f"   仅Attrition日期: {record['only_attrition_dates']}")
            print(f"   仅Attrition_365_new日期: {record['only_attrition_365_new_dates']}")
            print(f"   Breaks数量: {record['breaks_count']}")
            print("-" * 60)
    
    # 统计摘要
    print(f"\n{'='*80}")
    print("差异记录Breaks情况统计")
    print(f"{'='*80}")
    print(f"有差异且有Breaks的记录数: {len(differences_with_breaks)}")
    print(f"有差异但无Breaks的记录数: {len(differences_without_breaks)}")
    print(f"总差异记录数: {len(differences_with_breaks) + len(differences_without_breaks)}")
    
    if len(differences_with_breaks) + len(differences_without_breaks) > 0:
        breaks_percentage = len(differences_with_breaks) / (len(differences_with_breaks) + len(differences_without_breaks)) * 100
        print(f"有Breaks的差异记录占比: {breaks_percentage:.2f}%")
        print(f"无Breaks的差异记录占比: {100 - breaks_percentage:.2f}%")

if __name__ == "__main__":
    check_differences_breaks()