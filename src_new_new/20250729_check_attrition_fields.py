import pandas as pd
from pymongo import MongoClient
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def check_attrition_fields():
    """检查Attrition相关字段的实际数据"""
    logging.info("开始检查Attrition相关字段...")
    
    # 连接数据库
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    
    # 检查各种Attrition字段的存在情况
    fields_to_check = [
        "Attrition", 
        "Attrition_365", 
        "Attrition_365_new",
        "Attrition_180",
        "Attrition_270", 
        "Attrition_450"
    ]
    
    for field in fields_to_check:
        count = collection.count_documents({field: {"$exists": True, "$ne": None}})
        logging.info(f"{field}: {count} 个文档")
        
        # 获取一个样本文档
        sample = collection.find_one({field: {"$exists": True, "$ne": None}})
        if sample and field in sample:
            logging.info(f"  {field} 示例: {sample[field]}")
    
    # 检查有数据的文档
    logging.info("\n检查有实际数据的文档...")
    
    # 检查Attrition字段
    attrition_sample = collection.find_one({"Attrition": {"$exists": True, "$ne": None, "$ne": {}}})
    if attrition_sample:
        logging.info(f"Attrition示例: {attrition_sample.get('Attrition')}")
    
    # 检查Attrition_365字段
    attrition_365_sample = collection.find_one({"Attrition_365": {"$exists": True, "$ne": None, "$ne": {}}})
    if attrition_365_sample:
        logging.info(f"Attrition_365示例: {attrition_365_sample.get('Attrition_365')}")
    
    # 检查Attrition_365_new字段
    attrition_365_new_sample = collection.find_one({"Attrition_365_new": {"$exists": True, "$ne": None, "$ne": {}}})
    if attrition_365_new_sample:
        logging.info(f"Attrition_365_new示例: {attrition_365_new_sample.get('Attrition_365_new')}")
    
    # 统计有实际数据的文档数
    attrition_with_data = collection.count_documents({
        "Attrition": {"$exists": True, "$ne": None, "$ne": {}}
    })
    attrition_365_with_data = collection.count_documents({
        "Attrition_365": {"$exists": True, "$ne": None, "$ne": {}}
    })
    attrition_365_new_with_data = collection.count_documents({
        "Attrition_365_new": {"$exists": True, "$ne": None, "$ne": {}}
    })
    
    logging.info(f"\n有实际数据的文档数:")
    logging.info(f"  Attrition: {attrition_with_data}")
    logging.info(f"  Attrition_365: {attrition_365_with_data}")
    logging.info(f"  Attrition_365_new: {attrition_365_new_with_data}")

if __name__ == "__main__":
    check_attrition_fields()