import pandas as pd
import numpy as np
from pymongo import MongoClient
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/attrition_detailed_analysis.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def extract_detailed_attrition_data(collection, collection_name):
    """提取详细的attrition数据，包括所有字段"""
    attrition_data = []
    
    for doc in collection.find({}):
        repo_name = doc.get("repo_name", "")
        core_dev_login = doc.get("core_dev_login", "")
        repo_id = doc.get("repo_id", "")
        core_dev_id = doc.get("core_dev_id", "")
        
        # 处理旧的attrition格式
        if "Attrition" in doc and doc["Attrition"]:
            old_attrition = doc["Attrition"]
            if "attrition_date" in old_attrition:
                for date in old_attrition["attrition_date"]:
                    attrition_data.append({
                        "repo_name": repo_name,
                        "repo_id": repo_id,
                        "core_dev_login": core_dev_login,
                        "core_dev_id": core_dev_id,
                        "attrition_date": date,
                        "source": "old_attrition",
                        "attrition_type": "old_format"
                    })
        
        # 处理新的attrition_new格式 - 所有attrition类型
        if "Attrition_new" in doc and doc["Attrition_new"]:
            new_attrition = doc["Attrition_new"]
            
            # 检查所有attrition类型
            for attrition_key, attrition_value in new_attrition.items():
                if attrition_value and "attrition_date" in attrition_value:
                    for date in attrition_value["attrition_date"]:
                        attrition_data.append({
                            "repo_name": repo_name,
                            "repo_id": repo_id,
                            "core_dev_login": core_dev_login,
                            "core_dev_id": core_dev_id,
                            "attrition_date": date,
                            "source": f"new_{attrition_key}",
                            "attrition_type": attrition_key
                        })
    
    df = pd.DataFrame(attrition_data)
    logging.info(f"{collection_name} 详细attrition数据条数: {len(df)}")
    return df

def compare_attrition_by_type(old_collection, new_collection):
    """按类型比较attrition数据"""
    logging.info("开始按类型比较attrition数据...")
    
    # 提取详细数据
    old_attrition_df = extract_detailed_attrition_data(old_collection, "旧集合")
    new_attrition_df = extract_detailed_attrition_data(new_collection, "新集合")
    
    # 保存详细数据
    old_attrition_df.to_csv("../result/20250728_compare_test/old_attrition_detailed.csv", index=False)
    new_attrition_df.to_csv("../result/20250728_compare_test/new_attrition_detailed.csv", index=False)
    
    # 按类型统计
    old_type_counts = old_attrition_df.groupby("source").size()
    new_type_counts = new_attrition_df.groupby("source").size()
    
    logging.info("旧集合attrition类型统计:")
    for source, count in old_type_counts.items():
        logging.info(f"  {source}: {count}")
    
    logging.info("新集合attrition类型统计:")
    for source, count in new_type_counts.items():
        logging.info(f"  {source}: {count}")
    
    return old_attrition_df, new_attrition_df

def analyze_attrition_differences(old_attrition_df, new_attrition_df):
    """分析attrition差异的详细原因"""
    logging.info("开始分析attrition差异的详细原因...")
    
    # 创建唯一标识符用于比较
    old_attrition_df['unique_id'] = old_attrition_df['repo_name'] + '_' + old_attrition_df['core_dev_login'] + '_' + old_attrition_df['attrition_date']
    new_attrition_df['unique_id'] = new_attrition_df['repo_name'] + '_' + new_attrition_df['core_dev_login'] + '_' + new_attrition_df['attrition_date']
    
    # 找出只在旧集合中存在的记录
    old_only = old_attrition_df[~old_attrition_df['unique_id'].isin(new_attrition_df['unique_id'])]
    
    # 找出只在新集合中存在的记录
    new_only = new_attrition_df[~new_attrition_df['unique_id'].isin(old_attrition_df['unique_id'])]
    
    # 找出两个集合都存在的记录
    common = old_attrition_df[old_attrition_df['unique_id'].isin(new_attrition_df['unique_id'])]
    
    logging.info(f"只在旧集合中的attrition记录: {len(old_only)}")
    logging.info(f"只在新集合中的attrition记录: {len(new_only)}")
    logging.info(f"两个集合都存在的attrition记录: {len(common)}")
    
    # 保存差异数据
    old_only.to_csv("../result/20250728_compare_test/old_only_attrition.csv", index=False)
    new_only.to_csv("../result/20250728_compare_test/new_only_attrition.csv", index=False)
    common.to_csv("../result/20250728_compare_test/common_attrition.csv", index=False)
    
    return old_only, new_only, common

def analyze_attrition_by_repo(old_only, new_only, common):
    """按仓库分析attrition差异"""
    logging.info("开始按仓库分析attrition差异...")
    
    # 统计每个仓库的差异
    old_only_by_repo = old_only.groupby('repo_name').size().sort_values(ascending=False)
    new_only_by_repo = new_only.groupby('repo_name').size().sort_values(ascending=False)
    
    # 找出差异最大的仓库
    all_repos = set(old_only_by_repo.index) | set(new_only_by_repo.index)
    
    repo_differences = []
    for repo in all_repos:
        old_count = old_only_by_repo.get(repo, 0)
        new_count = new_only_by_repo.get(repo, 0)
        repo_differences.append({
            'repo_name': repo,
            'old_only_count': old_count,
            'new_only_count': new_count,
            'difference': new_count - old_count
        })
    
    repo_diff_df = pd.DataFrame(repo_differences).sort_values('difference', ascending=False)
    repo_diff_df.to_csv("../result/20250728_compare_test/repo_attrition_differences.csv", index=False)
    
    logging.info("仓库attrition差异统计:")
    logging.info(f"差异最大的前10个仓库:")
    for _, row in repo_diff_df.head(10).iterrows():
        logging.info(f"  {row['repo_name']}: 旧集合独有 {row['old_only_count']}, 新集合独有 {row['new_only_count']}, 差异 {row['difference']}")
    
    return repo_diff_df

def analyze_attrition_by_developer(old_only, new_only, common):
    """按开发者分析attrition差异"""
    logging.info("开始按开发者分析attrition差异...")
    
    # 统计每个开发者的差异
    old_only_by_dev = old_only.groupby('core_dev_login').size().sort_values(ascending=False)
    new_only_by_dev = new_only.groupby('core_dev_login').size().sort_values(ascending=False)
    
    # 找出差异最大的开发者
    all_devs = set(old_only_by_dev.index) | set(new_only_by_dev.index)
    
    dev_differences = []
    for dev in all_devs:
        old_count = old_only_by_dev.get(dev, 0)
        new_count = new_only_by_dev.get(dev, 0)
        dev_differences.append({
            'core_dev_login': dev,
            'old_only_count': old_count,
            'new_only_count': new_count,
            'difference': new_count - old_count
        })
    
    dev_diff_df = pd.DataFrame(dev_differences).sort_values('difference', ascending=False)
    dev_diff_df.to_csv("../result/20250728_compare_test/developer_attrition_differences.csv", index=False)
    
    logging.info("开发者attrition差异统计:")
    logging.info(f"差异最大的前10个开发者:")
    for _, row in dev_diff_df.head(10).iterrows():
        logging.info(f"  {row['core_dev_login']}: 旧集合独有 {row['old_only_count']}, 新集合独有 {row['new_only_count']}, 差异 {row['difference']}")
    
    return dev_diff_df

def analyze_attrition_date_patterns(old_only, new_only, common):
    """分析attrition日期的模式差异"""
    logging.info("开始分析attrition日期模式差异...")
    
    # 转换日期格式
    for df in [old_only, new_only, common]:
        df['attrition_date_parsed'] = pd.to_datetime(df['attrition_date'], errors='coerce')
    
    # 按年份统计
    old_only_by_year = old_only.groupby(old_only['attrition_date_parsed'].dt.year).size()
    new_only_by_year = new_only.groupby(new_only['attrition_date_parsed'].dt.year).size()
    
    logging.info("按年份统计attrition差异:")
    logging.info(f"旧集合独有记录按年份: {dict(old_only_by_year)}")
    logging.info(f"新集合独有记录按年份: {dict(new_only_by_year)}")
    
    # 保存年份统计
    year_stats = pd.DataFrame({
        'year': list(set(old_only_by_year.index) | set(new_only_by_year.index)),
        'old_only_count': [old_only_by_year.get(year, 0) for year in set(old_only_by_year.index) | set(new_only_by_year.index)],
        'new_only_count': [new_only_by_year.get(year, 0) for year in set(old_only_by_year.index) | set(new_only_by_year.index)]
    })
    year_stats['difference'] = year_stats['new_only_count'] - year_stats['old_only_count']
    year_stats = year_stats.sort_values('year')
    year_stats.to_csv("../result/20250728_compare_test/attrition_year_statistics.csv", index=False)
    
    return year_stats

def generate_attrition_analysis_report(old_attrition_df, new_attrition_df, 
                                     old_only, new_only, common,
                                     repo_diff_df, dev_diff_df, year_stats):
    """生成详细的attrition分析报告"""
    
    report = {
        "analysis_timestamp": datetime.now().isoformat(),
        "summary": {
            "old_attrition_total": len(old_attrition_df),
            "new_attrition_total": len(new_attrition_df),
            "old_only_count": len(old_only),
            "new_only_count": len(new_only),
            "common_count": len(common),
            "total_difference": len(new_attrition_df) - len(old_attrition_df)
        },
        "detailed_analysis": {
            "repos_with_largest_differences": repo_diff_df.head(10).to_dict('records'),
            "developers_with_largest_differences": dev_diff_df.head(10).to_dict('records'),
            "year_statistics": year_stats.to_dict('records')
        }
    }
    
    # 保存JSON报告
    with open("../result/20250728_compare_test/attrition_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_report = f"""
# Attrition详细分析报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 总体统计
- 旧集合attrition记录总数: {len(old_attrition_df)}
- 新集合attrition记录总数: {len(new_attrition_df)}
- 总差异: {len(new_attrition_df) - len(old_attrition_df)}

## 详细差异分析
- 只在旧集合中的记录: {len(old_only)}
- 只在新集合中的记录: {len(new_only)}
- 两个集合都存在的记录: {len(common)}

## 差异最大的仓库 (前10个)
{chr(10).join(f"- {row['repo_name']}: 差异 {row['difference']} (旧独有: {row['old_only_count']}, 新独有: {row['new_only_count']})" for _, row in repo_diff_df.head(10).iterrows())}

## 差异最大的开发者 (前10个)
{chr(10).join(f"- {row['core_dev_login']}: 差异 {row['difference']} (旧独有: {row['old_only_count']}, 新独有: {row['new_only_count']})" for _, row in dev_diff_df.head(10).iterrows())}

## 按年份统计
{chr(10).join(f"- {row['year']}: 差异 {row['difference']} (旧独有: {row['old_only_count']}, 新独有: {row['new_only_count']})" for _, row in year_stats.iterrows())}

## 可能的原因分析

### 新集合独有记录的可能原因:
1. **新的attrition计算逻辑**: 新代码使用了不同的attrition计算方式
2. **更多的attrition类型**: 新代码包含了多种attrition阈值 (180d, 270d, 365d, 450d)
3. **改进的数据处理**: 新代码可能处理了更多的边缘情况
4. **更新的数据源**: 使用了更新的commit数据

### 旧集合独有记录的可能原因:
1. **数据过滤差异**: 新代码可能过滤掉了一些旧代码认为有效的记录
2. **计算逻辑变化**: 新的计算方式可能排除了某些边界情况
3. **数据质量改进**: 新代码可能更严格地验证数据质量

### 建议的进一步分析:
1. 检查具体的attrition计算逻辑差异
2. 验证数据源的一致性
3. 分析时间窗口和阈值的设置差异
"""
    
    with open("../result/20250728_compare_test/attrition_analysis_report.md", "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    logging.info("Attrition详细分析报告已生成完成")

def main():
    """主函数"""
    logging.info("开始Attrition详细分析...")
    
    # 连接数据库
    db = connect_mongodb()
    old_collection = db["project_analysis"]
    new_collection = db["project_analysis_new"]
    
    # 1. 按类型比较attrition数据
    old_attrition_df, new_attrition_df = compare_attrition_by_type(old_collection, new_collection)
    
    # 2. 分析attrition差异
    old_only, new_only, common = analyze_attrition_differences(old_attrition_df, new_attrition_df)
    
    # 3. 按仓库分析差异
    repo_diff_df = analyze_attrition_by_repo(old_only, new_only, common)
    
    # 4. 按开发者分析差异
    dev_diff_df = analyze_attrition_by_developer(old_only, new_only, common)
    
    # 5. 分析日期模式差异
    year_stats = analyze_attrition_date_patterns(old_only, new_only, common)
    
    # 6. 生成详细报告
    generate_attrition_analysis_report(old_attrition_df, new_attrition_df,
                                     old_only, new_only, common,
                                     repo_diff_df, dev_diff_df, year_stats)
    
    logging.info("Attrition详细分析完成！")

if __name__ == "__main__":
    main()