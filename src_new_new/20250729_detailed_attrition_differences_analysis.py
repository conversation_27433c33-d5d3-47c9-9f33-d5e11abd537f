import pandas as pd
import numpy as np
from pymongo import MongoClient
import logging
from datetime import datetime
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/detailed_attrition_differences_analysis.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def extract_detailed_differences(db):
    """提取详细的Attrition差异数据"""
    logging.info("开始提取详细的Attrition差异数据...")
    
    collection = db["project_analysis"]
    
    detailed_differences = []
    
    # 获取所有有Attrition或Attrition_365_new字段的文档
    cursor = collection.find({
        "$or": [
            {"Attrition": {"$exists": True, "$ne": None}},
            {"Attrition_365_new": {"$exists": True, "$ne": None}}
        ]
    })
    
    for doc in cursor:
        repo_name = doc.get("repo_name", "")
        core_dev_login = doc.get("core_dev_login", "")
        
        # 提取Attrition数据
        attrition_dates = []
        if "Attrition" in doc and doc["Attrition"]:
            attrition = doc["Attrition"]
            if "attrition_date" in attrition:
                attrition_dates = attrition["attrition_date"]
        
        # 提取Attrition_365_new数据
        attrition_365_new_dates = []
        if "Attrition_365_new" in doc and doc["Attrition_365_new"]:
            attrition_365_new = doc["Attrition_365_new"]
            if "attrition_date" in attrition_365_new:
                attrition_365_new_dates = attrition_365_new["attrition_date"]
        
        # 提取Breaks数据
        breaks_data = []
        if "Breaks" in doc and doc["Breaks"]:
            breaks_data = doc["Breaks"]
        
        # 计算差异
        attrition_set = set(attrition_dates)
        attrition_365_new_set = set(attrition_365_new_dates)
        
        common_dates = attrition_set & attrition_365_new_set
        only_attrition_dates = attrition_set - attrition_365_new_set
        only_attrition_365_new_dates = attrition_365_new_set - attrition_set
        
        # 只记录有差异的数据
        if len(only_attrition_dates) > 0 or len(only_attrition_365_new_dates) > 0:
            detailed_differences.append({
                "repo_name": repo_name,
                "core_dev_login": core_dev_login,
                "attrition_dates": list(attrition_dates),
                "attrition_365_new_dates": list(attrition_365_new_dates),
                "common_dates": list(common_dates),
                "only_attrition_dates": list(only_attrition_dates),
                "only_attrition_365_new_dates": list(only_attrition_365_new_dates),
                "breaks_data": breaks_data,
                "breaks_count": len(breaks_data),
                "attrition_count": len(attrition_dates),
                "attrition_365_new_count": len(attrition_365_new_dates),
                "difference_type": "only_attrition" if len(only_attrition_dates) > 0 and len(only_attrition_365_new_dates) == 0 else
                                 "only_attrition_365_new" if len(only_attrition_dates) == 0 and len(only_attrition_365_new_dates) > 0 else
                                 "both_different"
            })
    
    df = pd.DataFrame(detailed_differences)
    logging.info(f"提取了 {len(df)} 条有差异的记录")
    
    return df

def print_differences_with_breaks(df):
    """打印有差异的记录以及对应的breaks"""
    logging.info("打印有差异的记录以及对应的breaks...")
    
    print(f"\n{'='*100}")
    print("Attrition vs Attrition_365_new 差异记录及对应Breaks")
    print(f"{'='*100}")
    
    # 按差异程度排序（差异日期数量）
    df_sorted = df.copy()
    df_sorted["total_differences"] = df_sorted["only_attrition_dates"].apply(len) + df_sorted["only_attrition_365_new_dates"].apply(len)
    df_sorted = df_sorted.sort_values("total_differences", ascending=False)
    
    for i, (_, row) in enumerate(df_sorted.iterrows()):
        print(f"\n{i+1}. {row['repo_name']} - {row['core_dev_login']}")
        print(f"   差异类型: {row['difference_type']}")
        print(f"   Attrition日期: {row['attrition_dates']}")
        print(f"   Attrition_365_new日期: {row['attrition_365_new_dates']}")
        print(f"   仅Attrition日期: {row['only_attrition_dates']}")
        print(f"   仅Attrition_365_new日期: {row['only_attrition_365_new_dates']}")
        print(f"   Breaks数量: {row['breaks_count']}")
        
        # 显示所有的Breaks
        if row['breaks_count'] > 0:
            print(f"   所有Breaks:")
            for j, break_info in enumerate(row['breaks_data']):
                print(f"     {j+1}. {break_info.get('start_date', '')} 到 {break_info.get('end_date', '')} "
                      f"(持续{break_info.get('duration_units', 0):.1f}单位)")
        else:
            print(f"   没有Breaks记录")
        print("-" * 80)

def save_differences_to_csv(df):
    """保存差异数据到CSV文件"""
    logging.info("保存差异数据到CSV文件...")
    
    # 创建输出目录
    output_dir = "../result/20250729_attrition_differences"
    os.makedirs(output_dir, exist_ok=True)
    
    # 准备CSV数据
    csv_data = []
    for _, row in df.iterrows():
        # 为每个break创建一行记录
        if row['breaks_count'] > 0:
            for break_info in row['breaks_data']:
                csv_data.append({
                    "repo_name": row["repo_name"],
                    "core_dev_login": row["core_dev_login"],
                    "difference_type": row["difference_type"],
                    "attrition_dates": str(row["attrition_dates"]),
                    "attrition_365_new_dates": str(row["attrition_365_new_dates"]),
                    "only_attrition_dates": str(row["only_attrition_dates"]),
                    "only_attrition_365_new_dates": str(row["only_attrition_365_new_dates"]),
                    "break_start_date": break_info.get("start_date", ""),
                    "break_end_date": break_info.get("end_date", ""),
                    "duration_units": break_info.get("duration_units", 0)
                })
        else:
            # 没有breaks的记录
            csv_data.append({
                "repo_name": row["repo_name"],
                "core_dev_login": row["core_dev_login"],
                "difference_type": row["difference_type"],
                "attrition_dates": str(row["attrition_dates"]),
                "attrition_365_new_dates": str(row["attrition_365_new_dates"]),
                "only_attrition_dates": str(row["only_attrition_dates"]),
                "only_attrition_365_new_dates": str(row["only_attrition_365_new_dates"]),
                "break_start_date": "",
                "break_end_date": "",
                "duration_units": 0
            })
    
    csv_df = pd.DataFrame(csv_data)
    csv_df.to_csv(f"{output_dir}/attrition_vs_attrition_365_new_differences_with_breaks.csv", index=False)
    
    # 也保存原始差异数据
    df.to_csv(f"{output_dir}/attrition_vs_attrition_365_new_differences.csv", index=False)
    
    logging.info(f"差异数据已保存到 {output_dir}")

def print_summary_statistics(df):
    """打印统计摘要"""
    print(f"\n{'='*80}")
    print("差异分析统计摘要")
    print(f"{'='*80}")
    print(f"总差异记录数: {len(df)}")
    print(f"涉及仓库数: {df['repo_name'].nunique()}")
    print(f"涉及开发者数: {df['core_dev_login'].nunique()}")
    
    # 差异类型统计
    diff_type_counts = df["difference_type"].value_counts()
    print(f"\n差异类型分布:")
    for diff_type, count in diff_type_counts.items():
        print(f"  {diff_type}: {count}")
    
    # Breaks统计
    total_breaks = df["breaks_count"].sum()
    print(f"\nBreaks统计:")
    print(f"  总Breaks数: {total_breaks}")
    print(f"  有Breaks的记录数: {len(df[df['breaks_count'] > 0])}")
    print(f"  无Breaks的记录数: {len(df[df['breaks_count'] == 0])}")

def main():
    """主函数"""
    logging.info("开始详细分析Attrition vs Attrition_365_new差异...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 1. 提取详细差异数据
    df = extract_detailed_differences(db)
    
    # 2. 打印有差异的记录以及对应的breaks
    print_differences_with_breaks(df)
    
    # 3. 保存差异数据到CSV
    save_differences_to_csv(df)
    
    # 4. 打印统计摘要
    print_summary_statistics(df)
    
    logging.info("详细Attrition差异分析完成！")

if __name__ == "__main__":
    main()