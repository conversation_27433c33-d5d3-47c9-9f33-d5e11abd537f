#!/usr/bin/env python3
"""
快速分析Attrition和Attrition_365字段差异
"""

import pandas as pd
from pymongo import MongoClient
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def quick_attrition_comparison():
    """快速分析Attrition和Attrition_365字段差异"""
    
    # 连接MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    
    print("开始快速分析Attrition和Attrition_365字段差异...")
    
    # 统计字段存在情况
    total_docs = collection.count_documents({})
    attrition_docs = collection.count_documents({"Attrition": {"$exists": True, "$ne": None}})
    attrition_365_docs = collection.count_documents({"Attrition_365": {"$exists": True, "$ne": None}})
    both_docs = collection.count_documents({
        "Attrition": {"$exists": True, "$ne": None},
        "Attrition_365": {"$exists": True, "$ne": None}
    })
    
    print(f"总文档数: {total_docs}")
    print(f"有Attrition字段的文档: {attrition_docs}")
    print(f"有Attrition_365字段的文档: {attrition_365_docs}")
    print(f"两个字段都有的文档: {both_docs}")
    
    # 分析几个具体案例
    print("\n分析具体案例...")
    
    # 找出有Attrition但没有Attrition_365的文档
    only_attrition = collection.find({
        "Attrition": {"$exists": True, "$ne": None},
        "Attrition_365": {"$exists": False}
    }).limit(5)
    
    print("只有Attrition字段的案例:")
    for doc in only_attrition:
        print(f"  {doc['repo_name']} - {doc['core_dev_login']}")
        if "Attrition" in doc and doc["Attrition"]:
            print(f"    Attrition日期: {doc['Attrition'].get('attrition_date', [])}")
    
    # 找出有Attrition_365但没有Attrition的文档
    only_attrition_365 = collection.find({
        "Attrition": {"$exists": False},
        "Attrition_365": {"$exists": True, "$ne": None}
    }).limit(5)
    
    print("\n只有Attrition_365字段的案例:")
    for doc in only_attrition_365:
        print(f"  {doc['repo_name']} - {doc['core_dev_login']}")
        if "Attrition_365" in doc and doc["Attrition_365"]:
            print(f"    Attrition_365日期: {doc['Attrition_365'].get('attrition_date', [])}")
    
    # 找出两个字段都有的文档
    both_fields = collection.find({
        "Attrition": {"$exists": True, "$ne": None},
        "Attrition_365": {"$exists": True, "$ne": None}
    }).limit(5)
    
    print("\n两个字段都有的案例:")
    for doc in both_fields:
        print(f"  {doc['repo_name']} - {doc['core_dev_login']}")
        if "Attrition" in doc and doc["Attrition"]:
            print(f"    Attrition日期: {doc['Attrition'].get('attrition_date', [])}")
        if "Attrition_365" in doc and doc["Attrition_365"]:
            print(f"    Attrition_365日期: {doc['Attrition_365'].get('attrition_date', [])}")
    
    # 分析代码差异
    print("\n代码差异分析:")
    print("1. Attrition字段来自: identify_break_disengagement.py")
    print("2. Attrition_365字段来自: generate_breaks_attritions_repo_level_multi.py")
    print("3. 主要差异:")
    print("   - 字段命名: Attrition vs Attrition_365")
    print("   - 计算逻辑: 可能使用了不同的阈值和算法")
    print("   - 数据处理: 可能使用了不同的时区处理方式")
    print("   - 代码实现: 来自不同的脚本文件")

if __name__ == "__main__":
    quick_attrition_comparison()