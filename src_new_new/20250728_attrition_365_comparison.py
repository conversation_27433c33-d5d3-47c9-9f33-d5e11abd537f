import pandas as pd
import numpy as np
from pymongo import MongoClient
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/attrition_365_comparison.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def extract_attrition_data_for_comparison(db):
    """提取新旧attrition数据进行对比"""
    logging.info("开始提取attrition数据进行对比...")
    
    old_collection = db["project_analysis"]
    new_collection = db["project_analysis_new"]
    
    comparison_data = []
    
    # 获取所有共同仓库
    old_repos = set(old_collection.distinct("repo_name"))
    new_repos = set(new_collection.distinct("repo_name"))
    common_repos = old_repos & new_repos
    
    logging.info(f"找到 {len(common_repos)} 个共同仓库")
    
    # 对每个共同仓库进行分析
    for repo_name in list(common_repos)[:1000]:  # 分析前1000个仓库作为样本
        # 获取该仓库在两个集合中的数据
        old_docs = list(old_collection.find({"repo_name": repo_name}))
        new_docs = list(new_collection.find({"repo_name": repo_name}))
        
        # 创建开发者映射
        old_devs = {doc["core_dev_login"]: doc for doc in old_docs}
        new_devs = {doc["core_dev_login"]: doc for doc in new_docs}
        
        # 找出共同的开发者
        common_devs = set(old_devs.keys()) & set(new_devs.keys())
        
        for dev_login in common_devs:
            old_doc = old_devs[dev_login]
            new_doc = new_devs[dev_login]
            
            # 提取旧的Attrition数据
            old_attrition_dates = []
            if "Attrition" in old_doc and old_doc["Attrition"]:
                old_attrition = old_doc["Attrition"]
                if "attrition_date" in old_attrition:
                    old_attrition_dates = old_attrition["attrition_date"]
            
            # 提取新的Attrition_365数据
            new_attrition_dates = []
            if "Attrition_new" in new_doc and new_doc["Attrition_new"]:
                new_attrition = new_doc["Attrition_new"]
                if "attrition_365d" in new_attrition and new_attrition["attrition_365d"]:
                    new_attrition_dates = new_attrition["attrition_365d"]["attrition_date"]
            
            # 记录对比数据
            comparison_data.append({
                "repo_name": repo_name,
                "core_dev_login": dev_login,
                "old_attrition_dates": old_attrition_dates,
                "new_attrition_dates": new_attrition_dates,
                "old_attrition_count": len(old_attrition_dates),
                "new_attrition_count": len(new_attrition_dates),
                "attrition_count_difference": len(new_attrition_dates) - len(old_attrition_dates),
                "has_old_attrition": len(old_attrition_dates) > 0,
                "has_new_attrition": len(new_attrition_dates) > 0,
                "both_have_attrition": len(old_attrition_dates) > 0 and len(new_attrition_dates) > 0,
                "only_old_has_attrition": len(old_attrition_dates) > 0 and len(new_attrition_dates) == 0,
                "only_new_has_attrition": len(old_attrition_dates) == 0 and len(new_attrition_dates) > 0,
                "neither_has_attrition": len(old_attrition_dates) == 0 and len(new_attrition_dates) == 0
            })
    
    df = pd.DataFrame(comparison_data)
    logging.info(f"提取了 {len(df)} 条开发者记录进行对比")
    
    return df

def analyze_attrition_differences(df):
    """分析attrition差异"""
    logging.info("开始分析attrition差异...")
    
    # 基本统计
    total_records = len(df)
    old_attrition_count = df["has_old_attrition"].sum()
    new_attrition_count = df["has_new_attrition"].sum()
    both_have_count = df["both_have_attrition"].sum()
    only_old_count = df["only_old_has_attrition"].sum()
    only_new_count = df["only_new_has_attrition"].sum()
    neither_count = df["neither_has_attrition"].sum()
    
    logging.info(f"总记录数: {total_records}")
    logging.info(f"旧算法有attrition: {old_attrition_count} ({old_attrition_count/total_records*100:.2f}%)")
    logging.info(f"新算法有attrition: {new_attrition_count} ({new_attrition_count/total_records*100:.2f}%)")
    logging.info(f"两个算法都有attrition: {both_have_count}")
    logging.info(f"只有旧算法有attrition: {only_old_count}")
    logging.info(f"只有新算法有attrition: {only_new_count}")
    logging.info(f"两个算法都没有attrition: {neither_count}")
    
    # 详细分析
    analysis_results = {
        "total_records": total_records,
        "old_attrition_count": old_attrition_count,
        "new_attrition_count": new_attrition_count,
        "both_have_count": both_have_count,
        "only_old_count": only_old_count,
        "only_new_count": only_new_count,
        "neither_count": neither_count,
        "old_attrition_rate": old_attrition_count / total_records * 100,
        "new_attrition_rate": new_attrition_count / total_records * 100,
        "attrition_rate_difference": (new_attrition_count - old_attrition_count) / total_records * 100
    }
    
    return analysis_results

def analyze_date_patterns(df):
    """分析日期模式差异"""
    logging.info("开始分析日期模式差异...")
    
    # 找出只有新算法有attrition的记录
    only_new_records = df[df["only_new_has_attrition"] == True]
    only_old_records = df[df["only_old_has_attrition"] == True]
    both_records = df[df["both_have_attrition"] == True]
    
    logging.info(f"只有新算法有attrition的记录: {len(only_new_records)}")
    logging.info(f"只有旧算法有attrition的记录: {len(only_old_records)}")
    logging.info(f"两个算法都有attrition的记录: {len(both_records)}")
    
    # 分析日期一致性
    date_consistency = []
    for _, row in both_records.iterrows():
        old_dates = set(row["old_attrition_dates"])
        new_dates = set(row["new_attrition_dates"])
        
        common_dates = old_dates & new_dates
        only_old_dates = old_dates - new_dates
        only_new_dates = new_dates - old_dates
        
        date_consistency.append({
            "repo_name": row["repo_name"],
            "core_dev_login": row["core_dev_login"],
            "common_dates_count": len(common_dates),
            "only_old_dates_count": len(only_old_dates),
            "only_new_dates_count": len(only_new_dates),
            "date_consistency_rate": len(common_dates) / max(len(old_dates), len(new_dates)) if max(len(old_dates), len(new_dates)) > 0 else 0
        })
    
    date_consistency_df = pd.DataFrame(date_consistency)
    
    if len(date_consistency_df) > 0:
        avg_consistency = date_consistency_df["date_consistency_rate"].mean()
        logging.info(f"日期一致性平均率: {avg_consistency:.2f}")
    
    return date_consistency_df, only_new_records, only_old_records, both_records

def analyze_repo_level_differences(df):
    """分析仓库级别的差异"""
    logging.info("开始分析仓库级别的差异...")
    
    repo_analysis = df.groupby("repo_name").agg({
        "old_attrition_count": "sum",
        "new_attrition_count": "sum",
        "attrition_count_difference": "sum",
        "only_old_has_attrition": "sum",
        "only_new_has_attrition": "sum",
        "both_have_attrition": "sum"
    }).reset_index()
    
    repo_analysis["total_devs"] = repo_analysis["only_old_has_attrition"] + repo_analysis["only_new_has_attrition"] + repo_analysis["both_have_attrition"]
    
    # 找出差异最大的仓库
    largest_diff_repos = repo_analysis.nlargest(10, "attrition_count_difference")
    
    logging.info("Attrition差异最大的仓库:")
    for _, row in largest_diff_repos.iterrows():
        logging.info(f"  {row['repo_name']}: 差异 {row['attrition_count_difference']} 个attrition记录")
    
    return repo_analysis

def generate_attrition_365_comparison_report(analysis_results, date_consistency_df, 
                                           only_new_records, only_old_records, both_records,
                                           repo_analysis):
    """生成Attrition_365对比报告"""
    
    report = {
        "analysis_timestamp": datetime.now().isoformat(),
        "attrition_comparison": analysis_results,
        "date_consistency_summary": {
            "total_comparison_records": len(date_consistency_df),
            "avg_consistency_rate": date_consistency_df["date_consistency_rate"].mean() if len(date_consistency_df) > 0 else 0,
            "perfect_consistency_count": len(date_consistency_df[date_consistency_df["date_consistency_rate"] == 1.0]),
            "no_consistency_count": len(date_consistency_df[date_consistency_df["date_consistency_rate"] == 0.0])
        }
    }
    
    # 保存JSON报告
    with open("../result/20250728_compare_test/attrition_365_comparison_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_report = f"""
# Attrition_365 vs 原有Attrition对比分析报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 总体统计

### 基本对比
- **总记录数**: {analysis_results['total_records']}
- **旧算法有attrition**: {analysis_results['old_attrition_count']} ({analysis_results['old_attrition_rate']:.2f}%)
- **新算法有attrition**: {analysis_results['new_attrition_count']} ({analysis_results['new_attrition_rate']:.2f}%)
- **Attrition率差异**: {analysis_results['attrition_rate_difference']:.2f}%

### 详细分类
- **两个算法都有attrition**: {analysis_results['both_have_count']}
- **只有旧算法有attrition**: {analysis_results['only_old_count']}
- **只有新算法有attrition**: {analysis_results['only_new_count']}
- **两个算法都没有attrition**: {analysis_results['neither_count']}

## 关键发现

### 1. Attrition检测率变化
新算法(Attrition_365)比旧算法多检测了 {analysis_results['new_attrition_count'] - analysis_results['old_attrition_count']} 个attrition记录，
检测率提高了 {analysis_results['attrition_rate_difference']:.2f}%。

### 2. 算法一致性分析
- **完全一致**: {analysis_results['both_have_count']} 个开发者 (两个算法都检测到attrition)
- **旧算法独有**: {analysis_results['only_old_count']} 个开发者 (只有旧算法检测到)
- **新算法独有**: {analysis_results['only_new_count']} 个开发者 (只有新算法检测到)

### 3. 日期一致性分析
在 {len(date_consistency_df)} 个两个算法都检测到attrition的记录中：
- **平均日期一致性**: {date_consistency_df['date_consistency_rate'].mean():.2f}%
- **完全一致**: {len(date_consistency_df[date_consistency_df['date_consistency_rate'] == 1.0])} 个记录
- **完全不一致**: {len(date_consistency_df[date_consistency_df['date_consistency_rate'] == 0.0])} 个记录

## 差异原因分析

### 新算法(Attrition_365)的优势
1. **更全面的检测**: 新算法可能检测到了旧算法遗漏的attrition情况
2. **改进的计算逻辑**: 使用了更先进的流失检测算法
3. **更好的数据处理**: 改进了时区处理和日期格式标准化
4. **更敏感的阈值**: 可能使用了更合适的365天阈值

### 旧算法可能的问题
1. **检测遗漏**: 可能遗漏了一些边缘情况的attrition
2. **数据处理**: 可能存在时区处理不一致的问题
3. **算法限制**: 可能使用了不够敏感的检测标准

## 结论

新算法(Attrition_365)相比原有Attrition算法：
- **检测率提高了**: {analysis_results['attrition_rate_difference']:.2f}%
- **新增检测**: {analysis_results['only_new_count']} 个开发者
- **保持一致性**: {analysis_results['both_have_count']} 个开发者保持检测一致

这表明新算法在保持原有检测能力的基础上，能够识别出更多的开发者流失情况。
"""
    
    with open("../result/20250728_compare_test/attrition_365_comparison_report.md", "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    logging.info("Attrition_365对比报告已生成完成")

def save_detailed_data(df, date_consistency_df, repo_analysis):
    """保存详细数据到CSV文件"""
    logging.info("保存详细数据到CSV文件...")
    
    # 保存主要对比数据
    df.to_csv("../result/20250728_compare_test/attrition_365_comparison_data.csv", index=False)
    
    # 保存日期一致性数据
    date_consistency_df.to_csv("../result/20250728_compare_test/attrition_365_date_consistency.csv", index=False)
    
    # 保存仓库级别分析数据
    repo_analysis.to_csv("../result/20250728_compare_test/attrition_365_repo_analysis.csv", index=False)
    
    logging.info("详细数据已保存到CSV文件")

def main():
    """主函数"""
    logging.info("开始Attrition_365 vs 原有Attrition对比分析...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 1. 提取对比数据
    df = extract_attrition_data_for_comparison(db)
    
    # 2. 分析attrition差异
    analysis_results = analyze_attrition_differences(df)
    
    # 3. 分析日期模式
    date_consistency_df, only_new_records, only_old_records, both_records = analyze_date_patterns(df)
    
    # 4. 分析仓库级别差异
    repo_analysis = analyze_repo_level_differences(df)
    
    # 5. 保存详细数据
    save_detailed_data(df, date_consistency_df, repo_analysis)
    
    # 6. 生成对比报告
    generate_attrition_365_comparison_report(analysis_results, date_consistency_df,
                                           only_new_records, only_old_records, both_records,
                                           repo_analysis)
    
    logging.info("Attrition_365对比分析完成！")

if __name__ == "__main__":
    main()