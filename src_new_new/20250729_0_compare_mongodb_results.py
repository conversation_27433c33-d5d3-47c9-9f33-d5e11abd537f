import pandas as pd
import numpy as np
from pymongo import MongoClient
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/compare_mongodb_results.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def get_collection_stats(db):
    """获取两个集合的基本统计信息"""
    old_collection = db["project_analysis"]
    new_collection = db["project_analysis_new"]
    
    old_count = old_collection.count_documents({})
    new_count = new_collection.count_documents({})
    
    logging.info(f"旧集合 'project_analysis' 文档数量: {old_count}")
    logging.info(f"新集合 'project_analysis_new' 文档数量: {new_count}")
    logging.info(f"差异: {new_count - old_count} 条")
    
    return old_collection, new_collection, old_count, new_count

def compare_repo_coverage(old_collection, new_collection):
    """比较两个集合中仓库的覆盖情况"""
    old_repos = set(old_collection.distinct("repo_name"))
    new_repos = set(new_collection.distinct("repo_name"))
    
    logging.info(f"旧集合中的仓库数量: {len(old_repos)}")
    logging.info(f"新集合中的仓库数量: {len(new_repos)}")
    
    # 找出新增的仓库
    new_only_repos = new_repos - old_repos
    old_only_repos = old_repos - new_repos
    
    logging.info(f"新集合独有的仓库: {len(new_only_repos)}")
    if new_only_repos:
        logging.info(f"新集合独有的仓库列表: {list(new_only_repos)[:10]}...")  # 只显示前10个
    
    logging.info(f"旧集合独有的仓库: {len(old_only_repos)}")
    if old_only_repos:
        logging.info(f"旧集合独有的仓库列表: {list(old_only_repos)[:10]}...")
    
    return old_repos, new_repos, new_only_repos, old_only_repos

def compare_developer_coverage(old_collection, new_collection):
    """比较两个集合中开发者的覆盖情况"""
    old_devs = set(old_collection.distinct("core_dev_login"))
    new_devs = set(new_collection.distinct("core_dev_login"))
    
    logging.info(f"旧集合中的开发者数量: {len(old_devs)}")
    logging.info(f"新集合中的开发者数量: {len(new_devs)}")
    
    # 找出新增的开发者
    new_only_devs = new_devs - old_devs
    old_only_devs = old_devs - new_devs
    
    logging.info(f"新集合独有的开发者: {len(new_only_devs)}")
    if new_only_devs:
        logging.info(f"新集合独有的开发者列表: {list(new_only_devs)[:10]}...")
    
    logging.info(f"旧集合独有的开发者: {len(old_only_devs)}")
    if old_only_devs:
        logging.info(f"旧集合独有的开发者列表: {list(old_only_devs)[:10]}...")
    
    return old_devs, new_devs, new_only_devs, old_only_devs

def extract_attrition_data(collection, collection_name):
    """从集合中提取attrition数据"""
    attrition_data = []
    
    for doc in collection.find({}):
        repo_name = doc.get("repo_name", "")
        core_dev_login = doc.get("core_dev_login", "")
        
        # 处理旧的attrition格式
        if "Attrition" in doc and doc["Attrition"]:
            old_attrition = doc["Attrition"]
            if "attrition_date" in old_attrition:
                for date in old_attrition["attrition_date"]:
                    attrition_data.append({
                        "repo_name": repo_name,
                        "core_dev_login": core_dev_login,
                        "attrition_date": date,
                        "source": "old_attrition"
                    })
        
        # 处理新的attrition_new格式
        if "Attrition_new" in doc and doc["Attrition_new"]:
            new_attrition = doc["Attrition_new"]
            if "attrition_365d" in new_attrition and new_attrition["attrition_365d"]:
                for date in new_attrition["attrition_365d"]["attrition_date"]:
                    attrition_data.append({
                        "repo_name": repo_name,
                        "core_dev_login": core_dev_login,
                        "attrition_date": date,
                        "source": "new_attrition_365d"
                    })
    
    df = pd.DataFrame(attrition_data)
    logging.info(f"{collection_name} attrition数据条数: {len(df)}")
    return df

def compare_attrition_data(old_collection, new_collection):
    """比较新旧attrition数据"""
    logging.info("开始比较attrition数据...")
    
    # 提取数据
    old_attrition_df = extract_attrition_data(old_collection, "旧集合")
    new_attrition_df = extract_attrition_data(new_collection, "新集合")
    
    # 保存到CSV文件
    old_attrition_df.to_csv("../result/20250728_compare_test/old_attrition_data.csv", index=False)
    new_attrition_df.to_csv("../result/20250728_compare_test/new_attrition_data.csv", index=False)
    
    logging.info("Attrition数据已保存到CSV文件")
    
    # 比较统计信息
    logging.info(f"旧集合attrition记录数: {len(old_attrition_df)}")
    logging.info(f"新集合attrition记录数: {len(new_attrition_df)}")
    
    # 按仓库统计
    old_repo_counts = old_attrition_df.groupby("repo_name").size()
    new_repo_counts = new_attrition_df.groupby("repo_name").size()
    
    logging.info(f"旧集合有attrition的仓库数: {len(old_repo_counts)}")
    logging.info(f"新集合有attrition的仓库数: {len(new_repo_counts)}")
    
    # 找出差异
    old_repos_with_attrition = set(old_attrition_df["repo_name"])
    new_repos_with_attrition = set(new_attrition_df["repo_name"])
    
    new_only_repos_attrition = new_repos_with_attrition - old_repos_with_attrition
    old_only_repos_attrition = old_repos_with_attrition - new_repos_with_attrition
    
    logging.info(f"新集合独有的有attrition仓库: {len(new_only_repos_attrition)}")
    if new_only_repos_attrition:
        logging.info(f"新集合独有的有attrition仓库: {list(new_only_repos_attrition)[:10]}...")
    
    logging.info(f"旧集合独有的有attrition仓库: {len(old_only_repos_attrition)}")
    if old_only_repos_attrition:
        logging.info(f"旧集合独有的有attrition仓库: {list(old_only_repos_attrition)[:10]}...")
    
    return old_attrition_df, new_attrition_df

def analyze_breaks_differences(old_collection, new_collection):
    """分析breaks数据的差异"""
    logging.info("开始分析breaks数据差异...")
    
    old_breaks_count = 0
    new_breaks_count = 0
    
    old_breaks_data = []
    new_breaks_data = []
    
    # 统计旧集合的breaks
    for doc in old_collection.find({}):
        breaks = doc.get("Breaks", [])
        old_breaks_count += len(breaks)
        for brk in breaks:
            old_breaks_data.append({
                "repo_name": doc.get("repo_name", ""),
                "core_dev_login": doc.get("core_dev_login", ""),
                "break_id": brk.get("break_id", ""),
                "start_date": brk.get("start_date", ""),
                "end_date": brk.get("end_date", ""),
                "duration_units": brk.get("duration_units", 0)
            })
    
    # 统计新集合的breaks
    for doc in new_collection.find({}):
        breaks = doc.get("Breaks", [])
        new_breaks_count += len(breaks)
        for brk in breaks:
            new_breaks_data.append({
                "repo_name": doc.get("repo_name", ""),
                "core_dev_login": doc.get("core_dev_login", ""),
                "break_id": brk.get("break_id", ""),
                "start_date": brk.get("start_date", ""),
                "end_date": brk.get("end_date", ""),
                "duration_units": brk.get("duration_units", 0)
            })
    
    logging.info(f"旧集合breaks总数: {old_breaks_count}")
    logging.info(f"新集合breaks总数: {new_breaks_count}")
    logging.info(f"差异: {new_breaks_count - old_breaks_count} 个breaks")
    
    # 保存breaks数据到CSV
    old_breaks_df = pd.DataFrame(old_breaks_data)
    new_breaks_df = pd.DataFrame(new_breaks_data)
    
    old_breaks_df.to_csv("../result/20250728_compare_test/old_breaks_data.csv", index=False)
    new_breaks_df.to_csv("../result/20250728_compare_test/new_breaks_data.csv", index=False)
    
    return old_breaks_df, new_breaks_df

def detailed_comparison_analysis(db, old_collection, new_collection):
    """进行详细的比较分析"""
    logging.info("开始详细比较分析...")
    
    # 创建结果目录
    import os
    os.makedirs("../result/20250728_compare_test", exist_ok=True)
    
    # 1. 基本统计对比
    old_collection, new_collection, old_count, new_count = get_collection_stats(db)
    
    # 2. 仓库覆盖对比
    old_repos, new_repos, new_only_repos, old_only_repos = compare_repo_coverage(old_collection, new_collection)
    
    # 3. 开发者覆盖对比
    old_devs, new_devs, new_only_devs, old_only_devs = compare_developer_coverage(old_collection, new_collection)
    
    # 4. Attrition数据对比
    old_attrition_df, new_attrition_df = compare_attrition_data(old_collection, new_collection)
    
    # 5. Breaks数据对比
    old_breaks_df, new_breaks_df = analyze_breaks_differences(old_collection, new_collection)
    
    # 6. 生成详细报告
    generate_comparison_report(old_count, new_count, 
                             old_repos, new_repos, new_only_repos, old_only_repos,
                             old_devs, new_devs, new_only_devs, old_only_devs,
                             old_attrition_df, new_attrition_df,
                             old_breaks_df, new_breaks_df)

def generate_comparison_report(old_count, new_count, 
                             old_repos, new_repos, new_only_repos, old_only_repos,
                             old_devs, new_devs, new_only_devs, old_only_devs,
                             old_attrition_df, new_attrition_df,
                             old_breaks_df, new_breaks_df):
    """生成详细的比较报告"""
    
    report = {
        "comparison_timestamp": datetime.now().isoformat(),
        "summary": {
            "old_documents_count": old_count,
            "new_documents_count": new_count,
            "document_difference": new_count - old_count,
            "old_repos_count": len(old_repos),
            "new_repos_count": len(new_repos),
            "new_only_repos_count": len(new_only_repos),
            "old_only_repos_count": len(old_only_repos),
            "old_devs_count": len(old_devs),
            "new_devs_count": len(new_devs),
            "new_only_devs_count": len(new_only_devs),
            "old_only_devs_count": len(old_only_devs),
            "old_attrition_count": len(old_attrition_df),
            "new_attrition_count": len(new_attrition_df),
            "old_breaks_count": len(old_breaks_df),
            "new_breaks_count": len(new_breaks_df)
        },
        "details": {
            "new_only_repos": list(new_only_repos),
            "old_only_repos": list(old_only_repos),
            "new_only_devs": list(new_only_devs),
            "old_only_devs": list(old_only_devs)
        }
    }
    
    # 保存报告
    with open("../result/20250728_compare_test/comparison_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_report = f"""
# MongoDB新旧结果比较报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 总体统计
- 旧集合文档数: {old_count}
- 新集合文档数: {new_count}
- 文档差异: {new_count - old_count}

## 仓库覆盖情况
- 旧集合仓库数: {len(old_repos)}
- 新集合仓库数: {len(new_repos)}
- 新集合独有仓库: {len(new_only_repos)}
- 旧集合独有仓库: {len(old_only_repos)}

## 开发者覆盖情况
- 旧集合开发者数: {len(old_devs)}
- 新集合开发者数: {len(new_devs)}
- 新集合独有开发者: {len(new_only_devs)}
- 旧集合独有开发者: {len(old_only_devs)}

## Attrition数据
- 旧集合attrition记录: {len(old_attrition_df)}
- 新集合attrition记录: {len(new_attrition_df)}

## Breaks数据
- 旧集合breaks记录: {len(old_breaks_df)}
- 新集合breaks记录: {len(new_breaks_df)}

## 详细差异分析

### 新集合独有仓库
{chr(10).join(f"- {repo}" for repo in list(new_only_repos)[:20])}
{'...' if len(new_only_repos) > 20 else ''}

### 旧集合独有仓库
{chr(10).join(f"- {repo}" for repo in list(old_only_repos)[:20])}
{'...' if len(old_only_repos) > 20 else ''}

### 新集合独有开发者
{chr(10).join(f"- {dev}" for dev in list(new_only_devs)[:20])}
{'...' if len(new_only_devs) > 20 else ''}

### 旧集合独有开发者
{chr(10).join(f"- {dev}" for dev in list(old_only_devs)[:20])}
{'...' if len(old_only_devs) > 20 else ''}
"""
    
    with open("../result/20250728_compare_test/comparison_report.md", "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    logging.info("比较报告已生成完成")

def main():
    """主函数"""
    logging.info("开始MongoDB新旧结果比较分析...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 进行详细比较分析
    detailed_comparison_analysis(db, db["project_analysis"], db["project_analysis_new"])
    
    logging.info("MongoDB新旧结果比较分析完成！")

if __name__ == "__main__":
    main()