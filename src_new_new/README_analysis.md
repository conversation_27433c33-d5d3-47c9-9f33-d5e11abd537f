# MongoDB新旧结果比较分析工具

## 概述

这个工具集用于比较MongoDB中新旧两个集合的结果差异，特别关注attrition（开发者流失）数据的差异分析。

## 文件说明

### 主要分析脚本

1. **20250728_compare_mongodb_results.py**
   - 基础比较分析
   - 比较两个MongoDB集合的基本统计信息
   - 分析仓库和开发者的覆盖情况
   - 生成基础比较报告

2. **20250728_attrition_detailed_analysis.py**
   - Attrition详细分析
   - 详细分析attrition数据的差异
   - 按仓库、开发者、时间模式进行分析
   - 生成attrition差异的详细报告

3. **20250728_code_logic_analysis.py**
   - 代码逻辑分析
   - 分析新旧代码的处理逻辑差异
   - 研究算法改进和数据质量变化
   - 生成代码逻辑分析报告

4. **20250728_run_all_analysis.py**
   - 主控制脚本
   - 一次性运行所有分析模块
   - 生成综合报告

## 使用方法

### 前置条件

1. 确保MongoDB服务正在运行
2. 确保数据库中有以下集合：
   - `project_analysis` (旧集合)
   - `project_analysis_new` (新集合)
3. 安装必要的Python包：
   ```bash
   pip install pymongo pandas numpy
   ```

### 运行方式

#### 方式1: 运行所有分析（推荐）
```bash
cd src_new_new
python 20250728_run_all_analysis.py
```

#### 方式2: 单独运行各个分析模块
```bash
# 基础比较分析
python 20250728_compare_mongodb_results.py

# Attrition详细分析
python 20250728_attrition_detailed_analysis.py

# 代码逻辑分析
python 20250728_code_logic_analysis.py
```

## 输出结果

### 结果文件位置
所有结果文件保存在 `../result/20250728_compare_test/` 目录下

### 主要输出文件

#### 基础比较结果
- `comparison_report.json` - 基础比较的JSON报告
- `comparison_report.md` - 基础比较的Markdown报告
- `old_attrition_data.csv` - 旧集合attrition数据
- `new_attrition_data.csv` - 新集合attrition数据
- `old_breaks_data.csv` - 旧集合breaks数据
- `new_breaks_data.csv` - 新集合breaks数据

#### Attrition详细分析结果
- `attrition_analysis_report.json` - Attrition分析的JSON报告
- `attrition_analysis_report.md` - Attrition分析的Markdown报告
- `old_attrition_detailed.csv` - 详细的旧attrition数据
- `new_attrition_detailed.csv` - 详细的新attrition数据
- `old_only_attrition.csv` - 只在旧集合中的attrition记录
- `new_only_attrition.csv` - 只在新集合中的attrition记录
- `common_attrition.csv` - 两个集合都存在的attrition记录
- `repo_attrition_differences.csv` - 按仓库的attrition差异
- `developer_attrition_differences.csv` - 按开发者的attrition差异
- `attrition_year_statistics.csv` - 按年份的attrition统计

#### 代码逻辑分析结果
- `code_logic_analysis_report.json` - 代码逻辑分析的JSON报告
- `code_logic_analysis_report.md` - 代码逻辑分析的Markdown报告
- `case_studies_analysis.csv` - 案例研究分析结果

#### 综合报告
- `comprehensive_analysis_report.md` - 综合分析报告

### 日志文件
日志文件保存在 `../logs/` 目录下：
- `compare_mongodb_results.log`
- `attrition_detailed_analysis.log`
- `code_logic_analysis.log`
- `run_all_analysis.log`

## 分析内容

### 1. 基础比较分析
- 文档数量对比
- 仓库覆盖情况对比
- 开发者覆盖情况对比
- Attrition数据对比
- Breaks数据对比

### 2. Attrition详细分析
- 新旧attrition数据的逐条对比
- 按仓库分析attrition差异
- 按开发者分析attrition差异
- 按时间模式分析attrition差异
- 差异原因分析

### 3. 代码逻辑分析
- 数据处理方式差异
- Attrition计算方式差异
- Breaks计算方式差异
- 算法改进分析
- 数据质量分析

## 主要差异原因分析

### 新代码的优势
1. **更准确的暂停检测**: 使用IQR统计方法能更好地识别异常暂停
2. **更全面的流失检测**: 结合多种阈值和暂停期分析
3. **更一致的数据处理**: 统一的时区处理和日期格式
4. **更丰富的数据结构**: 支持多种attrition类型

### 可能的问题
1. **数据量增加**: 新算法可能产生更多的attrition记录
2. **计算复杂度**: 新算法可能需要更多的计算资源
3. **结果解释**: 需要重新理解新的attrition定义

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否运行
   - 检查连接字符串是否正确

2. **集合不存在**
   - 确保数据库中有 `project_analysis` 和 `project_analysis_new` 集合
   - 检查集合名称是否正确

3. **权限问题**
   - 确保有足够的权限访问MongoDB
   - 检查文件写入权限

4. **内存不足**
   - 对于大型数据集，可能需要增加内存
   - 考虑分批处理数据

### 调试方法

1. 查看日志文件了解详细错误信息
2. 检查MongoDB连接状态
3. 验证数据集合的存在和内容
4. 使用较小的数据集进行测试

## 扩展和定制

### 添加新的分析模块
1. 创建新的Python脚本
2. 在 `20250728_run_all_analysis.py` 中添加脚本配置
3. 更新综合报告模板

### 修改分析参数
1. 在相应的脚本中修改参数
2. 重新运行分析
3. 比较结果差异

### 自定义报告格式
1. 修改报告生成函数
2. 添加新的可视化图表
3. 自定义输出格式

## 联系和支持

如有问题或需要进一步分析，请：
1. 查看日志文件获取详细错误信息
2. 检查MongoDB连接和数据状态
3. 联系开发团队获取支持

## 版本信息

- 版本: 1.0.0
- 创建日期: 2025-07-28
- 最后更新: 2025-07-28