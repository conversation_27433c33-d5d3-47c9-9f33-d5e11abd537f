import logging
from pymongo import MongoClient
import pandas as pd
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
from queue import Queue
import time
import argparse

# Configure logging
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"{log_dir}/generate_attrition_multithread.log", mode="w", encoding="utf-8"),
    ],
)

def get_processed_commit_file_repo_name(repo_name):
    """Get processed commit data for a repository"""
    output_path = f"../data/processed_commits/{repo_name.replace('/', '_')}_processed_commits.csv"
    repo_commit = pd.read_csv(output_path)
    if repo_commit.empty:
        raise ValueError("The processed commit file is empty.")
    return repo_commit

def process_single_repo(repo_name, attrition_limit, max_workers=8):
    """
    Process a single repository to generate Attrition_{attrition_limit}_new for all developers
    """
    thread_id = threading.current_thread().ident
    field_name = f"Attrition_{attrition_limit}_new"
    logging.info(f"[Thread-{thread_id}] Processing repository: {repo_name} for {field_name}")
    
    try:
        # Create a new MongoDB connection for this thread
        client = MongoClient("mongodb://localhost:27017/")
        db = client["disengagement"]
        collection = db["project_analysis"]
        
        # Get processed commit data for this repo
        repo_commits = get_processed_commit_file_repo_name(repo_name)
        repo_commits['date'] = pd.to_datetime(repo_commits['date'], format='mixed')
        
        # Get all developers for this repo from MongoDB
        developers = list(collection.find({"repo_name": repo_name}))
        
        updated_count = 0
        
        for dev in developers:
            dev_login = dev.get("core_dev_login")
            if not dev_login:
                continue
            
            logging.debug(f"[Thread-{thread_id}] Processing developer: {dev_login}")
            
            # Filter commits by the developer
            dev_commits = repo_commits[repo_commits['author_login'] == dev_login]
            if dev_commits.empty:
                logging.debug(f"[Thread-{thread_id}] No commits found for developer {dev_login}")
                continue
            
            dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')
            dev_commits = dev_commits.sort_values(by='date')
            
            # Get the last commit date for this developer
            last_commit_date = dev_commits['date'].max()
            project_last_commit_date = repo_commits['date'].max()
            
            # Ensure both dates are timezone-naive
            last_commit_date = last_commit_date.tz_localize(None)
            project_last_commit_date = project_last_commit_date.tz_localize(None)
            
            # Initialize attrition data
            attrition_dates = []
            
            # Check if developer has been inactive for more than attrition_limit days
            if (project_last_commit_date - last_commit_date).days > attrition_limit:
                attrition_dates.append(str(last_commit_date.date()))
                logging.info(f"[Thread-{thread_id}] Developer {dev_login} marked as disengaged since {last_commit_date.date()} (limit: {attrition_limit})")
            
            # Check breaks for attrition
            breaks = dev.get("Breaks", [])
            for break_event in breaks:
                duration_units = break_event.get("duration_units", 0)
                if duration_units >= attrition_limit:
                    attrition_dates.append(str(break_event["start_date"]))
                    logging.info(f"[Thread-{thread_id}] Developer {dev_login} marked as disengaged due to break starting {break_event['start_date']} (limit: {attrition_limit})")
            
            # Create Attrition_{attrition_limit}_new field
            attrition_new = None
            if attrition_dates:
                attrition_new = {
                    "attrition_date": attrition_dates
                }
            
            # Update the document with Attrition_{attrition_limit}_new field
            try:
                collection.update_one(
                    {"_id": dev["_id"]},
                    {"$set": {field_name: attrition_new}}
                )
                updated_count += 1
                logging.debug(f"[Thread-{thread_id}] Updated {field_name} for developer {dev_login}")
            except Exception as e:
                logging.error(f"[Thread-{thread_id}] Error updating {field_name} for developer {dev_login}: {e}")
        
        client.close()
        logging.info(f"[Thread-{thread_id}] Completed processing {repo_name}, updated {updated_count} developers for {field_name}")
        return repo_name, updated_count
        
    except Exception as e:
        logging.error(f"[Thread-{thread_id}] Error processing repository {repo_name}: {e}")
        return repo_name, 0

def generate_attrition_new_multithread(attrition_limits, max_workers=8):
    """
    Generate Attrition_{limit}_new field for each core developer using multiple threads
    
    Args:
        attrition_limits (list): List of attrition limits to process (e.g., [180, 270, 365, 450])
        max_workers (int): Number of worker threads
    """
    # Connect to MongoDB to get repository list
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    
    # Get all unique repos from the collection
    repos = collection.distinct("repo_name")
    client.close()
    
    logging.info(f"Found {len(repos)} repositories to process with {max_workers} threads")
    logging.info(f"Processing attrition limits: {attrition_limits}")
    
    total_updated = 0
    start_time = time.time()
    
    # Process each attrition limit
    for attrition_limit in attrition_limits:
        logging.info(f"\n=== Processing attrition limit: {attrition_limit} ===")
        limit_start_time = time.time()
        limit_total_updated = 0
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks for this limit
            future_to_repo = {executor.submit(process_single_repo, repo_name, attrition_limit): repo_name for repo_name in repos}
            
            # Process completed tasks
            for future in as_completed(future_to_repo):
                repo_name = future_to_repo[future]
                try:
                    repo_name, updated_count = future.result()
                    limit_total_updated += updated_count
                    logging.info(f"Repository {repo_name} completed with {updated_count} updates for limit {attrition_limit}")
                except Exception as e:
                    logging.error(f"Repository {repo_name} generated an exception for limit {attrition_limit}: {e}")
        
        limit_end_time = time.time()
        logging.info(f"Attrition limit {attrition_limit} completed in {limit_end_time - limit_start_time:.2f} seconds")
        logging.info(f"Total developers updated for limit {attrition_limit}: {limit_total_updated}")
        total_updated += limit_total_updated
    
    end_time = time.time()
    logging.info(f"\n=== ALL ATTRITION LIMITS COMPLETED ===")
    logging.info(f"Total processing time: {end_time - start_time:.2f} seconds")
    logging.info(f"Total developers updated across all limits: {total_updated}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate attrition fields for different limits')
    parser.add_argument('--limits', nargs='+', type=int, default=[180, 270, 365, 450],
                       help='Attrition limits to process (default: 180 270 365 450)')
    parser.add_argument('--workers', type=int, default=16,
                       help='Number of worker threads (default: 8)')
    parser.add_argument('--single-limit', type=int, default=None,
                       help='Process only a single limit (overrides --limits)')
    
    args = parser.parse_args()
    
    if args.single_limit:
        attrition_limits = [args.single_limit]
        logging.info(f"Processing single attrition limit: {args.single_limit}")
    else:
        attrition_limits = args.limits
        logging.info(f"Processing multiple attrition limits: {attrition_limits}")
    
    # Generate attrition fields
    generate_attrition_new_multithread(attrition_limits, max_workers=args.workers)

if __name__ == "__main__":
    main()