import pandas as pd
import numpy as np
from pymongo import MongoClient
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/attrition_vs_attrition_365_comparison.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def extract_attrition_comparison_data(db):
    """提取Attrition和Attrition_365的对比数据"""
    logging.info("开始提取Attrition和Attrition_365对比数据...")
    
    collection = db["project_analysis"]
    
    comparison_data = []
    
    # 获取所有有Attrition或Attrition_365字段的文档
    cursor = collection.find({
        "$or": [
            {"Attrition": {"$exists": True, "$ne": None}},
            {"Attrition_365_new": {"$exists": True, "$ne": None}}
        ]
    })
    
    for doc in cursor:
        repo_name = doc.get("repo_name", "")
        core_dev_login = doc.get("core_dev_login", "")
        
        # 提取Attrition数据
        attrition_dates = []
        if "Attrition" in doc and doc["Attrition"]:
            attrition = doc["Attrition"]
            if "attrition_date" in attrition:
                attrition_dates = attrition["attrition_date"]
        
        # 提取Attrition_365数据
        attrition_365_dates = []
        if "Attrition_365_new" in doc and doc["Attrition_365_new"]:
            attrition_365 = doc["Attrition_365_new"]
            if "attrition_date" in attrition_365:
                attrition_365_dates = attrition_365["attrition_date"]
        
        # 记录对比数据
        comparison_data.append({
            "repo_name": repo_name,
            "core_dev_login": core_dev_login,
            "attrition_dates": attrition_dates,
            "attrition_365_dates": attrition_365_dates,
            "attrition_count": len(attrition_dates),
            "attrition_365_count": len(attrition_365_dates),
            "count_difference": len(attrition_365_dates) - len(attrition_dates),
            "has_attrition": len(attrition_dates) > 0,
            "has_attrition_365": len(attrition_365_dates) > 0,
            "both_have_attrition": len(attrition_dates) > 0 and len(attrition_365_dates) > 0,
            "only_attrition": len(attrition_dates) > 0 and len(attrition_365_dates) == 0,
            "only_attrition_365": len(attrition_dates) == 0 and len(attrition_365_dates) > 0,
            "neither_has_attrition": len(attrition_dates) == 0 and len(attrition_365_dates) == 0
        })
    
    df = pd.DataFrame(comparison_data)
    logging.info(f"提取了 {len(df)} 条记录进行对比")
    
    return df

def analyze_field_differences(df):
    """分析Attrition和Attrition_365字段的差异"""
    logging.info("开始分析Attrition和Attrition_365字段差异...")
    
    # 基本统计
    total_records = len(df)
    attrition_count = df["has_attrition"].sum()
    attrition_365_count = df["has_attrition_365"].sum()
    both_have_count = df["both_have_attrition"].sum()
    only_attrition_count = df["only_attrition"].sum()
    only_attrition_365_count = df["only_attrition_365"].sum()
    neither_count = df["neither_has_attrition"].sum()
    
    logging.info(f"总记录数: {total_records}")
    logging.info(f"有Attrition字段: {attrition_count} ({attrition_count/total_records*100:.2f}%)")
    logging.info(f"有Attrition_365字段: {attrition_365_count} ({attrition_365_count/total_records*100:.2f}%)")
    logging.info(f"两个字段都有: {both_have_count}")
    logging.info(f"只有Attrition字段: {only_attrition_count}")
    logging.info(f"只有Attrition_365字段: {only_attrition_365_count}")
    logging.info(f"两个字段都没有: {neither_count}")
    
    # 详细分析
    analysis_results = {
        "total_records": total_records,
        "attrition_count": attrition_count,
        "attrition_365_count": attrition_365_count,
        "both_have_count": both_have_count,
        "only_attrition_count": only_attrition_count,
        "only_attrition_365_count": only_attrition_365_count,
        "neither_count": neither_count,
        "attrition_rate": attrition_count / total_records * 100,
        "attrition_365_rate": attrition_365_count / total_records * 100,
        "rate_difference": (attrition_365_count - attrition_count) / total_records * 100
    }
    
    return analysis_results

def analyze_date_consistency(df):
    """分析日期一致性"""
    logging.info("开始分析日期一致性...")
    
    # 找出两个字段都有的记录
    both_records = df[df["both_have_attrition"] == True]
    
    logging.info(f"两个字段都有的记录: {len(both_records)}")
    
    # 分析日期一致性
    date_consistency = []
    for _, row in both_records.iterrows():
        attrition_dates = set(row["attrition_dates"])
        attrition_365_dates = set(row["attrition_365_dates"])
        
        common_dates = attrition_dates & attrition_365_dates
        only_attrition_dates = attrition_dates - attrition_365_dates
        only_attrition_365_dates = attrition_365_dates - attrition_dates
        
        date_consistency.append({
            "repo_name": row["repo_name"],
            "core_dev_login": row["core_dev_login"],
            "common_dates_count": len(common_dates),
            "only_attrition_dates_count": len(only_attrition_dates),
            "only_attrition_365_dates_count": len(only_attrition_365_dates),
            "date_consistency_rate": len(common_dates) / max(len(attrition_dates), len(attrition_365_dates)) if max(len(attrition_dates), len(attrition_365_dates)) > 0 else 0
        })
    
    date_consistency_df = pd.DataFrame(date_consistency)
    
    if len(date_consistency_df) > 0:
        avg_consistency = date_consistency_df["date_consistency_rate"].mean()
        logging.info(f"日期一致性平均率: {avg_consistency:.2f}")
    
    return date_consistency_df, both_records

def analyze_repo_level_differences(df):
    """分析仓库级别的差异"""
    logging.info("开始分析仓库级别的差异...")
    
    repo_analysis = df.groupby("repo_name").agg({
        "attrition_count": "sum",
        "attrition_365_count": "sum",
        "count_difference": "sum",
        "only_attrition": "sum",
        "only_attrition_365": "sum",
        "both_have_attrition": "sum"
    }).reset_index()
    
    repo_analysis["total_devs"] = repo_analysis["only_attrition"] + repo_analysis["only_attrition_365"] + repo_analysis["both_have_attrition"]
    
    # 找出差异最大的仓库
    largest_diff_repos = repo_analysis.nlargest(10, "count_difference")
    
    logging.info("Attrition差异最大的仓库:")
    for _, row in largest_diff_repos.iterrows():
        logging.info(f"  {row['repo_name']}: 差异 {row['count_difference']} 个attrition记录")
    
    return repo_analysis

def analyze_code_differences():
    """分析代码逻辑差异"""
    logging.info("开始分析代码逻辑差异...")
    
    code_differences = {
        "attrition_calculation": {
            "old_approach": "使用Attrition字段，基于最后提交时间和长暂停期",
            "new_approach": "使用Attrition_365字段，基于365天阈值",
            "key_differences": [
                "字段名称不同: Attrition vs Attrition_365",
                "计算逻辑可能不同",
                "阈值设置可能不同",
                "数据处理方式可能不同"
            ]
        },
        "data_structure": {
            "old_structure": "Attrition: {attrition_date: [dates]}",
            "new_structure": "Attrition_365: {attrition_date: [dates]}",
            "similarity": "数据结构基本相同"
        },
        "processing_logic": {
            "old_logic": "基于identify_break_disengagement.py的逻辑",
            "new_logic": "基于generate_breaks_attritions_repo_level_multi.py的逻辑",
            "potential_differences": [
                "时区处理方式",
                "日期格式标准化",
                "阈值计算方式",
                "边缘情况处理"
            ]
        }
    }
    
    return code_differences

def generate_comparison_report(analysis_results, date_consistency_df, 
                              both_records, repo_analysis, code_differences):
    """生成对比报告"""
    
    report = {
        "analysis_timestamp": datetime.now().isoformat(),
        "field_comparison": analysis_results,
        "date_consistency_summary": {
            "total_comparison_records": len(date_consistency_df),
            "avg_consistency_rate": date_consistency_df["date_consistency_rate"].mean() if len(date_consistency_df) > 0 else 0,
            "perfect_consistency_count": len(date_consistency_df[date_consistency_df["date_consistency_rate"] == 1.0]),
            "no_consistency_count": len(date_consistency_df[date_consistency_df["date_consistency_rate"] == 0.0])
        },
        "code_differences": code_differences
    }
    
    # 保存JSON报告
    with open("../result/20250728_compare_test/attrition_vs_attrition_365_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_report = f"""
# Attrition vs Attrition_365 字段对比分析报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 总体统计

### 基本对比
- **总记录数**: {analysis_results['total_records']}
- **有Attrition字段**: {analysis_results['attrition_count']} ({analysis_results['attrition_rate']:.2f}%)
- **有Attrition_365字段**: {analysis_results['attrition_365_count']} ({analysis_results['attrition_365_rate']:.2f}%)
- **字段存在率差异**: {analysis_results['rate_difference']:.2f}%

### 详细分类
- **两个字段都有**: {analysis_results['both_have_count']}
- **只有Attrition字段**: {analysis_results['only_attrition_count']}
- **只有Attrition_365字段**: {analysis_results['only_attrition_365_count']}
- **两个字段都没有**: {analysis_results['neither_count']}

## 关键发现

### 1. 字段存在率差异
Attrition_365字段比Attrition字段多存在 {analysis_results['attrition_365_count'] - analysis_results['attrition_count']} 个记录，
存在率差异为 {analysis_results['rate_difference']:.2f}%。

### 2. 字段一致性分析
- **完全一致**: {analysis_results['both_have_count']} 个记录 (两个字段都存在)
- **Attrition独有**: {analysis_results['only_attrition_count']} 个记录 (只有Attrition字段存在)
- **Attrition_365独有**: {analysis_results['only_attrition_365_count']} 个记录 (只有Attrition_365字段存在)

### 3. 日期一致性分析
在 {len(date_consistency_df)} 个两个字段都存在的记录中：
- **平均日期一致性**: {date_consistency_df['date_consistency_rate'].mean():.2f}%
- **完全一致**: {len(date_consistency_df[date_consistency_df['date_consistency_rate'] == 1.0])} 个记录
- **完全不一致**: {len(date_consistency_df[date_consistency_df['date_consistency_rate'] == 0.0])} 个记录

## 代码逻辑差异分析

### 1. 计算方式差异
**Attrition字段**: {code_differences['attrition_calculation']['old_approach']}
**Attrition_365字段**: {code_differences['attrition_calculation']['new_approach']}

**关键差异**:
{chr(10).join(f"- {diff}" for diff in code_differences['attrition_calculation']['key_differences'])}

### 2. 数据结构差异
**Attrition结构**: {code_differences['data_structure']['old_structure']}
**Attrition_365结构**: {code_differences['data_structure']['new_structure']}
**相似性**: {code_differences['data_structure']['similarity']}

### 3. 处理逻辑差异
**Attrition处理**: {code_differences['processing_logic']['old_logic']}
**Attrition_365处理**: {code_differences['processing_logic']['new_logic']}

**潜在差异**:
{chr(10).join(f"- {diff}" for diff in code_differences['processing_logic']['potential_differences'])}

## 差异原因分析

### 1. 字段命名差异
- Attrition: 通用字段名
- Attrition_365: 明确指定365天阈值的字段名

### 2. 计算逻辑差异
- 可能使用了不同的阈值计算方法
- 可能使用了不同的数据处理方式
- 可能使用了不同的时区处理方式

### 3. 代码实现差异
- 两个字段可能来自不同的代码文件
- 可能使用了不同的算法实现
- 可能处理了不同的边缘情况

## 结论

Attrition和Attrition_365字段存在显著差异，主要原因包括：

1. **字段命名**: 明确指定了365天阈值
2. **计算逻辑**: 可能使用了不同的算法实现
3. **数据处理**: 可能改进了时区和日期处理
4. **代码来源**: 来自不同的处理脚本

建议进一步分析具体的代码实现差异，以确定哪个字段更准确。
"""
    
    with open("../result/20250728_compare_test/attrition_vs_attrition_365_report.md", "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    logging.info("Attrition vs Attrition_365对比报告已生成完成")

def save_detailed_data(df, date_consistency_df, repo_analysis):
    """保存详细数据到CSV文件"""
    logging.info("保存详细数据到CSV文件...")
    
    # 保存主要对比数据
    df.to_csv("../result/20250728_compare_test/attrition_vs_attrition_365_data.csv", index=False)
    
    # 保存日期一致性数据
    date_consistency_df.to_csv("../result/20250728_compare_test/attrition_vs_attrition_365_date_consistency.csv", index=False)
    
    # 保存仓库级别分析数据
    repo_analysis.to_csv("../result/20250728_compare_test/attrition_vs_attrition_365_repo_analysis.csv", index=False)
    
    logging.info("详细数据已保存到CSV文件")

def main():
    """主函数"""
    logging.info("开始Attrition vs Attrition_365字段对比分析...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 1. 提取对比数据
    df = extract_attrition_comparison_data(db)
    
    # 2. 分析字段差异
    analysis_results = analyze_field_differences(df)
    
    # 3. 分析日期一致性
    date_consistency_df, both_records = analyze_date_consistency(df)
    
    # 4. 分析仓库级别差异
    repo_analysis = analyze_repo_level_differences(df)
    
    # 5. 分析代码逻辑差异
    code_differences = analyze_code_differences()
    
    # 6. 保存详细数据
    save_detailed_data(df, date_consistency_df, repo_analysis)
    
    # 7. 生成对比报告
    generate_comparison_report(analysis_results, date_consistency_df,
                              both_records, repo_analysis, code_differences)
    
    logging.info("Attrition vs Attrition_365字段对比分析完成！")

if __name__ == "__main__":
    main()