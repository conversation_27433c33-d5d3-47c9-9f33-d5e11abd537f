import logging
from pymongo import MongoClient
import pandas as pd
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/compare_attrition_fields.log", mode="w", encoding="utf-8"),
    ],
)

def compare_attrition_fields():
    """
    Compare different Attrition fields in the database
    """
    # Connect to MongoDB
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    collection = db["project_analysis"]
    
    # Get sample documents with different attrition fields
    logging.info("Analyzing attrition fields...")
    
    # Count documents with different attrition fields
    attrition_fields = ['Attrition', 'Attrition_180', 'Attrition_270', 'Attrition_365', 'Attrition_450', 'Attrition_365_new']
    
    field_stats = {}
    for field in attrition_fields:
        count = collection.count_documents({field: {"$ne": None}})
        field_stats[field] = count
        logging.info(f"Documents with {field}: {count}")
    
    # Get sample documents with non-null attrition fields
    samples = {}
    for field in attrition_fields:
        sample = collection.find_one({field: {"$ne": None}})
        if sample:
            samples[field] = {
                "repo_name": sample.get("repo_name"),
                "core_dev_login": sample.get("core_dev_login"),
                field: sample.get(field)
            }
    
    # Print detailed comparison
    logging.info("\n=== DETAILED COMPARISON ===")
    for field, sample in samples.items():
        logging.info(f"\n{field}:")
        logging.info(f"  Repo: {sample['repo_name']}")
        logging.info(f"  Developer: {sample['core_dev_login']}")
        logging.info(f"  Value: {json.dumps(sample[field], indent=2)}")
    
    # Find documents where Attrition_365_new differs from other fields
    logging.info("\n=== FINDING DIFFERENCES ===")
    
    # Get a sample of documents to compare
    sample_docs = list(collection.find({}, {"repo_name": 1, "core_dev_login": 1, "Attrition": 1, "Attrition_365": 1, "Attrition_365_new": 1}).limit(10))
    
    for doc in sample_docs:
        repo_name = doc.get("repo_name")
        dev_login = doc.get("core_dev_login")
        
        attrition = doc.get("Attrition")
        attrition_365 = doc.get("Attrition_365")
        attrition_365_new = doc.get("Attrition_365_new")
        
        logging.info(f"\nRepo: {repo_name}, Developer: {dev_login}")
        logging.info(f"  Attrition: {attrition}")
        logging.info(f"  Attrition_365: {attrition_365}")
        logging.info(f"  Attrition_365_new: {attrition_365_new}")
        
        # Check if there are differences
        if attrition != attrition_365_new:
            logging.info(f"  *** DIFFERENCE DETECTED ***")
    
    client.close()

def analyze_attrition_logic_differences():
    """
    Analyze the differences in attrition logic between the two approaches
    """
    logging.info("\n=== ATTRITION LOGIC ANALYSIS ===")
    
    logging.info("""
    identify_break_disengagement.py logic:
    1. Check if (project_last_commit_date - last_commit_date).days > attrition_limit
       - If true, add last_commit_date to attrition_dates
    2. Check all breaks for duration_units >= attrition_limit
       - If true, add break start_date to attrition_dates
    3. Create Attrition field with attrition_date array
    
    20250626_0_generate_breaks_attritions_repo_level_multi.py logic:
    1. Reads existing Attrition_{limit} fields from project_analysis
    2. Extracts and reorganizes data into new collection
    3. Does NOT generate new attrition data, only processes existing data
    
    Key Differences:
    - identify_break_disengagement.py: Generates attrition data from scratch
    - generate_breaks_attritions_repo_level_multi.py: Only processes existing data
    - The Attrition_365 field appears to be null because it was never populated
    """)

def main():
    compare_attrition_fields()
    analyze_attrition_logic_differences()

if __name__ == "__main__":
    main()