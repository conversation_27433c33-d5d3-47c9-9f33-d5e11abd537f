#!/usr/bin/env python3
"""
Examples of how to use the improved attrition generation script
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and print the description"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print("Command completed successfully!")
        print(f"Output:\n{result.stdout}")
        if result.stderr:
            print(f"Stderr:\n{result.stderr}")
    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}")
        print(f"Error output:\n{e.stderr}")
    except Exception as e:
        print(f"Unexpected error: {e}")

def main():
    """Run various examples of the attrition generation script"""
    
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("Attrition Generation Script Examples")
    print("=" * 50)
    
    # Example 1: Generate all default limits (180, 270, 365, 450)
    cmd1 = "python3 generate_attrition_365_new_multithread.py"
    run_command(cmd1, "Generate attrition fields for all default limits (180, 270, 365, 450)")
    
    # Example 2: Generate only for limit 365
    cmd2 = "python3 generate_attrition_365_new_multithread.py --single-limit 365"
    run_command(cmd2, "Generate attrition fields only for limit 365")
    
    # Example 3: Generate for custom limits
    cmd3 = "python3 generate_attrition_365_new_multithread.py --limits 180 365"
    run_command(cmd3, "Generate attrition fields for custom limits (180, 365)")
    
    # Example 4: Generate with different number of workers
    cmd4 = "python3 generate_attrition_365_new_multithread.py --single-limit 270 --workers 4"
    run_command(cmd4, "Generate attrition fields for limit 270 with 4 worker threads")
    
    # Example 5: Generate for all common limits
    cmd5 = "python3 generate_attrition_365_new_multithread.py --limits 90 180 270 365 450 540"
    run_command(cmd5, "Generate attrition fields for extended limits (90, 180, 270, 365, 450, 540)")

if __name__ == "__main__":
    main()