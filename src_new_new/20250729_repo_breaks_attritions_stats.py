import pandas as pd
from pymongo import MongoClient
import logging
from datetime import datetime
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/repo_breaks_attritions_stats.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def get_collection_stats(db, collection_name):
    """获取指定集合的统计数据"""
    logging.info(f"开始统计 {collection_name} 集合的数据...")
    
    collection = db[collection_name]
    
    # 基本统计
    total_docs = collection.count_documents({})
    
    # 统计breaks和attritions
    total_breaks = 0
    total_attritions = 0
    unique_repos = set()
    unique_devs_in_breaks = set()
    unique_devs_in_attritions = set()
    
    # 统计duration信息
    all_durations = []
    
    cursor = collection.find({})
    for doc in cursor:
        repo_name = doc.get("repo_name", "")
        unique_repos.add(repo_name)
        
        breaks = doc.get("breaks", [])
        attritions = doc.get("attritions", [])
        
        total_breaks += len(breaks)
        total_attritions += len(attritions)
        
        # 统计breaks中的开发者
        for break_info in breaks:
            dev_login = break_info.get("dev_login", "")
            if dev_login:
                unique_devs_in_breaks.add(dev_login)
            
            duration = break_info.get("duration", 0)
            if duration > 0:
                all_durations.append(duration)
        
        # 统计attritions中的开发者
        for attrition in attritions:
            dev_login = attrition.get("dev_login", "")
            if dev_login:
                unique_devs_in_attritions.add(dev_login)
    
    # 计算duration统计
    duration_stats = {}
    if all_durations:
        duration_stats = {
            "avg_duration": sum(all_durations) / len(all_durations),
            "max_duration": max(all_durations),
            "min_duration": min(all_durations),
            "median_duration": sorted(all_durations)[len(all_durations)//2] if all_durations else 0
        }
    
    stats = {
        "collection_name": collection_name,
        "total_documents": total_docs,
        "unique_repos": len(unique_repos),
        "total_breaks": total_breaks,
        "total_attritions": total_attritions,
        "unique_devs_in_breaks": len(unique_devs_in_breaks),
        "unique_devs_in_attritions": len(unique_devs_in_attritions),
        "avg_breaks_per_repo": total_breaks / len(unique_repos) if unique_repos else 0,
        "avg_attritions_per_repo": total_attritions / len(unique_repos) if unique_repos else 0,
        "duration_stats": duration_stats
    }
    
    logging.info(f"  {collection_name}: 统计完成")
    return stats

def print_stats_summary(all_stats):
    """打印统计摘要"""
    print(f"\n{'='*100}")
    print("repo_breaks_attritions_{limit} 集合统计摘要")
    print(f"{'='*100}")
    
    # 创建比较表格
    comparison_data = []
    for stats in all_stats:
        comparison_data.append({
            "Collection": stats["collection_name"],
            "Total Docs": stats["total_documents"],
            "Unique Repos": stats["unique_repos"],
            "Total Breaks": stats["total_breaks"],
            "Total Attritions": stats["total_attritions"],
            "Unique Devs (Breaks)": stats["unique_devs_in_breaks"],
            "Unique Devs (Attritions)": stats["unique_devs_in_attritions"],
            "Avg Breaks/Repo": f"{stats['avg_breaks_per_repo']:.2f}",
            "Avg Attritions/Repo": f"{stats['avg_attritions_per_repo']:.2f}",
            "Avg Duration": f"{stats['duration_stats'].get('avg_duration', 0):.2f}",
            "Max Duration": stats['duration_stats'].get('max_duration', 0),
            "Min Duration": stats['duration_stats'].get('min_duration', 0)
        })
    
    # 打印表格
    df = pd.DataFrame(comparison_data)
    print(df.to_string(index=False))
    
    # 打印详细统计
    print(f"\n{'='*100}")
    print("详细统计信息")
    print(f"{'='*100}")
    
    for stats in all_stats:
        print(f"\n{stats['collection_name']}:")
        print(f"  总文档数: {stats['total_documents']:,}")
        print(f"  唯一仓库数: {stats['unique_repos']:,}")
        print(f"  总Breaks数: {stats['total_breaks']:,}")
        print(f"  总Attritions数: {stats['total_attritions']:,}")
        print(f"  Breaks中唯一开发者数: {stats['unique_devs_in_breaks']:,}")
        print(f"  Attritions中唯一开发者数: {stats['unique_devs_in_attritions']:,}")
        print(f"  平均每个仓库Breaks数: {stats['avg_breaks_per_repo']:.2f}")
        print(f"  平均每个仓库Attritions数: {stats['avg_attritions_per_repo']:.2f}")
        
        if stats['duration_stats']:
            print(f"  Breaks持续时间统计:")
            print(f"    平均持续时间: {stats['duration_stats']['avg_duration']:.2f} 天")
            print(f"    最大持续时间: {stats['duration_stats']['max_duration']} 天")
            print(f"    最小持续时间: {stats['duration_stats']['min_duration']} 天")
            print(f"    中位数持续时间: {stats['duration_stats']['median_duration']} 天")

def save_stats_results(all_stats):
    """保存统计结果"""
    logging.info("保存统计结果...")
    
    # 创建输出目录
    output_dir = "../result/20250729_repo_breaks_attritions_stats"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存详细统计数据
    stats_df = pd.DataFrame(all_stats)
    stats_df.to_csv(f"{output_dir}/repo_breaks_attritions_stats.csv", index=False)
    
    # 保存JSON格式的详细统计
    with open(f"{output_dir}/repo_breaks_attritions_stats.json", "w", encoding="utf-8") as f:
        json.dump(all_stats, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_report = f"""
# repo_breaks_attritions_{limit} 集合统计报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 统计摘要

| 集合名称 | 总文档数 | 唯一仓库数 | 总Breaks数 | 总Attritions数 | 平均Breaks/仓库 | 平均Attritions/仓库 |
|---------|---------|-----------|-----------|---------------|----------------|-------------------|
"""
    
    for stats in all_stats:
        markdown_report += f"| {stats['collection_name']} | {stats['total_documents']:,} | {stats['unique_repos']:,} | {stats['total_breaks']:,} | {stats['total_attritions']:,} | {stats['avg_breaks_per_repo']:.2f} | {stats['avg_attritions_per_repo']:.2f} |\n"
    
    markdown_report += f"""

## 详细统计

"""
    
    for stats in all_stats:
        markdown_report += f"""
### {stats['collection_name']}

- **总文档数**: {stats['total_documents']:,}
- **唯一仓库数**: {stats['unique_repos']:,}
- **总Breaks数**: {stats['total_breaks']:,}
- **总Attritions数**: {stats['total_attritions']:,}
- **Breaks中唯一开发者数**: {stats['unique_devs_in_breaks']:,}
- **Attritions中唯一开发者数**: {stats['unique_devs_in_attritions']:,}
- **平均每个仓库Breaks数**: {stats['avg_breaks_per_repo']:.2f}
- **平均每个仓库Attritions数**: {stats['avg_attritions_per_repo']:.2f}

"""
        
        if stats['duration_stats']:
            markdown_report += f"""
#### Breaks持续时间统计

- **平均持续时间**: {stats['duration_stats']['avg_duration']:.2f} 天
- **最大持续时间**: {stats['duration_stats']['max_duration']} 天
- **最小持续时间**: {stats['duration_stats']['min_duration']} 天
- **中位数持续时间**: {stats['duration_stats']['median_duration']} 天

"""
    
    with open(f"{output_dir}/repo_breaks_attritions_stats_report.md", "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    logging.info(f"统计结果已保存到 {output_dir}")

def main():
    """主函数"""
    logging.info("开始统计repo_breaks_attritions_{limit}集合的数据...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 统计各个limit集合
    limits = [180, 270, 365, 450]
    all_stats = []
    
    for limit in limits:
        collection_name = f"repo_breaks_attritions_{limit}"
        stats = get_collection_stats(db, collection_name)
        all_stats.append(stats)
    
    # 打印统计摘要
    print_stats_summary(all_stats)
    
    # 保存统计结果
    save_stats_results(all_stats)
    
    logging.info("repo_breaks_attritions_{limit}集合统计完成！")

if __name__ == "__main__":
    main()