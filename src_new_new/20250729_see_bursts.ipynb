import pandas as pd


attrition_with_burst_original = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')
attrition_with_burst_original_without_merge = pd.read_csv('../data/attritions_20250227_add_burst.csv')
attrition_with_burst_new = pd.read_csv('../data/attrition_csv/attritions_add_burst_merged_365.csv')
# attrition_with_burst_without_merge  = pd.read_csv('../data/attrition_csv/attrition_burst_core_merged_dev_365.csv')

p_test = pd.read_csv('../result/p_test.csv')









p_test

a = pd.read_csv('../data/attrition_csv/attrition_burst_core_dev_merged_365.csv')
a

attrition_with_burst_original

attrition_with_burst_new

# Compare the difference between attrition_with_burst_new and attrition_with_burst_original
# by repo_name, attrition_date, attrition_developer, burst

# First, ensure the relevant columns exist and are named consistently
# Standardize column names for comparison
def standardize_columns(df):
    df = df.copy()
    # Try to find the correct column names for attrition date and developer
    # Try common variants
    if 'attrition_date' not in df.columns:
        for col in df.columns:
            if 'date' in col and 'attrition' in col:
                df = df.rename(columns={col: 'attrition_date'})
    if 'attrition_developer' not in df.columns:
        # The new file uses 'attrition_develper' (typo), fix it
        if 'attrition_develper' in df.columns:
            df = df.rename(columns={'attrition_develper': 'attrition_developer'})
        else:
            for col in df.columns:
                if 'dev' in col and 'attrition' in col:
                    df = df.rename(columns={col: 'attrition_developer'})
    return df

attrition_with_burst_new_std = standardize_columns(attrition_with_burst_new)
attrition_with_burst_original_std = standardize_columns(attrition_with_burst_original)

# Select only the relevant columns for comparison
compare_cols = ['repo_name', 'attrition_date', 'attrition_developer', 'burst']

# Check which columns are present in both
present_cols = [col for col in compare_cols if col in attrition_with_burst_new_std.columns and col in attrition_with_burst_original_std.columns]

print("Columns used for comparison:", present_cols)

# Subset the dataframes
df_new = attrition_with_burst_new_std[present_cols].drop_duplicates().reset_index(drop=True)
df_org = attrition_with_burst_original_std[present_cols].drop_duplicates().reset_index(drop=True)

# Merge to find differences
merged = df_org.merge(df_new, on=present_cols, how='outer', indicator=True)

only_in_org = merged[merged['_merge'] == 'left_only']
only_in_new = merged[merged['_merge'] == 'right_only']

print(f"Rows only in original (not in new): {only_in_org.shape[0]}")
if only_in_org.shape[0] > 0:
    print("Sample rows only in original:")
    print(only_in_org[present_cols].head())

print(f"Rows only in new (not in original): {only_in_new.shape[0]}")
if only_in_new.shape[0] > 0:
    print("Sample rows only in new:")
    print(only_in_new[present_cols].head())


attrition_with_burst_original_without_merge

attrition_with_burst_without_merge = attrition_with_burst_without_merge[attrition_with_burst_without_merge['gap_less_than_84'] == False]

attrition_with_burst_without_merge

# Compare the differences between attrition_with_burst_original_without_merge and attrition_with_burst_without_merge

# 1. Check shape differences
print("Original without merge shape:", attrition_with_burst_original_without_merge.shape)
print("New without merge shape:", attrition_with_burst_without_merge.shape)

# 2. Check columns differences
print("\nColumns only in original:", set(attrition_with_burst_original_without_merge.columns) - set(attrition_with_burst_without_merge.columns))
print("Columns only in new:", set(attrition_with_burst_without_merge.columns) - set(attrition_with_burst_original_without_merge.columns))

# 3. Check for overlapping columns and compare value distributions for key columns
common_cols = [col for col in attrition_with_burst_original_without_merge.columns if col in attrition_with_burst_without_merge.columns]

# 4. Compare value counts for a key identifier (e.g., repo_name, attrition_id, burst)
for col in ['repo_name', 'burst', 'attrition_id']:
    if col in common_cols:
        print(f"\nValue counts for '{col}' in original:")
        print(attrition_with_burst_original_without_merge[col].value_counts().head())
        print(f"Value counts for '{col}' in new:")
        print(attrition_with_burst_without_merge[col].value_counts().head())

# 5. Find rows in original not in new and vice versa (using a subset of columns as key)
key_cols = [col for col in ['repo_name', 'attrition_id', 'burst'] if col in common_cols]
if key_cols:
    merged = attrition_with_burst_original_without_merge.merge(
        attrition_with_burst_without_merge,
        on=key_cols,
        how='outer',
        indicator=True
    )
    only_in_original = merged[merged['_merge'] == 'left_only']
    only_in_new = merged[merged['_merge'] == 'right_only']
    print(f"\nRows only in original (not in new): {only_in_original.shape[0]}")
    print(f"Rows only in new (not in original): {only_in_new.shape[0]}")
    if only_in_original.shape[0] > 0:
        print("Sample rows only in original:")
        print(only_in_original.head())
    if only_in_new.shape[0] > 0:
        print("Sample rows only in new:")
        print(only_in_new.head())
else:
    print("\nNo common key columns found for row-level comparison.")


def merge_attrition_bursts(df):
  """
  Merge records within the same burst if there are multiple attrition events.
  For each group (same repo_name and burst):
    - If there are multiple records:
        - Set attrition_date as the latest (maximum) time.
        - Compute tenure as the average.
        - Sum commit_percent and commits.
    - Otherwise, keep the record unchanged.
  Returns a new DataFrame with the merged results.
  """
  merged_records = []
  grouped = df.groupby(['repo_name', 'burst'])
  for (repo, burst), group in grouped:
    if len(group) > 1:
      merged = group.iloc[0].copy()  # copy common info
      merged['attrition_date'] = group['attrition_date'].max()
      merged['tenure'] = group['tenure'].mean()
      merged['commit_percent'] = group['commit_percent'].sum()
      merged['commits'] = group['commits'].sum()
      merged_records.append(merged)
    else:
      merged_records.append(group.iloc[0])
  return pd.DataFrame(merged_records)

# Process attritions DataFrame and display the merged series data.
attritions = attrition_with_burst_without_merge.copy()  # if needed
attritions = attritions[attritions['tenure'].notnull()]
attritions['tenure'] = attritions['tenure'].astype(int)
attritions = merge_attrition_bursts(attritions)
attritions

# compare attrition_with_burst_original and attritions
# Ensure both DataFrames have the same columns and comparable keys

# Make a copy to avoid modifying original
attritions_compare = attritions.copy()
attritions_compare = attritions_compare.rename(columns={'dev_login': 'attrition_developer'})

# Also ensure attrition_with_burst_original has the same column name for developer
original_compare = attrition_with_burst_original.copy()
original_compare = original_compare.rename(columns={'dev_login': 'attrition_developer'})

# Ensure columns are in the same order
common_cols = [col for col in attritions_compare.columns if col in original_compare.columns]
attritions_compare = attritions_compare[common_cols]
original_compare = original_compare[common_cols]

# Sort both DataFrames for reliable comparison
attritions_compare_sorted = attritions_compare.sort_values(by=common_cols).reset_index(drop=True)
original_compare_sorted = original_compare.sort_values(by=common_cols).reset_index(drop=True)

# Compare the DataFrames
comparison = attritions_compare_sorted.equals(original_compare_sorted)
print(f"Are the two datasets identical (after renaming and sorting)? {comparison}")

# If not identical, show differences
if not comparison:
    # Find rows in attritions not in original
    merged = attritions_compare_sorted.merge(
        original_compare_sorted,
        how='outer',
        indicator=True
    )
    only_in_attritions = merged[merged['_merge'] == 'left_only']
    only_in_original = merged[merged['_merge'] == 'right_only']
    print(f"Rows only in attritions (after merge/rename): {only_in_attritions.shape[0]}")
    if only_in_attritions.shape[0] > 0:
        print(only_in_attritions.head())
    print(f"Rows only in original: {only_in_original.shape[0]}")
    if only_in_original.shape[0] > 0:
        print(only_in_original.head())


only_in_attritions

only_in_original