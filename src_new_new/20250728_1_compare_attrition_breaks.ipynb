{"cells": [{"cell_type": "code", "execution_count": 4, "id": "8c56670c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total repos compared: 46860\n", "Identical repos: 45645\n", "Different repos: 1215\n", "Repos with differences:\n", " - 13-cf/afetch\n", " - 1461748123/rocketbot\n", " - 2amigos/yii2-file-upload-widget\n", " - 2factorauth/twofactorauth\n", " - 99designs/keyring\n", " - 99x/boilerplatejs\n", " - AUTOMATIC1111/stable-diffusion-webui\n", " - LAION-AI/Open-Assistant\n", " - MirrorNetworking/Telepathy\n", " - MultiQC/MultiQC\n", " - TheOdinProject/curriculum\n", " - abdur-rah<PERSON><PERSON>j/shopyo\n", " - absphreak/readme-jokes\n", " - acloudguru/serverless-plugin-aws-alerts\n", " - acmephp/acmephp\n", " - act-rules/act-rules.github.io\n", " - actions/download-artifact\n", " - actions/typescript-action\n", " - adafruit/adafruit_blinka\n", " - adafruit/adafruit_neopixel\n", " - adafruit/adafruit_tcs34725\n", " - adafruit/ci-arduino\n", " - adam<PERSON>/learnxinyminutes-docs\n", " - adopted-ember-addons/ember-collection\n", " - adroll/batchiepatchie\n", " - ai/convert-layout\n", " - ajaxorg/ace\n", " - a<PERSON><PERSON>/jansson\n", " - akshitagit/cpp\n", " - alandefreitas/matplotplusplus\n", " - aleph-alpha/ts-rs\n", " - alexays/waybar\n", " - alexcrichton/toml-rs\n", " - alex<PERSON><PERSON>/plots\n", " - alfg/opendrinks\n", " - alfresco/alfresco-sdk\n", " - alibaba/dragonwell11\n", " - alibaba/tengine\n", " - allalgorithms/cpp\n", " - allalgorithms/python\n", " - allenai/allenact\n", " - allo-media/text2num\n", " - alstr/todo-to-issue-action\n", " - amazon-archives/aws-big-data-blog\n", " - amazon-archives/aws-serverless-auth-reference-app\n", " - ambujraj/hacktoberfest2018\n", " - amitshekhariitbhu/rxjava2-android-samples\n", " - amondnet/vercel-action\n", " - amueller/word_cloud\n", " - anarios/return-youtube-dislike\n", " - andrewprock/pokerstove\n", " - android-async-http/android-async-http\n", " - angular-redux/ng-redux\n", " - angular-ui/ui-date\n", " - angular/angular.js\n", " - animate1978/mb-lab\n", " - anitab-org/vms\n", " - ansible/ansible\n", " - antonmedv/llama\n", " - antonmedv/walk\n", " - antvis/g6\n", " - antvis/graphin\n", " - antvis/xflow\n", " - anybotics/grid_map\n", " - apache/dubbo-go-hessian2\n", " - apache/dubbo-go-pixiu\n", " - apache/incubator-age\n", " - apache/incubator-dolphinscheduler\n", " - apenella/go-ansible\n", " - apibara/starknet-react\n", " - apple/swift-corelibs-libdispatch\n", " - ar-js-org/studio\n", " - arc53/docsgpt\n", " - ardatan/graphql-tools\n", " - aristocratos/btop\n", " - a<PERSON><PERSON>/rabbot\n", " - arshadkazmi42/first-issues\n", " - <PERSON><PERSON><PERSON>/govalidator\n", " - ascend/pytorch\n", " - ashutosh1919/masterportfolio\n", " - assert-rs/assert_cli\n", " - astaxie/build-web-application-with-golang\n", " - asteroidos/asteroidossync\n", " - asweigart/pyautogui\n", " - async-rs/async-std\n", " - atc-project/atomic-threat-coverage\n", " - atemerev/skynet\n", " - athanorlabs/atomic-swap\n", " - atom/symbols-view\n", " - attardi/wikiextractor\n", " - attickdoor/xivcomboplugin\n", " - audrey<PERSON>roy/cookiecutter-pypackage\n", " - aurbano/robinhood-node\n", " - austintackaberry/ydkjs-exercises\n", " - auth0/rules\n", " - automattic/monk\n", " - autumnai/leaf\n", " - avalanche123/imagine\n", " - avan<PERSON>/adminthemeb<PERSON>le\n", " - avinassh/rockstar\n", " - avli/clojurevscode\n", " - awesomedata/apd-core\n", " - aws-amplify/amplify-cli\n", " - aws-samples/amazon-neptune-samples\n", " - aws/aws-eks-best-practices\n", " - awslabs/aws-config-rules\n", " - awslabs/aws-serverless-data-lake-framework\n", " - awslabs/generative-ai-cdk-constructs\n", " - awslabs/open-data-registry\n", " - ayon-ssp/the-sde-prep\n", " - azat-co/expressworks\n", " - azure-samples/active-directory-b2c-xamarin-native\n", " - azure-samples/media-services-video-indexer\n", " - azure/app-service-linux-docs\n", " - azure/azure-powershell\n", " - azure/azure-resource-manager-schemas\n", " - badchoice/handesk\n", " - baidu/braft\n", " - b<PERSON><PERSON>ian/xdu_cs_learning\n", " - bareinhard/hacktoberfest-mathematics\n", " - basarat/typescript-book\n", " - batoulapps/adhan-js\n", " - b<PERSON><PERSON>/beaker\n", " - bbc/imager.js\n", " - bcit-ci/codeigniter\n", " - bdebon/youtube-thumbnail-tester-chrome-extension\n", " - bear/python-twitter\n", " - beautify-web/js-beautify\n", " - bebraw/pypandoc\n", " - bee-keeper/django-invitations\n", " - beetbox/beets\n", " - beeware/batavia\n", " - beeware/beeware\n", " - beeware/beeware.github.io\n", " - beeware/voc\n", " - bellshade/python\n", " - ben<PERSON><PERSON>/riotsharp\n", " - benoitc/gunicorn\n", " - berdario/pew\n", " - betagouv/api.gouv.fr\n", " - betalgo/openai\n", " - bevac<PERSON>/dragula\n", " - b<PERSON>lee/pygtail\n", " - bgrins/javascript-astar\n", " - bhupesh-v/30-seconds-of-cpp\n", " - bilde2910/hauk\n", " - billymoon/stylus\n", " - binance-chain/go-sdk\n", " - binarymatt/pyres\n", " - bin<PERSON><PERSON><PERSON>hier/ntfy\n", " - bitcoinjs/bech32\n", " - bitexpert/disco\n", " - bitovi/documentjs\n", " - bitwiseshiftleft/sjcl\n", " - bmaltais/kohya_ss\n", " - b<PERSON>ill/pycortexmdebug\n", " - bnb-chain/go-sdk\n", " - boltapp/sleet\n", " - bootandy/dust\n", " - borismus/keysocket\n", " - b<PERSON><PERSON><PERSON>/react-sparklines\n", " - borschik/borschik\n", " - boto/boto\n", " - bottlesdevs/bottles\n", " - brektrou/rtl8821cu\n", " - brian<PERSON><PERSON>/yajl-ruby\n", " - brittanyrw/emojiscreen\n", " - btel/svg_utils\n", " - bundesapi/deutschland\n", " - bvaughn/react-virtualized\n", " - bw2/configargparse\n", " - byteinternet/drf-oidc-auth\n", " - ca98am79/connect-dynamodb\n", " - cakephp/phinx\n", " - canboat/canboat\n", " - caolan/async\n", " - caolan/nodeunit\n", " - captivationsoftware/react-sticky\n", " - cardano-foundation/developer-portal\n", " - carla-simulator/scenario_runner\n", " - casia-iva-lab/fastsam\n", " - cassidoo/todometer\n", " - catboost/catboost\n", " - cazala/synaptic\n", " - ccrisan/motioneye\n", " - cdemoulins/pamixer\n", " - cellprofiler/cellprofiler-analyst\n", " - cellprofiler/python-bioformats\n", " - cfpaorg/minecraft-mod-language-package\n", " - chainapsis/keplr-chain-registry\n", " - chainsafe/dappeteer\n", " - chalmerlowe/intro_to_sprinting\n", " - chancejs/chancejs\n", " - charlesw/tesseract\n", " - chartjs/chart.js\n", " - chinese-poetry/chinese-poetry\n", " - chkr1011/mqttnet\n", " - chrisdev/django-pandas\n", " - cihub/seelog\n", " - circleci-public/circleci-demo-javascript-express\n", " - circus-tent/circus\n", " - ciscodevnet/dne-dna-code\n", " - claws/bh1750\n", " - clenemt/docdash\n", " - click-contrib/click-repl\n", " - clockworkpi/launcher\n", " - cloudevents/sdk-go\n", " - cloudflare/cloudflare-php\n", " - cloudfoundry/gorouter\n", " - clusterm/hakchi2\n", " - coddingtonbear/django-mailbox\n", " - codebrainz/geany-themes\n", " - codejoust/session.js\n", " - codemeta/codemeta\n", " - codethesaurus/codethesaur.us\n", " - codingtrain/website\n", " - codyogden/killedbygoogle\n", " - commenthol/date-holidays\n", " - composer/composer\n", " - concretesolutions/canarinho\n", " - conda-forge/staged-recipes\n", " - condemil/gist\n", " - connectai-e/feishu-openai\n", " - consenlabs/token-profile\n", " - constantcontact/php-sdk\n", " - consumet/consumet.ts\n", " - containers/dnsname\n", " - containers/podman-compose\n", " - coolbho3k/manuf\n", " - cosmin/stashy\n", " - coveooss/json-schema-for-humans\n", " - crackerakiua/ui-cropper\n", " - craigwblake/redline\n", " - crccheck/raphael-svg-import-classic\n", " - creationix/node-gir\n", " - cr<PERSON>berg/json-against-humanity\n", " - crowcpp/crow\n", " - crypt0s/fakedns\n", " - ctf-wiki/ctf-wiki\n", " - cyclonedx/cyclonedx-dotnet\n", " - cysharp/ulid\n", " - czerwonk/junos_exporter\n", " - czerwonk/ping_exporter\n", " - dalpo/rails_admin_nestable\n", " - dan<PERSON>kle/openhtmltopdf\n", " - dani-garcia/bitwarden_rs\n", " - danielgsims/php-collections\n", " - da<PERSON><PERSON><PERSON><PERSON>/seclists\n", " - darkreader/darkreader\n", " - data-dog/go-sqlmock\n", " - dataarts/dat.gui\n", " - datadog/integrations-extras\n", " - datadog/terraform-provider-datadog\n", " - datafaker-net/datafaker\n", " - datalux/osintgram\n", " - dateutil/dateutil\n", " - davedevelopment/phpmig\n", " - da<PERSON><PERSON><PERSON>/jedi-vim\n", " - david<PERSON><PERSON>/randomcolor\n", " - dbcli/pgcli\n", " - ddevault/truecraft\n", " - ddvk/rmfakecloud\n", " - deanhet/react-native-text-ticker\n", " - defillama/chainlist\n", " - definitelytyped/definitelytyped\n", " - denisidoro/navi\n", " - denodrivers/deno_mongo\n", " - denolib/typeorm\n", " - denvercoder1/github-readme-streak-stats\n", " - deta/deta-python\n", " - dexterhuang/cybercodeonline\n", " - dfinity/internet-identity\n", " - dgarijo/widoco\n", " - dhatim/python-license-check\n", " - dhewm/dhewm3\n", " - diegonetto/generator-ionic\n", " - digdes/soapcore\n", " - digininja/dvwa\n", " - digitallogicsimcommunity/digital-logic-sim-ce\n", " - disclose/diodb\n", " - disqus/django-bitfield\n", " - dius/java-faker\n", " - dj3500/hightail\n", " - django-cms/django-filer\n", " - django-compressor/django-compressor\n", " - django-extensions/django-extensions\n", " - django-guardian/django-guardian\n", " - django-notifications/django-notifications\n", " - django-ses/django-ses\n", " - d<PERSON><PERSON><PERSON>/django-admin-honeypot\n", " - dnsjava/dnsjava\n", " - docker-archive/docker-registry\n", " - docker/docker-py\n", " - doctrine-extensions/doctrineextensions\n", " - doctrine/cache\n", " - doctrine/doctrinemodule\n", " - docusign/docusign-esign-csharp-client\n", " - dogmaphobic/mavesp8266\n", " - donnemartin/system-design-primer\n", " - doramart/doracms\n", " - doublespeakgames/adarkroom\n", " - dozoisch/react-google-recaptcha\n", " - dr-prodigy/python-holidays\n", " - dragonchain/dragonchain\n", " - drcoms/drcom-generic\n", " - dropbox/dbxcli\n", " - dropbox/dropbox-sdk-dotnet\n", " - dropnet/dropnet\n", " - dubesar/ultimate-java-resources\n", " - dutchiexl/behathtmlformatterplugin\n", " - dvandal/cryptonote-nodejs-pool\n", " - dwyl/english-words\n", " - dylang/node-rss\n", " - e-/hangul.js\n", " - e-dard/flask-s3\n", " - easycorp/easyadminbundle\n", " - e<PERSON><PERSON><PERSON><PERSON>/linkedin-skill-assessments-quizzes\n", " - eco-stake/restake\n", " - edeng23/binance-trade-bot\n", " - electron/rcedit\n", " - elementary/website\n", " - eleutherai/gpt-neox\n", " - eleutherai/lm-evaluation-harness\n", " - ember-a11y/ember-a11y-testing\n", " - ember-cli/ember-exam\n", " - ember-cli/ember-try\n", " - emberian/evdev\n", " - emery<PERSON>/csrankings\n", " - emily<PERSON>e/tennis-refactoring-kata\n", " - emukidid/cleanrip\n", " - encode/apistar\n", " - encode/django-rest-framework\n", " - enkru/freelook\n", " - ensdomains/ens\n", " - epartment/nova-dependency-container\n", " - ericmjl/pyjanitor\n", " - eriwen/gradle-js-plugin\n", " - es-shims/es5-shim\n", " - esa/pykep\n", " - esanchezm/prometheus-qbittorrent-exporter\n", " - eslint-stylistic/eslint-stylistic\n", " - espruino/bangleapps\n", " - espruino/espruinodocs\n", " - esri/developer-support\n", " - ethereum/ethereum-org-website\n", " - ethereumbook/ethereumbook\n", " - etsy/opsweekly\n", " - ettegit/enragedrabbitproject\n", " - eventsource/eventsource\n", " - evernote/evernote-sdk-js\n", " - evilsocket/pwnagotchi\n", " - exercism/go\n", " - exercism/java\n", " - exercism/python\n", " - expdev07/coronavirus-tracker-api\n", " - expressjs/multer\n", " - eza-community/eza\n", " - facebook/facebook-python-business-sdk\n", " - faker-js/faker\n", " - falcosecurity/charts\n", " - fast-data-transfer/fdt\n", " - fcsonline/drill\n", " - felixge/node-dirty\n", " - fetrarij/ngx-daterangepicker-material\n", " - filestack/filestack-android\n", " - fineanmol/Hacktoberfest2023\n", " - fineanmol/Hacktoberfest2024\n", " - fineanmol/hacktoberfest\n", " - fineanmol/hacktoberfest2022\n", " - firebase/snippets-node\n", " - fireeye/speakeasy\n", " - flairnlp/flair\n", " - flameshot-org/flameshot\n", " - flask-admin/flask-admin\n", " - flatiron/director\n", " - flavio<PERSON><PERSON><PERSON>/trilhainfo\n", " - floydspace/serverless-esbuild\n", " - fluent-ffmpeg/node-fluent-ffmpeg\n", " - fluent/fluent-logger-java\n", " - flybywiresim/aircraft\n", " - flyingsaucerproject/flyingsaucer\n", " - fmfn/bayesianoptimization\n", " - fnproject/cli\n", " - fnproject/docs\n", " - foreversd/forever\n", " - formidablelabs/component-playground\n", " - formidablelabs/webpack-dashboard\n", " - forseti-security/policy-library\n", " - fossar/selfoss\n", " - fossasia/labyrinth\n", " - foundry-vtt-community/macros\n", " - fourplusone/terraform-provider-jira\n", " - freecodecamp/news-translation\n", " - freeopcua/opcua-asyncio\n", " - frictionlessdata/datapackage-py\n", " - friendsofcake/bootstrap-ui\n", " - friendsofsymfony/fosrestbundle\n", " - fullstack-hy2020/fullstack-hy2020.github.io\n", " - f<PERSON><PERSON><PERSON>/faker\n", " - ga4gh/ga4gh-schemas\n", " - gabrielfalcao/lettuce\n", " - gadgetoid/pinout.xyz\n", " - gammu/gammu\n", " - garand/sticky\n", " - garna<PERSON>/kappa\n", " - garris/backstopjs\n", " - gatsbyjs/gatsby-ja\n", " - gauntface/simple-push-demo\n", " - gd-programming/gddocs\n", " - gdisf/teaching-materials\n", " - geany/geany-themes\n", " - geekcomputers/python\n", " - genkgo/ember-localforage-adapter\n", " - geoext/geoext2\n", " - georgringer/news\n", " - getlogbook/logbook\n", " - getpelican/pelican\n", " - getsentry/rb\n", " - giac<PERSON><PERSON>/keylogger\n", " - github-api/github-api\n", " - github/paste-markdown\n", " - github/stale-repos\n", " - gitim/react-native-sortable-list\n", " - gitlabphp/client\n", " - gleam-lang/vscode-gleam\n", " - glistengine/glistengine\n", " - glium/glium\n", " - gnembon/scarpet\n", " - go-chi/chi\n", " - go-jose/go-jose\n", " - go-martini/martini\n", " - goadapp/goad\n", " - gomfunkel/node-mailchimp\n", " - google/big-bench\n", " - google/capirca\n", " - google/comprehensive-rust\n", " - google/draco\n", " - google/ios-webkit-debug-proxy\n", " - google/yapf\n", " - googlechromelabs/browser-fs-access\n", " - googlecloudplatform/cloud-build-notifiers\n", " - googlecloudplatform/dialogflow-integrations\n", " - googlecloudplatform/terraformer\n", " - gorilla/handlers\n", " - graham<PERSON><PERSON>/crypt-server\n", " - graphite-project/graphite-web\n", " - graphite-project/whisper\n", " - grasscutters/grasscutter\n", " - gregg<PERSON><PERSON>/recaptcha\n", " - gruntjs/grunt-contrib-jasmine\n", " - gruntwork-io/terratest\n", " - guake/guake\n", " - guillau<PERSON><PERSON><PERSON>/parsley.js\n", " - gunthercox/chatterbot-corpus\n", " - h2o/neverbleed\n", " - h5bp/html5please\n", " - hackclub/sprig\n", " - hacktoberfest17/programming\n", " - hakimel/reveal.js\n", " - harsha200105/desktopassistant\n", " - hashnode/mern-starter\n", " - hasura/learn-graphql\n", " - hbmartin/graphviz2drawio\n", " - hechoendrupal/drupal-console\n", " - helloworld017/atom-discord\n", " - helm/helm\n", " - hgourvest/node-firebird\n", " - hgraph-os/hgraph\n", " - hgrecco/pint\n", " - hhhrrrttt222111/codechef\n", " - highlightjs/highlight.js\n", " - hipo/university-domains-list\n", " - hmmlearn/hmmlearn\n", " - ho0ber/nk2tray\n", " - home-sweet-gnome/dash-to-panel\n", " - howtographql/howtographql\n", " - hoytech/vmtouch\n", " - htbox/allready\n", " - htbox/crisischeckin\n", " - hub4j/github-api\n", " - huggingface/optimum\n", " - huggingface/transformers\n", " - humanizr/humanizer\n", " - humbertogontijo/homeassistant-roborock\n", " - husobee/vestigo\n", " - huytd/agar.io-clone\n", " - hybridauth/hybridauth\n", " - hydroprotocol/hydro-scaffold-dex\n", " - hyperledger/caliper-benchmarks\n", " - hypeserver/react-date-range\n", " - hyunsik/bytesize\n", " - iam<PERSON><PERSON>human/py-solc-x\n", " - ian<PERSON><PERSON>/bluepy\n", " - icebob/fastest-validator\n", " - iluwatar/java-design-patterns\n", " - image-rs/image\n", " - indentno/phpunit-pretty-print\n", " - indianopensourcefoundation/dynamic-cli\n", " - instafluff/comfyjs\n", " - instagram/monkeytype\n", " - instapy/instapy\n", " - iomad/iomad\n", " - ipkn/crow\n", " - is-a-dev/register\n", " - ishaan28malik/hacktoberfest-2020\n", " - italia/developers.italia.it\n", " - itchio/itch\n", " - iwinston/typeorm-plus\n", " - jabref/abbrv.jabref.org\n", " - jackdclark/five\n", " - jackyzha0/quartz\n", " - jak<PERSON><PERSON><PERSON>/bonobo-git-server\n", " - jargonist/jargon.ist\n", " - jasmine/jasmine\n", " - javascript-obfuscator/webpack-obfuscator\n", " - javiers<PERSON>os/appupdater\n", " - jazzband/django-admin2\n", " - jazzband/django-model-utils\n", " - jazzband/django-silk\n", " - jazzband/django-taggit\n", " - jbeder/yaml-cpp\n", " - jc21/nginx-proxy-manager\n", " - jdm/asknot\n", " - jeffrey<PERSON>/laravel-mix\n", " - jeff<PERSON><PERSON>/bitbucket-issue-migration\n", " - jenkinsci/hashicorp-vault-plugin\n", " - jen<PERSON><PERSON>/kubernetes-cd-plugin\n", " - jens<PERSON>gers/laravel-mongodb\n", " - jero<PERSON><PERSON>n/laravel-adminlte\n", " - jessecar96/steambot\n", " - jfilter/react-native-onboarding-swiper\n", " - j<PERSON><PERSON>/django-kronos\n", " - jh0ker/mau_mau_bot\n", " - jitsi/ice4j\n", " - jk-gan/redux-flipper\n", " - jmreidy/grunt-browserify\n", " - jmxtrans/jmxtrans\n", " - jnimmo/hass-dmx\n", " - j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/datetimeextensions\n", " - jobbyphp/jobby\n", " - joelshepherd/tabliss\n", " - johnroper100/dropplets\n", " - johnsensible/django-sendfile\n", " - joke2k/faker\n", " - jon<PERSON><PERSON>/fanto<PERSON>ni\n", " - joomla/coding-standards\n", " - jorenvh/laravel-share\n", " - jorgebastida/awslogs\n", " - j<PERSON><PERSON><PERSON><PERSON>/ursa\n", " - jreese/znc-push\n", " - jserv/facebooc\n", " - json-c/json-c\n", " - jsonata-js/jsonata\n", " - json<PERSON><PERSON>/json-schema\n", " - jsonwebtoken/jsonwebtoken.github.io\n", " - junit-team/junit4\n", " - justin<PERSON><PERSON>/json-schema\n", " - j<PERSON><PERSON><PERSON>/geddit\n", " - k8sgpt-ai/k8sgpt-operator\n", " - kach/nearley\n", " - katalon-studio/katalon-recorder\n", " - kennytm/qrcode-rust\n", " - keraf/nocoin\n", " - keras-team/keras\n", " - kewlfft/ansible-aur\n", " - kgretzky/evilginx2\n", " - kkga/spacegray\n", " - klen/pylama\n", " - knative/docs\n", " - knex/knex\n", " - knplabs/knptimebundle\n", " - knplabs/php-github-api\n", " - knplabs/snappy\n", " - knuckleswtf/scribe\n", " - koajs/koala\n", " - koderlabs/ngx-device-detector\n", " - kongchen/swagger-maven-plugin\n", " - kosma/minmea\n", " - koying/spmc\n", " - krahets/hello-algo\n", " - kriasoft/node-sqlite\n", " - kt<PERSON>yer/jenkins-job-wrecker\n", " - kubernetes-csi/csi-test\n", " - kubernetes-sigs/external-dns\n", " - kubernetes-sigs/kustomize\n", " - kubernetes/enhancements\n", " - kubernetes/ingress-nginx\n", " - kubernetes/kube-state-metrics\n", " - kubicorn/kubicorn\n", " - kunjgit/GameZone\n", " - kvesteri/validators\n", " - kvesteri/wtforms-json\n", " - kyleamathews/react-headroom\n", " - laminas/laminas-stdlib\n", " - lance<PERSON><PERSON>on/bowser\n", " - langchain4j/langchain4j\n", " - langgenius/dify-docs\n", " - laravel-backpack/settings\n", " - laravel-notification-channels/webpush\n", " - laravel/framework\n", " - laravel/telescope\n", " - larien/aprenda-go-com-testes\n", " - lark-parser/lark\n", " - lcm-proj/lcm\n", " - ldapjs/node-ldapjs\n", " - leaflet/leaflet\n", " - leaflet/leaflet.markercluster\n", " - leaverou/bliss\n", " - le<PERSON><PERSON><PERSON>/python-javabridge\n", " - leethomason/tinyxml2\n", " - lfittl/libpg_query\n", " - linebender/glazier\n", " - linthesia/linthesia\n", " - linuxmint/cinnamon-spices-desklets\n", " - linuxserver/heimdall-apps\n", " - llazzaro/django-scheduler\n", " - lobaro/freertos-rust\n", " - logflare/pino-logflare\n", " - logicchains/lpathbench\n", " - longbill/jquery-date-range-picker\n", " - lookfirst/mui-rff\n", " - loomnetwork/cryptozombie-lessons\n", " - lord<PERSON>uve/pgzero\n", " - lost-stats/lost-stats.github.io\n", " - louislam/uptime-kuma\n", " - lozzd/nagdash\n", " - lra/mackup\n", " - luanfonce<PERSON>/speakerfight\n", " - lukeleppan/better-word-count\n", " - lukew3/mathgenerator\n", " - lunarvim/lunarvim.org\n", " - lunetics-org/localebundle\n", " - lxlxw/996.tsc\n", " - lxmls/lxmls-toolkit\n", " - m-bain/whisperx\n", " - m5stack/m5stack\n", " - maescool/catacomb-snatch\n", " - magento/inventory\n", " - magicjinn/mrbeastify-youtube\n", " - mailgun/flanker\n", " - mailruchamps/miniaicups\n", " - make-all/tuya-local\n", " - makecontributions/markdown-dungeon\n", " - mampfes/hacs_waste_collection_schedule\n", " - man-group/notebooker\n", " - managarm/lai\n", " - mantisbt-plugins/source-integration\n", " - maoschanz/dynamic-wallpaper-editor\n", " - mapbox/mapbox-sdk-js\n", " - mapbox/mapbox-tile-copy\n", " - marc<PERSON><PERSON><PERSON>/django-rest-swagger\n", " - marcos<PERSON>eira<PERSON>j/python-keycloak\n", " - marioiz<PERSON>erdo/jquery.serializejson\n", " - marketsquare/robotframework-requests\n", " - marocchino/sticky-pull-request-comment\n", " - marshmallow-code/django-rest-marshmallow\n", " - maschmann/php-ansible\n", " - mashery/iodocs\n", " - mashpie/i18n-node\n", " - material-table-core/core\n", " - maticzav/graphql-shield\n", " - matklad/once_cell\n", " - matryer/xbar\n", " - matschik/component-party\n", " - matschik/component-party.dev\n", " - matts<PERSON><PERSON>er/torch\n", " - mavam/libbf\n", " - maxim<PERSON><PERSON><PERSON>/aiohttp-apispec\n", " - maxs15/react-native-modalbox\n", " - mediaelement/mediaelement\n", " - meilisearch/charabia\n", " - meilisearch/meilisearch-java\n", " - meilisearch/meilisearch-php\n", " - mermaid-js/mermaid\n", " - metamask/eth-block-tracker\n", " - metaplex-foundation/metaplex\n", " - mholt/papaparse\n", " - michelf/php-markdown\n", " - microcharts-dotnet/microcharts\n", " - microsoft/artifacts-credprovider\n", " - microsoft/azure-pipelines-tasks\n", " - microsoft/cntk\n", " - microsoft/monaco-editor\n", " - microsoft/nnfusion\n", " - microsoft/rust-for-dotnet-devs\n", " - microsoft/studentsatbuild\n", " - microsoft/typescript-website\n", " - microsoft/vscode-extension-samples\n", " - microsoft/vscode-generator-code\n", " - microsoft/vscode-remote-try-go\n", " - microsoft/winfile\n", " - microsoft/wslg\n", " - microsoftdocs/mixed-reality\n", " - microsoftdocs/winrt-api\n", " - mi<PERSON><PERSON><PERSON><PERSON>/rfid\n", " - miguelcobain/ember-paper\n", " - mikekelly/hal-browser\n", " - milvus-io/bootcamp\n", " - milvus-io/milvus\n", " - mindspore-lab/minddiffusion\n", " - mipengine/mip-extensions\n", " - miracle2k/onkyo-eiscp\n", " - mitsu<PERSON>/redis-rs\n", " - mixpanel/mixpanel-node\n", " - mkleehammer/pyodbc\n", " - m<PERSON><PERSON><PERSON>/slickgrid\n", " - mobxjs/serializr\n", " - mobz/elasticsearch-head\n", " - modelscope/modelscope\n", " - modernizr/modernizr\n", " - mofarrell/p2pvc\n", " - mogol/flutter_secure_storage\n", " - molgenis/molgenis\n", " - mollie/mollie-api-php\n", " - moment/moment\n", " - mongo-express/mongo-express\n", " - mongodb-php/laravel-mongodb\n", " - mongodb/laravel-mongodb\n", " - monstra-cms/monstra\n", " - more-itertools/more-itertools\n", " - mousazeidbaker/aws-lambda-typing\n", " - mozilla-actions/sccache-action\n", " - mozilla/vtt.js\n", " - mpociot/laravel-apidoc-generator\n", " - mre/timelapse\n", " - mrichar1/clipster\n", " - mrmc/mrmc\n", " - mrtazz/checkmake\n", " - msoulier/tftpy\n", " - muan/emojilib\n", " - munin-monitoring/munin-node-win32\n", " - mycatapache/mycat-server\n", " - myetherwallet/etherwallet\n", " - nama<PERSON><PERSON><PERSON>/splitwise\n", " - napalm-automation/napalm-ansible\n", " - nas5w/typeofnan-javascript-quizzes\n", " - nate-parrott/flashlight\n", " - nats-io/nats.node\n", " - necrobot-private/necrobot\n", " - nefelim4ag/ananicy\n", " - nestjs/graphql\n", " - nestjs/nest\n", " - netbox-community/devicetype-library\n", " - netflix-skunkworks/sketchy\n", " - netflix/lemur\n", " - netflix/simianarmy\n", " - netguru/sticky-parallax-header\n", " - netlify/netlify-cms\n", " - networktocode/diffsync\n", " - nextjs-ja-translation/nextjs-ja-translation-docs\n", " - ngoduykhanh/powerdns-admin\n", " - ninja-build/ninja\n", " - no9/harmon\n", " - nock/nock\n", " - nodejitsu/docs\n", " - nodeschool/nodeschool.github.io\n", " - nodonisko/ionic-cache\n", " - noriste/cypress-wait-until\n", " - novoda/android-demos\n", " - nrf24/rf24mesh\n", " - nsqio/pynsq\n", " - nushell/nushell.github.io\n", " - nuxt-community/nuxt-property-decorator\n", " - nvidia/cutlass\n", " - nwidart/laravel-modules\n", " - nxpmicro/mfgtools\n", " - o365/python-o365\n", " - oauthlib/oauthlib\n", " - oca/connector-telephony\n", " - oca/server-tools\n", " - oca/timesheet\n", " - oca/web\n", " - ochinchina/supervisord\n", " - ocpsoft/prettytime\n", " - octopusdeploy/library\n", " - ogham/exa\n", " - ok100/lyvi\n", " - okfn-brasil/querido-diario\n", " - oleganza/corebitcoin\n", " - <PERSON><PERSON><PERSON><PERSON><PERSON>/tablewriter\n", " - oliver-moran/jimp\n", " - olivierkes/manuskript\n", " - omgnetwork/plasma-mvp\n", " - ondryaso/pi-rc522\n", " - onedr0p/exportarr\n", " - onelogin/python-saml\n", " - open-telemetry/opentelemetry-python-contrib\n", " - openai/gym-http-api\n", " - openapitools/openapi-generator\n", " - opencfp/opencfp\n", " - opencircuits/opencircuits\n", " - openfoodfacts/openfoodfacts-nodejs\n", " - opengenus/cosmos\n", " - openhab/openhab1-addons\n", " - openpracticelibrary/openpracticelibrary\n", " - openseadragon/openseadragon\n", " - opentok/opentok-node\n", " - openupm/openupm\n", " - openzim/gutenberg\n", " - openzipkin/zipkin-go\n", " - operationcode/operationcode_frontend\n", " - opsgenie/kubernetes-event-exporter\n", " - optimalbits/bull\n", " - orangehrm/orangehrm\n", " - orhane<PERSON>y/open-ai\n", " - orlies<PERSON>/nodemailer-mailgun-transport\n", " - osmlab/osm-community-index\n", " - owasp/cheatsheetseries\n", " - pacollins/hugo-future-imperfect-slim\n", " - paddlepaddle/models\n", " - paddlepaddle/research\n", " - palantir/palantir-java-format\n", " - palisadoesfoundation/talawa-api\n", " - pallets-eco/wtforms-sqlalchemy\n", " - pallets/jinja\n", " - parallax/jspdf\n", " - parse-community/parseui-android\n", " - parth<PERSON><PERSON><PERSON>/tkinter-designer\n", " - patrickjs/ng6-starter\n", " - patw0929/react-intl-tel-input\n", " - paypal/paypal-php-sdk\n", " - p<PERSON><PERSON><PERSON>/request-ip\n", " - pentaho/pentaho-reporting\n", " - pexpect/ptyprocess\n", " - pganalyze/libpg_query\n", " - phageparser/phageparser\n", " - philippj/steamworkspy\n", " - php-gettext/gettext\n", " - php-imagine/imagine\n", " - php-mime-mail-parser/php-mime-mail-parser\n", " - phpmyadmin/phpmyadmin\n", " - phpro/grumphp\n", " - piitaya/lovelace-mushroom\n", " - placeatlas/atlas\n", " - playgameservices/android-basic-samples\n", " - plentz/jquery-maskmoney\n", " - plouc/go-gitlab-client\n", " - pmndrs/react-three-flex\n", " - pmndrs/three-stdlib\n", " - pnp/sp-starter-kit\n", " - pokemongof/pokemongo-bot\n", " - polarismesh/polaris-go\n", " - poodarchu/det3d\n", " - posborne/cmsis-svd\n", " - postcss/postcss-url\n", " - powerdns/pdns-ansible\n", " - powmedia/backbone-forms\n", " - pqrs-org/KE-complex_modifications\n", " - prabhupant/python-ds\n", " - processing/p5.js\n", " - project-chip/rs-matter\n", " - prometheus/client_golang\n", " - provectus/swiss-army-kube\n", " - psf/requests\n", " - puran<PERSON><PERSON><PERSON>/md-date-time-picker\n", " - py-bson/bson\n", " - py-moneyed/py-moneyed\n", " - pyapi-gitlab/pyapi-gitlab\n", " - pydanny/cached-property\n", " - pyjanitor-devs/pyjanitor\n", " - pyjokes/pyjokes\n", " - pytest-dev/pytest-flask\n", " - python-beaver/python-beaver\n", " - python-diamond/diamond\n", " - python-geeks/automation-scripts\n", " - python-semver/python-semver\n", " - python-social-auth/social-core\n", " - python-visualization/folium\n", " - python-xlib/python-xlib\n", " - qiskit/qiskit-finance\n", " - qmk/qmk_firmware\n", " - qossmic/deprecation-detector\n", " - quantstack/ipycytoscape\n", " - quarnster/boxeebox-xbmc\n", " - quartz-scheduler/quartz\n", " - rabbitmq/tls-gen\n", " - rabrennie/anything.js\n", " - radis/radis\n", " - ralim/ironos\n", " - ranbuch/accessibility\n", " - rapid7/awsaml\n", " - rare-technologies/sqlitedict\n", " - rasplex/openpht\n", " - razorrun/react-native-vlc-media-player\n", " - react-bootstrap/react-bootstrap\n", " - react-component/cascader\n", " - react-component/switch\n", " - react-native-community/hooks\n", " - react-native-elements/react-native-elements\n", " - reactive-extensions/rxjs\n", " - reactiveui/refit\n", " - reactjs/es.reactjs.org\n", " - redis-rs/redis-rs\n", " - redis/hiredis\n", " - redux-saga/redux-saga\n", " - reduxjs/redux\n", " - reeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee/reeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee\n", " - release-drafter/release-drafter\n", " - <PERSON><PERSON>/laravel\n", " - remoteintech/remote-jobs\n", " - remotetechnologiesgroup/remotetech\n", " - remy/nodemon\n", " - request/request\n", " - requests/toolbelt\n", " - restify/node-restify\n", " - restorando/angular-pickadate\n", " - rhas<PERSON><PERSON>/gruut\n", " - rhempel/umm_malloc\n", " - richgel999/miniz\n", " - riemann/riemann-dash\n", " - rmm5t/jquery-timeago\n", " - rmmh/skybot\n", " - rneatherway/gh-slack\n", " - roave/dont\n", " - robinhood-unofficial/pyrh\n", " - robotwe<PERSON><PERSON>s/roslibjs\n", " - rochacbruno/dynaconf\n", " - rocketmap/rocketmap\n", " - rolling-scopes-school/tasks\n", " - romkavt/yandex-money-sdk-php\n", " - rp-rs/rp-hal\n", " - rspeer/ordered-set\n", " - rstudio/revealjs\n", " - rucaibox/crslab\n", " - ruflin/elastica\n", " - ruilisi/react-chessground\n", " - rust-itertools/itertools\n", " - rust-lang-ja/book-ja\n", " - rust-lang-nursery/glob\n", " - rust-lang/blog.rust-lang.org\n", " - rust-lang/cargo\n", " - rust-lang/glob\n", " - rust-lang/hashbrown\n", " - rust-lang/highfive\n", " - rust-lang/libc\n", " - rust-lang/libm\n", " - rust-lang/rust-clippy\n", " - rust-lang/rust-playpen\n", " - rust-lang/rustlings\n", " - rust-native-ui/libui-rs\n", " - rust-ndarray/ndarray-stats\n", " - rust-sdl2/rust-sdl2\n", " - rustaudio/cpal\n", " - rustdesk/rustdesk-server\n", " - rustwasm/wasm-bindgen\n", " - rustwasm/wasm-pack\n", " - ryanluker/vscode-coverage-gutters\n", " - sadanandpai/frontend-mini-challenges\n", " - saint<PERSON><PERSON><PERSON>/restify-mongoose\n", " - salesforce-marketingcloud/fuelsdk-php\n", " - sampsyo/bril\n", " - sampsyo/wideq\n", " - samsung/tizen-csharp-samples\n", " - sanic-org/sanic\n", " - santiq/bulletproof-nodejs\n", " - sap/ui5-uiveri5\n", " - sarriaroman/fabricplugin\n", " - schapman1974/tinymongo\n", " - schrodinger/fixed-data-table-2\n", " - scikit-learn/scikit-learn\n", " - sciunto-org/python-bibtexparser\n", " - s<PERSON><PERSON>/django-dbsettings\n", " - scrapy/scrapy\n", " - screepers/typed-screeps\n", " - scverse/squidpy\n", " - sddm/sddm\n", " - sdras/vue-vscode-snippets\n", " - sebs/etherscan-api\n", " - seeed-studio/seeed_arduino_can\n", " - seldaek/monolog\n", " - select2/select2\n", " - selectel/pyte\n", " - sendgrid/sendgrid-go\n", " - sendgrid/sendgrid-nodejs\n", " - sennevds/system_sensors\n", " - serayuzgur/crates\n", " - serhatbolsu/robotframework-appiumlibrary\n", " - serpapi/google-search-results-python\n", " - sfackler/rust-native-tls\n", " - sharkdp/bat\n", " - shazow/ssh-chat\n", " - shelljs/shelljs\n", " - shime/play-sound\n", " - shoaibrayeen/programmers-community\n", " - shon/httpagentparser\n", " - shouldjs/should.js\n", " - showndarya/hacktoberfest\n", " - shr<PERSON><PERSON><PERSON><PERSON>/gpt3-sandbox\n", " - sinergi/php-browser-detector\n", " - sinonjs/sinon\n", " - sitemesh/sitemesh2\n", " - sjwhitworth/golearn\n", " - skoczen/will\n", " - skovhus/jest-codemods\n", " - slack-go/slack\n", " - slackapi/python-slack-sdk\n", " - slaylines/canvas-engines-comparison\n", " - slicknode/graphql-query-complexity\n", " - slimevr/slimevr-server\n", " - slimphp/slim-skeleton\n", " - sly777/ran\n", " - smaranji<PERSON><PERSON><PERSON>/doc2pen\n", " - smi2/phpclickhouse\n", " - sonata-project/sonataadminbundle\n", " - sorayuki/obs-multi-rtmp\n", " - sparkfish/augraphy\n", " - spatie/laravel-backup\n", " - spatie/laravel-model-status\n", " - spatie/typescript-transformer\n", " - spotify/annoy\n", " - spotify/dns-java\n", " - spotify/missinglink\n", " - spotify/spydra\n", " - spotorm/spot2\n", " - spyder-ide/spyder-vim\n", " - sqlkata/querybuilder\n", " - square/flow\n", " - square/retrofit\n", " - square/sharkey\n", " - sr-sunny-raj/hacktoberfest2021-dsa\n", " - sshuttle/sshuttle\n", " - stackexchange/dapper\n", " - stararawn/bevy_ecs_tilemap\n", " - stefalda/react-localization\n", " - ste<PERSON><PERSON><PERSON>/geemusic\n", " - stlink-org/stlink\n", " - stm32-rs/stm32-rs\n", " - stm32-rs/stm32f4xx-hal\n", " - stomp-php/stomp-php\n", " - stopipv/isdi\n", " - storybookjs/vue-cli-plugin-storybook\n", " - streamaserver/streama\n", " - streamich/react-use\n", " - stripe/smokescreen\n", " - strongloop/node-foreman\n", " - styled-components/babel-plugin-styled-components\n", " - styled-components/polished\n", " - styled-components/styled-components\n", " - styled-components/styled-components-website\n", " - substack/tape\n", " - suke<PERSON>/jarvis\n", " - sukritishah15/ds-algo-point\n", " - supercilex/gnome-clipboard-history\n", " - su<PERSON><PERSON>u/jsdox\n", " - swagger-api/swagger-node\n", " - swagger-api/swagger-play\n", " - swagger-api/swagger-samples\n", " - swapnilsparsh/30daysofjavascript\n", " - swcarpentry/python-novice-inflammation\n", " - swkberlin/kata-bootstraps\n", " - sygil-dev/sygil-webui\n", " - syllo/nvtop\n", " - syrusakbary/flask-superadmin\n", " - syrusakbary/snapshottest\n", " - t3-oss/create-t3-app\n", " - tableau/webdataconnector\n", " - tarscloud/tarsjava\n", " - tcr/scissors\n", " - teamnewpipe/newpipe\n", " - techempower/frameworkbenchmarks\n", " - techlab/jquery-smartwizard\n", " - techtonica/curriculum\n", " - telmate/terraform-provider-proxmox\n", " - tencent/rapidjson\n", " - tensorflowkorea/tensorflow-kr\n", " - tesseractcoding/neoalgo\n", " - texitoi/structopt\n", " - tgdwyer/webcola\n", " - thamara/time-to-leave\n", " - the-cavalry/light-locker\n", " - the-control-group/voyager\n", " - thealgorithms/c\n", " - thealgorithms/c-plus-plus\n", " - thealgorithms/java\n", " - thealgorithms/javascript\n", " - thebjorn/pydeps\n", " - thedevdojo/voyager\n", " - theelous3/asks\n", " - thehive-project/cortex-analyzers\n", " - theodi/comma-chameleon\n", " - theofficialflow/vitashell\n", " - thephpleague/html-to-markdown\n", " - thephpleague/omnipay-paypal\n", " - thepracticaldev/1pr\n", " - therenegadecoder/sample-programs\n", " - thestk/rtaudio\n", " - thestk/rtmidi\n", " - thingpulse/esp8266-oled-ssd1306\n", " - thinkswell/javascript-mini-projects\n", " - thomaspark/gridgarden\n", " - thomwright/postgres-migrations\n", " - throwtheswitch/unity\n", " - tiaguinho/gosoap\n", " - tictail/tide\n", " - tighten/nova-stripe\n", " - tighten/parental\n", " - timusus/recyclerview-fastscroll\n", " - timvink/mkdocs-git-revision-date-localized-plugin\n", " - tinche/aiofiles\n", " - tivix/django-cron\n", " - tj/should.js\n", " - tjmehta/101\n", " - tmc/langchaingo\n", " - tng/archunitnet\n", " - toastdriven/restless\n", " - tobi-wan-kenobi/bumblebee-status\n", " - todogroup/repolinter\n", " - tokio-rs/tokio\n", " - tokio-rs/website\n", " - tomdionysus/foaas\n", " - torann/laravel-geoip\n", " - toystars/react-native-multiple-select\n", " - traverseda/pycraft\n", " - trendmicro/tlsh\n", " - trustwallet/assets\n", " - tryghost/ghost-android\n", " - tusharkesarwani/front-end-projects\n", " - tuya/tuya-homebridge\n", " - twigjs/twig.js\n", " - twiliodeved/api-snippets\n", " - twitchdev/twitch-cli\n", " - twitter/hraven\n", " - typeorm/typeorm\n", " - typescript-eslint/tslint-to-eslint-config\n", " - typestack/class-validator\n", " - uber/ringpop-go\n", " - uberi/speech_recognition\n", " - uberspace/lab\n", " - ui-lovelace-minimalist/ui\n", " - ultimaker/ultimaker2marlin\n", " - ultrabug/py3status\n", " - umbraspaceindustries/mks\n", " - unchained-capital/caravan\n", " - unitedincome/serverless-python-requirements\n", " - unitedstates/contact-congress\n", " - unity-technologies/dots-training-samples\n", " - urinx/weixinbot\n", " - urllib3/urllib3\n", " - uxsolutions/bootstrap-datepicker\n", " - vabene1111/recipes\n", " - vanderlin/ofxbox2d\n", " - vazkiimods/patchouli\n", " - vektra/mockery\n", " - vercel/commerce\n", " - vercel/react-keyframes\n", " - vfat-io/vfat-tools\n", " - vial-kb/vial-qmk\n", " - victorb/ngprogress\n", " - vigetlabs/blendid\n", " - vijaygupta18/hacktoberfest-2022\n", " - vikadata/vika.js\n", " - vim-vdebug/vdebug\n", " - vinceg/twitter-bootstrap-wizard\n", " - vingtcinq/python-mailchimp\n", " - vishen/go-chromecast\n", " - vishruth-s/competitivecode\n", " - visionmedia/superagent\n", " - visionmedia/supertest\n", " - vitasdk/vita-toolchain\n", " - vitormhenrique/octoprint-enclosure\n", " - vividcortex/angular-recaptcha\n", " - voxmedia/autotune\n", " - voxpupuli/puppetboard\n", " - vpenso/prometheus-slurm-exporter\n", " - vuejs/vuex\n", " - vuestorefront/vue-storefront\n", " - w2c/ispconfig3_roundcube\n", " - w3c/intersectionobserver\n", " - waikato/moa\n", " - wanasit/chrono\n", " - webankfintech/eventmesh\n", " - webcat12345/ngx-ui-switch\n", " - webdjoe/pyvesync\n", " - webfreak001/code-debug\n", " - webpack/webpack\n", " - websocket-client/websocket-client\n", " - wenyan<PERSON><PERSON>/ccfrank4dblp\n", " - wgsl-analyzer/wgsl-analyzer\n", " - wicwiu/wicwiu\n", " - willmc<PERSON><PERSON>/rich\n", " - withfig/autocomplete\n", " - witnessmenow/universal-arduino-telegram-bot\n", " - wolever/pip2pi\n", " - wolph/python-statsd\n", " - wolpi/prim-ftpd\n", " - worldveil/dejavu\n", " - wovalle/fireorm\n", " - wvengen/proguard-maven-plugin\n", " - xabaril/aspnetcore.diagnostics.healthchecks\n", " - xanzy/go-gitlab\n", " - xbmc/xbmc\n", " - xhtml2pdf/xhtml2pdf\n", " - ximion/appstream\n", " - yakovkhalinsky/backblaze-b2\n", " - yandex/pandora\n", " - yanickrochon/jquery.uix.multiselect\n", " - yatekii/imgui-wgpu-rs\n", " - yegor256/dynamo-archive\n", " - yhat/db.py\n", " - yiisoft/yii2-app-advanced\n", " - yosuke-furukawa/tower-of-babel\n", " - yrutschle/sslh\n", " - ysugimoto/aws-lambda-image\n", " - z4r/python-coveralls\n", " - z4yx/goauthing\n", " - zalando-incubator/cluster-lifecycle-manager\n", " - zalando/postgres-operator\n", " - zendframework/zend-servicemanager\n", " - zendframework/zend-validator\n", " - zengo-x/multi-party-ecdsa\n", " - zeromq/zeromq4-1\n", " - zeromq/zmqpp\n", " - ziv-barber/officegen\n", " - zmap/zgrab2\n", " - zulip/python-zulip-api\n"]}], "source": ["from pymongo import MongoClient\n", "\n", "def compare_collections():\n", "    client = MongoClient('mongodb://localhost:27017/')\n", "    db = client['disengagement']\n", "    col1 = db['repo_breaks_attritions']\n", "    col2 = db['repo_breaks_attritions_365']\n", "    # check if col1 and col2 exists\n", "    if not col1.count_documents({}) or not col2.count_documents({}):\n", "        print(\"One of the collections is empty\")\n", "        return\n", "    \n", "    # Get all repo_names in both collections\n", "    repo_names_1 = set(doc['repo_name'] for doc in col1.find({}, {'repo_name': 1}))\n", "    repo_names_2 = set(doc['repo_name'] for doc in col2.find({}, {'repo_name': 1}))\n", "    common_repo_names = repo_names_1 & repo_names_2\n", "\n", "    identical_repos = []\n", "    different_repos = []\n", "\n", "    for repo_name in sorted(common_repo_names):\n", "        doc1 = col1.find_one({'repo_name': repo_name})\n", "        doc2 = col2.find_one({'repo_name': repo_name})\n", "\n", "        # Sort breaks and attritions by id for reliable comparison\n", "        def sorted_list(lst, key='id'):\n", "            if not isinstance(lst, list):\n", "                return []\n", "            return sorted(lst, key=lambda x: x.get(key, 0))\n", "\n", "        breaks1 = sorted_list(doc1.get('breaks', []))\n", "        breaks2 = sorted_list(doc2.get('breaks', []))\n", "        attritions1 = sorted_list(doc1.get('attritions', []))\n", "        attritions2 = sorted_list(doc2.get('attritions', []))\n", "\n", "        if breaks1 == breaks2 and attritions1 == attritions2:\n", "            identical_repos.append(repo_name)\n", "        else:\n", "            different_repos.append(repo_name)\n", "\n", "    print(f\"Total repos compared: {len(common_repo_names)}\")\n", "    print(f\"Identical repos: {len(identical_repos)}\")\n", "    print(f\"Different repos: {len(different_repos)}\")\n", "    if different_repos:\n", "        print(\"Repos with differences:\")\n", "        for repo in different_repos:\n", "            print(f\" - {repo}\")\n", "\n", "if __name__ == \"__main__\":\n", "    compare_collections()\n"]}, {"cell_type": "code", "execution_count": null, "id": "49f2765f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}