import pandas as pd
from pymongo import MongoClient
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)

def check_database_structure():
    """检查数据库结构"""
    logging.info("开始检查数据库结构...")
    
    # 连接数据库
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    
    # 检查project_analysis集合
    collection = db["project_analysis"]
    
    # 获取一个样本文档
    sample_doc = collection.find_one()
    if sample_doc:
        logging.info("数据库结构:")
        for key, value in sample_doc.items():
            if isinstance(value, dict):
                logging.info(f"  {key}: {type(value)} - keys: {list(value.keys())}")
            else:
                logging.info(f"  {key}: {type(value)}")
    
    # 检查Attrition相关字段
    attrition_fields = []
    attrition_365_fields = []
    
    cursor = collection.find({})
    for doc in cursor:
        for key in doc.keys():
            if 'attrition' in key.lower():
                if '365' in key:
                    attrition_365_fields.append(key)
                else:
                    attrition_fields.append(key)
    
    logging.info(f"找到的Attrition字段: {list(set(attrition_fields))}")
    logging.info(f"找到的Attrition_365字段: {list(set(attrition_365_fields))}")
    
    # 检查具体的字段内容
    sample_with_attrition = collection.find_one({"Attrition": {"$exists": True}})
    if sample_with_attrition:
        logging.info("Attrition字段示例:")
        logging.info(f"  {sample_with_attrition.get('Attrition', {})}")
    
    sample_with_attrition_365 = collection.find_one({"Attrition_365_new": {"$exists": True}})
    if sample_with_attrition_365:
        logging.info("Attrition_365_new字段示例:")
        logging.info(f"  {sample_with_attrition_365.get('Attrition_365_new', {})}")
    
    # 统计字段存在情况
    attrition_count = collection.count_documents({"Attrition": {"$exists": True, "$ne": None}})
    attrition_365_count = collection.count_documents({"Attrition_365_new": {"$exists": True, "$ne": None}})
    
    logging.info(f"有Attrition字段的文档数: {attrition_count}")
    logging.info(f"有Attrition_365_new字段的文档数: {attrition_365_count}")

if __name__ == "__main__":
    check_database_structure()