import pandas as pd
import numpy as np
from pymongo import MongoClient
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/code_logic_analysis.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def analyze_processing_differences():
    """分析新旧代码处理逻辑的差异"""
    logging.info("开始分析新旧代码处理逻辑差异...")
    
    differences = {
        "data_processing": {
            "old_approach": "使用原有的commit数据处理方式",
            "new_approach": "使用新的commit数据处理方式，包括时区统一处理",
            "key_differences": [
                "新代码统一了时区处理: repo_commits['date'] = pd.to_datetime(repo_commits['date'], format='mixed')",
                "新代码对开发者commit数据也进行了时区处理: dev_commits['date'] = pd.to_datetime(dev_commits['date'], format='%Y-%m-%dT%H:%M:%SZ')"
            ]
        },
        "attrition_calculation": {
            "old_approach": "使用单一的attrition计算方式",
            "new_approach": "使用多种attrition阈值和新的计算逻辑",
            "key_differences": [
                "新代码支持多种attrition阈值: [180, 270, 365, 450]天",
                "新代码将breaks中的长暂停也计入attrition",
                "新代码使用Attrition_new字段结构，包含多种attrition类型"
            ]
        },
        "breaks_calculation": {
            "old_approach": "使用原有的breaks计算方式",
            "new_approach": "使用改进的breaks计算方式",
            "key_differences": [
                "新代码使用滑动窗口计算暂停: window_size=1",
                "新代码使用IQR方法识别长暂停: Tfov = Q3 + 3 * IQR",
                "新代码改进了暂停持续时间的计算单位"
            ]
        },
        "data_storage": {
            "old_approach": "存储到project_analysis集合",
            "new_approach": "存储到project_analysis_new集合",
            "key_differences": [
                "新代码使用不同的集合名称",
                "新代码改进了数据结构和字段命名",
                "新代码增加了更多的元数据字段"
            ]
        }
    }
    
    return differences

def analyze_specific_case_studies(db):
    """分析具体的案例研究，找出差异的原因"""
    logging.info("开始分析具体案例研究...")
    
    old_collection = db["project_analysis"]
    new_collection = db["project_analysis_new"]
    
    # 找出有显著差异的仓库
    case_studies = []
    
    # 获取所有仓库
    all_repos = set(old_collection.distinct("repo_name")) | set(new_collection.distinct("repo_name"))
    
    for repo in list(all_repos)[:10]:  # 分析前10个仓库作为案例
        old_docs = list(old_collection.find({"repo_name": repo}))
        new_docs = list(new_collection.find({"repo_name": repo}))
        
        old_devs = len(old_docs)
        new_devs = len(new_docs)
        
        # 统计attrition差异
        old_attrition_count = sum(1 for doc in old_docs if doc.get("Attrition"))
        new_attrition_count = sum(1 for doc in new_docs if doc.get("Attrition_new"))
        
        # 统计breaks差异
        old_breaks_count = sum(len(doc.get("Breaks", [])) for doc in old_docs)
        new_breaks_count = sum(len(doc.get("Breaks", [])) for doc in new_docs)
        
        case_studies.append({
            "repo_name": repo,
            "old_developers": old_devs,
            "new_developers": new_devs,
            "developer_difference": new_devs - old_devs,
            "old_attrition_count": old_attrition_count,
            "new_attrition_count": new_attrition_count,
            "attrition_difference": new_attrition_count - old_attrition_count,
            "old_breaks_count": old_breaks_count,
            "new_breaks_count": new_breaks_count,
            "breaks_difference": new_breaks_count - old_breaks_count
        })
    
    case_studies_df = pd.DataFrame(case_studies)
    case_studies_df = case_studies_df.sort_values("developer_difference", ascending=False)
    
    case_studies_df.to_csv("../result/20250728_compare_test/case_studies_analysis.csv", index=False)
    
    logging.info("案例研究分析完成")
    return case_studies_df

def analyze_data_quality_issues(db):
    """分析数据质量问题"""
    logging.info("开始分析数据质量问题...")
    
    old_collection = db["project_analysis"]
    new_collection = db["project_analysis_new"]
    
    quality_issues = {
        "missing_data": {
            "old_collection": {
                "total_docs": old_collection.count_documents({}),
                "docs_with_attrition": old_collection.count_documents({"Attrition": {"$exists": True, "$ne": None}}),
                "docs_with_breaks": old_collection.count_documents({"Breaks": {"$exists": True, "$ne": []}})
            },
            "new_collection": {
                "total_docs": new_collection.count_documents({}),
                "docs_with_attrition": new_collection.count_documents({"Attrition_new": {"$exists": True, "$ne": None}}),
                "docs_with_breaks": new_collection.count_documents({"Breaks": {"$exists": True, "$ne": []}})
            }
        },
        "data_consistency": {
            "old_repos_with_data": len(old_collection.distinct("repo_name")),
            "new_repos_with_data": len(new_collection.distinct("repo_name")),
            "old_devs_with_data": len(old_collection.distinct("core_dev_login")),
            "new_devs_with_data": len(new_collection.distinct("core_dev_login"))
        }
    }
    
    # 计算数据完整性
    old_attrition_rate = quality_issues["missing_data"]["old_collection"]["docs_with_attrition"] / quality_issues["missing_data"]["old_collection"]["total_docs"] * 100
    new_attrition_rate = quality_issues["missing_data"]["new_collection"]["docs_with_attrition"] / quality_issues["missing_data"]["new_collection"]["total_docs"] * 100
    
    quality_issues["attrition_rates"] = {
        "old_attrition_rate": old_attrition_rate,
        "new_attrition_rate": new_attrition_rate,
        "rate_difference": new_attrition_rate - old_attrition_rate
    }
    
    logging.info(f"数据质量分析完成")
    logging.info(f"旧集合attrition率: {old_attrition_rate:.2f}%")
    logging.info(f"新集合attrition率: {new_attrition_rate:.2f}%")
    logging.info(f"差异: {new_attrition_rate - old_attrition_rate:.2f}%")
    
    return quality_issues

def analyze_algorithmic_differences():
    """分析算法差异"""
    logging.info("开始分析算法差异...")
    
    algorithmic_differences = {
        "pause_detection": {
            "old_algorithm": "使用固定阈值或简单的时间差计算",
            "new_algorithm": "使用滑动窗口和IQR统计方法",
            "impact": "新算法能更准确地识别异常长的暂停期"
        },
        "attrition_detection": {
            "old_algorithm": "基于最后提交时间的简单阈值判断",
            "new_algorithm": "结合最后提交时间和长暂停期的综合判断",
            "impact": "新算法能识别更多类型的开发者流失情况"
        },
        "data_processing": {
            "old_algorithm": "可能存在的时区处理不一致",
            "new_algorithm": "统一的时区处理和日期格式标准化",
            "impact": "新算法提供更一致的时间计算"
        }
    }
    
    return algorithmic_differences

def generate_logic_analysis_report(differences, case_studies_df, quality_issues, algorithmic_differences):
    """生成代码逻辑分析报告"""
    
    report = {
        "analysis_timestamp": datetime.now().isoformat(),
        "processing_differences": differences,
        "case_studies_summary": {
            "total_cases_analyzed": len(case_studies_df),
            "largest_developer_difference": case_studies_df["developer_difference"].max(),
            "largest_attrition_difference": case_studies_df["attrition_difference"].max(),
            "largest_breaks_difference": case_studies_df["breaks_difference"].max()
        },
        "data_quality_analysis": quality_issues,
        "algorithmic_differences": algorithmic_differences
    }
    
    # 保存JSON报告
    with open("../result/20250728_compare_test/code_logic_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_report = f"""
# 代码逻辑差异分析报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 主要处理差异

### 1. 数据处理方式
**旧方法**: {differences['data_processing']['old_approach']}
**新方法**: {differences['data_processing']['new_approach']}

**关键差异**:
{chr(10).join(f"- {diff}" for diff in differences['data_processing']['key_differences'])}

### 2. Attrition计算方式
**旧方法**: {differences['attrition_calculation']['old_approach']}
**新方法**: {differences['attrition_calculation']['new_approach']}

**关键差异**:
{chr(10).join(f"- {diff}" for diff in differences['attrition_calculation']['key_differences'])}

### 3. Breaks计算方式
**旧方法**: {differences['breaks_calculation']['old_approach']}
**新方法**: {differences['breaks_calculation']['new_approach']}

**关键差异**:
{chr(10).join(f"- {diff}" for diff in differences['breaks_calculation']['key_differences'])}

## 数据质量分析

### 数据完整性
- 旧集合总文档数: {quality_issues['missing_data']['old_collection']['total_docs']}
- 新集合总文档数: {quality_issues['missing_data']['new_collection']['total_docs']}
- 旧集合有attrition的文档: {quality_issues['missing_data']['old_collection']['docs_with_attrition']}
- 新集合有attrition的文档: {quality_issues['missing_data']['new_collection']['docs_with_attrition']}

### Attrition率对比
- 旧集合attrition率: {quality_issues['attrition_rates']['old_attrition_rate']:.2f}%
- 新集合attrition率: {quality_issues['attrition_rates']['new_attrition_rate']:.2f}%
- 差异: {quality_issues['attrition_rates']['rate_difference']:.2f}%

## 算法差异分析

### 暂停检测算法
**旧算法**: {algorithmic_differences['pause_detection']['old_algorithm']}
**新算法**: {algorithmic_differences['pause_detection']['new_algorithm']}
**影响**: {algorithmic_differences['pause_detection']['impact']}

### 流失检测算法
**旧算法**: {algorithmic_differences['attrition_detection']['old_algorithm']}
**新算法**: {algorithmic_differences['attrition_detection']['new_algorithm']}
**影响**: {algorithmic_differences['attrition_detection']['impact']}

## 案例研究结果

分析了 {len(case_studies_df)} 个仓库的差异情况:

### 开发者数量差异最大的仓库
{chr(10).join(f"- {row['repo_name']}: 差异 {row['developer_difference']} 个开发者" for _, row in case_studies_df.head(5).iterrows())}

### Attrition差异最大的仓库
{chr(10).join(f"- {row['repo_name']}: 差异 {row['attrition_difference']} 个attrition记录" for _, row in case_studies_df.nlargest(5, 'attrition_difference').iterrows())}

## 结论和建议

### 新代码的优势:
1. **更准确的暂停检测**: 使用IQR统计方法能更好地识别异常暂停
2. **更全面的流失检测**: 结合多种阈值和暂停期分析
3. **更一致的数据处理**: 统一的时区处理和日期格式
4. **更丰富的数据结构**: 支持多种attrition类型

### 可能的问题:
1. **数据量增加**: 新算法可能产生更多的attrition记录
2. **计算复杂度**: 新算法可能需要更多的计算资源
3. **结果解释**: 需要重新理解新的attrition定义

### 建议:
1. 验证新算法的准确性
2. 建立新旧结果的映射关系
3. 考虑是否需要调整阈值参数
4. 进行更多的案例验证
"""
    
    with open("../result/20250728_compare_test/code_logic_analysis_report.md", "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    logging.info("代码逻辑分析报告已生成完成")

def main():
    """主函数"""
    logging.info("开始代码逻辑差异分析...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 1. 分析处理逻辑差异
    differences = analyze_processing_differences()
    
    # 2. 分析具体案例
    case_studies_df = analyze_specific_case_studies(db)
    
    # 3. 分析数据质量问题
    quality_issues = analyze_data_quality_issues(db)
    
    # 4. 分析算法差异
    algorithmic_differences = analyze_algorithmic_differences()
    
    # 5. 生成分析报告
    generate_logic_analysis_report(differences, case_studies_df, quality_issues, algorithmic_differences)
    
    logging.info("代码逻辑差异分析完成！")

if __name__ == "__main__":
    main()