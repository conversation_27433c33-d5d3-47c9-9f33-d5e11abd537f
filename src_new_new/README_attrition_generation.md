# Attrition Generation Script

这个脚本用于为MongoDB中的开发者数据生成不同时间限制的attrition字段。

## 功能特点

- **多线程处理**: 使用ThreadPoolExecutor进行并行处理，提高处理速度
- **灵活的时间限制**: 支持任意数量的attrition时间限制（如180, 270, 365, 450天）
- **命令行参数**: 支持通过命令行参数灵活配置
- **详细日志**: 提供详细的处理日志，便于调试和监控

## 使用方法

### 基本用法

```bash
# 使用默认设置（处理180, 270, 365, 450天限制）
python3 generate_attrition_365_new_multithread.py

# 只处理单个时间限制
python3 generate_attrition_365_new_multithread.py --single-limit 365

# 处理自定义的时间限制
python3 generate_attrition_365_new_multithread.py --limits 180 365

# 调整工作线程数量
python3 generate_attrition_365_new_multithread.py --workers 16
```

### 命令行参数

- `--limits`: 指定要处理的attrition时间限制列表（默认: 180 270 365 450）
- `--workers`: 指定工作线程数量（默认: 8）
- `--single-limit`: 只处理单个时间限制（会覆盖--limits参数）

### 使用示例

```bash
# 示例1: 处理所有默认限制
python3 generate_attrition_365_new_multithread.py

# 示例2: 只处理365天限制
python3 generate_attrition_365_new_multithread.py --single-limit 365

# 示例3: 处理自定义限制
python3 generate_attrition_365_new_multithread.py --limits 90 180 270 365

# 示例4: 使用更多线程加速处理
python3 generate_attrition_365_new_multithread.py --limits 180 365 --workers 16

# 示例5: 处理扩展的时间限制
python3 generate_attrition_365_new_multithread.py --limits 90 180 270 365 450 540
```

## 生成的字段

脚本会为每个指定的时间限制生成对应的字段：

- `Attrition_180_new`: 180天attrition限制
- `Attrition_270_new`: 270天attrition限制  
- `Attrition_365_new`: 365天attrition限制
- `Attrition_450_new`: 450天attrition限制

## 判断逻辑

对于每个开发者，脚本会检查以下条件来判断是否attrition：

1. **最后提交时间检查**: 如果开发者的最后提交时间与项目最后提交时间相差超过指定天数
2. **Break持续时间检查**: 如果开发者有任何break的持续时间超过指定天数

如果满足任一条件，会在对应的`Attrition_{limit}_new`字段中记录attrition日期。

## 输出格式

生成的attrition字段格式如下：

```json
{
  "Attrition_365_new": {
    "attrition_date": ["2023-01-15", "2023-06-20"]
  }
}
```

## 日志文件

脚本会在`../logs/`目录下生成日志文件：
- `generate_attrition_multithread.log`: 详细的处理日志

## 性能优化

- 使用多线程并行处理不同仓库
- 每个线程使用独立的MongoDB连接
- 批量处理减少数据库操作次数
- 可配置的工作线程数量

## 注意事项

1. 确保MongoDB服务正在运行
2. 确保`../data/processed_commits/`目录下有对应的commit数据文件
3. 根据系统性能调整`--workers`参数
4. 处理大量数据时建议使用SSD存储以提高I/O性能

## 与原始脚本的差异

相比原始的`identify_break_disengagement.py`：

1. **多线程支持**: 显著提高处理速度
2. **灵活的时间限制**: 支持任意数量的时间限制
3. **命令行参数**: 更灵活的使用方式
4. **更好的错误处理**: 更详细的错误日志和异常处理
5. **性能优化**: 减少内存使用和数据库连接开销