#!/usr/bin/env python3
"""
MongoDB新旧结果比较分析主控制脚本
运行所有分析模块并生成综合报告
"""

import os
import sys
import logging
from datetime import datetime
import subprocess

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/run_all_analysis.log", mode="w", encoding="utf-8"),
    ],
)

def run_script(script_name, description):
    """运行指定的Python脚本"""
    logging.info(f"开始运行: {description}")
    logging.info(f"执行脚本: {script_name}")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        
        if result.returncode == 0:
            logging.info(f"✅ {description} 执行成功")
            if result.stdout:
                logging.info(f"输出: {result.stdout}")
        else:
            logging.error(f"❌ {description} 执行失败")
            logging.error(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        logging.error(f"❌ {description} 执行异常: {e}")
        return False
    
    return True

def create_summary_report():
    """创建综合分析报告"""
    logging.info("创建综合分析报告...")
    
    # 检查结果文件是否存在
    result_dir = "../result/20250728_compare_test"
    if not os.path.exists(result_dir):
        logging.error(f"结果目录不存在: {result_dir}")
        return
    
    # 生成综合报告
    summary_report = f"""
# MongoDB新旧结果比较分析 - 综合报告

## 分析时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 执行的分析模块

### 1. 基础比较分析 (20250728_compare_mongodb_results.py)
- 比较两个MongoDB集合的基本统计信息
- 分析仓库和开发者的覆盖情况
- 生成基础比较报告

### 2. Attrition详细分析 (20250728_attrition_detailed_analysis.py)
- 详细分析attrition数据的差异
- 按仓库、开发者、时间模式进行分析
- 生成attrition差异的详细报告

### 3. 代码逻辑分析 (20250728_code_logic_analysis.py)
- 分析新旧代码的处理逻辑差异
- 研究算法改进和数据质量变化
- 生成代码逻辑分析报告

## 生成的结果文件

### 基础比较结果
- `comparison_report.json` - 基础比较的JSON报告
- `comparison_report.md` - 基础比较的Markdown报告
- `old_attrition_data.csv` - 旧集合attrition数据
- `new_attrition_data.csv` - 新集合attrition数据
- `old_breaks_data.csv` - 旧集合breaks数据
- `new_breaks_data.csv` - 新集合breaks数据

### Attrition详细分析结果
- `attrition_analysis_report.json` - Attrition分析的JSON报告
- `attrition_analysis_report.md` - Attrition分析的Markdown报告
- `old_attrition_detailed.csv` - 详细的旧attrition数据
- `new_attrition_detailed.csv` - 详细的新attrition数据
- `old_only_attrition.csv` - 只在旧集合中的attrition记录
- `new_only_attrition.csv` - 只在新集合中的attrition记录
- `common_attrition.csv` - 两个集合都存在的attrition记录
- `repo_attrition_differences.csv` - 按仓库的attrition差异
- `developer_attrition_differences.csv` - 按开发者的attrition差异
- `attrition_year_statistics.csv` - 按年份的attrition统计

### 代码逻辑分析结果
- `code_logic_analysis_report.json` - 代码逻辑分析的JSON报告
- `code_logic_analysis_report.md` - 代码逻辑分析的Markdown报告
- `case_studies_analysis.csv` - 案例研究分析结果

## 使用说明

### 查看结果
1. 查看Markdown报告了解总体情况
2. 查看CSV文件进行详细数据分析
3. 查看JSON报告获取结构化数据

### 进一步分析
1. 使用生成的CSV文件进行自定义分析
2. 根据报告中的差异进行针对性研究
3. 验证新算法的准确性和有效性

## 注意事项
- 所有结果文件保存在 `../result/20250728_compare_test/` 目录下
- 日志文件保存在 `../logs/` 目录下
- 确保MongoDB服务正在运行
- 确保有足够的磁盘空间存储结果文件

## 联系信息
如有问题或需要进一步分析，请查看日志文件或联系开发团队。
"""
    
    with open(f"{result_dir}/comprehensive_analysis_report.md", "w", encoding="utf-8") as f:
        f.write(summary_report)
    
    logging.info("综合分析报告已生成")

def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("开始执行MongoDB新旧结果比较分析")
    logging.info("=" * 60)
    
    # 创建结果目录
    result_dir = "../result/20250728_compare_test"
    os.makedirs(result_dir, exist_ok=True)
    
    # 定义要运行的脚本
    scripts = [
        {
            "name": "20250728_compare_mongodb_results.py",
            "description": "基础比较分析"
        },
        {
            "name": "20250728_attrition_detailed_analysis.py", 
            "description": "Attrition详细分析"
        },
        {
            "name": "20250728_code_logic_analysis.py",
            "description": "代码逻辑分析"
        }
    ]
    
    # 执行所有脚本
    success_count = 0
    total_count = len(scripts)
    
    for script in scripts:
        success = run_script(script["name"], script["description"])
        if success:
            success_count += 1
    
    # 生成综合报告
    create_summary_report()
    
    # 输出执行结果
    logging.info("=" * 60)
    logging.info(f"分析执行完成: {success_count}/{total_count} 个脚本成功执行")
    
    if success_count == total_count:
        logging.info("🎉 所有分析模块执行成功！")
        logging.info(f"结果文件保存在: {result_dir}")
    else:
        logging.warning(f"⚠️  {total_count - success_count} 个脚本执行失败，请检查日志")
    
    logging.info("=" * 60)

if __name__ == "__main__":
    main()