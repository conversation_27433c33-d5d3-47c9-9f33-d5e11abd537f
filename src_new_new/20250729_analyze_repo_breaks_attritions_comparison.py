import pandas as pd
from pymongo import MongoClient
import logging
from datetime import datetime
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/analyze_repo_breaks_attritions_comparison.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def extract_collection_data(db, collection_name):
    """提取指定集合的数据"""
    logging.info(f"开始提取 {collection_name} 集合的数据...")
    
    collection = db[collection_name]
    data = []
    
    cursor = collection.find({})
    for doc in cursor:
        repo_name = doc.get("repo_name", "")
        breaks = doc.get("breaks", [])
        attritions = doc.get("attritions", [])
        
        # 统计breaks信息
        breaks_stats = {
            "total_breaks": len(breaks),
            "unique_devs_in_breaks": len(set(break_info.get("dev_login", "") for break_info in breaks)),
            "avg_duration": sum(break_info.get("duration", 0) for break_info in breaks) / len(breaks) if breaks else 0,
            "max_duration": max((break_info.get("duration", 0) for break_info in breaks), default=0),
            "min_duration": min((break_info.get("duration", 0) for break_info in breaks), default=0)
        }
        
        # 统计attritions信息
        attritions_stats = {
            "total_attritions": len(attritions),
            "unique_devs_in_attritions": len(set(attrition.get("dev_login", "") for attrition in attritions)),
            "attrition_times": [attrition.get("attrition_time", "") for attrition in attritions]
        }
        
        data.append({
            "repo_name": repo_name,
            "breaks": breaks,
            "attritions": attritions,
            "breaks_stats": breaks_stats,
            "attritions_stats": attritions_stats
        })
    
    logging.info(f"  {collection_name}: 提取了 {len(data)} 条记录")
    return data

def compare_collections(db):
    """比较两个集合的数据"""
    logging.info("开始比较两个集合的数据...")
    
    # 提取两个集合的数据
    collection1_data = extract_collection_data(db, "repo_breaks_attritions")
    collection2_data = extract_collection_data(db, "repo_breaks_attritions_365")
    
    # 创建DataFrame
    df1 = pd.DataFrame(collection1_data)
    df2 = pd.DataFrame(collection2_data)
    
    # 基本统计比较
    print(f"\n{'='*80}")
    print("基本统计比较")
    print(f"{'='*80}")
    
    print(f"\nrepo_breaks_attritions:")
    print(f"  总记录数: {len(df1)}")
    print(f"  涉及仓库数: {df1['repo_name'].nunique()}")
    print(f"  总Breaks数: {df1['breaks_stats'].apply(lambda x: x['total_breaks']).sum()}")
    print(f"  总Attritions数: {df1['attritions_stats'].apply(lambda x: x['total_attritions']).sum()}")
    
    print(f"\nrepo_breaks_attritions_365:")
    print(f"  总记录数: {len(df2)}")
    print(f"  涉及仓库数: {df2['repo_name'].nunique()}")
    print(f"  总Breaks数: {df2['breaks_stats'].apply(lambda x: x['total_breaks']).sum()}")
    print(f"  总Attritions数: {df2['attritions_stats'].apply(lambda x: x['total_attritions']).sum()}")
    
    # 按仓库名进行匹配比较
    print(f"\n{'='*80}")
    print("按仓库名匹配比较")
    print(f"{'='*80}")
    
    # 获取所有唯一的仓库名
    all_repos = set(df1['repo_name'].tolist() + df2['repo_name'].tolist())
    logging.info(f"总共有 {len(all_repos)} 个唯一的仓库")
    
    comparison_data = []
    differences = []
    
    for repo_name in all_repos:
        # 获取两个集合中该仓库的数据
        repo_data1 = df1[df1['repo_name'] == repo_name]
        repo_data2 = df2[df2['repo_name'] == repo_name]
        
        has_data1 = len(repo_data1) > 0
        has_data2 = len(repo_data2) > 0
        
        comparison_record = {
            "repo_name": repo_name,
            "in_collection1": has_data1,
            "in_collection2": has_data2,
            "in_both": has_data1 and has_data2
        }
        
        if has_data1:
            record1 = repo_data1.iloc[0]
            comparison_record.update({
                "breaks_count_1": record1['breaks_stats']['total_breaks'],
                "attritions_count_1": record1['attritions_stats']['total_attritions'],
                "unique_devs_breaks_1": record1['breaks_stats']['unique_devs_in_breaks'],
                "unique_devs_attritions_1": record1['attritions_stats']['unique_devs_in_attritions'],
                "avg_duration_1": record1['breaks_stats']['avg_duration'],
                "max_duration_1": record1['breaks_stats']['max_duration'],
                "min_duration_1": record1['breaks_stats']['min_duration']
            })
        else:
            comparison_record.update({
                "breaks_count_1": 0,
                "attritions_count_1": 0,
                "unique_devs_breaks_1": 0,
                "unique_devs_attritions_1": 0,
                "avg_duration_1": 0,
                "max_duration_1": 0,
                "min_duration_1": 0
            })
        
        if has_data2:
            record2 = repo_data2.iloc[0]
            comparison_record.update({
                "breaks_count_2": record2['breaks_stats']['total_breaks'],
                "attritions_count_2": record2['attritions_stats']['total_attritions'],
                "unique_devs_breaks_2": record2['breaks_stats']['unique_devs_in_breaks'],
                "unique_devs_attritions_2": record2['attritions_stats']['unique_devs_in_attritions'],
                "avg_duration_2": record2['breaks_stats']['avg_duration'],
                "max_duration_2": record2['breaks_stats']['max_duration'],
                "min_duration_2": record2['breaks_stats']['min_duration']
            })
        else:
            comparison_record.update({
                "breaks_count_2": 0,
                "attritions_count_2": 0,
                "unique_devs_breaks_2": 0,
                "unique_devs_attritions_2": 0,
                "avg_duration_2": 0,
                "max_duration_2": 0,
                "min_duration_2": 0
            })
        
        comparison_data.append(comparison_record)
        
        # 检查是否有差异
        if has_data1 and has_data2:
            record1 = repo_data1.iloc[0]
            record2 = repo_data2.iloc[0]
            
            # 比较breaks
            breaks1 = record1['breaks']
            breaks2 = record2['breaks']
            
            # 比较attritions
            attritions1 = record1['attritions']
            attritions2 = record2['attritions']
            
            # 检查是否有差异
            has_differences = (
                len(breaks1) != len(breaks2) or
                len(attritions1) != len(attritions2) or
                breaks1 != breaks2 or
                attritions1 != attritions2
            )
            
            if has_differences:
                differences.append({
                    "repo_name": repo_name,
                    "breaks1": breaks1,
                    "breaks2": breaks2,
                    "attritions1": attritions1,
                    "attritions2": attritions2,
                    "breaks_count_diff": len(breaks1) - len(breaks2),
                    "attritions_count_diff": len(attritions1) - len(attritions2)
                })
    
    comparison_df = pd.DataFrame(comparison_data)
    
    logging.info(f"发现 {len(differences)} 个有差异的记录")
    
    return comparison_df, differences

def print_differences(differences):
    """打印有差异的记录"""
    print(f"\n{'='*100}")
    print(f"发现 {len(differences)} 个有差异的记录")
    print(f"{'='*100}")
    
    for i, diff in enumerate(differences[:20]):  # 只显示前20个
        print(f"\n{i+1}. {diff['repo_name']}")
        print(f"   Breaks数量差异: {diff['breaks_count_diff']} (repo_breaks_attritions: {len(diff['breaks1'])}, repo_breaks_attritions_365: {len(diff['breaks2'])})")
        print(f"   Attritions数量差异: {diff['attritions_count_diff']} (repo_breaks_attritions: {len(diff['attritions1'])}, repo_breaks_attritions_365: {len(diff['attritions2'])})")
        
        print(f"   repo_breaks_attritions Breaks:")
        for j, break_info in enumerate(diff['breaks1']):
            print(f"     {j+1}. {break_info.get('dev_login', '')} - {break_info.get('start_time', '')} 到 {break_info.get('end_time', '')} (持续{break_info.get('duration', 0)}天)")
        
        print(f"   repo_breaks_attritions_365 Breaks:")
        for j, break_info in enumerate(diff['breaks2']):
            print(f"     {j+1}. {break_info.get('dev_login', '')} - {break_info.get('start_time', '')} 到 {break_info.get('end_time', '')} (持续{break_info.get('duration', 0)}天)")
        
        print(f"   repo_breaks_attritions Attritions:")
        for j, attrition in enumerate(diff['attritions1']):
            print(f"     {j+1}. {attrition.get('dev_login', '')} - {attrition.get('attrition_time', '')}")
        
        print(f"   repo_breaks_attritions_365 Attritions:")
        for j, attrition in enumerate(diff['attritions2']):
            print(f"     {j+1}. {attrition.get('dev_login', '')} - {attrition.get('attrition_time', '')}")
        
        print("-" * 80)
    
    if len(differences) > 20:
        print(f"\n... 还有 {len(differences) - 20} 个差异记录未显示")

def save_analysis_results(comparison_df, differences):
    """保存分析结果"""
    logging.info("保存分析结果...")
    
    # 创建输出目录
    output_dir = "../result/20250729_repo_breaks_attritions_comparison"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存完整比较数据
    comparison_df.to_csv(f"{output_dir}/repo_breaks_attritions_comparison.csv", index=False)
    
    # 保存有差异的记录
    if differences:
        diff_data = []
        for diff in differences:
            diff_record = {
                "repo_name": diff["repo_name"],
                "breaks_count_diff": diff["breaks_count_diff"],
                "attritions_count_diff": diff["attritions_count_diff"],
                "breaks1_count": len(diff["breaks1"]),
                "breaks2_count": len(diff["breaks2"]),
                "attritions1_count": len(diff["attritions1"]),
                "attritions2_count": len(diff["attritions2"]),
                "breaks1_data": str(diff["breaks1"]),
                "breaks2_data": str(diff["breaks2"]),
                "attritions1_data": str(diff["attritions1"]),
                "attritions2_data": str(diff["attritions2"])
            }
            diff_data.append(diff_record)
        
        diff_df = pd.DataFrame(diff_data)
        diff_df.to_csv(f"{output_dir}/repo_breaks_attritions_differences.csv", index=False)
    
    # 生成统计报告
    stats = {
        "total_repos": len(comparison_df),
        "in_collection1_only": len(comparison_df[comparison_df["in_collection1"] & ~comparison_df["in_collection2"]]),
        "in_collection2_only": len(comparison_df[~comparison_df["in_collection1"] & comparison_df["in_collection2"]]),
        "in_both": len(comparison_df[comparison_df["in_both"]]),
        "differences_count": len(differences),
        "consistency_rate": (len(comparison_df[comparison_df["in_both"]]) - len(differences)) / len(comparison_df[comparison_df["in_both"]]) * 100 if len(comparison_df[comparison_df["in_both"]]) > 0 else 0
    }
    
    with open(f"{output_dir}/repo_breaks_attritions_comparison_stats.json", "w", encoding="utf-8") as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    logging.info(f"分析结果已保存到 {output_dir}")

def print_summary_statistics(comparison_df, differences):
    """打印统计摘要"""
    print(f"\n{'='*80}")
    print("repo_breaks_attritions vs repo_breaks_attritions_365 比较统计摘要")
    print(f"{'='*80}")
    print(f"总仓库数: {len(comparison_df)}")
    print(f"仅在repo_breaks_attritions中: {len(comparison_df[comparison_df['in_collection1'] & ~comparison_df['in_collection2']])}")
    print(f"仅在repo_breaks_attritions_365中: {len(comparison_df[~comparison_df['in_collection1'] & comparison_df['in_collection2']])}")
    print(f"两个集合都有: {len(comparison_df[comparison_df['in_both']])}")
    print(f"有差异的记录数: {len(differences)}")
    
    if len(comparison_df[comparison_df["in_both"]]) > 0:
        consistency_rate = (len(comparison_df[comparison_df["in_both"]]) - len(differences)) / len(comparison_df[comparison_df["in_both"]]) * 100
        print(f"一致性率: {consistency_rate:.2f}%")
    
    # 统计差异类型
    if differences:
        print(f"\n差异类型分析:")
        
        count_breaks_diff = 0
        count_attritions_diff = 0
        count_both_diff = 0
        
        for diff in differences:
            if diff["breaks_count_diff"] != 0 and diff["attritions_count_diff"] != 0:
                count_both_diff += 1
            elif diff["breaks_count_diff"] != 0:
                count_breaks_diff += 1
            elif diff["attritions_count_diff"] != 0:
                count_attritions_diff += 1
        
        print(f"  仅Breaks差异: {count_breaks_diff}")
        print(f"  仅Attritions差异: {count_attritions_diff}")
        print(f"  Breaks和Attritions都有差异: {count_both_diff}")

def main():
    """主函数"""
    logging.info("开始分析repo_breaks_attritions vs repo_breaks_attritions_365...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 1. 比较两个集合的数据
    comparison_df, differences = compare_collections(db)
    
    # 2. 打印有差异的记录
    print_differences(differences)
    
    # 3. 保存分析结果
    save_analysis_results(comparison_df, differences)
    
    # 4. 打印统计摘要
    print_summary_statistics(comparison_df, differences)
    
    logging.info("repo_breaks_attritions vs repo_breaks_attritions_365 比较分析完成！")

if __name__ == "__main__":
    main()