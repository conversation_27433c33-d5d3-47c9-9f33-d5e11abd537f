import pandas as pd
from pymongo import MongoClient
import logging
from datetime import datetime
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("../logs/analyze_breaks_consistency.log", mode="w", encoding="utf-8"),
    ],
)

def connect_mongodb():
    """连接到MongoDB数据库"""
    client = MongoClient("mongodb://localhost:27017/")
    db = client["disengagement"]
    return db

def extract_breaks_data(db, limits=[180, 270, 365, 450]):
    """提取各个limit集合的breaks数据"""
    logging.info("开始提取各个limit集合的breaks数据...")
    
    all_breaks_data = {}
    
    for limit in limits:
        collection_name = f"repo_breaks_attritions_{limit}"
        collection = db[collection_name]
        
        logging.info(f"处理集合: {collection_name}")
        
        breaks_data = []
        cursor = collection.find({})
        
        for doc in cursor:
            repo_name = doc.get("repo_name", "")
            attritions = doc.get("attritions", [])
            
            for attrition in attritions:
                attrition_id = attrition.get("id")
                attrition_time = attrition.get("attrition_time")
                dev_login = attrition.get("dev_login")
                breaks = attrition.get("breaks", [])
                
                breaks_data.append({
                    "repo_name": repo_name,
                    "attrition_id": attrition_id,
                    "attrition_time": attrition_time,
                    "dev_login": dev_login,
                    "breaks": breaks,
                    "breaks_count": len(breaks)
                })
        
        all_breaks_data[limit] = breaks_data
        logging.info(f"  {collection_name}: 提取了 {len(breaks_data)} 条记录")
    
    return all_breaks_data

def analyze_breaks_consistency(all_breaks_data):
    """分析breaks的一致性"""
    logging.info("开始分析breaks的一致性...")
    
    limits = list(all_breaks_data.keys())
    
    # 创建DataFrame进行比较
    dfs = {}
    for limit in limits:
        df = pd.DataFrame(all_breaks_data[limit])
        dfs[limit] = df
    
    # 1. 基本统计比较
    print(f"\n{'='*80}")
    print("各集合基本统计比较")
    print(f"{'='*80}")
    
    for limit in limits:
        df = dfs[limit]
        print(f"\n{limit}天集合:")
        print(f"  总记录数: {len(df)}")
        print(f"  涉及仓库数: {df['repo_name'].nunique()}")
        print(f"  涉及开发者数: {df['dev_login'].nunique()}")
        print(f"  总Breaks数: {df['breaks_count'].sum()}")
        print(f"  有Breaks的记录数: {len(df[df['breaks_count'] > 0])}")
        print(f"  无Breaks的记录数: {len(df[df['breaks_count'] == 0])}")
    
    # 2. 按repo_name和dev_login进行匹配比较
    print(f"\n{'='*80}")
    print("按仓库和开发者匹配比较")
    print(f"{'='*80}")
    
    # 创建合并的DataFrame进行比较
    comparison_data = []
    
    # 获取所有唯一的repo_name和dev_login组合
    all_repo_dev_pairs = set()
    for limit in limits:
        df = dfs[limit]
        pairs = set(zip(df['repo_name'], df['dev_login']))
        all_repo_dev_pairs.update(pairs)
    
    logging.info(f"总共有 {len(all_repo_dev_pairs)} 个唯一的仓库-开发者组合")
    
    # 对每个组合进行比较
    for repo_name, dev_login in all_repo_dev_pairs:
        record_data = {"repo_name": repo_name, "dev_login": dev_login}
        
        for limit in limits:
            df = dfs[limit]
            matching_records = df[(df['repo_name'] == repo_name) & (df['dev_login'] == dev_login)]
            
            if len(matching_records) > 0:
                record = matching_records.iloc[0]
                record_data[f"breaks_{limit}"] = record['breaks']
                record_data[f"breaks_count_{limit}"] = record['breaks_count']
                record_data[f"attrition_time_{limit}"] = record['attrition_time']
            else:
                record_data[f"breaks_{limit}"] = []
                record_data[f"breaks_count_{limit}"] = 0
                record_data[f"attrition_time_{limit}"] = None
        
        comparison_data.append(record_data)
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 3. 找出有差异的记录
    differences = []
    
    for _, row in comparison_df.iterrows():
        breaks_counts = [row[f'breaks_count_{limit}'] for limit in limits]
        breaks_data = [row[f'breaks_{limit}'] for limit in limits]
        
        # 检查是否有差异
        if len(set(breaks_counts)) > 1 or any(len(set(str(b))) > 1 for b in breaks_data):
            differences.append({
                "repo_name": row["repo_name"],
                "dev_login": row["dev_login"],
                "breaks_counts": {limit: row[f'breaks_count_{limit}'] for limit in limits},
                "breaks_data": {limit: row[f'breaks_{limit}'] for limit in limits},
                "attrition_times": {limit: row[f'attrition_time_{limit}'] for limit in limits}
            })
    
    logging.info(f"发现 {len(differences)} 个有差异的记录")
    
    return comparison_df, differences

def print_differences(differences, limits):
    """打印有差异的记录"""
    print(f"\n{'='*100}")
    print(f"发现 {len(differences)} 个有差异的记录")
    print(f"{'='*100}")
    
    for i, diff in enumerate(differences[:20]):  # 只显示前20个
        print(f"\n{i+1}. {diff['repo_name']} - {diff['dev_login']}")
        print(f"   Breaks数量比较:")
        for limit in limits:
            print(f"     {limit}天: {diff['breaks_counts'][limit]} 个breaks")
        
        print(f"   Breaks详情比较:")
        for limit in limits:
            breaks = diff['breaks_data'][limit]
            print(f"     {limit}天: {breaks}")
        
        print(f"   Attrition时间比较:")
        for limit in limits:
            attrition_time = diff['attrition_times'][limit]
            print(f"     {limit}天: {attrition_time}")
        
        print("-" * 80)
    
    if len(differences) > 20:
        print(f"\n... 还有 {len(differences) - 20} 个差异记录未显示")

def save_analysis_results(comparison_df, differences, limits):
    """保存分析结果"""
    logging.info("保存分析结果...")
    
    # 创建输出目录
    output_dir = "../result/20250729_breaks_consistency_analysis"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存完整比较数据
    comparison_df.to_csv(f"{output_dir}/breaks_comparison_all_records.csv", index=False)
    
    # 保存有差异的记录
    if differences:
        diff_data = []
        for diff in differences:
            diff_record = {
                "repo_name": diff["repo_name"],
                "dev_login": diff["dev_login"]
            }
            
            for limit in limits:
                diff_record[f"breaks_count_{limit}"] = diff["breaks_counts"][limit]
                diff_record[f"breaks_data_{limit}"] = str(diff["breaks_data"][limit])
                diff_record[f"attrition_time_{limit}"] = diff["attrition_times"][limit]
            
            diff_data.append(diff_record)
        
        diff_df = pd.DataFrame(diff_data)
        diff_df.to_csv(f"{output_dir}/breaks_differences.csv", index=False)
    
    # 生成统计报告
    stats = {
        "total_records": len(comparison_df),
        "differences_count": len(differences),
        "consistency_rate": (len(comparison_df) - len(differences)) / len(comparison_df) * 100 if len(comparison_df) > 0 else 0,
        "limits_analyzed": limits
    }
    
    with open(f"{output_dir}/breaks_consistency_stats.json", "w", encoding="utf-8") as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    logging.info(f"分析结果已保存到 {output_dir}")

def print_summary_statistics(comparison_df, differences, limits):
    """打印统计摘要"""
    print(f"\n{'='*80}")
    print("Breaks一致性分析统计摘要")
    print(f"{'='*80}")
    print(f"总记录数: {len(comparison_df)}")
    print(f"有差异的记录数: {len(differences)}")
    print(f"一致性率: {(len(comparison_df) - len(differences)) / len(comparison_df) * 100:.2f}%" if len(comparison_df) > 0 else "N/A")
    print(f"分析的阈值: {limits}")
    
    # 按差异类型统计
    if differences:
        print(f"\n差异类型分析:")
        
        # 统计只有某些limit有数据的记录
        count_missing_data = 0
        count_different_counts = 0
        count_different_content = 0
        
        for diff in differences:
            breaks_counts = list(diff["breaks_counts"].values())
            breaks_data = list(diff["breaks_data"].values())
            
            # 检查是否有缺失数据
            if 0 in breaks_counts and any(c > 0 for c in breaks_counts):
                count_missing_data += 1
            
            # 检查数量是否不同
            if len(set(breaks_counts)) > 1:
                count_different_counts += 1
            
            # 检查内容是否不同
            if any(len(set(str(b))) > 1 for b in breaks_data):
                count_different_content += 1
        
        print(f"  数据缺失差异: {count_missing_data}")
        print(f"  数量差异: {count_different_counts}")
        print(f"  内容差异: {count_different_content}")

def main():
    """主函数"""
    logging.info("开始分析repo_breaks_attritions集合的breaks一致性...")
    
    # 连接数据库
    db = connect_mongodb()
    
    # 1. 提取各个limit集合的breaks数据
    limits = [180, 270, 365, 450]
    all_breaks_data = extract_breaks_data(db, limits)
    
    # 2. 分析breaks的一致性
    comparison_df, differences = analyze_breaks_consistency(all_breaks_data)
    
    # 3. 打印有差异的记录
    print_differences(differences, limits)
    
    # 4. 保存分析结果
    save_analysis_results(comparison_df, differences, limits)
    
    # 5. 打印统计摘要
    print_summary_statistics(comparison_df, differences, limits)
    
    logging.info("Breaks一致性分析完成！")

if __name__ == "__main__":
    main()